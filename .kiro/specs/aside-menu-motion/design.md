# Design Document

## Overview

This design outlines the conversion of the aside menu components from CSS transitions to Framer Motion with spring effects. The implementation will replace styled-components animations with Framer Motion's `motion` components and spring configurations while maintaining all existing functionality and visual states.

## Architecture

### Component Structure

- **AsideMenuGroup**: Container component that manages menu group visibility and animations
- **AsideMenuItem**: Individual menu item component with submenu expansion animations
- **Motion Configuration**: Centralized spring animation settings for consistency

### Animation Strategy

- Replace CSS transitions with Framer Motion's `motion` components
- Use spring physics for natural, responsive animations
- Implement staggered animations for submenu items
- Maintain responsive behavior across different screen sizes

## Components and Interfaces

### AsideMenuGroup Component

**Current Implementation:**

- Uses styled-components with CSS transitions
- Manages opacity and visibility through CSS animations
- Handles responsive behavior with media queries

**New Implementation:**

- Convert to `motion.div` with Framer Motion animations
- Use `AnimatePresence` for enter/exit animations
- Implement spring-based opacity and scale transitions

```typescript
interface MotionConfig {
  spring: {
    type: 'spring';
    stiffness: number;
    damping: number;
    mass: number;
  };
  fadeIn: {
    initial: { opacity: number; scale: number };
    animate: { opacity: number; scale: number };
    exit: { opacity: number; scale: number };
  };
}
```

### AsideMenuItem Component

**Current Implementation:**

- Complex styled-components with height-based animations
- Manual height calculations for submenu expansion
- CSS transitions for expand icon rotation

**New Implementation:**

- Replace `SubMenuListWrap` with `motion.div`
- Use `layoutId` for smooth height transitions
- Implement staggered children animations for submenu items
- Spring-based icon rotation animations

```typescript
interface SubMenuAnimationProps {
  isOpen: boolean;
  itemCount: number;
  showAside: boolean;
}
```

## Data Models

### Animation Configuration

```typescript
export const menuAnimationConfig = {
  spring: {
    type: 'spring' as const,
    stiffness: 300,
    damping: 30,
    mass: 1,
  },
  menuGroup: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  menuItem: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
  },
  submenu: {
    initial: { height: 0, opacity: 0 },
    animate: { height: 'auto', opacity: 1 },
    exit: { height: 0, opacity: 0 },
  },
  expandIcon: {
    collapsed: { rotate: 0 },
    expanded: { rotate: 90 },
  },
};
```

### Component Props

```typescript
interface AsideMenuGroupProps {
  serviceType: ServiceType;
}

interface AsideMenuItemProps {
  serviceGroup: ServiceGroup;
}

interface ServiceType {
  name: string;
  serviceGroups: ServiceGroup[];
}

interface ServiceGroup {
  name: string;
  slug: string;
  imageUrl?: string;
  permissionSlug: string;
  serviceItems: ServiceItem[];
}

interface ServiceItem {
  name: string;
  slug: string;
  permissionSlug: string;
}
```

## Error Handling

### Animation Fallbacks

- Implement `prefers-reduced-motion` media query support
- Provide instant transitions when motion is disabled
- Handle cases where Framer Motion fails to load

### Performance Considerations

- Use `will-change` CSS property for optimized animations
- Implement animation cleanup on component unmount
- Debounce rapid state changes to prevent animation conflicts

```typescript
const motionProps = {
  ...(!prefersReducedMotion && {
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
    variants: menuAnimationConfig.menuItem,
    transition: menuAnimationConfig.spring,
  }),
};
```

## Testing Strategy

### Unit Tests

- Test animation state transitions
- Verify spring configuration values
- Test responsive behavior with different screen sizes
- Test accessibility compliance with reduced motion preferences

### Integration Tests

- Test menu expansion/collapse functionality
- Verify submenu animations work correctly
- Test navigation behavior remains unchanged
- Test performance with multiple menu items

### Visual Regression Tests

- Compare animation smoothness before and after
- Verify visual consistency across different states
- Test mobile responsive animations

## Implementation Details

### Motion Components Mapping

1. **MenuItemStyle** → `motion.div` with spring animations
2. **SubMenuListWrap** → `motion.div` with height animations
3. **expand-icon** → `motion.div` with rotation animations
4. **service-group-name** → `motion.div` with fade animations

### Spring Configuration Rationale

- **Stiffness: 300** - Provides responsive feel without being too bouncy
- **Damping: 30** - Smooth settling without oscillation
- **Mass: 1** - Standard mass for UI elements

### Responsive Considerations

- Maintain existing breakpoint at 820px
- Adapt animation duration for mobile devices
- Preserve touch interaction behavior

### Accessibility

- Respect `prefers-reduced-motion` system setting
- Maintain keyboard navigation functionality
- Preserve screen reader compatibility
- Ensure focus management during animations

## Migration Strategy

### Phase 1: AsideMenuGroup

- Replace styled-components with motion.div
- Implement basic fade and scale animations
- Test responsive behavior

### Phase 2: AsideMenuItem

- Convert submenu animations to Framer Motion
- Implement staggered children animations
- Add spring-based icon rotation

### Phase 3: Polish and Optimization

- Fine-tune spring configurations
- Add accessibility features
- Performance optimization
- Cross-browser testing
