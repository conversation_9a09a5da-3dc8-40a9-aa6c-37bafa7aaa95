/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: { scrollRestoration: true },
  reactStrictMode: false,
  trailingSlash: false,
  compiler: {
    styledComponents: {
      ssr: true,
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.honconnect.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  publicRuntimeConfig: {
    API_ENDPOINT: process.env.NEXT_PUBLIC_API_ENDPOINT,
    AUTH_ENDPOINT: process.env.NEXT_PUBLIC_AUTH_ENDPOINT,
    ORDER_ENDPOINT: process.env.NEXT_PUBLIC_API_ORDER_ENDPOINT,
    STOCK_ENDPOINT: process.env.NEXT_PUBLIC_API_STOCK_ENDPOINT,
    REDIRECT_URL: process.env.NEXT_PUBLIC_REDIRECT_URL,
    TINY_API_KEY: process.env.NEXT_PUBLIC_TINY_API_KEY,
    AUTH_CLIENT: process.env.NEXT_PUBLIC_AUTH_CLIENT,
    AUTH_SECRET: process.env.NEXT_PUBLIC_AUTH_SECRET,
    DEV_MODE: process.env.NEXT_PUBLIC_DEV_MODE,
  },
  serverRuntimeConfig: {
    API_ENDPOINT: process.env.NEXT_PUBLIC_API_ENDPOINT,
    AUTH_ENDPOINT: process.env.NEXT_PUBLIC_AUTH_ENDPOINT,
    ORDER_ENDPOINT: process.env.NEXT_PUBLIC_API_ORDER_ENDPOINT,
    STOCK_ENDPOINT: process.env.NEXT_PUBLIC_API_STOCK_ENDPOINT,
    REDIRECT_URL: process.env.NEXT_PUBLIC_REDIRECT_URL,
    TINY_API_KEY: process.env.NEXT_PUBLIC_TINY_API_KEY,
    AUTH_CLIENT: process.env.NEXT_PUBLIC_AUTH_CLIENT,
    AUTH_SECRET: process.env.NEXT_PUBLIC_AUTH_SECRET,
    DEV_MODE: process.env.NEXT_PUBLIC_DEV_MODE,
  },
};

module.exports = nextConfig;
