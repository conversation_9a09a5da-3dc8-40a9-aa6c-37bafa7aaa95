{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./src/*"], "@/store/*": ["./src/store/*"], "@/services/*": ["./src/services/*"], "@/components/*": ["./src/components/*"], "@/styles/*": ["./src/styles/*"], "@/pages/*": ["./src/pages/*"], "@/hooks/*": ["./src/hooks/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}