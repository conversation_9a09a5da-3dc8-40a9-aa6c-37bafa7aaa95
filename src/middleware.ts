import { NextRequest, NextResponse } from 'next/server';

export function middleware(req: NextRequest) {
  const cookieString = req.headers.get('cookie');
  if (cookieString && cookieString?.search('access_token') >= 0) {
    if (cookieString?.search('selected-company-id') >= 0) {
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL('/company', req.url));
  }
  if (req.nextUrl.pathname === '/preview') {
    return NextResponse.next();
  }
  if (req.nextUrl.pathname === '/') {
    return NextResponse.next();
  }
  return NextResponse.redirect(new URL('/', req.url));
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    '/((?!api|_next/static|_next|images|icons|login|favicon.ico|authorized).*)',
  ],
};
