export function dataURItoFile(dataURI: any, fileName: any) {
  // Convert the base64 data URI to a Blob
  const byteString = atob(dataURI.split(',')[1]);
  const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }
  const blob = new Blob([ab], { type: mimeString });

  // Create a new File object with the Blob and the specified fileName
  return new File([blob], fileName, { type: mimeString });
}
