export function getAppTitle(pathName: string): string {
  if (pathName.startsWith('/dashboard')) {
    return 'Dashboard';
  }
  if (pathName.startsWith('/company/manage-users/users')) {
    return 'Users';
  }
  if (pathName.startsWith('/company/manage-users/permissions')) {
    return 'Permissions';
  }
  if (pathName.startsWith('/company/setting/coating')) {
    return 'Coating';
  }
  if (pathName.startsWith('/company/setting/extra')) {
    return 'Extra';
  }
  if (pathName.startsWith('/company/setting/print')) {
    return 'Print';
  }
  if (pathName.startsWith('/company/contact')) {
    return 'Contact';
  }
  if (pathName.startsWith('/company')) {
    return 'Company';
  }
  if (pathName.startsWith('/product')) {
    return 'Product';
  }
  if (pathName.startsWith('/orders')) {
    return 'Orders';
  }
  if (pathName.startsWith('/stock/warehouse')) {
    return 'Warehouse';
  }
  if (pathName.startsWith('/stock/raw-material')) {
    return 'RM';
  }
  if (pathName.startsWith('/stock/purchase-order')) {
    return 'PO';
  }
  if (pathName.startsWith('/stock/setting/material')) {
    return 'Material';
  }
  if (pathName.startsWith('/stock/setting/brand')) {
    return 'Brand';
  }
  if (pathName.startsWith('/stock/setting/sizes')) {
    return 'Sizes';
  }
  return '';
}
