export enum ACTION {
  CREATE,
  UPDATE,
  DELETE,
}

export const fetcher = async (url: string) => {
  const response = await fetch(url);
  const results = await response.json();
  return results.data;
};

export function getPrStatusData(status: number | undefined) {
  switch (status) {
    case 1:
      return {
        id: 1,
        name: 'รออนุมัติใบขอซื้อ',
      };
    case 2:
      return {
        id: 2,
        name: 'อนุมัติสำเร็จ',
      };
    case 3:
      return {
        id: 3,
        name: 'ไม่อนุมัติ',
      };
    case 4:
      return {
        id: 4,
        name: 'ยกเลิก',
      };
    default:
      return {
        id: 0,
        name: 'แบบร่าง',
      };
  }
}
