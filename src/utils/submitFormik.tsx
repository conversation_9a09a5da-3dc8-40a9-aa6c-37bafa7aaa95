import Swal from 'sweetalert2';
import { isEmpty } from 'lodash';

export const submitFormik = async (event: any, formik: any) => {
  event?.preventDefault();
  await formik.validateForm();
  formik.setTouched({
    width: true,
    height: true,
    length: true,
    quantity: true,
    subMaterialDetailId: true,
    printSideComponentId: true,
    printMasterId: true,
    colorFront: true,
    colorBack: true,
  });

  const colorFrontQuantityPass = formik.values.colorFront.every(
    (cf: any) => cf && cf.color?.id
  );

  const colorBackQuantityCheckPass = formik.values.colorBack.every(
    (cb: any) => cb && cb.color?.id
  );
  if (
    !formik.isValid ||
    !colorFrontQuantityPass ||
    !colorBackQuantityCheckPass
  ) {
    await Swal.fire({
      title: 'โปรดตรวจสอบข้อมูล',
      text: 'ข้อมูลไม่ครบ หรือข้อมูลไม่ถูกต้อง',
      icon: 'warning',
      confirmButtonText: 'ตกลง',
      focusConfirm: true,
      didClose: () => {
        const firstKeyError = Object.keys(formik.errors)[0];
        let firstErrorField;
        if (
          typeof formik.errors[firstKeyError] === 'object' &&
          !Array.isArray(formik.errors[firstKeyError]) &&
          formik.errors[firstKeyError] !== null
        ) {
          // ค้นหา field แรกของ object ภายใน firstKeyError
          const innerFirstKey = Object.keys(formik.errors[firstKeyError])[0];
          firstErrorField = document.querySelector(
            `[name="${innerFirstKey}"]`
          ) as any;
        } else {
          // ใช้ firstKeyError โดยตรงหากไม่ใช่ object
          firstErrorField = document.querySelector(
            `[name="${firstKeyError}"]`
          ) as any;
        }
        if (firstErrorField) {
          firstErrorField.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center',
          });
        }
      },
    });
  } else if (isEmpty(formik.values.serviceChargeCost)) {
    await Swal.fire({
      title: 'โปรดเลือกค่าบริการ',
      text: 'ข้อมูลไม่ครบ หรือข้อมูลไม่ถูกต้อง',
      icon: 'warning',
      confirmButtonText: 'ตกลง',
      focusConfirm: true,
    });
  } else {
    formik.submitForm();
  }
};
