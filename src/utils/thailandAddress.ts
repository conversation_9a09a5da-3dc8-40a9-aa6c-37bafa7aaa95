import district from '@/utils/district.json';
import subDistrict from '@/utils/subDistrict.json';
import province from '@/utils/province.json';

export const getDistrictByProvince = (value: any) => {
  const filterDistrict = district.filter(
    (item: any) => item.PROVINCE_ID === value.PROVINCE_ID
  );
  return filterDistrict;
};
export const getSubDistrictByDistrict = (value: any) => {
  const filterSubDistrict = subDistrict.filter(
    (item: any) => item.DISTRICT_ID === value.DISTRICT_ID
  );
  return filterSubDistrict;
};

export const getProvinceByName = (value: string) => {
  const provinceByName = province.find(
    (item: any) => item.PROVINCE_NAME === value
  );
  return provinceByName;
};
