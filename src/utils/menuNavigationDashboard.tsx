import React from 'react';

export const menuNavigationDashboard = [
  {
    name: 'Home',
    url: '/dashboard',
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="home_FILL1_wght700_GRAD200_opsz24 1">
          <path
            id="Vector"
            d="M6.32578 22.0999C5.3762 22.0999 4.57213 21.7665 3.91358 21.0996C3.25505 20.4327 2.92578 19.6328 2.92578 18.6999V10.1749C2.92578 9.63969 3.04661 9.13088 3.28828 8.64848C3.52995 8.16609 3.86745 7.76657 4.30078 7.4499L9.95078 3.1999C10.2508 2.98324 10.5758 2.81657 10.9258 2.6999C11.2758 2.58324 11.6341 2.5249 12.0008 2.5249C12.3674 2.5249 12.7258 2.58324 13.0758 2.6999C13.4258 2.81657 13.7508 2.98324 14.0508 3.1999L19.7008 7.4499C20.1492 7.77255 20.4905 8.17412 20.7246 8.6546C20.9587 9.13507 21.0758 9.64184 21.0758 10.1749V18.6999C21.0758 19.6328 20.7465 20.4327 20.088 21.0996C19.4294 21.7665 18.6254 22.0999 17.6758 22.0999H14.3008V13.7999H9.70078V22.0999H6.32578Z"
            fill="#263238"
          />
        </g>
      </svg>
    ),
  },
  {
    name: 'Work',
    url: '#',
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="work_FILL1_wght500_GRAD0_opsz24 1">
          <path
            id="Vector"
            d="M4.07187 21.2031C3.44216 21.2031 2.90556 20.9813 2.46207 20.5379C2.01861 20.0944 1.79688 19.5578 1.79688 18.9281V8.16721C1.79688 7.5375 2.01863 7.0009 2.46213 6.55741C2.90559 6.11395 3.44218 5.89221 4.0719 5.89221H7.81483V3.89221C7.81483 3.2625 8.03657 2.7259 8.48005 2.28241C8.92352 1.83893 9.46011 1.61719 10.0898 1.61719H13.9105C14.5402 1.61719 15.0768 1.83893 15.5203 2.28241C15.9637 2.7259 16.1855 3.2625 16.1855 3.89221V5.89221H19.9284C20.5581 5.89221 21.0947 6.11395 21.5382 6.55741C21.9817 7.0009 22.2034 7.5375 22.2034 8.16721V18.9281C22.2034 19.5578 21.9817 20.0944 21.5382 20.5379C21.0947 20.9813 20.5581 21.2031 19.9284 21.2031H4.07187ZM10.0898 5.89221H13.9105V3.89221H10.0898V5.89221Z"
            fill="#263238"
          />
        </g>
      </svg>
    ),
  },
  {
    name: 'Notifications',
    url: '#',
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="notifications_FILL1_wght700_GRAD200_opsz24 1">
          <path
            id="Vector"
            d="M4.10039 19.7498C3.64039 19.7498 3.24206 19.5815 2.90539 19.2448C2.56872 18.9081 2.40039 18.5098 2.40039 18.0498C2.40039 17.5665 2.56872 17.1623 2.90539 16.8373C3.24206 16.5123 3.64039 16.3498 4.10039 16.3498H4.67539V10.4998C4.67539 8.88534 5.14206 7.42755 6.07539 6.12645C7.00872 4.82535 8.25039 3.96647 9.80039 3.5498V2.9998C9.80039 2.37897 10.0114 1.8571 10.4335 1.43418C10.8555 1.01126 11.3764 0.799805 11.996 0.799805C12.6156 0.799805 13.1379 1.01126 13.5629 1.43418C13.9879 1.8571 14.2004 2.37897 14.2004 2.9998V3.5498C15.7671 3.9498 17.0129 4.80397 17.9379 6.1123C18.8629 7.42064 19.3254 8.88314 19.3254 10.4998V16.3498H19.9254C20.3854 16.3498 20.7837 16.5123 21.1204 16.8373C21.4571 17.1623 21.6254 17.5623 21.6254 18.0373C21.6254 18.5123 21.4571 18.9165 21.1204 19.2498C20.7837 19.5831 20.3854 19.7498 19.9254 19.7498H4.10039ZM12.0274 23.0998C11.3594 23.0998 10.7879 22.8669 10.3129 22.4012C9.83789 21.9355 9.60039 21.3683 9.60039 20.6998H14.4254C14.4254 21.3665 14.1927 21.9331 13.7274 22.3998C13.262 22.8665 12.6954 23.0998 12.0274 23.0998Z"
            fill="#263238"
          />
        </g>
      </svg>
    ),
  },
  {
    name: 'Search',
    url: '#',
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="search_FILL1_wght600_GRAD200_opsz24 1">
          <path
            id="Vector"
            d="M9.34 16.6771C7.33112 16.6771 5.62672 15.9768 4.2268 14.5764C2.8269 13.1759 2.12695 11.4708 2.12695 9.46115C2.12695 7.45149 2.82719 5.74737 4.22765 4.3488C5.62812 2.95025 7.33319 2.25098 9.34285 2.25098C11.3525 2.25098 13.0567 2.95093 14.4552 4.35085C15.8538 5.75077 16.553 7.45516 16.553 9.46403C16.553 10.2329 16.4536 10.9448 16.2547 11.5999C16.0558 12.255 15.7751 12.835 15.4128 13.3401L20.8063 18.7477C21.094 19.0448 21.2378 19.4025 21.2378 19.8208C21.2378 20.2391 21.0893 20.597 20.7922 20.8945C20.497 21.1822 20.1396 21.326 19.7202 21.326C19.3008 21.326 18.9425 21.1822 18.6454 20.8945L13.2978 15.526C12.8027 15.8717 12.2092 16.1499 11.5172 16.3608C10.8251 16.5716 10.0994 16.6771 9.34 16.6771ZM9.33418 13.6032C10.5049 13.6032 11.4884 13.207 12.2847 12.4146C13.081 11.6222 13.4791 10.6406 13.4791 9.46985C13.4791 8.2991 13.081 7.31559 12.2847 6.51933C11.4884 5.72306 10.5049 5.32493 9.33418 5.32493C8.16341 5.32493 7.18184 5.72306 6.38945 6.51933C5.59707 7.31559 5.20088 8.2991 5.20088 9.46985C5.20088 10.6406 5.59707 11.6222 6.38945 12.4146C7.18184 13.207 8.16341 13.6032 9.33418 13.6032Z"
            fill="#263238"
          />
        </g>
      </svg>
    ),
  },
];
