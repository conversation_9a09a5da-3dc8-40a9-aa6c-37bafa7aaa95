export function verifyPermission(
  permissions: string[],
  serviceType: string,
  serviceGroup: string,
  action: string
) {
  let isVerified = false;

  for (let i = 0; i < permissions.length; i++) {
    const permission = permissions[i];
    const values = permission.split('.');
    if (values[2].includes(action)) {
      isVerified = true;
    }
  }

  return isVerified;
}

export function verifyServiceGroup() {}

export function verifyServiceType() {}
