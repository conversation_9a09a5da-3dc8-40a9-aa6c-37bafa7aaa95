export const validateImageFiles = async (
  files: File[],
  maxSizeInMB: number,
  isCompress?: boolean,
  ignoreQuality?: boolean
) => {
  const result = {
    status: true,
    message: '',
    files: [] as File[],
  };

  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
  ];

  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  const fileArray = Array.from(files);
  for (const file of fileArray) {
    if (!allowedTypes.includes(file.type)) {
      result.status = false;
      result.message =
        'Please select a valid image file (JPEG, PNG, SVG, WebP, or GIF)';
      return result;
    }

    if (isCompress && file.type !== 'image/svg+xml') {
      if (file.size > maxSizeInBytes) {
        const compressedFile = await compressImage(
          file,
          maxSizeInBytes,
          ignoreQuality
        );
        if (!compressedFile) {
          result.status = false;
          result.message = `File sizes should be less than ${maxSizeInMB} MB.`;
          return result;
        }
        result.files.push(compressedFile);
      } else {
        result.files.push(file);
      }
    } else {
      if (file.size > maxSizeInBytes) {
        result.status = false;
        result.message = `File sizes should be less than ${maxSizeInMB} MB.`;
        return result;
      }
      result.files.push(file);
    }
  }

  return result;
};

const compressImage = async (
  file: File,
  maxSizeInBytes: number,
  ignoreQuality?: boolean
): Promise<File | null> => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const image = new Image();
  image.src = URL.createObjectURL(file);

  return new Promise((resolve, reject) => {
    image.onload = async () => {
      canvas.width = image.naturalWidth;
      canvas.height = image.naturalHeight;
      ctx.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight);

      const outputType = 'image/webp';

      // สร้าง blob ครั้งแรก (full quality = 1)
      let newBlob = await createBlobFromCanvas(canvas, outputType, 1);
      if (!newBlob) {
        resolve(null);
        return;
      }

      // ถ้า blob เล็กพอแล้ว => คืนค่าได้เลย
      if (newBlob.size <= maxSizeInBytes) {
        resolve(new File([newBlob], file.name, { type: outputType }));
        return;
      }

      // ลด quality ทีละ 0.1
      let quality = 1;
      for (let i = 0; i < 10; i++) {
        quality -= 0.1;
        if (quality < 0.1) break;

        const compressedBlob = await createBlobFromCanvas(
          canvas,
          outputType,
          quality
        );
        if (!compressedBlob) {
          resolve(null);
          return;
        }

        if (compressedBlob.size <= maxSizeInBytes) {
          resolve(new File([compressedBlob], file.name, { type: outputType }));
          return;
        }
        newBlob = compressedBlob;
      }

      // ถ้ายังใหญ่เกิน => recursive re-draw (ถ้า ignoreQuality = true)
      if (ignoreQuality && newBlob.size > maxSizeInBytes) {
        const finalBlob = await recursiveReDrawCompress(
          newBlob,
          outputType,
          maxSizeInBytes,
          0,
          5
        );
        if (finalBlob && finalBlob.size <= maxSizeInBytes) {
          resolve(new File([finalBlob], file.name, { type: outputType }));
        } else {
          resolve(null);
        }
      } else {
        resolve(null); // ไฟล์ใหญ่เกิน
      }
    };

    image.onerror = () => {
      reject(new Error('Failed to load image'));
    };
  });
};

async function recursiveReDrawCompress(
  compressedBlob: Blob,
  fileType: string,
  maxSizeInBytes: number,
  loopCount: number,
  maxLoop: number
): Promise<Blob | null> {
  console.log('loopCount', loopCount);
  console.log('compressedBlob.size', compressedBlob.size);
  if (loopCount >= maxLoop) {
    return null;
  }

  // ถ้าขนาดพอแล้ว => ไม่ต้องบีบต่อ
  if (compressedBlob.size <= maxSizeInBytes) {
    return compressedBlob;
  }

  // re-draw compressedBlob ลง canvas ใหม่
  const blobUrl = URL.createObjectURL(compressedBlob);
  const tempImg = await loadImage(blobUrl);
  if (!tempImg) return null;

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  canvas.width = tempImg.naturalWidth;
  canvas.height = tempImg.naturalHeight;
  ctx.drawImage(tempImg, 0, 0, tempImg.naturalWidth, tempImg.naturalHeight);

  // เต็ม quality ก่อน (quality = 1)
  let newBlob = await createBlobFromCanvas(canvas, fileType, 1);
  if (!newBlob) return null;

  // ถ้าขนาดพอแล้ว => ไม่ต้องบีบต่อ
  if (newBlob.size <= maxSizeInBytes) {
    return newBlob;
  }

  // ถ้ายังใหญ่ => ลด quality ทีละ 0.2 อีก 5 รอบ
  let newQuality = 1;
  for (let i = 0; i < 5; i++) {
    newQuality -= 0.2;
    if (newQuality < 0.1) break;

    const stepBlob = await createBlobFromCanvas(canvas, fileType, newQuality);
    if (!stepBlob) return null;
    if (stepBlob.size <= maxSizeInBytes) {
      return stepBlob;
    }
    newBlob = stepBlob;
  }

  // ถ้ายังใหญ่ => recursive ซ้ำ
  return recursiveReDrawCompress(
    newBlob,
    fileType,
    maxSizeInBytes,
    loopCount + 1,
    maxLoop
  );
}

function loadImage(url: string): Promise<HTMLImageElement | null> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => resolve(null);
    img.src = url;
  });
}

// สร้าง blob จาก canvas
function createBlobFromCanvas(
  canvas: HTMLCanvasElement,
  type: string,
  quality: number
): Promise<Blob | null> {
  return new Promise((resolve) => {
    canvas.toBlob((blob) => resolve(blob), type, quality);
  });
}

export const validateVideoFiles = async (
  files: File[],
  maxSizeInMB: number
) => {
  const result = {
    status: true,
    message: '',
    files: [] as File[],
  };

  const allowedTypes = ['video/mp4', 'video/mov', 'video/avi'];
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

  for (const file of files) {
    if (!allowedTypes.includes(file.type)) {
      result.status = false;
      result.message = 'Please select a valid video file (mp4, mov, avi)';
      return result;
    }
    if (file.size > maxSizeInBytes) {
      result.status = false;
      result.message = `File sizes should be less than ${maxSizeInMB} MB`;
      return result;
    }
    result.files.push(file); // Add valid files
  }

  return result;
};
