export const calcAdjustCostBySubCostDiff = (estimate: any) => {
  // Return original data if invalid
  if (!estimate) {
    return estimate;
  }

  // Handle new data structure with estimateProducts
  if (estimate.estimateProducts && Array.isArray(estimate.estimateProducts)) {
    let hasChanges = false;
    let data: any = null;

    // Check if any changes are needed first
    estimate.estimateProducts.forEach((product: any) => {
      if (product.station && Array.isArray(product.station)) {
        const originalStationStr = JSON.stringify(product.station);
        const processedProduct = calcAdjustCostBySubCostDiffForProduct({
          station: structuredClone(product.station),
          estimateQuantity: estimate.estimateQuantity,
        });
        const newStationStr = JSON.stringify(processedProduct.station);

        if (originalStationStr !== newStationStr) {
          hasChanges = true;
        }
      }
    });

    // Only clone and modify if there are changes
    if (hasChanges) {
      console.log(
        'calcAdjustCostBySubCostDiff: changes detected, creating new object'
      );
      data = structuredClone(estimate);
      data.estimateProducts.forEach((product: any) => {
        if (product.station && Array.isArray(product.station)) {
          const processedProduct = calcAdjustCostBySubCostDiffForProduct({
            station: product.station,
            estimateQuantity: data.estimateQuantity,
          });
          product.station = processedProduct.station;
        }
      });
      return data;
    }

    return estimate;
  }

  // Fallback for old data structure
  const data = structuredClone(estimate);
  if (!data.station || !Array.isArray(data.station)) {
    console.warn(
      'calcAdjustCostBySubCostDiff: data.station is undefined or not an array',
      data
    );
    return data;
  }

  return calcAdjustCostBySubCostDiffForProduct(data);
};

const calcAdjustCostBySubCostDiffForProduct = (data: any) => {
  const costStation = data.station.find(
    (s: any) => s.optionCostType.name === 'ต้นทุน'
  );
  if (!costStation) return data;

  interface DiffEntry {
    up: number;
    down: number;
  }
  const diffMap: Record<number, DiffEntry> = {};

  data.station.forEach((st: any) => {
    if (st.optionCostType.name === 'ต้นทุน') return;

    st.subStation.forEach((sub: any) => {
      sub.subCost.forEach((c: any) => {
        if (c.costType === 0) return;

        const q = c.estimateQuantityId;
        const diff = +(c.priceCostResult - c.priceCalculate).toFixed(2);
        if (diff === 0) return;

        const d = (diffMap[q] ??= { up: 0, down: 0 });
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        diff > 0 ? (d.up += diff) : (d.down += diff);
      });
    });
  });

  const buildMap =
    (field: 'priceCostResult' | 'priceCalculate') => (type: 1 | 2) => {
      const map: Record<number, number> = {};
      data.station.forEach((st: any) => {
        if (st.optionCostType.name === 'ต้นทุน') return;

        st.subStation.forEach((sub: any) => {
          sub.subCost.forEach((c: any) => {
            if (c.costType === type) {
              const q = c.estimateQuantityId;
              map[q] = (map[q] ?? 0) + (c[field] ?? 0);
            }
          });
        });
      });
      return map;
    };

  const matMapRes = buildMap('priceCostResult')(1); // ปัจจุบัน
  const laborMapRes = buildMap('priceCostResult')(2);

  const matMapCalc = buildMap('priceCalculate')(1); // ก่อนปรับ
  const laborMapCalc = buildMap('priceCalculate')(2);

  const setRow = (title: string, fn: (q: number) => number) => {
    const sub = costStation.subStation.find((s: any) => s.subTitle === title);
    if (!sub) return;
    sub.subCost.forEach((c: any) => {
      c.priceCostResult = +fn(c.estimateQuantityId).toFixed(2);
      c.diff = 0;
    });
  };

  setRow('ราคาปรับขึ้น', (q) => diffMap[q]?.up ?? 0);
  setRow('ราคาปรับลง', (q) => diffMap[q]?.down ?? 0);
  setRow(
    'ผลของการปรับราคา',
    (q) => (diffMap[q]?.up ?? 0) + (diffMap[q]?.down ?? 0)
  );

  setRow('ค่าวัตถุดิบ', (q) => matMapRes[q] ?? 0);
  setRow('ค่าแรง', (q) => laborMapRes[q] ?? 0);

  setRow('สรุปราคาต้นทุน', (q) => (matMapRes[q] ?? 0) + (laborMapRes[q] ?? 0));

  setRow(
    'รวมราคาต้นทุนสุทธิ',
    (q) => (matMapCalc[q] ?? 0) + (laborMapCalc[q] ?? 0)
  );

  const sumSub = costStation.subStation.find(
    (s: any) => s.subTitle === 'สรุปราคาต้นทุน'
  );
  sumSub?.subCost.forEach((row: any) => {
    const costRow = costStation.cost.find(
      (c: any) => c.estimateQuantityId === row.estimateQuantityId
    );
    if (costRow) costRow.priceStation = row.priceCostResult;
  });

  ['กำไร', 'ส่วนลด'].forEach((name) => {
    const st = data.station.find((s: any) => s.optionCostType.name === name);
    const firstSub = st?.subStation?.[0];
    firstSub?.subCost.forEach((row: any) => {
      const costRow = st.cost.find(
        (c: any) => c.estimateQuantityId === row.estimateQuantityId
      );
      if (costRow) costRow.priceStation = row.priceCostResult;
    });
  });

  return data;
};

export const updateSummaryOfferingPrice = (estimate: any) => {
  // Return original data if invalid
  if (!estimate) {
    return estimate;
  }

  // Handle new data structure with estimateProducts
  if (estimate.estimateProducts && Array.isArray(estimate.estimateProducts)) {
    let hasChanges = false;
    let data: any = null;

    // Check if any changes are needed first
    estimate.estimateProducts.forEach((product: any) => {
      if (product.station && Array.isArray(product.station)) {
        const originalStationStr = JSON.stringify(product.station);
        const originalQuantityStr = JSON.stringify(estimate.estimateQuantity);

        const processedProduct = updateSummaryOfferingPriceForProduct({
          station: structuredClone(product.station),
          estimateQuantity: structuredClone(estimate.estimateQuantity),
        });

        const newStationStr = JSON.stringify(processedProduct.station);
        const newQuantityStr = JSON.stringify(
          processedProduct.estimateQuantity || estimate.estimateQuantity
        );

        if (
          originalStationStr !== newStationStr ||
          originalQuantityStr !== newQuantityStr
        ) {
          hasChanges = true;
        }
      }
    });

    // Only clone and modify if there are changes
    if (hasChanges) {
      data = structuredClone(estimate);
      data.estimateProducts.forEach((product: any) => {
        if (product.station && Array.isArray(product.station)) {
          const processedProduct = updateSummaryOfferingPriceForProduct({
            station: product.station,
            estimateQuantity: data.estimateQuantity,
          });

          product.station = processedProduct.station;
          // Update estimateQuantity if it exists in the product
          if (processedProduct.estimateQuantity) {
            data.estimateQuantity = processedProduct.estimateQuantity;
          }
        }
      });
      console.log(
        'updateSummaryOfferingPrice: changes detected, creating new object'
      );
      return data;
    }
    return estimate;
  }

  // Fallback for old data structure
  const data = structuredClone(estimate);
  if (!data.station || !Array.isArray(data.station)) {
    console.warn(
      'updateSummaryOfferingPrice: data.station is undefined or not an array',
      data
    );
    return data;
  }

  return updateSummaryOfferingPriceForProduct(data);
};

const updateSummaryOfferingPriceForProduct = (data: any) => {
  const costSt = data.station.find(
    (s: any) => s.optionCostType.name === 'ต้นทุน'
  );
  const profitSt = data.station.find(
    (s: any) => s.optionCostType.name === 'กำไร'
  );
  const discSt = data.station.find(
    (s: any) => s.optionCostType.name === 'ส่วนลด'
  );
  const saleSt = data.station.find(
    (s: any) => s.optionCostType.name === 'สรุปราคาเสนอขาย'
  );
  if (!costSt || !saleSt) return data;

  const vatRate = 0.07;

  const getPrice = (st: any, qId: number) =>
    st?.cost.find((c: any) => c.estimateQuantityId === qId)?.priceStation ?? 0;

  saleSt.cost.forEach((c: any) => {
    const qId = c.estimateQuantityId;

    const baseCost = getPrice(costSt, qId);
    const profit = getPrice(profitSt, qId);
    const discount = getPrice(discSt, qId); // เป็น 0 ถ้าไม่มี station/บรรทัด
    const base = Number((baseCost + profit - discount).toFixed(2));

    const vat = +(base * vatRate).toFixed(2);
    const total = +(base + vat).toFixed(2);

    c.priceStation = total;

    const subCostRow = saleSt.subStation[0].subCost.find(
      (s: any) => s.estimateQuantityId === qId
    );
    if (subCostRow) {
      // const qtyObj = data.estimateQuantity.find(
      //   (q: any) => q.estimateQuantityId === qId
      // );
      // const qtyAll = qtyObj.quantity + qtyObj.quantityAllowance;

      subCostRow.description = vat.toFixed(2); // VAT text
      // subCostRow.rawPrice = +(total / qtyAll).toFixed(2); // ราคา/ชิ้น
      subCostRow.priceCostResult = total;
      subCostRow.diff = 0;
    }

    const qItem = data.estimateQuantity?.find(
      (q: any) => q.estimateQuantityId === qId
    );
    if (qItem) qItem.totalSalePrice = total;
  });

  return data;
};
