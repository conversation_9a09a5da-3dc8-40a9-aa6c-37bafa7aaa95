export const buildEstimateStationBody = (estimateQuantityStation: any) => {
  return {
    estimateProductId: estimateQuantityStation.estimateProductId,
    isSetZero: estimateQuantityStation.isSetZero || false,
    station:
      estimateQuantityStation.station?.map((station: any) => ({
        estimateQuantityStationId: station.estimateQuantityStationId,
        cost:
          station.cost?.map((c: any) => ({
            estimateQuantityId: c.estimateQuantityId,
            priceStation: c.priceStation,
          })) || [],
        subStation:
          station.subStation?.map((sub: any) => ({
            estimateQuantityStationItemId: sub.estimateQuantityStationItemId,
            subCost:
              sub.subCost?.map((sc: any) => ({
                estimateQuantityId: sc.estimateQuantityId,
                rawPrice: sc.rawPrice ?? 0,
                priceCostResult: sc.priceCostResult ?? 0,
                costType: sc.costType ?? 0,
                ...(sc.description ? { description: sc.description } : {}),
              })) || [],
          })) || [],
      })) || [],
  };
};

// Helper function to find station in the new data structure
export const findStationInEstimateData = (
  estimateData: any,
  stationId: number
) => {
  const currentProduct = estimateData.estimateProducts?.find((product: any) =>
    product.station?.some((s: any) => s.estimateQuantityStationId === stationId)
  );

  if (!currentProduct) return null;

  return currentProduct.station.find(
    (s: any) => s.estimateQuantityStationId === stationId
  );
};
