// export type AsideMenuType = Array<{
//   group: string;
//   list: Array<{
//     name: string;
//     url: string;
//     icon: React.ReactNode;
//     subMenu?: Array<{
//       name: string;
//       url: string;
//       urlGroups?: string[];
//     }>;
//   }>;
// }>;
export type userMenuListType = Array<{
  name: string;
  icon: string;
  url: string;
}>;
export const userMenuList: userMenuListType = [
  {
    name: 'ข้อมูลส่วนตัว',
    icon: '/icons/aside/user-menu/icon-user-data.svg',
    url: '/personal-information',
  },
  {
    name: 'ประวัติการใช้งานระบบ',
    icon: '/icons/aside/user-menu/icon-history.svg',
    url: '',
  },
  {
    name: 'แพ็กเกจและการชำระเงิน',
    icon: '/icons/aside/user-menu/icon-paid.svg',
    url: '',
  },
  {
    name: 'ตั้งค่า',
    icon: '/icons/aside/user-menu/icon-setting.svg',
    url: '',
  },
  {
    name: 'ช่วยเหลือ',
    icon: '/icons/aside/user-menu/icon-help.svg',
    url: '',
  },
];
