// Export all motion configurations and utilities
export * from './motion-config';
export * from './accessibility';
export * from './type-utils';

// Re-export commonly used types from framer-motion
export type {
  Variants,
  Transition,
  HTMLMotionProps,
  MotionProps as FramerMotionProps,
  AnimationControls,
  TargetAndTransition,
  VariantLabels,
  CustomValueType,
} from 'framer-motion';

// Export aside menu specific types
export type {
  ServiceItem,
  ServiceGroup,
  ServiceType,
  AsideMenuMotionProps,
  AsideMenuGroupProps,
  AsideMenuItemProps,
  SubmenuAnimationState,
  MenuComponentType,
  EnhancedMotionProps as AsideEnhancedMotionProps,
  MenuKeyboardState,
  MenuAccessibilityProps,
  FullMenuItemProps,
  AnimationPerformanceOptions,
  MotionConfigWithPerformance,
  ExtractMotionProps,
} from '@/types/aside-menu';

// Export motion-enhanced types
export type {
  MotionServiceItem,
  MotionServiceGroup,
  MotionServiceType,
  AsideMenuMotionConfig,
  AsideMenuAnimationEvents,
  EnhancedAsideMenuGroupProps,
  EnhancedAsideMenuItemProps,
  AsideMenuMotionContext,
  UseAsideMenuMotionReturn,
  AsideMenuComponentFactory,
  AnimationPerformanceMetrics,
  AnimationPerformanceMonitor,
  AsideMenuMotionComponent,
  WithMotionEnhancement,
} from '@/types/aside-menu-motion';
