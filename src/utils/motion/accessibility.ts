import { useEffect, useState, useRef } from 'react';

/**
 * Hook to detect if user prefers reduced motion
 * Returns true if user has set prefers-reduced-motion: reduce
 */
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    // Set initial value
    setPrefersReducedMotion(mediaQuery.matches);

    // Listen for changes
    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    // Add event listener
    mediaQuery.addEventListener('change', handleChange);

    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return prefersReducedMotion;
};

/**
 * Synchronous function to check reduced motion preference
 * Use this for immediate checks without React hooks
 */
export const checkReducedMotion = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Helper to get appropriate animation duration based on reduced motion preference
 */
export const getAnimationDuration = (
  normalDuration: number,
  reducedDuration: number = 0
): number => {
  return checkReducedMotion() ? reducedDuration : normalDuration;
};

/**
 * Helper to conditionally apply animation properties
 */
export const conditionalAnimation = <T>(
  animationProps: T,
  fallbackProps: Partial<T> = {}
): T | Partial<T> => {
  return checkReducedMotion() ? fallbackProps : animationProps;
};

/**
 * CSS-in-JS helper for reduced motion media queries
 */
export const reducedMotionMediaQuery = '(prefers-reduced-motion: reduce)';

/**
 * Helper function to create CSS with reduced motion support
 */
export const withReducedMotion = (
  normalStyles: string,
  reducedStyles: string = ''
): string => {
  return `
    ${normalStyles}
    
    @media ${reducedMotionMediaQuery} {
      ${reducedStyles}
    }
  `;
};

/**
 * Hook for animation cleanup on component unmount
 * Provides a cleanup function that can be used in useEffect
 */
export const useAnimationCleanup = () => {
  const cleanupRef = useRef<(() => void)[]>([]);

  const addCleanup = (cleanup: () => void) => {
    cleanupRef.current.push(cleanup);
  };

  const runCleanup = () => {
    cleanupRef.current.forEach((cleanup) => cleanup());
    cleanupRef.current = [];
  };

  useEffect(() => {
    return () => {
      runCleanup();
    };
  }, []);

  return { addCleanup, runCleanup };
};

/**
 * Keyboard navigation helper for menu components
 */
export interface KeyboardNavigationProps {
  onEnter?: () => void;
  onSpace?: () => void;
  onArrowDown?: () => void;
  onArrowUp?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEscape?: () => void;
  onTab?: (event: React.KeyboardEvent) => void;
  onHome?: () => void;
  onEnd?: () => void;
}

/**
 * Enhanced keyboard navigation with type safety
 */
export interface EnhancedKeyboardNavigationProps
  extends KeyboardNavigationProps {
  disabled?: boolean;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  customKeyHandlers?: Record<string, (event: React.KeyboardEvent) => void>;
}

/**
 * Keyboard navigation return type
 */
export interface KeyboardNavigationReturn {
  handleKeyDown: (event: React.KeyboardEvent) => void;
  isNavigationActive: boolean;
}

export const useKeyboardNavigation = (
  props: EnhancedKeyboardNavigationProps
): KeyboardNavigationReturn => {
  const [isNavigationActive, setIsNavigationActive] = useState(false);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (props.disabled) return;

    setIsNavigationActive(true);

    // Handle custom key handlers first
    if (props.customKeyHandlers?.[event.key]) {
      props.customKeyHandlers[event.key](event);
      return;
    }

    const shouldPreventDefault = props.preventDefault ?? true;
    const shouldStopPropagation = props.stopPropagation ?? false;

    switch (event.key) {
      case 'Enter':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onEnter?.();
        break;
      case ' ':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onSpace?.();
        break;
      case 'ArrowDown':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onArrowDown?.();
        break;
      case 'ArrowUp':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onArrowUp?.();
        break;
      case 'ArrowLeft':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onArrowLeft?.();
        break;
      case 'ArrowRight':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onArrowRight?.();
        break;
      case 'Escape':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onEscape?.();
        break;
      case 'Home':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onHome?.();
        break;
      case 'End':
        if (shouldPreventDefault) event.preventDefault();
        if (shouldStopPropagation) event.stopPropagation();
        props.onEnd?.();
        break;
      case 'Tab':
        props.onTab?.(event);
        break;
      default:
        break;
    }
  };

  // Reset navigation active state after a delay
  useEffect(() => {
    if (isNavigationActive) {
      const timer = setTimeout(() => setIsNavigationActive(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isNavigationActive]);

  return { handleKeyDown, isNavigationActive };
};

/**
 * Legacy hook for backward compatibility
 * @deprecated Use useKeyboardNavigation with EnhancedKeyboardNavigationProps instead
 */
export const useBasicKeyboardNavigation = (props: KeyboardNavigationProps) => {
  return useKeyboardNavigation(props);
};

/**
 * Focus management utilities for accessible menu navigation
 */
export const focusManagement = {
  /**
   * Set focus to the first focusable element within a container
   */
  focusFirst: (container: HTMLElement | null) => {
    if (!container) return;

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    firstElement?.focus();
  },

  /**
   * Set focus to the last focusable element within a container
   */
  focusLast: (container: HTMLElement | null) => {
    if (!container) return;

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;
    lastElement?.focus();
  },

  /**
   * Get all focusable elements within a container
   */
  getFocusableElements: (container: HTMLElement | null): HTMLElement[] => {
    if (!container) return [];

    const elements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    return Array.from(elements) as HTMLElement[];
  },
};
