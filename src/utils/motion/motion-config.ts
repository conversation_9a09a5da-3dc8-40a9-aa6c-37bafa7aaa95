import { Variants, Transition, HTMLMotionProps } from 'framer-motion';

// TypeScript interfaces for animation configurations
export interface SpringConfig {
  type: 'spring';
  stiffness: number;
  damping: number;
  mass: number;
}

/**
 * Enhanced spring configuration with additional options
 */
export interface EnhancedSpringConfig extends SpringConfig {
  bounce?: number;
  duration?: number;
  velocity?: number;
}

/**
 * Animation state definitions for different menu components
 */
export interface AnimationStates {
  initial: Record<string, any>;
  animate: Record<string, any>;
  exit: Record<string, any>;
  hover?: Record<string, any>;
  tap?: Record<string, any>;
  focus?: Record<string, any>;
}

/**
 * Comprehensive aside menu animation configuration
 */
export interface AsideMenuAnimationConfig {
  spring: SpringConfig;
  enhancedSpring: EnhancedSpringConfig;
  menuGroup: AnimationStates;
  menuItem: AnimationStates;
  submenu: AnimationStates;
  expandIcon: {
    collapsed: { rotate: number };
    expanded: { rotate: number };
  };
  staggerChildren: {
    delayChildren: number;
    staggerChildren: number;
  };
  serviceGroupName: AnimationStates;
  menuIcon: AnimationStates;
}

/**
 * Enhanced motion props with better type safety
 */
export interface EnhancedMotionProps
  extends Omit<HTMLMotionProps<'div'>, 'children'> {
  variants?: Variants;
  transition?: Transition;
  prefersReducedMotion?: boolean;
  performanceOptimized?: boolean;
}

/**
 * Motion props with component-specific configurations
 */
export interface ComponentMotionProps {
  [componentType: string]: EnhancedMotionProps;
}

/**
 * Type-safe motion configuration for different component types
 */
export type MotionComponentType =
  | 'menuGroup'
  | 'menuItem'
  | 'submenu'
  | 'submenuItem'
  | 'expandIcon'
  | 'serviceGroupName'
  | 'menuIcon';

/**
 * Motion configuration factory type
 */
export interface MotionConfigFactory {
  getConfig: (
    componentType: MotionComponentType,
    options?: {
      prefersReducedMotion?: boolean;
      customTransition?: Transition;
      customVariants?: Variants;
    }
  ) => EnhancedMotionProps;
}

/**
 * Performance optimization configuration
 */
export interface PerformanceConfig {
  willChange: string;
  backfaceVisibility: 'visible' | 'hidden';
  perspective: number;
  transform3d: boolean;
}

/**
 * Legacy MotionProps interface for backward compatibility
 * @deprecated Use EnhancedMotionProps instead
 */
export interface MotionProps {
  initial?: string | object | boolean;
  animate?: string | object | boolean;
  exit?: string | object | boolean;
  variants?: Variants;
  transition?: object;
  [key: string]: any; // Allow additional motion props
}

// Aside menu motion configurations
export const asideMenuAnimationConfig: AsideMenuAnimationConfig = {
  spring: {
    type: 'spring',
    stiffness: 300,
    damping: 30,
    mass: 1,
  },
  enhancedSpring: {
    type: 'spring',
    stiffness: 300,
    damping: 30,
    mass: 1,
    bounce: 0.2,
    velocity: 0,
  },
  menuGroup: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    hover: { scale: 1.01 },
  },
  menuItem: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
    hover: { scale: 1.02 },
    tap: { scale: 0.98 },
    focus: { scale: 1.01, outline: '2px solid #455a64' },
  },
  submenu: {
    initial: { height: 0, opacity: 0 },
    animate: {
      height: 'auto',
      opacity: 1,
      transition: {
        height: { duration: 0.3, delay: 0 },
        opacity: { duration: 0.3, delay: 0.2 },
      },
    },
    exit: {
      height: 0,
      opacity: 0,
      transition: {
        opacity: { duration: 0.3, delay: 0 },
        height: { duration: 0.3, delay: 0.2 },
      },
    },
  },
  expandIcon: {
    collapsed: { rotate: 0 },
    expanded: { rotate: 90 },
  },
  staggerChildren: {
    delayChildren: 0.1,
    staggerChildren: 0.05,
  },
  serviceGroupName: {
    initial: { opacity: 0, x: -10 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -10 },
  },
  menuIcon: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    hover: { scale: 1.1 },
  },
};

// Variants for different aside menu components
export const asideMenuGroupVariants: Variants = {
  initial: asideMenuAnimationConfig.menuGroup.initial,
  animate: asideMenuAnimationConfig.menuGroup.animate,
  exit: asideMenuAnimationConfig.menuGroup.exit,
};

export const asideMenuItemVariants: Variants = {
  initial: asideMenuAnimationConfig.menuItem.initial,
  animate: asideMenuAnimationConfig.menuItem.animate,
  exit: asideMenuAnimationConfig.menuItem.exit,
};

export const asideSubmenuVariants: Variants = {
  initial: {
    height: 0,
    opacity: 0,
    x: -30,
    scale: 0.95,
  },
  animate: {
    height: 'auto',
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      height: { duration: 0.4, delay: 0, ease: 'easeOut' },
      opacity: { duration: 0.3, delay: 0.1 },
      x: { duration: 0.4, delay: 0.05 },
      scale: { duration: 0.3, delay: 0.1 },
    },
  },
  exit: {
    height: 0,
    opacity: 0,
    x: -30,
    scale: 0.95,
    transition: {
      opacity: { duration: 0.2, delay: 0 },
      height: { duration: 0.3, delay: 0.1 },
      x: { duration: 0.2, delay: 0 },
      scale: { duration: 0.2, delay: 0 },
    },
  },
};

export const asideSubmenuContainerVariants: Variants = {
  initial: {},
  animate: {
    transition: asideMenuAnimationConfig.staggerChildren,
  },
  exit: {},
};

export const asideSubmenuItemVariants: Variants = {
  initial: {
    opacity: 0,
    x: -30,
    y: -10,
    scale: 0.8,
  },
  animate: (index: number) => ({
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
    transition: {
      delay: index * 0.1,
      duration: 0.4,
      type: 'spring',
      stiffness: 200,
      damping: 20,
      mass: 0.8,
    },
  }),
  exit: {
    opacity: 0,
    x: -20,
    y: -5,
    scale: 0.9,
    transition: {
      duration: 0.3,
      ease: 'easeInOut',
    },
  },
};

export const asideExpandIconVariants: Variants = {
  collapsed: asideMenuAnimationConfig.expandIcon.collapsed,
  expanded: asideMenuAnimationConfig.expandIcon.expanded,
};

export const asideServiceGroupNameVariants: Variants = {
  initial: asideMenuAnimationConfig.serviceGroupName.initial,
  animate: asideMenuAnimationConfig.serviceGroupName.animate,
  exit: asideMenuAnimationConfig.serviceGroupName.exit,
};

export const asideMenuIconVariants: Variants = {
  initial: asideMenuAnimationConfig.menuIcon.initial,
  animate: asideMenuAnimationConfig.menuIcon.animate,
  exit: asideMenuAnimationConfig.menuIcon.exit,
  hover: asideMenuAnimationConfig.menuIcon.hover || { scale: 1.1 },
};

export const motionListConfig = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: {
    type: 'spring',
    stiffness: 100,
    damping: 4.5,
    mass: 0.2,
  },
};

export const motionFadeConfig = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: { duration: 0.3 },
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.3 },
  },
};

export const motionFadeDelayConfig = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: { opacity: { duration: 0.3, delay: 0.6 } },
  },
  exit: {
    opacity: 0,
    transition: { opacity: { duration: 0.3, delay: 0 } },
  },
};

export const motionColorPickerConfig = {
  initial: { opacity: 0, scaleY: 0.9 },
  animate: { opacity: 1, scaleY: 1 },
  exit: { opacity: 0, scaleY: 0.9 },
  transition: {
    type: 'spring',
    stiffness: 100,
    damping: 4.5,
    mass: 0.2,
  },
};

export const motionRowsTableCalculate = {
  initial: {
    opacity: 0,
    height: 0,
    marginTop: 0,
  },
  animate: {
    opacity: 1,
    height: 'auto',
    marginTop: '12px',
    transition: {
      opacity: { duration: 0.3, delay: 0.3 },
      height: { duration: 0.3, delay: 0 },
      marginTop: { duration: 0.3, delay: 0 },
    },
  },
  exit: {
    opacity: 0,
    height: 0,
    marginTop: 0,
    transition: {
      opacity: { duration: 0.3 },
      height: { duration: 0.3, delay: 0.3 },
      marginTop: { duration: 0.3, delay: 0.3 },
    },
  },
};

export const motionListItemConfig = {
  initial: { opacity: 0, height: 0, marginBottom: 0 },
  animate: {
    opacity: 1,
    height: 'auto',
    marginBottom: '24px',
    transition: {
      opacity: { duration: 0.3, delay: 0.3 },
      height: { duration: 0.3, delay: 0 },
      marginBottom: { duration: 0.3, delay: 0 },
    },
  },
  exit: {
    opacity: 0,
    height: 0,
    marginBottom: 0,
    transition: {
      opacity: { duration: 0.3, delay: 0 },
      height: { duration: 0.3, delay: 0.3 },
      marginBottom: { duration: 0.3, delay: 0.3 },
    },
  },
};

export const motionBadgeConfig = {
  initial: { opacity: 0, scale: 0.8 },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 10,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.2,
      ease: 'easeInOut',
    },
  },
};

export const motionProgress: Variants = {
  initial: { opacity: 0, width: 0 },
  animate: (customWidth: number) => ({
    opacity: 1,
    width: `${customWidth}%`,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  }),
  exit: {
    opacity: 0,
    backgroundColor: '#8fff995c',
    transition: {
      opacity: { duration: 0.3, delay: 0.3 },
      backgroundColor: { duration: 0.1, delay: 0 },
    },
  },
};

/**
 * Motion configuration factory for creating type-safe motion props
 */
export const createMotionConfigFactory = (): MotionConfigFactory => ({
  getConfig: (componentType, options = {}) => {
    const { prefersReducedMotion, customTransition, customVariants } = options;

    return getAsideMenuMotionProps(componentType, prefersReducedMotion, {
      transition: customTransition,
      variants: customVariants,
      includeHoverEffects: !prefersReducedMotion,
    });
  },
});

/**
 * Default motion configuration factory instance
 */
export const motionConfigFactory = createMotionConfigFactory();
// Performance optimization styles for animations
export const animationPerformanceStyles = {
  willChange: 'transform, opacity, height',
  backfaceVisibility: 'hidden' as const,
  perspective: 1000,
} as const;

// Helper function to get motion props based on reduced motion preference
export const getMotionProps = (
  variants: Variants,
  transition?: Transition,
  prefersReducedMotion?: boolean
): EnhancedMotionProps => {
  if (prefersReducedMotion) {
    return {
      style: {}, // No performance optimizations needed for static elements
      prefersReducedMotion: true,
    };
  }

  return {
    initial: 'initial' as const,
    animate: 'animate' as const,
    exit: 'exit' as const,
    variants,
    transition: transition || asideMenuAnimationConfig.spring,
    style: animationPerformanceStyles,
    prefersReducedMotion: false,
    performanceOptimized: true,
  };
};

// Helper function specifically for aside menu components
export const getAsideMenuMotionProps = (
  componentType: MotionComponentType,
  prefersReducedMotion?: boolean,
  customOptions?: {
    transition?: Transition;
    variants?: Variants;
    includeHoverEffects?: boolean;
  }
): EnhancedMotionProps => {
  if (prefersReducedMotion) {
    return {
      style: {}, // No performance optimizations needed for static elements
      prefersReducedMotion: true,
      performanceOptimized: false,
    };
  }

  const baseProps: EnhancedMotionProps = {
    style: animationPerformanceStyles,
    prefersReducedMotion: false,
    performanceOptimized: true,
  };

  const transition =
    customOptions?.transition || asideMenuAnimationConfig.spring;
  const includeHoverEffects = customOptions?.includeHoverEffects ?? true;

  switch (componentType) {
    case 'menuGroup':
      return {
        ...baseProps,
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideMenuGroupVariants,
        transition,
        ...(includeHoverEffects && { whileHover: 'hover' }),
      };
    case 'menuItem':
      return {
        ...baseProps,
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideMenuItemVariants,
        transition,
        ...(includeHoverEffects && {
          whileHover: 'hover',
          whileTap: 'tap',
          whileFocus: 'focus',
        }),
      };
    case 'submenu':
      return {
        ...baseProps,
        style: {
          ...animationPerformanceStyles,
          overflow: 'hidden', // Specific to submenu for height animations
        },
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideSubmenuVariants,
        transition,
      };
    case 'submenuItem':
      return {
        ...baseProps,
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideSubmenuItemVariants,
        transition,
      };
    case 'expandIcon':
      return {
        ...baseProps,
        variants: customOptions?.variants || asideExpandIconVariants,
        transition,
      };
    case 'serviceGroupName':
      return {
        ...baseProps,
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideServiceGroupNameVariants,
        transition: {
          ...transition,
          delay: 0.1, // Slight delay for service group name animation
        },
      };
    case 'menuIcon':
      return {
        ...baseProps,
        initial: 'initial' as const,
        animate: 'animate' as const,
        exit: 'exit' as const,
        variants: customOptions?.variants || asideMenuIconVariants,
        transition: {
          ...transition,
          stiffness: 400, // Slightly more responsive for icons
          damping: 25,
          mass: 0.8,
        },
        ...(includeHoverEffects && { whileHover: 'hover' }),
      };
    default:
      return {
        style: {},
        prefersReducedMotion: false,
        performanceOptimized: false,
      };
  }
};
