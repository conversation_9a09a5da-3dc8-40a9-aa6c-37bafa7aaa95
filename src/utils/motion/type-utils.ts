import { Variants, Transition } from 'framer-motion';
import { EnhancedMotionProps, MotionComponentType } from './motion-config';
import { AsideMenuMotionProps } from '@/types/aside-menu';

/**
 * Type utility to extract motion-related props from component props
 */
export const extractMotionProps = <T extends Record<string, any>>(
  props: T,
  motionKeys: (keyof T)[] = [
    'variants',
    'transition',
    'initial',
    'animate',
    'exit',
    'whileHover',
    'whileTap',
  ]
): {
  motionProps: Partial<EnhancedMotionProps>;
  otherProps: Omit<T, keyof EnhancedMotionProps>;
} => {
  const motionProps: Partial<EnhancedMotionProps> = {};
  const otherProps = { ...props };

  motionKeys.forEach((key) => {
    if (key in props) {
      (motionProps as any)[key] = props[key];
      delete (otherProps as any)[key];
    }
  });

  return { motionProps, otherProps };
};

/**
 * Type guard to check if an object contains valid motion props
 */
export const hasValidMotionProps = (obj: any): obj is AsideMenuMotionProps => {
  if (!obj || typeof obj !== 'object') return false;

  const motionPropKeys = [
    'variants',
    'transition',
    'initial',
    'animate',
    'exit',
  ];
  return motionPropKeys.some((key) => key in obj);
};

/**
 * Validates that variants object has required animation states
 */
export const validateVariants = (
  variants: Variants,
  requiredStates: string[] = ['initial', 'animate', 'exit']
): boolean => {
  if (!variants || typeof variants !== 'object') return false;

  return requiredStates.every((state) => state in variants);
};

/**
 * Validates transition configuration
 */
export const validateTransition = (transition: Transition): boolean => {
  if (!transition || typeof transition !== 'object') return false;

  // Check for spring transition
  if ('type' in transition && transition.type === 'spring') {
    return (
      'stiffness' in transition &&
      typeof transition.stiffness === 'number' &&
      'damping' in transition &&
      typeof transition.damping === 'number'
    );
  }

  // Check for tween transition
  if ('type' in transition && transition.type === 'tween') {
    return 'duration' in transition && typeof transition.duration === 'number';
  }

  return true; // Allow other transition types
};

/**
 * Creates a type-safe motion props object with validation
 */
export const createSafeMotionProps = (
  componentType: MotionComponentType,
  customProps?: Partial<EnhancedMotionProps>
): EnhancedMotionProps => {
  const baseProps: EnhancedMotionProps = {
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
  };

  if (customProps?.variants && !validateVariants(customProps.variants)) {
    console.warn(
      `Invalid variants provided for ${componentType}. Using default variants.`
    );
    delete customProps.variants;
  }

  if (customProps?.transition && !validateTransition(customProps.transition)) {
    console.warn(
      `Invalid transition provided for ${componentType}. Using default transition.`
    );
    delete customProps.transition;
  }

  return {
    ...baseProps,
    ...customProps,
  };
};

/**
 * Merges multiple motion prop objects with proper type safety
 */
export const mergeMotionProps = (
  ...propObjects: (Partial<EnhancedMotionProps> | undefined)[]
): EnhancedMotionProps => {
  const merged: EnhancedMotionProps = {};

  propObjects.forEach((props) => {
    if (props && hasValidMotionProps(props)) {
      Object.assign(merged, props);
    }
  });

  return merged;
};

/**
 * Type utility for component prop interfaces that include motion props
 */
export type WithMotionProps<T> = T & {
  motionProps?: AsideMenuMotionProps;
};

/**
 * Type utility for extracting non-motion props from component props
 */
export type WithoutMotionProps<T> = Omit<
  T,
  keyof AsideMenuMotionProps | 'motionProps'
>;

/**
 * Helper to create component props with optional motion props
 */
export const withMotionProps = <T>(
  baseProps: T,
  motionProps?: AsideMenuMotionProps
): WithMotionProps<T> => ({
  ...baseProps,
  ...(motionProps && { motionProps }),
});
