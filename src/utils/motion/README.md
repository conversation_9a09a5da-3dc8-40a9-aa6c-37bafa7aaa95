# Motion Configuration Utilities

This directory contains centralized animation configurations and utilities for the aside menu components using Framer Motion with spring effects.

## Files Overview

- `motion-config.ts` - Main configuration file with spring settings and animation variants
- `accessibility.ts` - Accessibility utilities for reduced motion preferences
- `index.ts` - Main export file for easy importing
- `examples.tsx` - Usage examples for all motion components
- `__tests__/` - Test files for all utilities

## Key Features

### 🌊 Spring-Based Animations

- Natural, physics-based motion using Framer Motion springs
- Consistent animation timing across all components
- Optimized spring configurations for UI elements

### ♿ Accessibility Support

- Automatic detection of `prefers-reduced-motion` system setting
- Graceful fallbacks when animations are disabled
- Hooks and utilities for accessibility-aware animations

### 🎯 TypeScript Support

- Full TypeScript interfaces for all animation configurations
- Type-safe motion props and variants
- Intellisense support for all utilities

## Usage

### Basic Import

```typescript
import {
  asideMenuAnimationConfig,
  getAsideMenuMotionProps,
  useReducedMotion,
} from '@/utils/motion';
```

### Using Motion Props

```typescript
import { motion } from 'framer-motion';
import { getAsideMenuMotionProps, useReducedMotion } from '@/utils/motion';

const MyComponent = () => {
  const prefersReducedMotion = useReducedMotion();
  const motionProps = getAsideMenuMotionProps(
    'menuGroup',
    prefersReducedMotion
  );

  return <motion.div {...motionProps}>Content here</motion.div>;
};
```

### Accessibility-Aware Animations

```typescript
import { useReducedMotion, conditionalAnimation } from '@/utils/motion';

const MyComponent = () => {
  const prefersReducedMotion = useReducedMotion();

  const animationProps = conditionalAnimation(
    {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      transition: { type: 'spring', stiffness: 300 },
    },
    {
      // Fallback for reduced motion
      initial: { opacity: 1, scale: 1 },
      animate: { opacity: 1, scale: 1 },
    }
  );

  return <motion.div {...animationProps}>Content</motion.div>;
};
```

## Animation Configurations

### Spring Settings

```typescript
{
  type: 'spring',
  stiffness: 300,  // Responsive feel without being bouncy
  damping: 30,     // Smooth settling without oscillation
  mass: 1          // Standard mass for UI elements
}
```

### Available Component Types

- `menuGroup` - For AsideMenuGroup components
- `menuItem` - For individual menu items
- `submenu` - For submenu containers
- `submenuItem` - For individual submenu items
- `expandIcon` - For expand/collapse icons

### Animation Variants

#### Menu Group

- **Initial**: `{ opacity: 0, x: -20 }`
- **Animate**: `{ opacity: 1, x: 0 }`
- **Exit**: `{ opacity: 0, x: -20 }`

#### Menu Item

- **Initial**: `{ opacity: 0, scale: 0.95 }`
- **Animate**: `{ opacity: 1, scale: 1 }`
- **Exit**: `{ opacity: 0, scale: 0.95 }`

#### Submenu

- **Initial**: `{ height: 0, opacity: 0 }`
- **Animate**: `{ height: 'auto', opacity: 1 }`
- **Exit**: `{ height: 0, opacity: 0 }`

#### Expand Icon

- **Collapsed**: `{ rotate: 0 }`
- **Expanded**: `{ rotate: 90 }`

## Accessibility Features

### Reduced Motion Detection

The utilities automatically detect when a user has enabled `prefers-reduced-motion: reduce` in their system settings and provide appropriate fallbacks.

### Available Accessibility Utilities

#### `useReducedMotion()`

React hook that returns `true` if the user prefers reduced motion.

#### `checkReducedMotion()`

Synchronous function to check reduced motion preference without React hooks.

#### `getAnimationDuration(normalDuration, reducedDuration?)`

Helper to get appropriate animation duration based on user preference.

#### `conditionalAnimation(animationProps, fallbackProps?)`

Helper to conditionally apply animation properties based on reduced motion preference.

#### `withReducedMotion(normalStyles, reducedStyles?)`

CSS-in-JS helper for creating styles with reduced motion media queries.

## Testing

Run the tests with:

```bash
npm test src/utils/motion
```

The test suite covers:

- All animation configurations
- Motion prop generation
- Accessibility utilities
- Edge cases and error handling

## Performance Considerations

- Animations use `will-change` CSS property for optimization
- Spring configurations are tuned for 60fps performance
- Reduced motion fallbacks prevent unnecessary calculations
- Animation cleanup is handled automatically

## Browser Support

- Modern browsers with CSS `prefers-reduced-motion` support
- Graceful degradation for older browsers
- Server-side rendering compatible

## Migration from CSS Transitions

When migrating from CSS transitions to these motion utilities:

1. Replace styled-components with `motion` components
2. Use `getAsideMenuMotionProps()` for consistent configuration
3. Add `AnimatePresence` for enter/exit animations
4. Test with reduced motion preferences enabled

## Examples

See `examples.tsx` for complete usage examples of all motion components.
