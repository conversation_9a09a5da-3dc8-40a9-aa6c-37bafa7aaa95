import moment from 'moment';
import 'moment/locale/th';
import dayjs from 'dayjs';

export const dateThaiFormat = (date: string, isNoTime?: boolean) => {
  const dayText = moment(date).format('DD');
  const monthTextThai = moment(date).locale('th').format('MMM');
  const yearThaiText = moment(date).add(543, 'years').format('YYYY');
  const timeText = moment(date).format('HH:mm น.');
  if (isNoTime) return `${dayText} ${monthTextThai} ${yearThaiText}`;
  return `${dayText} ${monthTextThai} ${yearThaiText}, ${timeText}`;
};
export const convertDateFormat = (date: any, isNumber?: boolean): any => {
  const formatDate = 'DD/MM/YYYY';
  if (isNumber)
    return Number(dayjs.utc(date).tz('Asia/Bangkok').format('HH.mm'));
  return dayjs.utc(date).tz('Asia/Bangkok').format(formatDate);
};
export const dateStringDayNow = (valueDate: any) => {
  const date = new Date();
  const dataDate = {
    selectDate: convertDateFormat(valueDate),
    today: convertDateFormat(date),
    tomorrow: convertDateFormat(date.setDate(date.getDate() + 1)),
    yesterday: convertDateFormat(date.setDate(date.getDate() - 2)),
  };
  switch (dataDate.selectDate) {
    case dataDate.today:
      return 'วันนี้';
    case dataDate.tomorrow:
      return 'พรุ่งนี้';
    case dataDate.yesterday:
      return 'เมื่อวาน';
    default:
      return '';
  }
};
