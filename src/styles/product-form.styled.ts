import styled from 'styled-components';

export const AttributeHeaderStyle = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-top: 15px;
  h2 {
    margin: 0;
    font-weight: 500;
    font-size: 1.2em;
  }
  button {
    border-radius: 8px;
  }
`;

export const AttributeListStyle = styled.div`
  display: flex;
  flex-direction: column;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;

export const AttributeItemStyle = styled.div`
  padding: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #dbe2e5;
  &:last-child {
    border-bottom: none;
  }
  svg {
    opacity: 0.5;
  }
`;

export const AttributeDialogStyle = styled.div`
  width: 640px;
  max-width: 100%;
  .heading-popup {
    position: relative;
    height: 60px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    h2 {
      text-align: center;
      margin: 0;
      font-weight: 600;
      font-size: 1.3em;
    }
    .close-btn {
      right: 10px;
      top: 50%;
      position: absolute;
      transform: translateY(-50%);
    }
  }
  .form-container {
    padding: 40px;
    .image-wrap {
      display: flex;
      width: 100%;
      justify-content: center;
      column-gap: 16px;
      .group {
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        .topic {
        }
        .image {
          width: 150px !important;
          height: 150px !important;
        }
      }
    }
  }
  .image-helper {
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    p {
      padding: 0;
      margin: 0;
    }
  }

  img {
    width: 100%;
    height: 100%;
    max-width: 300px;
    max-height: 300px;
    object-fit: cover;
    border-radius: 16px;
  }
`;
