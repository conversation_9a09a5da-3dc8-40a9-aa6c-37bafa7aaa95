import styled from 'styled-components';

export const ModalTaskTimelineStyles = styled.div`
  max-height: 100vh;
  min-width: 600px;
  > header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dbe2e5;
    h3 {
      font-size: 20px;
      margin: 0;
    }
    button {
      min-width: unset;
    }
  }
  > div {
    border-top: 5px solid #f5f7f8;
    background: #fff;
    border-bottom: 1px solid #dbe2e5;
    padding: 2rem;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 18px;
        font-weight: bold;
      }
    }
    .img-process {
      max-width: 100%;
      img {
        width: 100%;
      }
    }
    .box-detail-process {
      button {
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        background: #fff;
        box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.08);
      }
      .detail {
        padding-top: 1rem;
        height: 0 !important;
        overflow: hidden;
        &.show {
          height: unset !important;
        }
        .profile {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          p {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
          }
          span {
            font-size: 14px;
          }
        }
        ul {
          margin-top: 1rem;
          li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            line-height: 10px;
            > div {
              display: flex;
              align-items: center;
              gap: 0.2rem;
              p {
                min-width: 120px;
              }
            }

            span {
              font-weight: bold;
              &.not-found {
                color: #cfd8dc;
              }
            }
          }
        }
        .status,
        .responsible-person {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          p {
            min-width: 143px;
          }
          > .status-text {
            display: flex;
            align-items: center;
            border-radius: 20px;
            background: #fff5d3;
            color: #f9a925;
            padding: 5px 10px;
          }
          .imgae-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }
        }
      }
    }
    &.box-process {
      border-top: unset !important;
    }
    &.box-amount {
      .details {
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        margin-top: 1rem;
        > .MuiGrid-root {
          margin: 0;
          width: auto;
          .MuiGrid-item {
            padding: 0;
            border: 1px solid #dbe2e5;
            &:first-child {
              border-top: 0;
              border-left: 0;
            }
            &:nth-child(2) {
              border-top: 0;
              border-left: 0;
              border-right: 0;
            }
            &:nth-child(3) {
              border-top: 0;
              border-bottom: 0;
              border-left: 0;
            }
            &:last-child {
              border-top: 0;
              border-left: 0;
              border-bottom: 0;
            }
          }
          .item {
            padding: 1rem;
            > div {
              display: flex;
              align-items: center;
              gap: 0.2rem;
            }
            p {
              margin: 0;
              font-size: 18px;
              font-weight: bold;
            }
          }
        }
      }
      .products {
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        margin-top: 1rem;
        .item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 10px;
          border-bottom: 1px solid #dbe2e5;
          &:last-child {
            border-bottom: none;
          }
          > div {
            p {
              margin: 0;
              font-weight: bold;
            }
            span {
              font-size: 12px;
            }
          }
        }
      }
    }
    &.box-work {
      .box-work-time {
        border: 1px solid #dbe2e5;
        border-radius: 16px !important;
        overflow: hidden;
        .MuiPaper-root {
          margin: 0 !important;
          border-radius: 0 !important;
          box-shadow: none !important;
          &.Mui-expanded {
            margin: 0 !important;
          }
          .MuiAccordionSummary-root {
            .MuiAccordionSummary-expandIconWrapper {
              &.Mui-expanded {
                svg {
                  color: #263238;
                }
              }
              svg {
                color: #dbe2e5;
              }
            }
            header {
              padding: 0;
              border: 0;
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              > div {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 0.5rem;
                p {
                  margin: 0;
                  font-weight: 600;
                }
              }
              .time {
                border-radius: 40px;
                background: #f5f7f8;
                padding: 0.4rem 0.8rem;
                font-weight: 600;
              }
            }
          }
          .MuiAccordionDetails-root {
            background: #f5f7f8;
            padding: 16px !important;
            .details {
              display: flex;
              align-items: start;
              gap: 0.5rem;
              .point {
                position: relative;
                min-width: 30px;
                min-height: 100px !important;
                display: flex;
                align-items: end;
                justify-content: center;
                .dot {
                  position: absolute;
                  top: 8px;
                  left: 0;
                  right: 0;
                  width: 8px;
                  height: 8px;
                  background: #dbe2e5;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: auto;
                }
                .line {
                  width: 1px;
                  height: 100%;
                  min-height: 80px;
                  background: #dbe2e5;
                }
              }
              .work-list {
                .date {
                  border: 1px solid #dbe2e5;
                  background: #fff;
                  border-radius: 30px;
                  width: fit-content;
                  padding: 0.1rem 0.5rem;
                  margin-bottom: 0.8rem;
                }
                ul {
                  list-style: none;
                  padding: 0;
                  padding-left: 0.7rem;
                  margin: 0;
                  display: flex;
                  flex-direction: column;
                  gap: 0.3rem;
                  li {
                    span {
                      color: #000 !important;
                    }
                    &.text-red {
                      color: #d32f2f;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    &.box-process-production {
      border-bottom: unset !important;
      header {
        flex-direction: column;
        align-items: start;
        p {
          margin: 0;
        }
      }
      > div {
        display: flex;
        align-items: center;
        justify-content: start;
        .MuiTimelineItem-root {
          .MuiTimelineConnector-root {
            border-bottom: 20px;
          }
          &.done {
            .MuiTimelineConnector-root {
              background-color: #263238;
            }
          }
          &:before {
            content: unset;
          }
          &.loading {
            .MuiTypography-root {
              ul {
                margin: 1rem 0;
                margin-left: -11px;
                list-style-type: disc;
                li {
                  color: #16d5c5;

                  padding-left: 20px;
                  &::marker {
                    color: #263238;
                  }
                }
              }
            }
            .MuiTimelineDot-root {
              svg {
                font-size: 60%;
              }
            }
          }
          &.waiting {
            .MuiTimelineDot-root {
              opacity: 0.5;
              background: #fff;
            }
          }
          .MuiTimelineDot-root {
            max-width: 20px;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #263238;
            svg {
              font-size: 100%;
            }
          }
          .MuiTypography-root {
            color: #263238 !important;
          }
        }
      }
    }
  }
`;
