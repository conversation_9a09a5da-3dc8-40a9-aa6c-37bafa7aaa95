import { LoadingFadein } from '@/styles/share.styled';
import styled from 'styled-components';

export const CompanyPageStyle = styled.div`
  display: flex;
  justify-content: center;
  animation: ${LoadingFadein} 0.3s ease-in;
  align-items: center;
  height: calc(100dvh);
  overflow-y: auto;
  padding: 0 40px;
  @media screen and (max-width: 480px) {
    padding: 0 18px;
    height: calc(100vh - 80px);
  }
  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: white;
    padding: 0 0 24px;
    @media screen and (max-width: 380px) {
      flex-direction: column;
      row-gap: 8px;
    }
    .topic {
      font-weight: 600;
      font-size: 28px;
    }
    .create-btn {
      display: flex;
      align-items: center;
    }
  }

  .company {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    max-width: 85%;
    row-gap: 24px;
    width: 480px;
    .topic {
      font-size: 28px;
      font-weight: 600;
      color: #263238;
    }
    > p {
      color: #263238;
    }
    .text-zone {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 6px;
      .topic {
        font-size: 22px;
        font-weight: 600;
        text-align: center;
        color: #cfd8dc;
      }
      .description {
        font-size: 16px;
        text-align: center;
        line-height: 1.6;
        color: #cfd8dc;
      }
    }
    .text-error {
      color: #d32f2f;
    }
    fieldset {
      border: 0;
    }
  }
`;
