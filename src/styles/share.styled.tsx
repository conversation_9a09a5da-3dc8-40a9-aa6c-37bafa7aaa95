import styled, { css, keyframes } from 'styled-components';
import { Switch } from '@mui/material';
import { SwitchProps } from '@mui/material/Switch';
import { isEmpty } from 'lodash';

export const LoadingFadein = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;
export const scaleUp = keyframes`
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;
export const LoadingAsideMenuGroup = keyframes`
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;
export const AsideChangeCompany = keyframes`
  0% {
    opacity: 0;
    transform: translate(-50%,-16px);
  }
  20% {
    transform: translate(-50%,0px) scaleY(.8);
  }
  40% {
    transform: translate(-50%,-8px);
  }
  60% {
    transform: translate(-50%,0px) scaleY(.95);
  }
  80% {
    transform: translate(-50%,-4px);
  }
  100% {
    opacity: 1;
    transform: translate(-50%,0px);
  }
`;
export const FadeInStyled = styled.div`
  animation: ${LoadingFadein} 0.3s ease-in;
`;
export const PaginationStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  .MuiSelect-select {
    padding-left: 0;
  }
  .MuiPagination-ul {
    flex-wrap: nowrap !important;
    li {
      margin: 0 4px;
      height: 32px;
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      @media screen and (max-width: 767px) {
        margin: 0;
      }
      @media screen and (max-width: 600px) {
        display: none;
      }
      button {
        border: 1px solid #dbe2e5;
        border-radius: 8px;
      }
      &:first-of-type {
        margin-left: 0;
        @media screen and (max-width: 600px) {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      &:last-child {
        margin-right: 0;
        @media screen and (max-width: 600px) {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .MuiPagination-root {
    margin: 8px 0 0 0 !important;
  }

  .MuiFormControl-root {
    //margin: 8px 0 0 0;
    margin: 9px 0 0 0;
    min-width: 97px;
    @media screen and (max-width: 768px) {
      min-width: 82px;
    }
  }

  .MuiPaginationItem-page {
    @media screen and (max-width: 600px) {
      display: none;
    }
  }

  .MuiPaginationItem-ellipsis {
    @media screen and (max-width: 600px) {
      display: none;
    }
  }

  .MuiButtonBase-root {
    background: none !important;
    color: #1e1e1e;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    text-align: center;
    letter-spacing: 0.1px;
    border: none;
    font-size: 12px;
    min-width: 24px;
    height: 24px;
    @media screen and (min-width: 768px) {
      font-size: 16px;
      min-width: 32px;
      height: 32px;
    }
  }

  .Mui-selected {
    background: #1e1e1e !important;
    color: #fff !important;
    font-style: normal !important;
    font-weight: 400 !important;
    line-height: 150% !important;
    text-align: center !important;
    letter-spacing: 0.1px !important;
    font-size: 12px !important;
    min-width: 24px !important;
    height: 24px !important;
    @media screen and (min-width: 768px) {
      font-size: 16px !important;
      min-width: 32px !important;
      height: 32px !important;
    }

    :hover {
      background: rgba(30, 30, 30, 0.8) !important;
    }
  }

  @media screen and (max-width: 510px) {
    div {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .MuiOutlinedInput-notchedOutline {
    border: none;
  }

  .show-number {
    .MuiInputBase-root {
      border: 0 !important;
      box-shadow: none !important;
    }
    .select-text {
      font-size: 12px;
      font-weight: 500;
      @media screen and (min-width: 768px) {
        font-size: 16px;
      }
    }
    .show-text {
      font-size: 12px;
      @media screen and (min-width: 768px) {
        font-size: 16px;
      }
      @media screen and (max-width: 370px) {
        display: none;
      }
    }
  }
`;
export const FormModalStyle = styled.div<{
  $width?: number;
  $notTranslateXHeader?: boolean;
}>`
  ${({ $width }) => css`
    width: ${$width ? `${$width}px` : 'auto'};
  `};
  max-width: 100%;
  //max-height: 630px;
  height: 100%;
  margin-top: 32px;
  .btn-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    position: absolute;
    padding: 15px 24px 24px;
    background: #fff;
    left: 0;
    bottom: 0;
    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 20px;
      top: -20px;
      left: 0;
      background: linear-gradient(to bottom, transparent, #ffffff80, #ffffff);
    }
  }
  fieldset {
    border: 0;
  }
  .fade-in {
    animation: ${LoadingFadein} 0.3s ease-in;
  }
  .content-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .header {
      height: 56px;
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      position: absolute;
      top: 0;
      z-index: 9;
      width: 100%;
      background-color: #fff;

      box-shadow: 0 1px 0 0 #ddd;
      ${({ $notTranslateXHeader }) =>
        $notTranslateXHeader
          ? css`
              transform: translateX(0);
            `
          : css`
              transform: translateX(-24px);
            `};
      @media screen and (max-width: 400px) {
        justify-content: start;
      }
      .title {
        position: absolute;
        right: 50%;
        transform: translateX(50%);
        white-space: nowrap;
        @media screen and (max-width: 400px) {
          position: relative;
          right: initial;
          transform: none;
          margin-left: 24px;
        }
      }
      .back {
        position: absolute;
        left: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.3s;
        transform-origin: center;
        cursor: pointer;
        color: #cfd8dc;
      }
      .x-close {
        position: absolute;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.3s;
        transform-origin: center;
        cursor: pointer;
        color: #cfd8dc;
        &:hover {
          transform: rotate(90deg);
        }
        &.header-button {
          &:hover {
            transform: none;
          }
        }
      }
    }
    .border-none {
      box-shadow: none;
    }
    .form-wrap {
      height: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 24px;

      &.data-grid-row-pointer {
        .MuiDataGrid-row {
          cursor: pointer !important;
        }
      }

      form {
        height: 100%;
        display: flex;
        flex-direction: column;
        //row-gap: 24px;
        div:first-child {
          //p {
          //  margin-top: 0;
          //}
          //label {
          //  margin-top: 0;
          //}
        }
        button {
          font-weight: 400 !important;
        }
        .hr-custom {
          border-bottom: 1px solid #ddd;
          margin: 0 -24px;
        }
        .topic {
          font-size: 22px;
          font-weight: 600;
        }
        .MuiDataGrid-root {
          padding: 0 !important;
          .MuiDataGrid-columnHeaderTitle {
            font-size: 16px !important;
            font-weight: 600 !important;
          }
          span:first-child {
            div {
              width: 24px;
              height: 24px;
            }
          }
          .MuiDataGrid-columnHeaderTitleContainer {
            overflow: visible;
            .MuiDataGrid-columnHeaderTitleContainerContent {
              overflow: visible;
            }
          }
          .MuiDataGrid-columnHeadersInner .MuiDataGrid-columnHeader {
            &:nth-child(2) {
              padding-left: 4px !important;
            }
          }
          .MuiDataGrid-cell {
            font-size: 16px !important;
            &:nth-child(2) {
              padding-left: 4px !important;
            }
          }
          .MuiDataGrid-virtualScroller {
            overflow: auto !important;
            min-height: 378px;
            max-height: 378px;
            @media screen and (max-height: 805px) {
              min-height: 128px;
              max-height: calc(100vh - 464px);
            }
          }
          ::-webkit-scrollbar {
            height: 0px;
            @media screen and (max-width: 530px) {
              height: 4px;
            }
          }
          .MuiDataGrid-cell {
            border: none !important;
          }
          .MuiDataGrid-virtualScrollerContent {
            border: none !important;
          }
        }
        .animate-form {
          animation: ${LoadingFadein} 0.3s ease-in;
        }
      }
    }
  }
  input[type='number'] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
  }
`;
export const FilterRoleStyled = styled.div<{
  $openFilter: boolean;
}>`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 16px;
  margin-top: 24px;
  .filter-role {
    display: flex;
    align-items: center;
    justify-content: center;
    .filter-btn {
      button {
        width: 40px;
        height: 40px !important;
        border-radius: 8px;
        min-width: 0px;
        background-color: #f5f7f8;
        border: none;
        box-shadow: 0 0 0 1px #dbe2e5 !important;
      }
    }

    .filter-role-card {
      width: 300px;
      max-width: calc(100% - 48px);
      border-radius: 8px;
      box-shadow: 0px 0px 16px 0px #26323833;
      background-color: white;
      position: absolute;
      z-index: 1;
      top: 128px;
      right: 24px;
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      opacity: 0;
      visibility: hidden;
      transition: 0.15s ease-out;
      transform-origin: right top;
      transform: scale(0.7);
      ${({ $openFilter }) =>
        $openFilter &&
        css`
          visibility: visible;
          opacity: 1;
          transform: scale(1);
        `};
      .header-card {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #dbe2e5;
        font-size: 14px;
        padding: 0 16px;
        .topic {
          font-weight: 600;
        }
        .clear {
          text-decoration: underline;
          cursor: pointer;
        }
      }
      .checkbox-card {
        display: flex;
        flex-direction: column;
        padding: 2px 24px;
        max-height: 316px;
        overflow: auto;
        @media screen and (max-height: 750px) {
          max-height: 200px;
        }
        @media screen and (max-height: 650px) {
          max-height: 120px;
        }
        .MuiFormControlLabel-root {
          column-gap: 8px;
          height: 39.2px;
          .MuiTypography-root {
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          span:first-child {
            div {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
      .btn-wrap {
        padding: 0 24px 24px;
      }
    }
  }
`;

export const CustomIosSwitchStyle = styled.div`
  .MuiSwitch-switchBase {
    transform: translateX(calc(-100% + 22px)) !important;
    margin: 0 !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    &:hover {
      background-color: transparent;
    }
  }
  .Mui-checked {
    transform: translateX(calc(100% - 22px)) !important;
  }
  .MuiSwitch-track {
    height: 16px !important;
    width: 28px;
  }
  .MuiSwitch-root {
    display: flex !important;
    align-items: center !important;
    height: 16px !important;
    width: 28px;
    margin: 0 !important;
  }
  .MuiSwitch-thumb {
    height: 12px !important;
    width: 12px !important;
    box-shadow: none !important;
  }
  .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
    background: #16d5c5 !important;
  }
`;
export const IOSSwitchCustom = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(() => ({
  width: 30,
  height: 18,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(12px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: '#30D5C7',
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      color: '#30D5C7',
      border: '6px solid #fff',
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color: '#ccc',
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: 0.7,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 14,
    height: 14,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor: '#E9E9EA',
    opacity: 1,
  },
}));

export const AppTableStyle = styled.div<{
  $isAvatar?: boolean;
  $disableHeaderPadding?: boolean;
  $rows?: any;
  $disableLastRowBorder?: boolean | undefined;
}>`
  .content-wrap {
    width: 100%;
    //min-height: calc(100vh - 64px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    animation: ${LoadingFadein} 0.3s ease-in;
      &.job-list{
          min-height: calc(100vh - (70px + 60px));
        display: flex;
        flex-direction: column;
        justify-content: space-between;
          .lay-data-status {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              border-radius: 4px;
              background: #263238;
              color: #fff;
              padding: 5px 10px;
          }
      }
    .MuiDataGrid-cell--editing {
      box-shadow: none !important;
    }
    .MuiDataGrid-root {
      width: 100%;
      border: none;
      //input {
      //  all: unset !important;
      //}
      .MuiDataGrid-virtualScrollerContent {
        transition: 0.3s ease-out !important;
        ${({ $rows }) =>
          isEmpty($rows) &&
          css`
            height: 56px !important;
          `}
      }
      * {
        //line-height: 1;
      }
      .MuiDataGrid-columnHeaders {
        border-bottom: none;
        background-color: #f5f7f8;
        border-radius: 0;
        * {
          line-height: initial;
        }
      }
      .MuiDataGrid-columnHeadersInner .MuiDataGrid-columnHeader {
        padding: ${(prop) =>
          prop.$disableHeaderPadding ? '' : '0 16px !important'};
        ${({ $isAvatar }) =>
          $isAvatar &&
          css`
            &:nth-child(2) {
              padding-left: 0 !important;
            }
          `};
      }
      
      .MuiDataGrid-cell {
        font-size: 14px;
        padding: 0 16px !important;
        ${({ $isAvatar }) =>
          $isAvatar &&
          css`
            &:nth-child(2) {
              padding-left: 0 !important;
            }
          `};
      }
      .MuiDataGrid-row--lastVisible .MuiDataGrid-cell {
        ${({ $disableLastRowBorder }) =>
          $disableLastRowBorder
            ? css`
                border: none !important;
              `
            : css`
                border-bottom: 1px solid rgba(224, 224, 224, 1) !important;
              `};
      }
      }

      .MuiDataGrid-columnSeparator {
        display: none;
      }
      .MuiDataGrid-columnHeaderTitle {
        font-weight: 400;
        font-size: 12px;
      }
      .MuiDataGrid-cell:focus,
      .MuiDataGrid-cell:focus-within,
      .MuiDataGrid-columnHeader:focus,
      .MuiDataGrid-columnHeader:focus-within {
        outline: none;
      }
    }
    &.check-box-table {
      .MuiDataGrid-root {
        .MuiDataGrid-columnHeaderTitleContainer {
          overflow: visible !important;
          .MuiDataGrid-columnHeaderTitleContainerContent {
            overflow: visible !important;
          }
        }
        .MuiDataGrid-columnHeadersInner .MuiDataGrid-columnHeader {
          &:nth-child(2) {
            padding-left: 4px !important;
          }
          &:nth-child(3) {
            padding-left: 4px !important;
          }
        }
        .MuiDataGrid-cell {
          &:nth-child(2) {
            padding-left: 4px !important;
          }
          &:nth-child(3) {
            padding-left: 4px !important;
          }
        }
      }
    }
  }
`;
export const ActionGroupStyle = styled.div`
  display: flex;
  align-items: center;
  column-gap: 8px;
`;

export const AppContentStyle = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.6s ease-in;
  @media screen and (max-width: 820px) {
    margin-top: 64px;
    height: calc(100vh - 64px);
    overflow: auto;
  }
  .content-wrap {
    width: 100%;
    &.animate {
      animation: ${LoadingFadein} 0.6s ease-in;
    }
  }
`;

export const BreadcrumbsAndButtonStyled = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
  column-gap: 24px;
  min-height: 64px;
  border-bottom: 1px solid #dbe2e5;
  .MuiBreadcrumbs-root {
    font-size: 14px !important;
  }
  .MuiBreadcrumbs-separator {
    animation: ${LoadingFadein} 0.3s ease-in;
  }
  .MuiBreadcrumbs-li {
    p {
      animation: ${LoadingFadein} 0.3s ease-in;
    }
  }
`;

export const ScrollBarStyled = styled.div`
  width: 100%;
  max-width: 100%;
  position: relative;
  /* width */
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  /* Track */
  ::-webkit-scrollbar-track {
    background: #26323838;
    border-radius: 0;
  }
  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #263238;
    border-radius: 0;
  }
  hr {
    width: 1px;
    height: 24px;
    background: #dbe2e5;
  }
`;
export const FilterWrapStyled = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 0 0;
  border-bottom: 1px solid #dbe2e5;
  column-gap: 40px;
  overflow-y: auto;
  min-height: 58px;
  animation: ${LoadingFadein} 0.3s ease-in;
  .order-date {
    display: flex;
    align-items: center;
    column-gap: 16px;
    fieldset {
      border: none;
    }
  }
`;
export const EmptyField = styled.div`
  width: 100%;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #d32f2f;
  cursor: pointer;
  color: #d32f2f;
  &:hover {
    text-decoration: underline;
  }
`;
export const EmptyProductAttr = styled.div`
  width: 100%;
  height: 72px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #dbe2e5;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
`;
export const StatusChipStyle = styled.div`
  display: flex;
  align-items: center;
  column-gap: 4px;
  font-size: 14px;
  height: 40px;
  justify-content: center;
  border-radius: 20px;
  padding: 0 16px 0 12px;
  line-height: 1;
  &.pending {
    color: #fbc02d;
    background: #fff9e6;
  }
  &.done {
    color: #8bc34a;
    background: #f1fce3;
  }
`;

export const BadgeStyle = styled.div`
  height: 20px;
  width: 20px;
  min-width: 20px;
  border-radius: 4px;
  background: #607d8b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: white;
  font-size: 12px;
  &.active {
    background: #263238;
  }
`;
export const TableCellImageStyle = styled.div`
  img {
    width: 40px;
    height: 40px;
  }
`;
