import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

export const ContactPreviewStyle = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  display: flex;
  flex-direction: column;
  align-items: center;
  .header {
    height: 376px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .profile {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 8px;
      margin-top: 64px;
      padding: 0 24px;
      .image-wrap {
        width: 108px;
        height: 108px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid white;
        filter: drop-shadow(0px 0px 8px rgba(38, 50, 56, 0.12));
      }
      .name {
        font-size: 28px;
        font-weight: 600;
        text-align: center;
        line-height: 1.4;
      }
      .code {
        height: 32px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7f8;
        border-radius: 20px;
        column-gap: 16px;
        .id {
          font-size: 16px;
          font-weight: 600;
        }
        .copy {
          color: #90a4ae;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: 0.15s ease-out;
          &:hover {
            filter: brightness(0.8);
          }
          svg {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
    .tab-wrap {
      width: 100%;
    }
    .line {
      width: 100%;
      height: 8px;
      border-top: 1px solid #dbe2e5;
      background-color: #f5f7f8;
      position: absolute;
      bottom: -8px;
    }
  }
  .content {
    padding: 0 24px;
    width: 800px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
`;
