import { createTheme } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    dark: Palette['primary'];
    default: Palette['primary'];
    blueGrey: Palette['primary'];
    Hon: Palette['primary'];
  }
  interface PaletteOptions {
    dark?: PaletteOptions['primary'];
    default: Palette['primary'];
    blueGrey: Palette['primary'];
    Hon: Palette['primary'];
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    dark: true;
    default: true;
    blueGrey: true;
    Hon: true;
  }
}

declare module '@mui/material/Checkbox' {
  interface CheckboxPropsColorOverrides {
    dark: true;
    default: true;
  }
}

export const theme = createTheme({
  components: {
    MuiFormControl: {
      styleOverrides: {
        root: {
          '.MuiInputBase-root': {
            boxShadow: '0 0 0 1px #DBE2E5',
            borderRadius: '8px',
            transition: '0.4s',
            overflow: 'hidden',
            fontSize: '14px !important',
            '&:hover': {
              boxShadow: '0 0 0 1px #263238 !important',
            },
            '>fieldset': {
              border: 'none',
            },
            '.Mui-focused': {
              '&fieldset': {
                border: '1px solid #263238 !important',
              },
            },
          },
          input: {
            height: '40px',
            padding: '0 14px',
          },
        },
      },
    },
    MuiCheckbox: {
      defaultProps: {
        style: {},
      },
      styleOverrides: {
        root: {
          borderRadius: '50%',
        },
      },
      variants: [
        {
          props: { color: 'dark' },
          style: {
            color: '#DBE2E5',
          },
        },
      ],
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          fontFamily: 'Prompt',
          boxShadow: 'none',
          borderRadius: '8px',
          textTransform: 'none',
          fontSize: '14px !important',
          height: '40px',
        },
      },
      variants: [
        {
          props: { size: 'small' },
          style: {
            height: '40px !important',
          },
        },
        {
          props: { variant: 'outlined' },
          style: {
            border: 'none !important',
          },
        },
      ],
    },
    MuiButton: {
      defaultProps: {
        style: {
          height: 40,
        },
      },
      styleOverrides: {
        root: {
          fontFamily: 'Prompt',
          boxShadow: 'none !important',
          borderRadius: '8px',
          textTransform: 'none',
          fontWeight: '400',
        },
      },
      variants: [
        {
          props: { size: 'small' },
          style: {
            height: '40px !important',
          },
        },
        {
          props: { variant: 'contained' },
          style: {
            '&:hover': {
              boxShadow: 'none',
            },
          },
        },
        {
          props: { variant: 'outlined', color: 'blueGrey' },
          style: {
            color: '#212121',
            border: '1px solid #DBE2E5',
          },
        },
      ],
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: '#CFD8DC',
          transition: '0.15s ease-out',
          '&:hover': {
            color: '#222',
            background: '#F5F7F8',
          },
        },
      },
      variants: [
        {
          props: { color: 'error' },
          style: {
            color: '#CFD8DC',
            '&:hover': {
              background: '#FDE8EF',
              color: '#D32F2F',
            },
          },
        },
      ],
    },
    MuiInputBase: {
      defaultProps: {
        style: {
          borderRadius: '8px',
          border: 'none',
        },
      },
      helperText: {
        style: {
          color: '#E91E63',
        },
      },
      styleOverrides: {
        '&.Mui-focused': {},
        fieldset: {
          border: 'none',
        },
        input: {
          padding: '0 14px',
          color: 'black',
          fontSize: '14px !important',
          '&::placeholder': {
            color: '#ccc',
            opacity: 1,
          },
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        size: 'small',
        fullWidth: true,
      },
      styleOverrides: {
        root: {
          color: 'red !important',
          width: '100%',
          borderRadius: '8px',
          '>div': {
            borderRadius: '8px',
            boxShadow: '0 0 0 1px #ddd',
            '&.Mui-focused': {
              boxShadow: '0 0 0 1px #263238 !important',
            },
          },
          fieldset: {
            color: 'red',
            border: 'none',
            borderRadius: '8px',
          },
          '.MuiInputBase-input': {
            lineHeight: '1',
          },
        },
        input: {
          fontSize: '16px',
          outline: 'none',
          width: '100%',
          height: '100%',
          borderRadius: '8px !important',
          lineHeight: '1',
          '&::placeholder': {
            color: '#fff',
          },
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          background: '#F5F7F8',
          '.MuiTableCell-root': {
            color: '#90A4AE',
            fontWeight: 400,
          },
        },
      },
    },
  },
  typography: {
    fontFamily: 'Prompt, sans-serif',
    fontWeightRegular: 400, // Regular font weight
    fontWeightSemiBold: 600, // Semi-Bold font weight
    fontWeightBold: 700, // Bold font weight
  },
  palette: {
    primary: {
      main: '#263238',
      contrastText: '#fff',
    },
    success: {
      main: '#16B569',
      contrastText: '#fff',
    },
    secondary: {
      main: '#5DAA2F',
      contrastText: '#fff',
    },
    error: {
      main: '#D32F2F',
      contrastText: '#fff',
    },
    dark: {
      main: '#263238',
      contrastText: '#fff',
    },
    blueGrey: {
      main: '#999',
      dark: '#111',
      light: '#000',
      contrastText: '#212121',
    },
    Hon: {
      main: '#30D5C7',
      dark: '#30D5C7',
      light: '#30D5C7',
      contrastText: '#FFF',
    },
    default: {
      main: '#F5F5F5',
      contrastText: '#1E1E1E',
    },
  },
} as any);
