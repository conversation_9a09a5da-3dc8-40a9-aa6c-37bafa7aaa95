import styled from 'styled-components';

export const JobDataTable = styled.div`
  background: #fff;
  .detail {
    padding: 24px;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        font-size: 18px;
        font-weight: 600;
      }
    }
    .box-data-table {
      margin-top: 1rem;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      overflow: hidden;
      &.mt-0 {
        margin-top: 0 !important;
      }
      .MuiDataGrid-columnHeaders {
        min-height: 34px !important;
        max-height: 34px !important;
        color: #90a4ae;
      }
      .MuiDataGrid-virtualScroller {
        margin-top: 34px !important;
      }
      .MuiDataGrid-row {
        &:last-child {
          .MuiDataGrid-cell {
            border: none !important;
          }
        }
      }
    }
  }
`;
