import styled, { css } from 'styled-components';
import { isUndefined } from 'lodash';

const InvoiceStatusChip = styled.div<{
  $status?: number;
}>`
  height: 40px;
  padding: 8px 12px 8px 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  column-gap: 6px;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.14px;

  ${({ $status }) =>
    !isUndefined($status) &&
    ($status === 1 || $status === 2 || $status === 3) &&
    css`
      background: #fff5d3;
      color: #f9a925;
    `}
  ${({ $status }) =>
    !isUndefined($status) &&
    $status === 4 &&
    css`
      background: #e6f8cf;
      color: #8bc34a;
    `}
  ${({ $status }) =>
    !isUndefined($status) &&
    $status === 5 &&
    css`
      background: #f5f7f8;
      color: #263238;
    `}
  ${({ $status }) =>
    !isUndefined($status) &&
    ($status === 6 || $status === 7) &&
    css`
      background: #fde8ef;
      color: #d32f2f;
    `}
`;

export default InvoiceStatusChip;
