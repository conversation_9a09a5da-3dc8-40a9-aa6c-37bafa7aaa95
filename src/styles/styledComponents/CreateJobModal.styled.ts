import styled from 'styled-components';

export const CreateJobModalStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  margin-top: 24px;
  min-height: 460px;
  max-height: 460px;
  overflow: auto;
  position: relative;
  &.box-request-purchase-order {
    .detail-wrap {
      .detail {
        padding: 1rem 0.8rem !important;
        .key {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 12px !important;
          font-weight: 400 !important;
        }
        .value {
          font-size: 14px !important;
          font-weight: 600 !important;
        }
      }
    }
    .text-group {
      gap: 5px !important;
      justify-content: center !important;
      .text-bottom {
        span {
          font-size: 12px !important;
        }
      }
    }
  }
  .text-not-found {
    padding: 11rem 0;
    text-align: center;
    color: #cfd8dc;
  }
  .count {
    font-weight: 600;
  }
  .selector {
    width: 100%;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    position: relative;
    .selected {
      box-shadow: #263238 0px 0px 0px 2px inset;
      position: absolute;
      height: 100%;
      width: 100%;
      border-radius: 16px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .header-create-job-modal {
      width: 100%;
      flex: 1 1 0%;
      padding: 12px;
      display: flex;
      align-items: center;
      column-gap: 12px;
      justify-content: space-between;
      .left-side {
        display: flex;
        column-gap: 12px;
        align-items: center;
        .image {
          width: 36px;
          min-width: 36px;
          height: 36px;
          border-radius: 4px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 4px;
          * {
            line-height: 1;
          }
          .text-top {
            font-size: 14px;
            font-weight: 600;
          }
          .text-bottom {
            display: flex;
            align-items: center;
            column-gap: 6px;
            .user {
              width: 16px;
              min-width: 16px;
              height: 16px;
              img {
                height: 100%;
                width: 100%;
                object-fit: cover;
              }
            }
            .od-code {
              font-size: 10px;
            }
          }
        }
      }
      .right-side {
        height: 100%;
        display: flex;
        align-items: center;
      }
    }
    .detail-wrap {
      width: 100%;
      flex: 1 1 0%;
      display: flex;
      border-top: 1px solid #dbe2e5;
      .detail {
        display: flex;
        flex-direction: column;
        flex: 1 1 0%;
        position: relative;
        padding: 8px 12px;
        justify-content: center;
        border-right: 1px solid #dbe2e5;
        &:last-child {
          border-right: unset;
        }
        //&:nth-child(2) {
        //  &:before {
        //    content: '';
        //    position: absolute;
        //    left: 0;
        //    height: 100%;
        //    width: 1px;
        //    background: #dbe2e5;
        //    transform: translateX(-50%);
        //  }
        //  &:after {
        //    content: '';
        //    position: absolute;
        //    right: 0;
        //    height: 100%;
        //    width: 1px;
        //    background: #dbe2e5;
        //    transform: translateX(-50%);
        //  }
        //}
        .key {
          font-size: 10px;
          font-weight: 600;
        }
        .value {
          font-size: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }
      }
    }
  }
`;
