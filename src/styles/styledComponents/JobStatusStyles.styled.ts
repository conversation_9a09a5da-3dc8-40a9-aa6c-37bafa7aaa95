import styled from 'styled-components';

const JobStatusStyles = styled.div`
  padding: 24px;
  background-color: #fff;
  .box-send-withdraw {
    margin-top: 1rem;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 1rem;
    button {
      min-width: 250px;
    }
  }
  header {
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .box-text-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      h3 {
        margin: 0;
        font-size: 22px;
      }
      .box-status {
        display: flex;
        align-items: center;
        gap: 0.3rem;
        padding: 8px 12px;
        border-radius: 8px;
        background: #fff5d3;
        span {
          color: #f9a925;
        }
      }
    }

    .group-buttons {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      .btn-withdraw {
        border: 1px solid #dbe2e5;
      }
      .content {
        width: 500px;
      }
    }
  }
  .box-job-status {
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
    .box-header-production {
      padding: 16px;
    }
    .box-production {
      .card-accordion {
        margin: 0 !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        &.active {
          .text-header {
            .zone-point-and-line {
              .box-point {
                border-color: #000 !important;
                > div {
                  background: #000 !important;
                }
              }
              .line-bottom {
                background: #000 !important;
              }
            }
            span {
              color: #000 !important;
            }
          }
        }
        &:first-child {
          .text-header {
            .zone-point-and-line {
              .line-top {
                display: none !important;
              }
            }
          }
        }
        &:last-child {
          .text-header {
            .zone-point-and-line {
              .line-bottom {
                display: none !important;
              }
            }
          }
        }
        .MuiCollapse-root {
          .MuiAccordionDetails-root {
            background: #f5f7f8;
            padding: 0 1rem !important;
            overflow: hidden;
            .text-details {
              width: 100%;
              display: flex;
              align-items: center;
              padding: 1rem 0;
              overflow: hidden;
              .zone-point-and-line {
                width: 100%;
                display: flex;
                align-items: start;
                justify-content: start;
                gap: 0.7rem;
                .details {
                  width: 100%;
                  display: flex;
                  flex-direction: column;
                  > div {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  }
                  .result-table {
                    margin-top: 1rem;
                    padding: 1rem;
                    background: #fff;
                    border: 1px solid #dbe2e5;
                    border-radius: 12px;
                    ul {
                      list-style: none;
                      display: flex;
                      align-items: center;
                      width: 100%;
                      margin: 0;
                      padding: 0;
                      li {
                        width: calc(100% / 7);
                        header {
                          padding: 0;
                          border: none;
                          justify-content: start;
                          gap: 0.3rem;
                        }
                        .value {
                          display: flex;
                          align-items: center;
                          min-height: 30px;
                          padding-left: 0.5rem;
                          font-weight: bold;
                          &.not-found {
                            color: #cfd8dc;
                          }
                        }
                      }
                    }
                  }
                  .note {
                    display: flex;
                    align-items: center;
                    justify-content: start;
                    gap: 0.3rem;
                    margin-top: 0.5rem;
                    span {
                      &:first-child {
                        text-decoration: underline;
                      }
                    }
                  }
                  .result {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    .text {
                      font-weight: 600;
                    }
                    .value {
                      color: #cfd8dc;
                    }
                  }
                  .btn {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    button {
                      min-width: 130px;
                      background: #263238;
                      color: #fff;
                    }
                  }
                }
                .box-point-child {
                  width: 18px;
                  height: 18px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative;
                  margin-top: 0.8rem;
                  background: #f5f7f8;
                  z-index: 3;
                  .line {
                    width: 1px !important;
                    height: 50vh !important;
                    background: #000;
                    border-radius: 20px;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 1px;
                    right: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: auto;
                    z-index: 0;
                  }
                  .line-top {
                    position: absolute;
                    bottom: 0;
                    top: -40px;
                    left: 1px;
                    right: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: auto;
                    width: 1px !important;
                    height: 100% !important;
                    background: #dbe2e5 !important;
                    border-radius: 20px;
                  }
                  .line-bottom {
                    position: absolute;
                    bottom: 0;
                    top: 30px;
                    left: 1px;
                    right: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: auto;
                    width: 1px !important;
                    height: 100% !important;
                    background: #dbe2e5 !important;
                    border-radius: 20px;
                  }
                  .bg-dot {
                    padding: 0.2rem;
                    background: #f5f7f8;
                    z-index: 1;
                    .dot {
                      width: 8px;
                      height: 8px;
                      border-radius: 50%;
                      background: #000;
                    }
                  }
                }
              }
            }
          }
        }

        //&:last-child {
        //  .text-header {
        //    .zone-point-and-line {
        //      .line-bottom {
        //        display: none !important;
        //      }
        //    }
        //  }
        //}
        .MuiAccordionSummary-root {
          overflow: hidden;
          min-height: 60px;
          .MuiAccordionSummary-content {
            margin: 0;
            min-height: 48px;
            .text-header {
              display: flex;
              align-items: center;
              gap: 0.7rem;
              .tag-status {
                padding: 0.3rem 1rem;
                background: #fff5d3;
                border-radius: 30px;
                display: flex;
                align-items: center;
                justify-content: start;
                gap: 0.3rem;
                span {
                  font-size: 14px;
                  color: #f9a925;
                  font-weight: 300 !important;
                }
              }
              .zone-point-and-line {
                position: relative;
                .line-top {
                  position: absolute;
                  bottom: 0;
                  top: -40px;
                  left: 0;
                  right: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: auto;
                  width: 1px !important;
                  height: 100% !important;
                  background: #dbe2e5;
                  border-radius: 20px;
                }
                .line-bottom {
                  position: absolute;
                  bottom: 0;
                  top: 30px;
                  left: 0;
                  right: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: auto;
                  width: 1px !important;
                  height: 100% !important;
                  background: #dbe2e5;
                  border-radius: 20px;
                }
                .box-point {
                  width: 18px;
                  height: 18px;
                  border-radius: 50%;
                  border: 1px solid #dbe2e5;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: #fff;
                  z-index: 5;

                  > div {
                    width: 8px;
                    height: 8px;
                    background: #dbe2e5;
                    border-radius: 50%;
                  }
                }
              }
              span {
                font-size: 16px;
                font-weight: bold;
                color: #cfd8dc;
              }
            }
          }
          svg {
            color: #000 !important;
          }
        }
      }
    }
    .box-data-table {
      .MuiDataGrid-row {
        &:last-child {
          .MuiDataGrid-cell {
            border-bottom: none !important;
          }
        }
      }
      #basic-button {
        min-width: unset !important;
      }
    }
  }
`;
export default JobStatusStyles;
