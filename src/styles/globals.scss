
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

*{
  -webkit-box-sizing:border-box;
  -moz-box-sizing:border-box;
  box-sizing:border-box;
}
html {
  scroll-behavior: smooth;
  -webkit-tap-highlight-color: transparent;
}

body {
  /*color: rgb(var(--foreground-rgb));*/
  margin: 0;
  font-family: Prompt, sans-serif;
  color: #263238;
  font-size: 14px;
  .rdrMonthAndYearWrapper {
    padding-top: 0 !important;
  }
}
.font-prompt {
  font-family: Prompt, sans-serif;
}
.border-cancel-btn-swal {
  border: 1px solid #DBE2E5 !important;
}
ul{
  list-style: none;
  padding: 0;
  margin: 0;
}
p {
  color: #263238;
}
a {
  text-decoration: none;
  color: #263238;
}
.error-text {
  color: #e81621 !important;
}
.not-found-material {
  text-align: center;
  color: #607d8b;
  margin: 6rem 0;
}
#basic-menu {
  .btn-delete-withdraw {
    &:hover{
      background-color: #FDE8EF;
    }
  }
}
.dialog-task-timeline{
  top: unset !important;
  left: unset !important;
  .MuiDialog-container{
    justify-content: end;
    .MuiDialog-paper{
      border-radius: 0 !important;
      max-width: 600px !important;
      height: 100vh;
      margin-top: 0 !important;
    }
  }
}
.dialog-detail-production{
  left: unset;
  .MuiDialog-container{
    height: unset !important;
    .MuiDialog-paper{
      border-radius: unset !important;
      min-width: 700px !important;
      max-width: 700px !important;
      height: 100vh;
      margin-top: 0 !important;
      header{
        padding: 1rem;
        border-bottom: 1px solid #DBE2E5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        button{
          min-width: unset;
          &:hover{
            color: #CFD8DC;
          }
        }
        h2{
          margin: 0;
        }
      }
      .content{
        padding: 1rem 1rem 4rem 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        >div{
          padding: 0 1rem;
          h3{
            font-size: 20px;
          }
          .card-result{
            border-radius: 16px;
            border: 1px solid #DBE2E5;
            padding: .7rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            .img{
              min-width: 80px;
            }
            .result-details{
              display: flex;
              flex-direction: column;
              gap: 1rem;
              p{
                font-weight: 600;
                margin: 0;
                span{
                  font-weight: 300;
                }
              }
              .MuiGrid-item{
                padding-top: .2rem ;
                .value{

                }
              }

            }
          }
          .card-save-work{
            border: 1px solid #DBE2E5;
            border-radius: 16px !important;
            overflow: hidden;
            .MuiGrid-item{
              border: 1px solid #DBE2E5;
              border-bottom: none;
              .value{
                padding: 1rem;
                >div{
                  display: flex;
                  align-items: center;
                  gap: .2rem;
                }
                >span{
                  margin-top: .5rem;
                  &.found{
                    color: #CFD8DC;
                  }
                }
              }
            }
          }
          .box-work-time{
            border: 1px solid #DBE2E5;
            border-radius: 16px !important;
            overflow: hidden;
            .MuiPaper-root{
              margin: 0 !important;
              border-radius: 0 !important;
              box-shadow: none !important;
              &.Mui-expanded{
                margin: 0 !important;
              }
              .MuiAccordionSummary-root{
                .MuiAccordionSummary-expandIconWrapper{
                  &.Mui-expanded{
                    svg{
                      color:#263238;
                    }
                  }
                  svg{
                    color: #DBE2E5;
                  }
                }
                header{
                  padding: 0;
                  border: 0;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                  >div{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: .5rem;
                    p{
                      margin: 0;
                      font-weight: 600;
                    }
                  }
                  .time{
                    border-radius: 40px;
                    background: #F5F7F8;
                    padding: .4rem .8rem;
                    font-weight: 600;
                  }
                }
              }
              .MuiAccordionDetails-root{
                background: #F5F7F8;
                padding: 16px !important;
                .details{
                  display: flex;
                  align-items: start;
                  gap: .5rem;
                  .point{
                    position: relative;
                    min-width: 30px;
                    min-height: 100px !important;
                    display: flex;
                    align-items: end;
                    justify-content: center;
                    .dot{
                      position: absolute;
                      top: 8px;
                      left: 0;
                      right: 0;
                      width: 8px;
                      height: 8px;
                      background: #DBE2E5;
                      border-radius: 50%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: auto;
                    }
                    .line{
                      width: 1px;
                      height: 100%;
                      min-height: 80px;
                      background: #DBE2E5;
                    }
                  }
                  .work-list{
                    .date{
                      border: 1px solid #DBE2E5;
                      background: #fff;
                      border-radius: 30px;
                      width: fit-content;
                      padding: .1rem .5rem;
                      margin-bottom: .8rem;
                    }
                    ul{
                      list-style: none;
                      padding: 0;
                      padding-left: .7rem;
                      margin: 0;
                      display: flex;
                      flex-direction: column;
                      gap: .3rem;
                      li{
                        span{
                          color: #000 !important;
                        }
                        &.text-red{
                          color: #D32F2F;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

}
