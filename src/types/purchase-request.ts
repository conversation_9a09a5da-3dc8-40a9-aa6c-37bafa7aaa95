// User
type User = {
  id: number;
  name: string;
};

// Dimension
type Dimension = {
  id: number;
  name: string;
  imageUrl: string | null;
};

// MaterialSizeDimension
type MaterialSizeDimension = {
  id: number;
  name: string;
};

// DimensionConfig
type DimensionConfig = {
  id: number;
  configName: string;
  dimension: Dimension[];
  materialSizeDimension: MaterialSizeDimension[];
};

// MaterialType
type MaterialType = {
  id: number;
  name: string;
};

// Material
type Material = {
  id: number;
  name: string;
  countSubMaterial: number;
  dimensionConfig: DimensionConfig;
  materialType: MaterialType;
};

// SubMaterial
type SubMaterial = {
  id: number;
  name: string;
  imageUrl: string | null;
  countSubMaterialDetail: number;
};

// OptionsType
type OptionsType = {
  id: number;
  optionsTypeId: number;
  optionsType: {
    id: number;
    name: string;
    uuid: string;
  };
};

// OptionsCategory
type OptionsCategory = {
  id: number;
  name: string;
  optionType: OptionsType[];
};

// OptionsCostType
type OptionsCostType = {
  id: number;
  name: string;
  description: string | null;
};

// Options
type Options = {
  id: number;
  name: string;
  optionsCostType: OptionsCostType;
  optionsCategory: OptionsCategory;
};

// SubMaterialDetail
type SubMaterialDetail = {
  id: number;
  name: string;
  side: number;
  subMaterialId: number;
  options: Options;
};

// PickingType
type PickingType = {
  id: number;
  name: string;
};

// ItemSize
type ItemSize = {
  id: number;
  name: string;
};

// CountDimension
type CountDimension = {
  id: number;
  name: string;
  imageUrl: string | null;
};

// RawMaterial
type RawMaterial = {
  id: number;
  rawMaterialNo: string;
  name: string;
  description: string | null;
  material: Material;
  subMaterial: SubMaterial;
  subMaterialDetail: SubMaterialDetail;
  pickingType: PickingType;
  imageUrl: string | null;
  isPurchase: boolean;
  isSell: boolean;
  isSerialNumber: boolean;
  isLotExpirationDate: boolean;
  itemSize: ItemSize;
  countDimension: CountDimension;
  rawPrice: number;
};

// Brand
type Brand = {
  id: number;
  name: string;
  description: string | null;
  imageUrl: string | null;
};

// PrOrderList
type PrOrderList = {
  id: number;
  name: string;
  price: number;
  rawMaterial: RawMaterial;
  brand: Brand;
  amount: number;
};

type Province = {
  id: number;
  geoId: number;
  provinceCode: string;
  name: string;
};

type Contact = {
  id: number;
  code: string;
  taxNumber: string;
  taxAddress: string;
  province: Province;
};

// PrOrder
type PrOrder = {
  id: number;
  prOrderNo: string;
  user: User;
  prOrderLists: PrOrderList[];
  deletedReason: string | null;
  annotation: string | null;
  jobRef: string | null;
  status: number;
  statusEnum: string;
  note: string | null;
  requestedDeliveryDate: number | null;
  isDeleted: boolean;
  createdDate: number;
  modifiedDate: number | null;
  contact?: Contact;
};

// PurchaseRequestData
export type PurchaseRequestData = PrOrder;
