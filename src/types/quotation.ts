export type QuotationCreateRequest = {
  layDataOrderId: number;
};

export type QuotationCreateResponse = {
  status: boolean;
  message: string;
  data: {
    id: number;
    quotationNo: string;
    layDataOrderId: number;
    dueDate: any;
    remark: string | null;
  };
};

export type QuotationUpdateRequest = {
  quotationId: number; // fix id
  layDataOrderId: number; // Input type select
  dueDate: string; // '2025-03-15' Input type date
  remark: string | null; // input type textarea
  netPrice: number; // Input type number on text field
  totalPrice: number; // Input type number on text field
  balancePayment: number; // Input type number on text field
  discount: number; // Input type number on text field
  discountRate: number; // Input type number on text field
  vatRate: number; // Input type number on text field
  vatAmount: number; // Input type number on text field
  quotationItem: {
    productId: number; // Input type number on text field
    layDataId: number;
    description: string;
    quantity: number; // Input type number on text field
    priceUnit: number; // Input type number on text field
    price: number; // Input type number on text field
    discount: number; // Input type number on text field
  }[];
};

export type QuotationUpdateResponse = {
  status: boolean;
  message: string;
  data: {
    id: number;
    quotationNo: string;
    layDataOrderId: number;
    dueDate: any;
    remark: string;
    customerData: {
      imageUrl: string | null;
      contactType: {
        id: number;
        name: string;
      };
      id: number;
      code: string;
      name: string;
      taxNumber: string;
      creditType: {
        id: number;
        day: number;
      };
      email: string;
      phoneNumber: string;
      taxAddress: string;
      zipcode: string;
      subDistrict: {
        id: number;
        geoId: number;
        subDistrictCode: string;
        name: string;
      };
      district: {
        id: number;
        geoId: number;
        districtCode: string;
        name: string;
      };
      province: {
        id: number;
        geoId: number;
        provinceCode: string;
        name: string;
      };
      isActive: boolean;
      isCredit: boolean;
    };
    approvedUserId: number | null;
    createdUser: {
      id: number;
      name: string;
      imageUrl: null;
      userTypeName: string;
    };
    netPrice: number;
    totalPrice: number;
    balancePayment: number;
    discount: number;
    discountRate: number;
    vatRate: number;
    vatAmount: number;
    quotationItem: [
      {
        id: number;
        peakProduct: null;
        name: string;
        description: null;
        quantity: number;
        price: number;
        vatType: number;
        peakId: string;
      }
    ];
  };
};

export type QuotationDetailType = {
  id: number;
  quotationNo: string;
  layDataOrderId: number;
  dueDate: number;
  remark: string;
  customerData: {
    imageUrl: string;
    contactType: {
      id: number;
      name: string;
    };
    id: number;
    code: string;
    name: string;
    taxNumber: string;
    creditType: {
      id: number;
      day: number;
    };
    email: string;
    phoneNumber: string;
    taxAddress: string;
    zipcode: string;
    subDistrict: {
      id: number;
      geoId: number;
      subDistrictCode: string;
      name: string;
    };
    district: {
      id: number;
      geoId: number;
      districtCode: string;
      name: string;
    };
    province: {
      id: number;
      geoId: number;
      provinceCode: string;
      name: string;
    };
  };
  approvedUserId: null;
  createdUser: {
    id: number;
    name: string;
    imageUrl: null;
    userTypeName: string;
  };
  netPrice: number;
  totalPrice: number;
  balancePayment: number;
  discount: number;
  discountRate: number;
  vatRate: number;
  vatAmount: number;
  quotationItem: [];
};
