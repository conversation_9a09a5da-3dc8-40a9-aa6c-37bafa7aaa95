export type TDataSheet = {
  rawMaterial?: TRawMaterial;
  itemSize?: TItemSize;
  subItemSize?: TItemSize;
  dieCutRawMaterial?: TDieCutRawMaterial;
  plateRawMaterial?: TPlateRawMaterial;
  machine?: any;
};
export type TProductionOrder = {
  id: number;
  artwork: TArtwork[];
  jobNo: string;
  isLaySetting: boolean;
  productionOrderStatus: TProductionOrderStatus;
  workStatus: number;
  workStatusEnum: string;
  isConfig: boolean;
  createUser: TCreateUser;
  layDataOrderId: number;
  layoutFrontUrl?: string;
  layoutBackUrl?: string;
  createdDate: number;
  withdrawal: TWithdrawal[];
  productionOrderQuantity: TProductionOrderQuantity[];
  layDataDetailFront: TLayDataDetail;
  layDataDetailBack: TLayDataDetail;
  groupLayData?: TGroupLayData[];
  rawMaterial: TRawMaterial;
  layData: any;
  rawMaterialId: number;
  itemSize: TItemSize;
  itemSizeId: number;
  subItemSize: TItemSize;
  subItemSizeId: number;
  dieCutRawMaterial: TDieCutRawMaterial;
  dieCutRawMaterialId: number;
  plateRawMaterial: TPlateRawMaterial;
  plateRawMaterialId: number;
  machine?: any;
  machineId: number;
  machineModel?: {
    id: number;
    nameModel: string;
  };
  stage?: TStage[];
  priorityLevel: number;
  priorityLevelEnum: string;
  scheduleDate: number;
  scheduledStartDate: number;
};
export type TStage = {
  id: number;
  isActive: boolean;
  productionPlanStage: TProductionPlanStage;
  productionTime: TProductionTime;
};
export type TProductionTime = {
  id: number;
  minute: number;
  nameTime: string;
};
export type TProductionPlanStage = {
  description?: string;
  id: number;
  name: string;
};
export type TPlateRawMaterial = {
  id: number;
  imageUrl?: string;
  itemSize: TItemSize;
  materialId: number;
  name: string;
  rawMaterialNo: string;
  subMaterialDetail: TSubMaterialDetail;
  subMaterialId: number;
};
export type TDieCutRawMaterial = {
  id: number;
  imageUrl?: string;
  itemSize: TItemSize;
  materialId: number;
  name: string;
  rawMaterialNo: string;
  subMaterialDetail: TSubMaterialDetail;
  subMaterialId: number;
};
export type TRawMaterial = {
  id: number;
  imageUrl: string;
  itemSize: TItemSize;
  materialId: number;
  name: string;
  rawMaterialNo: string;
  subMaterialDetail: TSubMaterialDetail;
  subMaterialId: number;
};
export type TSubMaterialDetail = {
  id: number;
  name: string;
  side?: number;
  subMaterialId: number;
  subMaterialImageUrl?: string;
};
export type TGroupLayData = {
  id: number;
  isDefault: boolean;
  ldCode: string;
  productModelImageUrl: string;
  productModelName: string;
  quantity: number;
  quantityAllowance: number;
  quantityPerSheet: number;
  totalSalePrice: number;
};
export type TProductionOrderQuantity = {
  createUser: TCreateUser;
  id: number;
  name: string;
  quantity: number;
  quantityAllowance: number;
  status: number;
  statusEnum: string;
  totalQuantity: number;
  printSheetQuantity: number;
  printSheetAllowance: number;
  totalPrintSheet: number;
};
export type TLayDataDetail = {
  coating: TCoating;
  extra: TExtra[];
  finish: TFinish[];
};
export type TCoating = {
  layDataProductColor: TLayDataProductColor[];
  printSystemName: string;
};
export type TExtra = {
  extraAreaDimension: string;
  extraMaterName: string;
  extraSubMaterial: string;
  quantity: number;
};
export type TFinish = {
  coatingMasterName: string;
  coatingOrder: number;
  coatingOrderEnum: string;
  finishName: string;
};
export type TLayDataProductColor = {
  colorName: string;
  printColor: string;
  printColorImageUrl: string;
};
export type TArtwork = {
  artworkDetailModifiedDate: number;
  id: number;
  ldCode: string;
  link: string;
  title: string;
  user: TCreateUser;
};
export type TCreateUser = {
  id: number;
  name: string;
  imageUrl: string | null;
  userTypeName: string;
};
export type TProductionOrderStatus = {
  id: number;
  name: string;
};
export type TWithdrawal = {
  brand?: TBrand;
  id: number;
  imageUrl?: string;
  isStock?: boolean;
  itemSize: TItemSize;
  listRawMaterialBrandConfig: TBrand[];
  materialName: string;
  name: string;
  no?: number;
  quantity: number;
  rawMaterialId: number;
  rawMaterialNo: string;
  statusStock: number;
  statusStockEnum: string;
};
export type TBrand = {
  description?: string;
  id?: number;
  imageUrl?: string;
  name?: string;
};
export type TItemSize = {
  id?: number;
  name?: string;
  itemSizeName?: string;
};
export type TAddMaterial = {
  rawMaterialId: number;
  brandId: number;
};
export type TDataUpdateProductOrder = {
  productionOrderId: number;
  scheduledStartDate: string;
  scheduleDate: string;
  priorityLevel: number;
  rawMaterialId: number;
  itemSizeId: number;
  subItemSizeId: number;
  dieCutRawMaterialId: number;
  plateRawMaterialId: number;
  machineId: number;
  machineModelId: number;
  stage: TNewStage[];
};
export type TNewStage = {
  id: number;
  sortIndex: number;
  isActive: boolean;
  productionTimeId: number;
  productionPlanStageId: number;
};
export type TAddQuantity = {
  productionOrderId: number;
  name: string;
  annotationId: number;
  printSheetAllowance: number;
};
export type TAddArtwork = {
  productionOrderId: number;
  title: string;
  link: string;
};
export type TAddLayData = {
  productionOrderId: number;
  layDataId: number;
};
export type TDataLayoutFile = {
  layoutFrontFile?: TUploadLayoutFile | null;
  layoutBackFile?: TUploadLayoutFile | null;
};
export type TUploadLayoutFile = {
  productionOrderId: number;
  file?: any;
  typeLayOut: number;
};
export enum ECoatingOrderEnum {
  BEFORE = 'BEFORE',
  AFTER = 'AFTER',
}
