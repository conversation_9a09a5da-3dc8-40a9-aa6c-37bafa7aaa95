type EstimateQuantityType = {
  quantity: number | string;
};

type ColorPrintType = {
  printColorId: number | null;
  colorId: number | null;
  printAreaDimensionId: number | null;
};

type PrintingRequestType = {
  printingSideDimensionId: number | null;
  printSystemId: number | '';
  colorFront: ColorPrintType[];
  colorBack: ColorPrintType[];
};

type FinishType = {
  coatingOrder: number;
  coatingMasterId: number | null;
  finishSubMaterialDetailId: number | null;
  side: number;
};

type CoatingRequestType = {
  coatingSideDimensionId: number | null;
  finishFront: FinishType[];
  finishBack: FinishType[];
};

type ExtraType = {
  extraMasterId: number | null;
  blogSubMaterialDetailId: number | null;
  extraSideDimensionId: number | null;
  extraAreaDimensionId: number | null;
};

type ServiceLayType = {
  serviceLayId: number | null;
  serviceLayTypeId: number | null;
};

export type PrepareFormType = {
  id: number | null;
  width: number | string;
  height: number | string;
  length: number | string;
  estimateQuantity: EstimateQuantityType[];
  materialMasterId: number | null;
  subMaterialDetailId: number | null;
  productParts: any[];
  printingRequest: PrintingRequestType;
  coatingRequest: CoatingRequestType;
  extra: ExtraType[];
  productDesignId: number | null;
  masterExample: number[];
  serviceLay: ServiceLayType[];
  note: string;
};
