export type OpenCreateType = {
  status: boolean;
  type: string;
};

export type OpenDeleteType = {
  status: boolean;
  name: string;
  id: number | null;
};
export type ModalValueType = {
  id: number | null;
  name: string;
  description: string;
  urlSlug: string;
  imageUrl: string;
  printSide: number[];
};
export type CategoryRowsType = {
  id: number;
  name: string;
  description: string;
  imageUrl: string | null;
  urlSlug: string;
  countProductType: number;
  active: boolean;
};
