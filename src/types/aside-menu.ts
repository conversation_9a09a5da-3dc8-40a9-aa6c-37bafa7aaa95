import { HTMLMotionProps, Variants, Transition } from 'framer-motion';
import { KeyboardNavigationProps } from '@/utils/motion/accessibility';

/**
 * Base service item interface
 */
export interface ServiceItem {
  name: string;
  slug: string;
  isActive: boolean;
  permissionSlug: string;
}

/**
 * Service group interface with motion-related properties
 */
export interface ServiceGroup {
  name: string;
  slug: string;
  serviceTypeId?: number;
  imageUrl?: string;
  permissionSlug: string;
  serviceItems: ServiceItem[];
  isActive: boolean;
}

/**
 * Service type interface for menu groups
 */
export interface ServiceType {
  name: string;
  slug: string;
  serviceGroups: ServiceGroup[];
  isActive: boolean;
}

/**
 * Motion-specific props for aside menu components
 */
export interface AsideMenuMotionProps
  extends Omit<HTMLMotionProps<'div'>, 'children'> {
  variants?: Variants;
  transition?: Transition;
  prefersReducedMotion?: boolean;
}

/**
 * Props for AsideMenuGroup component with motion support
 */
export interface AsideMenuGroupProps {
  serviceType: ServiceType;
  motionProps?: AsideMenuMotionProps;
  className?: string;
  'data-testid'?: string;
}

/**
 * Props for AsideMenuItem component with motion and accessibility support
 */
export interface AsideMenuItemProps {
  serviceGroup: ServiceGroup;
  motionProps?: AsideMenuMotionProps;
  keyboardNavigation?: KeyboardNavigationProps;
  className?: string;
  'data-testid'?: string;
}

/**
 * Animation state for submenu components
 */
export interface SubmenuAnimationState {
  isOpen: boolean;
  itemCount: number;
  showAside: boolean;
  focusedIndex: number;
}

/**
 * Motion configuration for different menu component types
 */
export type MenuComponentType =
  | 'menuGroup'
  | 'menuItem'
  | 'submenu'
  | 'submenuItem'
  | 'expandIcon'
  | 'serviceGroupName'
  | 'menuIcon';

/**
 * Enhanced motion props with component-specific configurations
 */
export interface EnhancedMotionProps extends AsideMenuMotionProps {
  componentType?: MenuComponentType;
  animationState?: Partial<SubmenuAnimationState>;
  performanceOptimized?: boolean;
}

/**
 * Keyboard navigation state for menu items
 */
export interface MenuKeyboardState {
  focusedSubmenuIndex: number;
  isSubmenuOpen: boolean;
  activeMenuPath: string;
}

/**
 * Accessibility props for menu components
 */
export interface MenuAccessibilityProps {
  role?: 'menu' | 'menuitem' | 'menubar';
  'aria-expanded'?: boolean;
  'aria-haspopup'?: boolean | 'menu';
  'aria-current'?: 'page' | 'step' | 'location' | 'date' | 'time' | boolean;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  tabIndex?: number;
}

/**
 * Combined props interface for fully-featured menu items
 */
export interface FullMenuItemProps extends AsideMenuItemProps {
  accessibilityProps?: MenuAccessibilityProps;
  keyboardState?: MenuKeyboardState;
  onStateChange?: (state: Partial<MenuKeyboardState>) => void;
  onNavigate?: (path: string) => void;
}

/**
 * Performance optimization options for animations
 */
export interface AnimationPerformanceOptions {
  willChange?: string;
  backfaceVisibility?: 'visible' | 'hidden';
  perspective?: number;
  transform3d?: boolean;
  gpuAcceleration?: boolean;
}

/**
 * Motion configuration with performance options
 */
export interface MotionConfigWithPerformance {
  variants: Variants;
  transition: Transition;
  performance?: AnimationPerformanceOptions;
  reducedMotionFallback?: Partial<HTMLMotionProps<'div'>>;
}

/**
 * Type guard to check if an object is a ServiceGroup
 */
export const isServiceGroup = (obj: any): obj is ServiceGroup => {
  return (
    obj &&
    typeof obj.name === 'string' &&
    typeof obj.slug === 'string' &&
    typeof obj.permissionSlug === 'string' &&
    Array.isArray(obj.serviceItems) &&
    typeof obj.isActive === 'boolean'
  );
};

/**
 * Type guard to check if an object is a ServiceType
 */
export const isServiceType = (obj: any): obj is ServiceType => {
  return (
    obj &&
    typeof obj.name === 'string' &&
    typeof obj.slug === 'string' &&
    Array.isArray(obj.serviceGroups) &&
    typeof obj.isActive === 'boolean' &&
    obj.serviceGroups.every(isServiceGroup)
  );
};

/**
 * Utility type for extracting motion props from component props
 */
export type ExtractMotionProps<T> = T extends { motionProps?: infer U }
  ? U
  : never;

/**
 * Utility type for component props without motion-specific properties
 */
export type WithoutMotionProps<T> = Omit<
  T,
  'motionProps' | 'variants' | 'transition' | 'initial' | 'animate' | 'exit'
>;
