// itemSize
type ContentItem = {
  id: number;
  description: string;
  itemSizeName: string;
  size: number;
};

export type itemSizeListType = {
  content: ContentItem[];
  totalElements: number | null;
};

// subItemSize
type Dimension = {
  id: number;
  name: string;
};

type UnitSize = {
  id: number;
  name: string;
  dimension: boolean;
};

type SubItemSizeValueDto = {
  dimension: Dimension;
  value: number;
  rawValue: number;
  unitSize: UnitSize;
  type: string;
};

type Content = {
  id: number;
  itemSizeName: string;
  description: string;
  itemSizeId: number;
  cut: number;
  subItemSizeValueDto: SubItemSizeValueDto[];
};
export type subItemSizeListType = {
  content: Content[];
  totalElements: number | null;
};
