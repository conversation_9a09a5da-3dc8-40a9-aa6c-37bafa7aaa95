export type KebabMenu = {
  isEdit?: KebabStatusUrl;
  isRemove?: boolean;
  isImportItem?: boolean;
  isTransferItem?: boolean;
  isProductConfig?: boolean;
  isDetail?: boolean;
  isMeatBall?: boolean;
  isCancelPr?: boolean;
  isHistory?: KebabStatusUrl;
  isReportLayout?: boolean;
  isHowTo?: boolean;
  isCancel?: KebabStatusUrl;
  isCustom?: boolean;
  customText?: string;
  isStop?: KebabStatusUrl;
  isReject?: boolean;
  isReplyEdit?: boolean;
  isRejectInvoice?: boolean;
  isCancelInvoice?: boolean;
  isCreateProduct?: boolean;
  isDetailOrder?: KebabStatusUrl;
  isDelete?: KebabStatusUrl;
  isHistoryPO?: KebabStatusUrl;
  isApprove?: KebabStatusUrl;
  isNotApproved?: KebabStatusUrl;
};
export type KebabStatusUrl = {
  status: boolean;
  url?: string;
  action?: () => void;
  disabled?: boolean;
};
