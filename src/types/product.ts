export type ProductByIdType = {
  id: number;
  name: string;
  description: string | null;
  code: string;
  urlSlug: string;
  isActive: boolean;
  imageUrl: string;
  productTypeId: number;
  productTypeName: string;
  productCategoryId: number;
  productCategoryName: string;
  productSize: {
    id: number;
    minWidth: number;
    minHeight: number;
    minLength: number;
    maxWidth: number;
    maxHeight: number;
    maxLength: number;
  };
  componentId: number;
  componentName: string;
};
