type Contact = {
  id: number;
  code: string;
  imageUrl: string;
  name: string;
  contactType: {
    id: number;
    name: string;
  };
  contactRole: ContactRole[];
  phoneNumber: string;
  email: string;
  creditType: {
    id: number;
    day: number;
  };
};

type ContactRole = {
  id: number;
  name: string;
};

export type ContactResponseProps = {
  content: Contact[];
  totalElements: number | null;
};
