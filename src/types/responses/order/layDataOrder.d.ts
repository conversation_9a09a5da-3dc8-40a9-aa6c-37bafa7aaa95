type LayDataStatusType = {
  id: number;
  name: string;
};

type ProductModelType = {
  productModelId: number;
  productModelName: string;
  imageUrl: string;
  productId: number;
  productName: string;
};

type LayDataQuantityType = {
  id: number;
  quantity: number;
  quantityAllowance: number;
  costPrice: number;
  serviceCharge: number;
  profit: number;
  isConfigCost: boolean;
  isDefault: boolean;
  netPrice: number;
  profitRate: number;
  discount: number;
  discountRate: number;
  vatAmount: number;
  vatRate: number;
  isSetting: boolean;
  isConfirm: boolean;
  totalSalePrice: number;
};

type SubMaterialDetailType = {
  id: number;
  name: string;
  side: number;
  subMaterialId: number;
  subMaterialImageUrl: string;
};

type PrintingSideDimensionType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type PrintSystemType = {
  id: number;
  name: string;
};

type PrintColorType = {
  id: number;
  name: string;
  imageUrl: string;
};

type ColorType = {
  id: number;
  name: string;
};

type PrintAreaDimensionType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type ColorEntryType = {
  id: number;
  printColor: PrintColorType;
  color: ColorType;
  printAreaDimension: PrintAreaDimensionType;
};

type PrintingRequestType = {
  printingSideDimension: PrintingSideDimensionType;
  printSystem: PrintSystemType;
  colorFront: ColorEntryType[];
  colorBack: ColorEntryType[];
};

type CoatingSideDimensionType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type FinishSubMaterialDetailType = {
  id: number;
  name: string;
  side: number | null;
  subMaterialId: number;
  subMaterialImageUrl: string | null;
};

type FinishEntryType = {
  id: number;
  coatingOrder: number;
  coatingOrderEnum: string;
  coatingMasterId: number;
  finishSubMaterialDetail: FinishSubMaterialDetailType;
};

type CoatingRequestType = {
  coatingSideDimension: CoatingSideDimensionType;
  finishFront: FinishEntryType[];
  finishBack: FinishEntryType[];
};

type BlogSubMaterialDetailType = {
  id: number;
  name: string;
  side: number;
  subMaterialId: number;
  subMaterialImageUrl: string;
};

type ExtraSideDimensionType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type ExtraAreaDimensionType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type ExtraType = {
  id: number;
  extraMasterId: number;
  blogSubMaterialDetail: BlogSubMaterialDetailType;
  extraSideDimension: ExtraSideDimensionType;
  extraAreaDimension: ExtraAreaDimensionType;
};

type ProductDesignType = {
  productDesignId: number;
  name: string;
  imageUrl: string;
  description: string;
};

type MasterExampleType = {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
};

type ServiceLayItemType = {
  id: number;
  serviceLay: {
    id: number;
    name: string;
  };
  serviceLayType: {
    id: number;
    name: string;
    type: number;
    typeEnum: string;
  };
};

export type layDataOrderType = {
  id: number;
  layDataStatus: LayDataStatusType;
  isSpec: boolean;
  isConfirm: boolean;
  ldCode: string;
  productModel: ProductModelType;
  width: number;
  length: number;
  height: number;
  layDataQuantity: LayDataQuantityType[];
  materialMasterId: number;
  subMaterialDetail: SubMaterialDetailType;
  productParts: any[];
  printingRequest: PrintingRequestType;
  coatingRequest: CoatingRequestType;
  extra: ExtraType[];
  productDesign: ProductDesignType;
  masterExample: MasterExampleType[];
  serviceLay: ServiceLayItemType[];
  note: string;
  printPlate: null;
};
