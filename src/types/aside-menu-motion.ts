/**
 * Comprehensive type definitions for aside menu motion implementation
 * This file consolidates all motion-related types for the aside menu system
 */

import { Variants, Transition } from 'framer-motion';
import {
  ServiceType,
  ServiceGroup,
  ServiceItem,
  AsideMenuMotionProps,
  MenuKeyboardState,
  MenuAccessibilityProps,
} from './aside-menu';
import {
  EnhancedMotionProps,
  MotionComponentType,
} from '@/utils/motion/motion-config';
import { EnhancedKeyboardNavigationProps } from '@/utils/motion/accessibility';

/**
 * Motion-enhanced service item with animation state
 */
export interface MotionServiceItem extends ServiceItem {
  animationDelay?: number;
  customVariants?: Variants;
  isAnimating?: boolean;
}

/**
 * Motion-enhanced service group with animation configuration
 */
export interface MotionServiceGroup extends Omit<ServiceGroup, 'serviceItems'> {
  serviceItems: MotionServiceItem[];
  expandAnimation?: {
    duration: number;
    easing: string;
    stagger: number;
  };
  collapseAnimation?: {
    duration: number;
    easing: string;
  };
  isExpanded?: boolean;
  animationState?:
    | 'idle'
    | 'expanding'
    | 'collapsing'
    | 'expanded'
    | 'collapsed';
}

/**
 * Motion-enhanced service type with global animation settings
 */
export interface MotionServiceType extends Omit<ServiceType, 'serviceGroups'> {
  serviceGroups: MotionServiceGroup[];
  globalAnimationSettings?: {
    reducedMotion: boolean;
    animationSpeed: 'slow' | 'normal' | 'fast';
    enableStagger: boolean;
  };
}

/**
 * Complete motion configuration for aside menu components
 */
export interface AsideMenuMotionConfig {
  // Component-specific configurations
  menuGroup: {
    variants: Variants;
    transition: Transition;
    stagger?: {
      delayChildren: number;
      staggerChildren: number;
    };
  };
  menuItem: {
    variants: Variants;
    transition: Transition;
    hoverVariants?: Variants;
    focusVariants?: Variants;
  };
  submenu: {
    variants: Variants;
    transition: Transition;
    containerVariants?: Variants;
    itemVariants?: Variants;
  };
  expandIcon: {
    variants: Variants;
    transition: Transition;
  };
  // Global settings
  global: {
    reducedMotion: boolean;
    performanceMode: boolean;
    debugMode: boolean;
  };
}

/**
 * Animation event handlers for aside menu components
 */
export interface AsideMenuAnimationEvents {
  onAnimationStart?: (definition: any) => void;
  onAnimationComplete?: (definition: any) => void;
  onHoverStart?: (event: MouseEvent, info: any) => void;
  onHoverEnd?: (event: MouseEvent, info: any) => void;
  onTapStart?: (event: MouseEvent | TouchEvent, info: any) => void;
  onTap?: (event: MouseEvent | TouchEvent, info: any) => void;
  onFocus?: (event: FocusEvent) => void;
  onBlur?: (event: FocusEvent) => void;
}

/**
 * Enhanced props for AsideMenuGroup with full motion support
 */
export interface EnhancedAsideMenuGroupProps {
  serviceType: MotionServiceType;
  motionConfig?: Partial<AsideMenuMotionConfig>;
  animationEvents?: AsideMenuAnimationEvents;
  className?: string;
  'data-testid'?: string;
  // Accessibility props
  role?: string;
  'aria-label'?: string;
  // Performance props
  performanceOptimized?: boolean;
  lazyLoad?: boolean;
}

/**
 * Enhanced props for AsideMenuItem with full motion and keyboard support
 */
export interface EnhancedAsideMenuItemProps {
  serviceGroup: MotionServiceGroup;
  motionConfig?: Partial<AsideMenuMotionConfig>;
  keyboardNavigation?: EnhancedKeyboardNavigationProps;
  animationEvents?: AsideMenuAnimationEvents;
  accessibilityProps?: MenuAccessibilityProps;
  className?: string;
  'data-testid'?: string;
  // State management
  keyboardState?: MenuKeyboardState;
  onStateChange?: (state: Partial<MenuKeyboardState>) => void;
  // Navigation
  onNavigate?: (path: string, serviceItem?: MotionServiceItem) => void;
  onSubmenuToggle?: (isOpen: boolean, serviceGroup: MotionServiceGroup) => void;
  // Performance
  virtualizeSubmenus?: boolean;
  preloadSubmenus?: boolean;
}

/**
 * Motion context for aside menu system
 */
export interface AsideMenuMotionContext {
  // Global state
  isReducedMotion: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';
  performanceMode: boolean;

  // Current animation states
  activeAnimations: Set<string>;
  animationQueue: Array<{
    id: string;
    type: MotionComponentType;
    priority: number;
  }>;

  // Configuration
  config: AsideMenuMotionConfig;

  // Methods
  updateConfig: (newConfig: Partial<AsideMenuMotionConfig>) => void;
  queueAnimation: (
    id: string,
    type: MotionComponentType,
    priority?: number
  ) => void;
  cancelAnimation: (id: string) => void;
  clearAnimationQueue: () => void;
}

/**
 * Hook return type for aside menu motion management
 */
export interface UseAsideMenuMotionReturn {
  // Motion props generators
  getMenuGroupProps: (
    overrides?: Partial<EnhancedMotionProps>
  ) => EnhancedMotionProps;
  getMenuItemProps: (
    overrides?: Partial<EnhancedMotionProps>
  ) => EnhancedMotionProps;
  getSubmenuProps: (
    overrides?: Partial<EnhancedMotionProps>
  ) => EnhancedMotionProps;
  getExpandIconProps: (
    isExpanded: boolean,
    overrides?: Partial<EnhancedMotionProps>
  ) => EnhancedMotionProps;

  // State
  isReducedMotion: boolean;
  animationConfig: AsideMenuMotionConfig;

  // Controls
  enableAnimations: () => void;
  disableAnimations: () => void;
  updateAnimationSpeed: (speed: 'slow' | 'normal' | 'fast') => void;

  // Performance
  preloadAnimations: () => void;
  cleanupAnimations: () => void;
}

/**
 * Type for motion-aware aside menu component factory
 */
export interface AsideMenuComponentFactory {
  createMenuGroup: (props: EnhancedAsideMenuGroupProps) => React.ComponentType;
  createMenuItem: (props: EnhancedAsideMenuItemProps) => React.ComponentType;
  createSubmenu: (props: any) => React.ComponentType;

  // Configuration
  configure: (config: Partial<AsideMenuMotionConfig>) => void;
  getConfig: () => AsideMenuMotionConfig;

  // Utilities
  validateProps: (props: any, componentType: MotionComponentType) => boolean;
  optimizeForPerformance: (enable: boolean) => void;
}

/**
 * Animation performance metrics
 */
export interface AnimationPerformanceMetrics {
  frameRate: number;
  animationDuration: number;
  memoryUsage: number;
  cpuUsage: number;
  droppedFrames: number;
  timestamp: number;
}

/**
 * Performance monitoring for animations
 */
export interface AnimationPerformanceMonitor {
  startMonitoring: (animationId: string) => void;
  stopMonitoring: (animationId: string) => AnimationPerformanceMetrics;
  getMetrics: (animationId: string) => AnimationPerformanceMetrics | null;
  getAllMetrics: () => Record<string, AnimationPerformanceMetrics>;
  clearMetrics: () => void;
}

/**
 * Type guards for motion-enhanced types
 */
export const isMotionServiceItem = (obj: any): obj is MotionServiceItem => {
  return obj && typeof obj === 'object' && 'name' in obj && 'slug' in obj;
};

export const isMotionServiceGroup = (obj: any): obj is MotionServiceGroup => {
  return (
    obj &&
    typeof obj === 'object' &&
    'serviceItems' in obj &&
    Array.isArray(obj.serviceItems)
  );
};

export const isMotionServiceType = (obj: any): obj is MotionServiceType => {
  return (
    obj &&
    typeof obj === 'object' &&
    'serviceGroups' in obj &&
    Array.isArray(obj.serviceGroups)
  );
};

/**
 * Utility types for component composition
 */
export type AsideMenuMotionComponent<P = {}> = React.ComponentType<
  P & {
    motionProps?: AsideMenuMotionProps;
    animationEvents?: AsideMenuAnimationEvents;
  }
>;

export type WithMotionEnhancement<T> = T & {
  motionConfig?: Partial<AsideMenuMotionConfig>;
  performanceOptimized?: boolean;
  debugMode?: boolean;
};
