import {
  <PERSON><PERSON>,
  <PERSON>putAdorn<PERSON>,
  <PERSON><PERSON>ield,
  Toggle<PERSON>utton,
  ToggleButtonGroup,
} from '@mui/material';
import styled from 'styled-components';
import { useFormik } from 'formik';
import * as yup from 'yup';
import React, { ReactElement, useState } from 'react';
import { LoadingFadein } from '@/styles/share.styled';
import CompanyLayout from '@/layouts/BlankLayout';
import ImageField from '@/components/ImageField';
import moment from 'moment/moment';
import 'dayjs/locale/th';
import CalendarMonthRoundedIcon from '@mui/icons-material/CalendarMonthRounded';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import HonDatePicker from '@/components/HonDatePicker';

const PersonalInformationStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  animation: ${LoadingFadein} 0.3s ease-in;
  padding-top: 40px;
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 480px;
    @media screen and (max-width: 575px) {
      width: 85%;
    }
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
  }

  .gender-wrap {
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: #f5f7f8;
    .MuiToggleButtonGroup-root {
      height: 40px;
      margin: 0 3px;
      .Mui-selected {
        background-color: #263238 !important;
        color: #fff !important;
        border-radius: 4px;
        z-index: 1;
        &:before {
          content: '';
          display: none;
        }
        &:after {
          content: '';
          display: none;
        }
      }
      button {
        background-color: transparent;
        color: #263238;
        font-size: 16px;
        font-weight: 600;
        border: 0;
        &:nth-child(2) {
          &:before {
            content: '';
            height: 24px;
            width: 1px;
            background-color: #dbe2e5;
            position: absolute;
            left: 0;
          }
          &:after {
            content: '';
            height: 24px;
            width: 1px;
            background-color: #dbe2e5;
            position: absolute;
            right: 0;
          }
        }
      }
    }
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อจริง'),
  lastName: yup.string().required('กรุณากรอกนามสกุล'),
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
    .required('กรุณากรอกเบอร์โทรศัพท์'),
  email: yup
    .string()
    .email('รูปแบบอีเมล์ไม่ถูกต้อง')
    .required('กรุณากรอกอีเมล'),
  birthDate: yup
    .string()
    .matches(/^\d{2}\/\d{2}\/\d{4}$/, 'รูปแบบวันเกิดไม่ถูกต้อง (DD/MM/YYYY)')
    .required('กรุณากรอกวันเกิด')
    .test('min-age', 'อายุต้องไม่น้อยกว่า 15 ปี', function (value) {
      const today = new Date();
      const birthDate = moment(value, 'DD/MM/YYYY').toDate();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 15;
    })
    .test('max-age', 'เกิดข้อผิดพลาด', function (value) {
      const today = new Date();
      const birthDate = moment(value, 'DD/MM/YYYY').toDate();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age <= 100;
    }),
});
const PersonalInformation = () => {
  const [gender, setGender] = useState('male');
  const [openCalendar, setOpenCalendar] = useState<boolean>(false);
  const router = useRouter();
  const formik = useFormik({
    initialValues: {
      name: '',
      lastName: '',
      phoneNumber: '',
      email: '',
      imageFile: '',
      gender: 'male',
      birthDate: '',
    },
    validationSchema,
    onSubmit: (_values: any) => {
      // const sendValue = {
      //   ...values,
      //   birthDate: moment(values.birthDate, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      // };
    },
  });
  const handleChangeGender = (
    event: React.MouseEvent<HTMLElement>,
    newGender: string
  ) => {
    if (newGender) {
      setGender(newGender);
      formik.setFieldValue('gender', newGender);
    }
  };

  return (
    <>
      <ProductNav
        title="แก้ไขข้อมูลส่วนตัว"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/setting/company/info'}
      >
        <Button
          type="button"
          variant="outlined"
          color="blueGrey"
          style={{
            color: '#78909C',
            width: '120px',
            height: '40px',
          }}
          onClick={() => {
            router.push('/dashboard');
          }}
        >
          ยกเลิก
        </Button>
      </ProductNav>
      <PersonalInformationStyle>
        <form onSubmit={formik.handleSubmit}>
          <ImageField
            textUploadBtn="Upload"
            defaultBackground="/icons/blank-profile.svg"
            handleChange={(files) => formik.setFieldValue('imageFile', files)}
          />
          <p className="field-title">ชื่อจริง</p>
          <TextField
            type="text"
            name="name"
            placeholder="ชื่อจริงของคุณ"
            value={formik.values.name}
            onChange={formik.handleChange}
            error={formik.touched.name && Boolean(formik.errors.name)}
            helperText={formik.touched.name && formik.errors.name}
          />
          <p className="field-title">นามสกุล</p>
          <TextField
            type="text"
            name="lastName"
            placeholder="นามสกุลของคุณ"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            error={formik.touched.lastName && Boolean(formik.errors.lastName)}
            helperText={formik.touched.lastName && formik.errors.lastName}
          />
          <p className="field-title">โทรศัพท์</p>
          <TextField
            name="phoneNumber"
            placeholder="หมายเลขโทรศัพท์"
            value={formik.values.phoneNumber}
            onChange={formik.handleChange}
            inputProps={{ maxLength: 10 }}
            onKeyPress={(event) => {
              if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                event.preventDefault();
              }
            }}
            error={
              formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)
            }
            helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
          />
          <p className="field-title">เพศ</p>
          <div className="gender-wrap">
            <ToggleButtonGroup
              color="primary"
              value={gender}
              exclusive
              onChange={handleChangeGender}
              fullWidth
            >
              <ToggleButton value="male">ชาย</ToggleButton>
              <ToggleButton value="female">หญิง</ToggleButton>
              <ToggleButton value="any">อื่นๆ</ToggleButton>
            </ToggleButtonGroup>
          </div>
          <p className="field-title">อีเมล</p>
          <TextField
            type="email"
            name="email"
            placeholder="Email"
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
          />
          <p className="field-title">วันเกิด</p>
          <TextField
            type="text"
            fullWidth
            name="birthDate"
            size="small"
            placeholder="DD/MM/YY"
            value={formik.values.birthDate}
            onChange={formik.handleChange}
            onClick={() => {
              setOpenCalendar(true);
            }}
            error={formik.touched.birthDate && Boolean(formik.errors.birthDate)}
            helperText={formik.touched.birthDate && formik.errors.birthDate}
            InputProps={{
              readOnly: true,
              endAdornment: (
                <InputAdornment position="end">
                  <CalendarMonthRoundedIcon
                    style={{
                      color: '#263238',
                    }}
                  />
                </InputAdornment>
              ),
            }}
          />
          <Button
            type="submit"
            variant="contained"
            color="dark"
            fullWidth
            sx={{ fontSize: '16px', margin: '40px 0 24px 0' }}
          >
            เริ่มต้นใช้งาน
          </Button>
        </form>
      </PersonalInformationStyle>
      <HonDatePicker
        open={openCalendar}
        dateValue={formik.values.birthDate}
        handleChangeDate={(val: any) => {
          formik.setFieldValue('birthDate', val);
        }}
        handleOpen={(value: any) => setOpenCalendar(value)}
      />
    </>
  );
};

PersonalInformation.getLayout = function getLayout(page: ReactElement) {
  return <CompanyLayout>{page}</CompanyLayout>;
};

export default PersonalInformation;
