import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import ContactForm from '@/components/company/contact/ContactForm';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import apiContact from '@/services/core/contact';
import { setSnackBar } from '@/store/features/alert';
import { isEmpty } from 'lodash';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const Edit = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const { id } = router.query;
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<any>({});

  const handleUploadImage = async (res: any, file: any) => {
    const formData: any = new FormData();
    formData.append('file', file.get('file'));
    formData.append('contactId', id);
    const resUpload = await apiContact.uploadImage(formData);
    if (!resUpload.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขรายชื่อผู้จัดจำหน่ายสำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/company/supplier');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };
  const editContact = async (value: any, file: any) => {
    setSubmitting(true);
    const res = await apiContact.updateContact(value);
    if (!res.isError) {
      setDisable(true);
      if (file !== null) {
        await handleUploadImage(res, file);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'แก้ไขรายชื่อผู้จัดจำหน่ายสำเร็จ',
            severity: 'success',
          })
        );
        await router.push('/company/supplier');
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
    setDisable(false);
  };
  const getContactById = async () => {
    const res = await apiContact.getContactById(id as string);
    if (!res.isError) {
      setInitialValues(res.data);
    }
  };
  useEffect(() => {
    getContactById();
  }, []);
  return (
    <>
      <ProductNav
        title="แก้ไข Supplier"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/company/supplier'}
      />
      {!isEmpty(initialValues) &&
        isAllowed(permissions, 'company.supplier.update') && (
          <ContactForm
            submitting={submitting}
            disable={disable}
            handleSubmitContact={editContact}
            initialValues={initialValues}
            contactRoleTypeId={2}
          />
        )}
      {!isAllowed(permissions, 'company.supplier.update') && <NotPermission />}
    </>
  );
};
Edit.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default Edit;
