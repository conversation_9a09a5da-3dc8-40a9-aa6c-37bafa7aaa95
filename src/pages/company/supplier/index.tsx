import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import { FiltersType } from '@/types/app';
import { Avatar, Button } from '@mui/material';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import apiContact from '@/services/core/contact';
import { ContactResponseProps } from '@/types/contact';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { setSnackBar } from '@/store/features/alert';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

type ContactListPageProps = {
  contactRole: string;
  authorities: string[];
};
const initialOpenDeleteModal = {
  status: false,
  id: null,
  name: '',
};
const Index = ({ contactRole, authorities }: ContactListPageProps) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const { user } = useAppSelector(userSelector);
  const router = useRouter();
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchName: '',
  });
  const [contactList, setContactList] = useState<ContactResponseProps>({
    content: [],
    totalElements: null,
  });
  const [openDeleteModal, setOpenDeleteModal] = useState<any>(
    initialOpenDeleteModal
  );
  const [loadingDeleteContact, setLoadingDeleteContact] =
    useState<boolean>(false);
  const columns: GridColDef[] = [
    {
      field: 'code',
      headerName: 'รหัส',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 148,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'name',
      headerName: 'ชื่อลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center overflow-hidden"
            style={{
              columnGap: '14px',
            }}
          >
            <Avatar
              alt={params.row.name}
              src={!isEmpty(params.row.imageUrl) ? params.row.imageUrl : '#'}
              sx={{
                width: 34,
                height: 34,
                backgroundColor: '#30D5C7',
                textTransform: 'uppercase',
              }}
            />
            <div className="overflow-hidden text-ellipsis">
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'contactType',
      headerName: 'ประเภทลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="overflow-hidden text-ellipsis">
            {params.row.contactType.name || '-'}
          </div>
        );
      },
    },
    {
      field: 'phoneNumber',
      headerName: 'โทรศัพท์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.phoneNumber || '-'}</div>;
      },
    },

    {
      field: 'email',
      headerName: 'อีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 252,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'creditEnum',
      headerName: 'ประเภทการชำระเงิน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'creditType',
      headerName: 'เครดิต',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 0.7,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.creditType?.day
              ? `${params.row.creditType?.day} วัน`
              : '-'}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 170,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                disabled={!isAllowed(permissions, 'company.supplier.list')}
                onClick={() => {
                  router.push(`/company/supplier/${params.row.id}`);
                }}
              >
                รายละเอียด
              </Button>
              <PopoverAction
                triggerElement={
                  <div className="kebab">
                    <div className="dot" />
                  </div>
                }
                customItems={[
                  {
                    disabled: !isAllowed(
                      permissions,
                      'company.supplier.update'
                    ),
                    IconElement: () => <SvgPencilIcon />,
                    title: 'แก้ไข',
                    onAction: () => {
                      router.push(`/company/supplier/${params.row.id}/edit`);
                    },
                  },
                  {
                    disabled: !isAllowed(
                      permissions,
                      'company.supplier.delete'
                    ),
                    IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                    title: 'ลบรายการ',
                    onAction: () => {
                      setOpenDeleteModal({
                        status: true,
                        id: params.row.id,
                        name: params.row.name,
                      });
                    },
                    cssProps: {
                      color: '#D32F2F',
                      '&:hover': {
                        backgroundColor: '#FDE8EF',
                      },
                    },
                  },
                ]}
              />
            </div>
          </>
        );
      },
    },
  ];
  const handleRemoveContact = async () => {
    setLoadingDeleteContact(true);
    const res = await apiContact.deleteContact(openDeleteModal.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ลบ ${openDeleteModal.name} สำเร็จ`,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message.message}`,
          severity: 'error',
        })
      );
    }
    setOpenDeleteModal(initialOpenDeleteModal);
    setLoadingDeleteContact(false);
    getContactList();
  };
  const getContactList = async () => {
    const res = await apiContact.getContactList({
      ...filters,
      search: filters.searchName,
      contactRoleId: 2,
    });
    if (!res.isError) {
      setContactList({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };
  useEffect(() => {
    getContactList();
  }, [filters, user]);
  useEffect(() => {
    getContactList();
  }, [contactRole]);
  return (
    <>
      <AppModalConfirm
        open={openDeleteModal.status}
        isReason={false}
        onClickClose={() => {
          setOpenDeleteModal(false);
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ลบรายชื่อ Supplier`}
        confirmDescription={`ยืนยันที่จะลบ ${openDeleteModal.name} ออกจากรายชื่อ Supplier”`}
        loadingConfirm={loadingDeleteContact}
        onConfirm={() => {
          handleRemoveContact();
        }}
      />
      <ProductNav title="Supplier" showBorderBottom={true}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้าง Supplier"
            borderRadius={'8px'}
            disabled={!isAllowed(permissions, 'company.supplier.create')}
            onClick={() => {
              router.push('/company/supplier/create');
            }}
          />
        </ActionGroupStyle>
      </ProductNav>

      {isAllowed(permissions, 'company.supplier.list') ? (
        <>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div className={'px-4'}>{contactList.content.length} รายการ</div>
              <SearchInput
                makeSearchValue={(newValue) =>
                  setFilters({
                    ...filters,
                    searchName: newValue,
                    page: 0,
                  })
                }
              />
            </FilterWrapStyled>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle $isAvatar={true} $rows={contactList.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={170} />
                  <DataGrid
                    hideFooter={true}
                    rows={contactList.content}
                    columns={columns}
                    paginationMode="server"
                    rowCount={contactList?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={0}
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        </>
      ) : (
        <NotPermission />
      )}
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default Index;
