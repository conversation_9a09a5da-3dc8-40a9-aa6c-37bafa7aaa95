import { CompanyPageStyle } from '@/styles/company.styled';
import { useFormik } from 'formik';
import * as yup from 'yup';

import React, { ReactElement, useEffect, useState } from 'react';
import apiCompany from '@/services/core/company';
import Swal from 'sweetalert2';
import apiBusiness from '@/services/core/business';
import {
  FormControl,
  FormHelperText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import BasicHeader from '@/components/company/BasicHeader';
import { useRouter } from 'next/router';
import BlankLayout from '@/layouts/BlankLayout';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อบริษัท'),
  businessTypeId: yup.number().required('กรุณาระบุประเภทธุรกิจ'),
});

const CompanyCreatePage = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [businessType, setBusinessType] = useState<any>([]);
  const router = useRouter();
  const { pathname } = router;
  const formik = useFormik({
    initialValues: {
      name: '',
      businessTypeId: '',
    },
    validationSchema,
    onSubmit: async (values: any) => {
      setLoading(true);
      const res = await apiCompany.addCompany(values);
      if (!res.isError) {
        Swal.fire({
          title: 'บันทึกสำเร็จ',
          text: 'สร้างบริษัทสำเร็จ',
          icon: 'success',
        }).then(() => {
          router.push('/company');
        });
      } else {
        Swal.fire({
          title: 'บันทึกไม่สำเร็จ',
          text: 'โปรดลองใหม่อีกครั้ง',
          icon: 'error',
        });
      }
      setLoading(false);
    },
  });

  const getBusinessType = async () => {
    const res = await apiBusiness.getBusinessType();
    if (!res.isError) {
      setBusinessType(res.data);
    }
  };
  useEffect(() => {
    getBusinessType();
  }, []);
  return (
    <>
      <BasicHeader path={pathname} />
      <CompanyPageStyle>
        <div className="company">
          <div className="w-full">
            <div className="topic">สร้างบริษัท</div>
            <form onSubmit={formik.handleSubmit}>
              <div className="pb-4">
                <p>
                  <span className="text-error">*</span>ชื่อบริษัท
                </p>
                <TextField
                  type="text"
                  name="name"
                  placeholder="ชื่อบริษัทใหม่"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </div>
              <div className="pb-4">
                <p>
                  <span className="text-error">*</span>ประเภทธุรกิจ
                </p>
                <FormControl sx={{ width: '100%' }}>
                  <Select
                    sx={{
                      height: '40px',
                      border: '0',
                    }}
                    displayEmpty
                    name="businessTypeId"
                    value={formik.values.businessTypeId}
                    onChange={formik.handleChange}
                    input={<OutlinedInput />}
                    renderValue={(selected) => {
                      if (!selected) {
                        return (
                          <div className="text-[14px] text-[#B0BEC5]">
                            เลือกประเภทธุรกิจ
                          </div>
                        );
                      }
                      const selectedItem = businessType.find(
                        (item: any) => item.id === selected
                      );
                      return selectedItem ? selectedItem.name : '';
                    }}
                    error={
                      formik.touched.businessTypeId &&
                      Boolean(formik.errors.businessTypeId)
                    }
                  >
                    <MenuItem disabled value="">
                      <em>-- Please select --</em>
                    </MenuItem>
                    {businessType.map((name: any, index: number) => (
                      <MenuItem key={index} value={name.id}>
                        {name.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.businessTypeId &&
                    formik.errors.businessTypeId && (
                      <FormHelperText error>
                        {formik.errors.businessTypeId}
                      </FormHelperText>
                    )}
                </FormControl>
              </div>
              <div className="mt-[20px] pb-[40px]">
                <LoadingButton
                  type="submit"
                  variant="contained"
                  color="dark"
                  fullWidth
                  sx={{ fontSize: '16px' }}
                  loadingIndicator="Creating…"
                  loading={loading}
                >
                  <span>สร้างบริษัท</span>
                </LoadingButton>
              </div>
            </form>
          </div>
        </div>
      </CompanyPageStyle>
    </>
  );
};

CompanyCreatePage.getLayout = function getLayout(page: ReactElement) {
  return <BlankLayout>{page}</BlankLayout>;
};

export default CompanyCreatePage;
