'use client';

import { CompanyPageStyle } from '@/styles/company.styled';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { ReactElement, useEffect, useState } from 'react';
import CompanyList from '@/components/company/CompanyList';
import { isEmpty } from 'lodash';
import apiCompany from '@/services/core/company';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '@/store';
import { getUserProfile } from '@/store/features/user';
import BlankLayout from '@/layouts/BlankLayout';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { isAllowed } from '@/utils/permission';
import { setPermission } from '@/store/features/permission/actions';
import { permissionSelector } from '@/store/features/permission/reducer';

export type companyListProps = {
  name: string;
  businessType: {
    id: number;
    name: string;
    description: string;
  };
};
const CompanyPage = ({ authorities }: { authorities: string[] }) => {
  const [companyList, setCompanyList] = useState<companyListProps[] | null>([]);
  const router = useRouter();

  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const getCompanyList = async () => {
    const res = await apiCompany.getMeCompanyList();
    if (!res.isError && !isEmpty(res.data)) {
      setCompanyList(res.data);
    } else {
      setCompanyList(null);
    }
  };
  useEffect(() => {
    getCompanyList();
  }, []);

  useEffect(() => {
    dispatch(getUserProfile());
  }, []);

  return (
    <>
      <CompanyPageStyle>
        {companyList !== null ? (
          <CompanyList
            data={companyList}
            reLoadData={() => {
              getCompanyList();
            }}
          ></CompanyList>
        ) : (
          <div className="company">
            <Image
              src={'/images/company/empty-company.svg'}
              width={56}
              height={80}
              alt=""
            />
            <div className="text-zone">
              <div className="topic">คุณยังไม่มีบริษัทในระบบ</div>
              <div className="description">
                สามารถสร้างใหม่ได้ที่นี่ หรือ ต้องได้รับการเชิญเข้าร่วมงาน
                จากบริษัทอื่นเพื่อเริ่มต้นใช้งานระบบ
              </div>
            </div>
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="สร้างบริษัท"
              disabled={!isAllowed(permissions, 'company.info.create')}
              onClick={(_e: any) => {
                router.push('/company/create');
              }}
            />
          </div>
        )}
      </CompanyPageStyle>
    </>
  );
};

CompanyPage.getLayout = function getLayout(page: ReactElement) {
  return <BlankLayout>{page}</BlankLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default CompanyPage;
