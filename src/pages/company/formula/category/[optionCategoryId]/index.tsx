import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import styled from 'styled-components';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import Image from 'next/image';
import apiFormula from '@/services/stock/formula';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
  optionsCostTypeId: yup.number().required('กรุณาเลือกประเภทต้นทุน'),
});
const OptionsCategoryPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { optionCategoryId } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [initialValue] = useState<any>({
    id: null,
    name: '',
    optionsCostTypeId: null,
    optionsCategoryId: Number(optionCategoryId),
  });
  const [open, setOpen] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<any>({
    id: null,
    name: '',
    open: false,
  });
  const [optionCostTypeList, setOptionCostTypeList] = useState<any>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [actionForm, setActionForm] = useState<'edit' | 'create'>('create');
  const [optionCategoryById, setOptionCategoryById] = useState<any>({});
  const [filters, setFilters] = useState<any>({
    size: 10,
    page: 0,
    searchName: '',
    ascending: true,
    optionsCategoryId: Number(optionCategoryId),
    optionsCostTypeId: null,
  });
  const [rows, setRows] = useState<any>({
    content: [],
    totalElements: null,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    control,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });
  const watchCostType = useWatch({
    control,
    name: 'optionsCostTypeId',
    defaultValue: initialValue.optionsCostTypeId || '',
  });
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'optionsCategory',
      headerName: 'หมวดหมู่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 174,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.optionsCategory.name}</div>;
      },
    },
    {
      field: 'optionsCostType',
      headerName: 'ประเภทต้นทุน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.optionsCostType.name}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.list')
                }
                onClick={async () => {
                  await router.push(
                    `/company/formula/category/${optionCategoryId}/preset/${params.row.id}`
                  );
                }}
              >
                พรีเซ็ต
              </Button>
              <IconButton
                onClick={() => {
                  setValue('name', params.row.name);
                  setValue('id', params.row.id);
                  setValue('optionsCostTypeId', params.row.optionsCostType.id);
                  setActionForm('edit');
                  setOpen(true);
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.update')
                }
              >
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton
                onClick={() => {
                  setOpenDelete({
                    ...params.row,
                    open: true,
                  });
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.delete')
                }
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];

  const getList = async () => {
    const res = await apiFormula.getOption({
      ...filters,
      search: filters.searchName,
    });
    if (!res.isError) {
      setRows({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };

  const getOptionCostTypeList = async () => {
    const res = await apiFormula.getCostType();
    if (!res.isError) {
      const addId = res.data.map((item: any, index: number) => {
        return {
          ...item,
          id: index + 1,
        };
      });
      setOptionCostTypeList(addId);
    }
  };

  const getOptionsCategoryById = async () => {
    const res = await apiFormula.getOptionsCategoryById(
      Number(optionCategoryId)
    );
    if (!res.isError) {
      setOptionCategoryById(res.data);
    }
  };

  useEffect(() => {
    getOptionCostTypeList();
  }, []);

  useEffect(() => {
    if (optionCategoryId) {
      getOptionsCategoryById();
    }
  }, [optionCategoryId]);
  useEffect(() => {
    getList();
  }, [filters]);

  const handleOpen = () => {
    setOpen(true);
    setActionForm('create');
    reset(initialValue);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (actionForm === 'create') {
      const res = await apiFormula.createOptions(values);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'สร้างประเภทสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getList();
        setOpen(false);
      }
    } else {
      const res = await apiFormula.updateOptions(values);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'แก้ไขประเภทสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getList();
        setOpen(false);
      }
    }
    setSubmitting(false);
  };
  const handleDelete = async () => {
    setSubmitting(true);
    const res = await apiFormula.deleteOptions(openDelete.id);
    if (!res.isError) {
      await getList();
      setOpenDelete({
        ...openDelete,
        open: false,
      });
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? `ลบ ${openDelete.name} สำเร็จ` : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };
  return (
    <>
      <AppModalConfirm
        open={openDelete.open}
        isReason={false}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยืนยันการลบ
          </div>
        }
        confirmDescription={`คุณต้องการลบประเภท “${openDelete.name}”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleDelete();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <Dialog open={open} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  {actionForm === 'create' ? 'สร้างประเภท' : 'แก้ไขประเภท'}
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p>ชื่อ</p>
                    <TextField
                      placeholder="ชื่อ"
                      {...register('name')}
                      error={Boolean(hookFormErrors.name)}
                      helperText={hookFormErrors.name?.message as ReactNode}
                    />
                  </div>
                  <div>
                    <p>ประเภทต้นทุน</p>
                    <FormControl
                      sx={{
                        width: '100%',
                      }}
                    >
                      <Select
                        sx={{
                          height: '40px',
                          border: '0',
                        }}
                        displayEmpty
                        {...register('optionsCostTypeId')}
                        error={Boolean(hookFormErrors.optionsCostTypeId)}
                        input={<OutlinedInput />}
                        renderValue={(selected) => {
                          if (!selected) {
                            return (
                              <div className="text-[14px] text-[#B0BEC5]">
                                กรุณาเลือก
                              </div>
                            );
                          }
                          const selectedItem = optionCostTypeList.find(
                            (item: any) => item.id === selected
                          );
                          return selectedItem ? selectedItem.name : '';
                        }}
                        value={watchCostType || ''}
                        onChange={(e) => {
                          setValue('optionsCostTypeId', e.target.value, {
                            shouldValidate: true,
                          });
                        }}
                      >
                        {optionCostTypeList.map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {hookFormErrors.optionsCostTypeId && (
                        <FormHelperText error>
                          {
                            hookFormErrors.optionsCostTypeId
                              .message as ReactNode
                          }
                        </FormHelperText>
                      )}
                    </FormControl>
                  </div>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={handleClose}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <ProductNav
        title={!isEmpty(optionCategoryById) ? `${optionCategoryById.name}` : ''}
        showBorderBottom={true}
        backUrl={'/company/formula/category'}
      >
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างประเภท"
            borderRadius={'20px'}
            onClick={() => {
              handleOpen();
            }}
            disabled={
              !isAllowed(permissions, 'formula.formula-category.create')
            }
          />
        </ActionGroupStyle>
      </ProductNav>
      <FilterWrapStyle>
        <FormControl
          sx={{
            minWidth: '240px',
          }}
        >
          <Select
            sx={{
              height: '40px',
              border: '0',
            }}
            displayEmpty
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    ประเภทต้นทุนทั้งหมด
                  </div>
                );
              }
              const selectedItem = optionCostTypeList.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            value={filters.optionsCostTypeId || ''}
            onChange={(e) => {
              setFilters({
                ...filters,
                optionsCostTypeId: e.target.value,
              });
            }}
          >
            <MenuItem value={''}>ประเภทต้นทุนทั้งหมด</MenuItem>
            {optionCostTypeList.map((item: any) => (
              <MenuItem key={item.id} value={item.id}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <SearchInput
          makeSearchValue={(newValue) =>
            setFilters({
              ...filters,
              searchName: newValue,
              page: 0,
            })
          }
        />
      </FilterWrapStyle>
      {rows.totalElements !== null &&
        isAllowed(permissions, 'formula.formula-category.list') && (
          <AppContentStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content ? rows.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                  <div className="px-[16px]">
                    <AppPagination
                      filters={filters}
                      totalElements={
                        rows?.totalElements ? rows.totalElements : 0
                      }
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.formula-category.list') && (
        <NotPermission />
      )}
    </>
  );
};
const FilterWrapStyle = styled.div`
  width: 100%;
  min-height: 64px;
  justify-content: end;
  align-content: center;
  display: flex;
  padding: 0 24px;
  align-items: center;
  column-gap: 8px;
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
`;
OptionsCategoryPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default OptionsCategoryPage;
