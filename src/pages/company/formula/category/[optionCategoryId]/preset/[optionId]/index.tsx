import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import styled from 'styled-components';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import Image from 'next/image';
import apiFormula from '@/services/stock/formula';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ChipStyled = styled.div`
  height: 24px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  background-color: #f5f7f8;
  border-radius: 12px;
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
});

const OptionsCategoryPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { optionCategoryId, optionId } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [initialValue] = useState<any>({
    optionsId: Number(optionId),
    name: '',
    values: [],
  });
  const [open, setOpen] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<any>({
    id: null,
    name: '',
    open: false,
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [optionById, setOptionById] = useState<any>({});
  const [actionForm, setActionForm] = useState<'edit' | 'create'>('create');
  const [optionCategoryById, setOptionCategoryById] = useState<any>({});
  const [filters, setFilters] = useState<any>({
    size: 10,
    page: 0,
    searchName: '',
    ascending: true,
    optionsId: Number(optionId),
  });
  const [rows, setRows] = useState<any>({
    content: [],
    totalElements: null,
  });
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    control,
    formState: { errors: hookFormErrors },
  } = useForm<any>({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });

  const watchValues = useWatch({
    control,
    name: 'values',
    defaultValue: initialValue.values || [],
  });
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'optionsType',
      headerName: 'ตัวแปร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 174,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center"
            style={{
              columnGap: '8px',
            }}
          >
            {!isEmpty(params.row.values)
              ? params.row.values.map((item: any, index: React.Key) => {
                  return (
                    <ChipStyled key={index}>{item.optionsType.name}</ChipStyled>
                  );
                })
              : '-'}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 64,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              {/* <IconButton */}
              {/*  onClick={() => { */}
              {/*    reset(params.row); */}
              {/*    setActionForm('edit'); */}
              {/*    setOpen(true); */}
              {/*  }} */}
              {/* > */}
              {/*  <Image src={'/icons/edit.svg'} width={24} height={24} alt="" /> */}
              {/* </IconButton> */}
              <IconButton
                onClick={() => {
                  setOpenDelete({
                    ...params.row,
                    open: true,
                  });
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.delete')
                }
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  const getOptionById = async () => {
    const res = await apiFormula.getOptionById(Number(optionId));
    if (!res.isError) {
      setOptionById(res.data);
    }
  };
  const getList = async () => {
    const res = await apiFormula.getPreset({
      ...filters,
      search: filters.searchName,
    });
    if (!res.isError) {
      setRows({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };
  const getOptionsCategoryById = async () => {
    const res = await apiFormula.getOptionsCategoryById(
      Number(optionCategoryId)
    );
    if (!res.isError) {
      setOptionCategoryById(res.data);
    }
  };
  useEffect(() => {
    if (optionId) {
      getOptionById();
      getOptionsCategoryById();
    }
  }, [optionId]);

  useEffect(() => {
    getList();
  }, [filters]);

  const handleOpen = () => {
    setOpen(true);
    setActionForm('create');
    reset(initialValue);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (actionForm === 'create') {
      const res = await apiFormula.createPreset(values);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'สร้างพรีเซ็ตสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getList();
        setOpen(false);
      }
    }
    setSubmitting(false);
  };
  const handleDelete = async () => {
    setSubmitting(true);
    const res = await apiFormula.deletePreset(openDelete.id);
    if (!res.isError) {
      await getList();
      setOpenDelete({
        ...openDelete,
        open: false,
      });
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? `ลบ ${openDelete.name} สำเร็จ` : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };
  useEffect(() => {
    if (open && actionForm === 'create') {
      optionCategoryById?.optionType.map((item: any, index: number) => {
        setValue(`values.${index}`, {
          value: '',
          optionsTypeId: item.optionsTypeId,
        });
      });
    }
  }, [open]);
  return (
    <>
      <AppModalConfirm
        open={openDelete.open}
        isReason={false}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยืนยันการลบ
          </div>
        }
        confirmDescription={`คุณต้องการลบพรีเซ็ต “${openDelete.name}”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleDelete();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <Dialog open={open} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  {actionForm === 'create' ? 'สร้างพรีเซ็ต' : 'แก้ไขพรีเซ็ต'}
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p>ชื่อ</p>
                    <TextField
                      placeholder="ชื่อ"
                      {...register('name')}
                      error={Boolean(hookFormErrors.name)}
                      helperText={hookFormErrors.name?.message as ReactNode}
                    />
                  </div>
                  {!isEmpty(optionCategoryById) &&
                    !isEmpty(optionCategoryById.optionType) && (
                      <FormGridStyle>
                        {optionCategoryById.optionType.map(
                          (item: any, index: number) => {
                            return (
                              <div key={item.id}>
                                <p>{item.optionsType.name}</p>
                                <TextField
                                  type="number"
                                  placeholder="กรอกค่า"
                                  {...register(`values.${index}.value`)}
                                  value={watchValues[index]?.value ?? ''}
                                  onKeyPress={(e) => {
                                    if (
                                      e.charCode === 45 ||
                                      e.charCode === 101
                                    ) {
                                      e.preventDefault(); // ป้องกันตัวอักษร '-' และ 'e'
                                    }
                                  }}
                                  onChange={(e) => {
                                    const inputValue = e.target.value;
                                    if (inputValue !== '00') {
                                      setValue(
                                        `values.${index}`,
                                        {
                                          value: inputValue,
                                          optionsTypeId: item.optionsTypeId,
                                        },
                                        {
                                          shouldValidate: true,
                                        }
                                      );
                                    }
                                  }}
                                  error={Boolean(
                                    (hookFormErrors.values as any)?.[index]
                                      ?.value
                                  )}
                                  helperText={
                                    (hookFormErrors.values as any)?.[index]
                                      ?.value?.message as ReactNode
                                  }
                                />
                              </div>
                            );
                          }
                        )}
                      </FormGridStyle>
                    )}
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={handleClose}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <ProductNav
        title={!isEmpty(optionById) ? `พรีเซ็ต ${optionById.name}` : ''}
        showBorderBottom={true}
        backUrl={`/company/formula/category/${optionCategoryId}`}
      >
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างพรีเซ็ต"
            borderRadius={'20px'}
            onClick={() => {
              handleOpen();
            }}
            disabled={
              !isAllowed(permissions, 'formula.formula-category.create')
            }
          />
        </ActionGroupStyle>
      </ProductNav>
      <FilterWrapStyle>
        <SearchInput
          makeSearchValue={(newValue) => {
            setFilters({
              ...filters,
              searchName: newValue,
              page: 0,
            });
          }}
        />
      </FilterWrapStyle>
      {rows.totalElements !== null &&
        isAllowed(permissions, 'formula.formula-category.list') && (
          <AppContentStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content ? rows.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                  <div className="px-[16px]">
                    <AppPagination
                      filters={filters}
                      totalElements={
                        rows?.totalElements ? rows.totalElements : 0
                      }
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.formula-category.list') && (
        <NotPermission />
      )}
    </>
  );
};
const FormGridStyle = styled.div`
  width: 100%;
  display: grid;
  column-gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(228px, 1fr));
`;
const FilterWrapStyle = styled.div`
  width: 100%;
  min-height: 64px;
  justify-content: end;
  align-content: center;
  display: flex;
  padding: 0 24px;
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
`;
OptionsCategoryPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default OptionsCategoryPage;
