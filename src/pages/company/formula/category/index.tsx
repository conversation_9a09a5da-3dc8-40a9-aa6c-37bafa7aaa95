import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import styled from 'styled-components';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import Image from 'next/image';
import apiFormula from '@/services/stock/formula';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ChipStyled = styled.div`
  height: 24px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  background-color: #f5f7f8;
  border-radius: 12px;
`;
const SelectChipStyled = styled.div`
  display: flex;
  align-items: center;
  column-gap: 8px;
  border-radius: 20px;
  padding: 4px 12px;
  background: #edf1f2;
  .color-name {
    font-size: 14px;
    padding-right: 8px;
    line-height: 1;
  }
`;
const CheckboxWrap = styled.div`
  span {
    &:hover {
      background: none !important;
    }
  }
  .MuiTouchRipple-root {
    display: none !important;
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
  optionsType: yup.array().of(yup.number().required()).min(1, 'กรุณาเลือก'),
});

const OptionsCategoryPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [initialValue] = useState<any>({
    id: null,
    name: '',
    optionsType: [],
  });
  const [open, setOpen] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<any>({
    id: null,
    name: '',
    open: false,
  });
  const [optionTypeList, setOptionTypeList] = useState<any>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [actionForm, setActionForm] = useState<'edit' | 'create'>('create');
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchName: '',
    ascending: true,
  });
  const [rows, setRows] = useState<any>({
    content: [],
    totalElements: null,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    getValues,
    control,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });
  const watchOptionsType = useWatch({
    control,
    name: 'optionsType',
    defaultValue: initialValue.optionsType || [],
  });
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'optionsType',
      headerName: 'ตัวแปร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 174,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center"
            style={{
              columnGap: '8px',
            }}
          >
            {!isEmpty(params.row.optionType)
              ? params.row.optionType.map((item: any, index: React.Key) => {
                  return (
                    <ChipStyled key={index}>{item.optionsType.name}</ChipStyled>
                  );
                })
              : '-'}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.list')
                }
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={async () => {
                  await router.push(
                    `/company/formula/category/${params.row.id}`
                  );
                }}
              >
                ประเภท
              </Button>
              <IconButton
                onClick={() => {
                  reset(params.row);
                  const selectedOptionIds = params.row.optionType.map(
                    (option: any) => option.optionsTypeId
                  );
                  setValue('optionsType', selectedOptionIds);
                  setActionForm('edit');
                  setOpen(true);
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.update')
                }
              >
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton
                onClick={() => {
                  setOpenDelete({
                    ...params.row,
                    open: true,
                  });
                }}
                disabled={
                  !isAllowed(permissions, 'formula.formula-category.delete')
                }
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];

  const getList = async () => {
    const res = await apiFormula.getOptionsCategory({
      ...filters,
      search: filters.searchName,
    });
    if (!res.isError) {
      setRows({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };
  const getOptionTypeList = async () => {
    const res = await apiFormula.getOptionsType();
    if (!res.isError) {
      setOptionTypeList(res.data);
    }
  };

  useEffect(() => {
    getOptionTypeList();
  }, []);
  useEffect(() => {
    getList();
  }, [filters]);

  const handleOpen = () => {
    setValue('optionsType', []);
    setOpen(true);
    setActionForm('create');
    reset(initialValue);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      if (actionForm === 'create') {
        const res = await apiFormula.createOptionsCategory(values);
        if (!res.isError) {
          const sendValue = {
            ...values,
            optionsCategoryId: res.data.id,
            optionsType: !isEmpty(getValues('optionsType'))
              ? getValues('optionsType').map((id: number) => ({
                  optionsTypeId: id,
                }))
              : [],
          };
          const resUpdateTypeGroup =
            await apiFormula.updateOptionsCategoryTypeGroup(sendValue);

          dispatch(
            setSnackBar({
              status: true,
              text: !resUpdateTypeGroup.isError
                ? 'สร้างหมวดหมู่สำเร็จ'
                : 'เกิดข้อผิดพลาด',
              severity: !resUpdateTypeGroup.isError ? 'success' : 'error',
            })
          );

          if (!resUpdateTypeGroup.isError) {
            await getList();
            setOpen(false);
          }
        }
      } else {
        const sendValue = {
          ...values,
          optionsCategoryId: values.id,
          optionsType: !isEmpty(getValues('optionsType'))
            ? getValues('optionsType').map((id: number) => ({
                optionsTypeId: id,
              }))
            : [],
        };

        const resUpdateTypeGroup =
          await apiFormula.updateOptionsCategoryTypeGroup(sendValue);
        const res = await apiFormula.updateOptionsCategory(values);

        dispatch(
          setSnackBar({
            status: true,
            text: !res.isError ? 'แก้ไขหมวดหมู่สำเร็จ' : 'เกิดข้อผิดพลาด',
            severity: !res.isError ? 'success' : 'error',
          })
        );

        if (!res.isError && !resUpdateTypeGroup.isError) {
          await getList();
          setOpen(false);
        }
      }
    } catch (error) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    setSubmitting(true);
    const res = await apiFormula.deleteOptionsCategory(openDelete.id);
    if (!res.isError) {
      await getList();
      setOpenDelete({
        ...openDelete,
        open: false,
      });
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? `ลบ ${openDelete.name} สำเร็จ` : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };
  // console.log('hookFormErrors', hookFormErrors);
  return (
    <>
      <AppModalConfirm
        open={openDelete.open}
        isReason={false}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยืนยันการลบ
          </div>
        }
        confirmDescription={`คุณต้องการลบหมวดหมู่ “${openDelete.name}”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleDelete();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <Dialog open={open} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {actionForm === 'create' ? 'สร้างหมวดหมู่' : 'แก้ไขหมวดหมู่'}
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div>
                    <p>ชื่อ</p>
                    <TextField
                      placeholder="ชื่อ"
                      {...register('name')}
                      error={Boolean(hookFormErrors.name)}
                      helperText={hookFormErrors.name?.message as ReactNode}
                    />
                  </div>
                  <div>
                    <p>ตัวแปร</p>
                    <FormControl
                      fullWidth
                      sx={{
                        height: '40px',
                      }}
                    >
                      <Select
                        fullWidth
                        multiple
                        value={watchOptionsType} // array
                        onChange={(e) =>
                          setValue('optionsType', e.target.value as number[], {
                            shouldValidate: true,
                          })
                        }
                        error={Boolean(hookFormErrors.optionsType)}
                        displayEmpty
                        sx={{
                          fontSize: '14px',
                          fieldset: {
                            borderWidth: '1px !important',
                          },
                        }}
                        renderValue={(selected) => (
                          <div
                            className="flex flex-wrap gap-2"
                            style={{ padding: '4px 0' }}
                          >
                            {selected.length > 0 ? (
                              selected.map((value: any) => {
                                const option = optionTypeList.find(
                                  (item: any) => item.id === value
                                );
                                return (
                                  <SelectChipStyled key={option?.id}>
                                    {option?.name}
                                  </SelectChipStyled>
                                );
                              })
                            ) : (
                              <SelectChipStyled
                                style={{
                                  height: '32px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  padding: '0 8px',
                                }}
                              >
                                <div className="color-name !p-0">
                                  กรุณาเลือก
                                </div>
                              </SelectChipStyled>
                            )}
                          </div>
                        )}
                      >
                        <MenuItem value="" disabled sx={{ fontSize: '14px' }}>
                          กรุณาเลือก
                        </MenuItem>
                        {optionTypeList.map((item: any) => (
                          <MenuItem
                            key={item.id}
                            value={item.id}
                            sx={{
                              fontSize: '14px',
                              minHeight: '40px !important',
                              height: '40px !important',
                            }}
                          >
                            <CheckboxWrap>
                              <Checkbox
                                color="primary"
                                checked={watchOptionsType.includes(item.id)}
                              />
                            </CheckboxWrap>
                            <span>{item.name}</span>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {Boolean(hookFormErrors.optionsType) && (
                      <FormHelperText
                        error
                        sx={{
                          marginLeft: '14px',
                        }}
                      >
                        {hookFormErrors.optionsType?.message as ReactNode}
                      </FormHelperText>
                    )}
                  </div>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={handleClose}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <ProductNav title="หมวดหมู่" showBorderBottom={true}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างหมวดหมู่"
            borderRadius={'20px'}
            onClick={() => {
              handleOpen();
            }}
            disabled={
              !isAllowed(permissions, 'formula.formula-category.create')
            }
          />
        </ActionGroupStyle>
      </ProductNav>
      <FilterWrapStyle>
        <SearchInput
          makeSearchValue={(newValue) =>
            setFilters({
              ...filters,
              searchName: newValue,
              page: 0,
            })
          }
        />
      </FilterWrapStyle>
      {rows.totalElements !== null &&
        isAllowed(permissions, 'formula.formula-category.list') && (
          <AppContentStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content ? rows.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                  <div className="px-[16px]">
                    <AppPagination
                      filters={filters}
                      totalElements={
                        rows?.totalElements ? rows.totalElements : 0
                      }
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.formula-category.list') && (
        <NotPermission />
      )}
    </>
  );
};
const FilterWrapStyle = styled.div`
  width: 100%;
  min-height: 64px;
  justify-content: end;
  align-content: center;
  display: flex;
  padding: 0 24px;
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
`;
OptionsCategoryPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default OptionsCategoryPage;
