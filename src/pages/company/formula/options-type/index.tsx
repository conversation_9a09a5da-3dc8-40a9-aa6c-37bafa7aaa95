import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import apiFormula from '@/services/stock/formula';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import short from 'short-uuid';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
});

const OptionsTypePage = ({ authorities }: { authorities: string[] }) => {
  const [open, setOpen] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [actionForm, setActionForm] = useState<'edit' | 'create'>('create');
  const [rows, setRows] = useState<any>({
    content: [],
  });
  const [initialValue] = useState<any>({
    name: '',
    uuid: '',
    id: null,
  });
  const [openDelete, setOpenDelete] = useState<any>({
    id: null,
    name: '',
    open: false,
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <IconButton
              disabled={!isAllowed(permissions, 'formula.options-type.create')}
              onClick={() => {
                reset(params.row);
                setActionForm('edit');
                setOpen(true);
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              disabled={!isAllowed(permissions, 'formula.options-type.delete')}
              onClick={() => {
                setOpenDelete({
                  ...params.row,
                  open: true,
                });
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </div>
        );
      },
    },
  ];

  const handleDelete = async () => {
    setSubmitting(true);
    const res = await apiFormula.deleteOptionsType(openDelete.id);
    if (!res.isError) {
      await getList();
      setOpenDelete({
        ...openDelete,
        open: false,
      });
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? `ลบ ${openDelete.name} สำเร็จ` : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };

  const getList = async () => {
    const res = await apiFormula.getOptionsType();
    if (!res.isError) {
      setRows({
        content: res.data,
      });
    }
  };

  useEffect(() => {
    getList();
  }, []);

  const handleClose = () => {
    setOpen(false);
  };

  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (actionForm === 'create') {
      const uuid = `hon${short.generate()}`;
      const sendValue = {
        ...values,
        uuid,
      };
      const res = await apiFormula.createOptionsType(sendValue);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'สร้างตัวแปรสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getList();
        setOpen(false);
      }
    } else {
      const res = await apiFormula.updateOptionsType(values);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'แก้ไขตัวแปรสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getList();
        setOpen(false);
      }
    }

    setSubmitting(false);
  };

  const handleOpen = () => {
    setOpen(true);
    setActionForm('create');
    reset(initialValue);
  };

  return (
    <>
      <AppModalConfirm
        open={openDelete.open}
        isReason={false}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยืนยันการลบ
          </div>
        }
        confirmDescription={`คุณต้องการลบตัวแปร “${openDelete.name}”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleDelete();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <Dialog open={open} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {actionForm === 'create' ? 'สร้างตัวแปร' : 'แก้ไขตัวแปร'}
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p>ชื่อ</p>
                    <TextField
                      placeholder="ชื่อ"
                      {...register('name')}
                      error={Boolean(hookFormErrors.name)}
                      helperText={hookFormErrors.name?.message as ReactNode}
                      autoFocus={true}
                    />
                  </div>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={handleClose}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <ProductNav title="ตัวแปร" showBorderBottom={true}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างตัวแปร"
            borderRadius={'20px'}
            onClick={handleOpen}
            disabled={!isAllowed(permissions, 'formula.options-type.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      {rows?.content?.length > 0 &&
        isAllowed(permissions, 'formula.options-type.list') && (
          <AppContentStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content || []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: TableNoRowsOverlay,
                        LoadingOverlay: TableLoadingOverlay,
                      }}
                    />
                  </ScrollBarStyled>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.options-type.list') && (
        <NotPermission />
      )}
    </>
  );
};

OptionsTypePage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default OptionsTypePage;
