import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import styled from 'styled-components';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import apiFormula from '@/services/stock/formula';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { isEmpty, isNull } from 'lodash';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { numberWithCommas } from '@/utils/number';
import { displayFormula } from '@/utils/formula/displayFormula';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const FormulaPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [initialValue] = useState<any>({
    formulaId: null,
  });
  const [open, setOpen] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<any>({
    id: null,
    name: '',
    open: false,
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [filters, setFilters] = useState<any>({
    size: 10,
    page: 0,
    optionsCategoryId: '',
    optionsId: '',
    ascending: true,
    searchName: '',
  });
  const [optionsCategoryList, setOptionsCategoryList] = useState<any>([]);
  const [optionsList, setOptionsList] = useState<any>([]);
  const [testFormulaProp, setTestFormulaProp] = useState<any>({});
  const [presetByOptionId, setPresetByOptionId] = useState<any>({});
  const [presetId, setPresetId] = useState<any>('');
  const [evaluateResult, setEvaluateResult] = useState<number | null>(null);
  const [rows, setRows] = useState<any>({
    content: [],
    totalElements: null,
  });

  const validationSchema = yup.object({
    ...(!isEmpty(testFormulaProp?.option?.optionsCategory?.optionType)
      ? testFormulaProp.option.optionsCategory.optionType.reduce(
          (schema: any, ot: any) => {
            schema[ot.optionsType.uuid] = yup
              .number()
              .required(`กรุณากรอก ${ot.optionsType.name}`)
              .typeError(`กรุณากรอกค่า`)
              .moreThan(0, 'กรุณากรอกค่า > 0');
            return schema;
          },
          {}
        )
      : {}),
  });
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors: hookFormErrors, isSubmitted },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });

  const columns: GridColDef[] = [
    {
      field: 'option',
      headerName: '#',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.option.name}</div>;
      },
    },
    {
      field: 'formula',
      headerName: 'สูตร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        let text = '';
        if (
          params.row &&
          params.row.option &&
          params.row.option.optionsCategory &&
          Array.isArray(params.row.option.optionsCategory.optionType)
        ) {
          const { optionType } = params.row.option.optionsCategory;
          const formatedOptionType = optionType.map(
            (item: any) => item.optionsType
          );
          text = displayFormula(params.row.formula, formatedOptionType);
        }
        return <div className="flex items-center">{text}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '84px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              onClick={async () => {
                reset();
                setValue('formulaId', params.row.id);
                setPresetId('');
                setEvaluateResult(null);
                setTestFormulaProp(params.row);
                setPresetByOptionId({});
                await getPresetByOptionId(params.row.option.id);
                setOpen(true);
              }}
              disabled={!isAllowed(permissions, 'formula.composition.list')}
            >
              ทดสอบ
            </Button>
            <IconButton
              onClick={async () => {
                await router.push(
                  `/company/formula/create?id=${params.row.id}`
                );
              }}
              disabled={!isAllowed(permissions, 'formula.composition.update')}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              onClick={() => {
                setOpenDelete({
                  ...params.row,
                  open: true,
                });
              }}
              disabled={!isAllowed(permissions, 'formula.composition.delete')}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </div>
        );
      },
    },
  ];

  const getPresetByOptionId = async (optionId: number) => {
    const res = await apiFormula.getPresetByOptionId(optionId);
    if (!res.isError) {
      setPresetByOptionId(res.data);
    }
  };

  const getList = async () => {
    const res = await apiFormula.getFormula({
      ...filters,
      searchTerm: filters.searchName,
    });
    if (!res.isError) {
      setRows({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };
  useEffect(() => {
    getList();
  }, [filters]);

  const handleDelete = async () => {
    setSubmitting(true);
    const res = await apiFormula.deleteFormula(openDelete.id);
    if (!res.isError) {
      await getList();
      setOpenDelete({
        ...openDelete,
        open: false,
      });
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? `ลบ ${openDelete.name} สำเร็จ` : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };
  const getOptionsCategoryList = async () => {
    const res = await apiFormula.getOptionsCategoryList();
    if (!res.isError) {
      setOptionsCategoryList(res.data);
    }
  };
  useEffect(() => {
    getOptionsCategoryList();
  }, []);
  const getOption = async () => {
    const res = await apiFormula.getOption(filters);
    if (!res.isError) {
      setOptionsList(res.data.content);
    }
  };
  useEffect(() => {
    getOption();
  }, [filters.optionsCategoryId]);

  const handleClose = () => {
    setOpen(false);
  };

  const onSubmit = async (values: any) => {
    setSubmitting(true);

    const parsedValues = Object.keys(values).reduce((acc: any, key: string) => {
      acc[key] = Number.isNaN(Number(values[key]))
        ? values[key]
        : Number(values[key]);
      return acc;
    }, {});

    const res = await apiFormula.evaluate(parsedValues);
    if (!res.isError) {
      setEvaluateResult(res.data);
    }
    setSubmitting(false);
  };

  useEffect(() => {
    if (presetId) {
      presetByOptionId
        .find((item: any) => item.id === presetId)
        .values.map((item: any) => {
          setValue(`${item.optionsType.uuid}`, Number(item.value));
        });
    }
  }, [presetId]);
  return (
    <>
      <AppModalConfirm
        open={openDelete.open}
        isReason={false}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยืนยันการลบ
          </div>
        }
        confirmDescription={`คุณต้องการลบสูตร “${openDelete.name}”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleDelete();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <Dialog open={open} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  ทดสอบสูตร
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                {!isEmpty(testFormulaProp) &&
                  (() => {
                    let text = '';
                    if (
                      testFormulaProp &&
                      testFormulaProp.option &&
                      testFormulaProp.option.optionsCategory &&
                      Array.isArray(
                        testFormulaProp.option.optionsCategory.optionType
                      )
                    ) {
                      const { optionType } =
                        testFormulaProp.option.optionsCategory;
                      const formatedOptionType = optionType.map(
                        (item: any) => item.optionsType
                      );
                      text = displayFormula(
                        testFormulaProp.formula,
                        formatedOptionType
                      );
                    }
                    return (
                      <div
                        style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          marginTop: '16px',
                          fontSize: '20px',
                          fontWeight: '600',
                          marginBottom: '-48px',
                        }}
                      >
                        {text}
                      </div>
                    );
                  })()}
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  {!isEmpty(presetByOptionId) && (
                    <div>
                      <p>พรีเซ็ต</p>
                      <FormControl
                        sx={{
                          width: '100%',
                        }}
                      >
                        <Select
                          sx={{
                            height: '40px',
                            border: '0',
                          }}
                          displayEmpty
                          input={<OutlinedInput />}
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <div className="text-[14px] text-[#B0BEC5]">
                                  กรุณาเลือก
                                </div>
                              );
                            }
                            const selectedItem = presetByOptionId.find(
                              (item: any) => item.id === selected
                            );
                            return selectedItem ? selectedItem.name : '';
                          }}
                          value={presetId || ''}
                          onChange={(e: any) => {
                            setPresetId(e.target.value);
                          }}
                        >
                          {presetByOptionId.map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </div>
                  )}
                  {!isEmpty(testFormulaProp) &&
                    testFormulaProp.option.optionsCategory.optionType.map(
                      (ot: any, index: number) => {
                        const { uuid } = ot.optionsType;
                        return (
                          <div key={index}>
                            <p>{ot.optionsType.name}</p>
                            <TextField
                              type="number"
                              placeholder={`${ot.optionsType.name}`}
                              {...register(`${uuid}`)}
                              value={watch(`${uuid}`) ?? ''}
                              onKeyPress={(e) => {
                                if (e.charCode === 45 || e.charCode === 101) {
                                  e.preventDefault(); // ป้องกันตัวอักษร '-' และ 'e'
                                }
                              }}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                if (inputValue !== '00') {
                                  setValue(`${uuid}`, e.target.value, {
                                    shouldValidate: !!isSubmitted,
                                  });
                                }
                              }}
                              error={Boolean(hookFormErrors[uuid])}
                              helperText={
                                hookFormErrors[uuid]?.message as ReactNode
                              }
                            />
                          </div>
                        );
                      }
                    )}
                  <EvaluateResultStyle>
                    {!isNull(evaluateResult) ? (
                      <span>
                        ผลลัพธ์ = {numberWithCommas(evaluateResult)} บาท
                      </span>
                    ) : (
                      <span
                        style={{
                          color: '#B0BEC5',
                        }}
                      >
                        ผลลัพธ์
                      </span>
                    )}
                  </EvaluateResultStyle>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={handleClose}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'คำนวณ'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <ProductNav title="สูตรทั้งหมด" showBorderBottom={true}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างสูตร"
            borderRadius={'20px'}
            disabled={!isAllowed(permissions, 'formula.composition.create')}
            onClick={async () => {
              await router.push('/company/formula/create');
            }}
          />
        </ActionGroupStyle>
      </ProductNav>

      {rows.totalElements !== null &&
        isAllowed(permissions, 'formula.composition.list') && (
          <AppContentStyle>
            <FilterWrapStyle>
              <FormControl
                sx={{
                  minWidth: '124px',
                }}
              >
                <Select
                  sx={{
                    height: '40px',
                    border: '0',
                  }}
                  displayEmpty
                  input={<OutlinedInput />}
                  renderValue={(selected) => {
                    if (!selected) {
                      return (
                        <div className="text-[14px] text-[#B0BEC5]">
                          หมวดหมู่ทั้งหมด
                        </div>
                      );
                    }
                    const selectedItem = optionsCategoryList.find(
                      (item: any) => item.id === selected
                    );
                    return selectedItem ? selectedItem.name : '';
                  }}
                  value={filters.optionsCategoryId || ''}
                  onChange={(e) => {
                    setOptionsList([]);
                    setFilters({
                      ...filters,
                      optionsCategoryId: e.target.value,
                      optionsId: '',
                    });
                  }}
                >
                  <MenuItem value={''}>หมวดหมู่ทั้งหมด</MenuItem>
                  {optionsCategoryList.map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl
                sx={{
                  minWidth: '124px',
                }}
              >
                <Select
                  sx={{
                    height: '40px',
                    border: '0',
                  }}
                  displayEmpty
                  disabled={!filters.optionsCategoryId || isEmpty(optionsList)}
                  input={<OutlinedInput />}
                  renderValue={(selected) => {
                    if (!selected) {
                      return (
                        <div className="text-[14px] text-[#B0BEC5]">
                          ประเภททั้งหมด
                        </div>
                      );
                    }
                    const selectedItem = optionsList.find(
                      (item: any) => item.id === selected
                    );
                    return selectedItem ? selectedItem.name : '';
                  }}
                  value={filters.optionsId || ''}
                  onChange={(e) => {
                    setFilters({
                      ...filters,
                      optionsId: e.target.value,
                    });
                  }}
                >
                  <MenuItem value={''}>ประเภททั้งหมด</MenuItem>
                  {optionsList?.map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <SearchInput
                makeSearchValue={(newValue) =>
                  setFilters({
                    ...filters,
                    searchName: newValue,
                    page: 0,
                  })
                }
              />
            </FilterWrapStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content ? rows.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                  <div className="px-[16px]">
                    <AppPagination
                      filters={filters}
                      totalElements={
                        rows?.totalElements ? rows.totalElements : 0
                      }
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.composition.list') && <NotPermission />}
    </>
  );
};
const EvaluateResultStyle = styled.div`
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px dashed #dbe2e5;
  margin-top: 24px;
`;
const FilterWrapStyle = styled.div`
  width: 100%;
  min-height: 64px;
  justify-content: end;
  align-content: center;
  display: flex;
  padding: 0 24px;
  align-items: center;
  column-gap: 8px;
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
`;
FormulaPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default FormulaPage;
