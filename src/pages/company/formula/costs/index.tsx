import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';

import {
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import apiFormula from '@/services/stock/formula';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CostsPage = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [rows, setRows] = useState<any>({
    content: [],
  });

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 174,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    // {
    //   field: '#',
    //   headerName: 'จัดการ',
    //   editable: false,
    //   headerAlign: 'right',
    //   align: 'right',
    //   minWidth: 124,
    //   disableColumnMenu: true,
    //   sortable: false,
    //   cellClassName: 'stickyCell',
    //   renderCell: (params: any) => {
    //     return (
    //       <>
    //         <div className="flex items-center">
    //           <IconButton
    //             onClick={() => {
    //               reset(params.row);
    //               const selectedOptionIds = params.row.optionType.map(
    //                 (option: any) => option.optionsTypeId
    //               );
    //               setSelectedOptionsType(selectedOptionIds);
    //               setActionForm('edit');
    //               setOpen(true);
    //             }}
    //           >
    //             <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
    //           </IconButton>
    //           <IconButton
    //             onClick={() => {
    //               setOpenDelete({
    //                 ...params.row,
    //                 open: true,
    //               });
    //             }}
    //           >
    //             <Image
    //               src={'/icons/delete.svg'}
    //               width={24}
    //               height={24}
    //               alt=""
    //             />
    //           </IconButton>
    //         </div>
    //       </>
    //     );
    //   },
    // },
  ];

  const getList = async () => {
    const res = await apiFormula.getCostType();
    if (!res.isError) {
      const addId = res.data.map((item: any, index: number) => {
        return {
          ...item,
          id: index + 1,
        };
      });
      setRows({
        content: addId,
      });
    }
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <>
      <ProductNav title="ประเภทต้นทุน" showBorderBottom={true} />
      {rows.totalElements !== null &&
        isAllowed(permissions, 'formula.costs.list') && (
          <AppContentStyle>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={false} $rows={rows?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <DataGrid
                      hideFooter={true}
                      rows={rows?.content ? rows.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={rows?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'formula.costs.list') && <NotPermission />}
    </>
  );
};
CostsPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default CostsPage;
