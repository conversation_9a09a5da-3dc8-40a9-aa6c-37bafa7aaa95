import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import FormulaForm from '@/components/formula/FormulaForm';
import apiFormula from '@/services/stock/formula';
import { useRouter } from 'next/router';

const CreateFormula = () => {
  const router = useRouter();
  const { id } = router.query;
  const [initialValue, setInitialValue] = useState<any>({
    optionsId: null,
    name: '',
    formula: '',
    nameLong: '',
  });
  const getFormulaById = async () => {
    const res = await apiFormula.getFormulaById(Number(id));
    if (!res.isError) {
      setInitialValue({
        optionsCategoryId: res.data.option.optionsCategory.id,
        optionsId: res.data.option.id,
        name: res.data.name,
        formula: res.data.formula,
        nameLong: res.data.nameLong,
      });
    }
  };
  useEffect(() => {
    if (id) {
      getFormulaById();
    }
  }, [id]);
  return (
    <>
      <ProductNav
        title={id === undefined ? 'สร้างสูตร' : 'แก้ไขสูตร'}
        showBorderBottom={true}
        backUrl={'/company/formula/list'}
      />
      <FormulaForm initialValue={initialValue} />
    </>
  );
};
CreateFormula.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default CreateFormula;
