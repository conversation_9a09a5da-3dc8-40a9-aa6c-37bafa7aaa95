import React, { ReactElement, useState } from 'react';
import 'dayjs/locale/th';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import apiContact from '@/services/core/contact';
import { useRouter } from 'next/router';
import ContactForm from '@/components/company/contact/ContactForm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const CreateContact = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const handleUploadImage = async (res: any, file: any) => {
    const formData: any = new FormData();
    formData.append('file', file.get('file'));
    formData.append('contactId', res.data.id);
    const resUpload = await apiContact.uploadImage(formData);
    if (!resUpload.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มรายชื่อสำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/company/contact');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };
  const createContact = async (value: any, file: any) => {
    setSubmitting(true);
    const res = await apiContact.createContact(value);
    if (!res.isError) {
      setDisable(true);
      if (file !== null) {
        await handleUploadImage(res, file);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เพิ่มรายชื่อสำเร็จ',
            severity: 'success',
          })
        );
        await router.push('/company/contact');
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
    setDisable(false);
  };

  return (
    <>
      <ProductNav
        title="สร้างลูกค้า"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/company/contact'}
      />
      <ContactForm
        submitting={submitting}
        disable={disable}
        handleSubmitContact={createContact}
        contactRoleTypeId={1}
      />
    </>
  );
};

CreateContact.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default CreateContact;
