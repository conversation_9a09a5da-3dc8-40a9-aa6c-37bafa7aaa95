import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import { Avatar, Button } from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import { useAppDispatch, useAppSelector } from '@/store';
import apiContact from '@/services/core/contact';
import { userSelector } from '@/store/features/user';
import { GetServerSideProps } from 'next';
import { ContactResponseProps } from '@/types/contact';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import Image from 'next/image';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { setSnackBar } from '@/store/features/alert';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';

// const ContactChipStyled = styled.div`
//   height: 24px;
//   padding: 0 12px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   font-size: 10px;
//   background-color: #f5f7f8;
//   border-radius: 12px;
// `;
type ContactListPageProps = {
  contactRole: string;
  authorities: string[];
};
const initialOpenDeleteModal = {
  status: false,
  id: null,
  name: '',
};
const Index = ({ contactRole, authorities }: ContactListPageProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(userSelector);
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [openDeleteModal, setOpenDeleteModal] = useState<any>(
    initialOpenDeleteModal
  );
  const [loadingDeleteContact, setLoadingDeleteContact] =
    useState<boolean>(false);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchName: '',
  });
  const [contactList, setContactList] = useState<ContactResponseProps>({
    content: [],
    totalElements: null,
  });
  const columns: GridColDef[] = [
    {
      field: 'code',
      headerName: 'รหัส',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 148,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'name',
      headerName: 'ชื่อลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 234,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center overflow-hidden"
            style={{
              columnGap: '14px',
            }}
          >
            <Avatar
              alt={params.row.name}
              src={params.row?.imageUrl || '#'}
              sx={{
                width: 34,
                height: 34,
                backgroundColor: '#30D5C7',
                textTransform: 'uppercase',
              }}
            />
            <div className="overflow-hidden text-ellipsis">
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'customerLevelName',
      headerName: 'ระดับลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="overflow-hidden text-ellipsis">
            {params.row.customerLevelName || '-'}
          </div>
        );
      },
    },
    {
      field: 'contactType',
      headerName: 'ประเภทลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="overflow-hidden text-ellipsis">
            {params.row.contactType.name || '-'}
          </div>
        );
      },
    },
    // {
    //   field: 'contactRole',
    //   headerName: 'การติดต่อ',
    //   editable: false,
    //   headerAlign: 'left',
    //   align: 'left',
    //   minWidth: 174,
    //   flex: 1,
    //   disableColumnMenu: true,
    //   sortable: false,
    //   renderCell: (params: any) => {
    //     return (
    //       <div
    //         className="flex items-center"
    //         style={{
    //           columnGap: '8px',
    //         }}
    //       >
    //         {params.row?.contactRole ? (
    //           <ContactChipStyled>
    //             {params.row.contactRole.name}
    //           </ContactChipStyled>
    //         ) : (
    //           '-'
    //         )}
    //       </div>
    //     );
    //   },
    // },
    {
      field: 'phoneNumber',
      headerName: 'โทรศัพท์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.phoneNumber || '-'}</div>;
      },
    },

    {
      field: 'email',
      headerName: 'อีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 252,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'creditEnum',
      headerName: 'ประเภทการชำระเงิน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'creditType',
      headerName: 'เครดิต',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 0.7,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.creditType?.day
              ? `${params.row.creditType?.day} วัน`
              : '-'}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 170,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                disabled={!isAllowed(permissions, 'company.contact.list')}
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={() => {
                  router.push(`/company/contact/${params.row.id}`);
                }}
              >
                รายละเอียด
              </Button>
              {/* <KebabTable */}
              {/*  item={params.row} */}
              {/*  handleRemove={(item: any) => { */}
              {/*    setOpenDeleteModal({ */}
              {/*      status: true, */}
              {/*      id: item.id, */}
              {/*      name: item.name, */}
              {/*    }); */}
              {/*  }} */}
              {/*  isEdit={{ */}
              {/*    status: true, */}
              {/*    action: () => */}
              {/*      router.push(`/company/contact/${params.row.id}/edit`), */}
              {/*  }} */}
              {/*  isRemove={true} */}
              {/* /> */}
              <PopoverAction
                triggerElement={
                  <div className="kebab">
                    <div className="dot" />
                  </div>
                }
                customItems={[
                  {
                    disabled: !isAllowed(permissions, 'company.contact.update'),
                    IconElement: () => <SvgPencilIcon />,
                    title: 'แก้ไข',
                    onAction: () => {
                      router.push(`/company/contact/${params.row.id}/edit`);
                    },
                  },
                  {
                    disabled: !isAllowed(permissions, 'company.contact.delete'),
                    IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                    title: 'ลบ',
                    onAction: () => {
                      setOpenDeleteModal({
                        status: true,
                        id: params.row.id,
                        name: params.row.name,
                      });
                    },
                    cssProps: {
                      color: '#D32F2F',
                      '&:hover': {
                        backgroundColor: '#FDE8EF',
                      },
                    },
                  },
                ]}
              />
            </div>
          </>
        );
      },
    },
  ];

  const handleRemoveContact = async () => {
    setLoadingDeleteContact(true);
    const res = await apiContact.deleteContact(openDeleteModal.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ลบ ${openDeleteModal.name} สำเร็จ`,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message.message}`,
          severity: 'error',
        })
      );
    }
    setOpenDeleteModal(initialOpenDeleteModal);
    setLoadingDeleteContact(false);
    getContactList();
  };
  // const handleChangeStatus = (event: React.SyntheticEvent, newStatus: any) => {
  //   router.push(`/company/contact?contactRole=${newStatus}`, undefined);
  // };
  const getContactList = async () => {
    const res = await apiContact.getContactList({
      ...filters,
      search: filters.searchName,
      contactRoleId: 1,
    });
    if (!res.isError) {
      setContactList({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };
  useEffect(() => {
    getContactList();
  }, [filters, user]);
  useEffect(() => {
    getContactList();
  }, [contactRole]);
  return (
    <>
      <AppModalConfirm
        open={openDeleteModal.status}
        isReason={false}
        onClickClose={() => {
          setOpenDeleteModal(false);
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ลบรายชื่อติดต่อ`}
        confirmDescription={`ยืนยันที่จะลบ ${openDeleteModal.name} ออกจากรายชื่อติดต่อ”`}
        loadingConfirm={loadingDeleteContact}
        onConfirm={() => {
          handleRemoveContact();
        }}
      />
      <ProductNav title="รายชื่อลูกค้า" showBorderBottom={true}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างลูกค้า"
            borderRadius={'8px'}
            // bgColor={'linear-gradient(180deg, #16D5C5 0%, #12BEB2 100%)'}
            disabled={!isAllowed(permissions, 'company.contact.create')}
            onClick={() => {
              router.push('/company/contact/create');
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      {contactList.totalElements !== null &&
        isAllowed(permissions, 'company.contact.list') && (
          <AppContentStyle>
            <ScrollBarStyled>
              <FilterWrapStyled>
                <div
                  style={{
                    minWidth: '718px',
                  }}
                  className={'px-4'}
                >
                  {contactList.content.length} รายการ
                  {/* <Tabs */}
                  {/*  value={contactRole} */}
                  {/*  onChange={handleChangeStatus} */}
                  {/*  variant="standard" */}
                  {/* > */}
                  {/*  {contactRoleTab.map((item: any, index: number) => ( */}
                  {/*    <Tab */}
                  {/*      key={index} */}
                  {/*      label={ */}
                  {/*        <div */}
                  {/*          className="flex items-center" */}
                  {/*          style={{ */}
                  {/*            columnGap: '16px', */}
                  {/*            minHeight: '32px', */}
                  {/*          }} */}
                  {/*        > */}
                  {/*          {item.name} */}
                  {/*        </div> */}
                  {/*      } */}
                  {/*      value={item.contactRole} */}
                  {/*      sx={{ */}
                  {/*        color: */}
                  {/*          contactRole !== item.contactRole ? '#78909C' : '', */}
                  {/*      }} */}
                  {/*    /> */}
                  {/*  ))} */}
                  {/* </Tabs> */}
                </div>
                <SearchInput
                  makeSearchValue={(newValue) =>
                    setFilters({
                      ...filters,
                      searchName: newValue,
                      page: 0,
                    })
                  }
                />
              </FilterWrapStyled>
            </ScrollBarStyled>
            <div className="content-wrap">
              <AppTableStyle $isAvatar={true} $rows={contactList?.content}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={170} />
                    <DataGrid
                      hideFooter={true}
                      rows={contactList?.content ? contactList.content : []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={contactList?.totalElements || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                  <div className="px-[16px]">
                    <AppPagination
                      filters={filters}
                      totalElements={
                        contactList?.totalElements
                          ? contactList.totalElements
                          : 0
                      }
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                </div>
              </AppTableStyle>
            </div>
          </AppContentStyle>
        )}
      {!isAllowed(permissions, 'company.contact.list') && <NotPermission />}
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query, req, res } = context;
  const { contactRole } = query;
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
        contactRole: contactRole || 'all',
      },
    };
  }
  return {
    props: {},
  };
};
export default Index;
