import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import apiContact from '@/services/core/contact';
import { useRouter } from 'next/router';
import ContactDetail from '@/components/setting/contact/ContactDetail';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import NotPermission from '@/components/NotPermission';
import { isAllowed } from '@/utils/permission';

const Index = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [contactById, setContactById] = useState<any>({});
  // const [addressByContactId, setAddressByContactId] = useState<any>({});
  const getContactById = async () => {
    const res = await apiContact.getContactById(id as string);
    if (!res.isError) {
      setContactById(res.data);
    }
  };
  // const getAddressByContactId = async () => {
  //   const res = await apiContactAddress.getAddressByCustomerId(Number(id));
  //   if (!res.isError) {
  //     setAddressByContactId(res.data);
  //   }
  // };
  useEffect(() => {
    getContactById();
    // getAddressByContactId();
  }, []);

  return (
    <>
      <ProductNav
        title={'ข้อมูลลูกค้า'}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/company/contact'}
      />
      {isAllowed(permissions, 'company.contact.list') ? (
        <ContactDetail contactById={contactById} />
      ) : (
        <NotPermission />
      )}
      {/* {!isEmpty(contactById) && ( */}
      {/*  <ContactPreview */}
      {/*    contactById={contactById} */}
      {/*    addressByContactId={addressByContactId} */}
      {/*    reloadAddressByContactId={getAddressByContactId} */}
      {/*  /> */}
      {/* )} */}
    </>
  );
};

Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Index;
