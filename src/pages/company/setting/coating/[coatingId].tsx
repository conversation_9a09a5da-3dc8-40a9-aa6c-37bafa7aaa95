import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Breadcrumbs,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import apiFinish from '@/services/stock/finish';
import ProductNav from '@/components/product/ProductNav';
import Link from 'next/link';
import apiCoating from '@/services/stock/coating';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
});
const CoatingPage = () => {
  const router = useRouter();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [finishInfo, setFinishInfo] = useState<any>(null);
  const [rows, setRows] = useState([]);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      imageUrl: null,
    },
    validationSchema,
    onSubmit: (values: any) => {
      submitDialog(values);
    },
  });

  useEffect(() => {
    getCoatingInfo();
  }, []);

  const getCoatingInfo = async () => {
    const { coatingId } = router.query;
    const res = await apiCoating.getInfo(`${coatingId}`);
    if (res && !res.isError) {
      setFinishInfo(res.data);
      setRows(res.data.finish);
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    const { coatingId } = router.query;
    if (dialogMode === 'create') {
      const res = await apiFinish.create({ ...values, coatingId });
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
          getCoatingInfo()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      const res = await apiFinish.update({ ...values, coatingId });
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() =>
          getCoatingInfo()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      formik.setValues(data);
    } else {
      formik.resetForm();
    }
    setOpenDialog(true);
  };

  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiFinish.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getCoatingInfo();
          });
        } else {
          Swal.fire(
            'เกิดข้อผิดพลาด',
            `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
            'error'
          );
        }
      }
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <IconButton onClick={() => openDialog('update', params.row)}>
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton onClick={() => remove(params.row)}>
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];

  return (
    <>
      <ProductNav
        backUrl={`/company/setting/coating`}
        title={finishInfo ? finishInfo.name : ''}
        showBorderBottom
      />
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/company/setting/coating"
            style={{
              color: '#B0B4B6',
            }}
          >
            Coating
          </Link>
          {finishInfo ? <p>{finishInfo.name}</p> : ''}
        </Breadcrumbs>
        <ActionGroupStyle>
          <div onClick={() => openDialog('create', null)}>
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="Add Finish"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={100} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  rowCount={rows.length || 0}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Finish
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p className="mt-0">Name</p>
                    <TextField
                      placeholder="Finish Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </div>
                  <div>
                    <p>Description</p>
                    <TextField
                      name="description"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      multiline
                      rows={4}
                      placeholder="Finish Description"
                    />
                  </div>
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

CoatingPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default CoatingPage;
