import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import ImageField from '@/components/ImageField';
import apiCoating from '@/services/stock/coating';
import AppPagination from '@/components/global/AppPagination';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { numberWithCommas } from '@/utils/number';
import Image from 'next/image';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
  file: yup.mixed().required('กรุณาอัปโหลดไฟล์'),
});
const validationUpdateSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
});
const CoatingPage = () => {
  const router = useRouter();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
  });
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      imageUrl: null,
      file: null,
    },
    validationSchema:
      dialogMode === 'create' ? validationSchema : validationUpdateSchema,
    onSubmit: (values: any) => {
      submitDialog(values);
    },
  });

  useEffect(() => {
    getCoatingList();
  }, [filters]);

  const getCoatingList = async () => {
    const res = await apiCoating.getList(filters);
    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    const { id: materialId } = router.query;
    const formData = new FormData();
    if (values.file) {
      formData.append('file', values.file[0]);
    }
    delete values.file;
    if (dialogMode === 'create') {
      formData.append(
        'createCoatingRequest',
        JSON.stringify({ ...values, materialId })
      );
      const res = await apiCoating.create(formData);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
          getCoatingList()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      formData.append(
        'createCoatingRequest',
        JSON.stringify({ ...values, materialId })
      );
      const res = await apiCoating.update(formData);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() =>
          getCoatingList()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      formik.resetForm();
      formik.setValues(data);
    } else {
      formik.resetForm();
    }
    setOpenDialog(true);
  };

  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiCoating.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getCoatingList();
          });
        } else {
          Swal.fire('เกิดข้อผิดพลาด', `${res.message.message}`, 'error');
        }
      }
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            <div
              className="flex items-center justify-center"
              style={{
                columnGap: '14px',
              }}
            >
              <Image
                src={params.row.imageUrl || '/images/product/empty-product.svg'}
                width={40}
                height={40}
                alt=""
                style={{
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
              <div>{params.row.name}</div>
            </div>
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: 'finish',
      headerName: 'Finish',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.finish.length) || '-'}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 188,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={() => {
                  router.push(`/company/setting/coating/${params.row.id}`);
                }}
              >
                รายละเอียด
              </Button>
              <IconButton onClick={() => openDialog('update', params.row)}>
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton onClick={() => remove(params.row)}>
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="Coatings" showBorderBottom>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(newValue) =>
              setFilters({
                ...filters,
                searchName: newValue,
                page: 0,
              })
            }
          />
          <div onClick={() => openDialog('create', null)}>
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="Create Coating"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={188} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements || 0}
                  // pageSize={filters.sizes}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={totalElements || 0}
                  handleChangeFilters={(newValues: any) =>
                    setFilters(newValues)
                  }
                />
              </div>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={isOpenDialog}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Coating
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <ImageField
                    handleChange={(file: any) =>
                      formik.setFieldValue('file', file)
                    }
                    defaultBackground={
                      formik.values.imageUrl || '/images/add-image.svg'
                    }
                    borderRadius="14px"
                    alertRequire={
                      formik.touched.file && Boolean(formik.errors.file)
                    }
                    conditionText={
                      'Upload JPG, PNG or SVG best sizes 160x160 file maximum 2 mb.'
                    }
                    textUploadBtn="Upload"
                  />
                  <div>
                    <p>Name</p>
                    <TextField
                      placeholder="Coating Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </div>
                  <div>
                    <p>Description</p>
                    <TextField
                      name="description"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      multiline
                      rows={4}
                      placeholder="Coating Description"
                    />
                  </div>
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

CoatingPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default CoatingPage;
