import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import ImageField from '@/components/ImageField';
import apiExtra from '@/services/stock/extra';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { isEmpty, isNull } from 'lodash';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import styled from 'styled-components';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import apiRawMaterial from '@/services/stock/raw-material';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const BlockChipStyled = styled.div`
  display: flex;
  align-items: center;
  column-gap: 8px;
  border-radius: 20px;
  height: 24px;
  padding: 0 12px;
  background: #f5f7f8;
  box-shadow: 0px 0px 4px 0px rgba(38, 50, 56, 0.1);

  .block-name {
    font-size: 14px;
    line-height: 1;
  }
`;

const CheckboxWrap = styled.div`
  span {
    &:hover {
      background: none !important;
    }
  }
  .MuiTouchRipple-root {
    display: none !important;
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
  rawMaterialBlock: yup
    .array()
    .of(
      yup.object({
        id: yup.number().required('กรุณาเลือกบล็อกพิมพ์'),
      })
    )
    .min(1, 'กรุณาเลือกบล็อกพิมพ์')
    .required('กรุณาเลือกบล็อกพิมพ์'),
});

const ExtraPage = () => {
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [blockList, setBlockList] = useState<any[]>([]);
  const [file, setFile] = useState<any>(null);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
  });
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      rawMaterialBlock: [],
    } as any,
    validationSchema,
    enableReinitialize: true,
    validateOnChange: true,
    onSubmit: (values: any) => {
      submitDialog(values);
    },
  });

  useEffect(() => {
    getColorList();
  }, [filters]);
  useEffect(() => {
    getBlockList();
  }, []);
  const getBlockList = async () => {
    const res = await apiRawMaterial.getOptions({
      rawMaterialTypeId: 6,
    });
    if (res && !res.isError) {
      setBlockList(res.data);
    }
  };
  const getColorList = async () => {
    const res = await apiExtra.getList(filters);
    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
  };
  const submitDialog = async (values: any) => {
    setSubmitting(true);
    if (dialogMode === 'create') {
      const res = await apiExtra.create(values);
      if (!res.isError) {
        if (!isNull(file)) {
          const formData = new FormData();
          formData.append('file', file[0]);
          formData.append('id', res.data.id);
          const resUpload = await apiExtra.uploadImage(formData);
          if (!resUpload.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: 'เพิ่ม Extra สำเร็จ',
                severity: 'success',
              })
            );
            getColorList();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
                severity: 'error',
              })
            );
            getColorList();
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: 'เพิ่ม Extra สำเร็จ',
              severity: 'success',
            })
          );
          getColorList();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    } else if (dialogMode === 'update') {
      const res = await apiExtra.update(values);
      if (!res.isError) {
        if (!isNull(file)) {
          const formData = new FormData();
          formData.append('file', file[0]);
          formData.append('id', res.data.id);
          const resUpload = await apiExtra.uploadImage(formData);
          if (!resUpload.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: 'แก้ไข Extra สำเร็จ',
                severity: 'success',
              })
            );
            getColorList();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
                severity: 'error',
              })
            );
            getColorList();
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: 'แก้ไข Extra สำเร็จ',
              severity: 'success',
            })
          );
          getColorList();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    }
    setFile(null);
    setSubmitting(false);
    setOpenDialog(false);
  };
  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      formik.setValues(data);
    } else {
      formik.resetForm();
    }
    setOpenDialog(true);
  };

  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiExtra.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getColorList();
          });
        } else {
          Swal.fire('เกิดข้อผิดพลาด', `${res.message.message}`, 'error');
        }
      }
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Colors',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            <div
              className="flex items-center justify-center"
              style={{
                columnGap: '14px',
              }}
            >
              <Image
                src={params.row.imageUrl || '/images/product/empty-product.svg'}
                width={40}
                height={40}
                alt=""
                style={{
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
              <div>{params.row.name}</div>
            </div>
          </div>
        );
      },
    },
    {
      field: 'rawMaterialBlock',
      headerName: 'บล็อกพิมพ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={
              !isEmpty(params.row.rawMaterialBlock) &&
              params.row.rawMaterialBlock.map((item: any, index: number) => {
                return (
                  <span
                    key={index}
                    className={
                      index < params.row.rawMaterialBlock.length - 1
                        ? 'pr-[4px]'
                        : ''
                    }
                  >
                    {item.name}
                    {index < params.row.rawMaterialBlock.length - 1 && ','}
                  </span>
                );
              })
            }
            placement="bottom"
            arrow
          >
            <div className="overflow-hidden text-ellipsis">
              {!isEmpty(params.row.rawMaterialBlock) &&
                params.row.rawMaterialBlock.map((item: any, index: number) => {
                  return (
                    <span
                      key={index}
                      className={
                        index < params.row.rawMaterialBlock.length - 1
                          ? 'pr-[4px]'
                          : ''
                      }
                    >
                      {item.name}
                      {index < params.row.rawMaterialBlock.length - 1 && ','}
                    </span>
                  );
                })}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <IconButton onClick={() => openDialog('update', params.row)}>
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton onClick={() => remove(params.row)}>
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  const handleChangeBlockIds = (value: number[]) => {
    const newBlockIds = value.map((id: number) => {
      return { id };
    });
    formik.setFieldValue('rawMaterialBlock', newBlockIds);
  };
  return (
    <>
      <ProductNav title={'Extras'} showBorderBottom>
        <ActionGroupStyle>
          <div onClick={() => openDialog('create', null)}>
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="Create Extra"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={100} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements || 0}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
            <div className="px-[16px]">
              <AppPagination
                filters={filters}
                totalElements={totalElements || 0}
                handleChangeFilters={(newValues: any) => {
                  setFilters(newValues);
                }}
              />
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Extra
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <ImageField
                    handleChange={(file: any) => {
                      setFile(file);
                    }}
                    defaultBackground={
                      formik.values.imageUrl || '/images/add-image.svg'
                    }
                    borderRadius="14px"
                  />
                  <div>
                    <p>Name</p>
                    <TextField
                      placeholder="Extra Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </div>
                  <div>
                    <p>Description</p>
                    <TextField
                      name="description"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      multiline
                      rows={4}
                      placeholder="Extra Description"
                    />
                  </div>
                  {!isEmpty(blockList) && (
                    <div>
                      <p>บล็อกพิมพ์</p>
                      <FormControl
                        sx={{
                          minHeight: '40px',
                          '.MuiInputBase-root': {
                            height: '100%',
                            '>div': {
                              padding: '8px 14px 8px 8px',
                            },
                          },
                        }}
                        fullWidth
                      >
                        <Select
                          fullWidth
                          multiple
                          value={
                            !isEmpty(formik.values.rawMaterialBlock)
                              ? formik.values.rawMaterialBlock.map(
                                  (item: any) => item.id
                                )
                              : []
                          } // array
                          displayEmpty
                          onChange={(event: any) => {
                            handleChangeBlockIds(event.target.value);
                          }}
                          error={
                            formik.touched.rawMaterialBlock &&
                            Boolean(formik.errors.rawMaterialBlock)
                          }
                          sx={{
                            fontSize: '14px',
                          }}
                          renderValue={(selected) => (
                            <div className="flex flex-wrap gap-2 min-h-[24px] items-center">
                              {selected.map((value: number) => {
                                const block = blockList.find(
                                  (item: any) => item.id === value
                                );
                                return (
                                  <BlockChipStyled key={value}>
                                    <div className="block-name">
                                      {block.name}
                                    </div>
                                  </BlockChipStyled>
                                );
                              })}
                              {isEmpty(selected) && (
                                <BlockChipStyled>
                                  <div className="block-name">กรุณาเลือก</div>
                                </BlockChipStyled>
                              )}
                            </div>
                          )}
                        >
                          <MenuItem
                            value=""
                            disabled
                            sx={{
                              fontSize: '14px',
                            }}
                          >
                            กรุณาเลือก
                          </MenuItem>
                          {blockList.map((item: any) => (
                            <MenuItem
                              key={item.id}
                              value={item.id}
                              sx={{
                                fontSize: '14px',
                                minHeight: '40px !important',
                                height: '40px !important',
                              }}
                            >
                              <CheckboxWrap>
                                <Checkbox
                                  color="primary"
                                  checked={formik.values.rawMaterialBlock
                                    .map((item: any) => item.id)
                                    .includes(item.id)}
                                  icon={<IconUnCheckbox />}
                                  checkedIcon={<IconCheckboxBlack />}
                                  style={{ marginLeft: '-8px' }}
                                />
                              </CheckboxWrap>
                              <span>{item.name}</span>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      {formik.touched.rawMaterialBlock &&
                        formik.errors.rawMaterialBlock && (
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            กรุณาเลือกบล็อกพิมพ์
                          </FormHelperText>
                        )}
                    </div>
                  )}
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

ExtraPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ExtraPage;
