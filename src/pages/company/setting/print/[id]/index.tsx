import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Breadcrumbs,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import ImageField from '@/components/ImageField';
import apiColor from '@/services/stock/color';
import { isEmpty } from 'lodash';
import ProductNav from '@/components/product/ProductNav';
import Link from 'next/link';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import apiPrinting from '@/services/stock/printing';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
  file: yup.mixed().required('กรุณาอัปโหลดไฟล์'),
});
const validationUpdateSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
});
const ColorPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [data, setData] = useState<any>({});
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      id: '',
      printId: '',
      name: '',
      plateValue: '0',
      description: '',
      imageUrl: null,
      file: null,
    },
    validationSchema:
      dialogMode === 'create' ? validationSchema : validationUpdateSchema,
    onSubmit: (values: any) => {
      submitDialog(values);
    },
  });
  useEffect(() => {
    getColorListByPrintId();
  }, []);

  const getColorListByPrintId = async () => {
    const res = await apiPrinting.getById(Number(id));
    if (res && !res.isError) {
      setData(res.data);
    }
  };
  const submitDialog = async (values: any) => {
    setSubmitting(true);
    const formData = new FormData();
    if (values.file) {
      formData.append('file', values.file[0]);
    }
    delete values.file;
    if (dialogMode === 'create') {
      delete values.id;
      delete values.imageUrl;
      formData.append('colorRequest', JSON.stringify(values));
      const res = await apiColor.create(formData);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
          getColorListByPrintId()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      formData.append('colorRequest', JSON.stringify(values));
      const res = await apiColor.update(formData);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() =>
          getColorListByPrintId()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      formik.resetForm();
      formik.setValues(data);
    } else {
      formik.resetForm();
      formik.setFieldValue('printId', Number(id));
    }
    setOpenDialog(true);
  };
  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiColor.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getColorListByPrintId();
          });
        } else {
          Swal.fire(
            'เกิดข้อผิดพลาด',
            `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
            'error'
          );
        }
      }
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Colors',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 324,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
            }}
          >
            <Image
              src={params.row.imageUrl || '/images/product/empty-product.svg'}
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '8px',
                objectFit: 'cover',
              }}
            />
            <div>{params.row.name}</div>
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 324,
      flex: 2,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <IconButton onClick={() => openDialog('update', params.row)}>
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton onClick={() => remove(params.row)}>
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav
        title={data.name || ''}
        showBorderBottom
        backUrl={'/company/setting/print'}
      />
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/company/setting/print"
            className="opacity-40"
          >
            Prints
          </Link>
          {!isEmpty(data.name) && <p>{data.name}</p>}
        </Breadcrumbs>
        <ActionGroupStyle>
          <div onClick={() => openDialog('create', null)}>
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="Add Color"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={data.colors}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={100} />
                <DataGrid
                  hideFooter={true}
                  rows={!isEmpty(data.colors) ? data.colors : []}
                  columns={columns}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Color
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <ImageField
                    handleChange={(file: any) =>
                      formik.setFieldValue('file', file)
                    }
                    defaultBackground={
                      formik.values.imageUrl || '/images/add-image.svg'
                    }
                    borderRadius="14px"
                    alertRequire={
                      formik.touched.file && Boolean(formik.errors.file)
                    }
                    conditionText={
                      'Upload JPG, PNG or SVG best sizes 160x160 file maximum 2 mb.'
                    }
                    textUploadBtn="Upload"
                  />
                  <div>
                    <p>Name</p>
                    <TextField
                      placeholder="Color Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </div>
                  {/* <div> */}
                  {/*  <p>Plate</p> */}
                  {/*  <TextField */}
                  {/*    placeholder="Plate Value" */}
                  {/*    name="plateValue" */}
                  {/*    type="number" */}
                  {/*    value={formik.values.plateValue} */}
                  {/*    onChange={formik.handleChange} */}
                  {/*    error={ */}
                  {/*      formik.touched.plateValue && */}
                  {/*      Boolean(formik.errors.plateValue) */}
                  {/*    } */}
                  {/*    helperText={ */}
                  {/*      (formik.touched.plateValue && */}
                  {/*        formik.errors.plateValue) as string */}
                  {/*    } */}
                  {/*  /> */}
                  {/* </div> */}
                  <div>
                    <p>Description</p>
                    <TextField
                      name="description"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      multiline
                      rows={4}
                      placeholder="Color Description"
                    />
                  </div>
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

ColorPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ColorPage;
