import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import apiPrinting from '@/services/stock/printing';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import SearchInput from '@/components/SearchInput';
import ActionButton from '@/components/ActionButton';
import ProductNav from '@/components/product/ProductNav';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก ชื่อการพิมพ์'),
});

const PrintPage = () => {
  const router = useRouter();
  const [rows, setRows] = useState([]);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [filters, setFilters] = useState({
    page: 0,
    size: 20,
    searchName: '',
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      id: 0,
      name: '',
      description: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      submitDialog(values);
    },
  });

  useEffect(() => {
    getPrintList();
  }, [filters]);

  const getPrintList = async () => {
    const res = await apiPrinting.getList();
    if (res && !res.isError) {
      setRows(res.data);
      // setTotalElements(totalElement);
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    if (dialogMode === 'create') {
      const res = await apiPrinting.create(values);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
          getPrintList()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      const res = await apiPrinting.update(values);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() =>
          getPrintList()
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      formik.setValues(data);
    } else {
      formik.resetForm();
    }
    setOpenDialog(true);
  };

  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiPrinting.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getPrintList();
          });
        } else {
          Swal.fire(
            'เกิดข้อผิดพลาด',
            `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
            'error'
          );
        }
      }
    });
  };
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Mode',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: 'colors',
      headerName: 'Colors',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.colors.length)}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 188,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={() => {
                  router.push(`${router.pathname}/${params.row.id}`);
                }}
              >
                สีพิมพ์
              </Button>
              <IconButton onClick={() => openDialog('update', params.row)}>
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton onClick={() => remove(params.row)}>
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="Print" showBorderBottom>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(newValue) =>
              setFilters({
                ...filters,
                searchName: newValue,
                page: 0,
              })
            }
          />
          <div onClick={() => openDialog('create', null)}>
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="Create Print"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={188} />
                <DataGrid
                  hideFooter={true}
                  rows={!isEmpty(rows) ? rows : []}
                  columns={columns}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={isOpenDialog}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Print
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: 0,
                  }}
                >
                  <div>
                    <p>ชื่อการพิมพ์</p>
                    <TextField
                      type="text"
                      name="name"
                      placeholder="ระบุชื่อการพิมพ์"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        formik.touched.name && (formik.errors.name as string)
                      }
                    />
                  </div>
                  <div>
                    <p>รายละเอียด</p>
                    <TextField
                      type="text"
                      name="description"
                      placeholder="รายละเอียดการพิมพ์"
                      multiline
                      rows={4}
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.description &&
                        Boolean(formik.errors.description)
                      }
                      helperText={
                        formik.touched.description &&
                        (formik.errors.description as string)
                      }
                    />
                  </div>
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

PrintPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default PrintPage;
