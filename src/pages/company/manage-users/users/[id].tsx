import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import CreateUserForm from '@/components/setting/users/CreateUserForm';
import { useRouter } from 'next/router';
import apiCompany from '@/services/core/company';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const Edit = ({ authorities }: { authorities: string[] }) => {
  const [data, setData] = useState<any>({});
  const router = useRouter();
  const { id } = router.query;

  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const getMemberById = async () => {
    const res = await apiCompany.getMemberById(id);
    if (!res.isError) {
      setData(res);
    }
  };
  useEffect(() => {
    getMemberById();
  }, []);
  return (
    <>
      <ProductNav
        title="แก้ไขผู้ใช้งาน"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/company/manage-users/users'}
      />
      {isAllowed(permissions, 'company.manage-users.update') ? (
        <CreateUserForm type="edit" initialValue={data} />
      ) : (
        <NotPermission />
      )}
    </>
  );
};

Edit.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Edit;
