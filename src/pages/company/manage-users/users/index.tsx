import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import styled from 'styled-components';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import { Avatar, IconButton } from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import apiCompany from '@/services/core/company';
import AppPagination from '@/components/global/AppPagination';
import { ContentItem, ListType } from '@/types/user';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { OpenDeleteType } from '@/types/category';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { setSnackBar } from '@/store/features/alert';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';

const EmailStatusStyled = styled.div`
  display: flex;
  align-items: center;
  column-gap: 8px;
`;

const Index = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(userSelector);
  const { permissions } = useAppSelector(permissionSelector);
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [userList, setUserList] = useState<ListType>({
    content: [],
    totalElements: null,
  });

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const getMemberCompany = async () => {
    const res = await apiCompany.getMemberCompany({
      ...filters,
      name: filters.searchTerm || null,
    });
    if (!res.isError) {
      const addId = res.content.map((item: ContentItem, index: number) => {
        return {
          ...item,
          id: index + 1,
        };
      });
      const newData = {
        ...res,
        content: addId,
      };
      setUserList(newData);
    }
  };
  useEffect(() => {
    getMemberCompany();
  }, [filters, user]);

  const columns: GridColDef[] = [
    {
      field: 'userImg',
      headerName: 'ชื่อ',
      editable: false,
      align: 'left',
      headerAlign: 'left',
      width: 234,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center overflow-hidden"
            style={{
              columnGap: '14px',
            }}
          >
            <Avatar
              alt={params.row.name}
              src={!isEmpty(params.row.userImg) ? params.row.userImg : '#'}
              sx={{
                width: 34,
                height: 34,
                backgroundColor: '#30D5C7',
                textTransform: 'uppercase',
              }}
            />
            <div className="overflow-hidden text-ellipsis">
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'roleName',
      headerName: 'ตำแหน่ง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 134,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'email',
      headerName: 'อีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 224,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'phone',
      headerName: 'โทรศัพท์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 130,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.phone || '-'}</div>;
      },
    },
    {
      field: 'inviteVerified',
      headerName: 'การยืนยันอีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 222,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <>
            {params.row.inviteVerified ? (
              <EmailStatusStyled>
                <Image
                  src="/icons/icon-check-circle.svg"
                  width={24}
                  height={24}
                  alt=""
                />
                <span className="mt-[2px]">ยืนยันแล้ว</span>
              </EmailStatusStyled>
            ) : (
              <EmailStatusStyled>
                <Image
                  src="/icons/icon-warning.svg"
                  width={24}
                  height={24}
                  alt=""
                />
                <div>
                  ยังไม่ได้ยืนยัน{' '}
                  <span
                    className={`underline cursor-pointer ${
                      loading && 'text-[#CFD8DC] cursor-default'
                    }`}
                    onClick={() => {
                      if (!loading) {
                        inviteAgain(params.row);
                      }
                    }}
                  >
                    เชิญอีกครั้ง
                  </span>
                </div>
              </EmailStatusStyled>
            )}
          </>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 90,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <IconButton
              disabled={
                !params.row.inviteVerified ||
                !isAllowed(permissions, 'company.manage-users.update')
              }
              onClick={() => {
                router.push(
                  `/company/manage-users/users/${params.row.companyUserId}`
                );
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              disabled={!isAllowed(permissions, 'company.manage-users.delete')}
              onClick={() => {
                setOpenDelete({
                  status: true,
                  name: params.row.name,
                  id: params.row.companyUserId,
                });
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </>
        );
      },
    },
  ];

  const inviteAgain = async (row: any) => {
    setLoading(true);
    const res = await apiCompany.inviteMemberCompanyAgain(row.companyUserId);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ส่งคำเชิญถึง ${row.email} สำเร็จ`,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `เกิดข้อผิดพลาด`,
          severity: 'success',
        })
      );
    }
    setLoading(false);
  };
  const deleteMember = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiCompany.deleteMemberCompany(openDelete.id);
      if (res.status) {
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
        getMemberCompany();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: `${res.message}`,
            severity: 'error',
          })
        );
      }
    }
    setOpenDelete({
      ...openDelete,
      status: false,
      id: null,
    });
    setLoading(false);
  };
  const handleSearch = (searchTerm: string) => {
    setFilters({ ...filters, searchTerm, page: 0 });
  };
  return (
    <>
      <ProductNav title="ผู้ใช้งาน" showBorderBottom={false}>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />

          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="เพิ่มผู้ใช้งาน"
            borderRadius={'20px'}
            disabled={!isAllowed(permissions, 'company.manage-users.create')}
            onClick={() => {
              router.push(`${router.pathname}/create`);
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          {isAllowed(permissions, 'company.manage-users.list') ? (
            <AppTableStyle $rows={userList?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={90} />
                  <DataGrid
                    hideFooter={true}
                    rows={userList?.content ? userList.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={userList?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={
                      userList?.totalElements ? userList.totalElements : 0
                    }
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          ) : (
            <NotPermission />
          )}
        </div>
      </AppContentStyle>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={`คุณต้องการที่จะลบ ${openDelete.name}`}
        loadingConfirm={loading}
        onConfirm={() => {
          deleteMember();
        }}
      />
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Index;
