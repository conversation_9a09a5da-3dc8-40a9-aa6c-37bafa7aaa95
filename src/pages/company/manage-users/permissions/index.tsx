import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import { IconButton } from '@mui/material';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { useRouter } from 'next/router';
import useSWR from 'swr';
import AppPagination from '@/components/global/AppPagination';
import { numberWithCommas } from '@/utils/number';
import Swal from 'sweetalert2';
import apiRole from '@/services/core/role';
import { FiltersType } from '@/types/app';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const fetcher = async (params: FiltersType) => {
  return apiRole.getRole(params);
};
const Index = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const { data, mutate } = useSWR(filters, fetcher, {
    revalidateOnFocus: true,
  });
  const reloadData = async () => {
    const newData = await apiRole.getRole(filters);
    await mutate(newData, { revalidate: false });
  };

  useEffect(() => {
    reloadData();
  }, [filters]);

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'รายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 184,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'permissions',
      headerName: 'สิทธิการใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{numberWithCommas(params.row.permissions)}</>;
      },
    },
    {
      field: 'userCount',
      headerName: 'จำนวนผู้ใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{numberWithCommas(params.row.roleCount)}</>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 90,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <IconButton
              disabled={!isAllowed(permissions, 'company.permissions.update')}
              onClick={() => {
                router.push(
                  `/company/manage-users/permissions/${params.row.id}`
                );
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              disabled={!isAllowed(permissions, 'company.permissions.delete')}
              onClick={() => {
                deleteRole(params.row.id, params.row.name);
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </>
        );
      },
    },
  ];

  const handleDeleteRole = async (id: number, name: string) => {
    const res = await apiRole.deleteCustomRole(id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ลบ ${name} สำเร็จ`,
          severity: 'success',
        })
      );

      reloadData();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `เกิดข้อผิดพลาด`,
          severity: 'error',
        })
      );
    }
  };
  const deleteRole = (id: number, name: string) => {
    Swal.fire({
      iconHtml: '<div class="swal2-custom-icon delete"/>',
      title: `ลบ ${name}`,
      text: 'คุณต้องการที่จะลบข้อมูลสิทธิ์ของระบบ?',
      showCancelButton: true,
      showConfirmButton: true,
      showCloseButton: true,
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      reverseButtons: true,
      closeButtonHtml:
        '<img class="swal-close" src="/icons/icon-close.svg" alt=""/>',
      customClass: {
        popup: 'w-[600px] flex justify-center p-[28px] pt-[16px]',
        closeButton: 'swal-close',
        title: '!text-[#263238] !text-[28px] p-0 max-w-[90%] !m-auto !mb-[4px]',
        htmlContainer: 'w-[338px] max-w-[90%] !m-auto !text-[#263238]',
        actions: '!max-w-[100%] gap-[24px] w-full flex-nowrap',
        confirmButton:
          'w-full !max-w-[100%] font-prompt !text-[16px] !font-[400] !shadow-none !h-[48px] !m-0',
        cancelButton:
          'w-full !max-w-[100%] font-prompt !text-[16px] !font-[400] !bg-[#fff] !text-[#263238] ' +
          'border-cancel-btn-swal !shadow-none !bg-none !h-[48px] !m-0',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        handleDeleteRole(id, name);
      }
    });
  };
  const handleSearch = (value: string) => {
    setFilters({ ...filters, searchTerm: value, page: 0 });
  };
  return (
    <>
      <ProductNav title="สิทธิ์การใช้งาน" showBorderBottom={false}>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />
          <ActionButton
            disabled={!isAllowed(permissions, 'company.permissions.create')}
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างสิทธิ์"
            borderRadius={'20px'}
            onClick={() => {
              router.push('/company/manage-users/permissions/create');
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          {isAllowed(permissions, 'company.permissions.list') ? (
            <AppTableStyle $rows={data?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={90} />
                  <DataGrid
                    hideFooter={true}
                    rows={data?.content ? data.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={data?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={data?.totalElements ? data.totalElements : 0}
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          ) : (
            <NotPermission />
          )}
        </div>
      </AppContentStyle>
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default Index;
