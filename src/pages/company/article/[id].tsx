import Nav from '@/components/Nav';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import Container from '@/components/Container';
import ArticleForm from '@/components/article/ArticleForm';
import apiArticle from '@/services/core/article';
import { useRouter } from 'next/router';
import { LinearProgress } from '@mui/material';
import Swal from 'sweetalert2';

const UpdateArticle = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);

  useEffect(() => {
    if (router.query.id) {
      getArticle();
    }
  }, [router.query]);
  const save = async (values: any) => {
    const { id } = router.query;
    const res = await apiArticle.update(`${id}`, values);
    if (res && !res.isError) {
      Swal.fire({
        title: 'บันทึกเรียบร้อย',
        text: 'บันทึกบทความเรียบร้อย',
        icon: 'success',
      }).then(() => {
        router.push('/company/article');
      });
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถบันทึกบทความได้',
        icon: 'error',
      });
    }
  };

  const getArticle = async () => {
    setLoading(true);
    const { id } = router.query;
    if (id) {
      const res = await apiArticle.findOne(`${id}`);
      if (res && !res.isError) {
        setData(res.data);
      }
    }
    setLoading(false);
  };

  return (
    <>
      <Nav title="แก้ไขบทความ" backUrl="/company/article" />
      {loading && <LinearProgress />}
      {!loading && (
        <div className="flex justify-center">
          <Container width="100%">
            <ArticleForm
              initialValues={data}
              handleSubmit={(values) => save(values)}
            />
          </Container>
        </div>
      )}
    </>
  );
};

UpdateArticle.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default UpdateArticle;
