import Nav from '@/components/Nav';
import React, { ReactElement } from 'react';
import MainLayout from '@/layouts/MainLayout';
import Container from '@/components/Container';
import ArticleForm from '@/components/article/ArticleForm';
import apiArticle from '@/services/core/article';
import Swal from 'sweetalert2';
import { useRouter } from 'next/router';

const CreateArticle = () => {
  const router = useRouter();
  const save = async (values: any) => {
    const res = await apiArticle.create(values);
    if (res && !res.isError) {
      Swal.fire({
        title: 'บันทึกเรียบร้อย',
        text: 'บันทึกบทความเรียบร้อย',
        icon: 'success',
      }).then(() => {
        router.push('/company/article');
      });
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถบันทึกบทความได้',
        icon: 'error',
      });
    }
  };
  return (
    <>
      <Nav title="สร้างบทความ" backUrl="/company/article" />
      <div className="flex justify-center">
        <Container width="100%">
          <ArticleForm handleSubmit={(values) => save(values)} />
        </Container>
      </div>
    </>
  );
};

CreateArticle.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default CreateArticle;
