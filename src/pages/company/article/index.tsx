import Nav from '@/components/Nav';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import apiArticle from '@/services/core/article';
import {
  Button,
  IconButton,
  LinearProgress,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';
import dayjs from 'dayjs';
import { Edit3, Trash2 } from 'react-feather';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import { Add } from '@mui/icons-material';

const ArticleList = () => {
  const router = useRouter();
  const [rows, setRows] = useState<any[]>([]);
  const [totalRows, setTotalRows] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    page: 0,
    pageSize: 10,
  });

  useEffect(() => {
    getArticle();
  }, [filters]);
  const getArticle = async () => {
    setLoading(true);
    const res = await apiArticle.getAll(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalRows(res.data.totalElements);
    }
    setLoading(false);
  };

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setFilters({
      ...filters,
      page: value,
    });
  };

  const confirmDelete = async (article: any) => {
    Swal.fire({
      title: 'ยืนยันลบ',
      html: `<p>ลบ <span style="color: #09c69b">${article.title}</span> ?</p>`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiArticle.remove(article.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'บทความถูกลบแล้ว',
            icon: 'success',
            timer: 2000,
          }).then(() => {
            getArticle();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถลบบทความได้',
            icon: 'error',
          });
        }
      }
    });
  };

  return (
    <>
      <Nav title="รายการบทความ" subTitle={`ทั้งหมด : ${totalRows} บทความ`}>
        <Button
          variant="contained"
          color="dark"
          size="small"
          style={{ borderRadius: '32px', width: '100px' }}
          onClick={() => router.push('/company/article/create')}
          startIcon={<Add />}
        >
          สร้าง
        </Button>
      </Nav>
      {loading && <LinearProgress />}
      {!loading && (
        <div className="p-4 overflow-y-auto w-full">
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell width={130}>วันที่สร้าง</TableCell>
                <TableCell align="left">ชื่อบทความ</TableCell>
                <TableCell align="center" width={120}>
                  สถานะ
                </TableCell>
                <TableCell align="center" width={120}>
                  การจัดการ
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows.map((row) => (
                <TableRow
                  key={row.id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    {dayjs(row.createdDate).format('D/M/YY HH:mm')}
                  </TableCell>
                  <TableCell>{row.title}</TableCell>
                  <TableCell align="center">
                    {row.isPublished ? 'เผยแพร่' : 'ฉบับร่าง'}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-row gap-2">
                      <IconButton
                        onClick={() =>
                          router.push(`/company/article/${row.id}`)
                        }
                      >
                        <Edit3 />
                      </IconButton>
                      <IconButton onClick={() => confirmDelete(row)}>
                        <Trash2 />
                      </IconButton>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
      <div className="flex justify-center sm:justify-end pb-[5em]">
        {rows.length > 0 && (
          <Pagination
            count={Math.ceil(totalRows / filters.pageSize)}
            page={filters.page + 1}
            onChange={handleChangePage}
            color="primary"
          />
        )}
      </div>
    </>
  );
};

ArticleList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ArticleList;
