import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import apiCompany from '@/services/core/company';
import CompanyInfoForm from '@/components/setting/company/CompanyInfoForm';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';
import NotPermission from '@/components/NotPermission';

const Edit = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const updateCompany = async (data: any) => {
    setLoading(true);
    const resForm = await apiCompany.updateCompany(data);
    if (!resForm.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกสำเร็จ',
          severity: 'success',
        })
      );
    }
    setLoading(false);
  };
  return (
    <>
      <ProductNav
        title="แก้ไขข้อมูลบริษัท"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/company/info'}
      />
      {isAllowed(permissions, 'company.info.update') ? (
        <CompanyInfoForm
          isCheck={isCheck}
          loading={loading}
          handleIsCheck={(val: boolean) => {
            setIsCheck(val);
          }}
          handleUpdateCompany={(val: any) => {
            updateCompany(val);
          }}
        />
      ) : (
        <NotPermission />
      )}
    </>
  );
};

Edit.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Edit;
