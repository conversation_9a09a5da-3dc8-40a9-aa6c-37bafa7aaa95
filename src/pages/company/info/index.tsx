import React, { ReactElement, useEffect } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import Image from 'next/image';
import CompanyInfoFormPreview from '@/components/setting/company/CompanyInfoFormPreview';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';

const Index = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();

  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  return (
    <>
      <ProductNav
        title="ข้อมูลบริษัท"
        showBorderBottom={true}
        showAvatar={false}
      >
        <Button
          type="button"
          variant="outlined"
          color="blueGrey"
          style={{
            width: '40px',
            minWidth: '40px',
            height: '40px',
            padding: '0',
          }}
          disabled={!isAllowed(permissions, 'company.info.update')}
          onClick={(_e: any) => {
            router.push('/company/info/edit');
          }}
        >
          <Image src={'/icons/edit-black.svg'} width={24} height={24} alt="" />
        </Button>
      </ProductNav>
      <CompanyInfoFormPreview />
    </>
  );
};

Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Index;
