import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import PaymentNav from '@/components/accounting-finance/receipt/PaymentNav';
import PaymentOptionDataTable from '@/components/company/payment/PaymentOptionDataTable';
import ModalCreatePaymentOption from '@/components/company/payment/Modal/ModalCreatePaymentOption';
import apiBankCompany from '@/services/core/bank-company';
import { isEmpty } from 'lodash';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const Index = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [filters, setFilters] = useState({
    size: 10,
    page: 0,
  });
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<any>();
  const [dataEdit, setDataEdit] = useState<any>();
  const handleClose = () => {
    setOpen(false);
    setDataEdit({});
  };
  const getAllBankCompany = async (params?: any) => {
    const res = await apiBankCompany.getAllBankCompany(params);
    if (res && !res.isError) {
      setData(res.data);
    }
  };
  const createBankCompany = async (data: any) => {
    const res = await apiBankCompany.createBankCompany(data);
    if (res && !res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มข้อมูลช่องทางการชำระเงินสำเร็จ',
          severity: 'success',
        })
      );
      await getAllBankCompany(filters);
    }
  };
  const updateBankCompany = async (data: any, isActive?: boolean) => {
    const res = await apiBankCompany.updateBankCompany(data);
    if (res && !res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: `${
            !isActive
              ? 'แก้ไขข้อมูลช่องทางการชำระเงินสำเร็จ'
              : 'แก้ไขสถาณะช่องทางการชำระเงินสำเร็จ'
          }`,
          severity: 'success',
        })
      );
      await getAllBankCompany(filters);
    }
  };
  const deleteBankCompany = async (id: number) => {
    const res = await apiBankCompany.deleteBankCompany(id);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบข้อมูลช่องทางการชำระเงินสำเร็จ',
          severity: 'success',
        })
      );
      await getAllBankCompany(filters);
    }
  };
  useEffect(() => {
    getAllBankCompany(filters);
  }, [filters]);
  return (
    <>
      <PaymentNav title={'ช่องทางการชำระเงิน'} onAction={() => setOpen(true)} />
      {isAllowed(permissions, 'company.payment.list') ? (
        <PaymentOptionDataTable
          data={data}
          handleOpen={() => setOpen(true)}
          setDataEdit={(data: any) => setDataEdit(data)}
          onDelete={(id: number) => deleteBankCompany(id)}
          filters={filters}
          setFilters={(filters: any) => setFilters(filters)}
          updateStatusBankCompany={(data: any, isActive?: boolean) =>
            updateBankCompany(data, isActive)
          }
        />
      ) : (
        <NotPermission />
      )}
      <ModalCreatePaymentOption
        open={open}
        handleClose={() => {
          handleClose();
        }}
        dataEdit={dataEdit}
        onActionBankCompany={(data: any) =>
          isEmpty(dataEdit) ? createBankCompany(data) : updateBankCompany(data)
        }
      />
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default Index;
