import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiLayData from '@/services/order/layData';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import PrepareOrderFormZone from '@/components/order/prepare-order/PrepareOrderFormZone';
import PrepareOrderDetailZone from '@/components/order/prepare-order/PrepareOrderDetailZone';
import { useAppDispatch, useAppSelector } from '@/store';
import { orderSelector, setLayDataOrder } from '@/store/features/order';
import { isEmpty } from 'lodash';
import apiProductConfig from '@/services/stock/product-config';
import apiProduct from '@/services/stock/product';
import { FormControl, MenuItem, Select } from '@mui/material';
import { displayUnitList } from '@/utils/displayUnit';
import { layDataOrderType } from '@/types/responses/order/layDataOrder';

const PrepareOrderStyled = styled.div`
  width: 100%;
  display: flex;
  min-height: calc(100dvh - 64px);
`;
type Props = {
  layDataOrderFromServer: layDataOrderType;
};
const PrepareOrder = ({ layDataOrderFromServer }: Props) => {
  const dispatch = useAppDispatch();
  const { layDataOrder } = useAppSelector(orderSelector);
  const router = useRouter();
  const { orderId } = router.query;
  const [finishList, setFinishList] = useState<any>([]);
  const [productInfo, setProductInfo] = useState<any>({});
  const [displayUnit, setDisplayUnit] = useState<string>('mm');
  const getProductFinish = async (productId: number) => {
    const res = await apiProductConfig.getProductConfigOrder({
      productId,
      masterCategoryId: 3, // 3 = เคลือบ
    });
    if (!res.isError) {
      setFinishList(res.data);
    }
  };

  const getProductInfo = async (productId: number) => {
    const res = await apiProduct.getProductById(productId);
    if (res && !res.isError) {
      setProductInfo(res.data);
    }
  };

  useEffect(() => {
    getProductFinish(layDataOrderFromServer.productModel.productId).then();
    getProductInfo(layDataOrderFromServer.productModel.productId).then();
    dispatch(setLayDataOrder(layDataOrderFromServer));
  }, [layDataOrderFromServer]);

  return (
    <>
      <ProductNav
        title={'กำหนดสเปคสินค้า'}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={`/orders/${orderId}/spec?step=สเปคสินค้า`}
      >
        <FormControl fullWidth>
          <Select
            displayEmpty
            value={displayUnit}
            onChange={(e: any) => {
              setDisplayUnit(e.target.value);
            }}
          >
            {displayUnitList.map((item: string, index: number) => (
              <MenuItem key={index} value={item} className="!lowercase">
                {item}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </ProductNav>
      <PrepareOrderStyled>
        {!isEmpty(layDataOrder) && !isEmpty(productInfo) && (
          <>
            <PrepareOrderFormZone
              finishList={finishList}
              productInfo={productInfo}
              displayUnit={displayUnit}
            />
            <PrepareOrderDetailZone
              finishList={finishList}
              displayUnit={displayUnit}
            />
          </>
        )}
      </PrepareOrderStyled>
    </>
  );
};

PrepareOrder.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { orderId, layDataId } = context.query;
  const token = getCookie('access_token', {
    req: context.req,
  });
  const res = await apiLayData.getInfo(layDataId as string, token as string);
  if (res.data.isConfirm) {
    return {
      redirect: {
        permanent: false,
        destination: `/orders/${orderId}/spec`,
      },
    };
  }
  return {
    props: {
      layDataOrderFromServer: res.data,
    },
  };
};

export default PrepareOrder;
