import React, { ReactElement, useEffect, useState } from 'react';
import styled from 'styled-components';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import TableArtworkWithTopic from '@/components/order/artwork/TableArtworkWithTopic';
import { useRouter } from 'next/router';
import ModalAddDataArtwork from '@/components/order/artwork/modal/ModalAddDataArtwork';
import ModalDataArtworkDetail from '@/components/order/artwork/modal/ModalDataArtworkDetail';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import Image from 'next/image';
import ModalAddArtwork from '@/components/order/artwork/modal/ModalAddArtwork';
import ModalConfirmArtwork from '@/components/order/artwork/modal/ModalConfirmArtwork';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { numberWithCommas } from '@/utils/number';
import { isEmpty } from 'lodash';
import ModalArtworkExample from '@/components/order/artwork/modal/ModalArtworkExample';
import ModalAddFlie from '@/components/order/artwork/modal/ModalAddFile';

const ArtworkPageStyle = styled.div`
  width: 100%;
  padding: 24px;
  .topic {
    //color: #90a4ae;
    font-size: 12px;
    margin-bottom: 24px;
  }
  .artwork-table-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 48px;
  }
`;
const initialModalAddArtwork = {
  topic: '',
  description: '',
  url: '',
  artworkDetailId: '',
  artworkImages: [],
  artworkFile: {},
};
const ArtworkPage = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { orderId, layDataId } = router.query;
  const [openModalAddDataStep, setOpenModalAddDataStep] =
    useState<boolean>(false);
  const [openModalAddArtWorkStep, setOpenModalAddArtWorkStep] =
    useState<boolean>(false);
  const [openDetail, setOpenDetail] = useState<boolean>(false);
  const [deleteData, setDeleteData] = useState<any>({});
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [loadingDelete, setLoadingDelete] = useState<boolean>(false);
  const [openNextStep, setOpenNextStep] = useState<boolean>(false);
  const [loadingNextStep, setLoadingNextStep] = useState<boolean>(false);
  const [actionStep, setActionStep] = useState<number>(0);
  const [dataConfirmArtwork, setDataConfirmArtwork] = useState<any>({});
  const [openConfirmArtwork, setOpenConfirmArtwork] = useState<boolean>(false);
  const [layDataArtworkDetail, setLayDataArtworkDetail] = useState<any>([]);
  const [artworkConfigId, setArtworkConfigId] = useState<number>(0);
  const [artworkStatusId, setArtworkStatusId] = useState<number>(0);
  const [initialValueModalAddArtwork, setInitialValueModalAddArtwork] =
    useState<any>(initialModalAddArtwork);
  const [information, setInformation] = useState<any>({});
  const [openModalReportEditArtwork, setOpenModalReportEditArtwork] =
    useState<boolean>(false);
  const [loadingReportEditArtwork, setLoadingReportEditArtwork] =
    useState<boolean>(false);
  const [artworkDetailId, setArtworkDetailId] = useState<number>(0);
  const [openConfirmExample, setOpenConfirmExample] = useState<boolean>(false);
  const [loadingConfirmExample, setLoadingConfirmExample] =
    useState<boolean>(false);
  const [initialValueModalExample, setInitialValueModalExample] =
    useState<any>(false);
  const [openModalExample, setOpenModalExample] = useState<boolean>(false);
  const [openModalFile, setOpenModalFile] = useState<boolean>(false);
  const [initialValueModalFile, setInitialValueModalFile] = useState<any>({});
  const [openConfirmFile, setOpenConfirmFile] = useState<boolean>(false);
  const handleMakeDelete = (data: any, step: number) => {
    setActionStep(step);
    setDeleteData(data);
    setOpenConfirmDelete(true);
  };

  const handleDelete = async () => {
    setLoadingDelete(true);
    const res = await apiArtwork.deleteArtworkDetail(deleteData.id);
    if (!res.isError) {
      setOpenConfirmDelete(false);
      await getLayDataArtworkDetail();
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.error.response.data.message,
          severity: 'error',
        })
      );
    }
    setLoadingDelete(false);
  };
  const handleNextStep = async () => {
    setLoadingNextStep(true);
    const res = await apiArtwork.approvals({
      layDataArtWorkId: layDataId,
      statusId: artworkStatusId,
    });
    if (!res.isError) {
      setOpenNextStep(false);
      await handleAddArtwork(artworkConfigId + 1);
      await getLayDataArtworkDetail();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLoadingNextStep(false);
  };
  const handleOpenAddModal = (step: number, artworkConfigId: number) => {
    setInitialValueModalAddArtwork(initialModalAddArtwork);
    setActionStep(step);
    setArtworkConfigId(artworkConfigId);
    if (step === 1) {
      setOpenModalAddDataStep(true);
    } else if (step === 2) {
      setOpenModalAddArtWorkStep(true);
    }
  };
  const handleOpenEdit = (
    value: any,
    step: number,
    artworkConfigId: number
  ) => {
    setActionStep(step);
    setArtworkConfigId(artworkConfigId);
    if (step === 1) {
      setInitialValueModalAddArtwork({
        link: value.link || '',
        title: value.title || '',
        description: value.description || '',
        artworkDetailId: value.id,
        artworkImages: value.artworkImages || [],
        artworkFile: value.artworkFile || {},
      });
      setOpenModalAddDataStep(true);
    } else if (step === 2) {
      setInitialValueModalAddArtwork({
        title: value.title || '',
        description: value.description || '',
        artworkDetailId: value.id,
        artworkConfigId,
        artworkImages: value.artworkImages,
      });
      setOpenModalAddArtWorkStep(true);
    } else if (step === 3) {
      setInitialValueModalExample({
        title: value.title || '',
        description: value.description || '',
        artworkDetailId: value.id,
        artworkConfigId,
        artworkImages: value.artworkImages,
        artworkVideos: value.artworkVideos,
      });
      setOpenModalExample(true);
    } else if (step === 4) {
      setInitialValueModalFile({
        title: value.title || '',
        description: value.description || '',
        artworkDetailId: value.id,
        artworkConfigId,
        link: value.link || '',
      });
      setOpenModalFile(true);
    }
  };
  const handleMakeConfirmArtwork = (row: any, _step: number) => {
    setDataConfirmArtwork(row);
    setOpenConfirmArtwork(true);
  };
  const handleClickDetail = async (
    step: number,
    row: any,
    index: number | undefined
  ) => {
    if (step === 1 || step === 4) {
      setInformation({
        ...row,
        index,
      });
      setOpenDetail(true);
    } else if (step === 2 || step === 3) {
      await router.push(
        `/orders/${orderId}/artwork/${layDataId}/detail/${row.id}`
      );
    }
  };
  const getLayDataArtworkDetail = async () => {
    const res = await apiArtwork.getLayDataArtworkDetail(Number(layDataId));
    if (!res.isError) {
      setLayDataArtworkDetail(res.data);
      if (artworkConfigId) {
        const currentInitialModal = res.data.artworkConfig.find(
          (item: any) => item.id === artworkConfigId
        );
        const currentArtworkDetail = currentInitialModal.artworkDetail.find(
          (item: any) => item.id === initialValueModalAddArtwork.artworkDetailId
        );
        const currentArtworkExampleDetail =
          currentInitialModal.artworkDetail.find(
            (item: any) => item.id === initialValueModalExample.artworkDetailId
          );
        if (currentArtworkDetail) {
          setInitialValueModalAddArtwork({
            title: currentArtworkDetail.title || '',
            description: currentArtworkDetail.description || '',
            artworkDetailId: currentArtworkDetail.id,
            artworkConfigId,
            artworkImages: currentArtworkDetail.artworkImages,
            artworkFile: currentArtworkDetail.artworkFile || {},
          });
        }
        if (currentArtworkExampleDetail) {
          setInitialValueModalExample({
            title: currentArtworkExampleDetail.title || '',
            description: currentArtworkExampleDetail.description || '',
            artworkDetailId: currentArtworkExampleDetail.id,
            artworkConfigId,
            artworkImages: currentArtworkExampleDetail.artworkImages,
            artworkVideos: currentArtworkExampleDetail.artworkVideos,
          });
        }
      }
    }
  };
  useEffect(() => {
    if (layDataId) {
      getLayDataArtworkDetail();
    }
  }, [layDataId]);
  const handleAddArtwork = async (artworkConfigId: number) => {
    const rows = layDataArtworkDetail.artworkConfig.find(
      (item: any) => item.artworkStatus.id === 2
    ).artworkDetail;
    const sendData = {
      artworkConfigId,
      title: `Design ${numberWithCommas(rows.length + 1, 2)} / Artwork`,
      description: '',
      link: '',
    };
    const res = await apiArtwork.createArtworkDetail(sendData);
    if (!res.isError) {
      await getLayDataArtworkDetail();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? res.message : res.error.response.data.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
  };
  const handleReportEditArtwork = async (value: any) => {
    setLoadingReportEditArtwork(true);
    const res = await apiArtwork.convertStatus(value);
    if (!res.isError) {
      setOpenModalReportEditArtwork(false);
      await getLayDataArtworkDetail();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLoadingReportEditArtwork(false);
  };
  const handleClickConfirmExample = (row: any) => {
    setArtworkDetailId(row.id);
    setOpenConfirmExample(true);
  };
  const handleConfirmExample = async () => {
    setLoadingConfirmExample(true);
    const res = await apiArtwork.productExampleApproval(artworkDetailId);
    if (!res.isError) {
      setOpenConfirmExample(false);
      await getLayDataArtworkDetail();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLoadingConfirmExample(false);
  };
  // console.log('layDataArtworkDetail', layDataArtworkDetail);

  const handleConfirmFile = async () => {
    setLoadingNextStep(true);
    const res = await apiArtwork.approvals({
      layDataArtWorkId: layDataId,
      statusId: artworkStatusId,
    });
    if (!res.isError) {
      setOpenConfirmFile(false);
      await getLayDataArtworkDetail();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLoadingNextStep(false);
  };
  return (
    <>
      <ProductNav
        title={
          !isEmpty(layDataArtworkDetail) ? layDataArtworkDetail.ldCode : ''
        }
        showBorderBottom={true}
        backUrl={`/orders/${orderId}/spec?step=อาร์ตเวิร์ก`}
      />
      <ModalConfirmArtwork
        data={dataConfirmArtwork}
        open={openConfirmArtwork}
        handleClose={() => {
          setOpenConfirmArtwork(false);
        }}
        handleReFetchArtworkDetail={async () => {
          await getLayDataArtworkDetail();
        }}
      />
      <AppModalConfirm
        open={openConfirmFile}
        isReason={false}
        onClickClose={() => {
          setOpenConfirmFile(false);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยืนยันไฟล์อาร์ตเวิร์กส่งผลิต`}
        confirmDescription={`คุณได้ตรวจสอบข้อมูลเรียบร้อยแล้ว
จะทำการเข้าสู่การผลิตต่อไป`}
        loadingConfirm={loadingNextStep}
        onConfirm={async () => {
          await handleConfirmFile();
        }}
        maxWidth={'500px'}
      />
      <AppModalConfirm
        open={openConfirmExample}
        isReason={false}
        onClickClose={() => {
          setOpenConfirmExample(false);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยืนยันตัวอย่างสินค้า`}
        confirmDescription={`คุณได้ตรวจสอบข้อมูลเรียบร้อยแล้ว จะทำการเข้าสู่
การอัปเดทลิงก์ไฟล์งานอาร์ตเวิร์กส่งผลิตต่อไป`}
        loadingConfirm={loadingConfirmExample}
        onConfirm={async () => {
          await handleConfirmExample();
        }}
        maxWidth={'500px'}
      />
      <AppModalConfirm
        open={openModalReportEditArtwork}
        isReason={true}
        onClickClose={() => {
          setOpenModalReportEditArtwork(false);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`แจ้งแก้ไขอาร์ตเวิร์ก`}
        confirmDescription={`เมื่อยืนยันแจ้งแก้ไขอาร์ตเวิร์ก จะทำการกลับไปสถานะออกแบบอาร์ตเวิร์ก
เพื่อแก้ไขอาร์ตเวิร์กต่อไปและทำการยืนยันอาร์ตเวิร์กอีกครั้ง`}
        loadingConfirm={loadingReportEditArtwork}
        onConfirm={async (data: {
          annotationId: any;
          note: any;
          reason: string;
        }) => {
          if (!isEmpty(data.reason)) {
            const value = {
              artworkDetailId: information.id,
              description: data.reason,
            };
            await handleReportEditArtwork(value);
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: 'กรุณากรอกเหตุผลการแก้ไข',
                severity: 'error',
              })
            );
          }
        }}
        maxWidth={'500px'}
      />
      <ModalAddFlie
        open={openModalFile}
        initialValues={initialValueModalFile}
        handleClose={() => {
          setOpenModalFile(false);
        }}
        artworkConfigId={artworkConfigId}
        reFetchArtworkDetail={async () => {
          await getLayDataArtworkDetail();
        }}
      />
      <ModalAddDataArtwork
        open={openModalAddDataStep}
        initialValues={initialValueModalAddArtwork}
        handleClose={() => {
          setOpenModalAddDataStep(false);
        }}
        artworkConfigId={artworkConfigId}
        reFetchArtworkDetail={async () => {
          await getLayDataArtworkDetail();
        }}
      />
      <ModalDataArtworkDetail
        open={openDetail}
        handleClose={() => {
          setOpenDetail(false);
        }}
        data={information}
      />
      <ModalAddArtwork
        open={openModalAddArtWorkStep}
        handleClose={() => {
          setOpenModalAddArtWorkStep(false);
        }}
        reFetchArtworkDetail={async () => {
          await getLayDataArtworkDetail();
        }}
        initialValues={initialValueModalAddArtwork}
      />
      <ModalArtworkExample
        open={openModalExample}
        handleClose={() => {
          setOpenModalExample(false);
        }}
        reFetchArtworkDetail={async () => {
          await getLayDataArtworkDetail();
        }}
        initialValues={initialValueModalExample}
      />
      <AppModalConfirm
        open={openNextStep}
        onClickClose={() => {
          setOpenNextStep(false);
        }}
        confirmTitle={`ยืนยันส่งข้อมูล`}
        confirmDescription={`คุณได้ตรวจสอบข้อมูลเรียบร้อยแล้ว จะทำการเข้าสู่
การออกแบบอาร์ตเวิร์กต่อไป`}
        loadingConfirm={loadingNextStep}
        onConfirm={async () => {
          await handleNextStep();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
      />
      <AppModalConfirm
        open={openConfirmDelete}
        onClickClose={() => {
          setOpenConfirmDelete(false);
        }}
        confirmTitle={`${
          actionStep === 1
            ? 'ยืนยันการลบข้อมูล'
            : actionStep === 2
            ? 'ยืนยันลบแบบอาร์ตเวิร์ก'
            : actionStep === 4
            ? 'ยืนยันการลบไฟล์'
            : '-'
        }`}
        confirmDescription={`คุณต้องการที่จะลบ ${deleteData.title}?`}
        loadingConfirm={loadingDelete}
        onConfirm={async () => {
          await handleDelete();
        }}
      />
      <ArtworkPageStyle>
        <div className="topic">ออกแบบอาร์ตเวิร์ก</div>
        <div className="artwork-table-wrap">
          {layDataArtworkDetail.artworkConfig?.map((data: any) => {
            const step = data.artworkStatus.id;
            return (
              <TableArtworkWithTopic
                key={step}
                step={step}
                data={data}
                artworkConfig={layDataArtworkDetail.artworkConfig}
                handleClickAdd={async () => {
                  if (step === 1) {
                    handleOpenAddModal(step, data.id);
                  } else if (step === 2) {
                    await handleAddArtwork(data.id);
                    // setOpenModalAddArtWorkStep(true);
                  } else if (step === 4) {
                    setArtworkConfigId(data.id);
                    setInitialValueModalFile({
                      title: '',
                      description: '',
                      artworkDetailId: 0,
                      artworkConfigId: 0,
                      link: '',
                    });
                    setOpenModalFile(true);
                  } else {
                    setOpenModalAddDataStep(true);
                  }
                }}
                handleClickDetail={async (
                  step: number,
                  row: any,
                  index?: number
                ) => {
                  await handleClickDetail(step, row, index);
                }}
                handleOpenEdit={(value: any, step: number) => {
                  handleOpenEdit(value, step, data.id);
                }}
                handleConfirmDelete={(data: any, step: number) => {
                  handleMakeDelete(data, step);
                }}
                handleConfirmNextStep={() => {
                  setArtworkStatusId(step);
                  if (step === 1) {
                    setArtworkConfigId(data.id);
                  }
                  setOpenNextStep(true);
                }}
                handleOpenModalConfirmArtwork={(row: any, step: number) => {
                  handleMakeConfirmArtwork(row, step);
                }}
                reFetchArtworkDetail={async () => {
                  await getLayDataArtworkDetail();
                }}
                handleOpenModalReportEditArtwork={(row: any, _step: number) => {
                  setInformation(row);
                  setOpenModalReportEditArtwork(true);
                }}
                handleConfirmExample={(row: any) => {
                  handleClickConfirmExample(row);
                }}
                handleConfirmFile={async () => {
                  setArtworkStatusId(step);
                  setOpenConfirmFile(true);
                }}
              />
            );
          })}
        </div>
      </ArtworkPageStyle>
    </>
  );
};

ArtworkPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default ArtworkPage;
