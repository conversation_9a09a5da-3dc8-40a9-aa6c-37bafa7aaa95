import React, {
  ReactElement,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled, { css } from 'styled-components';
import Image from 'next/image';
import {
  Avatar,
  Button,
  CircularProgress,
  FormHelperText,
  TextField,
} from '@mui/material';
import { dateThaiFormat } from '@/utils/date';
import ActionButton from '@/components/ActionButton';
import IosShareIcon from '@mui/icons-material/IosShare';
import { useRouter } from 'next/router';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import InsertPhotoOutlinedIcon from '@mui/icons-material/InsertPhotoOutlined';
import EmojiEmotionsOutlinedIcon from '@mui/icons-material/EmojiEmotionsOutlined';
import ArtworkComment from '@/components/order/artwork/ArtworkComment';
import { isEmpty } from 'lodash';
import { LoadingFadein } from '@/styles/share.styled';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import apiArtwork from '@/services/order/artwork';
import Plyr from 'plyr-react';
import 'plyr/dist/plyr.css';
import { validateImageFiles } from '@/utils/size';
import { AnimatePresence, motion, wrap } from 'framer-motion';
import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
import ArrowBackRoundedIcon from '@mui/icons-material/ArrowBackRounded';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import RemoveRoundedIcon from '@mui/icons-material/RemoveRounded';

const ArtworkDetailStyle = styled.div<{ $isExtend: boolean }>`
  width: 100%;
  display: flex;
  height: 100dvh;
  overflow: hidden;
  animation: ${LoadingFadein} 0.3s ease-in;
  .image-content-wrapper {
    flex: 2 1 0%;
    display: flex;
    justify-content: center;
    background: #f5f7f8;
    padding: 64px;
    overflow: auto;
    .image-content {
      width: 100%;
      max-width: 900px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 24px;
      .image {
        width: 100%;
        height: 100%;
        position: relative;
        background: white;
        border: 1px solid #dbe2e5;
        aspect-ratio: 1/1;
        &.last {
          height: 40px;
          border: none;
          background: transparent;
        }
        img {
          object-fit: contain;
          cursor: pointer;
        }
      }
      .video {
        width: 100%;
        height: 100%;
        position: relative;
        background: white;
        border: 1px solid #dbe2e5;
        aspect-ratio: 1/1;
        display: flex;
        justify-content: center;
        align-items: center;
        .plyr__video-wrapper {
          background: transparent;
        }
        .plyr {
          width: 100%;
          height: 100%;
          max-width: 100%;
          max-height: 100%;
        }
        .plyr--full-ui input[type='range'] {
          color: #16d5c5;
          filter: saturate(1.5);
        }
        .plyr__controls {
          button {
            &:nth-child(1) {
              display: none;
            }
            &:nth-child(3) {
              display: none;
            }
          }
        }
        img {
          object-fit: contain;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .aside-artwork {
    flex: 1 1 0%;
    max-width: 640px;
    background: white;
    height: 100dvh;
    min-width: 400px;
    .aside-header {
      min-height: 64px;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e0e0e0;
      .profile-group {
        display: flex;
        align-items: center;
        column-gap: 12px;
        .profile-text-group {
          display: flex;
          flex-direction: column;
          .profile-name {
            font-weight: 600;
          }
          .date {
            font-size: 10px;
          }
        }
      }
      .action-group {
        display: flex;
        align-items: center;
        column-gap: 8px;
        .close-btn {
          height: 40px;
          width: 40px;
          border-radius: 8px;
          border: 1px solid #dbe2e5;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          padding: 12px 0;
          display: flex;
          cursor: pointer;
          position: relative;
          &:before {
            content: '';
            height: 2px;
            width: 20px;
            background: #263238;
            border-radius: 4px;
            transition: 0.3s ease-in-out;
            transform: rotate(45deg);
            position: absolute;
          }
          &:after {
            content: '';
            height: 2px;
            width: 20px;
            background: #263238;
            border-radius: 4px;
            transition: 0.3s ease-in-out;
            transform: rotate(-45deg);
            position: absolute;
          }
        }
      }
    }
    .aside-content {
      max-height: calc(100dvh - 64px);
      overflow: auto;
      width: 100%;
      .content-zone {
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        padding: 24px;
        .title-group {
          display: flex;
          align-items: center;
          gap: 20px;
          .title {
            font-size: 20px;
            font-weight: 600;
          }
          .chip {
            display: flex;
            align-items: center;
            height: 24px;
            border-radius: 20px;
            column-gap: 6px;
            background: #8bc34a;
            padding: 4px 12px 4px 4px;
            font-size: 12px;
            box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.16);
            border: 1px solid white;
            * {
              color: white;
            }
          }
        }
        .description {
          ${({ $isExtend }) =>
            !$isExtend &&
            css`
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow-wrap: break-word;
            `};
        }
        .less-more {
          font-weight: 600;
          margin-top: -12px;
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
        .condition {
          display: flex;
          align-items: center;
          column-gap: 4px;
          text-decoration: underline;
          cursor: pointer;
        }
        .comment-zone {
          width: 100%;
          display: flex;
          flex-direction: column;
          row-gap: 12px;
          .comment-topic {
            font-size: 16px;
            font-weight: 500;
          }
          .comment-wrap {
            display: flex;
            column-gap: 16px;
            width: 100%;
            margin-bottom: 8px;
            .text-area-wrap {
              display: flex;
              flex-direction: column;
              row-gap: 16px;
              width: 100%;
              .MuiInputBase-root {
                padding: 10px;
              }
              .MuiInputBase-inputMultiline {
                min-height: 40px;
                resize: vertical;
              }
              .comment-action-wrap {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .emoji-group {
                  display: flex;
                  align-items: center;
                  column-gap: 6px;
                  label {
                    display: flex;
                    align-items: center;
                  }
                  svg {
                    color: #d9d9d9;
                    font-size: 24px;
                    cursor: pointer;
                    transition: 0.15s ease-out;
                    &:hover {
                      color: #263238 !important;
                    }
                  }
                }
              }
              .image-preview-wrap {
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;
                animation: ${LoadingFadein} 0.3s ease-out;
                .image-preview {
                  border-radius: 8px;
                  overflow: hidden;
                  object-fit: cover;
                }
                .text-upload {
                  font-size: 14px;
                  color: rgb(144, 164, 174);
                  white-space: nowrap;
                }
              }
              .MuiFormHelperText-root {
                margin: 0 !important;
              }
            }
          }
        }
      }
    }
  }
`;

const ImageSliderWrapper = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
`;

const ImageBox = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  img {
    position: relative !important;
    width: fit-content !important;
    height: fit-content !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
  }
`;

const SliderActionButton = styled(motion.button)`
  position: absolute;
  top: 24px;
  right: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  border: 1px solid #cfd8dc;
  cursor: pointer;
  z-index: 21;
  svg {
    width: 20px;
    height: 20px;
  }
`;
const ArrowButton = styled(motion.button)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  border: 1px solid #cfd8dc;
  cursor: pointer;
  z-index: 21;
  svg {
    width: 20px;
    height: 20px;
  }
`;

const MotionSliderOverlay = styled(motion.div)`
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(12px);
`;

const slideVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? '25%' : '-25%',
    opacity: 0,
    transition: {
      duration: 0.2,
      ease: 'easeInOut',
    },
  }),
  center: {
    x: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 180,
      damping: 26,
      mass: 0.9,
    },
  },
  exit: (direction: number) => ({
    x: direction > 0 ? '-25%' : '25%',
    opacity: 0,
    transition: {
      duration: 0.2,
      ease: 'easeInOut',
    },
  }),
};

const validationSchema = yup.object({
  comment: yup.string().required('กรุณากรอก'),
});
const ArtworkDetail = () => {
  const router = useRouter();
  const { artworkId } = router.query;
  const { user } = useAppSelector(userSelector);
  const { orderId, layDataId } = router.query;
  const [isExtend, setIsExtend] = useState<boolean>(false);
  const [showExtendButton, setShowExtendButton] = useState<boolean>(false);
  const descriptionRef = useRef<HTMLDivElement>(null);
  const [errorImageUpload, setErrorImageUpload] = useState<string>('');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [blobUrls, setBlobUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [artworkCommentData, setArtworkCommentData] = useState<any>({});
  const refFileLabel = useRef<HTMLLabelElement | null>(null);
  const [isEditImage, setIsEditImage] = useState<boolean>(false);
  const [commentId, setCommentId] = useState<number>(0);

  const images = artworkCommentData.artworkImages?.map((i: any) => i.url) ?? [];
  const [[index, direction], setIndex] = useState<[number, number]>([0, 0]);
  const [isSliderOpen, setIsSliderOpen] = useState(false);
  const zoomStep = 0.5;
  const [zoom, setZoom] = useState(1); // scale เริ่มต้น
  const [position, setPosition] = useState({ x: 0, y: 0 }); // ตำแหน่งลาก
  const imageRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [constraints, setConstraints] = useState({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  });
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });

  const {
    register,
    handleSubmit,
    reset,
    watch,
    // setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      comment: '',
    },
  });

  /* ---------- ฟังก์ชัน slider ---------- */
  const paginate = (newDir: 1 | -1) => {
    setIndex(([prev]) => [wrap(0, images.length, prev + newDir), newDir]);
  };
  const openSlider = (idx: number) => {
    setIndex([idx, 0]);
    setIsSliderOpen(true);
  };
  const closeSlider = () => setIsSliderOpen(false);

  const handleZoomIn = () => setZoom((prev) => Math.min(prev + zoomStep, 5));
  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - zoomStep, 1));
    setPosition({ x: 0, y: 0 });
  };

  console.log('position', position);

  const resetZoom = () => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    dragStartRef.current = {
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    };
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || zoom === 1) return;
    const newX = e.clientX - dragStartRef.current.x;
    const newY = e.clientY - dragStartRef.current.y;

    setPosition({
      x: Math.min(Math.max(newX, constraints.left), constraints.right),
      y: Math.min(Math.max(newY, constraints.top), constraints.bottom),
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateDragConstraints = () => {
    if (!containerRef.current || !imageRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();

    // ใช้ขนาดจริง (หลัง transform) ของ <img>
    const imgEl = imageRef.current.querySelector('img');
    if (!imgEl) return;
    const imgRect = imgEl.getBoundingClientRect();

    const overflowX = Math.max(0, (imgRect.width - containerRect.width) / 2);
    const overflowY = Math.max(0, (imgRect.height - containerRect.height) / 2);

    setConstraints({
      left: -overflowX,
      right: overflowX,
      top: -overflowY,
      bottom: overflowY,
    });

    /* ถ้าซูมออกแล้วตำแหน่งเกินขอบ → รีเซ็ตเข้าในกรอบทันที */
    setPosition((prev) => ({
      x: Math.min(Math.max(prev.x, -overflowX), overflowX),
      y: Math.min(Math.max(prev.y, -overflowY), overflowY),
    }));
  };

  useEffect(() => {
    setTimeout(() => {
      updateDragConstraints();
    }, 200);
  }, [zoom, index]);

  useEffect(() => {
    resetZoom();
  }, [index, isSliderOpen]);

  useEffect(() => {
    if (descriptionRef.current) {
      const lineHeight = 24;
      const maxHeight = lineHeight * 2;
      const contentHeight = descriptionRef.current.scrollHeight;
      setShowExtendButton(contentHeight > maxHeight);
    }
  }, []);

  const handleImageUpload = async () => {
    setIsSubmitting(true);
    const validFiles = Array.from(selectedFiles);

    for (const file of validFiles) {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent: ProgressEvent) => {
          const percentComplete = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
          setUploadProgress(percentComplete);
        },
      };
      const formData = new FormData();
      formData.append('file', file);
      if (!isEditImage) {
        formData.append('artworkDetailId', artworkId as string);
      }
      // console.log('formData', formData.get('file'));
      setUploadProgress(0);
      if (!isEditImage) {
        const res = await apiArtwork.createCommentImage(formData, config);
        if (!res.isError) {
          await getArtworkDetail();
        }
      } else {
        const res = await apiArtwork.updateCommentImage(
          formData,
          config,
          commentId
        );
        setIsEditImage(false);
        if (!res.isError) {
          await getArtworkDetail();
        }
      }
      setIsSubmitting(false);
      setSelectedFiles([]);
    }
  };

  useEffect(() => {
    if (!isEmpty(selectedFiles)) {
      handleImageUpload().then();
    }
  }, [selectedFiles]);

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const sendValue = {
      ...values,
      artworkDetailId: artworkId,
    };
    const res = await apiArtwork.createComment(sendValue);
    if (!res.isError) {
      await getArtworkDetail();
      reset();
    }
    setIsSubmitting(false);
  };

  const getArtworkDetail = async () => {
    setBlobUrls([]);
    const res = await apiArtwork.getArtworkComment(Number(artworkId));
    if (!res.isError) {
      setArtworkCommentData(res.data);
    }
  };

  useEffect(() => {
    if (artworkId) {
      getArtworkDetail().then();
    }
  }, [artworkId]);

  if (!isEmpty(artworkCommentData)) {
    return (
      <ArtworkDetailStyle $isExtend={isExtend}>
        <div className="image-content-wrapper">
          <div className="image-content">
            {artworkCommentData.artworkImages?.map((img: any, idx: number) => (
              <div key={idx} className="image" onClick={() => openSlider(idx)}>
                <Image src={img.url} fill alt="Artwork" quality={100} />
              </div>
            ))}
            {artworkCommentData.artworkVideos?.map(
              (video: any, idx: number) => {
                return (
                  <div key={idx} className="video">
                    <Plyr
                      source={{
                        type: 'video',
                        sources: video.url,
                      }}
                    />
                  </div>
                );
              }
            )}
            <div className="image last" />
          </div>
        </div>
        <AnimatePresence mode="sync">
          {isSliderOpen && (
            <MotionSliderOverlay
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <ImageSliderWrapper ref={containerRef}>
                <SliderActionButton
                  style={{
                    right: 120,
                    background: zoom === 5 ? '#c4c4c4' : 'white',
                  }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleZoomIn}
                >
                  <AddRoundedIcon />
                </SliderActionButton>
                <SliderActionButton
                  style={{
                    right: 72,
                    background: zoom === 1 ? '#c4c4c4' : 'white',
                  }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleZoomOut}
                >
                  <RemoveRoundedIcon />
                </SliderActionButton>
                <SliderActionButton
                  whileTap={{
                    scale: 0.9,
                  }}
                  onClick={() => {
                    closeSlider();
                  }}
                >
                  <CloseRoundedIcon />
                </SliderActionButton>
                {/* ปุ่มย้อน */}
                <ArrowButton
                  style={{ left: 24 }}
                  whileTap={{
                    scale: 0.9,
                    y: '-50%',
                  }}
                  onClick={() => {
                    paginate(-1);
                  }}
                >
                  <ArrowBackRoundedIcon />
                </ArrowButton>

                {/* ---------- Current slide ---------- */}
                <AnimatePresence custom={direction} initial={false} mode="wait">
                  <ImageBox
                    key={index}
                    custom={direction}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                    style={{
                      cursor:
                        zoom > 1
                          ? isDragging
                            ? 'grabbing'
                            : 'grab'
                          : 'default',
                    }}
                    ref={imageRef}
                  >
                    <Image
                      src={images[index]}
                      fill
                      alt={`preview-${index}`}
                      draggable={false}
                      style={{
                        transform: `translate(${position.x}px, ${position.y}px) scale(${zoom})`,
                        transition: isDragging ? 'none' : 'transform 0.2s ease',
                        cursor:
                          zoom > 1
                            ? isDragging
                              ? 'grabbing'
                              : 'grab'
                            : 'default',
                      }}
                    />
                  </ImageBox>
                </AnimatePresence>

                {/* ปุ่มถัดไป */}
                <ArrowButton
                  style={{ right: 24 }}
                  whileTap={{
                    scale: 0.9,
                    y: '-50%',
                  }}
                  onClick={() => {
                    paginate(1);
                    resetZoom();
                  }}
                >
                  <ArrowForwardRoundedIcon />
                </ArrowButton>
              </ImageSliderWrapper>
            </MotionSliderOverlay>
          )}
        </AnimatePresence>

        <div className="aside-artwork">
          <div className="aside-header">
            <div className="profile-group">
              <div className="profile-image">
                <Avatar src={user.imageUrl || '#'}>{user.name[0]}</Avatar>
              </div>
              <div className="profile-text-group">
                <div className="profile-name">
                  {artworkCommentData.user.name}
                </div>
                <div className="date">
                  {dateThaiFormat(artworkCommentData.modifiedDate)}
                </div>
              </div>
            </div>
            <div className="action-group">
              <div
                onClick={() => {
                  //
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<IosShareIcon />}
                  text="แชร์"
                  borderRadius={'8px'}
                />
              </div>
              <div
                className="close-btn"
                onClick={async () => {
                  await router.push(`/orders/${orderId}/artwork/${layDataId}`);
                }}
              />
            </div>
          </div>
          <div className="aside-content">
            <div className="content-zone">
              <div className="title-group">
                <div className="title">{artworkCommentData.title}</div>
                {/* <div className="chip"> */}
                {/*  <TaskAltRoundedIcon sx={{ fontSize: '16px' }} /> */}
                {/*  <div>Final Design</div> */}
                {/* </div> */}
              </div>
              {artworkCommentData.description && (
                <div className="description" ref={descriptionRef}>
                  {artworkCommentData.description}
                </div>
              )}
              {showExtendButton && (
                <div
                  className="less-more"
                  onClick={() => {
                    setIsExtend(!isExtend);
                  }}
                >
                  {isExtend ? 'อ่านน้อยลง' : 'อ่านเพิ่มเติม'}
                </div>
              )}
              <div className="condition">
                <InfoOutlinedIcon sx={{ fontSize: '22px' }} />
                <div>เงื่อนไขการสั่งผลิต</div>
              </div>
              <div className="comment-zone">
                <div className="comment-topic">ความคิดเห็น</div>
                <div className="comment-wrap">
                  <Avatar src={user.imageUrl || '#'}>{user.name[0]}</Avatar>
                  <div className="text-area-wrap">
                    <TextField
                      type="text"
                      placeholder="พิมพ์ข้อความ..."
                      {...register('comment')}
                      multiline
                      rows={2}
                      error={Boolean(hookFormErrors.comment)}
                      helperText={hookFormErrors.comment?.message as ReactNode}
                    />
                    {!isEmpty(blobUrls) && isEmpty(errorImageUpload) && (
                      <div className="image-preview-wrap">
                        {blobUrls.map((item: any, index: number) => {
                          return (
                            <Image
                              src={item}
                              alt=""
                              width={40}
                              height={40}
                              key={index}
                              className="image-preview"
                              draggable={false}
                            />
                          );
                        })}
                        <div className="text-upload">
                          กำลังอัปโหลด...{uploadProgress}%
                        </div>
                      </div>
                    )}
                    <form
                      onSubmit={handleSubmit(onSubmit)}
                      className="comment-action-wrap"
                    >
                      <div className="emoji-group">
                        <label ref={refFileLabel}>
                          <input
                            type="file"
                            style={{ display: 'none' }}
                            onChange={async (event: any) => {
                              const { files } = event.target;
                              setUploadProgress(5);
                              const blob = Array.from(files);
                              const newBlobUrls = blob.map((file: any) =>
                                URL.createObjectURL(file)
                              );
                              setBlobUrls((prevBlobUrls: any) => [
                                ...prevBlobUrls,
                                ...newBlobUrls,
                              ]);
                              const validationResult = await validateImageFiles(
                                files,
                                25,
                                true,
                                true
                              );
                              if (validationResult.status) {
                                if (!isEmpty(errorImageUpload)) {
                                  setErrorImageUpload('');
                                }
                                const newFiles = Array.from(
                                  validationResult.files
                                );
                                setSelectedFiles((prevFiles: any) => [
                                  ...prevFiles,
                                  ...newFiles,
                                ]);
                              } else {
                                setErrorImageUpload(validationResult.message);
                                setUploadProgress(0);
                              }
                            }}
                          />
                          <InsertPhotoOutlinedIcon />
                        </label>
                        <EmojiEmotionsOutlinedIcon />
                      </div>
                      <Button
                        type="submit"
                        variant="contained"
                        color="dark"
                        disabled={isSubmitting || isEmpty(watch('comment'))}
                        onClick={async () => {
                          if (!isSubmitting) {
                            //
                          }
                        }}
                        sx={{
                          minHeight: '36px !important',
                          height: '36px !important',
                          minWidth: '140px !important',
                        }}
                      >
                        {isSubmitting ? (
                          <CircularProgress size={20} />
                        ) : (
                          <span className="mt-[2px]">แสดงความคิดเห็น</span>
                        )}
                      </Button>
                    </form>
                    {!isEmpty(errorImageUpload) && (
                      <FormHelperText error>{errorImageUpload}</FormHelperText>
                    )}
                  </div>
                </div>
                {!isEmpty(artworkCommentData) &&
                  artworkCommentData.artworkComment &&
                  artworkCommentData.artworkComment.map((commentItem: any) => {
                    return (
                      <ArtworkComment
                        key={commentItem.id}
                        data={commentItem}
                        reFetchComment={async () => {
                          await getArtworkDetail();
                        }}
                        handleClickEditImage={(commentId: number) => {
                          if (refFileLabel.current) {
                            setCommentId(commentId);
                            setIsEditImage(true);
                            refFileLabel.current.click();
                          }
                        }}
                      />
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
      </ArtworkDetailStyle>
    );
  }
  return null;
};

ArtworkDetail.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default ArtworkDetail;
