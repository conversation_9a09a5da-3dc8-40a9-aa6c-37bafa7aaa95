import ProductNav from '@/components/product/ProductNav';
import {
  Button,
  Checkbox,
  FormControlLabel,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import * as yup from 'yup';
import { useFormik } from 'formik';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import Image from 'next/image';
import apiLay from '@/services/order/lay';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/number';

const CalculateTableStyled = styled.div`
  width: 100%;
  padding: 24px;

  .MuiTable-root {
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    border-collapse: initial;
    border-spacing: initial;
    overflow: hidden;
    .MuiTableHead-root {
      .MuiTableRow-head {
        .MuiTableCell-head {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
    .MuiTableCell-root {
      color: #263238;
      border-right: 1px solid #dbe2e5;
      .MuiFormControlLabel-label {
        font-size: 14px;
      }
      .radio-custom {
        .MuiFormControlLabel-label {
          padding-top: 1px;
        }
      }
      &:last-child {
        border-right: 0;
      }
      .cell-value {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &.disabled {
          color: #b0bec5;
        }
        .value {
          font-weight: 600;
        }
        .unit {
          display: block;
        }
      }
      .cell-calculate {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        &.summarize {
          row-gap: 4px;
          .card {
            background: transparent;
            padding: 16px 16px 0;
            .chip {
              background: #263238;
              color: white;
            }
          }
        }
        .ratio-bar {
          width: 100%;
          height: 24px;
          border-radius: 12px;
          overflow: hidden;
          position: relative;
          display: flex;
          font-size: 12px;
          .material-bar {
            height: 100%;
            background: #605dec;
          }
          .wage-bar {
            height: 100%;
            background: #30d5c7;
          }
          .material-cost-percent {
            position: absolute;
            left: 12px;
            color: white;
            top: 50%;
            transform: translateY(-50%);
            margin-top: 1px;
          }
          .wage-cost-percent {
            position: absolute;
            right: 12px;
            color: white;
            top: 50%;
            transform: translateY(-50%);
            margin-top: 1px;
          }
        }
        .text-info {
          display: flex;
          align-items: baseline;
          font-size: 12px;
          column-gap: 8px;
          .dot {
            height: 8px;
            width: 8px;
            border-radius: 50%;
            &.material {
              background: #605dec;
            }
            &.wage {
              background: #30d5c7;
            }
          }
        }
        .card {
          padding: 16px;
          width: 100%;
          background: #f5f7f8;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          row-gap: 4px;
          border-radius: 8px;
          .title {
            display: block;
          }
          .value {
            font-size: 18px;
            font-weight: 600;
          }
          .chip {
            border-radius: 12px;
            height: 24px;
            display: flex;
            align-items: center;
            background: #dbe2e5;
            padding: 1px 12px 0;
          }
        }
      }
    }
    .MuiTableBody-root {
      .table-space {
        height: 4px;
        padding: 0;
        background: #f5f7f8;
        .MuiTableCell-root {
          padding: 0;
        }
      }
      .calculate {
        .MuiTableCell-root {
          vertical-align: top;
        }
      }
      .MuiTableRow-root {
        .discount {
          width: 100%;
          display: flex;
          align-items: start;
          justify-content: space-between;
        }
        &:last-child {
          .MuiTableCell-root {
            border-bottom: 0;
          }
        }
      }
    }
  }
  .action-group {
    width: 100%;
    display: flex;
    justify-content: end;
    margin-top: 34px;
    .content {
      width: 600px;
      max-width: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
`;

const validationSchema = yup.object({
  loss: yup.array().of(yup.number().required('กรุณากรอกกำไร')),
  profits: yup.array().of(yup.number().required('กรุณากรอกเผื่อเสีย')),
  discountPercent: yup
    .number()
    .min(1, `ส่วนลดต้องมากกว่า 0%`)
    .max(100, `ส่วนลดต้องไม่เกิน 100%`),
});
const Price = () => {
  const router = useRouter();
  const { id } = router.query;
  const [pricesList, setPricesList] = useState<any>({});
  const formik = useFormik({
    initialValues: {
      loss: [],
      profits: [],
      discount: false,
      discountPercent: 0,
      selectPrice: null,
      confirmPrice: false,
    },
    validationSchema,
    onSubmit: (_values) => {},
  });
  const handleChangeDiscount = (e: any) => {
    formik.setFieldValue('discount', e.target.checked);
  };
  const handleClickRadio = (e: any, index: number) => {
    if (index !== formik.values.selectPrice) {
      formik.setFieldValue('selectPrice', index);
    } else {
      formik.setFieldValue('selectPrice', null);
    }
  };
  const getComparePricesList = async () => {
    const res = await apiLay.getComparePricesList(id);
    if (!res.isError) {
      setPricesList(res);
    }
  };
  useEffect(() => {
    getComparePricesList();
  }, []);
  return (
    <>
      <ProductNav
        title={pricesList.ldCode ? pricesList.ldCode : 'LD'}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/orders/?status=PREPARE_PRICE_CALCULATE'}
        animate={false}
      >
        <div>
          <Button
            variant="outlined"
            size="small"
            color="blueGrey"
            sx={{
              minWidth: '40px',
            }}
          >
            <div className="action-dot">
              <div className="dot" />
            </div>
          </Button>
        </div>
      </ProductNav>
      {/* <div className=""> */}
      {/*  <ProductOrderStep /> */}
      {/* </div> */}
      {!isEmpty(pricesList) && (
        <CalculateTableStyled>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>จำนวนเทียบราคา</TableCell>
                {pricesList.content[0].value.map((item: any, index: number) => (
                  <TableCell key={index}>{item.name}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow className="table-space">
                {pricesList.content[0].value.map(
                  (_item: any, index: number) => {
                    return <TableCell key={index} />;
                  }
                )}
                <TableCell />
              </TableRow>
              {pricesList.content.map((item: any, index: number) => {
                return (
                  <TableRow key={index}>
                    <TableCell>{item.name}</TableCell>
                    {item.value.map((value: any, subIndex: number) => {
                      return (
                        <TableCell key={subIndex}>
                          <div className="cell-value">
                            <div className="value">
                              {numberWithCommas(value.value)}
                            </div>
                            <div className="unit">
                              {item.unit ? item.unit.name : ''}
                            </div>
                          </div>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
              <TableRow>
                <TableCell>รวมใบพิมพ์ + เผื่อเสีย</TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="loss[0]"
                    value={formik.values.loss[0]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>แผ่น</div>,
                    }}
                    error={
                      formik.touched.loss?.[0] &&
                      Boolean(formik.errors.loss?.[0])
                    }
                    helperText={
                      formik.touched.loss?.[0] && formik.errors.loss?.[0]
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="loss[1]"
                    value={formik.values.loss[1]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>แผ่น</div>,
                    }}
                    error={
                      formik.touched.loss?.[1] &&
                      Boolean(formik.errors.loss?.[1])
                    }
                    helperText={
                      formik.touched.loss?.[1] && formik.errors.loss?.[1]
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="loss[2]"
                    value={formik.values.loss[2]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>แผ่น</div>,
                    }}
                    error={
                      formik.touched.loss?.[2] &&
                      Boolean(formik.errors.loss?.[2])
                    }
                    helperText={
                      formik.touched.loss?.[2] && formik.errors.loss?.[2]
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>กำไร</TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="profits[0]"
                    value={formik.values.profits[0]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>%</div>,
                    }}
                    error={
                      formik.touched.profits?.[0] &&
                      Boolean(formik.errors.profits?.[0])
                    }
                    helperText={
                      formik.touched.profits?.[0] && formik.errors.profits?.[0]
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="profits[1]"
                    value={formik.values.profits[1]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>%</div>,
                    }}
                    error={
                      formik.touched.profits?.[1] &&
                      Boolean(formik.errors.profits?.[1])
                    }
                    helperText={
                      formik.touched.profits?.[1] && formik.errors.profits?.[1]
                    }
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    type="number"
                    placeholder="กรอก"
                    name="profits[2]"
                    value={formik.values.profits[2]}
                    onChange={formik.handleChange}
                    InputProps={{
                      endAdornment: <div>%</div>,
                    }}
                    error={
                      formik.touched.profits?.[2] &&
                      Boolean(formik.errors.profits?.[2])
                    }
                    helperText={
                      formik.touched.profits?.[2] && formik.errors.profits?.[2]
                    }
                  />
                </TableCell>
              </TableRow>
              <TableRow className="table-space">
                <TableCell />
                <TableCell />
                <TableCell />
                <TableCell />
              </TableRow>
              <TableRow className="calculate">
                <TableCell>คำนวณราคา</TableCell>
                <TableCell>
                  <div className="cell-calculate">
                    <div className="ratio-bar">
                      <div className="material-bar w-[70%]" />
                      <div className="wage-bar w-[30%]" />
                      <div className="material-cost-percent">70%</div>
                      <div className="wage-cost-percent">30%</div>
                    </div>
                    <div className="text-info">
                      <div className="dot material" />
                      <div className="text">ต้นทุนค่าวัตถุดิบ 10,628.76</div>
                    </div>
                    <div className="text-info">
                      <div className="dot wage" />
                      <div className="text">ต้นทุนค่าแรง 4,500.00</div>
                    </div>
                    <div className="card">
                      <div className="title">จำนวนผลิต (ชิ้น)</div>
                      <div className="value">1,100</div>
                      <div className="chip">1,000 + เผื่อเสีย 100</div>
                    </div>
                    <div className="card">
                      <div className="title">ต้นทุนรวม (บาท)</div>
                      <div className="value">33,649.00</div>
                      <div className="chip">30.59/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">กำไร 30%</div>
                      <div className="value">3,000.00 </div>
                      <div className="chip">3.00/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">ราคารวม (บาท)</div>
                      <div className="value">36,649.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="cell-calculate">
                    <div className="ratio-bar">
                      <div className="material-bar w-[70%]" />
                      <div className="wage-bar w-[30%]" />
                      <div className="material-cost-percent">70%</div>
                      <div className="wage-cost-percent">30%</div>
                    </div>
                    <div className="text-info">
                      <div className="dot material" />
                      <div className="text">ต้นทุนค่าวัตถุดิบ 10,628.76</div>
                    </div>
                    <div className="text-info">
                      <div className="dot wage" />
                      <div className="text">ต้นทุนค่าแรง 4,500.00</div>
                    </div>
                    <div className="card">
                      <div className="title">จำนวนผลิต (ชิ้น)</div>
                      <div className="value">2,150</div>
                      <div className="chip">2,000 + เผื่อเสีย 150</div>
                    </div>
                    <div className="card">
                      <div className="title">ต้นทุนรวม (บาท)</div>
                      <div className="value">33,649.00</div>
                      <div className="chip">30.59/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">กำไร 30%</div>
                      <div className="value">3,000.00</div>
                      <div className="chip">3.00/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">ราคารวม (บาท)</div>
                      <div className="value">36,649.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="cell-calculate">
                    <div className="ratio-bar">
                      <div className="material-bar w-[70%]" />
                      <div className="wage-bar w-[30%]" />
                      <div className="material-cost-percent">70%</div>
                      <div className="wage-cost-percent">30%</div>
                    </div>
                    <div className="text-info">
                      <div className="dot material" />
                      <div className="text">ต้นทุนค่าวัตถุดิบ 10,628.76</div>
                    </div>
                    <div className="text-info">
                      <div className="dot wage" />
                      <div className="text">ต้นทุนค่าแรง 4,500.00</div>
                    </div>
                    <div className="card">
                      <div className="title">จำนวนผลิต (ชิ้น)</div>
                      <div className="value">3,200</div>
                      <div className="chip">3,000 + เผื่อเสีย 200</div>
                    </div>
                    <div className="card">
                      <div className="title">ต้นทุนรวม (บาท)</div>
                      <div className="value">33,649.00</div>
                      <div className="chip">30.59/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">กำไร 30%</div>
                      <div className="value">3,000.00</div>
                      <div className="chip">3.00/ชิ้น</div>
                    </div>
                    <div className="card">
                      <div className="title">ราคารวม (บาท)</div>
                      <div className="value">36,649.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
              <TableRow className="table-space">
                <TableCell />
                <TableCell />
                <TableCell />
                <TableCell />
              </TableRow>
              <TableRow>
                <TableCell>
                  <div className="discount">
                    <FormControlLabel
                      control={
                        <Checkbox
                          color="primary"
                          checked={formik.values.discount}
                          onChange={(event: any) => {
                            handleChangeDiscount(event);
                          }}
                          icon={<IconUnCheckbox />}
                          checkedIcon={<IconCheckbox />}
                        />
                      }
                      label="ส่วนลด"
                    />
                    {formik.values.discount && (
                      <TextField
                        type="number"
                        placeholder="กรอก"
                        name="discountPercent"
                        value={formik.values.discountPercent}
                        onChange={formik.handleChange}
                        InputProps={{
                          endAdornment: <div>%</div>,
                        }}
                        error={Boolean(formik.errors.discountPercent)}
                        helperText={formik.errors.discountPercent}
                        sx={{
                          width: '154px',
                        }}
                      />
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div
                    className={`cell-value ${
                      !formik.values.discount ? 'disabled' : 'text-red-500'
                    }`}
                  >
                    <div className="value">- 0.00</div>
                    <div className="unit">บาท</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div
                    className={`cell-value ${
                      !formik.values.discount ? 'disabled' : 'text-red-500'
                    }`}
                  >
                    <div className="value">- 0.00</div>
                    <div className="unit">บาท</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div
                    className={`cell-value ${
                      !formik.values.discount ? 'disabled' : 'text-red-500'
                    }`}
                  >
                    <div className="value">- 0.00</div>
                    <div className="unit">บาท</div>
                  </div>
                </TableCell>
              </TableRow>
              <TableRow className="calculate">
                <TableCell>สรุปราคาขาย</TableCell>
                <TableCell>
                  <div className="cell-calculate summarize">
                    <div className="card">
                      <div className="title">ราคาขาย (บาท)</div>
                      <div className="value">36,249.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                    <div className="radio-custom">
                      <FormControlLabel
                        control={<Radio />}
                        label="ยืนยันราคาขาย"
                        onMouseUp={(e) => {
                          handleClickRadio(e, 1);
                        }}
                        checked={formik.values.selectPrice === 1}
                      />
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="cell-calculate summarize">
                    <div className="card">
                      <div className="title">ราคาขาย (บาท)</div>
                      <div className="value">36,249.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                    <div className="radio-custom">
                      <FormControlLabel
                        control={<Radio />}
                        label="ยืนยันราคาขาย"
                        onMouseUp={(e) => {
                          handleClickRadio(e, 2);
                        }}
                        checked={formik.values.selectPrice === 2}
                      />
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="cell-calculate summarize">
                    <div className="card">
                      <div className="title">ราคาขาย (บาท)</div>
                      <div className="value">36,249.00</div>
                      <div className="chip">33.59/ชิ้น</div>
                    </div>
                    <div className="radio-custom">
                      <FormControlLabel
                        control={<Radio />}
                        label="ยืนยันราคาขาย"
                        onMouseUp={(e) => {
                          handleClickRadio(e, 3);
                        }}
                        checked={formik.values.selectPrice === 3}
                      />
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
          <div className="action-group">
            <div className="content">
              <div className="header">
                <FormControlLabel
                  control={
                    <Checkbox
                      color="primary"
                      checked={formik.values.confirmPrice}
                      onChange={(event: any) => {
                        formik.setFieldValue(
                          'confirmPrice',
                          event.target.checked
                        );
                      }}
                      icon={<IconUnCheckbox />}
                      checkedIcon={<IconCheckbox />}
                    />
                  }
                  label="ยังไม่ยืนยันราคาขาย"
                />
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  style={{
                    height: '40px',
                    padding: '0 8px',
                  }}
                  onClick={(_e: any) => {
                    router.push(`/corporate/laydata/${id}/appraisal-sheet/`);
                  }}
                >
                  <div
                    className="flex items-center"
                    style={{
                      columnGap: '6px',
                    }}
                  >
                    <Image
                      src={'/icons/document.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                    <div>ใบประเมินราคา LD-X</div>
                  </div>
                </Button>
              </div>
              <Button
                type="button"
                variant="contained"
                color="primary"
                fullWidth
              >
                <div>บันทึกการคำนวณราคา</div>
              </Button>
            </div>
          </div>
        </CalculateTableStyled>
      )}
    </>
  );
};

Price.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Price;
