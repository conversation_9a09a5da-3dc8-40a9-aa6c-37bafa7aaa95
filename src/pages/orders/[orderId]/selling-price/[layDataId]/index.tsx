import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import apiOrder from '@/services/order/order';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import { FadeInStyled, LoadingFadein } from '@/styles/share.styled';
import { useForm, useWatch } from 'react-hook-form';
import {
  Button,
  CircularProgress,
  InputAdornment,
  TextField,
} from '@mui/material';
import ActionButton from '@/components/ActionButton';
import PercentIcon from '@mui/icons-material/Percent';
import apiLayDataQuantity from '@/services/order/layDataQuantity';
import { numberWithCommas } from '@/utils/number';
import apiCostCalculation from '@/services/order/costCalculation';
import SettingCostForm from '@/components/order/price/SettingCostForm';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  setCalculateCostValues,
  setCalculateMandatoryServiceChargeValues,
  setCalculateServiceChargeValues,
  setCostData,
  setCostServiceChargeData,
  setMandatoryCostServiceChargeData,
  settingCostSelector,
} from '@/store/features/selling-price/setting-cost';
import { setSnackBar } from '@/store/features/alert';

const SellingPricePageStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content-wrap {
    width: 1920px;
    max-width: 100%;
    display: flex;
    height: calc(100dvh - 64px);
    animation: ${LoadingFadein} 0.3s ease-in;
    position: relative;
    &:before {
      content: '';
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 2;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: 0;
      @media screen and (max-width: 1205px) {
        top: 50%;
        transform: translateY(calc(-50% + 11px));
      }
    }
    &:after {
      content: '';
      background: linear-gradient(
        to top,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 2;
      height: 24px;
      left: 0;
      width: 100%;
      top: 24px;
      transform: translateY(calc(-50% - 12px));
    }
    @media screen and (max-width: 820px) {
      margin-top: 64px;
      height: calc(100dvh - 128px);
    }
    @media screen and (max-width: 1205px) {
      flex-direction: column;
    }

    .left-side {
      flex: 1 1 0%;
      padding: 24px;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      overflow: auto;
      z-index: 1;

      .product-info {
        display: flex;
        align-items: center;
        gap: 20px;
        width: 100%;
        justify-content: space-between;
        @media screen and (max-width: 440px) {
          flex-direction: column;
          align-items: start;
        }
        .left-group {
          display: flex;
          align-items: center;
          gap: 20px;
          max-width: 100%;
          overflow: hidden;
          .image {
            width: 64px;
            min-width: 64px;
            height: 64px;
            overflow: hidden;
            border-radius: 8px;
          }

          .text-wrap {
            display: flex;
            flex-direction: column;
            row-gap: 4px;
            overflow: hidden;
            max-width: 100%;
            .name {
              font-size: 20px;
              font-weight: 600;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }

            .cost {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }
          }
        }
      }

      .cost-group {
        width: 100%;
        display: flex;
        flex-direction: column;

        .group-name {
          font-weight: 600;
          width: 100%;
          border-bottom: 2px solid #263238;
          height: 56px;
          display: flex;
          align-items: center;
        }

        .list-wrap {
          width: 100%;

          .list {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 24px;
            border-bottom: 1px solid #dbe2e5;
            height: 56px;

            .name {
              font-weight: 400;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .cost {
              font-weight: 600;
              &.wait {
                color: #f9a925;
              }
            }

            &:last-child {
              border-bottom: none;
            }
          }
        }

        .sum-amount {
          width: 100%;
          height: 56px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #f5f7f8;
          border-radius: 8px;
          padding: 0 24px;

          .sum {
            font-size: 20px;
            font-weight: 600;
          }
        }
      }
    }

    .right-side {
      flex: 1 1 0%;
      padding: 24px;
      background: #f5f7f8;
      border-left: 1px solid #dbe2e5;
      overflow: auto;
      z-index: 1;
      //background: #1b5c458f !important;
      @media screen and (max-width: 1205px) {
        background: white;
        border-top: 2px solid #dbe2e5;
        padding-top: 0;
      }
      @media screen and (max-width: 1205px) {
        padding-bottom: 0;
      }
      .text-topic {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .form-grid {
        display: grid;
        grid-gap: 24px;
        position: relative;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        margin-bottom: 32px;
        p {
          margin-top: 0;
        }
        .input-item {
          width: 100%;

          .profit-wrap {
            display: flex;
            column-gap: 8px;
          }
        }
      }
      .card-grid {
        display: grid;
        grid-gap: 24px;
        position: relative;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        .card {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          row-gap: 12px;
          border-radius: 16px;
          background: white;
          border: 1px solid #dbe2e5;
          padding: 24px;
          //background: #1b5c458f !important;

          .topic {
            font-weight: 400;
          }

          .price {
            font-size: 24px;
          }

          .chip {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 16px;
            background: #f5f7f8;
            padding: 0 10px;

            span {
              font-size: 12px;
              text-align: center;
            }
          }
        }
      }

      .summarize {
        width: 100%;
        margin-top: 40px;

        .list {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          column-gap: 24px;
          row-gap: 8px;
          height: 64px;
          border-top: 1px solid #dbe2e5;

          > div {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &:last-child {
            border-bottom: 1px solid #dbe2e5;
          }

          .sum {
            font-size: 24px;
            font-weight: 600;
            &.wait {
              color: #f9a925;
            }
          }
        }
      }
      .white-shadow-top {
        display: none;
        background: linear-gradient(
          to top,
          rgba(255, 255, 255, 0) 0%,
          rgb(255, 255, 255) 100%
        );
        position: sticky;
        z-index: 2;
        height: 24px;
        left: 0;
        width: 100%;
        top: 0;
        @media screen and (max-width: 1205px) {
          display: block;
        }
      }
      .white-shadow-bottom {
        display: none;
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0) 0%,
          rgb(255, 255, 255) 100%
        );
        position: sticky;
        z-index: 2;
        height: 24px;
        left: 0;
        width: 100%;
        bottom: 0;
        @media screen and (max-width: 1205px) {
          display: block;
        }
      }
    }
  }
`;
const CreateSellingPricePage = () => {
  // const costValueCache = getCookie('costValueCache');
  const { costData, costServiceChargeData } =
    useAppSelector(settingCostSelector);
  const router = useRouter();
  const { orderId, layDataId, layDataQuantityId } = router.query;
  const dispatch = useAppDispatch();
  const [orderById, setOrderById] = useState<any>({});
  const [layDataById, setLayDataById] = useState<any>({});
  const [layDataQuantity, setLayDataQuantity] = useState<any>({});
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isLoadingData, setIsLoadingData] = useState<boolean>(false);
  const [resetDirtyTrigger, setResetDirtyTrigger] = useState<boolean>(false);
  const {
    control,
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    reset,
  } = useForm<any>({
    defaultValues: {
      id: Number(layDataQuantityId),
      profit: '',
      profitRate: '',
      netPrice: '',
      discount: '',
      discountRate: '',
      vatAmount: '',
      vatRate: 7,
      totalSalePrice: '',
    },
  });
  const watchProfit = useWatch({
    control,
    name: 'profit',
    defaultValue: '',
  });
  const watchDiscountRate = useWatch({
    control,
    name: 'discountRate',
    defaultValue: '',
  });
  const watchDiscount = useWatch({
    control,
    name: 'discount',
    defaultValue: '',
  });
  const watchVatRate = useWatch({
    control,
    name: 'vatRate',
    defaultValue: '',
  });
  const watchVatAmount = useWatch({
    control,
    name: 'vatAmount',
    defaultValue: '',
  });
  const watchTotalSalePrice = useWatch({
    control,
    name: 'totalSalePrice',
    defaultValue: 0,
  });
  const getOrderById = async () => {
    const res = await apiOrder.getOrderById(orderId as string);
    if (!res.isError) {
      setOrderById(res.data);
    }
  };
  useEffect(() => {
    if (orderId) {
      getOrderById().then();
    }
  }, [orderId]);

  const getLayDataById = async () => {
    const findLayData = orderById.layData.find(
      (layDataItem: any) => layDataItem.id === Number(layDataId)
    );
    if (findLayData) {
      setLayDataById(findLayData);
    }
  };

  useEffect(() => {
    if (!isEmpty(orderById)) {
      getLayDataById().then();
    }
  }, [orderById]);

  const onSubmit = async (values: any) => {
    setSubmitting(true);
    const res = await apiLayDataQuantity.save(values);
    if (!res.isError) {
      await getLayDataQuantityByLayDataQuantityId();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };

  const getLayDataQuantityByLayDataQuantityId = async () => {
    const res = await apiLayDataQuantity.getLayDataQuantityByLayDataQuantityId(
      Number(layDataQuantityId)
    );
    if (!res.isError) {
      setLayDataQuantity(res.data);
      const {
        profit,
        netPrice,
        discount,
        vatAmount,
        vatRate,
        totalSalePrice,
        costPrice,
        serviceCharge,
      } = res.data;
      const basePrice = res.data.costPrice;
      const profitRateValue = parseFloat(
        ((profit / basePrice) * 100).toFixed(2)
      );
      const discountRateValue = parseFloat(
        ((discount / (costPrice + serviceCharge + profit)) * 100).toFixed(2)
      );
      const calculatedTotalSalePrice = parseFloat(
        (
          basePrice +
          ((costPrice + profit - discount) / 100) * (vatRate || 7)
        ).toFixed(2)
      );
      const values = {
        id: Number(layDataQuantityId),
        profit: profit || '',
        profitRate: profitRateValue || '',
        netPrice,
        discount: discount || '',
        discountRate: discountRateValue || '',
        vatAmount:
          vatAmount || ((costPrice + profit - discount) / 100) * (vatRate || 7),
        vatRate: vatRate || 7,
        totalSalePrice: totalSalePrice || calculatedTotalSalePrice,
      };
      reset(values);
    }
  };
  useEffect(() => {
    if (layDataQuantityId) {
      getLayDataQuantityByLayDataQuantityId().then();
    }
  }, [layDataQuantityId, costData, costServiceChargeData]);

  const getCostByLayDataId = async () => {
    if (layDataQuantityId) {
      const res = await apiCostCalculation.getCostByLayDataId(
        Number(layDataQuantityId)
      );
      if (!res.isError) {
        dispatch(setCostData(res.data));
        return true;
      }
      return false;
    }
  };

  const handleMakeCalculateCostValues = async (res: any) => {
    const values = {
      layDataQuantityId: Number(layDataQuantityId),
      printPlateCostId: 1,
      costConfig: res.map((cost: any) => ({
        costType: cost.costType,
        title: cost.title,
        optionCost: cost.optionCost.map((option: any) => ({
          name: option.name,
          rawMaterialId: option.rawMaterialId,
          componentId: option.componentId,
          optionsId: option.optionsId,
          optionsFormulaId:
            option.optionsFormula.find((formula: any) => formula.select)?.id ||
            'none',
          optionsPresetId:
            option.optionsPreset.find((preset: any) => preset.select)?.id ||
            'none',
          priceCostConfigDetail: option.priceCostConfigDetail || null,
        })),
      })),
    };
    if (values) {
      dispatch(setCalculateCostValues(values));
    }
  };
  const handleMakeCalculateServiceChargeValues = async (res: any) => {
    const values = {
      layDataQuantityId: Number(layDataQuantityId),
      printPlateCostId: 2,
      costConfig: res.map((cost: any) => ({
        costType: cost.costType,
        title: cost.title,
        optionCost: cost.optionCost.map((option: any) => ({
          id: option.id,
          name: option.name,
          rawMaterialId: option.rawMaterialId,
          componentId: option.componentId,
          optionsId: option.optionsId,
          serviceLayId: option.serviceLayId,
          optionsFormulaId:
            option.optionsFormula.find((formula: any) => formula.select)?.id ||
            'none',
          optionsPresetId:
            option.optionsPreset.find((preset: any) => preset.select)?.id ||
            'none',
          priceCostConfigDetail: option.priceCostConfigDetail || null,
        })),
      })),
    };
    if (values) {
      dispatch(setCalculateServiceChargeValues(values));
    }
  };

  const handleMakeCalculateMandatoryServiceChargeValues = async (res: any) => {
    const costConfig = res.map((costItem: any) => {
      const selectedOptionCost = costItem.optionCost.find(
        (optionCostItem: any) => optionCostItem.select
      );
      return {
        ...costItem,
        optionCost: selectedOptionCost || [],
      };
    });
    console.log('costConfig', costConfig);
    const values = {
      layDataQuantityId: Number(layDataQuantityId),
      printPlateCostId: 3,
      costConfig: costConfig.map((cost: any) => ({
        costType: cost.costType,
        title: cost.title,
        optionCost: [
          {
            id: cost.optionCost.id,
            name: cost.optionCost.name,
            rawMaterialId: cost.optionCost.rawMaterialId,
            componentId: cost.optionCost.componentId,
            optionsId: cost.optionCost.optionsId,
            serviceLayId: cost.optionCost.serviceLayId,
            optionsFormulaId:
              cost.optionCost.optionsFormula?.find(
                (formula: any) => formula.select
              )?.id || 'none',
            optionsPresetId:
              cost.optionCost.optionsPreset?.find(
                (preset: any) => preset.select
              )?.id || 'none',
            priceCostConfigDetail: cost.priceCostConfig || null,
          },
        ],
      })),
    };
    if (values) {
      dispatch(setCalculateMandatoryServiceChargeValues(values));
    }
  };

  useEffect(() => {
    handleMakeCalculateCostValues(costData).then();
  }, [costData]);

  useEffect(() => {
    if (!isEmpty(costServiceChargeData)) {
      handleMakeCalculateServiceChargeValues(costServiceChargeData).then();
    }
  }, [costServiceChargeData]);

  const getCostServiceChargeByLayDataQuantityId = async () => {
    if (layDataQuantityId) {
      const res =
        await apiCostCalculation.getCostServiceChargeByLayDataQuantityId({
          layDataQuantityId: Number(layDataQuantityId),
          type: 1,
        });
      if (!res.isError) {
        dispatch(setCostServiceChargeData(res.data));
        await handleMakeCalculateServiceChargeValues(res.data);
        return true;
      }
      return false;
    }
  };

  const getMandatoryCostServiceChargeByLayDataQuantityId = async () => {
    if (layDataQuantityId) {
      const res =
        await apiCostCalculation.getMandatoryCostServiceChargeByLayDataQuantityId(
          {
            layDataQuantityId: Number(layDataQuantityId),
          }
        );
      if (!res.isError) {
        dispatch(setMandatoryCostServiceChargeData(res.data));
        await handleMakeCalculateMandatoryServiceChargeValues(res.data);
        return true;
      }
      return false;
    }
  };

  const handleClickSettingCost = async () => {
    setIsLoadingData(true);
    const responseCostByLayDataId = await getCostByLayDataId();
    const responseCostServiceChargeByLayDataId =
      await getCostServiceChargeByLayDataQuantityId();
    const responseMandatoryCostServiceChargeByLayDataId =
      await getMandatoryCostServiceChargeByLayDataQuantityId();
    if (
      responseCostByLayDataId &&
      responseCostServiceChargeByLayDataId &&
      responseMandatoryCostServiceChargeByLayDataId
    ) {
      // todo check if costValueCache
      // if (costValueCache !== undefined) {
      //   // decode Base64 UTF-8
      //   const decodedStringValue = decodeURIComponent(
      //     Array.from(atob(costValueCache as string))
      //       .map((c) => `%${c.charCodeAt(0).toString(16).padStart(2, '0')}`)
      //       .join('')
      //   );
      //   const costValue = JSON.parse(decodedStringValue);
      //   const { calculateCostValues, calculateServiceChargeValues } = costValue;
      //   if (
      //     Number(layDataQuantityId) === calculateCostValues.layDataQuantityId
      //   ) {
      //     dispatch(setCalculateCostValues(calculateCostValues));
      //     dispatch(
      //       setCalculateServiceChargeValues(calculateServiceChargeValues)
      //     );
      //   }
      //   deleteCookie('costValueCache');
      // }
      setIsLoadingData(false);
      setOpen(true);
    }
  };

  const handleClearState = async () => {
    setOpen(false);
    await getCostServiceChargeByLayDataQuantityId();
    await getCostByLayDataId();
  };

  const handleChange = (name: string, value: number) => {
    const basePrice = layDataQuantity.costPrice;
    const { serviceCharge } = layDataQuantity;

    const calcVatAndTotal = (profit: number, discount: number) => {
      const vatBase = basePrice + serviceCharge + profit - discount;
      const vatAmount = parseFloat(
        ((vatBase * (watchVatRate || 7)) / 100).toFixed(2)
      );
      setValue('vatAmount', vatAmount);

      const totalSalePrice = parseFloat((vatBase + vatAmount).toFixed(2));
      setValue('totalSalePrice', totalSalePrice);
    };

    switch (name) {
      case 'profitRate': {
        const profitValue = parseFloat(((basePrice * value) / 100).toFixed(2));
        const netPrice = parseFloat((basePrice + profitValue).toFixed(2));
        setValue('profitRate', value);
        setValue('profit', profitValue);
        setValue('netPrice', netPrice);

        const discountValue = parseFloat(
          (
            ((basePrice + serviceCharge + profitValue) *
              (watchDiscountRate || 0)) /
            100
          ).toFixed(2)
        );
        setValue('discount', discountValue);

        calcVatAndTotal(profitValue, discountValue);
        break;
      }

      case 'profit': {
        const profitRateValue = parseFloat(
          ((value / basePrice) * 100).toFixed(2)
        );
        const netPrice = parseFloat((basePrice + value).toFixed(2));
        setValue('profit', value);
        setValue('profitRate', profitRateValue);
        setValue('netPrice', netPrice);

        const discountValue = parseFloat(
          (
            ((basePrice + serviceCharge + value) * (watchDiscountRate || 0)) /
            100
          ).toFixed(2)
        );
        setValue('discount', discountValue);

        calcVatAndTotal(value, discountValue);
        break;
      }

      case 'discountRate': {
        const discountValue = parseFloat(
          (((basePrice + serviceCharge + watchProfit) * value) / 100).toFixed(2)
        );
        setValue('discountRate', value);
        setValue('discount', discountValue);

        calcVatAndTotal(watchProfit, discountValue);
        break;
      }

      case 'discount': {
        const discountRateValue = parseFloat(
          ((value / (basePrice + serviceCharge + watchProfit)) * 100).toFixed(2)
        );
        setValue('discount', value);
        setValue('discountRate', discountRateValue);

        calcVatAndTotal(watchProfit, value);
        break;
      }

      default:
        console.log('Not found of case');
        break;
    }
  };

  // console.log('layDataQuantity', layDataQuantity);
  return (
    <SellingPricePageStyle>
      {open && !isEmpty(costData) ? (
        <>
          <ProductNav
            title={'ตั้งค่าต้นทุน'}
            showBorderBottom
            backUrl={`/orders/${orderId}/spec?step=price`}
            backUrlEvent={async () => {
              await handleClearState();
            }}
          />
          <SettingCostForm
            layDataById={layDataById}
            layDataQuantity={layDataQuantity}
            handleReFetchCost={async () => {
              await getCostByLayDataId();
              await getCostServiceChargeByLayDataQuantityId();
              await getMandatoryCostServiceChargeByLayDataQuantityId();
              setResetDirtyTrigger(!resetDirtyTrigger);
            }}
            resetDirtyTrigger={resetDirtyTrigger}
            handleClearState={handleClearState}
          />
        </>
      ) : (
        <ProductNav
          title={'คำนวณราคา'}
          showBorderBottom
          backUrl={`/orders/${orderId}/spec?step=เสนอราคา`}
        />
      )}
      {!isEmpty(layDataById) && !isEmpty(layDataQuantity) && !open && (
        <FadeInStyled
          style={{
            width: '100%',
          }}
        >
          <form onSubmit={handleSubmit(onSubmit)} className="content-wrap">
            <div className="left-side">
              <div className="product-info">
                <div className="left-group">
                  <Image
                    src={
                      layDataById.productModel.imageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    alt=""
                    width={160}
                    height={160}
                    className="image"
                  />
                  <div className="text-wrap">
                    <div className="name">{layDataById.ldCode}</div>
                    <div className="cost">
                      {`${layDataById.productModel.productName} • ${layDataById.productModel.productModelName}`}
                    </div>
                  </div>
                </div>
                <div
                  onClick={async () => {
                    await handleClickSettingCost();
                  }}
                >
                  <ActionButton
                    variant="outlined"
                    color="blueGrey"
                    icon={
                      isLoadingData ? (
                        <CircularProgress size={20} />
                      ) : (
                        <PercentIcon />
                      )
                    }
                    text="ตั้งค่าต้นทุน"
                    fullWidth
                  />
                </div>
              </div>
              {layDataQuantity.printPlateCost.map(
                (printPlateCostItem: any, printPlateCostItemIdx: number) => {
                  return (
                    <div className="cost-group" key={printPlateCostItemIdx}>
                      <div className="group-name">
                        {printPlateCostItem.name}
                      </div>
                      <div className="list-wrap">
                        {printPlateCostItem.cost?.costConfig.map(
                          (costConfigItem: any, costConfigIdx: number) => {
                            return (
                              <div className="list" key={costConfigIdx}>
                                <div className="name">
                                  {costConfigItem.title}
                                </div>
                                <div
                                  className={`cost ${
                                    costConfigItem.priceCostConfig === 0
                                      ? 'wait'
                                      : ''
                                  }`}
                                >
                                  {costConfigItem.priceCostConfig === 0
                                    ? 'รอคำนวณราคา'
                                    : numberWithCommas(
                                        costConfigItem.priceCostConfig,
                                        2
                                      )}
                                </div>
                              </div>
                            );
                          }
                        )}
                      </div>
                      {printPlateCostItem.cost && (
                        <div className="sum-amount">
                          รวม
                          <div className="sum">
                            {numberWithCommas(
                              printPlateCostItem.cost.costConfig.reduce(
                                (sum: number, config: any) =>
                                  sum + (config.priceCostConfig || 0),
                                0
                              ),
                              2
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }
              )}
            </div>
            <div className="right-side">
              <div className="white-shadow-top" />
              <div className="text-topic">สรุปราคาเสนอขาย</div>
              <div className="form-grid">
                <div className="input-item">
                  <p>กำไร</p>
                  <div className="profit-wrap">
                    <TextField
                      type="number"
                      fullWidth
                      placeholder="กรอก"
                      {...register(`profitRate`)}
                      error={Boolean(hookFormErrors.profitRate)}
                      helperText={
                        hookFormErrors.profitRate
                          ? (hookFormErrors.profitRate.message as ReactNode)
                          : ''
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (value < 0 || Number.isNaN(value)) {
                          e.target.value = '';
                          handleChange('profitRate', 0);
                        } else {
                          handleChange('profitRate', value);
                        }
                      }}
                      disabled={
                        !layDataQuantity.isConfigCost ||
                        layDataQuantity.printPlateCost.some(
                          (printPlateCostItem: any) => {
                            if (printPlateCostItem.cost === null) {
                              return true;
                            }
                            printPlateCostItem.cost?.costConfig.some(
                              (costConfigItem: any) =>
                                costConfigItem.priceCostConfig === 0
                            );
                          }
                        )
                      }
                      inputProps={{
                        step: '0.01',
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <div>%</div>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        minWidth: '100px',
                        width: '100px',
                      }}
                    />
                    <TextField
                      type="number"
                      fullWidth
                      placeholder="กรอก"
                      {...register(`profit`)}
                      error={Boolean(hookFormErrors.profit)}
                      helperText={
                        hookFormErrors.profit
                          ? (hookFormErrors.profit.message as ReactNode)
                          : ''
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (value < 0 || Number.isNaN(value)) {
                          e.target.value = '';
                          handleChange('profit', 0);
                        } else {
                          handleChange('profit', value);
                        }
                      }}
                      disabled={
                        !layDataQuantity.isConfigCost ||
                        layDataQuantity.printPlateCost.some(
                          (printPlateCostItem: any) => {
                            if (printPlateCostItem.cost === null) {
                              return true;
                            }
                            printPlateCostItem.cost?.costConfig.some(
                              (costConfigItem: any) =>
                                costConfigItem.priceCostConfig === 0
                            );
                          }
                        )
                      }
                      inputProps={{
                        step: '0.01',
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <div>บาท</div>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </div>
                <div className="input-item">
                  <p>ส่วนลด</p>
                  <div className="profit-wrap">
                    <TextField
                      type="number"
                      fullWidth
                      placeholder="กรอก"
                      {...register(`discountRate`)}
                      error={Boolean(hookFormErrors.discountRate)}
                      helperText={
                        hookFormErrors.discountRate
                          ? (hookFormErrors.discountRate.message as ReactNode)
                          : ''
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (value < 0 || Number.isNaN(value)) {
                          e.target.value = '';
                          handleChange('discountRate', 0);
                        } else {
                          handleChange('discountRate', value);
                        }
                      }}
                      onKeyDown={(e: any) => {
                        if (e.key === '-') {
                          e.preventDefault();
                        }
                      }}
                      disabled={
                        !layDataQuantity.isConfigCost ||
                        layDataQuantity.printPlateCost.some(
                          (printPlateCostItem: any) => {
                            if (printPlateCostItem.cost === null) {
                              return true;
                            }
                            printPlateCostItem.cost?.costConfig.some(
                              (costConfigItem: any) =>
                                costConfigItem.priceCostConfig === 0
                            );
                          }
                        )
                      }
                      inputProps={{
                        step: '0.01',
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <div>%</div>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        minWidth: '100px',
                        width: '100px',
                      }}
                    />
                    <TextField
                      type="number"
                      fullWidth
                      placeholder="กรอก"
                      {...register(`discount`)}
                      error={Boolean(hookFormErrors.discount)}
                      helperText={
                        hookFormErrors.discount
                          ? (hookFormErrors.discount.message as ReactNode)
                          : ''
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (value < 0 || Number.isNaN(value)) {
                          e.target.value = '';
                          handleChange('discount', 0);
                        } else {
                          handleChange('discount', value);
                        }
                      }}
                      onKeyDown={(e: any) => {
                        if (e.key === '-') {
                          e.preventDefault();
                        }
                      }}
                      disabled={
                        !layDataQuantity.isConfigCost ||
                        layDataQuantity.printPlateCost.some(
                          (printPlateCostItem: any) => {
                            if (printPlateCostItem.cost === null) {
                              return true;
                            }
                            printPlateCostItem.cost?.costConfig.some(
                              (costConfigItem: any) =>
                                costConfigItem.priceCostConfig === 0
                            );
                          }
                        )
                      }
                      inputProps={{
                        step: '0.01',
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <div>บาท</div>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="card-grid">
                <div className="card">
                  <div className="topic">ราคาต้นทุน</div>
                  <div className="price">
                    {numberWithCommas(layDataQuantity.costPrice)}
                  </div>
                  <div className="chip">
                    <span>บาท</span>
                  </div>
                </div>
                <div className="card">
                  <div className="topic">จำนวนผลิต</div>
                  <div className="price">
                    {numberWithCommas(layDataQuantity.quantity)}
                  </div>
                  <div className="chip">
                    <span>ชิ้น</span>
                  </div>
                </div>
                <div className="card">
                  <div className="topic">จำนวนเผื่อเสีย</div>
                  <div className="price">
                    {numberWithCommas(layDataQuantity.quantityAllowance)}
                  </div>
                  <div className="chip">
                    <span>ชิ้น</span>
                  </div>
                </div>
                <div className="card">
                  <div className="topic">กำไร</div>
                  <div className="price">{numberWithCommas(watchProfit)}</div>
                  <div className="chip">
                    <span>บาท</span>
                  </div>
                </div>
                <div className="card">
                  <div className="topic">ราคาต้นทุน + กำไร</div>
                  <div className="price">
                    {numberWithCommas(layDataQuantity.costPrice + watchProfit)}
                  </div>
                  <div className="chip">
                    <span>บาท</span>
                  </div>
                </div>
                <div className="card">
                  <div className="topic">ค่าบริการ</div>
                  <div className="price">
                    {numberWithCommas(layDataQuantity.serviceCharge)}
                  </div>
                  <div className="chip">
                    <span>บาท</span>
                  </div>
                </div>
              </div>
              <div className="summarize">
                <div className="list">
                  <div>ราคารวม</div>
                  <div>
                    {numberWithCommas(
                      layDataQuantity.costPrice +
                        layDataQuantity.serviceCharge +
                        watchProfit
                    )}{' '}
                    บาท
                  </div>
                </div>
                <div className="list">
                  <div>ส่วนลด ({numberWithCommas(watchDiscountRate)}%)</div>
                  <div>
                    {watchDiscount >= 1 && '-'}
                    {numberWithCommas(watchDiscount, 2)} บาท
                  </div>
                </div>
                <div className="list">
                  <div>VAT {watchVatRate}%</div>
                  <div>{numberWithCommas(watchVatAmount)} บาท</div>
                </div>
                <div className="list">
                  <div>ยอดราคาเสนอขาย</div>
                  <div
                    className={`sum ${watchTotalSalePrice === 0 ? 'wait' : ''}`}
                  >
                    {watchTotalSalePrice === 0
                      ? 'รอคำนวณราคา'
                      : `${numberWithCommas(watchTotalSalePrice)} บาท`}
                  </div>
                </div>
              </div>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                sx={{
                  marginTop: '40px',
                }}
                disabled={
                  !layDataQuantity.isConfigCost ||
                  layDataQuantity.printPlateCost.some(
                    (printPlateCostItem: any) => {
                      if (printPlateCostItem.cost === null) {
                        return true;
                      }
                      return printPlateCostItem.cost.costConfig.some(
                        (costConfigItem: any) =>
                          costConfigItem.priceCostConfig === 0
                      );
                    }
                  )
                }
              >
                {submitting ? (
                  <CircularProgress
                    size={20}
                    sx={{
                      color: 'white',
                    }}
                  />
                ) : (
                  'บันทึก'
                )}
              </Button>
              <div className="white-shadow-bottom" />
            </div>
          </form>
        </FadeInStyled>
      )}
    </SellingPricePageStyle>
  );
};

CreateSellingPricePage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default CreateSellingPricePage;
