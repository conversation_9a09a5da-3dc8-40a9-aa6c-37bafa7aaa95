import React, { ReactElement, useRef } from 'react';
import { useRouter } from 'next/router';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import AppraisalPapers from '@/components/corporate/product-order/lay/AppraisalPapers';

const AppraisalSheet = () => {
  const router = useRouter();
  const sheetRef = useRef<HTMLDivElement>(null);

  const { id } = router.query;
  return (
    <>
      <ProductNav
        title={`ใบประเมินราคา`}
        backUrl={`/corporate/laydata/${id}/price/`}
        showBorderBottom
      >
        {/* <ReactToPrint */}
        {/*  trigger={() => ( */}
        {/*    <div className="print-btn"> */}
        {/*      <Image */}
        {/*        src="/icons/icon-print.svg" */}
        {/*        width={24} */}
        {/*        height={24} */}
        {/*        alt="" */}
        {/*      /> */}
        {/*    </div> */}
        {/*  )} */}
        {/*  content={() => sheetRef.current} */}
        {/* /> */}
      </ProductNav>
      <div ref={sheetRef} className="w-full">
        <AppraisalPapers />
      </div>
    </>
  );
};
AppraisalSheet.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default AppraisalSheet;
