import React, { ReactElement, useEffect } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import apiOrder from '@/services/order/order';
import { isEmpty } from 'lodash';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';

import { useAppDispatch, useAppSelector } from '@/store';
import { orderSelector, setOrderById } from '@/store/features/order';
import SpecOrder from '@/components/order/spec/SpecOrder';

const Spec = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { step, orderId } = router.query;
  const { orderById } = useAppSelector(orderSelector);
  const getOrderById = async () => {
    const res = await apiOrder.getOrderById(orderId as string);
    if (!res.isError) {
      dispatch(setOrderById(res.data));
    }
  };

  useEffect(() => {
    if (orderId) {
      getOrderById().then();
    }
  }, [orderId]);

  // console.log('orderById', orderById);
  // console.log('currentLayData', currentLayData);

  return (
    <>
      <ProductNav
        title={!isEmpty(orderById) ? orderById.layDataOrderNo : ''}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={step === 'layout' ? '/orders?status=laying' : '/orders'}
        animate={false}
      />
      {!isEmpty(orderById) && (
        <SpecOrder
          reloadOrder={async () => {
            await getOrderById();
          }}
        />
      )}
    </>
  );
};

Spec.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { orderId, step } = context.query;
  const token = await getCookie('access_token', {
    req: context.req,
    res: context.res,
  });
  const res = await apiOrder.getOrderById(orderId as string, token as string);
  const resOrderStatus = await apiOrder.getOrderStatus(token as string);
  const currentStep = res.data.layDataOrderStatus;
  const paramsStep = resOrderStatus.data.find(
    (status: any) => status.name === step
  );
  const validStep = paramsStep.id <= currentStep.id;
  if (currentStep.id === 99) {
    return {
      redirect: {
        permanent: false,
        destination: '/orders',
      },
    };
  }

  if (!validStep) {
    return {
      redirect: {
        permanent: false,
        destination: `/orders/${orderId}/spec?step=${decodeURIComponent(
          currentStep.name
        )}`,
      },
    };
  }

  return {
    props: {},
  };
};

export default Spec;
