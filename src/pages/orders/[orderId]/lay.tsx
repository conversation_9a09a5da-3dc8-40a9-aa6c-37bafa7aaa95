import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { Button } from '@mui/material';
import LayDetail from '@/components/corporate/product-order/lay/LayDetail';
import { useRouter } from 'next/router';
import apiLay from '@/services/order/lay';
import { GetServerSideProps } from 'next';
import { wrapper } from '@/store';
import { getCookie } from 'cookies-next';
import { getUserProfile } from '@/store/features/user';

const LayPage = (props: any) => {
  const { data } = props;
  const router = useRouter();
  const { id } = router.query;
  const [dataForLay, setDataForLay] = useState<any>({});
  const getDataForLay = async () => {
    const res = await apiLay.getDataForLay(id);
    if (!res.isError) {
      setDataForLay(res);
    }
  };

  useEffect(() => {
    getDataForLay();
  }, [id]);

  useEffect(() => {
    const screenWidth = window.innerWidth;
    if (screenWidth <= 820) {
      document.body.style.overflow = 'hidden';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  return (
    <>
      <ProductNav
        title={data.ldCode || ''}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/orders/?status=PREPARE_LAYING'}
        animate={false}
      >
        <div>
          <Button
            variant="outlined"
            size="small"
            color="blueGrey"
            sx={{
              minWidth: '40px',
            }}
          >
            <div className="action-dot">
              <div className="dot" />
            </div>
          </Button>
        </div>
      </ProductNav>
      <LayDetail dataForLay={dataForLay} activeStep={2} />
    </>
  );
};

LayPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps =
  wrapper.getServerSideProps((store) => async ({ req, res, query }) => {
    const token = await getCookie('access_token', { req, res });
    await store.dispatch(getUserProfile());
    const data = await apiLay.getData(query.id, token);
    if (!data.isError) {
      if (data.status !== 'PREPARE_LAYING') {
        return {
          redirect: {
            permanent: false,
            destination: `/corporate/laydata/`,
          },
        };
      }
    }
    return {
      props: { data: data },
    };
  });
export default LayPage;
