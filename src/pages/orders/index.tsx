import React, { ReactElement, useEffect, useRef, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Badge, Tab, Tabs } from '@mui/material';
import AppPagination from '@/components/global/AppPagination';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import StatusButton from '@/components/layout-data/StatusButton';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ProductNav from '@/components/product/ProductNav';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
// import ModalCreateProduct from '@/components/layout-data/ModalCreateOrder';
import apiOrder from '@/services/order/order';
import SearchInput from '@/components/SearchInput';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { numberWithCommas } from '@/utils/number';
import styled from 'styled-components';
import dayjs from 'dayjs';
import AppDateRange from '@/components/global/AppDateRange';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import ModalCreateOrder from '@/components/order/modal/ModalCreateOrder';

type LayoutDataListPageProps = {
  status: string;
};
const ChipStyled = styled.div`
  height: 24px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  border-radius: 4px;
`;
type OrderStatusType = {
  id: number;
  name: string;
  sort: number;
  count: number;
};
const LayoutDataListPage = ({ status }: LayoutDataListPageProps) => {
  const router = useRouter();
  const scrollBarRef = useRef<HTMLDivElement>(null);
  const [rows, setRows] = useState<any>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    startDate: null,
    endDate: null,
    layDataOrderStatusId: 1,
  });
  const [orderStatus, setOrderStatus] = useState<OrderStatusType[]>([]);

  const getOrderStatus = async () => {
    const res = await apiOrder.getOrderStatus();
    if (!res.isError) {
      setOrderStatus(res.data);
    }
  };

  useEffect(() => {
    getOrderStatus().then();
  }, []);

  useEffect(() => {
    getList(status).then();
  }, [status, filters]);

  const getList = async (status: string) => {
    setLoading(true);
    let numberOrderStatus;
    switch (status) {
      case 'ชำระเงิน':
        numberOrderStatus = 1;
        break;
      case 'อาร์ตเวิร์ก':
        numberOrderStatus = 2;
        break;
      case 'การผลิต':
        numberOrderStatus = 3;
        break;
      case 'จัดส่ง':
        numberOrderStatus = 4;
        break;
      case 'ยกเลิก':
        numberOrderStatus = 5;
        break;
      default:
        numberOrderStatus = 1;
    }
    const res = await apiOrder.getList({
      ...filters,
      layDataOrderStatusId: numberOrderStatus,
    });
    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
    setLoading(false);
  };

  const changeStatus = async (status: string) => {
    await router.push(`/orders?status=${status}`, undefined);
  };

  useEffect(() => {
    setRows([]);
  }, [status]);

  // const createLD = async (contactId: number) => {
  //   const res = await apiLayData.create(contactId);
  //   if (res && !res.isError) {
  //     await getList('DRAFT');
  //   }
  // };

  const handleChangeStatus = async (
    event: React.SyntheticEvent,
    newStatus: any
  ) => {
    await changeStatus(newStatus);
  };

  const columns: GridColDef[] = [
    {
      field: 'layDataOrderNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 172,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'contact',
      headerName: 'ลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 210,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '10px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.contact.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.contact.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'totalPriceOrder',
      headerName: 'มูลค่ารวม (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.totalPriceOrder);
      },
    },
    {
      field: 'countLayData',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.countLayData);
      },
    },
    {
      field: 'b',
      headerName: 'การชำระเงิน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <ChipStyled
            style={{
              background: '#8BC34A',
            }}
          >
            n/a
          </ChipStyled>
        );
      },
    },
    {
      field: 'c',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <ChipStyled
            style={{
              background: 'black',
            }}
          >
            n/a
          </ChipStyled>
        );
      },
    },
    {
      field: 'createdBy',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '10px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.createdBy.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.createdBy.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'สร้างเมื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>{dayjs(params.row.createdDate).format('DD/MM/YYYY HH:mm')}</div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 144,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <StatusButton
                rowId={params.row.id}
                status={status}
                loading={loading}
              />
            </div>
          </>
        );
      },
    },
  ];

  return (
    <>
      <ProductNav title="ออเดอร์สินค้า" showBorderBottom>
        <ActionGroupStyle>
          <AppDateRange
            data={{
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            handleChange={(dateRange: any) => {
              setFilters({
                ...filters,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              });
            }}
          />
          {/* <ModalCreateProduct */}
          {/*  handleReloadList={async () => { */}
          {/*    await getList(`${router.query.status}`); */}
          {/*    await getOrderStatus(); */}
          {/*  }} */}
          {/* > */}
          {/*  <ActionButton */}
          {/*    variant="contained" */}
          {/*    color="Hon" */}
          {/*    icon={<AddCircle />} */}
          {/*    text="สร้างออเดอร์" */}
          {/*    borderRadius={'8px'} */}
          {/*  /> */}
          {/* </ModalCreateProduct> */}
          <ModalCreateOrder
            handleReloadList={async () => {
              await getList(status);
            }}
          >
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="สร้างคำสั่งซื้อ"
              borderRadius="8px"
            />
          </ModalCreateOrder>
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <ScrollBarStyled ref={scrollBarRef}>
          <FilterWrapStyled>
            <div
              style={{
                minWidth: '718px',
              }}
            >
              <Tabs
                value={status}
                onChange={handleChangeStatus}
                variant="standard"
              >
                {orderStatus.map((item: OrderStatusType, index: number) => (
                  <Tab
                    key={index}
                    label={
                      <div
                        className="flex items-center"
                        style={{
                          columnGap: '16px',
                          minHeight: '32px',
                        }}
                      >
                        {item.name}
                        <Badge
                          badgeContent={numberWithCommas(item.count) || '0'}
                          color="secondary"
                          sx={{
                            '.MuiBadge-badge': {
                              backgroundColor:
                                status !== item.name ? '#78909C' : '',
                            },
                          }}
                        />
                      </div>
                    }
                    value={item.name}
                    sx={{
                      color: status !== item.name ? '#78909C' : '',
                    }}
                  />
                ))}
              </Tabs>
            </div>
            <SearchInput
              makeSearchValue={(newValue) =>
                setFilters({
                  ...filters,
                  searchName: newValue,
                  page: 0,
                })
              }
            />
          </FilterWrapStyled>
        </ScrollBarStyled>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={144} />
                <DataGrid
                  hideFooter={true}
                  rows={rows}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  loading={loading}
                  components={{
                    NoRowsOverlay: TableNoRowsOverlay,
                    LoadingOverlay: TableLoadingOverlay,
                  }}
                />
              </ScrollBarStyled>
            </div>
            <div className="px-[16px]">
              <AppPagination
                filters={filters}
                totalElements={totalElements || 0}
                handleChangeFilters={(newValues: any) => {
                  setFilters(newValues);
                }}
              />
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
    </>
  );
};

LayoutDataListPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query } = context;
  const { status } = query;
  return {
    props: status ? { status } : { status: 'การชำระเงิน' },
  };
};
export default LayoutDataListPage;
