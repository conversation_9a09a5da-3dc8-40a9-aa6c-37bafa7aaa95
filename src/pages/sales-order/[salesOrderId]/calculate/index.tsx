import React, { ReactElement, useEffect, useState } from 'react';
import styled from 'styled-components';
import MainLayout from '@/layouts/MainLayout';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import CalculateHeader from '@/components/sales-order/calculate/CalculateHeader';
import DataTableCalculate from '@/components/sales-order/calculate/DataTableCalculate';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
  setSalesOrderById,
} from '@/store/features/estimate';
import apiEstimate from '@/services/order/estimate';
import { isEmpty } from 'lodash';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { Button, CircularProgress, TextField } from '@mui/material';
import { buildEstimateStationBody } from '@/utils/estimate';
import { setSnackBar } from '@/store/features/alert';
import { LoadingFadein } from '@/styles/share.styled';
import { numberWithCommas } from '@/utils/number';

const CalculatePageStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const EstimateCalculateWrapper = styled.div`
  width: 100%;
  position: relative;
  height: calc(100dvh - 64px);
  overflow: auto;
`;

const SaveSectionStyled = styled.div`
  width: 100%;
  display: flex;
  animation: ${LoadingFadein} 0.3s ease-in;
  .left-side {
    flex: 1 1 0%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 24px 0;
    row-gap: 16px;

    .remark-section {
      width: calc(100% - 48px);
    }
    .button-section {
      width: calc(100% - 48px);
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
  .right-side {
    flex: 2 1 0%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .title {
      //
      padding: 0 24px;
    }
    .amount {
      font-size: 32px;
      font-weight: 600;
      line-height: 1.1;
      padding: 0 24px;
    }
    .vat {
      color: #b0bec5;
      padding: 0 24px;
    }
  }
`;
const CalculatePage = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { salesOrderById, estimateQuantityStation } =
    useAppSelector(salesOrderSelector);
  const { salesOrderId } = router.query;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Calculate pagination
  const totalQuantityGroups =
    estimateQuantityStation?.quantityGroups?.length || 0;
  const totalPages = Math.ceil(totalQuantityGroups / itemsPerPage);

  // Get current page quantities
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentQuantityGroups =
    estimateQuantityStation?.quantityGroups?.slice(startIndex, endIndex) || [];

  // Get estimateQuantityIds for current page from all products
  const currentEstimateQuantityIds = currentQuantityGroups.flatMap(
    (group: any) =>
      estimateQuantityStation?.estimateProducts
        ?.map(
          (product: any) =>
            product.estimateQuantity?.find(
              (eq: any) =>
                eq.estimateQuantityGroupId === group.estimateQuantityGroupId
            )?.estimateQuantityId
        )
        .filter(Boolean) || []
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Check if the estimate is in readonly mode
  const isReadonly =
    estimateQuantityStation?.estimateStatus?.name !== 'เสนอราคา';

  const getSalesOrderById = async () => {
    const res = await apiEstimate.getSalesOrderById(salesOrderId as string);
    if (!res.isError) {
      dispatch(setSalesOrderById(res.data));
    }
  };

  const getStationByEstimateId = async () => {
    const res = await apiEstimateQuantityStation.getStationByEstimateId(
      Number(salesOrderId)
    );
    if (!res.isError) {
      dispatch(setEstimateQuantityStation(res.data));
    }
  };

  const handleSaveCalculate = async () => {
    // Prevent saving if readonly
    if (isReadonly) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ไม่สามารถแก้ไขข้อมูลได้ เนื่องจากสถานะไม่ใช่ "เสนอราคา"',
          severity: 'warning',
        })
      );
      return;
    }

    // Check if any product has isSetZero true and remarkEstimate is empty
    const hasSetZeroProduct = estimateQuantityStation?.estimateProducts?.some(
      (product: any) => product.isSetZero === true
    );

    if (hasSetZeroProduct && !estimateQuantityStation?.remarkEstimate?.trim()) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'กรุณากรอกหมายเหตุเนื่องจากเลือกเลย์รวม',
          severity: 'warning',
        })
      );
      return;
    }

    setIsSubmitting(true);

    // Check profit validation for all products
    let hasLowProfitOverall = false;

    if (estimateQuantityStation?.estimateProducts) {
      for (const product of estimateQuantityStation.estimateProducts) {
        // ตรวจสอบ validation เฉพาะ product ที่ไม่มี isSetZero = true
        if (!product.isSetZero) {
          const profitStation = product.station?.find(
            (s: any) => s.optionCostType.name === 'กำไร'
          );
          const costStation = product.station?.find(
            (s: any) => s.optionCostType.name === 'ต้นทุน'
          );

          const hasLowProfit = profitStation?.subStation.some((sub: any) =>
            sub.subCost.some((c: any) => {
              const totalCost =
                costStation?.cost.find(
                  (x: any) => x.estimateQuantityId === c.estimateQuantityId
                )?.priceStation ?? 0;

              if (!totalCost || c.priceCostResult == null) return false;

              const profitPercent = Number(
                ((c.priceCostResult / totalCost) * 100).toFixed(2)
              );

              return profitPercent < 7;
            })
          );

          if (hasLowProfit) {
            hasLowProfitOverall = true;
            break;
          }
        }
      }
    }

    if (hasLowProfitOverall) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'กำไรต้องมากกว่า 7% กรุณาตรวจสอบก่อนบันทึก',
          severity: 'warning',
        })
      );
      setIsSubmitting(false);
      return;
    }

    if (!estimateQuantityStation?.estimateProducts) {
      console.error('No products found');
      setIsSubmitting(false);
      return;
    }

    try {
      // Loop through all products and update station for each
      for (const product of estimateQuantityStation.estimateProducts) {
        const body = buildEstimateStationBody(product);
        const res = await apiEstimateQuantityStation.updateStation(body);

        if (res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
              severity: 'error',
            })
          );
          setIsSubmitting(false);
          return;
        }
      }

      // Update remarkEstimate
      await apiEstimate.updateRemarkEstimate({
        estimateId: estimateQuantityStation.estimateId,
        remarkEstimate: estimateQuantityStation.remarkEstimate || '',
      });

      dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกข้อมูลสำเร็จ',
          severity: 'success',
        })
      );

      await getStationByEstimateId();
    } catch (error) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
          severity: 'error',
        })
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  useEffect(() => {
    if (isEmpty(salesOrderById)) {
      getSalesOrderById().then();
    }
  }, [salesOrderById]);

  useEffect(() => {
    if (salesOrderId) {
      dispatch(setEstimateQuantityStation({}));
      setCurrentPage(1); // Reset to first page when salesOrderId changes
      getStationByEstimateId().then();
    }
    return () => {
      dispatch(setEstimateQuantityStation({}));
    };
  }, [salesOrderId]);

  return (
    <>
      <ProductNav
        title={salesOrderById.estimateNo || ''}
        showBorderBottom
        backUrl={`/sales-order/${salesOrderId}/spec?step=เสนอราคา`}
      />
      {!isEmpty(estimateQuantityStation) && (
        <EstimateCalculateWrapper>
          {estimateQuantityStation.estimateProducts?.map(
            (product: any, productIndex: number) => (
              <div key={productIndex}>
                <CalculateHeader
                  pagination={{
                    currentPage: currentPage,
                    totalPage: totalPages,
                  }}
                  currentPage={currentPage}
                  onChangePage={handlePageChange}
                  product={product}
                  isReadonly={isReadonly}
                />
                <CalculatePageStyled>
                  <DataTableCalculate
                    getStationByEstimateId={getStationByEstimateId}
                    product={product}
                    currentEstimateQuantityIds={currentEstimateQuantityIds}
                    isReadonly={isReadonly}
                  />
                </CalculatePageStyled>
                <HrSpaceStyle />
              </div>
            )
          )}
          <SaveSectionStyled>
            <div className="left-side">
              <div className="remark-section">
                <TextField
                  multiline
                  rows={4}
                  type="text"
                  placeholder="หมายเหตุ"
                  value={estimateQuantityStation.remarkEstimate || ''}
                  fullWidth
                  variant="outlined"
                  error={false}
                  disabled={isReadonly}
                  onChange={(event: any) => {
                    if (!isReadonly) {
                      const newData = {
                        ...estimateQuantityStation,
                        remarkEstimate: event.target.value,
                      };
                      dispatch(setEstimateQuantityStation(newData));
                    }
                  }}
                />
              </div>
              <div className="button-section">
                <Button
                  type="button"
                  variant="outlined"
                  color="inherit"
                  fullWidth
                  onClick={() => router.back()}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleSaveCalculate}
                  disabled={isSubmitting || isReadonly}
                >
                  {isSubmitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: '#fff',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </div>
            <div
              className="right-side"
              style={{
                background:
                  estimateQuantityStation?.priceTotal !== 0
                    ? '#F5F7F8'
                    : 'transparent',
              }}
            >
              {estimateQuantityStation?.priceTotal !== 0 && (
                <>
                  <span className="title">ราคารวม</span>
                  <span className="amount">
                    {numberWithCommas(estimateQuantityStation.priceTotal, 2)}
                  </span>
                  <span className="vat">
                    VAT{' '}
                    {numberWithCommas(
                      estimateQuantityStation.vatAmountTotal,
                      2
                    )}
                  </span>
                </>
              )}
            </div>
          </SaveSectionStyled>
        </EstimateCalculateWrapper>
      )}
    </>
  );
};

CalculatePage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default CalculatePage;
