import React, {
  ReactElement,
  ReactNode,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import { isEmpty, isNull } from 'lodash';
import styled from 'styled-components';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { FadeInStyled, LoadingFadein } from '@/styles/share.styled';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import Image from 'next/image';
import UploadIcon from '@mui/icons-material/Upload';
import ZoomInRoundedIcon from '@mui/icons-material/ZoomInRounded';
import { useAppDispatch, useAppSelector } from '@/store';
import useMediumZoom from '@/hooks/useMediumZoom';
import { setSnackBar } from '@/store/features/alert';
import { numberWithCommas } from '@/utils/number';
import apiRawMaterial from '@/services/stock/raw-material';
import apiSubItemSize from '@/services/stock/subItemSize';
import { validateImageFiles } from '@/utils/size';
import apiEstimate from '@/services/order/estimate';
import {
  salesOrderSelector,
  setSalesOrderById,
} from '@/store/features/estimate';
import SpecSalesOrder from '@/components/sales-order/SpecSalesOrder';
import apiEstimatePrintPlate from '@/services/order/estimate-print-plate';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';
import { ModalEditExtraLay } from '@/components/sales-order/modal/ModalEditExtraLay';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import { AnimatePresence, motion } from 'framer-motion';
import { motionFadeConfig } from '@/utils/motion/motion-config';

const CreatePrintSheetFormWrapperStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  animation: ${LoadingFadein} 0.15s ease-in;
  height: calc(100dvh - 64px);
  overflow: auto;
  position: absolute;
  background: white;
  //background: #9ea2a1;
  top: 64px;
  z-index: 2;

  form {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
  }

  .sheet-header {
    width: 100%;
    min-height: 128px;

    .content {
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      flex-wrap: wrap;
      gap: 24px;

      .left-group {
        display: flex;
        align-items: center;
        column-gap: 16px;

        .image {
          width: 80px;
          min-width: 80px;
          height: 80px;
          border-radius: 8px;
          overflow: hidden;
        }

        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 6px;

          .title {
            font-size: 24px;
            font-weight: 600;
          }

          .sub-title {
            font-size: 14px;
          }
        }
      }

      .right-group {
        display: flex;
        column-gap: 24px;

        p {
          margin-top: 4px;
        }
      }
    }

    .border-bar {
      border-top: 1px solid #dbe2e5;
      height: 8px;
      background: #f5f7f8;
    }
  }

  .data-print-sheet-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 0 24px;

    .form-group-wrap {
      display: flex;
      flex-direction: column;
      flex: 1 1 0%;
      row-gap: 16px;
      width: 50%;
      overflow: hidden;
      .title {
        flex: 1 1 0%;
        font-size: 20px;
        font-weight: 600;
      }

      .form-group {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        padding: 24px;
        min-height: 204px;
        overflow: hidden;

        .row {
          flex: 1 1 0%;
          display: flex;
          column-gap: 24px;
          width: 100%;

          p:not(.MuiFormHelperText-root) {
            margin: 0 0 8px 0;
          }

          .material-group {
            display: flex;
            align-items: center;
            column-gap: 16px;
            max-width: 100%;

            .image {
              width: 68px;
              min-width: 68px;
              height: 68px;
              border-radius: 50%;
              overflow: hidden;
            }

            .text-group {
              display: flex;
              flex-direction: column;
              row-gap: 8px;
              overflow: hidden;

              .name {
                font-weight: 600;
                font-size: 18px;
                line-height: 1;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .unit {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .image-upload-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    padding: 0 24px;

    .title {
      font-size: 20px;
      font-weight: 600;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
    }

    .image-zone {
      width: calc(100vw - 396px);
      display: flex;
      gap: 24px;
      position: relative;
      @media screen and (max-width: 650px) {
        flex-direction: column;
      }
      .block-event {
        position: absolute;
        width: calc(100% + 24px);
        height: 100%;
        background: rgba(255, 255, 255, 0.54);
        z-index: 2;
        right: -24px;
        border-radius: 16px;
        backdrop-filter: blur(8px);
      }
      .image-wrap {
        display: flex;
        flex-direction: column;
        width: 50%;
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        padding: 16px 0;
        aspect-ratio: 75 / 62;
        justify-content: space-between;
        @media screen and (max-width: 650px) {
          width: 100%;
        }
        .machine-selector-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 24px;
          margin-bottom: 16px;
          border-bottom: 1px solid #dbe2e5;
          padding: 0 16px 16px 16px;
          .input-group {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 24px;
            position: relative;
            .MuiFormControl-root {
              max-width: 180px;
            }
          }

          .color {
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 100%;
            overflow: hidden;
            width: 100%;
            .color-image {
              width: 24px;
              min-width: 24px;
              height: 24px;
              border-radius: 50%;
              overflow: hidden;
              img {
                width: 100%;
                height: 100%;
              }
            }
            span {
              font-weight: 500;
              white-space: nowrap;
              max-width: calc(100% - 40px);
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .text {
              font-weight: 500;
              white-space: nowrap;
              max-width: calc(100% - 40px);
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .side {
          height: 34px;
          margin-bottom: 16px;
          border-bottom: 1px solid #dbe2e5;
          padding: 0 16px;
        }

        .image {
          width: calc(100% - 32px);
          height: 100%;
          background: #f5f7f8;
          border: 1px dashed #dbe2e5;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          flex-direction: column;
          row-gap: 6px;
          min-height: 172px;
          position: relative;
          overflow: hidden;
          border-radius: 8px;
          &:hover {
            .action-event {
              visibility: visible;
              opacity: 1;
            }
          }

          img {
            object-fit: contain;
            max-width: 100%;
          }

          .action-event {
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            row-gap: 10px;
            background: rgba(38, 50, 56, 0.4);
            visibility: hidden;
            transition: 0.15s ease-out;
            opacity: 0;

            .action-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 172px;
              height: 40px;
              border-radius: 8px;
              position: relative;
              cursor: pointer;
              transition: 0.3s ease-out;

              &:hover {
                filter: brightness(0.9);
              }

              .icon-action {
                position: absolute;
                left: 8px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                justify-content: center;
              }

              &.preview {
                background: white;
              }

              &.remove {
                background: #d32f2f;
                color: white;
              }
            }
          }

          .icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #b0bec5;
          }

          .condition {
            font-size: 10px;
            color: #90a4ae;
          }
        }
      }
    }
  }
`;
const initialValue = {
  estimatePrintPlateId: '',
  estimateQuantity: {
    estimateQuantityId: '',
  },
  quantityPerSheet: '',
  rawMaterialId: '',
  itemSizeId: '',
  isUnSpecExtra: false,
  subItemSizeId: '',
  plateItemSizeId: '',
  plateSubItemSizeId: '',
  plateRawMaterialId: '',
  dieCutRawMaterialId: '',
  dieCutItemSizeId: '',
  dieCutSubItemSizeId: '',
  colorFront: [],
  colorBack: [],
};

const initialLayoutFile = { layoutFrontFile: null, layoutBackFile: null };
const initialLayoutBlob = { layoutFrontBlob: '', layoutBackBlob: '' };

const initialExtraFile = { extraFrontFile: null, extraBackFile: null };
const initialExtraBlob = { extraFrontBlob: '', extraBackBlob: '' };

type ColorItem = {
  estimateProductColorId: string;
  colorCode: string;
  // machineId: string;
};

type ExtraItem = {
  blogSubMaterialDetailId: number;
  extraAreaDimensionId: number | null;
  extras: {
    estimateProductExtraId: number;
    quantity: number;
    width: number;
    height: number;
  }[];
  extraGroups: {
    estimateProductExtraGroupId: number;
    widthMaterial: number;
    heightMaterial: number;
  } | null;
};

type FormValues = {
  estimatePrintPlateId: string;
  estimateQuantity: {
    estimateQuantityId: string;
  };
  isUnSpecExtra: boolean;
  quantityPerSheet: string;
  rawMaterialId: string;
  itemSizeId: string;
  subItemSizeId: string;
  plateItemSizeId: string;
  plateSubItemSizeId: string;
  plateRawMaterialId: string;
  dieCutRawMaterialId: string;
  dieCutItemSizeId: string;
  dieCutSubItemSizeId: string;
  colorFront: ColorItem[];
  colorBack: ColorItem[];
  extraFront: ExtraItem[];
  extraBack: ExtraItem[];
};

const extraItemSchema = yup.object({
  estimateProductExtraId: yup.number().required('กรุณาระบุ ID เทคนิค'),
  quantity: yup.number().required('กรุณาระบุจำนวน'),
  width: yup.number().nullable(),
  height: yup.number().nullable(),
});

export const Spec = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { salesOrderId } = router.query;
  const { salesOrderById } = useAppSelector(salesOrderSelector);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [layoutFile, setLayoutFile] = useState<any>(initialLayoutFile);
  const [layoutBlob, setLayoutBlob] = useState(initialLayoutBlob);
  const [extraFile, setExtraFile] = useState<any>(initialExtraFile);
  const [extraBlob, setExtraBlob] = useState(initialExtraBlob);
  const [errorImageUpload, setErrorImageUpload] = useState<any>({});
  const [openLayData, setOpenLayData] = useState<boolean>(false);
  useMediumZoom('.zoom-image', {
    margin: 40,
    container: '#zoom-container',
  });
  const [currentPrintPlate, setCurrentPrintPlate] = useState<any>({});
  const [currentLayData, setCurrentLayData] = useState<any>({});
  const [itemSizeList, setItemSizeList] = useState<any>([]);
  const [subItemSizeList, setSubItemSizeList] = useState<any>([]);
  const [plateList, setPlateList] = useState<any>([]);
  const [dieCutList, setDieCutList] = useState<any>([]);
  // const [machineList, setMachineList] = useState<any>([]);
  const [, setIsProcessing] = useState<boolean>(false);
  const leftRef = useRef<HTMLDivElement>(null);
  const rightRef = useRef<HTMLDivElement>(null);
  const leftExtraRef = useRef<HTMLDivElement>(null);
  const rightExtraRef = useRef<HTMLDivElement>(null);
  const [openEditExtra, setOpenEditExtra] = useState(false);
  const [editSide, setEditSide] = useState<'front' | 'back' | null>(null);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [editingExtra, setEditingExtra] = useState<ExtraItem | null>(null);

  const extraSideSchema = yup.object({
    blogSubMaterialDetailId: yup.number().required(),
    extraAreaDimensionId: yup.number().nullable(),

    // กฎ width/height
    extras: yup
      .array()
      .of(extraItemSchema)
      .test(
        // --- ชื่อ test & ข้อความ error ---
        'manual-size-required',
        'กรุณาระบุ width/height (>0) ให้ครบ',
        function (extras) {
          /*
           * this.parent คือ object extraFront[i] หรือ extraBack[i]
           * ---------------------------------------------------------------------
           * กติกาใหม่:
           *   - ถ้าเลือก Dimension (extraAreaDimensionId !== null)   →  ข้าม check
           *   - ถ้าไม่เลือก Dimension  (extraAreaDimensionId === null)→  บังคับ size
           */
          const { extraAreaDimensionId } = this.parent;

          // 1) มี Dimension แล้ว  → ผ่านเลย
          if (extraAreaDimensionId !== null) return true;

          // 2) ยังไม่มี Dimension
          if (!extras?.length) return false; // array ว่าง = ไม่ผ่าน
          return extras.every((ex: any) => ex.width > 0 && ex.height > 0);
        }
      ),

    extraGroups: yup
      .object({
        estimateProductExtraGroupId: yup.number().required(),
        widthMaterial: yup.number().moreThan(0).required(),
        heightMaterial: yup.number().moreThan(0).required(),
      })
      .nullable(),
  });

  const extraValidated = yup.array().of(extraSideSchema);
  const extraPassthrough = yup.array();

  /* ---------- ฟังก์ชันสร้าง schema หลัก ---------- */
  const buildValidationSchema = (currentLayData: any) => {
    const base: Record<string, any> = {
      estimatePrintPlateId: yup
        .number()
        .typeError('กรุณากรอก')
        .required('กรุณากรอก'),

      estimateQuantity: yup.object({
        estimateQuantityId: yup
          .number()
          .typeError('กรุณากรอก')
          .required('กรุณากรอก'),
      }),

      quantityPerSheet: yup
        .number()
        .typeError('กรุณากรอกจำนวน')
        .required('กรุณากรอกจำนวน'),

      rawMaterialId: yup.number().typeError('กรุณากรอก').required('กรุณากรอก'),

      itemSizeId: yup
        .number()
        .typeError('กรุณาเลือกขนาดใบเต็ม')
        .required('กรุณาเลือกขนาดใบเต็ม'),

      subItemSizeId: yup
        .number()
        .typeError('กรุณาเลือกขนาดใบพิมพ์')
        .required('กรุณาเลือกขนาดใบพิมพ์'),

      colorFront: yup.array().of(
        yup.object({
          estimateProductColorId: yup.string().required(),
          colorCode: yup.string(), // ไม่บังคับ code; ถ้าต้องบังคับให้ .required()
        })
      ),

      colorBack: yup.array().of(
        yup.object({
          estimateProductColorId: yup.string().required(),
          colorCode: yup.string(),
        })
      ),

      extraFront: yup
        .array()
        .when(['isUnSpecExtra'], ([flag]) =>
          flag ? extraPassthrough : extraValidated
        ),

      extraBack: yup
        .array()
        .when(['isUnSpecExtra'], ([flag]) =>
          flag ? extraPassthrough : extraValidated
        ),
    };

    /* --------- ถ้ามีระบบพิมพ์ → บังคับเพลท/บล็อก --------- */
    if (!isNull(currentLayData?.printingRequest?.printSystem)) {
      Object.assign(base, {
        plateItemSizeId: yup
          .number()
          .typeError('กรุณาเลือก')
          .required('กรุณาเลือก'),
        plateSubItemSizeId: yup
          .number()
          .typeError('กรุณาเลือก')
          .required('กรุณาเลือก'),
        plateRawMaterialId: yup
          .number()
          .typeError('กรุณาเลือกเพลท')
          .required('กรุณาเลือกเพลท'),

        dieCutRawMaterialId: yup
          .number()
          .typeError('กรุณาเลือก Die-cut')
          .required('กรุณาเลือก Die-cut'),
        dieCutItemSizeId: yup
          .number()
          .typeError('กรุณาเลือก')
          .required('กรุณาเลือก'),
        dieCutSubItemSizeId: yup
          .number()
          .typeError('กรุณาเลือก')
          .required('กรุณาเลือก'),
      });
    }

    /* ---------- ส่งออกเป็น schema ---------- */
    return yup.object().shape(base);
  };

  const validationSchema = useMemo(
    () => buildValidationSchema(currentLayData),
    [currentLayData] // เปลี่ยน schema เมื่อ layData เปลี่ยน
  );

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    // control,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm<FormValues>({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValue,
  });

  // const getMachineList = async () => {
  //   const res = await apiMachine.getMachineList();
  //   if (!res.isError) {
  //     setMachineList(res);
  //   }
  // };
  //
  // useEffect(() => {
  //   getMachineList().then();
  // }, [currentLayData]);

  useEffect(() => {
    if (!isEmpty(currentLayData)) {
      reset(watch());
    }
  }, [currentLayData, reset]);

  useEffect(() => {
    const syncHeight = () => {
      const left = leftRef.current;
      const right = rightRef.current;
      const leftExtra = leftExtraRef.current;
      const rightExtra = rightExtraRef.current;
      if (!left || !right || !leftExtra || !rightExtra) return;

      const leftHeight = left.offsetHeight;
      const rightHeight = right.offsetHeight;
      const leftExtraHeight = leftExtra.offsetHeight;
      const rightExtraHeight = rightExtra.offsetHeight;
      const maxHeight = Math.max(leftHeight, rightHeight);
      const maxExtraHeight = Math.max(leftExtraHeight, rightExtraHeight);

      left.style.height = `${maxHeight}px`;
      left.style.minHeight = `${maxHeight}px`;
      right.style.height = `${maxHeight}px`;
      right.style.minHeight = `${maxHeight}px`;

      leftExtra.style.height = `${maxExtraHeight}px`;
      leftExtra.style.minHeight = `${maxExtraHeight}px`;
      rightExtra.style.height = `${maxExtraHeight}px`;
      rightExtra.style.minHeight = `${maxExtraHeight}px`;
    };

    if (
      !leftRef.current ||
      !rightRef.current ||
      !leftExtraRef.current ||
      !rightExtraRef.current
    )
      return;

    const resizeObserver = new ResizeObserver(syncHeight);
    resizeObserver.observe(leftRef.current);
    resizeObserver.observe(rightRef.current);
    resizeObserver.observe(leftExtraRef.current);
    resizeObserver.observe(rightExtraRef.current);
    // Initial sync after refs mounted
    syncHeight();

    return () => {
      resizeObserver.disconnect();
    };
  }, [currentLayData, openLayData]);

  const getSalesOrderById = async () => {
    const res = await apiEstimate.getSalesOrderById(salesOrderId as string);
    if (!res.isError) {
      dispatch(setSalesOrderById(res.data));
    }
  };

  const getItemSize = async () => {
    const res = await apiRawMaterial.getSubMaterialById({
      limit: 9999,
      subMaterialDetailId: currentLayData.subMaterialDetail.id,
    });
    if (!res.isError) {
      setItemSizeList(res.data);
    }
  };

  const getSubItemSize = async () => {
    const res = await apiSubItemSize.getSubItemSizeListById({
      itemSizeId: watch('itemSizeId'),
    });
    if (!res.isError) {
      setSubItemSizeList(res.data);
    }
  };

  const getPlateList = async () => {
    const res = await apiRawMaterial.checkMaster({
      materialId: 5,
    });
    if (!res.isError) {
      setPlateList(res.data);
    }
  };

  const getDieCutList = async () => {
    const res = await apiRawMaterial.checkMaster({
      materialId: 6,
    });
    if (!res.isError) {
      setDieCutList(res.data);
    }
  };

  useEffect(() => {
    getPlateList().then();
    getDieCutList().then();
  }, []);

  useEffect(() => {
    if (!isEmpty(currentLayData)) {
      setItemSizeList([]);
      getItemSize().then();
    }
  }, [currentLayData]);

  useEffect(() => {
    if (!isEmpty(salesOrderById) && !isEmpty(currentPrintPlate)) {
      const findLayData = salesOrderById.estimateProduct.find(
        (ld: any) => ld.id === currentPrintPlate.estimateProductId
      );
      if (findLayData) {
        setCurrentLayData(findLayData);
      }
    }
  }, [currentPrintPlate, salesOrderById]);

  const buildLayoutForm = (
    file: File,
    side: 'front' | 'back',
    printPlateId: string | number
  ) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('typeLayOut', side === 'front' ? '1' : '2');
    fd.append('estimatePrintPlateId', `${printPlateId}`);
    return fd;
  };

  const buildExtraForm = (
    file: File,
    side: 'front' | 'back',
    printPlateId: string | number
  ) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('typeLayOut', side === 'front' ? '1' : '2');
    fd.append('estimatePrintPlateId', `${printPlateId}`);
    return fd;
  };

  const onSubmit = async (values: FormValues) => {
    setSubmitting(true);

    /** 4.1  อัปเดตข้อมูลหลัก */
    const res = await apiEstimatePrintPlate.updateEstimatePrintPlate(values);
    if (res.isError) {
      dispatch(
        setSnackBar({ status: true, text: res.message, severity: 'error' })
      );
      setSubmitting(false);
      return;
    }
    dispatch(
      setSnackBar({ status: true, text: res.message, severity: 'success' })
    );

    /** 4.2  อัปโหลดเลย์เอาต์ */
    const layoutUploads = [
      layoutFile.layoutFrontFile &&
        (await apiEstimatePrintPlate.uploadLayoutFile(
          buildLayoutForm(
            layoutFile.layoutFrontFile,
            'front',
            values.estimatePrintPlateId
          )
        )),
      layoutFile.layoutBackFile &&
        (await apiEstimatePrintPlate.uploadLayoutFile(
          buildLayoutForm(
            layoutFile.layoutBackFile,
            'back',
            values.estimatePrintPlateId
          )
        )),
    ];

    /** 4.3  อัปโหลดเทคนิคพิเศษ */
    const extraUploads = [
      extraFile.extraFrontFile &&
        (await apiEstimatePrintPlate.uploadExtraLayout(
          buildExtraForm(
            extraFile.extraFrontFile,
            'front',
            values.estimatePrintPlateId
          )
        )),
      extraFile.extraBackFile &&
        (await apiEstimatePrintPlate.uploadExtraLayout(
          buildExtraForm(
            extraFile.extraBackFile,
            'back',
            values.estimatePrintPlateId
          )
        )),
    ];

    const results = await Promise.allSettled([
      ...layoutUploads,
      ...extraUploads,
    ] as Promise<any>[]);

    const anyError = results.some(
      (r) =>
        r.status === 'rejected' ||
        (r.status === 'fulfilled' && r.value?.isError)
    );

    dispatch(
      setSnackBar({
        status: true,
        text: anyError
          ? 'บันทึกสำเร็จ แต่มีไฟล์บางส่วนอัปโหลดไม่ผ่าน'
          : 'บันทึกสำเร็จ',
        severity: anyError ? 'warning' : 'success',
      })
    );

    /** 4.4  รีเฟรช */
    await getSalesOrderById();
    await getPrintPlateById(values.estimatePrintPlateId, true);
    setLayoutFile(initialLayoutFile);
    setExtraFile(initialExtraFile);
    setSubmitting(false);
    setOpenLayData(false);
  };

  const getPrintPlateById = async (
    estimatePrintPlateId: string,
    blockOpen: boolean
  ) => {
    const res = await apiEstimatePrintPlate.getEstimatePrintPlateById(
      Number(estimatePrintPlateId)
    );
    if (!res.isError) {
      await handleClickEditPrintPlate(res.data, blockOpen);
    }
  };

  useEffect(() => {
    if (salesOrderId) {
      getSalesOrderById().then();
    }
  }, [salesOrderId]);

  const handleLayoutUpload = async (
    files: File[],
    side: 'front' | 'back'
  ): Promise<void> => {
    const file = files[0];
    if (!file) return;

    if (side === 'front') {
      setLayoutFile((s: any) => ({ ...s, layoutFrontFile: file }));
      setLayoutBlob((s) => ({
        ...s,
        layoutFrontBlob: URL.createObjectURL(file),
      }));
    } else {
      setLayoutFile((s: any) => ({ ...s, layoutBackFile: file }));
      setLayoutBlob((s) => ({
        ...s,
        layoutBackBlob: URL.createObjectURL(file),
      }));
    }
  };

  const removeLayoutImage = (side: 'front' | 'back'): void => {
    if (side === 'front') {
      setLayoutFile((s: any) => ({ ...s, layoutFrontFile: null }));
      setLayoutBlob((s) => ({ ...s, layoutFrontBlob: '' }));
    } else {
      setLayoutFile((s: any) => ({ ...s, layoutBackFile: null }));
      setLayoutBlob((s) => ({ ...s, layoutBackBlob: '' }));
    }
  };

  const handleExtraUpload = async (
    files: File[],
    side: 'front' | 'back'
  ): Promise<void> => {
    const file = files[0];
    if (!file) return;

    if (side === 'front') {
      setExtraFile((s: any) => ({ ...s, extraFrontFile: file }));
      setExtraBlob((s) => ({
        ...s,
        extraFrontBlob: URL.createObjectURL(file),
      }));
    } else {
      setExtraFile((s: any) => ({ ...s, extraBackFile: file }));
      setExtraBlob((s) => ({
        ...s,
        extraBackBlob: URL.createObjectURL(file),
      }));
    }
  };

  const removeExtraImage = (side: 'front' | 'back'): void => {
    if (side === 'front') {
      setExtraFile((s: any) => ({ ...s, extraFrontFile: null }));
      setExtraBlob((s) => ({ ...s, extraFrontBlob: '' }));
    } else {
      setExtraFile((s: any) => ({ ...s, extraBackFile: null }));
      setExtraBlob((s) => ({ ...s, extraBackBlob: '' }));
    }
  };

  const transformExtras = (extras: any[]) => {
    return extras.map((item) => ({
      blogSubMaterialDetailId: item.blogSubMaterialDetail?.id ?? null,
      extraAreaDimensionId: item.extraAreaDimension?.id ?? null,
      extras:
        item.extras?.map((ex: any) => ({
          estimateProductExtraId: ex.estimateProductExtraId,
          quantity: ex.quantity,
          width: ex.width,
          height: ex.height,
        })) ?? [],
      extraGroups: item.extraGroups
        ? {
            estimateProductExtraGroupId:
              item.extraGroups.estimateProductExtraGroupId,
            widthMaterial: item.extraGroups.widthMaterial,
            heightMaterial: item.extraGroups.heightMaterial,
          }
        : null,
    }));
  };

  const handleClickEditPrintPlate = async (data: any, blockOpen?: boolean) => {
    setCurrentPrintPlate(data);
    setValue('estimatePrintPlateId', data.id);
    setValue('isUnSpecExtra', data.isUnSpecExtra);
    setValue('estimateQuantity.estimateQuantityId', data.estimateQuantity.id);
    setValue('quantityPerSheet', data.quantityPerSheet);
    setValue('rawMaterialId', data.rawMaterial?.id ?? '');
    setValue('itemSizeId', data.itemSize?.id ?? '');
    setValue('subItemSizeId', data.subItemSize?.id ?? '');
    setValue('plateRawMaterialId', data.plateRawMaterial?.id ?? '');
    setValue('plateItemSizeId', data.plateItemSize?.id ?? '');
    setValue('plateSubItemSizeId', data.plateSubItemSize?.id ?? '');
    setValue('dieCutRawMaterialId', data.dieCutRawMaterial?.id ?? '');
    setValue('dieCutItemSizeId', data.dieCutItemSize?.id ?? '');
    setValue('dieCutSubItemSizeId', data.dieCutSubItemSize?.id ?? '');
    setValue(
      'colorFront',
      data.colorFront.map((item: any) => ({
        estimateProductColorId: item.id,
        colorCode: item.colorCode,
        // machineId: item.machine?.id ?? '',
      }))
    );
    setValue(
      'colorBack',
      data.colorBack.map((item: any) => ({
        estimateProductColorId: item.id,
        colorCode: item.colorCode,
        // machineId: item.machine?.id ?? '',
      }))
    );
    setValue('extraFront', transformExtras(data.extraFront));
    setValue('extraBack', transformExtras(data.extraBack));
    setLayoutBlob({
      layoutFrontBlob: data.layoutFrontUrl,
      layoutBackBlob: data.layoutBackUrl,
    });
    setExtraBlob({
      extraFrontBlob: data.extraFrontUrl,
      extraBackBlob: data.extraBackUrl,
    });
    if (!blockOpen) {
      setOpenLayData(true);
    }
  };
  const handlePreviewClick = (event: any, imageSelector: string) => {
    event.preventDefault();
    const zoomElement = document.querySelector(imageSelector) as HTMLElement;
    if (zoomElement) {
      zoomElement.click();
    }
  };

  const handleChangeItemSizeId = (value: number) => {
    setValue('subItemSizeId', '');
    setValue('itemSizeId', value.toString(), {
      shouldValidate: true,
    });
  };

  useEffect(() => {
    if (watch('itemSizeId')) {
      getSubItemSize().then();
    }
  }, [watch('itemSizeId')]);

  const handleChangeColorCode = (
    side: 'front' | 'back',
    index: number,
    value: string
  ) => {
    // อัปเดตค่าใน React Hook Form
    if (side === 'front') {
      setValue(`colorFront.${index}.colorCode`, value, {
        shouldValidate: true,
      });
    } else {
      setValue(`colorBack.${index}.colorCode`, value, { shouldValidate: true });
    }
  };

  const handleSaveDraft = async () => {
    const body = watch();
    setSubmitting(true);

    const res = await apiEstimatePrintPlate.updateEstimatePrintPlate(body);
    if (res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message?.message ?? 'บันทึกแบบร่างไม่สำเร็จ',
          severity: 'error',
        })
      );
      setSubmitting(false);
      return;
    }

    /* อัปโหลดเฉพาะไฟล์ที่เลือกไว้ */
    const uploads: Promise<any>[] = [];

    if (layoutFile.layoutFrontFile)
      uploads.push(
        apiEstimatePrintPlate.uploadLayoutFile(
          buildLayoutForm(
            layoutFile.layoutFrontFile,
            'front',
            body.estimatePrintPlateId
          )
        )
      );
    if (layoutFile.layoutBackFile)
      uploads.push(
        apiEstimatePrintPlate.uploadLayoutFile(
          buildLayoutForm(
            layoutFile.layoutBackFile,
            'back',
            body.estimatePrintPlateId
          )
        )
      );
    if (extraFile.extraFrontFile)
      uploads.push(
        apiEstimatePrintPlate.uploadExtraLayout(
          buildExtraForm(
            extraFile.extraFrontFile,
            'front',
            body.estimatePrintPlateId
          )
        )
      );
    if (extraFile.extraBackFile)
      uploads.push(
        apiEstimatePrintPlate.uploadExtraLayout(
          buildExtraForm(
            extraFile.extraBackFile,
            'back',
            body.estimatePrintPlateId
          )
        )
      );

    await Promise.allSettled(uploads);

    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: 'success',
      })
    );
    await getSalesOrderById();
    setSubmitting(false);
  };

  const checkExtra = (data: any) => {
    if (isNull(data.extraAreaDimension)) {
      const unFinished = data.extras?.some(
        (item: any) => !item.height || !item.width
      );
      const isFoil = !isNull(data.extraGroups);
      const isFoilRequired =
        isFoil &&
        (!data.extraGroups?.heightMaterial || !data.extraGroups?.widthMaterial);

      if (unFinished) {
        let message = 'ระบุขนาดเทคนิค';
        if (isFoilRequired) {
          message += ', ระบุขนาดฟอยล์';
        }
        return (
          <span
            style={{
              color: '#D32F2F',
            }}
          >
            {' '}
            - {message}
          </span>
        );
      }
    }
  };

  const handleClickEditExtra = (
    side: 'front' | 'back',
    index: number,
    data: ExtraItem
  ) => {
    setEditSide(side);
    setEditIndex(index);
    setEditingExtra(data);
    setOpenEditExtra(true);
  };

  const handleSaveExtra = (updated: ExtraItem) => {
    if (editSide === null || editIndex === null) return;

    /* ---------- 1) อัปเดตใน react-hook-form ---------- */
    const fieldName = editSide === 'front' ? 'extraFront' : 'extraBack';
    const formArr = [...watch(fieldName)];
    formArr[editIndex] = updated; // แทนที่ค่าที่ถูกแก้
    setValue(fieldName as any, formArr, { shouldValidate: true });

    /* ---------- 2) อัปเดตใน currentPrintPlate ---------- */
    setCurrentPrintPlate((prev: any) => {
      if (!prev) return prev;

      const cloned = { ...prev };
      cloned[fieldName] = [...cloned[fieldName]]; // clone array
      cloned[fieldName][editIndex] = (() => {
        const original = { ...cloned[fieldName][editIndex] }; // สำเนา item เดิม

        /* ----- 2.1 sync extras (qty / size) ----- */
        original.extras = original.extras.map((ex: any) => {
          const found = updated.extras.find(
            (u) => u.estimateProductExtraId === ex.estimateProductExtraId
          );
          return found
            ? {
                ...ex,
                quantity: found.quantity,
                width: found.width,
                height: found.height,
              }
            : ex; // ไม่พบ = ไม่เปลี่ยน
        });

        /* ----- 2.2 sync extraGroups (ฟอยล์) ----- */
        if (original.extraGroups && updated.extraGroups) {
          original.extraGroups = {
            ...original.extraGroups,
            widthMaterial: updated.extraGroups.widthMaterial,
            heightMaterial: updated.extraGroups.heightMaterial,
          };
        }

        return original; // คืน item ที่หน้าตาเหมือนเดิม แต่ข้อมูลใหม่
      })();

      return cloned;
    });
  };

  const handleClickCheckbox = (isCheck: boolean) => {
    setValue('isUnSpecExtra', isCheck);
  };

  const renderExtraText = (ef: any) => {
    // 1) ชื่อ + จำนวนจุด
    const parts: string[] = [
      ef.blogSubMaterialDetail.name,
      `${numberWithCommas(ef.count)} จุด`,
    ];

    // 2) ขนาดเทคนิค (กรณีไม่มี Dimension)
    if (isNull(ef.extraAreaDimension)) {
      const first = ef.extras?.[0];
      if (first?.width && first?.height) {
        parts.push(`ขนาดเทคนิค ${first.width}x${first.height} นิ้ว`);
      }
    } else {
      // มี Dimension → ใช้ชื่อ Dimension แทน
      parts.push(ef.extraAreaDimension.name);
    }

    // 3) ขนาดฟอยล์ (ถ้ามี extraGroups)
    if (ef.extraGroups?.widthMaterial && ef.extraGroups?.heightMaterial) {
      parts.push(
        `ขนาดฟอยล์ ${ef.extraGroups.widthMaterial}x${ef.extraGroups.heightMaterial} นิ้ว`
      );
    }

    return parts.join(', ');
  };

  return (
    <>
      {openLayData ? (
        <>
          <ProductNav
            title={'ข้อมูลเลย์เอาท์'}
            showBorderBottom={true}
            showAvatar={false}
            animate={false}
            backUrl={'#'}
            backUrlEvent={() => {
              setOpenLayData(false);
            }}
          />
          <ModalEditExtraLay
            open={openEditExtra}
            extra={editingExtra}
            onClose={() => setOpenEditExtra(false)}
            onSave={handleSaveExtra}
          />
        </>
      ) : (
        <ProductNav
          title={!isEmpty(salesOrderById) ? 'รายละเอียด' : ''}
          showBorderBottom={true}
          showAvatar={false}
          backUrl={'/sales-order'}
          animate={false}
        />
      )}
      {openLayData && !isEmpty(currentLayData) && (
        <CreatePrintSheetFormWrapperStyle>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="sheet-header">
              <div className="content">
                <div className="left-group">
                  <div className="image">
                    <Image
                      src={'/images/product/empty-product.svg'}
                      width={160}
                      height={160}
                      alt={'img'}
                      className="image"
                    />
                  </div>
                  <div className="text-group">
                    <div className="title">{currentLayData.ldCode}</div>
                    <div className="sub-title">
                      {currentLayData.productModel.productModelName} •
                      ขนาดสินค้า {numberWithCommas(currentLayData.width)} x{' '}
                      {numberWithCommas(currentLayData.height)} x{' '}
                      {numberWithCommas(currentLayData.length)} mm.
                    </div>
                  </div>
                </div>
                <div className="right-group">
                  <div>
                    <p>จำนวนผลิต</p>
                    <TextField
                      type="number"
                      value={currentPrintPlate.estimateQuantity.quantity}
                      InputProps={{
                        endAdornment: <div className="pl-[12px]">ชิ้น</div>,
                      }}
                      disabled
                    />
                  </div>
                  <div>
                    <p>จำนวน/ใบพิมพ์</p>
                    <TextField
                      type="number"
                      placeholder="จำนวน/ใบพิมพ์"
                      {...register('quantityPerSheet')}
                      error={Boolean(hookFormErrors.quantityPerSheet)}
                      helperText={
                        hookFormErrors.quantityPerSheet
                          ? (hookFormErrors.quantityPerSheet
                              .message as ReactNode)
                          : ''
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (value < 1) {
                          e.target.value = '';
                        } else {
                          setValue('quantityPerSheet', value.toString(), {
                            shouldValidate: true,
                          });
                        }
                      }}
                      onKeyDown={(e: any) => {
                        if (e.key === '-') {
                          e.preventDefault();
                        }
                      }}
                      sx={{
                        width: '156px',
                      }}
                      InputProps={{
                        endAdornment: <div>ตัว</div>,
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="border-bar" />
            </div>
            <div className="data-print-sheet-wrapper">
              <div className="form-group-wrap">
                <div className="title">Material</div>
                <div className="form-group">
                  <div className="row">
                    <div className="material-group">
                      <div className="image">
                        <Image
                          src={'/images/product/empty-product.svg'}
                          width={80}
                          height={80}
                          alt={'img'}
                          className="image"
                        />
                      </div>
                      <div className="text-group">
                        <div className="name">
                          {currentLayData.subMaterialDetail.name}
                        </div>
                        <div className="unit">
                          {currentLayData.subMaterialDetail.name}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="row">
                    <FadeInStyled
                      style={{
                        width: watch('itemSizeId') ? '50%' : '100%',
                      }}
                    >
                      <p>ขนาดใบเต็ม</p>
                      <FormControl
                        sx={{
                          height: '40px',
                        }}
                        fullWidth
                      >
                        <Select
                          {...register('itemSizeId')}
                          error={Boolean(hookFormErrors.itemSizeId)}
                          displayEmpty
                          value={watch('itemSizeId')}
                          onChange={(e: any) => {
                            handleChangeItemSizeId(e.target.value);
                          }}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {itemSizeList.map((item: any, index: React.Key) => (
                            <MenuItem
                              key={index}
                              value={item.itemSize.id}
                              onClick={() => {
                                setValue('rawMaterialId', item.id);
                              }}
                            >
                              {item.itemSize.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      {hookFormErrors.itemSizeId && (
                        <FormHelperText
                          error
                          sx={{
                            margin: '4px 14px 0',
                          }}
                        >
                          {hookFormErrors.itemSizeId.message}
                        </FormHelperText>
                      )}
                    </FadeInStyled>
                    {watch('itemSizeId') && (
                      <FadeInStyled
                        style={{
                          width: '50%',
                        }}
                      >
                        <p>ขนาดใบพิมพ์</p>
                        <FormControl
                          sx={{
                            height: '40px',
                          }}
                          fullWidth
                        >
                          <Select
                            {...register('subItemSizeId')}
                            error={Boolean(hookFormErrors.subItemSizeId)}
                            displayEmpty
                            value={watch('subItemSizeId')}
                            onChange={(e: any) => {
                              setValue('subItemSizeId', e.target.value, {
                                shouldValidate: true,
                              });
                            }}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {subItemSizeList.map((item: any) => (
                              <MenuItem key={item.id} value={item.id}>
                                {item.itemSizeName}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        {hookFormErrors.subItemSizeId && (
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            {hookFormErrors.subItemSizeId.message}
                          </FormHelperText>
                        )}
                      </FadeInStyled>
                    )}
                  </div>
                </div>
              </div>
              {!isNull(currentLayData.printingRequest.printSystem) && (
                <div className="form-group-wrap">
                  <div className="title">แม่พิมพ์</div>
                  <div className="form-group">
                    <div className="row">
                      {/* {!isEmpty(plateList) ? ( */}
                      <FadeInStyled
                        style={{
                          width: '100%',
                        }}
                      >
                        <p>เพลท</p>
                        <FormControl
                          sx={{
                            height: '40px',
                          }}
                          fullWidth
                        >
                          <Select
                            {...register('plateRawMaterialId', {
                              valueAsNumber: true,
                            })}
                            error={!!hookFormErrors.plateRawMaterialId}
                            displayEmpty
                            value={watch('plateRawMaterialId') || ''}
                            onChange={(e) => {
                              const plateId = Number(e.target.value);
                              if (!plateId) return;

                              // เซ็ตค่าของเพลท
                              setValue('plateRawMaterialId', String(plateId), {
                                shouldValidate: true,
                              });

                              const plate = plateList.find(
                                (p: any) => p.id === plateId
                              );
                              if (!plate) return;

                              const { itemSize } = plate;

                              setValue('plateItemSizeId', itemSize.id, {
                                shouldValidate: true,
                              });
                              setValue(
                                'plateSubItemSizeId',
                                itemSize.subItemSizeDto?.[0]?.id ?? '',
                                { shouldValidate: true }
                              );

                              // หา Die-cut ที่มี itemSize.id ตรงกัน
                              const autoDieCut = dieCutList.find(
                                (d: any) => d.itemSize.id === itemSize.id
                              );

                              if (autoDieCut) {
                                setValue('dieCutRawMaterialId', autoDieCut.id, {
                                  shouldValidate: true,
                                });
                                setValue('dieCutItemSizeId', itemSize.id, {
                                  shouldValidate: true,
                                });
                                setValue(
                                  'dieCutSubItemSizeId',
                                  itemSize.subItemSizeDto?.[0]?.id ?? '',
                                  { shouldValidate: true }
                                );
                              } else {
                                // ถ้าไม่พบให้เคลียร์ (หรือจะคงค่าเดิมไว้ก็ได้)
                                setValue('dieCutRawMaterialId', '', {
                                  shouldValidate: true,
                                });
                                setValue('dieCutItemSizeId', '', {
                                  shouldValidate: true,
                                });
                                setValue('dieCutSubItemSizeId', '', {
                                  shouldValidate: true,
                                });
                              }
                            }}
                          >
                            <MenuItem disabled value="">
                              <span className="text-[#78909C]">กรุณาเลือก</span>
                            </MenuItem>
                            {plateList.map((p: any) => (
                              <MenuItem key={p.id} value={p.id}>
                                {p.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        {hookFormErrors.plateRawMaterialId && (
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            {hookFormErrors.plateRawMaterialId.message}
                          </FormHelperText>
                        )}
                      </FadeInStyled>
                      {/* // ) : ( */}
                      {/* //   !isEmpty(watch('plateRawMaterialId')) && ( */}
                      {/* //     <FadeInStyled */}
                      {/* //       onClick={async () => { */}
                      {/* //         await router.push('/stock/raw-material/create'); */}
                      {/* //       }} */}
                      {/* //       style={{ width: '100%' }} */}
                      {/* //     > */}
                      {/* //       <p>เพลท</p> */}
                      {/* //       <EmptyField>กรุณาสร้าง Raw-Material</EmptyField> */}
                      {/* //     </FadeInStyled> */}
                      {/* //   ) */}
                      {/* // )} */}
                    </div>
                    <div className="row">
                      {/* {!isEmpty(dieCutList) ? ( */}
                      <FadeInStyled style={{ width: '100%' }}>
                        <p>Die-cut</p>
                        <FormControl
                          sx={{
                            height: '40px',
                          }}
                          fullWidth
                        >
                          <Select
                            {...register('dieCutRawMaterialId')}
                            error={Boolean(hookFormErrors.dieCutRawMaterialId)}
                            value={watch('dieCutRawMaterialId')}
                            displayEmpty
                            disabled={true}
                            // onChange={(event: any) => {
                            //   setValue(
                            //     'dieCutRawMaterialId',
                            //     event.target.value,
                            //     {
                            //       shouldValidate: true,
                            //     }
                            //   );
                            //   const findDieCutItemSizeId = dieCutList.find(
                            //     (pI: any) => pI.id === event.target.value
                            //   );
                            //   if (findDieCutItemSizeId) {
                            //     setValue(
                            //       'dieCutItemSizeId',
                            //       findDieCutItemSizeId.itemSize.id,
                            //       {
                            //         shouldValidate: true,
                            //       }
                            //     );
                            //   }
                            //   setValue(
                            //     'dieCutSubItemSizeId',
                            //     findDieCutItemSizeId.itemSize.subItemSizeDto[0]
                            //       .id,
                            //     {
                            //       shouldValidate: true,
                            //     }
                            //   );
                            // }}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {dieCutList.map((dieCutItem: any) => (
                              <MenuItem
                                key={dieCutItem.id}
                                value={dieCutItem.id}
                              >
                                {dieCutItem.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        {hookFormErrors.dieCutRawMaterialId && (
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            {hookFormErrors.dieCutRawMaterialId.message}
                          </FormHelperText>
                        )}
                      </FadeInStyled>
                      {/* ) : ( */}
                      {/*  <FadeInStyled */}
                      {/*    onClick={async () => { */}
                      {/*      await router.push('/stock/raw-material/create'); */}
                      {/*    }} */}
                      {/*    style={{ width: '100%' }} */}
                      {/*  > */}
                      {/*    <p>บล็อกไดคัท</p> */}
                      {/*    <EmptyField>กรุณาสร้าง Raw-Material</EmptyField> */}
                      {/*  </FadeInStyled> */}
                      {/* )} */}
                      {/* {!isEmpty(dieCutSize) && ( */}
                      {/* )} */}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="image-upload-section">
              <div className="title">
                การพิมพ์{' | '}
                {currentLayData.printingRequest.printSystem?.name || 'ไม่พิมพ์'}
              </div>
              <div className="image-zone">
                <div className="image-wrap">
                  <div
                    ref={leftRef}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      // justifyContent: 'space-between',
                    }}
                  >
                    <div className="side">ด้านหน้า</div>
                    {currentLayData.printingRequest.colorFront.map(
                      (cf: any, index: number) => {
                        // const machines = machineListMap.front[cf.color.id] || [];
                        return (
                          <div className="machine-selector-item" key={index}>
                            <div className="color">
                              <div className="color-image">
                                <Image
                                  src={
                                    cf.printColor.imageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  alt=""
                                  width={62}
                                  height={62}
                                />
                              </div>
                              <span>{cf.printColor.name}</span>
                            </div>
                            <div className="input-group">
                              <TextField
                                type="text"
                                fullWidth
                                placeholder={
                                  !cf.isColorCode ? cf.printColor.name : 'Code'
                                }
                                {...register(`colorFront.${index}.colorCode`)}
                                value={
                                  watch(`colorFront.${index}.colorCode`) || ''
                                }
                                onChange={(e: any) =>
                                  handleChangeColorCode(
                                    'front',
                                    index,
                                    e.target.value
                                  )
                                }
                                disabled={!cf.isColorCode}
                                // error={Boolean(errorObj?.colorCode)}
                                // helperText={
                                //     errorObj?.colorCode &&
                                //     errorObj.colorCode.message
                                // }
                              />
                              {/* <FormControl fullWidth> */}
                              {/*  <Select */}
                              {/*    {...register(`colorFront.${index}.machineId`)} */}
                              {/*    error={Boolean( */}
                              {/*      hookFormErrors.colorFront?.[index] */}
                              {/*        ?.machineId */}
                              {/*    )} */}
                              {/*    displayEmpty */}
                              {/*    value={watch(`colorFront.${index}.machineId`)} */}
                              {/*    onChange={(e: any) => { */}
                              {/*      setValue( */}
                              {/*        `colorFront.${index}.machineId`, */}
                              {/*        e.target.value, */}
                              {/*        { */}
                              {/*          shouldValidate: true, */}
                              {/*        } */}
                              {/*      ); */}
                              {/*    }} */}
                              {/*  > */}
                              {/*    <MenuItem disabled value=""> */}
                              {/*      <div className="text-[#78909C]"> */}
                              {/*        กรุณาเลือก */}
                              {/*      </div> */}
                              {/*    </MenuItem> */}
                              {/*    {machineList.map((item: any, idx: number) => ( */}
                              {/*      <MenuItem key={idx} value={item.id}> */}
                              {/*        {item.name} */}
                              {/*      </MenuItem> */}
                              {/*    ))} */}
                              {/*  </Select> */}
                              {/* </FormControl> */}
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      overflow: 'hidden',
                    }}
                  >
                    <label
                      className={`image ${
                        isEmpty(layoutBlob.layoutFrontBlob)
                          ? 'cursor-pointer '
                          : '!border-0'
                      }`}
                    >
                      {!isEmpty(layoutBlob.layoutFrontBlob) ? (
                        <>
                          <img
                            src={layoutBlob.layoutFrontBlob}
                            className="zoom-image front"
                          />
                          <div className="action-event">
                            <div
                              className="action-btn preview"
                              onClick={(event) =>
                                handlePreviewClick(event, '.zoom-image.front')
                              }
                            >
                              <div className="icon-action">
                                <ZoomInRoundedIcon />
                              </div>
                              Preview
                            </div>
                            <div
                              className="action-btn preview"
                              onClick={(event: any) => {
                                event.preventDefault();
                                removeLayoutImage('front');
                              }}
                            >
                              <div className="icon-action">
                                <Image
                                  src="/icons/icon-edit-document.svg"
                                  width={24}
                                  height={24}
                                  alt={'img'}
                                />
                              </div>
                              Change
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          <input
                            type="file"
                            style={{ display: 'none' }}
                            onChange={async (event: any) => {
                              const { files } = event.target;
                              setIsProcessing(true);
                              const validationResult = await validateImageFiles(
                                files,
                                2,
                                true,
                                false
                              );
                              setIsProcessing(false);
                              if (validationResult.status) {
                                const newFiles = Array.from(
                                  validationResult.files
                                );
                                await handleLayoutUpload(newFiles, 'front');
                                setErrorImageUpload((prev: any) => ({
                                  ...prev,
                                  layoutFront: '',
                                }));
                              } else {
                                setErrorImageUpload((prev: any) => ({
                                  ...prev,
                                  layoutFront: validationResult.message,
                                }));
                              }
                            }}
                          />
                          <div className="icon">
                            <UploadIcon
                              sx={{
                                color: '#dbe2e5',
                              }}
                            />
                          </div>
                          <div>อัปโหลดรูป</div>
                          <div className="condition">
                            ไฟล์ JPEG, PNG, SVG, หรือ GIF ขนาดไม่เกิน 2 MB.
                          </div>
                        </>
                      )}
                    </label>
                  </div>
                </div>
                <div className="image-wrap">
                  <div
                    ref={rightRef}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <div className="side">ด้านหลัง</div>
                    {currentLayData.printingRequest.colorBack.map(
                      (cf: any, index: number) => {
                        // const machines = machineListMap.front[cf.color.id] || [];
                        return (
                          <div className="machine-selector-item" key={index}>
                            <div className="color">
                              <div className="color-image">
                                <Image
                                  src={
                                    cf.printColor.imageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  alt=""
                                  width={62}
                                  height={62}
                                />
                              </div>
                              <span>{cf.printColor.name}</span>
                            </div>
                            <div className="input-group">
                              <TextField
                                type="text"
                                fullWidth
                                placeholder={
                                  !cf.isColorCode ? cf.printColor.name : 'Code'
                                }
                                {...register(`colorBack.${index}.colorCode`)}
                                value={
                                  watch(`colorBack.${index}.colorCode`) || ''
                                }
                                onChange={(e: any) =>
                                  handleChangeColorCode(
                                    'back',
                                    index,
                                    e.target.value
                                  )
                                }
                                disabled={!cf.isColorCode}
                                // error={Boolean(errorObj?.colorCode)}
                                // helperText={
                                //     errorObj?.colorCode &&
                                //     errorObj.colorCode.message
                                // }
                              />
                              {/* <FormControl fullWidth> */}
                              {/*  <Select */}
                              {/*    {...register(`colorBack.${index}.machineId`)} */}
                              {/*    error={Boolean( */}
                              {/*      hookFormErrors.colorBack?.[index]?.machineId */}
                              {/*    )} */}
                              {/*    displayEmpty */}
                              {/*    value={watch(`colorBack.${index}.machineId`)} */}
                              {/*    onChange={(e: any) => { */}
                              {/*      setValue( */}
                              {/*        `colorBack.${index}.machineId`, */}
                              {/*        e.target.value, */}
                              {/*        { */}
                              {/*          shouldValidate: true, */}
                              {/*        } */}
                              {/*      ); */}
                              {/*    }} */}
                              {/*  > */}
                              {/*    <MenuItem disabled value=""> */}
                              {/*      <div className="text-[#78909C]"> */}
                              {/*        กรุณาเลือก */}
                              {/*      </div> */}
                              {/*    </MenuItem> */}
                              {/*    {machineList.map((item: any, idx: number) => ( */}
                              {/*      <MenuItem key={idx} value={item.id}> */}
                              {/*        {item.name} */}
                              {/*      </MenuItem> */}
                              {/*    ))} */}
                              {/*  </Select> */}
                              {/* </FormControl> */}
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      overflow: 'hidden',
                    }}
                  >
                    <label
                      className={`image ${
                        isEmpty(layoutBlob.layoutBackBlob)
                          ? 'cursor-pointer'
                          : '!border-0'
                      }`}
                    >
                      {!isEmpty(layoutBlob.layoutBackBlob) ? (
                        <>
                          <img
                            src={layoutBlob.layoutBackBlob}
                            className="zoom-image back"
                          />
                          <div className="action-event">
                            <div
                              className="action-btn preview"
                              onClick={(event) =>
                                handlePreviewClick(event, '.zoom-image.back')
                              }
                            >
                              <div className="icon-action">
                                <ZoomInRoundedIcon />
                              </div>
                              Preview
                            </div>
                            <div
                              className="action-btn preview"
                              onClick={(event: any) => {
                                event.preventDefault();
                                removeLayoutImage('back');
                              }}
                            >
                              <div className="icon-action">
                                <Image
                                  src="/icons/icon-edit-document.svg"
                                  width={24}
                                  height={24}
                                  alt={'img'}
                                />
                              </div>
                              Change
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          <input
                            type="file"
                            style={{ display: 'none' }}
                            onChange={async (event: any) => {
                              const { files } = event.target;
                              setIsProcessing(true);
                              const validationResult = await validateImageFiles(
                                files,
                                2,
                                true,
                                false
                              );
                              setIsProcessing(false);
                              if (validationResult.status) {
                                const newFiles = Array.from(
                                  validationResult.files
                                );
                                await handleLayoutUpload(newFiles, 'back');
                                setErrorImageUpload((prev: any) => ({
                                  ...prev,
                                  layoutBack: '',
                                }));
                              } else {
                                setErrorImageUpload((prev: any) => ({
                                  ...prev,
                                  layoutBack: validationResult.message,
                                }));
                              }
                            }}
                          />
                          <div className="icon">
                            <UploadIcon
                              sx={{
                                color: '#dbe2e5',
                              }}
                            />
                          </div>
                          <div>อัปโหลดรูป</div>
                          <div className="condition">
                            ไฟล์ JPEG, PNG, SVG, หรือ GIF ขนาดไม่เกิน 2 MB.
                          </div>
                        </>
                      )}
                    </label>
                  </div>
                </div>
              </div>
              {(!isEmpty(errorImageUpload.layoutFront) ||
                !isEmpty(errorImageUpload.layoutBack)) && (
                <div className="w-full flex justify-center items-center">
                  <FormHelperText
                    error
                    sx={{
                      margin: '4px 14px 0',
                    }}
                  >
                    {errorImageUpload.layoutFront ||
                      errorImageUpload.layoutBack}
                  </FormHelperText>
                </div>
              )}
            </div>
            {(!isEmpty(currentPrintPlate.extraFront) ||
              !isEmpty(currentPrintPlate.extraBack)) && (
              <div className="image-upload-section">
                <div className="title">
                  เทคนิคพิเศษ
                  <FormControlLabel
                    control={
                      <Checkbox
                        color="primary"
                        checked={Boolean(watch('isUnSpecExtra'))}
                        onChange={(event: any) => {
                          handleClickCheckbox(event.target.checked);
                        }}
                        icon={<IconUnCheckbox />}
                        checkedIcon={<IconCheckbox />}
                      />
                    }
                    label="ไม่ระบุข้อมูล"
                  />
                </div>
                <div className="image-zone">
                  <AnimatePresence mode="sync">
                    {watch('isUnSpecExtra') && (
                      <motion.div
                        className="block-event"
                        {...motionFadeConfig}
                        key={'block-event'}
                      />
                    )}
                  </AnimatePresence>
                  {!isEmpty(currentPrintPlate.extraFront) && (
                    <div className="image-wrap">
                      <div
                        ref={leftExtraRef}
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          // justifyContent: 'space-between',
                        }}
                      >
                        <div className="side">ด้านหน้า</div>
                        {currentPrintPlate.extraFront.map(
                          (ef: any, index: number) => {
                            return (
                              <div
                                className="machine-selector-item"
                                key={index}
                              >
                                <div className="color">
                                  <div className="text">
                                    {renderExtraText(ef)}
                                    {checkExtra(ef)}
                                  </div>
                                </div>

                                <div className="input-group">
                                  {isNull(ef.extraAreaDimension) ? (
                                    <Button
                                      type="button"
                                      variant="outlined"
                                      color="blueGrey"
                                      onClick={() =>
                                        handleClickEditExtra('front', index, ef)
                                      }
                                      style={{
                                        height: '40px',
                                        width: '40px',
                                        minWidth: '40px',
                                        position: 'absolute',
                                        right: '-8px',
                                      }}
                                    >
                                      <div
                                        style={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          width: '20px',
                                          height: '20px',
                                        }}
                                      >
                                        <SvgEditIcon />
                                      </div>
                                    </Button>
                                  ) : (
                                    <Image
                                      src={
                                        ef.extraAreaDimension.imageUrl ||
                                        '/images/product/empty-product.svg'
                                      }
                                      alt=""
                                      width={40}
                                      height={40}
                                      style={{
                                        borderRadius: '8px',
                                        position: 'absolute',
                                        right: '-8px',
                                      }}
                                    />
                                  )}
                                  {/* <div>ขนาดคาดการณ์ 50x20 mm</div> */}
                                </div>
                              </div>
                            );
                          }
                        )}
                      </div>
                      <div
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          overflow: 'hidden',
                        }}
                      >
                        <label
                          className={`image ${
                            isEmpty(extraBlob.extraFrontBlob)
                              ? 'cursor-pointer'
                              : '!border-0'
                          }`}
                        >
                          {extraBlob.extraFrontBlob ? (
                            <>
                              <img
                                src={extraBlob.extraFrontBlob}
                                className="zoom-image extra-front"
                              />
                              {/* preview / change */}
                              <div className="action-event">
                                <div
                                  className="action-btn preview"
                                  onClick={(e) =>
                                    handlePreviewClick(
                                      e,
                                      '.zoom-image.extra-front'
                                    )
                                  }
                                >
                                  <ZoomInRoundedIcon className="icon-action" />{' '}
                                  Preview
                                </div>
                                <div
                                  className="action-btn preview"
                                  onClick={(e: any) => {
                                    e.preventDefault();
                                    removeExtraImage('front');
                                  }}
                                >
                                  <div className="icon-action">
                                    <Image
                                      src="/icons/icon-edit-document.svg"
                                      width={24}
                                      height={24}
                                      alt={'img'}
                                    />
                                  </div>
                                  Change
                                </div>
                              </div>
                            </>
                          ) : (
                            <>
                              <input
                                type="file"
                                style={{ display: 'none' }}
                                onChange={async (e: any) => {
                                  const { files } = e.target;
                                  setIsProcessing(true);
                                  const vr = await validateImageFiles(
                                    files,
                                    2,
                                    true,
                                    false
                                  );
                                  setIsProcessing(false);
                                  if (vr.status) {
                                    await handleExtraUpload(
                                      Array.from(vr.files),
                                      'front'
                                    );
                                    setErrorImageUpload((prev: any) => ({
                                      ...prev,
                                      extraFront: '',
                                    }));
                                  } else {
                                    setErrorImageUpload((prev: any) => ({
                                      ...prev,
                                      extraFront: vr.message,
                                    }));
                                  }
                                }}
                              />
                              <UploadIcon
                                sx={{ color: '#dbe2e5' }}
                                className="icon"
                              />
                              <div>อัปโหลดรูป</div>
                              <div className="condition">
                                ไฟล์ JPEG, PNG, SVG, หรือ GIF ขนาดไม่เกิน 2 MB.
                              </div>
                            </>
                          )}
                        </label>
                      </div>
                    </div>
                  )}
                  {/* ---------- BACK  (ด้านหลัง) ---------- */}
                  {!isEmpty(currentPrintPlate.extraBack) && (
                    <div className="image-wrap">
                      {/* ===== ฝั่งข้อความ + ปุ่มแก้ไข ===== */}
                      <div
                        ref={rightExtraRef}
                        style={{ display: 'flex', flexDirection: 'column' }}
                      >
                        <div className="side">ด้านหลัง</div>

                        {currentPrintPlate.extraBack.map(
                          (eb: any, index: number) => (
                            <div className="machine-selector-item" key={index}>
                              <div className="color">
                                <div className="text">
                                  {renderExtraText(eb)}
                                  {checkExtra(eb)}
                                </div>
                              </div>
                              <div className="input-group">
                                {isNull(eb.extraAreaDimension) ? (
                                  <Button
                                    type="button"
                                    variant="outlined"
                                    color="blueGrey"
                                    onClick={() =>
                                      handleClickEditExtra('back', index, eb)
                                    }
                                    style={{
                                      height: 40,
                                      width: 40,
                                      minWidth: 40,
                                      position: 'absolute',
                                      right: '-8px',
                                    }}
                                  >
                                    <div
                                      style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: 20,
                                        height: 20,
                                      }}
                                    >
                                      <SvgEditIcon />
                                    </div>
                                  </Button>
                                ) : (
                                  <Image
                                    src={
                                      eb.extraAreaDimension.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    alt=""
                                    width={40}
                                    height={40}
                                    style={{
                                      borderRadius: '8px',
                                      position: 'absolute',
                                      right: '-8px',
                                    }}
                                  />
                                )}
                              </div>
                            </div>
                          )
                        )}
                      </div>

                      {/* ===== ฝั่งอัปโหลด/แสดงรูป layout ===== */}
                      <div
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          overflow: 'hidden',
                        }}
                      >
                        <label
                          className={`image ${
                            isEmpty(extraBlob.extraBackBlob)
                              ? 'cursor-pointer'
                              : '!border-0'
                          }`}
                        >
                          {/* มีรูปแล้ว */}
                          {!isEmpty(extraBlob.extraBackBlob) ? (
                            <>
                              <img
                                src={extraBlob.extraBackBlob}
                                className="zoom-image back"
                              />
                              <div className="action-event">
                                <div
                                  className="action-btn preview"
                                  onClick={(e) =>
                                    handlePreviewClick(e, '.zoom-image.back')
                                  }
                                >
                                  <div className="icon-action">
                                    <ZoomInRoundedIcon />
                                  </div>
                                  Preview
                                </div>
                                <div
                                  className="action-btn preview"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    removeExtraImage('back');
                                  }}
                                >
                                  <div className="icon-action">
                                    <Image
                                      src="/icons/icon-edit-document.svg"
                                      width={24}
                                      height={24}
                                      alt="img"
                                    />
                                  </div>
                                  Change
                                </div>
                              </div>
                            </>
                          ) : (
                            /* ยังไม่มีรูป */
                            <>
                              <input
                                type="file"
                                style={{ display: 'none' }}
                                onChange={async (e: any) => {
                                  const { files } = e.target;
                                  setIsProcessing(true);
                                  const vr = await validateImageFiles(
                                    files,
                                    2,
                                    true,
                                    false
                                  );
                                  setIsProcessing(false);
                                  if (vr.status) {
                                    await handleExtraUpload(
                                      Array.from(vr.files),
                                      'back'
                                    );
                                    setErrorImageUpload((prev: any) => ({
                                      ...prev,
                                      extraBack: '',
                                    }));
                                  } else {
                                    setErrorImageUpload((prev: any) => ({
                                      ...prev,
                                      extraBack: vr.message,
                                    }));
                                  }
                                }}
                              />
                              <div className="icon">
                                <UploadIcon sx={{ color: '#dbe2e5' }} />
                              </div>
                              <div>อัปโหลดรูป</div>
                              <div className="condition">
                                ไฟล์ JPEG, PNG, SVG, หรือ GIF ขนาดไม่เกิน 2 MB.
                              </div>
                            </>
                          )}
                        </label>
                      </div>
                    </div>
                  )}
                </div>
                {!isEmpty(
                  errorImageUpload.extraFront ||
                    !isEmpty(errorImageUpload.extraBack)
                ) && (
                  <div className="w-full flex justify-center items-center">
                    <FormHelperText
                      error
                      sx={{
                        margin: '4px 14px 0',
                      }}
                    >
                      {errorImageUpload.extraFront ||
                        errorImageUpload.extraBack}
                    </FormHelperText>
                  </div>
                )}
              </div>
            )}
            <div className="flex my-[24px] px-[24px] justify-end">
              <div className="flex gap-[24px] w-[50%]">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={async () => {
                    await handleSaveDraft();
                  }}
                  disabled={submitting}
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'black',
                      }}
                    />
                  ) : (
                    'บันทึกแบบร่าง'
                  )}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  fullWidth
                  disabled={submitting}
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CreatePrintSheetFormWrapperStyle>
      )}

      {!isEmpty(salesOrderById) && (
        <SpecSalesOrder
          reloadOrder={async () => {
            await getSalesOrderById();
          }}
          handleClickEditPrintPlate={async (data: string) => {
            await handleClickEditPrintPlate(data);
          }}
        />
      )}
    </>
  );
};

// const CreateRawMaterialStyle = styled.div`
//   width: 100%;
//   height: calc(100dvh - 64px);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   text-decoration: underline;
//   @media screen and (max-width: 820px) {
//     height: calc(100dvh - 64px + 72px);
//   }
// `;

Spec.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

// const getServerSideProps: GetServerSideProps = async (context) => {
//   const { salesOrderId, step } = context.query;
//   const token = await getCookie('access_token', {
//     req: context.req,
//     res: context.res,
//   });
//   const res = await apiEstimate.getSalesOrderById(
//     salesOrderId as string,
//     token as string
//   );
//   const resSalesOrderStatus = await apiEstimate.getSalesOrderStatus(
//     token as string
//   );
//   const currentStep = res.data.estimateStatus;
//   const paramsStep = resSalesOrderStatus.data.find(
//     (status: any) => status.name === step
//   );
//   const validStep = paramsStep.id <= currentStep.id;
//   if (currentStep.id === 99) {
//     return {
//       redirect: {
//         permanent: false,
//         destination: '/sales-order',
//       },
//     };
//   }
//
//   if (!validStep) {
//     return {
//       redirect: {
//         permanent: false,
//         destination: `/sales-order/${salesOrderId}/spec?step=${decodeURIComponent(
//           currentStep.name
//         )}`,
//       },
//     };
//   }
//
//   return {
//     props: {},
//   };
// };

// @ts-ignore
export default Spec;
