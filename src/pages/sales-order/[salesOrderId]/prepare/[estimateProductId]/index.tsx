import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import PrepareOrderFormZone from '@/components/order/prepare-order/PrepareOrderFormZone';
import PrepareOrderDetailZone from '@/components/order/prepare-order/PrepareOrderDetailZone';
import { useAppDispatch, useAppSelector } from '@/store';
import { isEmpty } from 'lodash';
import apiProductConfig from '@/services/stock/product-config';
import apiProduct from '@/services/stock/product';
import { FormControl, MenuItem, Select } from '@mui/material';
import { displayUnitList } from '@/utils/displayUnit';
import apiEstimateProduct from '@/services/order/estimate-product';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const PrepareOrderStyled = styled.div`
  width: 100%;
  display: flex;
  min-height: calc(100dvh - 64px);
`;
type Props = {
  estimateDataSalesOrderFromServer: any;
};
const PrepareOrder = ({ estimateDataSalesOrderFromServer }: Props) => {
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const router = useRouter();
  const { salesOrderId } = router.query;
  const [finishList, setFinishList] = useState<any>([]);
  const [productInfo, setProductInfo] = useState<any>({});
  const [displayUnit, setDisplayUnit] = useState<string>('mm');
  const getProductFinish = async (productId: number) => {
    const res = await apiProductConfig.getProductConfigOrder({
      productId,
      masterCategoryId: 3, // 3 = เคลือบ
    });
    if (!res.isError) {
      setFinishList(res.data);
    }
  };

  const getProductInfo = async (productId: number) => {
    const res = await apiProduct.getProductById(productId);
    if (res && !res.isError) {
      setProductInfo(res.data);
    }
  };

  useEffect(() => {
    getProductFinish(
      estimateDataSalesOrderFromServer.productModel.productId
    ).then();
    getProductInfo(
      estimateDataSalesOrderFromServer.productModel.productId
    ).then();
    dispatch(setEstimateDataSalesOrder(estimateDataSalesOrderFromServer));
  }, [estimateDataSalesOrderFromServer]);

  return (
    <>
      <ProductNav
        title={'กำหนดสเปคสินค้า'}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={`/sales-order/${salesOrderId}/spec?step=สเปคสินค้า`}
      >
        <FormControl fullWidth>
          <Select
            displayEmpty
            value={displayUnit}
            onChange={(e: any) => {
              setDisplayUnit(e.target.value);
            }}
          >
            {displayUnitList.map((item: string, index: number) => (
              <MenuItem key={index} value={item} className="!lowercase">
                {item}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </ProductNav>
      <PrepareOrderStyled id={'zoom-container'}>
        {!isEmpty(estimateDataSalesOrder) && !isEmpty(productInfo) && (
          <>
            <PrepareOrderFormZone
              finishList={finishList}
              productInfo={productInfo}
              displayUnit={displayUnit}
            />
            <PrepareOrderDetailZone
              finishList={finishList}
              displayUnit={displayUnit}
            />
          </>
        )}
      </PrepareOrderStyled>
    </>
  );
};

PrepareOrder.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { salesOrderId, estimateProductId } = context.query;
  const token = getCookie('access_token', {
    req: context.req,
  });
  const res = await apiEstimateProduct.getEstimateProductById(
    estimateProductId as string,
    token as string
  );
  if (res.data.isConfirm) {
    return {
      redirect: {
        permanent: false,
        destination: `/sales-order/${salesOrderId}/spec`,
      },
    };
  }
  return {
    props: {
      estimateDataSalesOrderFromServer: res.data,
    },
  };
};

export default PrepareOrder;
