import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { OpenDeleteType } from '@/types/category';
import { Button, Divider } from '@mui/material';
import { numberWithCommas } from '@/utils/number';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import SearchInput from '@/components/SearchInput';
import Image from 'next/image';
import ModalCreateProject from '@/components/project/modal/ModalCreateProject';
import apiProject from '@/services/order/project';
import dayjs from 'dayjs';
import PopoverAction from '@/components/PopoverActionn';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';

const ProjectPage = () => {
  const dispatch = useAppDispatch();
  const [rows, setRows] = useState<any>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    search: '',
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [loadingRows, setLoadingRows] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [initialValues, setInitialValues] = useState<any>({
    id: null,
    name: '',
  });
  useEffect(() => {
    getProject().then();
  }, [filters]);

  const getProject = async () => {
    setLoadingRows(true);
    const res = await apiProject.getProject(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalElements(res.data.totalElements);
    }
    setLoadingRows(false);
  };
  const handleDelete = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiProject.deleteProject(openDelete.id);
      if (!res.isError) {
        await getProject();
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'โปรเจกต์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'count',
      headerName: 'รายการใบเสนอราคา',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 172,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.count)}</div>;
      },
    },
    {
      field: 'createdDate',
      headerName: 'สร้างเมื่อ',
      minWidth: 172,
      align: 'right',
      headerAlign: 'right',
      renderCell: (params: any) => (
        <div>
          {dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm น.')}
        </div>
      ),
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 188,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '84px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              onClick={() => {
                //
              }}
            >
              รายละเอียด
            </Button>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => <SvgEditIcon />,
                  title: 'แก้ไข',
                  onAction: async () => {
                    setInitialValues(params.row);
                    setFormMode('edit');
                    setOpen(true);
                  },
                },
                ...(params.row.count === 0
                  ? [
                      {
                        IconElement: () => <SvgDeleteIcon />,
                        title: 'ลบ',
                        onAction: async () => {
                          setOpenDelete({
                            name: params.row.name,
                            status: true,
                            id: params.row.id,
                          });
                        },
                      },
                    ]
                  : []),
              ]}
            />
          </div>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="โปรเจกต์" showBorderBottom>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างโปรเจกต์"
            borderRadius={'20px'}
            onClick={() => {
              setFormMode('create');
              setOpen(true);
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      <ModalCreateProject
        open={open}
        setOpen={setOpen}
        formMode={formMode}
        getProject={getProject}
        initialValues={initialValues}
      />
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบโปรเจกต์นี้?'}
        loadingConfirm={loading}
        onConfirm={async () => {
          await handleDelete();
        }}
      />
      <AppContentStyle>
        <ScrollBarStyled>
          <div className="flex justify-between items-center px-4 py-2">
            <div>{'2'} รายการ</div>
            <div className="flex gap-2 items-center">
              <Image
                className="cursor-pointer"
                src="/icons/icon-filter.svg"
                alt=""
                width={24}
                height={24}
              />
              <Divider />
              <SearchInput
                makeSearchValue={(newValue) => {
                  setFilters((prev: any) => ({
                    ...prev,
                    search: newValue,
                  }));
                }}
              />
            </div>
          </div>
        </ScrollBarStyled>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={188} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements || 0}
                  // pageSize={filters.sizes}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  loading={loadingRows}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={totalElements || 0}
                  handleChangeFilters={(newValues: any) =>
                    setFilters(newValues)
                  }
                />
              </div>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
    </>
  );
};

ProjectPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ProjectPage;
