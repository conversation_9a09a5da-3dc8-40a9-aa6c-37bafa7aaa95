import { Search } from '@mui/icons-material';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled, { css } from 'styled-components';
import ProductItem from '@/components/product/ProductItem';
import ProductNav from '@/components/product/ProductNav';
import { Button, Chip, CircularProgress } from '@mui/material';
import Image from 'next/image';
import apiProduct from '@/services/stock/product';
import Swal from 'sweetalert2';
import { useRouter } from 'next/router';

const ProductToolsStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  .tool-details {
    z-index: 50;
    position: relative;
    max-width: 100%;
    padding: 0 20px;
    h2 {
      font-size: 2em;
      color: white;
      margin-top: 0;
      @media screen and (max-width: 650px) {
        font-size: 1.8em;
      }
    }
    .search-input {
      display: flex;
      flex-direction: row;
      background: white;
      height: 48px;
      border-radius: 32px;
      width: 546px;
      max-width: 100%;
      > div {
        display: flex;
        svg {
          color: #cfd8dc;
        }
        &:nth-of-type(1) {
          width: 60px;
          align-items: center;
          justify-content: center;
        }
        &:nth-of-type(2) {
          padding-left: 0;
          flex: 1;
          align-items: center;
          justify-content: left;
        }
      }
      input {
        height: 40px;
        border: none;
        width: 90%;
        font-size: 1em;
        outline-width: 0;
        &::placeholder {
          color: #cfd8dc;
        }
      }
    }
  }
  .category {
    h3 {
      text-align: center;
      color: white;
      font-size: 1em;
      margin-top: 26px;
    }
    .chip-list {
      display: flex;
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      gap: 10px;
    }
  }
`;

const ProductListStyle = styled.div`
  padding: 40px;
  display: grid;
  grid-gap: 40px;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  @media screen and (max-width: 650px) {
    grid-gap: 10px;
    padding: 20px;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
`;

const ChipCustomStyle = styled(Chip)<{ $active: boolean }>`
  ${({ $active }) =>
    $active
      ? css`
          background: white;
          color: #30d5c7;
          border: 1px solid #ffffff00;
        `
      : css`
          background: none;
          color: white;
          border: 1px solid white;
        `}}
`;

const categoryMocks = [
  {
    id: 1,
    name: 'Packaging',
  },
  {
    id: 2,
    name: 'Mailers',
  },
  {
    id: 3,
    name: 'Bags',
  },
  {
    id: 4,
    name: 'Toys',
  },
  {
    id: 5,
    name: 'Box',
  },
];

const TemplateProducts = () => {
  const router = useRouter();
  const [rows, setRows] = useState<any[]>([]);
  const [productSelectedIds, setProductSelectedIds] = useState<number[]>([]);
  const [searchInput, setSearchInput] = useState('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    categoryId: null,
  });

  useEffect(() => {
    getProductTemplate();
  }, []);
  const toggleCheckbox = (id: number) => {
    if (productSelectedIds.includes(id)) {
      setProductSelectedIds(productSelectedIds.filter((item) => item !== id));
    } else {
      setProductSelectedIds([...productSelectedIds, id]);
    }
  };

  const getProductTemplate = async () => {
    const res = await apiProduct.getProductTemplate(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
    }
  };

  useEffect(() => {
    getProductTemplate();
  }, [filters]);

  const importProducts = async () => {
    const res = await apiProduct.importProducts({
      productId: productSelectedIds,
    });
    if (res && !res.isError) {
      Swal.fire({
        title: 'นำเข้าข้อมูลเรียบร้อย',
        text: 'นำเข้าข้อมูลสินค้าเรียบร้อยแล้ว',
        icon: 'success',
      }).then(() => {
        router.push('/product');
      });
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถนำเข้าข้อมูลสินค้าได้ กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };
  const handleSearch = (event: any) => {
    setSearchInput(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        searchName: event.target.value,
      });
    }, 2000);
    setTimer(newTimer);
  };

  return (
    <>
      <ProductNav title="HON Template" backUrl="/setting/product">
        <Button
          variant="contained"
          size="small"
          style={{ width: '140px' }}
          color="dark"
          onClick={() => importProducts()}
        >
          Import ({productSelectedIds.length})
        </Button>
      </ProductNav>
      <ProductToolsStyle>
        <Image
          src="/images/product/search-bg.png"
          alt=""
          fill
          quality={100}
          style={{ objectFit: 'cover' }}
        />
        <div className="tool-details">
          <h2 className="text-center">PICK THE PACKAGING TEMPLATES</h2>
          <div className="search-input">
            <div className="">
              {loadingSearch ? <CircularProgress size={25} /> : <Search />}
            </div>
            <div>
              <input
                placeholder="Search Product"
                value={searchInput}
                onChange={handleSearch}
              />
            </div>
          </div>
          <div className="category">
            <h3>CATEGORY</h3>
            <div className="chip-list">
              <ChipCustomStyle
                label="All Product"
                $active={`${filters.categoryId}` === `null`}
                onClick={() =>
                  setFilters({
                    ...filters,
                    categoryId: null,
                  })
                }
              />
              {categoryMocks.map((item) => (
                <ChipCustomStyle
                  key={item.id}
                  label={item.name}
                  $active={filters.categoryId === item.id}
                  onClick={() =>
                    setFilters({
                      ...filters,
                      categoryId: item.id,
                    })
                  }
                />
              ))}
            </div>
          </div>
        </div>
      </ProductToolsStyle>
      <ProductListStyle>
        {rows &&
          rows.map((item: any, index: number) => (
            <ProductItem
              key={index}
              data={item}
              handleClick={(value: number) => toggleCheckbox(value)}
              checked={productSelectedIds.includes(item.id)}
            />
          ))}
      </ProductListStyle>
    </>
  );
};

TemplateProducts.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default TemplateProducts;
