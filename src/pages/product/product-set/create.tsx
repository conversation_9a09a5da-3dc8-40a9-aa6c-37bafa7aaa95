import ProductNav from '@/components/product/ProductNav';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import InformationForm from '@/components/product-set/form/InformationForm';
import Swal from 'sweetalert2';
import { useRouter } from 'next/router';
import apiProductSet from '@/services/stock/product-set';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ProductCreatePageStyle = styled.div`
  position: relative;
  width: 100%;
  max-width: 100%;
  padding: 0 24px;
  display: flex;
  justify-content: center;
`;

const CreateProductSet = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [productSetDetail, setProductSetDetail] = useState<any>(null);
  useEffect(() => {
    fetchProductSetDetail();
  }, [id]);

  const fetchProductSetDetail = async () => {
    if (id) {
      const res = await apiProductSet.getProductBySetId(String(id));
      if (res.status) {
        setProductSetDetail(res.data);
      }
    }
  };
  const createProduct = async (values: any) => {
    const formData: any = new FormData();
    formData.append(
      'productSetRequest',
      JSON.stringify({
        name: values.name,
        description: values.description,
        urlSlug: values.urlSlug,
        productModelId: values.productModelId,
      })
    );
    formData.append('thumbnail', values.thumbnail);
    const res: any = await apiProductSet.createProductSet(formData);
    if (res.status === true) {
      Swal.fire('สำเร็จ', 'สร้างเซ็ตสินค้าเรียบร้อย', 'success').then(() => {
        router.push(`/product/product-set`);
      });
    } else {
      Swal.fire(
        'เกิดข้อผิดพลาด',
        'ไม่สามารถสร้างเซ็ตสินค้าได้ กรุณาลองใหม่อีกครั้ง',
        'error'
      );
    }
  };

  const updateProduct = async (values: any) => {
    const formData: any = new FormData();
    formData.append(
      'productSetRequest',
      JSON.stringify({
        name: values.name,
        description: values.description,
        urlSlug: values.urlSlug,
        productModelId: values.productModelId,
      })
    );
    formData.append('thumbnail', values.thumbnail);
    const res: any = await apiProductSet.updateProductSet(Number(id), formData);
    if (res.status === true) {
      Swal.fire('สำเร็จ', 'แก้ไขเซ็ตสินค้าเรียบร้อย', 'success').then(() => {
        router.push(`/product/product-set`);
      });
    } else {
      Swal.fire(
        'เกิดข้อผิดพลาด',
        'ไม่สามารถแก้ไขเซ็ตสินค้าได้ กรุณาลองใหม่อีกครั้ง',
        'error'
      );
    }
  };

  return (
    <>
      <ProductNav
        title={`${id ? 'แก้ไข' : 'เพิ่ม'}เซ็ตสินค้า`}
        showBorderBottom
        backUrl="/product/product-set"
      />
      {(!id && isAllowed(permissions, 'product.product-set.create')) ||
      (id && isAllowed(permissions, 'product.product-set.update')) ? (
        <ProductCreatePageStyle>
          <InformationForm
            defaultModel={productSetDetail?.productModel || []}
            initialValues={{
              name: productSetDetail?.name || '',
              description: productSetDetail?.description || '',
              thumbnail: productSetDetail?.thumbnail || '',
              productModelId:
                productSetDetail?.productModel.map((md: any) => md.id) || [],
            }}
            handleSubmit={(values: any) =>
              id ? updateProduct(values) : createProduct(values)
            }
          />
        </ProductCreatePageStyle>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

CreateProductSet.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default CreateProductSet;
