import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import apiProductSet from '@/services/stock/product-set';
import SearchInput from '@/components/SearchInput';
import Swal from 'sweetalert2';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import Image from 'next/image';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { IconButton } from '@mui/material';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ProductSetPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [productSetSelect, setProductSetSelect] = useState<any>(null);
  const [filters, setFilters] = useState<{
    size: number;
    page: number;
    searchName?: string | null;
    searchStatus?: boolean | null;
  }>({
    size: 10,
    page: 0,
    searchName: null,
    searchStatus: null,
  });

  useEffect(() => {
    setFilters({ ...filters, searchName: '' });
  }, []);

  const [productSetList, setProductSetList] = useState<any>({
    content: [],
    totalElements: null,
  });

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'รายการสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 324,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <>
            <div
              className="flex items-center justify-center"
              style={{
                columnGap: '14px',
                maxWidth: '100%',
              }}
            >
              <Image
                src={
                  params.row.thumbnail || '/images/product/empty-product.svg'
                }
                width={40}
                height={40}
                alt=""
                style={{
                  borderRadius: '4px',
                  objectFit: 'cover',
                  minWidth: '40px',
                }}
              />
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  lineHeight: '1',
                }}
              >
                {params.row.name}
              </div>
            </div>
          </>
        );
      },
    },
    {
      field: 'countModel',
      headerName: 'โมเดล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.countModel || '-'}</div>;
      },
    },
    {
      field: 'description',
      headerName: 'Description',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 252,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'active',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'center',
      align: 'center',
      minWidth: 152,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{
                opacity: isAllowed(permissions, 'product.product-set.update')
                  ? 1
                  : 0.2,
              }}
              checked={params.row.isActive}
              onClick={(e: any) => {
                if (isAllowed(permissions, 'product.product-set.update')) {
                  handleStatus(params.row.id, e.target.checked);
                }
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: 'h',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 200,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <IconButton
              disabled={!isAllowed(permissions, 'product.product-set.update')}
              onClick={() =>
                router.push(`/product/product-set/create?id=${params.id}`)
              }
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              disabled={!isAllowed(permissions, 'product.product-set.delete')}
              onClick={() => {
                setProductSetSelect(params.row);
                setOpenDeleteModal(true);
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </div>
        );
      },
    },
  ];

  const handleStatus = async (id: any, checked: boolean) => {
    Swal.fire({
      title: `${checked ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}`,
      text: 'ยืนยันการเปลี่ยนสถานะ ?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const response = await apiProductSet.updateStatus(id, checked);
        if (response.status) {
          Swal.fire('สำเร็จ', 'เปลี่ยนสถานะเรียบร้อย', 'success').then(() => {
            getProductSetList();
          });
        } else {
          getProductSetList();
        }
      } else {
        getProductSetList();
      }
    });
  };

  const getProductSetList = async () => {
    const res = await apiProductSet.getProductSetList(filters);
    if (!res.isError) {
      setProductSetList(res.data);
    }
  };
  useEffect(() => {
    getProductSetList();
  }, [filters]);
  const handleSearch = (searchName: string) => {
    setFilters({ ...filters, searchName: searchName, page: 0 });
  };

  function onCloseAction() {
    setOpenDeleteModal(false);
    setProductSetSelect(null);
  }

  async function onDelete() {
    setLoading(true);
    const res = await apiProductSet.removeProductSet(productSetSelect.id);
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
      await getProductSetList();
    }
    onCloseAction();
    setLoading(false);
  }

  return (
    <>
      <ProductNav title="เซ็ตสินค้า" showBorderBottom={false}>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="เพิ่มเซ็ตสินค้า"
            borderRadius={'20px'}
            onClick={() => {
              router.push('/product/product-set/create');
            }}
            disabled={!isAllowed(permissions, 'product.product-set.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppModalConfirm
        open={openDeleteModal}
        onClickClose={() => {
          onCloseAction();
        }}
        confirmTitle={`ลบเซ็ตสินค้า`}
        confirmDescription={`คุณต้องการที่จะลบเซ็ตสินค้า ${
          productSetSelect?.name || ''
        }`}
        loadingConfirm={loading}
        onConfirm={() => {
          onDelete();
        }}
      />
      {isAllowed(permissions, 'product.product-set.list') && (
        <>
          {productSetList.totalElements !== null ? (
            <AppContentStyle>
              <div className="content-wrap">
                <AppTableStyle $isAvatar={true} $rows={productSetList?.content}>
                  <div className="content-wrap">
                    <ScrollBarStyled>
                      <HeaderColumnAction text="จัดการ" width={108} />
                      <DataGrid
                        hideFooter={true}
                        rows={
                          productSetList?.content ? productSetList.content : []
                        }
                        columns={columns}
                        paginationMode="server"
                        rowCount={productSetList?.totalElements || 0}
                        // pageSize={filters.sizes}
                        disableSelectionOnClick={false}
                        autoHeight={true}
                        sortModel={[]}
                        getRowHeight={() => 56}
                        headerHeight={48}
                        components={{
                          NoRowsOverlay: () => <TableNoRowsOverlay />,
                          LoadingOverlay: () => <TableLoadingOverlay />,
                        }}
                      />
                    </ScrollBarStyled>
                    <div className="px-[16px]">
                      <AppPagination
                        filters={filters}
                        totalElements={
                          productSetList?.totalElements
                            ? productSetList.totalElements
                            : 0
                        }
                        handleChangeFilters={(newValues: FiltersType) => {
                          setFilters(newValues);
                        }}
                      />
                    </div>
                  </div>
                </AppTableStyle>
              </div>
            </AppContentStyle>
          ) : (
            <div
              className={'flex flex-col items-center h-[600px] justify-center'}
            >
              <div className={'font-bold text-[18px] text-gray-400'}>
                ไม่มีเซ็ตสินค้าในรายการ
              </div>
              <div className={'text-[14px] text-gray-200'}>
                สร้างเซ็ตสินค้าด้วยการนำเข้าข้อมูลโมเดลจากสินค้า
              </div>
            </div>
          )}
        </>
      )}
      {!isAllowed(permissions, 'product.product-set.list') && <NotPermission />}
    </>
  );
};
ProductSetPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default ProductSetPage;
