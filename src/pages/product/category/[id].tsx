import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import apiProduct from '@/services/stock/product';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { numberWithCommas } from '@/utils/number';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import Image from 'next/image';
import { OpenCreateType, OpenDeleteType } from '@/types/category';
import { RowsType } from '@/types/type';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const initialModalValue = {
  id: null,
  productCategoryId: null,
  name: '',
};
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
});
const TypeList = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  // const { snackBar } = useAppSelector(alertSelector);
  const { id } = router.query;
  const [rows, setRows] = useState<RowsType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [category, setCategory] = useState<any>({});
  const [totalElements, setTotalElements] = useState<number>(0);
  const [openCreate, setOpenCreate] = useState<OpenCreateType>({
    status: false,
    type: '',
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const {
    register,
    handleSubmit,
    reset,
    // watch,
    setValue,
    // setError,
    // clearErrors,
    formState: {
      errors: hookFormErrors,
      // isSubmitted
    },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialModalValue,
  });

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ประเภทสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <>
            <div
              className="flex items-center justify-center"
              style={{
                columnGap: '14px',
                maxWidth: '100%',
              }}
            >
              <Image
                src={params.row.imageUrl || '/images/product/empty-product.svg'}
                width={40}
                height={40}
                alt=""
                style={{
                  borderRadius: '4px',
                  objectFit: 'cover',
                  minWidth: '40px',
                }}
              />
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  lineHeight: '1',
                }}
              >
                {params.row.name}
              </div>
            </div>
          </>
        );
      },
    },
    {
      field: 'countProduct',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 80,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.countProduct);
      },
    },
    {
      field: 'status',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 90,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{
                m: 1,
                opacity: isAllowed(permissions, 'product.category.update')
                  ? 1
                  : 0.2,
              }}
              checked={params.row.isActive}
              onClick={async (e: any) => {
                await handleChangeStatus(params.row.id, e.target.checked);
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: 'any',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 88,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <IconButton
              disabled={!isAllowed(permissions, 'product.category.update')}
              onClick={async () => {
                await getTypeById(params.row.id);
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              onClick={async () => {
                await handleDeleteData(params.row.name, params.row.id);
              }}
              disabled={
                params.row.countProduct !== 0 ||
                !isAllowed(permissions, 'product.category.delete')
              }
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </>
        );
      },
    },
  ];
  const getTypeById = async (id: number) => {
    const res = await apiProduct.getTypeById(id);
    if (!res.isError) {
      reset();
      setValue('id', res.data.id);
      setValue('name', res.data.name);
      setOpenCreate({
        status: true,
        type: 'edit',
      });
    }
  };

  const getCategoryById = async () => {
    const res = await apiProduct.getCategoryById(id as string);
    if (!res.isError) {
      setCategory(res.data);
    }
  };

  const getType = async () => {
    const res = await apiProduct.getTypeByCategoryId(id as string);
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setRows(res.data);
        setTotalElements(res.data.totalElements);
      } else {
        setRows([]);
      }
    }
  };

  useEffect(() => {
    getCategoryById();
  }, []);

  useEffect(() => {
    getType();
  }, []);

  const onSubmitModal = async (values: any, type: string) => {
    setLoading(true);
    if (type === 'create') {
      const body = {
        ...values,
        productCategoryId: id,
      };
      const res = await apiProduct.addType(body);
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        setOpenCreate({
          ...openCreate,
          status: false,
        });
        await getType();
      }
      // }
    } else if (type === 'edit') {
      const body = {
        ...values,
        productCategoryId: id,
      };
      const res = await apiProduct.updateType(body);
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        setOpenCreate({
          ...openCreate,
          status: false,
        });
        await getType();
      }
    }
    setLoading(false);
  };

  const onClose = () => {
    setOpenCreate({
      ...openCreate,
      status: false,
    });
  };

  const handleDeleteData = async (name: string, id: number) => {
    setOpenDelete({
      name,
      status: true,
      id,
    });
  };

  const handleDeleteType = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiProduct.deleteType(openDelete.id);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
        await getType();
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };

  const onOpen = () => {
    setOpenCreate({
      status: true,
      type: 'create',
    });
    reset();
  };

  const handleChangeStatus = async (id: number, check: boolean) => {
    const res = await apiProduct.changeStatusType(id, check);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
      await getType();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'error',
        })
      );
    }
  };

  return (
    <>
      {category && (
        <>
          <ProductNav
            title={category.name}
            backUrl="/product/category"
            showBorderBottom
          >
            <ActionGroupStyle>
              <ActionButton
                variant="outlined"
                color="blueGrey"
                icon={<AddCircle />}
                text="เพิ่มประเภท"
                borderRadius={'20px'}
                onClick={() => {
                  onOpen();
                }}
                disabled={!isAllowed(permissions, 'product.category.create')}
              />
            </ActionGroupStyle>
          </ProductNav>
          <AppContentStyle>
            {isAllowed(permissions, 'product.category.list') ? (
              <div className="content-wrap">
                <AppTableStyle $rows={rows}>
                  <div className="content-wrap">
                    <ScrollBarStyled>
                      <HeaderColumnAction text="จัดการ" width={88} />
                      <DataGrid
                        hideFooter={true}
                        rows={!isEmpty(rows) ? rows : []}
                        columns={columns}
                        paginationMode="server"
                        rowCount={totalElements || 0}
                        disableSelectionOnClick={false}
                        autoHeight={true}
                        sortModel={[]}
                        getRowHeight={() => 56}
                        headerHeight={48}
                        components={{
                          NoRowsOverlay: () => <TableNoRowsOverlay />,
                          LoadingOverlay: () => <TableLoadingOverlay />,
                        }}
                      />
                    </ScrollBarStyled>
                  </div>
                </AppTableStyle>
              </div>
            ) : (
              <NotPermission />
            )}
          </AppContentStyle>
        </>
      )}
      <Dialog open={openCreate.status}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {openCreate.type === 'create'
                    ? 'เพิ่มประเภทสินค้า'
                    : 'แก้ไขประเภทสินค้า'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit((value) =>
                    onSubmitModal(value, openCreate.type)
                  )}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <p>ชื่อประเภท</p>
                  <TextField
                    placeholder="ชื่อประเภท"
                    {...register('name')}
                    error={Boolean(hookFormErrors.name)}
                    helperText={hookFormErrors.name?.message as ReactNode}
                  />
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={() => {
                        onClose();
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <LoadingButton
                      type="submit"
                      loading={loading}
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      บันทึก
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบข้อมูลประเภทของหมวดหมู่สินค้า?'}
        loadingConfirm={loading}
        onConfirm={() => {
          handleDeleteType();
        }}
      />
    </>
  );
};
TypeList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default TypeList;
