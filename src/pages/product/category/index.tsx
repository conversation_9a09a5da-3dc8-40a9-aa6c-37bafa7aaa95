import React, {
  ReactElement,
  ReactNode,
  useCallback,
  useEffect,
  useState,
} from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import {
  Button,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import apiProduct from '@/services/stock/product';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { isEmpty } from 'lodash';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import { useRouter } from 'next/router';
import { numberWithCommas } from '@/utils/number';
import AppPagination from '@/components/global/AppPagination';
import { FiltersType } from '@/types/app';
import {
  CategoryRowsType,
  OpenCreateType,
  OpenDeleteType,
} from '@/types/category';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { useDeepEffect } from '@/hooks/useDeepEffect';
import { yupResolver } from '@hookform/resolvers/yup';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const initialModalValue = {
  id: null,
  name: '',
};

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
});

const Index = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const router = useRouter();
  const [rows, setRows] = useState<CategoryRowsType[]>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [category, setCategory] = useState<any>([]);
  const [openCreate, setOpenCreate] = useState<OpenCreateType>({
    status: false,
    type: '',
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });

  const {
    register,
    handleSubmit,
    reset,
    // watch,
    // setValue,
    // setError,
    // clearErrors,
    formState: {
      errors: hookFormErrors,
      // isSubmitted
    },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialModalValue,
  });

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'หมวดหมู่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 314,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'countProductType',
      headerName: 'จำนวนประเภท',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.countProductType);
      },
    },
    {
      field: 'status',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 90,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{
                m: 1,
                opacity: isAllowed(permissions, 'product.category.update')
                  ? 1
                  : 0.2,
              }}
              checked={params.row.isActive}
              onClick={(e: any) => {
                if (isAllowed(permissions, 'product.category.update')) {
                  handleChangeStatus(params.row.id, e.target.checked);
                }
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 208,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                color: '#90A4AE',
                width: '98px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              disabled={
                !isAllowed(permissions, 'product.category.create') ||
                !isAllowed(permissions, 'product.category.update')
              }
              onClick={() => {
                router.push(`/product/category/${params.row.id}`);
              }}
            >
              ประเภทสินค้า
            </Button>
            <IconButton
              disabled={!isAllowed(permissions, 'product.category.update')}
              onClick={() => {
                getCategoryById(params.row.id);
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              onClick={() => {
                handleDeleteData(params.row.name, params.row.id);
              }}
              disabled={
                params.row.countProductType !== 0 ||
                !isAllowed(permissions, 'product.category.delete')
              }
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </>
        );
      },
    },
  ];

  const getCategory = useCallback(async () => {
    const res = await apiProduct.getCategory(filters);
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setCategory(res.data);
        if (!isEmpty(res.data.content)) {
          setRows(res.data.content);
          setTotalElements(res.data.totalElements);
        }
      } else {
        setCategory([]);
        setRows([]);
      }
    }
  }, [filters]);

  const getCategoryById = async (id: string) => {
    const res = await apiProduct.getCategoryById(id);
    if (!res.isError) {
      handleSetEditData(res.data);
    }
  };

  async function fetchData() {
    await getCategory();
  }

  useDeepEffect(() => {
    fetchData().then();
  }, [filters]);

  const onSubmitModal = async (values: any) => {
    setLoading(true);
    if (openCreate.type === 'create') {
      const res = await apiProduct.addCategory(values);
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message}`,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getCategory();
        setOpenCreate({
          ...openCreate,
          status: false,
        });
      }
    } else if (openCreate.type === 'edit') {
      const res = await apiProduct.updateCategory(values);
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message}`,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getCategory();
        setOpenCreate({
          ...openCreate,
          status: false,
        });
      }
    }
    setLoading(false);
  };

  const onClose = () => {
    setOpenCreate({
      ...openCreate,
      status: false,
    });
  };

  const handleDeleteData = async (name: string, id: number) => {
    setOpenDelete({
      name,
      status: true,
      id,
    });
  };

  const handleDeleteCategory = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiProduct.deleteCategory(openDelete.id);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
        await fetchData();
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };

  const handleSearch = (searchTerm: string) => {
    setFilters({ ...filters, searchTerm, page: 0 });
  };

  const handleSetEditData = (val: any) => {
    reset(val);
    setOpenCreate({
      status: true,
      type: 'edit',
    });
  };

  const onOpen = () => {
    setOpenCreate({
      status: true,
      type: 'create',
    });
    reset(initialModalValue);
  };
  const handleChangeStatus = async (id: number, check: boolean) => {
    const res = await apiProduct.changeStatusCategory(id, check);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `เปลี่ยนสถานะสำเร็จ`,
          severity: 'success',
        })
      );
      await fetchData();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `เปลี่ยนสถานะไม่สำเร็จ`,
          severity: 'error',
        })
      );
    }
  };
  return (
    <>
      <ProductNav title="หมวดหมู่สินค้า">
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างหมวดหมู่"
            borderRadius={'20px'}
            onClick={() => {
              onOpen();
            }}
            disabled={!isAllowed(permissions, 'product.category.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'product.category.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={208} />
                  <DataGrid
                    hideFooter={true}
                    rows={!isEmpty(rows) ? rows : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements || 0}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={
                      category?.totalElements ? category.totalElements : 0
                    }
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>

      <Dialog open={openCreate.status}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {openCreate.type === 'create'
                    ? 'เพิ่มหมวดหมู่สินค้า'
                    : 'แก้ไขหมวดหมู่สินค้า'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit(onSubmitModal)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p>ชื่อหมวดหมู่</p>
                    <TextField
                      placeholder="ชื่อประเภท"
                      {...register('name')}
                      error={Boolean(hookFormErrors.name)}
                      helperText={
                        hookFormErrors.name
                          ? (hookFormErrors.name.message as ReactNode)
                          : ''
                      }
                    />
                  </div>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={() => {
                        onClose();
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <LoadingButton
                      type="submit"
                      loading={loading}
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      บันทึก
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบข้อมูลหมวดหมู่สินค้า?'}
        loadingConfirm={loading}
        onConfirm={async () => {
          await handleDeleteCategory();
        }}
      />
    </>
  );
};

Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Index;
