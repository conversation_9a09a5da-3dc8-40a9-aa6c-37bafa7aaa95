import React, { ReactElement, useCallback, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';

import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import ProductNav from '@/components/product/ProductNav';
import apiProduct from '@/services/stock/product';
import SearchInput from '@/components/SearchInput';
import AppPagination from '@/components/global/AppPagination';
import {
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import Image from 'next/image';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import ActionButton from '@/components/ActionButton';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useDeepEffect } from '@/hooks/useDeepEffect';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import SvgSettingIcon from '@/components/svg-icon/SvgSettingIcon';

const ProductPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState<any[]>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
  });
  const [openConfirmDelete, setOpenConfirmDelete] = useState<any>({
    open: false,
    title: '',
    description: '',
    id: null,
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const getProducts = useCallback(async () => {
    const res = await apiProduct.getProductList(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalElements(res.data.totalElements);
    }
  }, [filters]);

  useDeepEffect(() => {
    getProducts().then();
  }, [filters]);

  const updateProductStatus = async (productId: string, status: boolean) => {
    const res = await apiProduct.updateStatus(productId, {
      isActive: status,
    });
    if (res && !res.isError) {
      await getProducts();
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'รายการสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={params.row.imageUrl || '/images/product/empty-product.svg'}
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '4px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'productCategoryName',
      headerName: 'หมวดหมู่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'productTypeName',
      headerName: 'ประเภท',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },

    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 224,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.description || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'isAction',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 88,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{
                m: 1,
                opacity: isAllowed(permissions, 'product.list-product.update')
                  ? 1
                  : 0.2,
              }}
              checked={params.row.isActive}
              onClick={async (e: any) => {
                if (isAllowed(permissions, 'product.list-product.update')) {
                  await updateProductStatus(params.row.id, e.target.checked);
                }
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 88,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            {/* <KebabTable */}
            {/*  item={params.row} */}
            {/*  handleRemove={async (item: any) => { */}
            {/*    setOpenConfirmDelete({ */}
            {/*      id: item.id, */}
            {/*      open: true, */}
            {/*      title: 'ยืนยันลบสินค้า', */}
            {/*      description: `คุณต้องการลบ ${item.name} ใช่หรือไม่`, */}
            {/*    }); */}
            {/*  }} */}
            {/*  isEdit={{ */}
            {/*    status: true, */}
            {/*    url: `/product/${params.row.id}`, */}
            {/*  }} */}
            {/*  isProductConfig={true} */}
            {/*  isRemove={true} */}
            {/* /> */}
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'product.list-product.update'
                  ),
                  IconElement: () => <SvgPencilIcon />,
                  title: 'แก้ไข',
                  onAction: () => {
                    router.push(`/product/${params.row.id}`);
                  },
                },
                {
                  disabled: !isAllowed(
                    permissions,
                    'product.list-product.update'
                  ),
                  IconElement: () => <SvgSettingIcon />,
                  title: 'ตั้งค่า',
                  onAction: () => {
                    router.push(`/product/${params.row.id}/setting`);
                  },
                },
                {
                  disabled: !isAllowed(
                    permissions,
                    'product.list-product.delete'
                  ),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    setOpenConfirmDelete({
                      id: params.row.id,
                      open: true,
                      title: 'ยืนยันลบสินค้า',
                      description: `คุณต้องการลบ ${params.row.name} ใช่หรือไม่`,
                    });
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const handleConfirmDelete = async () => {
    setLoadingConfirm(true);
    const res = await apiProduct.remove(openConfirmDelete.id);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLoadingConfirm(false);
    setOpenConfirmDelete({
      ...openConfirmDelete,
      open: false,
    });
    if (res && !res.isError) {
      await getProducts();
    }
  };
  return (
    <>
      <AppModalConfirm
        open={openConfirmDelete.open}
        onClickClose={() => {
          setOpenConfirmDelete({
            ...openConfirmDelete,
            open: false,
          });
        }}
        confirmTitle={openConfirmDelete.title}
        confirmDescription={openConfirmDelete.description}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleConfirmDelete();
        }}
        icon={
          <Image
            src={'/icons/icon-trash-red.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="350px"
        bgIcon="#FDE8EF"
        isDelete={true}
        confirmText={'ยืนยันลบ'}
        cancelText={'ไว้ภายหลัง'}
      />
      <ProductNav title="สินค้า">
        <div className="flex flex-row gap-2">
          <SearchInput
            makeSearchValue={(val: string) =>
              setFilters({
                ...filters,
                searchName: val,
                page: 0,
              })
            }
          />

          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างสินค้า"
            borderRadius={'20px'}
            onClick={async () => {
              await router.push('/product/create');
            }}
            disabled={!isAllowed(permissions, 'product.list-product.create')}
          />
        </div>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          {isAllowed(permissions, 'product.list-product.list') ? (
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={88} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    // checkboxSelection={true}
                    rowCount={totalElements || 0}
                    // pageSize={filters.size}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          ) : (
            <NotPermission />
          )}
        </div>
      </AppContentStyle>
    </>
  );
};

ProductPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default ProductPage;
