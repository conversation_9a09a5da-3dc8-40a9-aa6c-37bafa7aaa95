import ProductNav from '@/components/product/ProductNav';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import InformationForm from '@/components/product/form/InformationForm';
import apiProduct from '@/services/stock/product';
import { useRouter } from 'next/router';
import { LoadingFadein } from '@/styles/share.styled';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

export const ProductFormStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  animation: ${LoadingFadein} 0.3s ease-in;
  width: 100%;
  margin-top: 48px;
  button {
    svg {
      width: 20px;
      height: 20px;
    }
  }
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 640px;
    padding: 0 24px;
    max-width: 100%;
  }
  .field-title {
    margin: 24px 0 8px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 24px;
    align-items: center;
  }
  .fade {
    animation: ${LoadingFadein} 0.3s ease-in;
  }
`;
const CreateProduct = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [openConfirm, setOpenConfirm] = useState<any>({
    title: 'สร้างสินค้าสำเร็จ',
    description:
      'หลังจากที่คุณสร้างสินค้าใหม่เรียบร้อยแล้ว คุณจะต้องเข้าไปปรับแต่งสินค้า เพิ่มข้อมูลคุณลักษณะของสินค้า และ ตั้งค่าข้อมูลการผลิตให้ครบถ้วน',
    open: false,
    id: null,
  });
  const createProduct = async (values: any, imageFile: File) => {
    setSubmitting(true);
    const res = await apiProduct.create(values);
    if (imageFile && !res.isError) {
      const formData = new FormData();
      formData.append('productId', res.data.id as string);
      formData.append('file', imageFile);
      await apiProduct.uploadImageProduct(formData);
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
    if (res && !res.isError) {
      setOpenConfirm({
        ...openConfirm,
        id: res.data.id,
        open: true,
      });
    }
  };
  return (
    <>
      <AppModalConfirm
        open={openConfirm.open}
        isReason={false}
        onClickClose={async () => {
          setOpenConfirm({
            ...openConfirm,
            open: false,
          });
          await router.push('/product');
        }}
        icon={
          <CheckRoundedIcon
            sx={{
              fontSize: '48px',
              color: '#8BC34A',
            }}
          />
        }
        bgIcon={'#E6F8CF'}
        confirmTitle={openConfirm.title}
        confirmDescription={openConfirm.description}
        loadingConfirm={false}
        onConfirm={async () => {
          await router.push(`/product/${openConfirm.id}/setting`);
        }}
        maxWidth={'412px'}
        confirmText="ปรับแต่งสินค้า"
        cancelText="ไว้ภายหลัง"
      />
      <ProductNav title="สร้างสินค้า" showBorderBottom backUrl="/product" />
      {isAllowed(permissions, 'product.list-product.create') ? (
        <ProductFormStyle>
          <InformationForm
            handleSubmit={(values: any, imageFile: File) =>
              createProduct(values, imageFile)
            }
            submitting={submitting}
          />
        </ProductFormStyle>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

CreateProduct.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default CreateProduct;
