import React, { ReactElement, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import MainLayout from '@/layouts/MainLayout';
import ProductSettingHeader from '@/components/product/ProductSettingHeader';
import ProductNav from '@/components/product/ProductNav';
import ProductAttributes from '@/components/product/ProductAttributes';
import { LoadingFadein } from '@/styles/share.styled';
import { useRouter } from 'next/router';
import apiProduct from '@/services/stock/product';
import { ProductByIdType } from '@/types/product';
import { isEmpty } from 'lodash';
import { setProductAttributes } from '@/store/features/product/attributes';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const SettingProductStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  animation: ${LoadingFadein} 0.3s ease-in;
  height: calc(100dvh - 64px);
  overflow: auto;
  .border-bar {
    border-top: 1px solid #dbe2e5;
    background: #f5f7f8;
    min-height: 8px;
  }
`;
const SettingProductPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const refSettingProduct = useRef<any>(null);
  const [productData, setProductData] = useState<ProductByIdType>({
    code: '',
    componentId: 0,
    componentName: '',
    description: '',
    id: 0,
    imageUrl: '',
    isActive: false,
    name: '',
    productCategoryId: 0,
    productCategoryName: '',
    productSize: {
      id: 0,
      maxHeight: 0,
      maxLength: 0,
      maxWidth: 0,
      minHeight: 0,
      minLength: 0,
      minWidth: 0,
    },
    productTypeId: 0,
    productTypeName: '',
    urlSlug: '',
  });
  const getProductData = async () => {
    const { id } = router.query;
    const res = await apiProduct.getProductById(id as string);
    if (res && !res.isError) {
      setProductData(res.data);
    }
  };

  useEffect(() => {
    if (id) {
      getProductData().then();
    }
  }, [id]);

  useEffect(() => {
    return () => {
      dispatch(setProductAttributes([]));
    };
  }, []);

  return (
    <>
      <ProductNav
        title={productData.code ?? ''}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/product'}
        animate={true}
      />
      {!isEmpty(productData) &&
        isAllowed(permissions, 'product.list-product.update') && (
          <SettingProductStyle ref={refSettingProduct}>
            <ProductSettingHeader productData={productData} />
            <div className="border-bar" />
            <ProductAttributes refSettingProduct={refSettingProduct} />
          </SettingProductStyle>
        )}
      {!isAllowed(permissions, 'product.list-product.update') && (
        <NotPermission />
      )}
    </>
  );
};

SettingProductPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default SettingProductPage;
