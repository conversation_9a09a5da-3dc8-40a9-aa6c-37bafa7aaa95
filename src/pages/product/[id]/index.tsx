import ProductNav from '@/components/product/ProductNav';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import InformationForm from '@/components/product/form/InformationForm';
import apiProduct from '@/services/stock/product';
import { useRouter } from 'next/router';
import { ProductFormStyle } from '@/pages/product/create';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ProductCreatePageStyle = styled.div`
  position: relative;
  width: 100%;
`;

const EditProductPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  useEffect(() => {
    if (router.query.id) {
      getProductInfo().then();
    }
  }, [router]);
  const getProductInfo = async () => {
    setLoading(true);
    const { id } = router.query;
    const res = await apiProduct.getProductById(id as string);
    if (res && !res.isError) {
      setData({
        ...res.data,
        displayUnit: 'mm',
      });
    }
    setLoading(false);
  };

  const updateProductInfo = async (values: any, imageFile: File) => {
    setSubmitting(true);
    const { id } = router.query;
    const res = await apiProduct.update({
      ...values,
      id,
    });
    if (imageFile) {
      const formData = new FormData();
      formData.append('productId', id as string);
      formData.append('file', imageFile);
      await apiProduct.uploadImageProduct(formData);
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
    if (res && !res.isError) {
      await router.push('/product');
    }
  };
  return (
    <>
      <ProductCreatePageStyle>
        {data && (
          <>
            <ProductNav
              title="แก้ไขสินค้า"
              showBorderBottom
              backUrl="/product"
            />
          </>
        )}
        {isAllowed(permissions, 'product.list-product.update') ? (
          <ProductFormStyle>
            {!loading && (
              <InformationForm
                initialValues={data}
                handleSubmit={(values: any, imageFile: File) =>
                  updateProductInfo(values, imageFile)
                }
                submitting={submitting}
              />
            )}
          </ProductFormStyle>
        ) : (
          <NotPermission />
        )}
      </ProductCreatePageStyle>
    </>
  );
};

EditProductPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default EditProductPage;
