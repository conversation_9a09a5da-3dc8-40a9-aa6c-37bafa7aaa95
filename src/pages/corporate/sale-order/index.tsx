import React, { ReactElement, useEffect, useState } from 'react';
import Nav from '@/components/Nav';
import MainLayout from '@/layouts/MainLayout';
import SearchInput from '@/components/SearchInput';
import ButtonResponsive from '@/components/global/ButtonResponsive';
import { AddCircle, Close, Search } from '@mui/icons-material';
import StatusTab from '@/components/sale-order/StatusTab';
import styled from 'styled-components';
import AppPagination from '@/components/global/AppPagination';
import OrderItemCard from '@/components/sale-order/OrderItemCard';
import {
  Avatar,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { DialogHeaderStyle } from '@/styles/dialog.styled';

const OrderListStyle = styled.div`
  padding: 40px;
  display: grid;
  grid-gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  min-height: calc(100vh - 220px);
  > div {
    height: fit-content;
  }
`;

const ContactListStyle = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  min-height: 300px;
  .contact-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    border-radius: 16px;
    padding: 10px;
    transition: 0.3s;
    &:hover {
      background: #f5f7f8;
    }
    h4 {
      margin: 0;
    }
    p {
      font-size: 0.8em;
      margin: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      font-weight: 400;
    }
  }
`;

const mockRes = [
  {
    id: 1,
    orderNumber: 'LD00001',
    createdDate: 1698908518,
    orderItem: [
      {
        id: 1,
        imageUrl: null,
        orderItemId: 'SWO0001',
        name: 'Mailer Bos Standard',
        orderItemStatus: 'Layouting',
      },
      {
        id: 2,
        imageUrl: null,
        orderItemId: 'SWO0002',
        name: 'Mailer Bos Standard',
        orderItemStatus: 'Layouting',
      },
    ],
    contact: {
      id: 1,
      name: 'Taylor Swift',
      imageUrl: null,
      tel: '09000000000',
    },
  },
];
const SaleOrderList = () => {
  const [rows, setRows] = useState<any>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 20,
    orderStatus: 'PREPARING',
    searchName: '',
  });
  const [isShowContactModal, setShowContactModal] = useState(false);

  useEffect(() => {
    getOrderList();
  }, [filters]);

  const getOrderList = async () => {
    setRows(mockRes);
    setTotalElements(100);
  };

  return (
    <>
      <>
        <Nav title="รายการขาย">
          <div className="flex flex-row gap-2">
            <SearchInput
              makeSearchValue={(val) =>
                setFilters({
                  ...filters,
                  searchName: val,
                  page: 0,
                })
              }
            />
            <ButtonResponsive
              icon={<AddCircle />}
              text="สร้างใหม่"
              handleClick={() => setShowContactModal(true)}
            />
          </div>
        </Nav>
        <div>
          <StatusTab
            activeStatus={filters.orderStatus}
            handleClick={(orderStatus) =>
              setFilters({
                ...filters,
                orderStatus,
              })
            }
          />
        </div>
        <OrderListStyle>
          {rows.map((item: any, index: number) => (
            <OrderItemCard data={item} key={index} />
          ))}
        </OrderListStyle>
        <div className="p-4">
          <AppPagination
            filters={filters}
            totalElements={totalElements}
            handleChangeFilters={(newValues: any) => setFilters(newValues)}
          />
        </div>
      </>
      <Dialog
        open={isShowContactModal}
        onClose={() => setShowContactModal(false)}
      >
        <DialogHeaderStyle>
          <div className="flex flex-row items-center justify-between">
            <h2 className="m-0 my-2">สร้างรายการขาย</h2>
            <IconButton onClick={() => setShowContactModal(false)}>
              <Close />
            </IconButton>
          </div>
        </DialogHeaderStyle>
        <DialogContent style={{ width: '500px' }}>
          <div className="py-4">
            <p>โปรดเลือกลูกค้า</p>
            <TextField
              fullWidth
              placeholder="ค้นหา ชื่อลูกค้า ชื่อบริษัท หรือ เบอร์โทร ..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </div>
          <ContactListStyle>
            <div className="contact-item">
              <Avatar />
              <div className="">
                <h4>ทนงทวย คงควรคอย</h4>
                <p>
                  <span>บุคคลธรรมดา</span>
                  <span>0987776666</span>
                  <span>note</span>
                </p>
              </div>
            </div>
          </ContactListStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

SaleOrderList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default SaleOrderList;
