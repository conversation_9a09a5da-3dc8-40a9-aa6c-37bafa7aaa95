import { AddCircle } from '@mui/icons-material';
import React, { ReactElement, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Avatar,
  Badge,
  Button,
  Divider,
  MenuItem,
  Select,
  Tab,
  Tabs,
} from '@mui/material';
import SearchInput from '@/components/SearchInput';
import { GetServerSideProps } from 'next';
import AppDateRange from '@/components/global/AppDateRange';
import { useRouter } from 'next/router';
import { quotationStatus } from '@/utils/quotationStatus';
import ActionButton from '@/components/ActionButton';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { isEmpty } from 'lodash';
import AppPagination from '@/components/global/AppPagination';
import { FiltersType } from '@/types/app';
import { numberWithCommas } from '@/utils/number';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';

type LayoutDataListPageProps = {
  status: string;
};
const Quotation = ({ status }: LayoutDataListPageProps) => {
  const router = useRouter();
  const [quotationList] = useState<any>({
    content: [
      {
        id: 1,
        code: 'QT-2023001',
        customer: 'อัมพร จันทร์กระจ่าง',
        numberOfItems: 2,
        sell: 49000,
        seller: 'อัมพร จันทร์กระจ่าง',
        createdDate: '20 ธ.ค. 2566, 15:34 น.',
      },
      {
        id: 2,
        code: 'QT-2023001',
        customer: 'อัมพร จันทร์กระจ่าง',
        numberOfItems: 2,
        sell: 49000,
        seller: 'อัมพร จันทร์กระจ่าง',
        createdDate: '20 ธ.ค. 2566, 15:34 น.',
      },
      {
        id: 3,
        code: 'QT-2023001',
        customer: 'อัมพร จันทร์กระจ่าง',
        numberOfItems: 2,
        sell: 49000,
        seller: 'อัมพร จันทร์กระจ่าง',
        createdDate: '20 ธ.ค. 2566, 15:34 น.',
      },
      {
        id: 4,
        code: 'QT-2023001',
        customer: 'อัมพร จันทร์กระจ่าง',
        numberOfItems: 2,
        sell: 49000,
        seller: 'อัมพร จันทร์กระจ่าง',
        createdDate: '20 ธ.ค. 2566, 15:34 น.',
      },
      {
        id: 5,
        code: 'QT-2023001',
        customer: 'อัมพร จันทร์กระจ่าง',
        numberOfItems: 2,
        sell: 49000,
        seller: 'อัมพร จันทร์กระจ่าง',
        createdDate: '20 ธ.ค. 2566, 15:34 น.',
      },
    ],
    totalElements: 5,
  });
  const [statusCount] = useState<any>({
    waitingConfirm: 0,
    productionSent: 0,
    canceled: 0,
    draft: 0,
  });
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    desc: true,
    startDate: null,
    endDate: null,
  });
  const columns: GridColDef[] = [
    {
      field: 'code',
      headerName: 'Code',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              fontWeight: 600,
            }}
          >
            {params.row.code}
          </div>
        );
      },
    },
    {
      field: 'customer',
      headerName: 'ลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center"
            style={{
              columnGap: '14px',
            }}
          >
            <Avatar
              src={params.row.imageUrl}
              sx={{
                height: '32px',
                width: '32px',
              }}
            />
            <span>{params.row.customer}</span>
          </div>
        );
      },
    },
    {
      field: 'numberOfItems',
      headerName: 'จำนวนรายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.numberOfItems);
      },
    },
    {
      field: 'sell',
      headerName: 'ยอดขาย (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.sell, 2);
      },
    },
    {
      field: 'seller',
      headerName: 'ฝ่ายขาย',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center"
            style={{
              columnGap: '14px',
            }}
          >
            <Avatar
              src={params.row.imageUrl}
              sx={{
                height: '32px',
                width: '32px',
              }}
            />
            <span>{params.row.seller}</span>
          </div>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'วันที่สร้าง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <>
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '104px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
              }}
              onClick={() => {
                router.push(`/corporate/quotation/1`);
              }}
            >
              รายละเอียด
            </Button>
          </>
        );
      },
    },
  ];
  const handleChangeStatus = (event: React.SyntheticEvent, newStatus: any) => {
    router.push(`/corporate/quotation?status=${newStatus}`);
  };

  return (
    <>
      <ProductNav title="ใบเสนอราคา" showBorderBottom>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(newValue) =>
              setFilters({
                ...filters,
                searchName: newValue,
                page: 0,
              })
            }
          />
          <div
            onClick={(_e: any) => {
              router.push('/corporate/quotation/create');
            }}
          >
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="สร้างใบเสนอราคา"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <FilterWrapStyled>
          <div
            style={{
              minWidth: 'fit-content',
            }}
          >
            <Tabs
              value={status}
              onChange={handleChangeStatus}
              variant="standard"
            >
              {quotationStatus.map((item, index) => (
                <Tab
                  key={index}
                  label={
                    <div
                      className="flex items-center"
                      style={{
                        columnGap: '16px',
                      }}
                    >
                      {item.name}
                      <Badge
                        badgeContent={
                          numberWithCommas(statusCount[item.key]) || '0'
                        }
                        color="secondary"
                        sx={{
                          '.MuiBadge-badge': {
                            backgroundColor:
                              status !== item.status ? '#78909C' : '',
                          },
                        }}
                      ></Badge>
                    </div>
                  }
                  value={item.status}
                  sx={{
                    color: status !== item.status ? '#78909C' : '',
                  }}
                />
              ))}
            </Tabs>
          </div>
          <div className="order-date">
            <Select
              displayEmpty
              size="small"
              variant="outlined"
              value={`${filters.desc}`}
              onChange={(event: any) =>
                setFilters({
                  ...filters,
                  desc: event.target.value,
                })
              }
            >
              <MenuItem value="true">ใหม่สุดก่อน</MenuItem>
              <MenuItem value="false">เก่าสุดก่อน</MenuItem>
            </Select>
            <Divider orientation="vertical" variant="middle" flexItem />
            <AppDateRange
              data={{
                startDate: filters.startDate,
                endDate: filters.endDate,
              }}
              handleChange={(dateRange: any) => {
                setFilters({
                  ...filters,
                  startDate: dateRange.startDate,
                  endDate: dateRange.endDate,
                });
              }}
            />
          </div>
        </FilterWrapStyled>
        <div className="content-wrap">
          <AppTableStyle $rows={quotationList.content}>
            <div className="content-wrap">
              <DataGrid
                hideFooter={true}
                rows={
                  !isEmpty(quotationList.content) ? quotationList.content : []
                }
                columns={columns}
                paginationMode="server"
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={
                    quotationList?.totalElements
                      ? quotationList.totalElements
                      : 0
                  }
                  handleChangeFilters={(newValues: FiltersType) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
    </>
  );
};

Quotation.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query } = context;
  const { status } = query;
  return {
    props: status ? { status } : { status: 'WAITING_CONFIRM' },
  };
};
export default Quotation;
