import React, { ReactElement, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import Image from 'next/image';
import QuotationPapers from '@/components/corporate/quotation/QuotationPapers';
import { isEmpty } from 'lodash';
import { Button, Menu, MenuItem } from '@mui/material';
import styled from 'styled-components';
import AppModalConfirm from '@/components/global/AppModalConfirm';

const StatusStyled = styled.div`
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 16px 0 8px;
  column-gap: 8px;
  cursor: default;
`;
const CancelBarStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 12px;
  background-color: #fde8ef;
  color: #d32f2f;
  column-gap: 12px;
  position: sticky;
  top: 64px;
  z-index: 9;
  white-space: nowrap;
  .overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;
const QuotationSheet = () => {
  const router = useRouter();
  const sheetRef = useRef<HTMLDivElement>(null);
  const [isCancel] = useState<boolean>(true);
  const [openConfirmSend, setOpenConfirmSend] = useState<boolean>(false);
  const [openCancel, setOpenCancel] = useState<boolean>(false);
  const [renderSummarizePage, setRenderSummarizePage] =
    useState<boolean>(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [dataQuotation, setDataQuotation] = useState<any>({
    rowsPerSheet: [
      [
        {
          id: 1,
          ldCode: 'LD-2023001',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 2,
          ldCode: 'LD-2023002',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 3,
          ldCode: 'LD-2023003',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 4,
          ldCode: 'LD-2023004',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 5,
          ldCode: 'LD-2023005',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 6,
          ldCode: 'LD-2023006',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 7,
          ldCode: 'LD-2023007',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 8,
          ldCode: 'LD-2023008',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 9,
          ldCode: 'LD-2023009',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 10,
          ldCode: 'LD-2023010',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 11,
          ldCode: 'LD-2023011',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 12,
          ldCode: 'LD-2023012',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 13,
          ldCode: 'LD-2023013',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 14,
          ldCode: 'LD-2023014',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
        {
          id: 15,
          ldCode: 'LD-2023015',
          name: 'Mailer Box Standard',
          detail:
            'ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม • ด้านหน้า CMYK • เคลือบ Vanish Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm • ออกแบบอาร์ตเวิร์กใหม่',
          quantity: 500,
          pricePerUnit: 39,
          total: 19500,
        },
      ],
    ],
  });

  const { id } = router.query;
  const handleNewData = (rowThisSheet: any, remainingData: any) => {
    setDataQuotation((prevData: any) => ({
      ...prevData,
      rowsPerSheet: [
        ...prevData.rowsPerSheet.slice(0, -1), // เก็บข้อมูลเดิมยกเว้นข้อมูลหน้าสุดท้าย
        rowThisSheet, // แทนที่ข้อมูลหน้าสุดท้ายด้วย rowThisSheet
        remainingData, // เพิ่ม remainingData เป็นหน้าต่อไป
      ],
    }));
  };
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleSendProduction = () => {
    setOpenConfirmSend(false);
  };
  const handleCancel = (_reason: string) => {
    setOpenCancel(false);
  };
  return (
    <>
      <ProductNav
        title={`ใบเสนอราคา QT-XX`}
        backUrl={`/corporate/laydata/${id}/price/`}
        showBorderBottom
      >
        <StatusStyled
          style={{
            color: '#F9A825',
            background: '#FFF9E6',
          }}
        >
          <Image src={'/icons/schedule.svg'} width={24} height={24} alt="" />
          <span>รอลูกค้ายืนยัน</span>
        </StatusStyled>
        {/* <ReactToPrint */}
        {/*  trigger={() => ( */}
        {/*    <div className="print-btn"> */}
        {/*      <Image */}
        {/*        src="/icons/icon-print.svg" */}
        {/*        width={24} */}
        {/*        height={24} */}
        {/*        alt="" */}
        {/*      /> */}
        {/*    </div> */}
        {/*  )} */}
        {/*  content={() => sheetRef.current} */}
        {/* /> */}
        <div>
          <Button
            variant="outlined"
            size="small"
            color="blueGrey"
            sx={{
              minWidth: '40px',
            }}
            onClick={handleClick}
          >
            <div className="action-dot">
              <div className="dot" />
            </div>
          </Button>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            sx={{
              '.MuiList-root': {
                padding: '8px',
              },
              li: {
                width: 'auto',
              },
            }}
          >
            <MenuItem
              onClick={handleClose}
              sx={{
                padding: '0 8px',
                height: '40px',
                width: '100px',
                borderRadius: '8px',
              }}
            >
              <div
                className="drop-menu"
                onClick={() => {
                  router.push(`/corporate/quotation/${id}`);
                }}
              >
                <Image
                  src="/icons/edit-black.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                แก้ไข
              </div>
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              sx={{
                padding: '0 8px',
                height: '40px',
                width: '100px',
                borderRadius: '8px',
              }}
            >
              <div
                className="drop-menu"
                onClick={() => {
                  setOpenConfirmSend(true);
                }}
              >
                <Image
                  src="/icons/icon-export-notes.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                ส่งผลิต
              </div>
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              sx={{
                padding: '0 8px',
                height: '40px',
                width: '100px',
                borderRadius: '8px',
              }}
            >
              <div className="drop-menu">
                <Image
                  src="/icons/aside/user-menu/icon-history.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                ประวัติ
              </div>
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              sx={{
                padding: '0 8px',
                height: '40px',
                width: '100px',
                borderRadius: '8px',
                '&:hover': {
                  backgroundColor: '#FDE8EF',
                },
              }}
            >
              <div
                className="drop-menu text-[#D32F2F]"
                onClick={() => {
                  setOpenCancel(true);
                }}
              >
                <Image
                  src="/icons/icon-scan-delete.svg"
                  alt=""
                  width={24}
                  height={24}
                />
                ยกเลิกรายการ
              </div>
            </MenuItem>
          </Menu>
        </div>
      </ProductNav>
      {isCancel && (
        <CancelBarStyled>
          <Image
            src="/icons/icon-scan-delete.svg"
            alt=""
            width={24}
            height={24}
          />
          <span className="overflow">
            ยกเลิกการใช้งานใบเสนอราคาเนื่องจาก ลูกค้าหายไม่ชำระเงินติดต่อไม่ได้
          </span>
          <span
            style={{
              textDecoration: 'underline',
              cursor: 'pointer',
            }}
          >
            ประวัติรายการ
          </span>
        </CancelBarStyled>
      )}
      <div
        ref={sheetRef}
        className="w-full flex flex-col items-center p-[24px]"
      >
        {!isEmpty(dataQuotation.rowsPerSheet) &&
          dataQuotation.rowsPerSheet.map((items: any, index: number) => {
            const startIndex = dataQuotation.rowsPerSheet
              .slice(0, index)
              .reduce((total: number, currentItems: any) => {
                return total + currentItems.length;
              }, 0);
            return (
              <QuotationPapers
                key={items.id}
                data={items}
                startIndex={startIndex}
                makeNewData={(rowThisSheet: any, remainingData: any) => {
                  handleNewData(rowThisSheet, remainingData);
                }}
                makeRenderSummarizePage={(isRender: boolean) => {
                  setRenderSummarizePage(isRender);
                }}
              />
            );
          })}
        {renderSummarizePage && (
          <QuotationPapers isRenderSummarizePage startIndex={0} />
        )}
      </div>
      <AppModalConfirm
        open={openConfirmSend}
        onClickClose={() => {
          setOpenConfirmSend(false);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยืนยันการส่งผลิต`}
        confirmDescription={
          'ลูกค้าได้ทำการยืนยันสเปคสินค้า จำนวนสินค้า และราคาสินค้าแล้ว\n' +
          'คุณต้องการยืนยันการสั่งซื้อแล้วส่งผลิตต่อไป?'
        }
        loadingConfirm={false}
        onConfirm={() => {
          handleSendProduction();
        }}
      />
      <AppModalConfirm
        open={openCancel}
        isReason
        onClickClose={() => {
          setOpenCancel(false);
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยกเลิกใบเสนอราคา`}
        confirmDescription={
          'คุณต้องการที่จะยกเลิกใบเสนอราคา QT-XX\n' +
          'จะต้องระบุเหตุผลการยกเลิกรายการนี้'
        }
        loadingConfirm={false}
        onConfirm={(reason: any) => {
          handleCancel(reason);
        }}
      />
    </>
  );
};
QuotationSheet.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default QuotationSheet;
