import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { FormModalStyle } from '@/styles/share.styled';
import Image from 'next/image';

import { isEmpty } from 'lodash';
import { Button, Dialog, DialogContent, IconButton } from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import QuotationForm from '@/components/corporate/quotation/QuotationForm';
import apiContact from '@/services/core/contact';

const initialDataCustomerRender = {
  name: '',
  email: '',
  imageUrl: '',
};
const EditQuotation = () => {
  const [openModalAddress, setOpenModalAddress] = useState<boolean>(false);
  const [customerAddressList, setCustomerAddressList] = useState<any>([]);
  const [selectedAddress, setSelectedAddress] = useState<any>({});
  const [dataCustomerRender, setDataCustomerRender] = useState<any>(
    initialDataCustomerRender
  );
  const handleFindCustomer = async (id: any) => {
    const res = await apiContact.getContactById(id);
    if (!res.isError) {
      setCustomerAddressList(res.data);
    }
  };
  useEffect(() => {
    if (!isEmpty(customerAddressList)) {
      setOpenModalAddress(true);
    }
  }, [customerAddressList]);
  const onCloseModalAddress = () => {
    setOpenModalAddress(false);
  };
  const handleClickSelectAddress = (data: any) => {
    onCloseModalAddress();
    setSelectedAddress(data);
    setDataCustomerRender({
      name: customerAddressList.name,
      email: customerAddressList.email,
      imageUrl: customerAddressList.imageUrl,
    });
  };
  const handleRemoveContactAddress = () => {
    setDataCustomerRender(initialDataCustomerRender);
    setSelectedAddress({});
  };
  return (
    <>
      <ProductNav
        title="แก้ไขใบเสนอราคา"
        showBorderBottom
        backUrl={'/corporate/quotation'}
      ></ProductNav>
      <QuotationForm
        findCustomer={(id: number) => {
          handleFindCustomer(id);
        }}
        dataAddressForRender={selectedAddress}
        dataCustomerRender={dataCustomerRender}
        removeContactAddress={handleRemoveContactAddress}
      />
      <Dialog open={openModalAddress}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">ข้อมูลสำหรับออกใบกำกับภาษี</div>
                <div
                  className="x-close"
                  onClick={() => {
                    onCloseModalAddress();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <div className="modal-address-content">
                  {!isEmpty(customerAddressList.contactAddresses) &&
                    customerAddressList.contactAddresses.map((item: any) => {
                      return (
                        <div
                          className={`list ${
                            !isEmpty(selectedAddress) &&
                            selectedAddress.id === item.id
                              ? 'active'
                              : ''
                          }`}
                          key={item.id}
                          onClick={() => {
                            handleClickSelectAddress(item);
                          }}
                        >
                          <div className="title">
                            {item.isCorporate ? 'นิติบุคคล' : 'บุคคลธรรมดา'} •{' '}
                            {item.taxNumber} {item.phoneNumber} •{' '}
                            {customerAddressList.email}
                          </div>
                          <div className="description">
                            <Image
                              src={'/icons/company-info/icon-address.svg'}
                              width={16}
                              height={16}
                              alt=""
                            />
                            {item.address} ต.{item.district} อ.
                            {item.subDistrict} จ.{item.province}{' '}
                            {item.postalCode}
                          </div>
                        </div>
                      );
                    })}
                </div>
                <Button
                  type="button"
                  variant="contained"
                  color="dark"
                  fullWidth
                >
                  เพิ่มข้อมูล
                </Button>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

EditQuotation.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default EditQuotation;
