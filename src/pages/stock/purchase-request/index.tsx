import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { Badge, Checkbox, Tab, Tabs } from '@mui/material';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import useSWR from 'swr';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { getPrStatusData } from '@/utils/helper';
import { useDebounceCallback } from 'usehooks-ts';
import ModalSuppliers from '@/components/purchase-request/PurchaseRequestForm/ModalSuppliers';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import apiAnnotation from '@/services/stock/annotation';
import apiPurchaseRequestRaw from '@/services/stock/purchaseRequest-raw';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import PopoverAction from '@/components/PopoverActionn';
import SvgPaperPencilIcon from '@/components/svg-icon/SvgPaperPencilIcon';
import SvgPaperCancelIcon from '@/components/svg-icon/SvgPaperCancelIcon';
import AppDateRange from '@/components/global/AppDateRange';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { PurchaseReqStatus } from '@/store/features/purchase-request/types';
import styled from 'styled-components';
import ModalReasonConfirm from '@/components/purchase-request/modal/ModalReasonConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

type SearchParams = {
  page: number;
  size: number;
  startDate: string | null;
  endDate: string | null;
  search: string | null;
  status: number;
};

const fetcherAnnotationList = async () => {
  const res = await apiAnnotation.annotationList();
  return res;
};

const PurchaseRequestPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [search, setSearch] = useState<string | null>('');
  const [statusId, setStatusId] = useState<number>(0);
  const [prReq, setPrReq] = useState<any>(null);
  const [prOrderRaw, setPrOrderRaw] = useState<any>(null);
  const [prStatus, setPrStatus] = useState<
    { id: number; name: string; count: number }[]
  >([]);

  const [filters, setFilters] = useState<SearchParams>({
    page: 0,
    size: 10,
    startDate: null,
    endDate: null,
    search: search || '',
    status: -1,
  });

  const [open, setOpen] = useState<boolean>(false);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [selectPrCancel, setSelectPrCancel] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const debounced = useDebounceCallback(setSearch, 700);

  useEffect(() => {
    fetchPurchaseStatus();
    if (filters?.status === -1) {
      fetchPrOrderRaw();
    } else {
      fetchPurchaseReqs();
    }
  }, [filters]);

  useEffect(() => {
    setFilters({
      ...filters,
      page: 0,
      search: search || '',
    });
  }, [search]);

  const fetchPurchaseReqs = async () => {
    const response: any = await apiPurchaseRequest.getList(filters);
    if (response.status) {
      setPrReq(response.data);
    } else {
      setPrReq(null);
    }
  };

  const fetchPurchaseStatus = async () => {
    const response: any = await apiPurchaseRequest.getStatus();
    if (response.status) {
      setPrStatus(response.data);
    } else {
      setPrStatus([]);
    }
  };

  const fetchPrOrderRaw = async () => {
    const response: any = await apiPurchaseRequestRaw.getPrOrderRawPage({
      page: filters.page,
      size: filters.size,
      prOrderRawStatusId: 1,
      startDate: filters.startDate,
      endDate: filters.endDate,
      search: filters.search,
    });
    if (response.status) {
      setPrOrderRaw(response.data);
    } else {
      setPrOrderRaw(null);
    }
  };

  const { data: response } = useSWR(
    isModalOpen ? '/annotationList' : null,
    fetcherAnnotationList
  );

  const annotationList = response?.data || [];
  const matchedAnnotations = annotationList.filter(
    (item: any) => item.annotationType.id === 1
  );

  const totalItems =
    statusId === 0 ? prOrderRaw?.totalElements || 0 : prReq?.totalElements || 0;

  const handleChangeStatus = (_event: React.SyntheticEvent, newStatus: any) => {
    setStatusId(newStatus);
    setSearch(null);
    setFilters({
      ...filters,
      page: 0,
      search: '',
      status: newStatus - 1,
    });
  };

  const handleCheckedAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const allIds = prOrderRaw?.content?.map((row: any) => row.id);
      setSelectedItems(allIds);
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelected = (e: any, params: any) => {
    setSelectedItems((prevSelected) => {
      if (e.target.checked) {
        return [...prevSelected, params.row.id];
      }
      return prevSelected.filter((item) => item !== params.row.id);
    });
  };

  const onCreatePR = async () => {
    setOpen(true);
  };

  const handleSupplierSelected = async (suppliers: any) => {
    const changeItems =
      selectedItems.length > 0
        ? selectedItems.map((id) => ({ prOrderRawId: id }))
        : [];
    const body = {
      contactId: suppliers.id,
      prOrderRawData: changeItems,
    };
    const res = await apiPurchaseRequest.createPR(body);
    if (res.status) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'สร้างใบขอซื้อสำเร็จ',
          severity: 'success',
        })
      );
      setTimeout(() => {
        handleCloseModal();
        router.push(`${router.pathname}/${res.data.id}/create`);
      }, 500);
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'สร้างใบขอซื้อไม่สำเร็จ',
          severity: 'error',
        })
      );
      setTimeout(() => handleCloseModal(), 500);
    }
  };

  const handleCloseModal = () => {
    setSelectPrCancel(null);
    setIsModalOpen(false);
  };

  const handleConfirmCancelPR = async (reason: any) => {
    const res = await apiPurchaseRequest.cancelPrOrder({
      prOrderId: selectPrCancel.id,
      reason: {
        annotationId: reason.annotationId,
        note: reason.note,
      },
    });
    if (res.status) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'ยกเลิกใบขอซื้อสำเร็จ',
          severity: 'success',
        })
      );
      fetchPurchaseReqs();
      fetchPurchaseStatus();
      setTimeout(() => handleCloseModal(), 500);
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'ยกเลิกใบขอซื้อไม่สำเร็จ',
          severity: 'error',
        })
      );
      setTimeout(() => handleCloseModal(), 500);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'prOrderNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'contact',
      headerName: 'ตัวแทนจำหน่าย',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 3,
      renderCell: (params) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.contact?.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.contact?.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'countPrList',
      headerName: 'จำนวนรายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'status',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        let bgColor = '';
        let textColor = '';
        switch (params.row.status) {
          case PurchaseReqStatus.APPROVED:
            bgColor = '#e6f7ed';
            textColor = '#10b981';
            break;
          case PurchaseReqStatus.REJECT:
            bgColor = '#fee2e2';
            textColor = '#ef4444';
            break;
          case PurchaseReqStatus.CANCEL:
            bgColor = '#fee2e2';
            textColor = '#ef4444';
            break;
          case PurchaseReqStatus.DRAFT:
            bgColor = '#f3f4f6';
            textColor = '#6b7280';
            break;
          case PurchaseReqStatus.WAITING:
            bgColor = '#FFF8E6';
            textColor = '#f59e0b';
            break;
          default:
            bgColor = '#f3f4f6';
            textColor = '#6b7280';
            break;
        }
        const statusText =
          params.row.statusEnum ||
          getPrStatusData(params.row.status)?.name ||
          '-';
        return (
          <div
            style={{
              backgroundColor: bgColor,
              color: textColor,
              padding: '6px 12px',
              borderRadius: '8px',
              fontWeight: 500,
              display: 'inline-block',
              fontSize: '14px',
            }}
          >
            {statusText}
          </div>
        );
      },
    },
    {
      field: 'user',
      headerName: 'ผู้ขอซื้อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.user?.imageUrl || '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.user?.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'สร้างเมื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div>{dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm')}</div>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        if (params.row.status === PurchaseReqStatus.WAITING) {
          return (
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'stock.purchase-request.list'
                  ),
                  IconElement: () => <SvgPaperPencilIcon />,
                  title: 'รายละเอียด',
                  onAction: () => {
                    if (params.row.statusEnum === 'แบบร่าง') {
                      router.push(`${router.pathname}/${params.row.id}/create`);
                    } else {
                      router.push(`${router.pathname}/${params.row.id}`);
                    }
                  },
                },
                {
                  disabled: !isAllowed(
                    permissions,
                    'stock.purchase-request.cancel'
                  ),
                  IconElement: () => <SvgPaperCancelIcon fill={'#D32F2F'} />,
                  title: 'ยกเลิกใบขอซื้อ',
                  onAction: () => {
                    setSelectPrCancel(params.row);
                    setIsModalOpen(true);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                    display: filters.status === 1 ? '' : 'none',
                  },
                },
              ]}
            />
          );
        }
        return (
          <DetailButton
            disabled={!isAllowed(permissions, 'stock.purchase-request.list')}
            onClick={() => {
              if (isAllowed(permissions, 'stock.purchase-request.list')) {
                if (params.row.status === PurchaseReqStatus.DRAFT) {
                  router.push(
                    `/stock/purchase-request/${params.row.id}/create`
                  );
                } else {
                  router.push(`/stock/purchase-request/${params.row.id}`);
                }
              }
            }}
          >
            รายละเอียด
          </DetailButton>
        );
      },
    },
  ];

  const columnsJob: GridColDef[] = [
    {
      field: 'checkbox',
      headerName: '',
      width: 80,
      sortable: false,
      editable: false,
      disableColumnMenu: true,
      headerAlign: 'center',
      align: 'center',
      renderHeader: () => {
        const isAllSelected =
          prOrderRaw?.content?.length > 0 &&
          selectedItems.length === prOrderRaw?.content?.length;
        return (
          <Checkbox
            color="primary"
            checked={isAllSelected}
            onChange={handleCheckedAll}
            icon={<IconUnCheckbox />}
            checkedIcon={<IconCheckboxBlack />}
          />
        );
      },
      renderCell: (params) => {
        if (params.row.id == null) return null;
        const isSelected = selectedItems.includes(params.row.id);
        return (
          <Checkbox
            color="primary"
            checked={isSelected}
            onChange={(e: any) => handleSelected(e, params)}
            icon={<IconUnCheckbox />}
            checkedIcon={<IconCheckboxBlack />}
          />
        );
      },
    },
    {
      field: 'name',
      headerName: 'รายการวัสดุ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'jobNo',
      headerName: 'รายการผลิต(อ้างอิง)',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      flex: 1,
    },
    {
      field: 'materialName',
      headerName: 'วัสดุ',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      flex: 1,
    },
    {
      field: 'brand',
      headerName: 'แบรนด์',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      flex: 1,
      renderCell: (params) => <div>{params.row.brand?.name || '-'}</div>,
    },
    {
      field: 'quantity',
      headerName: 'จำนวน',
      editable: false,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      minWidth: 162,
      flex: 1,
    },
    {
      field: 'prOrderRawStatus',
      headerName: 'สถานะ',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      flex: 1,
      renderCell: (params) => (
        <div
          className={
            'border-none border-yellow-100 bg-yellow-100 rounded-sm p-1.5 font-bold text-amber-400'
          }
        >
          {params.row.prOrderRawStatus?.name}
        </div>
      ),
    },
    {
      field: 'createUser',
      headerName: 'ผู้สร้าง',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      flex: 1,
      renderCell: (params) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.createUser?.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.createUser?.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'วันที่ส่งคำขอ',
      editable: false,
      sortable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      flex: 1,
      renderCell: (params) => (
        <div>{dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm')}</div>
      ),
    },
  ];

  return (
    <div>
      <ModalReasonConfirm
        open={isModalOpen}
        matchedAnnotations={matchedAnnotations}
        onClickClose={handleCloseModal}
        onConfirm={handleConfirmCancelPR}
        confirmTitle="ยืนยันยกเลิกใบขอซื้อ"
        confirmDescription={`คุณต้องการยกเลิกใบขอซื้อ “${selectPrCancel?.prOrderNo}”  โดยมีสาเหตุที่ต้องยกเลิกดังนี้`}
        icon={
          <Image
            src={'/icons/icon-cancel-pr.svg'}
            width={40}
            height={40}
            alt=""
          />
        }
        reasonTypeCancelList={[]}
        loadingConfirm={false}
        maxWidth="420px"
      />
      <ProductNav title="รายการขอซื้อ" showBorderBottom>
        <ActionGroupStyle>
          <AppDateRange
            data={{
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            handleChange={(dateRange: any) => {
              setFilters({
                ...filters,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              });
            }}
          />
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างรายการขอซื้อ"
            onClick={onCreatePR}
            disabled={!isAllowed(permissions, 'stock.purchase-request.create')}
          />
          <ModalSuppliers
            open={open}
            handleClose={() => setOpen(false)}
            onSubmit={handleSupplierSelected}
          />
        </ActionGroupStyle>
      </ProductNav>
      {isAllowed(permissions, 'stock.purchase-request.list') ? (
        <AppContentStyle>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div style={{ minWidth: '800px' }}>
                <Tabs
                  value={Number(statusId)}
                  onChange={handleChangeStatus}
                  variant="standard"
                >
                  <Tab
                    label={
                      <div
                        className="flex items-center"
                        style={{
                          columnGap: '16px',
                          minHeight: '32px',
                        }}
                      >
                        สินค้ารอสั่งซื้อ
                        <Badge
                          badgeContent={prOrderRaw?.totalElements || '0'}
                          color="secondary"
                          sx={{
                            '.MuiBadge-badge': {
                              backgroundColor:
                                Number(statusId) === 0 ? '#263238' : '#607D8B',
                            },
                          }}
                        />
                      </div>
                    }
                    value={0}
                    sx={{
                      color: Number(statusId) === 0 ? '#263238' : '',
                    }}
                  />
                  {prStatus?.map((item: any, index: React.Key) => (
                    <Tab
                      key={index}
                      label={
                        <div
                          className="flex items-center"
                          style={{
                            columnGap: '16px',
                            minHeight: '32px',
                          }}
                        >
                          {getPrStatusData(item.value).name}
                          <Badge
                            badgeContent={item.count || '0'}
                            color="secondary"
                            sx={{
                              '.MuiBadge-badge': {
                                backgroundColor:
                                  Number(statusId) === item.id
                                    ? '#263238'
                                    : '#607D8B',
                              },
                            }}
                          />
                        </div>
                      }
                      value={item.id}
                      sx={{
                        color: Number(statusId) === item.id ? '#263238' : '',
                      }}
                    />
                  ))}
                </Tabs>
              </div>
            </FilterWrapStyled>

            <div className="flex justify-between items-center px-4 py-2">
              <div>{totalItems} รายการ</div>
              <div className="flex gap-2 items-center">
                {/* <Image */}
                {/*  className="cursor-pointer" */}
                {/*  src="/icons/icon-filter.svg" */}
                {/*  alt="" */}
                {/*  width={24} */}
                {/*  height={24} */}
                {/* /> */}
                <SearchInput
                  makeSearchValue={(newValue) => {
                    debounced(newValue);
                    if (statusId === 0) {
                      setFilters((prev) => ({ ...prev, rawPage: 0 }));
                    } else {
                      setFilters((prev) => ({ ...prev, page: 0 }));
                    }
                  }}
                />
              </div>
            </div>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle
              $rows={
                statusId === 0
                  ? prOrderRaw?.content || []
                  : prReq?.content || []
              }
            >
              <div className="content-wrap">
                <ScrollBarStyled>
                  <DataGrid
                    hideFooter={true}
                    rows={
                      statusId === 0
                        ? prOrderRaw?.content || []
                        : prReq?.content || []
                    }
                    columns={statusId === 0 ? columnsJob : columns}
                    paginationMode="server"
                    rowCount={
                      (statusId === 0
                        ? prOrderRaw?.totalElements
                        : prReq?.totalElements) || 0
                    }
                    pageSize={filters.size}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    // loading={
                    //   statusId === 0 ? isPrOrderRawLoading : isPrListLoading
                    // }
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                      NoResultsOverlay: () => <TableNoRowsOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={
                      (statusId === 0
                        ? prOrderRaw?.totalElements
                        : prReq?.totalElements) || 0
                    }
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        </AppContentStyle>
      ) : (
        <NotPermission />
      )}
    </div>
  );
};

PurchaseRequestPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default PurchaseRequestPage;

const DetailButton = styled.div<{ disabled: boolean }>`
  font-size: 12px;
  border-radius: 8px;
  border: 1px solid #dbe2e5;
  padding: 10px 10px;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.3 : 1)};
  &:hover {
    border: 1px solid #263238;
  }
`;
