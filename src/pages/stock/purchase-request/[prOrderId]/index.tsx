import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  purchaseRequestSelector,
  setPurchaseRequest,
} from '@/store/features/purchase-request';
import useSWR from 'swr';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import { useRouter } from 'next/router';
import {
  ArrowBack,
  HighlightOff,
  HistoryOutlined,
  InfoOutlined,
} from '@mui/icons-material';
import { PrRenderStatus } from '@/pages/stock/purchase-request/[prOrderId]/create';
import ActionButton from '@/components/ActionButton';
import PopoverAction from '@/components/PopoverActionn';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import SvgArrowDoubleBackIcon from '@/components/svg-icon/SvgArrowDoubleBackIcon';
import { setSnackBar } from '@/store/features/alert';
import SvgPaperCancelIcon from '@/components/svg-icon/SvgPaperCancelIcon';
import { PurchaseReqStatus } from '@/store/features/purchase-request/types';
import Image from 'next/image';
import SvgExportNoteIcon from '@/components/svg-icon/SvgExportNoteIcon';
import SvgCalendarIcon from '@/components/svg-icon/SvgCalendarIcon';
import moment from 'moment/moment';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import SvgScheduleFillIcon from '@/components/svg-icon/SvgScheduleFillIcon';
import SvgCheckCircleFillIcon from '@/components/svg-icon/SvgCheckCircleFillIcon';
import ConfirmModal from '@/components/global/ConfirmModal';
import apiAnnotation from '@/services/stock/annotation';
import _, { isEmpty } from 'lodash';
import { userSelector } from '@/store/features/user';
import DrawerHistory from '@/components/purchase-request/DrawerHistory';
import ModalReasonConfirm from '@/components/purchase-request/modal/ModalReasonConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

export type ConfirmModalQuotationType = {
  open: boolean;
  title: string;
  description: string;
  iconElement: () => any;
  confirmLabel: string;
  confirmAction: () => void;
};

export const confirmModalInit: ConfirmModalQuotationType = {
  open: false,
  title: '',
  description: '',
  iconElement: () => null,
  confirmLabel: '',
  confirmAction: () => null,
};

const fetcherApproveList = async () => {
  const res = await apiAnnotation.annotationList();
  return res;
};

const PurchaseRequestDetail = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { user } = useAppSelector(userSelector);
  const { prOrderId } = router.query;
  const dispatch = useAppDispatch();
  const { purchaseReq } = useAppSelector(purchaseRequestSelector);
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [confirmModal, setConfirmModal] =
    useState<ConfirmModalQuotationType>(confirmModalInit);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  // const { data: prData, mutate: fetchPrData } = useSWR(
  //   prOrderId ? `/pr-order/${prOrderId}` : null,
  //   () => apiPurchaseRequest.getById(prOrderId) as any
  // );
  const [prData, setPrData] = useState<any>(null);
  const [historyVisible, setHistoryVisible] = useState<boolean>(false);

  const [approveState, setApproveState] = useState<{
    isApprove: boolean;
    currentApprove: any;
    nextApprove: any;
  }>({ isApprove: false, currentApprove: null, nextApprove: null });

  useEffect(() => {
    handleApproveStep();
  }, [purchaseReq]);

  const handleApproveStep = () => {
    const nonApprove = _.filter(
      purchaseReq?.detail?.prOrderApprovals,
      (item: any) => item.status === 'รออนุมัติ'
    );
    const minApprove = _.minBy(nonApprove, (item: any) =>
      _.get(item, 'role.sort')
    );
    const nextApprove = _.find(
      nonApprove,
      (item: any) => item.role.sort === minApprove.role.sort + 1
    );
    if (minApprove && user) {
      setApproveState({
        ...approveState,
        isApprove: minApprove.userApprove.id === user.id,
        currentApprove: minApprove,
        nextApprove: nextApprove || null,
      });
    }
  };

  const [reasonConfirm, setReasonConfirm] = useState<{
    visible: boolean;
    method: 'reject' | 'cancel' | null;
    title: string;
  }>({
    visible: false,
    method: null,
    title: '',
  });

  const { data: response } = useSWR('/annotationList', fetcherApproveList);
  const matchedApprovals = response?.data?.filter(
    (item: any) => item.annotationType?.id === 2
  );

  const matchedAnnotations = response?.data.filter(
    (item: any) => item.annotationType.id === 1
  );

  useEffect(() => {
    fetchPrData();
  }, [prOrderId]);

  const fetchPrData = async () => {
    const res: any = await apiPurchaseRequest.getById(prOrderId);
    if (!res.isError) {
      setPrData(res);
    } else {
      router.push('/stock/purchase-request/');
    }
  };

  useEffect(() => {
    if (prData) {
      dispatch(setPurchaseRequest({ status: prData?.status, detail: prData }));
    }
  }, [prData]);

  const RenderConfirmBtnLabel = () => {
    switch (purchaseReq?.status) {
      case PurchaseReqStatus.WAITING:
        return 'ยืนยันอนุมัติขอซื้อ';
      default:
        return '';
    }
  };

  const KabubCustomItems: any[] = [
    {
      IconElement: () => <SvgArrowDoubleBackIcon />,
      title: 'ตีกลับไปแก้',
      onAction: () => {
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ยืนยันตีกลับไปแก้',
          description: `คุณต้องการตีกลับไปแก้ไขใบขอซื้อ\n "${purchaseReq?.detail?.prOrderNo}" ใช่หรือไม่`,
          iconElement: () => <SvgArrowDoubleBackIcon />,
          confirmLabel: 'ส่งคำขอ',
          confirmAction: async () => {
            const response = await apiPurchaseRequest.changeStatusToDraft(
              Number(prOrderId)
            );
            if (response.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ตีกลับไปแก้สำเร็จ',
                  severity: 'success',
                })
              );
              fetchPrData();
              setTimeout(
                () =>
                  router.push(`/stock/purchase-request/${prOrderId}/create`),
                1000
              );
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ตีกลับไปแก้ไม่สำเร็จ',
                  severity: 'error',
                })
              );
            }
          },
        });
      },
      cssProps: {
        display:
          purchaseReq?.status === PurchaseReqStatus.WAITING &&
          isAllowed(permissions, 'stock.purchase-request.update')
            ? ''
            : 'none',
      },
    },
    {
      IconElement: () => <HighlightOff />,
      title: 'ไม่อนุมัติ',
      onAction: () => {
        setReasonConfirm({
          ...reasonConfirm,
          visible: true,
          method: 'reject',
          title: 'ไม่อนุมัติ',
        });
      },
      cssProps: {
        display:
          purchaseReq?.status === PurchaseReqStatus.WAITING &&
          isAllowed(permissions, 'stock.purchase-request.reject')
            ? ''
            : 'none',
      },
    },
    {
      IconElement: () => <HistoryOutlined />,
      title: 'ประวัติรายการ',
      onAction: () => setHistoryVisible(true),
    },
    {
      IconElement: () => <InfoOutlined />,
      title: 'วิธีใช้งาน',
      onAction: () => null,
      cssProps: {
        display: 'none',
      },
    },
    {
      IconElement: () => <SvgPaperCancelIcon fill={'#D32F2F'} />,
      title: 'ยกเลิกใบขอซื้อ',
      onAction: () => {
        setReasonConfirm({
          ...reasonConfirm,
          visible: true,
          method: 'cancel',
          title: 'ยกเลิก',
        });
      },
      cssProps: {
        color: '#D32F2F',
        '&:hover': {
          backgroundColor: '#FDE8EF',
        },
        display:
          (purchaseReq?.status === PurchaseReqStatus.DRAFT ||
            purchaseReq?.status === PurchaseReqStatus.WAITING) &&
          isAllowed(permissions, 'stock.purchase-request.cancel')
            ? ''
            : 'none',
      },
    },
  ];

  const onCloseConfirmModal = () => {
    setConfirmModal({ ...confirmModal, open: false });
    setTimeout(() => {
      setConfirmModal({ ...confirmModalInit });
      setConfirmLoading(false);
    }, 500);
  };

  const handleCloseReasonConfirm = () => {
    setReasonConfirm({
      ...reasonConfirm,
      visible: false,
      method: null,
    });
  };

  const handleApprove = () => {
    setConfirmModal({
      ...confirmModal,
      open: true,
      title: approveState?.nextApprove
        ? 'ยืนยันอนุมัติใบขอซื้อ'
        : 'อนุมัติใบขอซื้อ และส่งจัดซื้อ',
      description: approveState?.nextApprove
        ? `คุณได้ตรวจสอบข้อมูลใบขอซื้อ “${purchaseReq?.detail?.prOrderNo}” เรียบร้อยแล้ว\n` +
          `จะทำการส่งข้อมูลให้ผู้อนุมัติขั้นที่2 (${approveState?.nextApprove?.userApprove?.name}) ต่อไป`
        : `คุณได้ตรวจสอบข้อมูลใบขอซื้อ “${purchaseReq?.detail?.prOrderNo}” เรียบร้อยแล้ว\n` +
          'จะทำการส่งข้อมูลไปการจัดซื้อต่อไป',
      iconElement: () => <SvgExportNoteIcon />,
      confirmLabel: 'ยืนยัน',
      confirmAction: async () => {
        setConfirmLoading(true);
        const res = await apiPurchaseRequest.approve(Number(prOrderId));
        if (res.status) {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'ทำรายการสำเร็จ',
              severity: 'success',
            })
          );
          fetchPrData();
          setTimeout(() => {
            onCloseConfirmModal();
          }, 500);
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'ทำรายการไม่สำเร็จ',
              severity: 'error',
            })
          );
          setTimeout(() => onCloseConfirmModal(), 500);
        }
      },
    });
  };

  const handleReject = async (reason: any) => {
    if (purchaseReq && prOrderId) {
      const res = await apiPurchaseRequest.rejectPrOrder({
        prOrderId: Number(prOrderId),
        reason: {
          annotationId: reason.annotationId,
          note: reason.note,
        },
      });
      if (res.status) {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'ทำรายการสำเร็จ',
            severity: 'success',
          })
        );
        fetchPrData();
        setTimeout(() => handleCloseReasonConfirm(), 500);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'ทำรายการไม่สำเร็จ',
            severity: 'error',
          })
        );
        setTimeout(() => handleCloseReasonConfirm(), 500);
      }
    }
  };

  const handleConfirmCancelPR = async (reason: any) => {
    if (purchaseReq && prOrderId) {
      const res = await apiPurchaseRequest.cancelPrOrder({
        prOrderId: Number(prOrderId),
        reason: {
          annotationId: reason.annotationId,
          note: reason.note,
        },
      });
      if (res.status) {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'ยกเลิกใบขอซื้อสำเร็จ',
            severity: 'success',
          })
        );
        fetchPrData();
        setTimeout(() => handleCloseReasonConfirm(), 500);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'ยกเลิกใบขอซื้อไม่สำเร็จ',
            severity: 'error',
          })
        );
        setTimeout(() => handleCloseReasonConfirm(), 500);
      }
    }
  };

  const getApproveDisable = () => {
    if (approveState.nextApprove) {
      if (
        !approveState?.isApprove ||
        !isAllowed(permissions, 'stock.purchase-request.approve-step1')
        // !isAllowed(permissions, 'stock.purchase-request.update')
      ) {
        return true;
      }
      return false;
    }
    if (
      !approveState?.isApprove ||
      !isAllowed(permissions, 'stock.purchase-request.approve-step2')
      // !isAllowed(permissions, 'stock.purchase-request.update')
    ) {
      return true;
    }
    return false;
  };

  return (
    <Container>
      <ConfirmModal
        open={confirmModal.open}
        handleCloseModal={onCloseConfirmModal}
        loading={confirmLoading}
        title={confirmModal.title}
        description={confirmModal.description}
        iconElement={confirmModal.iconElement}
        confirmLabel={confirmModal.confirmLabel}
        confirmAction={confirmModal.confirmAction}
      />
      <ModalReasonConfirm
        open={reasonConfirm.visible}
        matchedApprove={matchedApprovals}
        matchedAnnotations={matchedAnnotations}
        onClickClose={() => handleCloseReasonConfirm()}
        onConfirm={
          reasonConfirm.method === 'reject'
            ? handleReject
            : handleConfirmCancelPR
        }
        confirmTitle={`ยืนยัน${reasonConfirm.title}ใบขอซื้อ`}
        confirmDescription={`คุณตรวจสอบข้อมูลเรียบร้อยแล้ว แต่ต้องการ${
          reasonConfirm.title
        }ใบขอซื้อ "${purchaseReq?.detail?.prOrderNo || '-'}"  โดยมีสาเหตุที่${
          reasonConfirm.title
        }ใบขอซื้อดังนี้`}
        icon={
          <Image
            src={'/icons/icon-cancel-pr.svg'}
            width={40}
            height={40}
            alt=""
          />
        }
        reasonTypeReject={reasonConfirm.method === 'reject'}
        reasonTypeCancelList={reasonConfirm.method === 'cancel'}
        loadingConfirm={false}
        maxWidth="420px"
      />
      <DrawerHistory
        open={historyVisible}
        onClose={() => setHistoryVisible(false)}
      />
      {isAllowed(permissions, 'stock.purchase-request.list') ? (
        <div className={'flex flex-col gap-[8px]'}>
          <PrHeader>
            <div className={'header'}>
              <div className={'header-info'}>
                <ArrowBack
                  className={'cursor-pointer'}
                  onClick={() => router.push('/stock/purchase-request/')}
                />
                <span className={'quotation-id'}>
                  {purchaseReq?.detail?.prOrderNo}
                </span>
                {purchaseReq?.detail?.prOrderApprovals && (
                  <PrRenderStatus
                    status={purchaseReq?.status}
                    approve={
                      approveState?.currentApprove?.userApprove?.name || ''
                    }
                  />
                )}
              </div>
              <div className={'header-action'}>
                {purchaseReq?.status === PurchaseReqStatus.WAITING && (
                  <ActionButton
                    variant="contained"
                    color="Hon"
                    text={RenderConfirmBtnLabel()}
                    onClick={() => {
                      if (approveState?.isApprove) {
                        handleApprove();
                      }
                    }}
                    disabled={getApproveDisable()}
                  />
                )}
                {/* <ActionButton */}
                {/*  variant="outlined" */}
                {/*  color="blueGrey" */}
                {/*  text="ดาวน์โหลดไฟล์" */}
                {/*  borderRadius={'8px'} */}
                {/* /> */}
                <JobDetailKebabWrap>
                  <PopoverAction
                    triggerElement={<MoreHorizRoundedIcon />}
                    customItems={KabubCustomItems}
                  />
                </JobDetailKebabWrap>
              </div>
            </div>
            <div className={'quotation-info'}>
              <div className={'customer-info'}>
                <div className={'detail-item user-display a'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        purchaseReq?.detail?.user?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>
                      {purchaseReq?.detail?.user?.name}
                    </div>
                    <div className={'info'}>ผู้สร้าง</div>
                  </div>
                </div>
                <div className={'detail-item user-display b'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        purchaseReq?.detail?.contact?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>
                      {purchaseReq?.detail?.contact?.name}
                    </div>
                    <div className={'info'}>ตัวแทนจำหน่าย</div>
                  </div>
                </div>
                <div className={'detail-item c'}>
                  <span className={'detail'}>
                    <span className={'bold'}>ที่อยู่ตัวแทนจำหน่าย :</span>
                    {purchaseReq?.detail && (
                      <>
                        {`${purchaseReq?.detail?.contact?.contactType?.name} • เลขประจำตัวผู้เสียภาษี ${purchaseReq?.detail?.contact?.taxNumber} โทร ${purchaseReq?.detail?.contact?.phoneNumber} • ${purchaseReq?.detail?.contact?.email} ${purchaseReq?.detail?.contact?.contactAddress?.address} ${purchaseReq?.detail?.contact?.contactAddress?.subDistrict?.name} ${purchaseReq?.detail?.contact?.contactAddress?.district?.name} ${purchaseReq?.detail?.contact?.contactAddress?.province.name} ${purchaseReq?.detail?.contact?.contactAddress?.zipcode}`}
                      </>
                    )}
                  </span>
                </div>
              </div>
              <div className={'quotation-detail'}>
                <div className={'detail-item b label-value'}>
                  <div className={'title'}>
                    <SvgCalendarIcon />
                    <span>วันที่สร้าง</span>
                  </div>
                  <div className={'value'}>
                    {moment(purchaseReq?.detail?.createdDate).format(
                      'DD/MM/YYYY, HH:mm น.'
                    )}
                  </div>
                </div>
                <div className={'detail-item c label-value'}>
                  <div
                    className={'value'}
                  >{`${purchaseReq?.detail?.prOrderLists?.length} รายการ`}</div>
                  <div className={'title'}>
                    <SvgExportNoteIcon />
                    <span>รายการขอซื้อ</span>
                  </div>
                </div>
                <div className={'detail-item a'}>
                  <span className={'detail'}>
                    <span className={'bold'}>หมายเหตุ</span>
                    {`${purchaseReq?.detail?.note}`}
                  </span>
                </div>
              </div>
            </div>
          </PrHeader>
          <PrContent>
            <div className="table-container">
              <div className="section-title">รายการขอซื้อ</div>
              <TableWrapper>
                <TableContainer
                  component={Paper}
                  elevation={0}
                  sx={{
                    '& .MuiTable-root': {
                      borderCollapse: 'separate',
                      borderSpacing: '0px',
                    },
                    '& .MuiTableHead-root .MuiTableRow-root': {
                      height: '32px',
                    },
                    '& .MuiTableHead-root .MuiTableCell-root': {
                      padding: '4px 16px',
                      fontSize: '12px',
                    },
                    '& .MuiTableBody-root .MuiTableRow-root': {
                      height: '72px',
                    },
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell align="center" sx={{ width: '50px' }}>
                          #
                        </TableCell>
                        <TableCell>รายการขอซื้อ</TableCell>
                        <TableCell align="center">วัสดุ</TableCell>
                        <TableCell align="center" sx={{ width: '220px' }}>
                          รายการสั่งผลิต (อ้างอิง)
                        </TableCell>
                        <TableCell align="center" sx={{ width: '180px' }}>
                          แบรนด์
                        </TableCell>
                        <TableCell align="right" sx={{ width: '180px' }}>
                          จำนวน
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {purchaseReq &&
                      purchaseReq.detail &&
                      purchaseReq.detail.prOrderLists?.length > 0 ? (
                        purchaseReq.detail.prOrderLists.map(
                          (field: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell align="center">{index + 1}</TableCell>
                              <TableCell>
                                <div>
                                  <div className="flex gap-4">
                                    {/* <div className="relative w-8 h-8"> */}
                                    {/*  <Image */}
                                    {/*    src={ */}
                                    {/*      field.rawMaterial.imageUrl || */}
                                    {/*      '/icons/icon-default.png' */}
                                    {/*    } */}
                                    {/*    alt="" */}
                                    {/*    width={40} */}
                                    {/*    height={40} */}
                                    {/*    className="object-cover rounded !min-w-[40px]" */}
                                    {/*  /> */}
                                    {/* </div> */}
                                    <div className="flex flex-col">
                                      <span className="font-medium">
                                        {field.rawMaterial.name ||
                                          'ไม่ระบุชื่อ'}
                                        {field.rawMaterial.subMaterialDetail
                                          ?.side
                                          ? ` • ${field.rawMaterial.subMaterialDetail.side} ด้าน`
                                          : ''}
                                      </span>
                                      <span className="text-sm text-gray-600">
                                        {field.rawMaterial.rawMaterialNo || '-'}
                                        {field.rawMaterial.itemSize?.name
                                          ? ` • ${field.rawMaterial.itemSize.name}`
                                          : ''}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell align="center">
                                {field.rawMaterial.material?.name}
                              </TableCell>
                              <TableCell align="center">
                                <div>{field.jobNo || 'ไม่มี'}</div>
                              </TableCell>
                              <TableCell align="center">
                                <div>{field.brand.name}</div>
                              </TableCell>
                              <TableCell align="right">
                                <div>{`${field.amount} ${field.rawMaterial.countDimension.name}`}</div>
                              </TableCell>
                            </TableRow>
                          )
                        )
                      ) : (
                        <TableRow>
                          <TableCell
                            align="center"
                            colSpan={6}
                            sx={{ color: '#dbe2e5' }}
                          >
                            ไม่มีรายการขอซื้อ
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </TableWrapper>
            </div>
            <div className="approval-container">
              <div className="section-title">ผู้อนุมัติ</div>
              <div className={'pr-info-content'}>
                {purchaseReq &&
                  purchaseReq.detail?.prOrderApprovals.map(
                    (item: any, index: any) => {
                      return (
                        <div
                          key={index}
                          className={`content-item ${index === 0 && 'bd'}`}
                        >
                          <div className={'approval'}>
                            <div className={'avatar'}>
                              <Image
                                src={
                                  item.userApprove.imageUrl ||
                                  '/images/product/empty-product.svg'
                                }
                                width={40}
                                height={40}
                                alt=""
                                style={{
                                  borderRadius: '50%',
                                  objectFit: 'cover',
                                }}
                              />
                            </div>
                            <div className={'title-info'}>
                              <div className={'name flex gap-2 items-center'}>
                                <span>
                                  {`${item.userApprove.name || ''} ${
                                    item.userApprove.name ? ` • ` : ''
                                  }`}
                                </span>
                                <span>
                                  {`${
                                    item.role.sort === 1
                                      ? 'ผู้อนุมัติขั้นแรก'
                                      : 'ผู้อนุมัติขั้นสูง'
                                  }`}
                                </span>
                                {item.status === 'รออนุมัติ' && (
                                  <span>
                                    <ApproveStatus
                                      color={'#F9A925'}
                                      isIcon={true}
                                    >
                                      <SvgScheduleFillIcon />
                                      <span>{item.status}</span>
                                    </ApproveStatus>
                                  </span>
                                )}
                                {item.status === 'อนุมัติ' && (
                                  <>
                                    <span
                                      className={
                                        'text-[#B0BEC5] text-[12px] font-[400]'
                                      }
                                    >
                                      {`เมื่อวันที่ ${moment(
                                        item.modifiedDate
                                      ).format('DD/MM/YY, HH:mm น.')}`}
                                    </span>
                                    <span>
                                      <ApproveStatus
                                        color={'#8BC34A'}
                                        isIcon={true}
                                      >
                                        <SvgCheckCircleFillIcon />
                                        <span>{item.status}</span>
                                      </ApproveStatus>
                                    </span>
                                  </>
                                )}
                                {item.status === 'ไม่อนุมัติ' && (
                                  <>
                                    <span
                                      className={
                                        'text-[#B0BEC5] text-[12px] font-[400]'
                                      }
                                    >
                                      {`เมื่อวันที่ ${moment(
                                        item.modifiedDate
                                      ).format('DD/MM/YY, HH:mm น.')}`}
                                    </span>
                                    <span>
                                      <ApproveStatus
                                        color={'#D32F2F'}
                                        isIcon={true}
                                      >
                                        <SvgPaperCancelIcon />
                                        <span>{item.status}</span>
                                      </ApproveStatus>
                                    </span>
                                  </>
                                )}
                              </div>
                              <div className={'detail'}>
                                {item.userApprove.name ? (
                                  <>{`${item.role.name} • ${item.userApprove.email}`}</>
                                ) : (
                                  <div className={'text-gray-400'}>
                                    {`ยังไม่ได้เลือกผู้อนุมัติใบขอซื้อขั้น${
                                      item.sort === 1 ? 'แรก' : 'สูง'
                                    }`}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    }
                  )}
              </div>
            </div>
          </PrContent>
        </div>
      ) : (
        <NotPermission />
      )}
    </Container>
  );
};

PurchaseRequestDetail.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default PurchaseRequestDetail;

const Container = styled.div`
  background: #f5f7f8;
`;

const PrHeader = styled.div`
  background: #fff;
  border-bottom: 1px solid #dbe2e5;
  padding: 40px;
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-info {
      display: flex;
      align-items: center;
      column-gap: 16px;
      .quotation-id {
        color: #263238;
        font-size: 40px;
        font-weight: 600;
        letter-spacing: 0.4px;
      }
    }
    .header-action {
      display: flex;
      align-items: center;
      column-gap: 16px;
    }
  }
  .quotation-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    .user-display {
      .avatar {
        display: flex;
        align-items: center;
      }
      .label {
        .name {
          font-size: 14px;
          font-weight: 600;
        }
        .info {
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
    .customer-info {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item.a {
        display: flex;
        align-items: center;
        column-gap: 16px;
        padding: 24px;
        border-bottom: 1px solid #dbe2e5;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.b {
        border-bottom: 1px solid #dbe2e5;
        display: flex;
        align-items: center;
        column-gap: 16px;
        padding: 24px;
      }
      .detail-item.c {
        grid-column: span 2 / span 2;
        color: #263238;
        font-size: 12px;
        font-weight: 400;
        line-height: normal;
        padding: 24px;
        .bold {
          font-weight: 600;
          margin-right: 8px;
        }
        .detail {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
    .label-value {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 24px;
      row-gap: 8px;
      .title {
        display: flex;
        column-gap: 8px;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
      }
      .value {
        font-size: 14px;
        font-weight: 600;
      }
    }
    .quotation-detail {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item.b {
        border-bottom: 1px solid #dbe2e5;
        border-right: 1px solid #dbe2e5;
      }

      .detail-item.c {
        border-bottom: 1px solid #dbe2e5;
      }

      .detail-item.a {
        grid-column: span 2 / span 2;
        color: #263238;
        font-size: 12px;
        font-weight: 400;
        line-height: normal;
        padding: 24px;
        .bold {
          font-weight: 600;
          margin-right: 8px;
          text-decoration: underline;
        }
        .detail {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }
`;

const PrContent = styled.div`
  border-top: 1px solid #dbe2e5;
  background: #fff;
  padding: 40px;
  display: flex;
  flex-direction: column;
  row-gap: 28px;
  .table-container {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    .section-title {
      color: #263238;
      font-size: 22px;
      font-weight: 600;
      line-height: 1;
    }
  }
  .approval-container {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    .section-title {
      color: #263238;
      font-size: 18px;
      font-weight: 600;
      line-height: 1;
    }
    .pr-info-content {
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      .content-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        height: 82.5px;
        .approval {
          width: 100%;
          height: 48px;
          background: #fff;
          display: flex;
          align-items: center;
          column-gap: 16px;
          position: relative;
          .avatar {
            display: flex;
            align-items: center;
          }
          .title-info {
            .name {
              font-size: 14px;
              font-weight: 700;
            }
            .detail {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
      .content-item.bd {
        border-bottom: 1px solid #dbe2e5;
      }
    }
  }
`;

const JobDetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;

const TableWrapper = styled.div`
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #dbe2e5;
    border-radius: 16px !important;
  }
  .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root {
    border-bottom: none;
  }
`;

const ApproveStatus = styled.div<{
  color: string;
  isIcon: boolean;
}>`
  display: flex;
  padding: 8px ${({ isIcon }) => (isIcon ? '8px' : '16px')};
  align-items: center;
  gap: 8px;
  color: ${({ color }) => color};
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.14px;
  svg {
    path {
      fill: ${({ color }) => color};
    }
  }
`;
