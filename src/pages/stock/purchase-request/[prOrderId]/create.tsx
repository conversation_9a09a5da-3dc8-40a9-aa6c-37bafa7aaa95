import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  purchaseRequestSelector,
  setPurchaseRequest,
} from '@/store/features/purchase-request';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import { useRouter } from 'next/router';
import { ArrowBack, InfoOutlined } from '@mui/icons-material';
import SvgScheduleFillIcon from '@/components/svg-icon/SvgScheduleFillIcon';
import SvgCheckCircleFillIcon from '@/components/svg-icon/SvgCheckCircleFillIcon';
import SvgPaperCancelIcon from '@/components/svg-icon/SvgPaperCancelIcon';
import { PurchaseReqStatus } from '@/store/features/purchase-request/types';
import PrUpdateForm from '@/components/purchase-request/PrUpdateForm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CreatePurchaseRequest = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { prOrderId } = router.query;
  const dispatch = useAppDispatch();
  const { purchaseReq } = useAppSelector(purchaseRequestSelector);
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  // const { data: prData, mutate: fetchPrData } = useSWR(
  //   prOrderId ? `/pr-order/${prOrderId}` : null,
  //   () => apiPurchaseRequest.getById(prOrderId) as any
  // );
  const [prData, setPrData] = useState<any>(null);
  useEffect(() => {
    fetchPrData();
  }, [prOrderId]);

  const fetchPrData = async () => {
    const res: any = await apiPurchaseRequest.getById(prOrderId);
    if (!res.isError) {
      setPrData(res);
    } else {
      router.push('/stock/purchase-request/');
    }
  };

  useEffect(() => {
    dispatch(setPurchaseRequest({ status: prData?.status, detail: prData }));
  }, [prData]);

  return (
    <Container>
      <PrNav>
        <ArrowBack
          className={'icon back'}
          onClick={() => router.push('/stock/purchase-request')}
        />
        <span>สร้างรายการขอซื้อ</span>
        <InfoOutlined className={'icon info'} />
      </PrNav>
      {isAllowed(permissions, 'stock.purchase-request.list') ? (
        <div className={'flex flex-col gap-[8px]'}>
          <PrHeader>
            <span className={'pr-id'}>{purchaseReq?.detail?.prOrderNo}</span>
            <PrRenderStatus status={purchaseReq?.status} />
          </PrHeader>
          <PrContent>
            <PrUpdateForm />
          </PrContent>
        </div>
      ) : (
        <NotPermission />
      )}
    </Container>
  );
};

CreatePurchaseRequest.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default CreatePurchaseRequest;

const Container = styled.div``;
const PrNav = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px 0 16px;
  border-bottom: 1px solid #dbe2e5;
  background: #fff;

  .icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  span {
    font-size: 22px;
    font-weight: 600;
  }
  .info {
    color: #dbe2e5;
  }
`;
const PrHeader = styled.div`
  height: 120px;
  background: #fff;
  border-bottom: 1px solid #dbe2e5;
  display: flex;
  align-items: center;
  column-gap: 16px;
  padding: 0 40px;
  .pr-id {
    color: #263238;
    font-size: 40px;
    font-weight: 600;
    letter-spacing: 0.4px;
  }
`;
const PrContent = styled.div`
  border-top: 1px solid #dbe2e5;
  background: #fff;
`;

export const PrRenderStatus = ({
  status,
  approve,
}: {
  status: number;
  approve?: string;
}) => {
  switch (status) {
    case PurchaseReqStatus.DRAFT:
      return (
        <PrStatus
          color={'#000'}
          background={'#f5f7f8'}
          borderColor={'#dbe2e5'}
          radius={'8px'}
          isIcon={false}
        >
          แบบร่าง
        </PrStatus>
      );
    case PurchaseReqStatus.WAITING:
      return (
        <PrStatus
          color={'#F9A925'}
          background={'#FFF5D3'}
          borderColor={'#F9A925'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgScheduleFillIcon />
          <span>{`รออนุมัติใบขอซื้อ ${approve ? `• ${approve}` : ''}`}</span>
        </PrStatus>
      );
    case PurchaseReqStatus.APPROVED:
      return (
        <PrStatus
          color={'#8BC34A'}
          background={'#E6F8CF'}
          borderColor={'#8BC34A'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgCheckCircleFillIcon />
          <span>อนุมัติสำเร็จ</span>
        </PrStatus>
      );
    case PurchaseReqStatus.REJECT:
      return (
        <PrStatus
          color={'#D32F2F'}
          background={'#FDE8EF'}
          borderColor={'#D32F2F'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgPaperCancelIcon />
          <span>ไม่อนุมัติ</span>
        </PrStatus>
      );
    case PurchaseReqStatus.CANCEL:
      return (
        <PrStatus
          color={'#D32F2F'}
          background={'#FDE8EF'}
          borderColor={'#D32F2F'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgPaperCancelIcon />
          <span>ยกเลิกใบขอซื้อ</span>
        </PrStatus>
      );
    default:
  }
  return null;
};

const PrStatus = styled.div<{
  color: string;
  background: string;
  borderColor: string;
  radius: string;
  isIcon: boolean;
}>`
  display: flex;
  height: 32px;
  padding: 8px ${({ isIcon }) => (isIcon ? '8px' : '16px')};
  align-items: center;
  gap: 8px;
  border-radius: ${({ radius }) => radius};
  border: 1px solid ${({ borderColor }) => borderColor};
  background: ${({ background }) => background};
  color: ${({ color }) => color};
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.14px;
  svg {
    path {
      fill: ${({ color }) => color};
    }
  }
`;
