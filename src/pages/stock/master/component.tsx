import React, { Fragment, ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { ACTION } from '@/utils/helper';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import { useAppDispatch, useAppSelector } from '@/store';
import { FiltersType } from '@/types/app';
import useSWR from 'swr';
import apiMasterComponent from '@/services/stock/master-component';
import { setSnackBar } from '@/store/features/alert';
import MasterComponentForm from '@/components/master/MasterComponentForm';
import apiMaster from '@/services/stock/master';
import apiComponentType from '@/services/stock/component-type';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

type MasterComponentFilter = FiltersType & {
  search: string | null;
  masterId: number | null;
  componentCategoryId: number | null;
  componentTypeId: number | null;
};

const fetcherMaster = async () => {
  const response = await apiMaster.getAll({ page: 0, size: 100 }); // todo change api
  return response.data;
};

const fetcherComponent = async () => {
  const response = await apiComponentType.getAllByMaster({ masterId: 1 });
  return response.data;
};

const fetcher = async (url: string, params: MasterComponentFilter) => {
  const response = await apiMasterComponent.getAll(params);
  return response.data;
};

const key = '/master/component';

const MasterComponent = ({ authorities }: { authorities: string[] }) => {
  const title = 'Master Component';
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [action, setAction] = useState(ACTION.CREATE);
  const [dataSelect, setDataSelect] = useState<any>(null);

  const { data: masterList } = useSWR('/master/list', fetcherMaster, {
    revalidateOnFocus: true,
  });

  const { data: componentList } = useSWR(
    '/master/component',
    fetcherComponent,
    {
      revalidateOnFocus: true,
    }
  );

  const [filters, setFilters] = useState<MasterComponentFilter>({
    page: 0,
    size: 20,
    masterId: null,
    search: null,
    componentCategoryId: null,
    componentTypeId: null,
  });

  const { data, mutate } = useSWR(
    [
      key,
      {
        page: filters.page,
        size: filters.size,
        search: filters.search,
        masterId: filters.masterId,
        componentCategoryId: filters.componentCategoryId,
        componentTypeId: filters.componentTypeId,
      },
    ],
    ([
      url,
      { page, size, search, masterId, componentCategoryId, componentTypeId },
    ]) =>
      fetcher(url, {
        page,
        size,
        search,
        masterId,
        componentCategoryId,
        componentTypeId,
      })
  );

  async function reloadData() {
    const newData = await fetcher(key, {
      page: filters.page,
      size: filters.size,
      search: filters.search,
      masterId: filters.masterId,
      componentCategoryId: filters.componentCategoryId,
      componentTypeId: filters.componentTypeId,
    });
    await mutate({ ...data, ...newData }, { revalidate: false });
  }

  function onOpen(value: any, action: ACTION) {
    setAction(action);
    setOpenModal(true);
    setDataSelect(value);
  }

  function onClose() {
    setOpenModal(false);
  }

  function onCloseAction() {
    setOpenDeleteModal(false);
    setDataSelect(null);
  }

  function selectDataAction(value: any, action: ACTION) {
    setAction(action);
    setOpenDeleteModal(true);
    setDataSelect(value);
  }

  async function onDelete() {
    setLoading(true);
    const res = await apiMasterComponent.delete(dataSelect.id);
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
      await reloadData();
    }
    onCloseAction();
    setDataSelect(null);
    setLoading(false);
  }

  const columns: GridColDef[] = [
    // {
    //   field: 'no',
    //   headerName: '#',
    //   editable: false,
    //   headerAlign: 'center',
    //   align: 'center',
    //   maxWidth: 50,
    //   flex: 1,
    //   disableColumnMenu: true,
    //   sortable: false,
    //   renderCell: (params: any) => {
    //     console.log('params', params);
    //     return <div>{params.id}</div>;
    //   },
    // },
    {
      field: 'master',
      headerName: 'Master',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.master.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'component',
      headerName: 'Component',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.component.name}
            </div>
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 64,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'master.master-component.delete'
                  ),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    selectDataAction(params.row, ACTION.DELETE);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];

  return (
    <Fragment>
      <MasterComponentForm
        title={`${
          action === ACTION.CREATE ? 'สร้าง' : 'แก้ไข'
        } Master Component`}
        openModal={openModal}
        action={action}
        onClose={onClose}
        reloadData={reloadData}
        initialValues={dataSelect}
        masterList={masterList}
        componentList={componentList}
      />
      <AppModalConfirm
        open={openDeleteModal}
        onClickClose={() => {
          setDataSelect(null);
          setOpenDeleteModal(false);
        }}
        confirmTitle={`ลบ Master component`}
        confirmDescription={`คุณต้องการที่จะลบ ${dataSelect?.name || ''} ?`}
        loadingConfirm={loading}
        onConfirm={() => {
          onDelete();
        }}
      />
      <ProductNav title="Master Component" showBorderBottom={false}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text={`สร้าง ${title}`}
            borderRadius={'20px'}
            onClick={() => {
              onOpen(null, ACTION.CREATE);
            }}
            disabled={!isAllowed(permissions, 'master.master-component.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'master.master-component.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={data?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={224} />
                  <DataGrid
                    hideFooter={true}
                    rows={data?.content ? data.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={data?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={data?.totalElements ? data.totalElements : 0}
                    handleChangeFilters={(newValues: any) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </Fragment>
  );
};

MasterComponent.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default MasterComponent;
