import React, { Fragment, ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { ACTION } from '@/utils/helper';
import apiMaster from '@/services/stock/master';
import useSWR from 'swr';
import AppPagination from '@/components/global/AppPagination';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { FiltersType } from '@/types/app';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import MasterForm from '@/components/master/MasterForm';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import apiMasterCategory from '@/services/stock/master-category';
import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

type FilterType = {
  size: number;
  page: number;
  ascending?: boolean;
  masterCategoryId?: number;
};

const fetcherMasterCategory = async () => {
  const response = await apiMasterCategory.getAll();
  return response.data;
};

const fetcher = async (url: string, params: FilterType) => {
  const response = await apiMaster.getAll(params);
  return response.data;
};

const key = '/master/category';

const Master = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const router = useRouter();
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [action, setAction] = useState(ACTION.CREATE);
  const [masterSelect, setMasterSelect] = useState<any>(null);

  const [filters, setFilters] = useState<FiltersType>({
    page: 0,
    size: 10,
    searchTerm: '',
    ascending: false,
  });
  const [masterCategoryId] = useState<any>(null);
  const { data: masterCategoryList } = useSWR(
    '/master/category',
    fetcherMasterCategory,
    {
      revalidateOnFocus: true,
    }
  );
  const { data, mutate } = useSWR(
    [
      key,
      {
        page: filters.page,
        size: filters.size,
        ascending: filters?.ascending || false,
        masterCategoryId: masterCategoryId,
      },
    ],
    ([url, { page, size, ascending, masterCategoryId }]) =>
      fetcher(url, {
        page,
        size,
        ascending,
        masterCategoryId,
      })
  );
  async function reloadData() {
    const newData = await fetcher(key, {
      page: filters.page,
      size: filters.size,
      ascending: filters?.ascending || false,
      masterCategoryId: masterCategoryId,
    });
    await mutate({ ...data, ...newData }, { revalidate: false });
  }

  function onOpen(value: any, action: ACTION) {
    setAction(action);
    setOpenModal(true);
    setMasterSelect(value);
  }

  function onClose() {
    setOpenModal(false);
  }

  function onCloseAction() {
    setOpenDeleteModal(false);
    setMasterSelect(null);
  }

  function selectDataAction(value: any, action: ACTION) {
    setAction(action);
    setOpenDeleteModal(true);
    setMasterSelect(value);
  }

  async function onDelete() {
    setLoading(true);
    const res = await apiMaster.delete(masterSelect.id);
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
      await reloadData();
    }
    onCloseAction();
    setLoading(false);
  }

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'รายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={params.row.imageUrl || '/images/product/empty-product.svg'}
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'masterCategory',
      headerName: 'Category',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        const positionName = (position: number) => {
          switch (position) {
            case 1:
              return 'ก่อนพิมพ์';
            default:
              return 'หลังพิมพ์';
          }
        };
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {(
              <div>{`${params.row.masterCategory.name} ${
                params.row.position !== 0
                  ? `(${positionName(params.row.position)})`
                  : ''
              }`}</div>
            ) || <span style={{ color: '#CFD8DC' }}>ไม่มี</span>}
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row.description || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                disabled={!isAllowed(permissions, 'master.master-type.list')}
                onClick={async () => {
                  await router.push(`/stock/master/${params.row.id}`);
                }}
              >
                รายละเอียด
              </Button>
              <PopoverAction
                triggerElement={
                  <div className="kebab">
                    <div className="dot" />
                  </div>
                }
                customItems={[
                  {
                    disabled: !isAllowed(
                      permissions,
                      'master.master-type.update'
                    ),
                    IconElement: () => <SvgPencilIcon />,
                    title: 'แก้ไข',
                    onAction: () => {
                      onOpen(params.row, ACTION.UPDATE);
                    },
                  },
                  {
                    disabled: !isAllowed(
                      permissions,
                      'master.master-type.delete'
                    ),
                    IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                    title: 'ลบรายการ',
                    onAction: () => {
                      selectDataAction(params.row, ACTION.DELETE);
                    },
                    cssProps: {
                      color: '#D32F2F',
                      '&:hover': {
                        backgroundColor: '#FDE8EF',
                      },
                    },
                  },
                ]}
              />
            </div>
          </>
        );
      },
    },
  ];

  return (
    <Fragment>
      <MasterForm
        title={`${action === ACTION.CREATE ? 'สร้าง' : 'แก้ไข'} Master`}
        openModal={openModal}
        action={action}
        onClose={onClose}
        reloadData={reloadData}
        initialValues={masterSelect}
        masterCategoryList={masterCategoryList}
      />
      <AppModalConfirm
        open={openDeleteModal}
        onClickClose={() => {
          onCloseAction();
        }}
        confirmTitle={`ลบ ${masterSelect?.name || ''}`}
        confirmDescription={`คุณต้องการที่จะลบ ${masterSelect?.name || ''}`}
        loadingConfirm={loading}
        onConfirm={() => {
          onDelete();
        }}
      />
      <ProductNav title="Master" showBorderBottom={false}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้าง Master"
            borderRadius={'20px'}
            onClick={() => {
              onOpen(null, ACTION.CREATE);
            }}
            disabled={!isAllowed(permissions, 'master.master-type.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'master.master-type.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={data?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={164} />
                  <DataGrid
                    hideFooter={true}
                    rows={data?.content ? data.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={data?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={data?.totalElements ? data.totalElements : 0}
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </Fragment>
  );
};

Master.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default Master;
