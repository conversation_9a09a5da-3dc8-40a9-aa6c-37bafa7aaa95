import React, { Fragment, ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import useSWR, { useSWRConfig } from 'swr';
import AppPagination from '@/components/global/AppPagination';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { useRouter } from 'next/router';
import apiMasterConfig from '@/services/stock/master-config';
import apiMaster from '@/services/stock/master';
import { isEmpty } from 'lodash';
import ModalConfigMaterial from '@/components/master/ModalConfigMaterial';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import Image from 'next/image';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import PopoverAction from '@/components/PopoverActionn';
import { isAllowed } from '@/utils/permission';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import NotPermission from '@/components/NotPermission';

const key = '/master-config/material';

const fetcher = async (url: string, params: any) => {
  const response = await apiMasterConfig.getListMasterConfigMaterial(params);
  return response.data;
};
const fetcherMasterById = async (url: string, masterId: any) => {
  const response = await apiMaster.getById(masterId);
  return response.data;
};
const initialOpenDeleteModal = {
  status: false,
  id: null,
  name: '',
};
const MasterConfigMaterialPage = ({
  authorities,
}: {
  authorities: string[];
}) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const { masterId } = router.query;
  const { mutate } = useSWRConfig();
  const [openDeleteModal, setOpenDeleteModal] = useState<any>(
    initialOpenDeleteModal
  );
  const [open, setOpen] = useState<boolean>(false);
  const [loadingDelete] = useState<boolean>(false);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    masterId,
    materialId: null,
  });

  const { data } = useSWR(
    [
      key,
      {
        page: filters.page,
        size: filters.size,
        masterId,
        materialId: filters.materialId,
      },
    ],
    ([url, { page, size, masterId, materialId }]) =>
      fetcher(url, {
        page,
        size,
        masterId,
        materialId,
      })
  );
  const handleReloadList = async () => {
    await mutate(
      [
        key,
        {
          page: filters.page,
          size: filters.size,
          masterId,
          materialId: filters.materialId,
        },
      ],
      (cachedData: any) => {
        if (!cachedData) {
          console.log('cachedData undefined');
          return {
            content: [],
            totalElements: 0,
          };
        }
        return {
          ...cachedData,
          content: cachedData.content,
        };
      },
      true
    );
  };
  const { data: masterById } = useSWR(
    ['/master', masterId],
    ([url, masterId]) => fetcherMasterById(url, masterId)
  );

  const columns: GridColDef[] = [
    {
      field: 'subMaterial.name',
      headerName: 'Sub-Material',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.subMaterial.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'master',
      headerName: 'Master',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="overflow-hidden text-ellipsis">
            {params.row.master.name}
          </div>
        );
      },
    },
    {
      field: 'status',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 90,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{ m: 1 }}
              checked={params.row.isProductConfig}
              onClick={async (e: any) => {
                await handleChangeStatusProductConfig(
                  params.row.id,
                  e.target.checked
                );
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 64,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'master.master-type.delete'
                  ),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    setOpenDeleteModal({
                      status: true,
                      id: params.row.id,
                      name: params.row.subMaterial.name,
                    });
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const handleDelete = async () => {
    setOpenDeleteModal({
      ...openDeleteModal,
      status: false,
    });
    await mutate(
      [
        key,
        {
          page: filters.page,
          size: filters.size,
          masterId,
          materialId: filters.materialId,
        },
      ],
      (cachedData: any) => {
        if (!cachedData) {
          console.log('cachedData undefined');
          return {
            content: [],
            totalElements: 0,
          };
        }
        return {
          ...cachedData,
          content: cachedData.content.filter(
            (item: any) => item.id !== openDeleteModal.id
          ),
        };
      },
      false
    );

    const res = await apiMaster.deleteMaterialMasterConfigById(
      openDeleteModal.id
    );

    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? 'ลบข้อมูลสำเร็จ' : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );

    if (!res.isError) {
      await mutate(
        [
          key,
          {
            page: filters.page,
            size: filters.size,
            masterId,
            materialId: filters.materialId,
          },
        ],
        (cachedData: any) => {
          if (!cachedData) {
            console.log('cachedData undefined');
            return {
              content: [],
              totalElements: 0,
            };
          }
          return {
            ...cachedData,
            content: cachedData.content.filter(
              (item: any) => item.id !== openDeleteModal.id
            ),
          };
        },
        true
      );
    }
  };

  const handleChangeStatusProductConfig = async (
    id: number,
    check: boolean
  ) => {
    // update in cache
    await mutate(
      [
        key,
        {
          page: filters.page,
          size: filters.size,
          masterId,
          materialId: filters.materialId,
        },
      ],
      (cachedData: any) => {
        if (!cachedData) {
          console.log('cachedData undefined');
          return {
            content: [],
            totalElements: 0,
          };
        }
        return {
          ...cachedData,
          content: cachedData.content.map((item: any) =>
            item.id === id ? { ...item, isProductConfig: check } : item
          ),
        };
      },
      false
    );

    const res = await apiMaster.updateMaterialMasterConfigById({
      id,
      isProductConfig: check,
    });

    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? 'ตั้งค่าสำเร็จ' : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );

    if (!res.isError) {
      await mutate(
        [
          key,
          {
            page: filters.page,
            size: filters.size,
            masterId,
            materialId: filters.materialId,
          },
        ],
        (cachedData: any) => {
          if (!cachedData) {
            console.log('cachedData undefined');
            return {
              content: [],
              totalElements: 0,
            };
          }
          return {
            ...cachedData,
            content: cachedData.content.map((item: any) =>
              item.id === id ? { ...item, isProductConfig: check } : item
            ),
          };
        },
        true
      );
    }
  };

  return (
    <>
      <AppModalConfirm
        open={openDeleteModal.status}
        isReason={false}
        onClickClose={() => {
          setOpenDeleteModal(false);
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ลบรายการ`}
        confirmDescription={`”ยืนยันที่จะลบ ${openDeleteModal.name} ออกจากรายการ”`}
        loadingConfirm={loadingDelete}
        onConfirm={async () => {
          await handleDelete();
        }}
      />
      <ModalConfigMaterial
        open={open}
        handleClose={() => {
          setOpen(false);
        }}
        title="Config Material"
        handleReloadList={async () => {
          await handleReloadList();
        }}
      />
      <ProductNav
        title={
          !isEmpty(masterById)
            ? `${masterById.name} | ${masterById.masterCategory.name}`
            : ''
        }
        showBorderBottom={false}
        backUrl={'/stock/master'}
      >
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="Config Material"
            borderRadius={'20px'}
            onClick={() => {
              setOpen(true);
            }}
            disabled={!isAllowed(permissions, 'master.master-type.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'master.master-type.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={data?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={64} />
                  <DataGrid
                    hideFooter={true}
                    rows={data?.content ? data.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={data?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={data?.totalElements ? data.totalElements : 0}
                    handleChangeFilters={(newValues: any) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </>
  );
};

MasterConfigMaterialPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default MasterConfigMaterialPage;
