import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import { useRouter } from 'next/router';
import apiRawMaterial from '@/services/stock/raw-material';
import styled from '@emotion/styled';
import Image from 'next/image';
import { Autorenew, MoreHoriz } from '@mui/icons-material';
import { Button, Menu, MenuItem } from '@mui/material';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const RawMaterialDetail = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rawMaterialDetail, setRawMaterialDetail] = useState<any>();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  useEffect(() => {
    if (id) {
      fetchRawMaterialDetail();
    } else {
      setRawMaterialDetail(undefined);
    }
  }, [id]);

  const fetchRawMaterialDetail = async () => {
    const detail = await apiRawMaterial.getRawMaterialById(id);
    if (detail.status) {
      setRawMaterialDetail(detail.data);
    } else {
      setRawMaterialDetail(undefined);
    }
  };

  return (
    <>
      <ProductNav
        title={rawMaterialDetail?.name || ''}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/stock/raw-material'}
      />
      {isAllowed(permissions, 'stock.raw-material.list') ? (
        <MainContainer>
          <DetailHeader>
            <div className={'title'}>{rawMaterialDetail?.name || ''}</div>
            <div className={'action'}>
              <div className={'date'}>
                <div>ข้อมูล ณ 12/04/2024, 12:34</div>
                <Autorenew className={'w-[20px] h-[20px]'} />
              </div>
              <div className={'action-select'}></div>
              <div className={'more'}>
                <Button
                  variant="outlined"
                  size="small"
                  aria-controls={open ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                  onClick={handleClick}
                >
                  <MoreHoriz />
                </Button>
                <Menu
                  id="basic-menu"
                  anchorEl={anchorEl}
                  open={open}
                  onClose={handleClose}
                  MenuListProps={{
                    'aria-labelledby': 'basic-button',
                  }}
                >
                  <MenuItem onClick={handleClose}>...</MenuItem>
                </Menu>
              </div>
            </div>
          </DetailHeader>
          <InformationContainer>
            <div className={'grid-item cover-grid'}>
              <div className={'cover'}>
                <Image
                  className={'image'}
                  src={
                    rawMaterialDetail?.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={142}
                  height={142}
                  alt=""
                />
              </div>
            </div>
            <div className={'grid-item info-grid-1'}>
              <div className={'info'}>
                <div className={'info-item'}>
                  <div className={'title'}>รหัส</div>
                  <div className={'value'}>
                    {rawMaterialDetail?.rawMaterialNo}
                  </div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>ชื่อสินค้า</div>
                  <div className={'value'}>{rawMaterialDetail?.name}</div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>สเปค</div>
                  <div className={'value'}>
                    {rawMaterialDetail?.itemSize.name}
                  </div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>ยี่ห้อ</div>
                  <div className={'value'}>{rawMaterialDetail?.brand.name}</div>
                </div>
              </div>
            </div>
            <div className={'grid-item info-grid-2'}>
              <div className={'info'}>
                <div className={'info-item'}>
                  <div className={'title'}>รายละเอียด</div>
                  <div className={'value'}>
                    {rawMaterialDetail?.description}
                  </div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>การหยิบสินค้า</div>
                  <div className={'value'}>
                    {rawMaterialDetail?.pickingType.name}
                  </div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>ข้อมูลบังคับ</div>
                  <div className={'value'}>{`${
                    rawMaterialDetail?.isLotExpirationDate
                      ? 'ล็อต/วันหมดอายุ'
                      : ''
                  } ${
                    rawMaterialDetail?.isSerialNumber &&
                    rawMaterialDetail?.isLotExpirationDate
                      ? ','
                      : ''
                  } ${
                    rawMaterialDetail?.isSerialNumber ? 'Serial Number' : ''
                  }`}</div>
                </div>
                <div className={'info-item'}>
                  <div className={'title'}>การซื้อขาย</div>
                  <div className={'value'}>
                    <div className={'flex gap-2'}>
                      {rawMaterialDetail?.isSell && (
                        <div className={'block-text'}>
                          <div className={'flex items-center'}>
                            <Image
                              className={'image'}
                              src={'/icons/check-cicle-over.svg'}
                              width={18}
                              height={18}
                              alt=""
                            />
                          </div>
                          <div>สามารถขายได้</div>
                        </div>
                      )}
                      {rawMaterialDetail?.isPurchase && (
                        <div className={'block-text'}>
                          <div className={'flex items-center'}>
                            <Image
                              className={'image'}
                              src={'/icons/check-cicle-over.svg'}
                              width={18}
                              height={18}
                              alt=""
                            />
                          </div>
                          <div>สามารถซื้อได้</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </InformationContainer>
          <StatContainer></StatContainer>
          <TableContainer></TableContainer>
        </MainContainer>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

RawMaterialDetail.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default RawMaterialDetail;

const DetailHeader = styled.div`
  height: 120px;
  background: #fff;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 40px;
    font-weight: 600;
  }
  .action {
    display: flex;
    column-gap: 16px;
    .date {
      font-size: 12px;
      font-weight: 400;
      display: flex;
      align-items: center;
      column-gap: 8px;
      color: #90a4ae;
    }
    .more {
      .MuiButtonBase-root {
        min-width: 40px !important;
        min-height: 40px !important;
        padding: 0 !important;
      }
    }
  }
`;
const MainContainer = styled.div`
  width: 100%;
  background: #f5f7f8;
`;
const InformationContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(8, 2fr);
  column-gap: 32px;
  width: 100%;
  padding: 40px;
  background: #fff;
  margin: 8px 0;
  @media screen and (max-width: 1280px) {
    row-gap: 0;
  }
  @media screen and (max-width: 940px) {
    display: flex;
    flex-direction: column;
    width: 100%;
    row-gap: 16px;
  }
  .cover-grid {
    grid-column: 1/2;
    grid-row: 1;
  }
  .info-grid-1 {
    grid-column: 2/5;
    grid-row: 1;
    @media screen and (max-width: 1280px) {
      grid-column: 2/9;
    }
  }
  .info-grid-2 {
    grid-column: 5/9;
    grid-row: 1;
    @media screen and (max-width: 1280px) {
      grid-column: 2/9;
      grid-row: 2;
    }
  }
  .grid-item {
    display: flex;
    column-gap: 24px;
    .cover {
      width: 142px;
      .image {
        border-radius: 16px;
      }
    }
    .info {
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      .info-item {
        display: flex;
        column-gap: 24px;
        font-size: 14px;
        .title {
          color: #90a4ae;
          width: 90px;
        }
        .value {
          .block-text {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #263238;
            border-radius: 20px;
            height: 20px;
            padding: 0 8px 0 2px;
            column-gap: 8px;
            font-size: 12px;
          }
        }
      }
    }
  }
`;
const StatContainer = styled.div``;
const TableContainer = styled.div``;
