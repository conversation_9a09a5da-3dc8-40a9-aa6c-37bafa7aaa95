import React, { ReactElement, useEffect, useState } from 'react';
import 'dayjs/locale/th';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import RawMaterialForm from '@/components/raw-material/RawMaterialForm';
import apiRawMaterial from '@/services/stock/raw-material';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CreateRawMaterial = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rawMaterialDetail, setRawMaterialDetail] = useState<any>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);

  useEffect(() => {
    if (id) {
      fetchRawMaterialDetail();
    }
  }, [id]);

  const fetchRawMaterialDetail = async () => {
    const detail = await apiRawMaterial.getRawMaterialById(id);
    if (detail.status) {
      setRawMaterialDetail(detail.data);
    }
  };

  const createRawMaterial = async (value: any, image: any) => {
    setSubmitting(true);
    const res = await apiRawMaterial.createRawMaterial(value);
    // console.log('res', res);
    if (!res.isError) {
      setDisable(true);
      if (image.isNew) {
        await uploadImage(res.data.id, image.file).then((res) => {
          if (res) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message,
                severity: 'success',
              })
            );
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message,
                severity: 'warning',
              })
            );
          }
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
      }
      await router.push('/stock/raw-material');
    } else if (res.message?.message === 'RawMaterial already exists') {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };

  const uploadImage = async (rawMaterialId: any, file: any) => {
    const formData = new FormData();
    formData.append('rawMaterialId', rawMaterialId);
    formData.append('file', file);

    const response = await apiRawMaterial.saveImage(formData);
    return response.status;
  };

  const updateRawMaterial = async (value: any, image: any) => {
    setSubmitting(true);
    const res = await apiRawMaterial.updateRawMaterial(value);
    // console.log('res', res);
    if (!res.isError) {
      setDisable(true);
      if (image.isNew) {
        await uploadImage(value.id, image.file).then((res) => {
          if (res) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message,
                severity: 'success',
              })
            );
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message,
                severity: 'warning',
              })
            );
          }
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
      }
      await router.push('/stock/raw-material');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };

  return (
    <>
      <ProductNav
        title={`${rawMaterialDetail ? 'แก้ไขวัสดุ' : 'สร้างวัสดุ'}`}
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/stock/raw-material'}
      />
      {(!rawMaterialDetail &&
        isAllowed(permissions, 'stock.raw-material.create')) ||
      (rawMaterialDetail &&
        isAllowed(permissions, 'stock.raw-material.update')) ? (
        <RawMaterialForm
          submitting={submitting}
          disable={disable}
          handleSubmit={async (value: any, image: any) => {
            if (value.id) {
              await updateRawMaterial(value, image);
            } else {
              await createRawMaterial(value, image);
            }
          }}
          initialValues={rawMaterialDetail}
        />
      ) : (
        <NotPermission />
      )}
    </>
  );
};

CreateRawMaterial.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default CreateRawMaterial;
