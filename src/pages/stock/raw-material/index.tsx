import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle, Sort } from '@mui/icons-material';
import { useRouter } from 'next/router';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { OpenDeleteType } from '@/types/category';
import apiRawMaterial from '@/services/stock/raw-material';
import { Button } from '@mui/material';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import SearchInput from '@/components/SearchInput';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import FilterForm from '@/components/raw-material/FilterForm';
import styled from '@emotion/styled';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

const RawMaterialList = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [filterCollapse, setFilterCollapse] = useState<boolean>(false);
  const [filters, setFilters] = useState<{
    size: number;
    page: number;
    searchName?: string | null;
    materialId?: number | null;
    subMaterialId?: number | null;
    subMaterialDetailId?: number | null;
    brandId?: number | null;
    pickingTypeId?: number | null;
  }>({
    page: 0,
    size: 10,
    searchName: null,
    materialId: null,
    subMaterialId: null,
    subMaterialDetailId: null,
    brandId: null,
    pickingTypeId: null,
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    const response = await apiRawMaterial.getList(filters);
    if (response.status) {
      setRows(response.data.content);
      setTotalElements(response.data.totalElements);
    } else {
      setRows([]);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiRawMaterial.deleteRawMaterial(openDelete.id);
      // console.log('res', res);
      if (!res.isError) {
        await fetchData();
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: `${res.message.message}`,
            severity: 'error',
          })
        );
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };
  const handleOpenDelete = async (name: string, id: number) => {
    setOpenDelete({
      name,
      status: true,
      id,
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'rawMaterialNo',
      headerName: 'รหัส',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 108,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'name',
      headerName: 'ชื่อวัสดุ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 3,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={params.row.imageUrl || '/images/product/empty-product.svg'}
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '4px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'material',
      headerName: 'Material',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 150,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row?.material?.name || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'rawMaterialType',
      headerName: 'ประเภท',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 150,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row?.rawMaterialType?.name || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 150,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '98px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              disabled={!isAllowed(permissions, 'stock.raw-material.list')}
              onClick={() => {
                router.push(`/stock/raw-material/${params.row.id}`);
              }}
            >
              รายละเอียด
            </Button>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'stock.raw-material.update'
                  ),
                  IconElement: () => <SvgPencilIcon />,
                  title: 'แก้ไข',
                  onAction: () => {
                    router.push(
                      `/stock/raw-material/create?id=${params.row.id}`
                    );
                  },
                },
                {
                  disabled: !isAllowed(
                    permissions,
                    'stock.raw-material.delete'
                  ),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    handleOpenDelete(params.row.name, params.row.id);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];

  const handleSearch = (searchName: string) => {
    setFilters({ ...filters, searchName: searchName, page: 0 });
  };

  return (
    <>
      <ProductNav title="รายการวัสดุ" showBorderBottom>
        <ActionGroupStyle>
          {/* <Notifications /> */}
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />
          <FilterCollapse
            active={filterCollapse}
            onClick={() => setFilterCollapse(!filterCollapse)}
          >
            <Sort
              style={{
                color: '#263238',
              }}
            />
          </FilterCollapse>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างวัสดุ"
            borderRadius={'20px'}
            onClick={() => {
              router.push('/stock/raw-material/create');
            }}
            disabled={!isAllowed(permissions, 'stock.raw-material.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบวัสดุ?'}
        loadingConfirm={loading}
        onConfirm={() => {
          handleDelete();
        }}
      />
      {/* <StatusTab activeStatus={'TOTAL'} handleClick={() => null} /> */}
      <AppContentStyle>
        <div className="content-wrap">
          {filterCollapse && (
            <FilterForm
              onFilterChange={(value: any) => {
                setFilters({
                  ...filters,
                  materialId: value.materialId,
                  subMaterialId: value.subMaterialId,
                  subMaterialDetailId: value.subMaterialDetailId,
                });
              }}
            />
          )}
          <AppTableStyle $rows={rows}>
            {isAllowed(permissions, 'stock.raw-material.list') ? (
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={150} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    // checkboxSelection={true}
                    rowCount={totalElements || 0}
                    // pageSize={filters.size}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            ) : (
              <NotPermission />
            )}
          </AppTableStyle>
        </div>
      </AppContentStyle>
    </>
  );
};

RawMaterialList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default RawMaterialList;

const FilterCollapse = styled.div<{ active: boolean }>`
  border: 1px solid #dedede;
  height: 40px;
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  cursor: pointer;
  svg {
    path {
      fill: ${({ active }) => (active ? '#626262' : '#dedede')};
    }
  }
  &:hover {
    border: 1px solid #645c5c;
  }
`;
