import React, { ReactElement, useEffect, useRef, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import { Button, Dialog, DialogContent, IconButton } from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiItemSize from '@/services/stock/itemSize';
import { itemSizeListType } from '@/types/itemSize';
import { OpenDeleteType } from '@/types/category';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import ItemSizeForm from '@/components/sizes/ItemSizeForm';
import _, { isEmpty } from 'lodash';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import styled from 'styled-components';
import { setSnackBar } from '@/store/features/alert';
import { FormMode } from '@/components/order/lay/modal/ModalLayCreatePrintSheet';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const DimensionChipStyle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  border: 1px solid #263238;
  font-size: 12px;
  line-height: 1;
  padding: 0 8px;
`;

type openCreateModalType = {
  status: boolean;
  type: FormMode;
};
const ItemSize = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(userSelector);
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialItemSize, setInitialItemSize] = useState<any>({});
  const [openCreateModal, setOpenCreateModal] = useState<openCreateModalType>({
    status: false,
    type: 'create',
  });
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [itemSizeList, setItemSizeList] = useState<itemSizeListType>({
    content: [],
    totalElements: null,
  });

  const wrapperRef = useRef(null);

  const getItemSize = async () => {
    const res = await apiItemSize.getItemSize({
      ...filters,
      name: filters.searchTerm || null,
    });
    if (!res.isError) {
      setItemSizeList({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };

  useEffect(() => {
    getItemSize().then();
  }, [filters, user]);

  const columns: GridColDef[] = [
    {
      field: 'itemSizeName',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.itemSizeName || '-'}</div>;
      },
    },
    {
      field: 'dimensionConfigResponse.configName',
      headerName: 'ขนาด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <DimensionChipStyle>
            {params.row.dimensionConfig.configName || '-'}
          </DimensionChipStyle>
        );
      },
    },
    {
      field: 'count',
      headerName: 'จำนวนย่อย/ใบพิมพ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.size || '0'}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 224,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '98px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              disabled={!isAllowed(permissions, 'stock.sizes.list')}
              onClick={() => {
                router.push(`/stock/setting/sizes/${params.row.id}`);
              }}
            >
              ขนาดย่อย
            </Button>
            <IconButton
              disabled={
                params.row.size > 1 ||
                !isAllowed(permissions, 'stock.sizes.update')
              }
              className={`${params.row.size > 1 && 'opacity-20'}`}
              onClick={() => {
                onEdit(params.row);
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              disabled={!isAllowed(permissions, 'stock.sizes.delete')}
              onClick={() => {
                setOpenDelete({
                  status: true,
                  name: params.row.itemSizeName,
                  id: params.row.id,
                });
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </div>
        );
      },
    },
  ];
  const deleteItemSize = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiItemSize.deleteItemSizeById(openDelete.id);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
        getItemSize();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: `${res.message.message}`,
            severity: 'error',
          })
        );
      }
    }
    setOpenDelete({
      ...openDelete,
      status: false,
      id: null,
    });
    setLoading(false);
  };

  // const handleSearch = (searchTerm: string) => {
  //   setFilters({ ...filters, searchTerm });
  // };

  const getItemSizeById = async (id: number) => {
    const res = await apiItemSize.getItemSizeById(Number(id));
    if (res.status) {
      setInitialItemSize(res.data);
      setOpenCreateModal({
        status: true,
        type: 'edit',
      });
    } else {
      setInitialItemSize({});
    }
  };
  const onEdit = async (data: any) => {
    await getItemSizeById(data.id);
  };
  const onClose = () => {
    setOpenCreateModal({
      ...openCreateModal,
      status: false,
    });
    setInitialItemSize({});
  };

  useEffect(() => {
    if (!openCreateModal.status) {
      setInitialItemSize({});
    }
  }, [openCreateModal.status]);

  const handelSubmit = async (values: any) => {
    setLoading(true);
    if (openCreateModal.type === 'create') {
      const createValues = _.omit(values, ['id']);
      const res = await apiItemSize.addItemSize(createValues);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: 'บันทึกสำเร็จ',
            severity: 'success',
          })
        );
        setOpenCreateModal({
          ...openCreateModal,
          status: false,
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    } else if (openCreateModal.type === 'edit') {
      const res = await apiItemSize.updateItemSize(values);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: 'บันทึกสำเร็จ',
            severity: 'success',
          })
        );
        setOpenCreateModal({
          ...openCreateModal,
          status: false,
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    }
    setLoading(false);
    await getItemSize();
  };

  return (
    <>
      <ProductNav title="ขนาด" showBorderBottom={false}>
        <ActionGroupStyle>
          {/* <SearchInput */}
          {/*  makeSearchValue={(val: string) => { */}
          {/*    handleSearch(val); */}
          {/*  }} */}
          {/* /> */}

          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างขนาดสูงสุด"
            borderRadius={'20px'}
            onClick={() => {
              setOpenCreateModal({
                status: true,
                type: 'create',
              });
            }}
            disabled={!isAllowed(permissions, 'stock.sizes.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'stock.sizes.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={itemSizeList?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={224} />
                  <DataGrid
                    hideFooter={true}
                    rows={itemSizeList?.content ? itemSizeList.content : []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={itemSizeList?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={
                      itemSizeList?.totalElements
                        ? itemSizeList.totalElements
                        : 0
                    }
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
      <Dialog
        open={openCreateModal.status}
        onClose={() => {
          setOpenCreateModal({
            ...openCreateModal,
            status: false,
          });
        }}
      >
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {openCreateModal.type === 'create'
                    ? 'สร้างขนาดสูงสุด'
                    : 'แก้ไขขนาดสูงสุด'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <ItemSizeForm
                  handleSubmit={async (value: any) => {
                    await handelSubmit(value);
                  }}
                  submitting={loading}
                  disable={false}
                  initialValues={initialItemSize}
                />
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={`คุณต้องการที่จะลบ ${openDelete.name}`}
        loadingConfirm={loading}
        onConfirm={async () => {
          await deleteItemSize();
        }}
      />
    </>
  );
};
ItemSize.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default ItemSize;
