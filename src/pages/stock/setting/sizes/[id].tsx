import React, { ReactElement, useEffect, useRef, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { Breadcrumbs, Dialog, DialogContent, IconButton } from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  FormModalStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { FiltersType } from '@/types/app';
import AppPagination from '@/components/global/AppPagination';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Link from 'next/link';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { subItemSizeListType } from '@/types/itemSize';
import apiSubItemSize from '@/services/stock/subItemSize';
import { useRouter } from 'next/router';
import { OpenDeleteType } from '@/types/category';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { numberWithCommas } from '@/utils/number';
import SubItemSizeForm from '@/components/sizes/SubItemSizeForm';
import apiItemSize from '@/services/stock/itemSize';
import _, { isEmpty } from 'lodash';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import styled from 'styled-components';
import { setSnackBar } from '@/store/features/alert';
import { FormMode } from '@/components/order/lay/modal/ModalLayCreatePrintSheet';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const DimensionChipStyle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  border: 1px solid #263238;
  font-size: 12px;
  line-height: 1;
  padding: 0 8px;
`;
type openCreateModalType = {
  status: boolean;
  type: FormMode;
};
const SubItemSize = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(userSelector);
  const router = useRouter();
  const { id } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialSubItemSize, setInitialSubItemSize] = useState<any>(null);
  const [itemSize, setItemSize] = useState<any>(null);
  const [openCreateModal, setOpenCreateModal] = useState<openCreateModalType>({
    status: false,
    type: 'create',
  });
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
    DESC: false,
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [subItemSizeList, setSubItemSizeList] = useState<subItemSizeListType>({
    content: [],
    totalElements: null,
  });
  const wrapperRef = useRef(null);

  const getSubItemSize = async () => {
    const res = await apiSubItemSize.getSubItemSizeById({
      ...filters,
      name: filters.searchTerm || null,
      itemSizeId: id,
    });
    if (!res.isError) {
      setSubItemSizeList({
        content: res.data.content,
        totalElements: res.data.totalElements,
      });
    }
  };

  useEffect(() => {
    getSubItemSize().then();
  }, [filters, user]);

  useEffect(() => {
    if (id) {
      fetchItemSizeById().then();
    }
  }, [id]);

  const fetchItemSizeById = async () => {
    const res = await apiItemSize.getItemSizeById(Number(id));
    if (res.status) {
      setItemSize(res.data);
    } else {
      setItemSize(null);
    }
  };
  const columns: GridColDef[] = [
    {
      field: 'itemSizeName',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.itemSizeName || '-'}</div>;
      },
    },
    {
      field: 'dimensionConfigName',
      headerName: 'ขนาด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <DimensionChipStyle>
            {params.row.dimensionConfigName || '-'}
          </DimensionChipStyle>
        );
      },
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.description || '-'}</div>;
      },
    },
    {
      field: 'cut',
      headerName: 'ตัด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 88,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.cut)}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        const isFirstRow = params.api.getRowIndex(params.id) === 0;
        if (!isFirstRow) {
          return (
            <div className="flex items-center">
              <IconButton
                disabled={!isAllowed(permissions, 'stock.sizes.update')}
                onClick={() => {
                  onEdit(params.row);
                }}
              >
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton
                disabled={!isAllowed(permissions, 'stock.sizes.delete')}
                onClick={() => {
                  setOpenDelete({
                    status: true,
                    name: params.row.itemSizeName,
                    id: params.row.id,
                  });
                }}
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          );
        }
      },
    },
  ];

  const deleteSubItemSize = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiSubItemSize.deleteSubItemSizeById(openDelete.id);
      if (res.status) {
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
        getSubItemSize();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: `${res.message}`,
            severity: 'error',
          })
        );
      }
    }
    setOpenDelete({
      ...openDelete,
      status: false,
      id: null,
    });
    setLoading(false);
  };

  const onOpen = () => {
    setInitialSubItemSize({});
    setOpenCreateModal({
      status: true,
      type: 'create',
    });
  };
  const onEdit = (data: any) => {
    setInitialSubItemSize(data);
    setOpenCreateModal({
      status: true,
      type: 'edit',
    });
  };
  const onClose = () => {
    setOpenCreateModal({
      ...openCreateModal,
      status: false,
    });
  };

  const handelSubmit = async (values: any) => {
    setLoading(true);
    if (openCreateModal.type === 'create') {
      const createValue: any = _.omit(values, ['id', 'masterUnitSizeId']);
      const res = await apiSubItemSize.addSubItemSize(createValue);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: 'บันทึกสำเร็จ',
            severity: 'success',
          })
        );
        setOpenCreateModal({
          ...openCreateModal,
          status: false,
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    } else if (openCreateModal.type === 'edit') {
      const updateValue: any = _.omit(values, ['masterUnitSizeId']);
      const res = await apiSubItemSize.updateSubItemSize(updateValue);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: 'บันทึกสำเร็จ',
            severity: 'success',
          })
        );
        setOpenCreateModal({
          ...openCreateModal,
          status: false,
        });
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    }
    setLoading(false);
    await getSubItemSize();
  };

  return (
    <>
      <ProductNav
        title={`${
          subItemSizeList &&
          subItemSizeList.content &&
          subItemSizeList.content.length > 0
            ? subItemSizeList.content[0].itemSizeName
            : ''
        }`}
        backUrl="/stock/setting/sizes"
        showBorderBottom
      />
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/stock/setting/sizes"
            className="opacity-40"
          >
            Sizes
          </Link>
          <p>
            {subItemSizeList &&
            subItemSizeList.content &&
            subItemSizeList.content.length > 0
              ? subItemSizeList.content[0].itemSizeName
              : ''}
          </p>
        </Breadcrumbs>
        <ActionGroupStyle>
          <ActionButton
            variant="outlined"
            color="blueGrey"
            icon={<AddCircle />}
            text="เพิ่มขนาดย่อย"
            borderRadius={'20px'}
            onClick={() => {
              onOpen();
            }}
            disabled={!isAllowed(permissions, 'stock.sizes.create')}
          />
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        {isAllowed(permissions, 'stock.sizes.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={subItemSizeList?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={100} />
                  <DataGrid
                    hideFooter={true}
                    rows={
                      subItemSizeList?.content ? subItemSizeList.content : []
                    }
                    columns={columns}
                    paginationMode="server"
                    rowCount={subItemSizeList?.totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={
                      subItemSizeList?.totalElements
                        ? subItemSizeList.totalElements
                        : 0
                    }
                    handleChangeFilters={(newValues: FiltersType) => {
                      setFilters(newValues);
                    }}
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
      <Dialog
        open={openCreateModal.status}
        onClose={() => {
          setOpenCreateModal({ ...openCreateModal, status: false });
        }}
      >
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {openCreateModal.type === 'create'
                    ? 'สร้างขนาดย่อย'
                    : 'แก้ไขขนาดย่อย'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                {!isEmpty(itemSize) && (
                  <SubItemSizeForm
                    handleSubmit={async (value: any) => {
                      await handelSubmit(value);
                    }}
                    submitting={loading}
                    disable={false}
                    initialValues={initialSubItemSize}
                    itemSize={itemSize}
                  />
                )}
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={`คุณต้องการที่จะลบ ${openDelete.name}`}
        loadingConfirm={loading}
        onConfirm={async () => {
          await deleteSubItemSize();
        }}
      />
    </>
  );
};
SubItemSize.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default SubItemSize;
