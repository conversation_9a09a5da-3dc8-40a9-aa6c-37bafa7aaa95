import React, { ReactElement, useEffect, useState } from 'react';
import 'dayjs/locale/th';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import apiBrand from '@/services/stock/brand';
import BrandForm from '@/components/brand/BrandForm';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const EditBrand = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const [brandById, setBrandById] = useState<any>({});
  const updateBrand = async (value: any, file: any) => {
    setSubmitting(true);
    const formData: any = new FormData();
    if (file !== null) {
      formData.append('file', file.get('file'));
    } else {
      formData.append('file', null);
    }
    formData.append('brandRequest', JSON.stringify(value));
    const res = await apiBrand.updateBrand(formData);
    if (!res.isError) {
      setDisable(true);
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขแบรนด์สำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/stock/setting/brand');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };
  const getBrandById = async () => {
    const res = await apiBrand.getBrandById(id as string);
    if (!res.isError) {
      setBrandById(res.data);
    }
  };
  useEffect(() => {
    if (!isEmpty(id)) {
      getBrandById();
    }
  }, [id]);
  return (
    <>
      <ProductNav
        title="แก้ไขแบรนด์"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/stock/setting/brand'}
      />
      {!isEmpty(brandById) && isAllowed(permissions, 'stock.brand.update') && (
        <BrandForm
          submitting={submitting}
          disable={disable}
          handleSubmitBrand={updateBrand}
          initialValues={brandById}
        />
      )}
      {!isAllowed(permissions, 'stock.brand.update') && <NotPermission />}
    </>
  );
};

EditBrand.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default EditBrand;
