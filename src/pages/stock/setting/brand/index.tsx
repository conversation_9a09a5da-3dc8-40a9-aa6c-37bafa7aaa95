import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import apiBrand from '@/services/stock/brand';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { OpenDeleteType } from '@/types/category';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

const BrandList = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    getBrand();
  }, [filters]);

  const getBrand = async () => {
    const res = await apiBrand.getList(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalElements(res.data.totalElements);
    }
  };
  const handleDeleteBrand = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiBrand.deleteBrand(openDelete.id);
      if (!res.isError) {
        getBrand();
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: `${res.message.message}`,
            severity: 'error',
          })
        );
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };
  const handleOpenDelete = async (name: string, id: number) => {
    setOpenDelete({
      name,
      status: true,
      id,
    });
  };
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อแบรนด์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={params.row.imageUrl || '/images/product/empty-product.svg'}
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '4px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row.description || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 64,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(permissions, 'stock.brand.update'),
                  IconElement: () => <SvgPencilIcon />,
                  title: 'แก้ไข',
                  onAction: () => {
                    router.push(`/stock/setting/brand/${params.row.id}`);
                  },
                },
                {
                  disabled: !isAllowed(permissions, 'stock.brand.delete'),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    handleOpenDelete(params.row.name, params.row.id);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="แบรนด์" showBorderBottom>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างแบรนด์"
            borderRadius={'20px'}
            disabled={!isAllowed(permissions, 'stock.brand.create')}
            onClick={() => {
              router.push('/stock/setting/brand/create');
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบแบรนด์?'}
        loadingConfirm={loading}
        onConfirm={() => {
          handleDeleteBrand();
        }}
      />
      <AppContentStyle>
        {isAllowed(permissions, 'stock.brand.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={64} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </>
  );
};

BrandList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default BrandList;
