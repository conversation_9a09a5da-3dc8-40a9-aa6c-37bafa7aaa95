import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import SearchInput from '@/components/SearchInput';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControlLabel,
  FormHelperText,
  IconButton,
  Radio,
  RadioGroup,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import { AddCircle, Check } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiMaterial from '@/services/stock/material';
import Swal from 'sweetalert2';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FadeInStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { numberWithCommas } from '@/utils/number';
import Image from 'next/image';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiDimensions from '@/services/stock/dimensions';
import _, { isEmpty } from 'lodash';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import FormModal from '@/components/global/form/FormModal';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

type SelectType = {
  id: number;
  name: string;
};
const MaterialList = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { fromPath } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [viewTypeSelect, setViewTypeSelect] = useState<SelectType[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [dimensionConfigs, setDimensionConfigs] = useState<any[]>([]);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
    materialTypeId: null,
  });
  const formik = useFormik({
    initialValues: {
      id: null,
      name: '',
      materialTypeId: null,
      dimensionConfigId: null,
      isCut: false,
      isGsm: false,
      isSide: false,
    },
    validate: (values: any) => {
      const errors: any = {};
      if (!values.name) {
        errors.name = 'กรุณากรอกชื่อ';
      }
      if (!values.dimensionConfigId) {
        errors.dimensionConfigId = 'กรุณาเลือกสเปค';
      }
      return errors;
    },
    onSubmit: async (values: any) => {
      await submitDialog(values);
    },
  });

  const getMaterialType = async () => {
    const res = await apiMaterial.getMaterialType();
    if (!res.isError) {
      setViewTypeSelect(res.data);
      if (res.data[0]) {
        setFilters({
          ...filters,
          materialTypeId: res.data[0].id,
        });
      }
    }
  };

  useEffect(() => {
    getDimensionConfigs();
    getMaterialType();
  }, []);

  useEffect(() => {
    if (isOpenDialog && !isEmpty(dimensionConfigs) && dialogMode === 'create') {
      formik.setFieldValue('dimensionConfigId', dimensionConfigs[0].id);
    }
  }, [isOpenDialog]);

  const getDimensionConfigs = async () => {
    const response: any = await apiDimensions.getConfigList();
    if (response.status) {
      await setDimensionConfigs(response.data);
    } else {
      await setDimensionConfigs([]);
    }
  };

  useEffect(() => {
    if (!isEmpty(viewTypeSelect)) {
      getMaterials();
    }
  }, [filters]);

  const getMaterials = async () => {
    const res = await apiMaterial.getList(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalElements(res.data.totalElements); // fix data
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    values.dimensionConfigId = Number(values.dimensionConfigId);
    if (dialogMode === 'create') {
      const final = _.omit(values, ['id']);
      const res = await apiMaterial.create(final);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(
          async () => {
            setFilters({
              ...filters,
              materialTypeId: values.materialTypeId,
            });
            await getMaterials();
          }
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      const res = await apiMaterial.update(values);
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(
          async () => {
            setFilters({
              ...filters,
              materialTypeId: values.materialTypeId,
            });
            await getMaterials();
          }
        );
      } else {
        Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = (mode: string, data: any) => {
    setDialogMode(mode);
    formik.resetForm();
    if (mode === 'update') {
      formik.setFieldValue('id', data.id);
      formik.setFieldValue('name', data.name);
      formik.setFieldValue('materialTypeId', data.materialType.id);
      formik.setFieldValue('dimensionConfigId', data.dimensionConfig.id);
      formik.setFieldValue('isCut', data.isCut);
      formik.setFieldValue('isGsm', data.isGsm);
      formik.setFieldValue('isSide', data.isSide);
    } else if (viewTypeSelect[0].id) {
      formik.setFieldValue('materialTypeId', viewTypeSelect[0].id);
    }
    setOpenDialog(true);
  };

  const remove = async (item: any) => {
    if (item) {
      Swal.fire({
        title: 'ยืนยันการลบ',
        text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'ยกเลิก',
        confirmButtonText: 'ยืนยัน',
      }).then(async (result) => {
        if (result.isConfirmed) {
          const res = await apiMaterial.delete(item.id);
          if (res && !res.isError) {
            Swal.fire(
              'ลบเรียบร้อย',
              `ลบ ${item.name} เรียบร้อยแล้ว`,
              'success'
            ).then(() => {
              getMaterials();
            });
          } else {
            Swal.fire(
              'เกิดข้อผิดพลาด',
              `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
              'error'
            );
          }
        }
      });
    } else {
      Swal.fire(
        'ไม่สามารถลบได้',
        `กรุณาลบ Sub Material ทั้งหมด ใน Material นี้ก่อน`,
        'warning'
      );
    }
  };
  const handleChangeViewType = (event: React.SyntheticEvent, newValue: any) => {
    setFilters({
      ...filters,
      materialTypeId: newValue,
    });
  };
  const handleChangeCheckbox = (e: any, value: number) => {
    if (e.target.checked) {
      formik.setFieldValue('materialTypeId', value);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Material',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'materialType',
      headerName: 'Material Type',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.materialType.name}</div>;
      },
    },
    {
      field: 'countSubMaterial',
      headerName: 'Sub Materials',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.countSubMaterial)}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 204,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                disabled={!isAllowed(permissions, 'product.material.list')}
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={async () => {
                  if (fromPath) {
                    await router.push(
                      `${router.pathname}/${params.row.id}?fromPath=${fromPath}`
                    );
                  } else {
                    await router.push(`${router.pathname}/${params.row.id}`);
                  }
                }}
              >
                รายละเอียด
              </Button>
              <IconButton
                onClick={() => openDialog('update', params.row)}
                disabled={
                  params.row.countSubMaterial !== 0 ||
                  !isAllowed(permissions, 'product.material.update')
                }
              >
                <Image
                  src={'/icons/edit.svg'}
                  width={24}
                  height={24}
                  alt=""
                  style={{
                    opacity: params.row.countSubMaterial !== 0 ? 0.6 : 1,
                  }}
                />
              </IconButton>
              <IconButton
                disabled={!isAllowed(permissions, 'product.material.delete')}
                onClick={() =>
                  remove(params.row.countSubMaterial > 0 ? false : params.row)
                }
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                  style={{
                    opacity: params.row.countSubMaterial !== 0 ? 0.5 : 1,
                  }}
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="Materials" showBorderBottom>
        <ActionGroupStyle>
          <ActionButton
            disabled={!isAllowed(permissions, 'product.material.create')}
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="Create Material"
            borderRadius={'20px'}
            onClick={() => openDialog('create', null)}
          />
        </ActionGroupStyle>
      </ProductNav>
      {!isEmpty(viewTypeSelect) && (
        <FadeInStyled
          style={{
            width: '100%',
          }}
        >
          <div className="w-full flex justify-between pr-[16px]">
            <Tabs
              value={filters.materialTypeId}
              onChange={handleChangeViewType}
              variant="standard"
            >
              {viewTypeSelect.map((item: SelectType, index: number) => {
                return <Tab key={index} label={item.name} value={item.id} />;
              })}
            </Tabs>
            <div className={'flex items-center'}>
              <SearchInput
                makeSearchValue={(newValue) =>
                  setFilters({
                    ...filters,
                    searchName: newValue,
                    page: 0,
                  })
                }
              />
            </div>
          </div>
        </FadeInStyled>
      )}
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={rows}>
            {isAllowed(permissions, 'product.material.list') ? (
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={204} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            ) : (
              <NotPermission />
            )}
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog
        open={isOpenDialog}
        onClose={() => {
          setOpenDialog(false);
        }}
      >
        <DialogContent>
          <FormModal
            title={`${
              dialogMode === 'create' ? 'Create Material' : 'Edit Material'
            }`}
            handleClose={() => {
              setOpenDialog(false);
            }}
            width={492}
          >
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {dialogMode === 'create' ? 'Create' : 'Edit'} Material
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p>Name</p>
                    <TextField
                      placeholder="Material Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </div>
                  <div
                    className="flex flex-col"
                    style={{
                      columnGap: '8px',
                      marginTop: '16px',
                    }}
                  >
                    {viewTypeSelect.map((item: SelectType, index: number) => (
                      <FormControlLabel
                        key={index}
                        control={
                          <Checkbox
                            sx={CheckboxStyle}
                            checked={formik.values.materialTypeId === item.id}
                            onChange={(event: any) => {
                              handleChangeCheckbox(event, item.id);
                            }}
                            icon={<Check />}
                            checkedIcon={<Check className={'p-1'} />}
                          />
                        }
                        label={item.name}
                      />
                    ))}
                  </div>
                  <div>
                    <p>ตั้งค่าสเปค</p>
                    <RadioGroup
                      name="dimensionConfigId"
                      value={formik.values.dimensionConfigId}
                      onChange={formik.handleChange}
                    >
                      {dimensionConfigs &&
                        dimensionConfigs.map((item: any, i) => {
                          return (
                            <FormControlLabel
                              key={i}
                              value={item.id}
                              control={
                                <Radio
                                  icon={<Check />}
                                  checkedIcon={<Check className={'p-1'} />}
                                  sx={RadioStyle}
                                  checked={
                                    Number(item.id) ===
                                    Number(formik.values.dimensionConfigId)
                                  }
                                />
                              }
                              label={item.configName}
                            />
                          );
                        })}
                    </RadioGroup>
                    {formik.touched.dimensionConfigId &&
                      formik.errors.dimensionConfigId && (
                        <FormHelperText
                          error
                          sx={{
                            margin: '4px 14px 0',
                          }}
                        >
                          {formik.errors.dimensionConfigId}
                        </FormHelperText>
                      )}
                  </div>
                  <div
                    className="flex flex-col pb-[6rem]"
                    style={{
                      columnGap: '8px',
                      marginTop: '16px',
                    }}
                  >
                    <p>ตั้งค่าพิเศษ</p>
                    <FormControlLabel
                      id={'isCut'}
                      name={'isCut'}
                      control={
                        <Checkbox
                          sx={CheckboxStyle}
                          checked={formik.values.isCut}
                          onChange={formik.handleChange}
                          icon={<Check />}
                          checkedIcon={<Check className={'p-1'} />}
                        />
                      }
                      label={'ตัด'}
                    />
                    <FormControlLabel
                      id={'isGsm'}
                      name={'isGsm'}
                      control={
                        <Checkbox
                          sx={CheckboxStyle}
                          checked={formik.values.isGsm}
                          onChange={formik.handleChange}
                          icon={<Check />}
                          checkedIcon={<Check className={'p-1'} />}
                        />
                      }
                      label={'แกรม'}
                    />
                    <FormControlLabel
                      id={'isSide'}
                      name={'isSide'}
                      control={
                        <Checkbox
                          sx={CheckboxStyle}
                          checked={formik.values.isSide}
                          onChange={formik.handleChange}
                          icon={<Check />}
                          checkedIcon={<Check className={'p-1'} />}
                        />
                      }
                      label={'ด้านเคลือบ'}
                    />
                  </div>
                  <div className="btn-bottom">
                    <Button
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={25}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

MaterialList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default MaterialList;

const RadioStyle = {
  color: '#fff',
  backgroundColor: '#fff',
  width: '24px',
  height: '24px',
  border: '1px solid #dbe2e5',
  margin: '6px 10px',
  '&.Mui-checked': {
    color: '#fff',
    backgroundColor: '#000',
  },
};

const CheckboxStyle = {
  color: '#fff',
  backgroundColor: '#fff',
  width: '24px',
  height: '24px',
  border: '1px solid #dbe2e5',
  margin: '6px 10px',
  borderRadius: '6px',
  '&.Mui-checked': {
    color: '#fff',
    backgroundColor: '#000',
  },
};
