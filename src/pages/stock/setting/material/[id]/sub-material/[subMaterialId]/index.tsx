import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Breadcrumbs,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import ProductNav from '@/components/product/ProductNav';
import apiSubMaterialDetail from '@/services/stock/subMaterialDetail';
import apiSubMaterial from '@/services/stock/subMaterial';
import Link from 'next/link';
import { useFormik } from 'formik';
import * as yup from 'yup';
import Swal from 'sweetalert2';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { LoadingButton } from '@mui/lab';
import AppPagination from '@/components/global/AppPagination';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import apiMaterial from '@/services/stock/material';
import FormModal from '@/components/global/form/FormModal';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';
import { numberWithCommas } from '@/utils/number';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
});
const SubMaterialDetailsList = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { fromPath } = router.query;
  const { subMaterialId } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState<any>([]);
  const [subMaterialInfo, setSubMaterialInfo] = useState<any>(null);
  const [materialInfo, setMaterialInfo] = useState<any>(null);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    subMaterialId: '',
  });
  const formik = useFormik({
    initialValues: {
      name: '',
      side: 0,
      gsm: null,
      cut: 2,
    } as any,
    validationSchema,
    validate: async (values: any) => {
      const errors: any = {};
      if (!values.gsm && materialInfo?.isGsm) {
        errors.gsm = 'กรุณากรอกจำนวนแกรม';
      }
      if (!values.cut && materialInfo?.isCut) {
        errors.cut = 'กรุณากรอกจำนวนตัด';
      }
      return errors;
    },
    onSubmit: async (values: any) => {
      const data = {
        ...values,
        side: Number(values.side),
        cut: Number(values.cut),
      };
      await submitDialog(data);
    },
  });

  useEffect(() => {
    if (subMaterialId) {
      setFilters({
        ...filters,
        subMaterialId: Number(subMaterialId),
      });
    }
  }, [subMaterialId]);

  useEffect(() => {
    if (filters.subMaterialId) {
      getSubMaterialDetails();
    }
  }, [filters]);

  useEffect(() => {
    getSubMaterialInfo();
    getMaterialInfo();
  }, []);

  const getMaterialInfo = async () => {
    const { id: materialId } = router.query;
    const res = await apiMaterial.getInfo(`${materialId}`);
    if (res && !res.isError) {
      setMaterialInfo(res.data);
    }
  };

  const getSubMaterialInfo = async () => {
    const res = await apiSubMaterial.getInfo(`${subMaterialId}`);
    if (res && !res.isError) {
      setSubMaterialInfo(res.data);
    }
  };

  const getSubMaterialDetails = async () => {
    const res = await apiSubMaterialDetail.getList(filters);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    if (dialogMode === 'create') {
      const res = await apiSubMaterialDetail.create({
        ...values,
        subMaterialId: subMaterialInfo.id,
        // side: values.side === 'null' ? null : parseInt(values.side),
      });
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() => {
          getSubMaterialDetails();
          if (fromPath) {
            const decoded64FromPath = atob(fromPath as string);
            router.push(`${decoded64FromPath}`);
          }
        });
      } else {
        await Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      const res = await apiSubMaterialDetail.update(
        {
          name: values.name,
          cut: values.cut,
          gsm: values.gsm,
          side: values.side,
        },
        values.id
      );
      if (res && !res.isError) {
        Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() => {
          getSubMaterialDetails();
          if (fromPath) {
            const decoded64FromPath = atob(fromPath as string);
            router.push(`${decoded64FromPath}`);
          }
        });
      } else {
        await Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = async (mode: string, data: any) => {
    setDialogMode(mode);
    if (mode === 'update') {
      const values = {
        id: data.id,
        name: data.name,
        cut: data.cut,
        gsm: data.gsm,
        side: data.side,
      };
      formik.setValues(values);
    } else {
      formik.resetForm();
    }
    setOpenDialog(true);
  };
  const remove = async (item: any) => {
    Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
      icon: 'warning',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
      confirmButtonText: 'ยืนยัน',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiSubMaterialDetail.delete(item.id);
        if (res && !res.isError) {
          Swal.fire(
            'ลบเรียบร้อย',
            `ลบ ${item.name} เรียบร้อยแล้ว`,
            'success'
          ).then(() => {
            getSubMaterialDetails();
          });
        } else {
          Swal.fire(
            'เกิดข้อผิดพลาด',
            `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
            'error'
          );
        }
      }
    });
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Sub Material Details',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 264,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'options',
      headerName: 'สูตร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 264,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="overflow-hidden text-ellipsis">
            {params.row.options?.name || '-'}
          </div>
        );
      },
    },
    {
      field: 'cut',
      headerName: 'ตัด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.cut === null
              ? 'ไม่มี '
              : `${numberWithCommas(params.row.cut)}`}
          </div>
        );
      },
    },
    {
      field: 'gsm',
      headerName: 'แกรม',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.gsm === null
              ? 'ไม่มี '
              : `${numberWithCommas(params.row.gsm)}`}
          </div>
        );
      },
    },
    {
      field: 'side',
      headerName: 'ด้านพิมพ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.side === null ? 'ไม่มี ' : `${params.row.side}`}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <IconButton
                disabled={!isAllowed(permissions, 'product.material.update')}
                onClick={() => openDialog('update', params.row)}
              >
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton
                disabled={!isAllowed(permissions, 'product.material.delete')}
                onClick={() => remove(params.row)}
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav
        backUrl={`/stock/setting/material/${router.query.id}`}
        title={subMaterialInfo ? subMaterialInfo.name : ''}
        showBorderBottom
      />
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/stock/setting/material"
            className="opacity-40"
          >
            Material
          </Link>
          {materialInfo && (
            <Link
              color="inherit"
              href={`/stock/setting/material/${materialInfo.id}`}
              className="opacity-40"
            >
              {materialInfo.name}
            </Link>
          )}
          {subMaterialInfo ? <p>{subMaterialInfo.name}</p> : ''}
        </Breadcrumbs>
        <ActionGroupStyle>
          <ActionButton
            disabled={!isAllowed(permissions, 'product.material.create')}
            variant="outlined"
            color="blueGrey"
            icon={<AddCircle />}
            text="Add Sub Material Detail"
            borderRadius={'20px'}
            onClick={() => openDialog('create', null)}
          />
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        {isAllowed(permissions, 'product.material.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={rows?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={124} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows?.content || []}
                    columns={columns}
                    paginationMode="server"
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    rowCount={rows?.totalElements || 0}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
              </div>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={rows?.totalElements || 0}
                  handleChangeFilters={(newValues: any) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModal
            title={`${
              dialogMode === 'create'
                ? 'Create Sub Material Details'
                : 'Edit Sub Material Details'
            }`}
            handleClose={() => {
              setOpenDialog(false);
            }}
            width={492}
          >
            <div className="form-wrap">
              <form onSubmit={formik.handleSubmit}>
                <div>
                  <p>ชื่อ</p>
                  <TextField
                    placeholder="กรุณากรอกชื่อ Sub Material Details"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={
                      (formik.touched.name && formik.errors.name) as string
                    }
                  />
                </div>
                {materialInfo?.isGsm && (
                  <div>
                    <p>แกรม</p>
                    <TextField
                      type={'number'}
                      placeholder="กรุณากรอกจำนวนแกรม"
                      id={'gsm'}
                      name="gsm"
                      value={formik.values.gsm}
                      onChange={formik.handleChange}
                      onFocus={(e) =>
                        e.target.addEventListener(
                          'wheel',
                          function (e) {
                            e.preventDefault();
                          },
                          { passive: false }
                        )
                      }
                      onKeyDown={(e) => {
                        if (e.key === '.' || e.key === 'e' || e.key === 'E') {
                          e.preventDefault();
                        }
                      }}
                      error={formik.touched.gsm && Boolean(formik.errors.gsm)}
                      helperText={
                        (formik.touched.gsm && formik.errors.gsm) as string
                      }
                    />
                  </div>
                )}
                {materialInfo?.isCut && (
                  <div>
                    <p>ตัด</p>
                    <RadioGroup
                      value={formik.values.cut}
                      onChange={formik.handleChange}
                      name="cut"
                    >
                      <FormControlLabel
                        value={2}
                        control={<Radio />}
                        label="2"
                      />
                      <FormControlLabel
                        value={3}
                        control={<Radio />}
                        label="3"
                      />
                      <FormControlLabel
                        value={4}
                        control={<Radio />}
                        label="4"
                      />
                    </RadioGroup>
                    {/* <TextField */}
                    {/*  type={'number'} */}
                    {/*  placeholder="กรุณากรอกจำนวนตัด" */}
                    {/*  id={'cut'} */}
                    {/*  name="cut" */}
                    {/*  value={formik.values.cut} */}
                    {/*  onChange={formik.handleChange} */}
                    {/*  onFocus={(e) => */}
                    {/*    e.target.addEventListener( */}
                    {/*      'wheel', */}
                    {/*      function (e) { */}
                    {/*        e.preventDefault(); */}
                    {/*      }, */}
                    {/*      { passive: false } */}
                    {/*    ) */}
                    {/*  } */}
                    {/*  error={formik.touched.cut && Boolean(formik.errors.cut)} */}
                    {/*  helperText={ */}
                    {/*    (formik.touched.cut && formik.errors.cut) as string */}
                    {/*  } */}
                    {/* /> */}
                  </div>
                )}
                {materialInfo?.isSide && (
                  <div>
                    <p>ด้านพิมพ์</p>
                    <RadioGroup
                      value={formik.values.side}
                      onChange={formik.handleChange}
                      name="side"
                    >
                      <FormControlLabel
                        value={0}
                        control={<Radio />}
                        label="ไม่มีด้าน"
                      />
                      <FormControlLabel
                        value={1}
                        control={<Radio />}
                        label="1 ด้าน"
                      />
                      <FormControlLabel
                        value={2}
                        control={<Radio />}
                        label="2 ด้าน"
                      />
                    </RadioGroup>
                  </div>
                )}
                <div className="flex gap-[24px] mt-[34px]">
                  <LoadingButton
                    type="button"
                    disabled={false}
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      fontWeight: '600',
                      fontSize: '16px',
                    }}
                    fullWidth
                    onClick={() => setOpenDialog(false)}
                  >
                    ยกเลิก
                  </LoadingButton>
                  <LoadingButton
                    type="submit"
                    variant="contained"
                    color="dark"
                    sx={{
                      fontWeight: '600',
                      fontSize: '16px',
                    }}
                    fullWidth
                  >
                    {submitting ? (
                      <CircularProgress
                        size={20}
                        style={{
                          color: 'white',
                        }}
                      />
                    ) : (
                      'บันทึก'
                    )}
                  </LoadingButton>
                </div>
              </form>
            </div>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

SubMaterialDetailsList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default SubMaterialDetailsList;
