import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import {
  Breadcrumbs,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import apiSubMaterial from '@/services/stock/subMaterial';
import ProductNav from '@/components/product/ProductNav';
import apiMaterial from '@/services/stock/material';
import Link from 'next/link';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import ImageField from '@/components/ImageField';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  FadeInStyled,
  ScrollBarStyled,
  TableCellImageStyle,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import { LoadingButton } from '@mui/lab';
import AppPagination from '@/components/global/AppPagination';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import FormModal from '@/components/global/form/FormModal';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก'),
  file: yup.mixed().required('กรุณาอัปโหลดไฟล์'),
  // optionsCategoryId: yup.number().required('กรุณาเลือก'),
  // optionsId: yup.number().required('กรุณาเลือก'),
});
const validationUpdateSchema = yup.object({
  name: yup.string().required('กรุณากรอก Name'),
  // optionsCategoryId: yup.number().required('กรุณาเลือก'),
  // optionsId: yup.number().required('กรุณาเลือก'),
});
const SubMaterialList = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const { id, fromPath } = router.query;
  const [rows, setRows] = useState<any>({});
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [materialInfo, setMaterialInfo] = useState<any>(null);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    materialId: '',
  });
  const formik = useFormik({
    initialValues: {
      name: '',
      imageUrl: null,
      file: null,
    },
    validationSchema:
      dialogMode === 'create' ? validationSchema : validationUpdateSchema,
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      await submitDialog(values);
    },
  });

  useEffect(() => {
    if (id) {
      setFilters({
        ...filters,
        materialId: Number(id),
      });
    }
  }, [id]);

  useEffect(() => {
    if (filters.materialId) {
      getSubMaterials();
    }
  }, [filters]);

  useEffect(() => {
    getMaterialInfo();
  }, []);

  const getMaterialInfo = async () => {
    const { id: materialId } = router.query;
    const res = await apiMaterial.getInfo(`${materialId}`);
    if (res && !res.isError) {
      setMaterialInfo(res.data);
    }
  };

  const getSubMaterials = async () => {
    const res = await apiSubMaterial.getList(filters);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const submitDialog = async (values: any) => {
    setSubmitting(true);
    const { id: materialId } = router.query;
    const sendValue = {
      name: values.name,
      materialId: Number(materialId),
    };
    if (dialogMode === 'create') {
      const res = await apiSubMaterial.create(sendValue);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('id', res.data.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiSubMaterial.uploadImage(formData);
          if (!resUploadFile.isError) {
            Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
              getSubMaterials()
            );
          } else {
            Swal.fire(
              'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
              'สร้างข้อมูลเรียบร้อย',
              'warning'
            ).then(() => getSubMaterials());
          }
        } else {
          Swal.fire('เรียบร้อย', 'สร้างข้อมูลเรียบร้อย', 'success').then(() =>
            getSubMaterials()
          );
        }
      } else {
        await Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    if (dialogMode === 'update') {
      const sendValue = {
        id: values.id,
        name: values.name,
        materialId: Number(materialId),
      };
      const res = await apiSubMaterial.update(sendValue);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('id', res.data.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiSubMaterial.uploadImage(formData);
          if (!resUploadFile.isError) {
            Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(
              () => getSubMaterials()
            );
          } else {
            Swal.fire(
              'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
              'อัปเดตข้อมูลเรียบร้อย',
              'warning'
            ).then(() => getSubMaterials());
          }
        } else {
          Swal.fire('เรียบร้อย', 'อัปเดตข้อมูลเรียบร้อย', 'success').then(() =>
            getSubMaterials()
          );
        }
      } else {
        await Swal.fire(
          'เกิดข้อผิดพลาด',
          'ไม่สามารถอัปเดตข้อมูลได้ กรุณาลองใหม่ภายหลัง',
          'error'
        );
      }
    }
    setOpenDialog(false);
    setSubmitting(false);
  };

  const openDialog = async (mode: string, data: any) => {
    formik.resetForm();
    setDialogMode(mode);
    if (mode === 'update') {
      await formik.setFieldValue('id', data.id);
      await formik.setFieldValue('name', data.name);
      await formik.setFieldValue('imageUrl', data.imageUrl);
    }
    setOpenDialog(true);
  };
  const remove = async (item: any) => {
    if (item) {
      Swal.fire({
        title: 'ยืนยันการลบ',
        text: `คุณต้องการลบ ${item.name} ใช่หรือไม่ ?`,
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'ยกเลิก',
        confirmButtonText: 'ยืนยัน',
      }).then(async (result) => {
        if (result.isConfirmed) {
          const res = await apiSubMaterial.delete(item.id);
          if (res && !res.isError) {
            Swal.fire(
              'ลบเรียบร้อย',
              `ลบ ${item.name} เรียบร้อยแล้ว`,
              'success'
            ).then(() => {
              getSubMaterials();
            });
          } else {
            Swal.fire(
              'เกิดข้อผิดพลาด',
              `ไม่สามารถลบ ${item.name} ได้ กรุณาลองใหม่ภายหลัง`,
              'error'
            );
          }
        }
      });
    } else {
      Swal.fire(
        'ไม่สามารถลบได้',
        `กรุณาลบ Sub Material Details ทั้งหมด ใน Sub Material นี้ก่อน`,
        'warning'
      );
    }
  };
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 300,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <TableCellImageStyle>
            <div
              className="flex items-center justify-center"
              style={{
                columnGap: '14px',
              }}
            >
              <Image
                src={params.row.imageUrl || '/images/product/empty-product.svg'}
                width={100}
                height={100}
                alt=""
                style={{
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
              <div>{params.row.name}</div>
            </div>
          </TableCellImageStyle>
        );
      },
    },
    {
      field: 'countSubMaterialDetail',
      headerName: 'Sub Material Detail',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 150,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {numberWithCommas(params.row.countSubMaterialDetail) || '0'}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 204,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                disabled={!isAllowed(permissions, 'product.material.list')}
                onClick={async () => {
                  if (fromPath) {
                    await router.push(
                      `/stock/setting/material/${id}/sub-material/${params.row.id}?fromPath=${fromPath}`
                    );
                  } else {
                    await router.push(
                      `/stock/setting/material/${id}/sub-material/${params.row.id}`
                    );
                  }
                }}
              >
                รายละเอียด
              </Button>
              <IconButton
                disabled={!isAllowed(permissions, 'product.material.update')}
                onClick={() => openDialog('update', params.row)}
              >
                <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
              </IconButton>
              <IconButton
                disabled={!isAllowed(permissions, 'product.material.delete')}
                onClick={() =>
                  remove(
                    params.row.countSubMaterialDetail > 0 ? false : params.row
                  )
                }
              >
                <Image
                  src={'/icons/delete.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </IconButton>
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav
        backUrl="/stock/setting/material"
        showBorderBottom
        title={materialInfo ? materialInfo.name : ''}
      />
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/stock/setting/material"
            className="opacity-40"
          >
            Material
          </Link>
          {materialInfo ? <p>{materialInfo.name}</p> : ''}
        </Breadcrumbs>
        <ActionGroupStyle>
          <ActionButton
            variant="outlined"
            color="blueGrey"
            icon={<AddCircle />}
            text="Add Sub Material"
            borderRadius={'20px'}
            onClick={() => openDialog('create', null)}
            disabled={!isAllowed(permissions, 'product.material.create')}
          />
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        {isAllowed(permissions, 'product.material.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={rows?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={204} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows?.content || []}
                    columns={columns}
                    // paginationMode="server"
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
              </div>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={rows?.totalElements || 0}
                  handleChangeFilters={(newValues: any) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModal
            title={`${
              dialogMode === 'create'
                ? 'Create Sub Material'
                : 'Edit Sub Material'
            }`}
            handleClose={() => {
              setOpenDialog(false);
            }}
            width={492}
          >
            <div className="content-wrap">
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    marginTop: '24px',
                  }}
                >
                  <ImageField
                    handleChange={(file: any) =>
                      formik.setFieldValue('file', file)
                    }
                    defaultBackground={
                      formik.values.imageUrl || '/images/add-image.svg'
                    }
                    borderRadius="14px"
                    alertRequire={
                      formik.touched.file && Boolean(formik.errors.file)
                    }
                    conditionText={
                      'Upload JPG, PNG or SVG best sizes 160x160 file maximum 2 mb.'
                    }
                    textUploadBtn="Upload"
                  />
                  <FadeInStyled>
                    <p>Name</p>
                    <TextField
                      placeholder="Sub Material Name"
                      name="name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        (formik.touched.name && formik.errors.name) as string
                      }
                    />
                  </FadeInStyled>
                  <div className="flex gap-[24px] mt-[34px]">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => setOpenDialog(false)}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? (
                        <CircularProgress
                          size={20}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

SubMaterialList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default SubMaterialList;
