import styled from 'styled-components';
import { GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import { Tooltip } from '@mui/material';
import KebabTable from '@/components/KebabTable';
import React from 'react';

const ImportBtnStyled = styled.div`
  height: 32px;
  width: 32px;
  border-radius: 16px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 0px 2px #f5f7f8;
  border: 1px solid #dbe2e5;
  cursor: pointer;
  transition: 0.15s ease-out;
  &:hover {
    background: #f5f7f8;
  }
`;

const columns: GridColDef[] = [
  {
    field: 'rawMaterial.rawMaterialNo',
    headerName: 'รหัส',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 78,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return <div>{params.row.rawMaterial.rawMaterialNo}</div>;
    },
  },
  {
    field: 'rawMaterial.imageUrl',
    headerName: 'ราการสินค้า',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 500,
    flex: 1,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return (
        <Tooltip
          title={
            `${params.row.rawMaterial.name} • ` +
            `${params.row.rawMaterial.brand.name} ${
              params.row.rawMaterial.itemSize?.name
                ? `• ${params.row.rawMaterial.itemSize.name} `
                : ''
            }${
              params.row.rawMaterial.subMaterialDetail?.side
                ? `• ${params.row.rawMaterial.subMaterialDetail.side} ด้าน`
                : ''
            }`
          }
          placement="top"
          arrow
        >
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.rawMaterial.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '4px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              <span>{`${params.row.rawMaterial.name} • `}</span>
              <span>{`${params.row.rawMaterial.brand.name} `}</span>
              {params.row.rawMaterial.itemSize?.name && (
                <span>{`• ${params.row.rawMaterial.itemSize.name} `}</span>
              )}
              {params.row.rawMaterial.subMaterialDetail?.side && (
                <span>
                  {`• ${params.row.rawMaterial.subMaterialDetail.side} ด้าน`}
                </span>
              )}
            </div>
          </div>
        </Tooltip>
      );
    },
  },
  {
    field: 'stock',
    headerName: 'การจัดเก็บ',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 162,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return <div>{params.row.stock.name}</div>;
    },
  },
  {
    field: 'quantity',
    headerName: 'จำนวน',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 162,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return <div>{numberWithCommas(params.row.quantity)}</div>;
    },
  },
  {
    field: 'priceUnit',
    headerName: 'ราคา/หน่วย',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 162,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return <div>{numberWithCommas(params.row.priceUnit)}</div>;
    },
  },
  {
    field: 'discountUnit',
    headerName: 'ส่วนลด/หน่วย',
    editable: false,
    headerAlign: 'left',
    align: 'left',
    minWidth: 162,
    disableColumnMenu: true,
    sortable: false,
    renderCell: (params: any) => {
      return <div>{numberWithCommas(params.row.discountUnit)}</div>;
    },
  },
  {
    field: 'totalPrice',
    headerName: 'ราคารวม',
    editable: false,
    headerAlign: 'right',
    align: 'right',
    minWidth: 162,
    disableColumnMenu: true,
    sortable: false,
    cellClassName: 'stickyCell',
    renderCell: (params: any) => {
      return <div>{numberWithCommas(params.row.totalPrice)}</div>;
    },
  },
];

const getColumnByStep = (
  step: number,
  handleOpenModalImport: (data: any) => void,
  handleClickDetail: (data: any) => void
) => {
  const stepColumns = columns.map((column) => {
    if ((step === 4 || step === 5) && column.field === 'totalPrice') {
      if (typeof column.cellClassName === 'string') {
        return {
          ...column,
          cellClassName: column.cellClassName.replace('stickyCell', ''),
        };
      }
      return {
        ...column,
        cellClassName: '',
      };
    }
    return column;
  });

  if (step !== 4 && step !== 5) {
    return stepColumns;
  }

  const additionalColumns: GridColDef[] = [
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => (
        <Tooltip
          title={
            params.row.poOrderStatus.id === 5
              ? 'นำเข้าแล้ว'
              : 'นำสินค้าเข้าคลัง'
          }
          placement="left"
          arrow
        >
          {params.row.poOrderStatus.id === 5 ? (
            <>
              <div
                style={{
                  height: '32px',
                  width: '32px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Image
                  src={'/icons/icon-task-new.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              {params.row.poOrderStatus.id === 5 && (
                <KebabTable
                  item={params.row}
                  isRemove={false}
                  isEdit={{
                    status: false,
                    url: '',
                  }}
                  isDetail={true}
                  handleClickDetail={(data: any) => {
                    handleClickDetail(data);
                  }}
                />
              )}
            </>
          ) : (
            <ImportBtnStyled
              onClick={() => {
                handleOpenModalImport(params.row);
              }}
            >
              <Image
                src={'/icons/icon-archive.svg'}
                width={24}
                height={24}
                alt=""
              />
            </ImportBtnStyled>
          )}
        </Tooltip>
      ),
    },
  ];

  return [...stepColumns, ...additionalColumns];
};

export default getColumnByStep;
