import { AddCircle } from '@mui/icons-material';
import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Badge, Button, Tab, Tabs } from '@mui/material';
import AppPagination from '@/components/global/AppPagination';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import ProductNav from '@/components/product/ProductNav';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import SearchInput from '@/components/SearchInput';
import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { numberWithCommas } from '@/utils/number';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { isEmpty, isNull } from 'lodash';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import ModalRequestPurchaseOrder from '@/components/purchase-order/ModalRequestPurchaseOrder';
import { isAllowed } from '@/utils/permission';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

dayjs.extend(utc);

type LayoutDataListPageProps = {
  poOrderStatusId: string;
  authorities: string[];
};
const initialOpenDeleteModal = {
  status: false,
  id: null,
  poNo: '',
};
const PurchaseOrderList = ({
  poOrderStatusId,
  authorities,
}: LayoutDataListPageProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [rows, setRows] = useState<any>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    search: '',
    poOrderStatusId: 0,
  });
  const [purchaseOrderStatus, setPurchaseOrderStatus] = useState<any>([]);
  const [openDeleteModal, setOpenDeleteModal] = useState<any>(
    initialOpenDeleteModal
  );
  const [loadingDeletePo, setLoadingDeletePo] = useState<boolean>(false);
  useEffect(() => {
    getList();
  }, [poOrderStatusId, filters]);
  const getPurchaseOrderStatus = async () => {
    const res = await apiPurchaseOrder.getPurchaseOrderStatus();
    if (!res.isError) {
      setPurchaseOrderStatus(res.data);
    }
  };
  useEffect(() => {
    getPurchaseOrderStatus();
  }, []);

  const getList = async () => {
    setLoading(true);
    const res = await apiPurchaseOrder.getList({
      ...filters,
      poOrderStatusId,
    });
    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
    setLoading(false);
  };

  const handleChangeStatus = (event: React.SyntheticEvent, newStatus: any) => {
    router.push(
      `/stock/purchase-order?poOrderStatusId=${newStatus}`,
      undefined
    );
  };
  const columns: GridColDef[] = [
    {
      field: 'poNo',
      headerName: 'รหัส',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'contact',
      headerName: 'ตัวแทนจำหน่าย',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.contact.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.contact.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'totalPrice',
      headerName: 'มูลค่ารวมสุทธิ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{numberWithCommas(params.row.totalPrice)} บาท</div>;
      },
    },
    {
      field: 'createdUser',
      headerName: 'ผู้สร้าง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 250,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.createdUser.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.createdUser.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'poDate',
      headerName: 'วันที่สั่งซื้อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{dayjs(params.row.poDate).format('DD/MM/YYYY HH:mm')}</div>;
      },
    },
    {
      field: 'expireDate',
      headerName: 'วันหมดอายุ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 148,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        if (!isNull(params.row.expireDate)) {
          return (
            <div>{dayjs(params.row.expireDate).utc().format('DD/MM/YYYY')}</div>
          );
        }
        return <span style={{ color: '#CFD8DC' }}>ไม่มี</span>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            {params.row.poOrderStatus.id === 1 ? (
              <Button
                disabled={
                  !isAllowed(permissions, 'stock.purchase-order.update')
                }
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                }}
                onClick={() => {
                  router.push(`/stock/purchase-order/${params.row.id}/edit`);
                }}
              >
                แก้ไข
              </Button>
            ) : (
              <div
                className="flex items-center"
                style={{
                  columnGap: '8px',
                }}
              >
                {params.row.poOrderStatus.id !== 1 && (
                  <Button
                    disabled={
                      !isAllowed(permissions, 'stock.purchase-order.list')
                    }
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    style={{
                      width: '84px',
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '12px',
                    }}
                    onClick={() => {
                      router.push(`/stock/purchase-order/${params.row.id}`);
                    }}
                  >
                    รายละเอียด
                  </Button>
                )}
                {params.row.poOrderStatus.id === 1 ||
                  (params.row.poOrderStatus.id === 6 && (
                    // <KebabTable
                    //   item={params.row}
                    //   handleRemove={(item: any) => {
                    //     setOpenDeleteModal({
                    //       status: true,
                    //       id: item.id,
                    //       poNo: item.poNo,
                    //     });
                    //   }}
                    //   isEdit={{
                    //     status: params.row.poOrderStatusId === 1,
                    //     url: `/stock/purchase-order/${params.row.id}/edit`,
                    //   }}
                    //   isRemove={params.row.poOrderStatus.id === 6}
                    // />
                    <PopoverAction
                      triggerElement={
                        <div className="kebab">
                          <div className="dot" />
                        </div>
                      }
                      customItems={[
                        {
                          disabled:
                            !isAllowed(
                              permissions,
                              'stock.purchase-order.update'
                            ) || params.row.poOrderStatusId !== 1,
                          IconElement: () => <SvgPencilIcon />,
                          title: 'แก้ไข',
                          onAction: () => {
                            router.push(
                              `/stock/purchase-order/${params.row.id}/edit`
                            );
                          },
                        },
                        {
                          disabled:
                            !isAllowed(
                              permissions,
                              'stock.purchase-order.delete'
                            ) || params.row.poOrderStatus.id !== 6,
                          IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                          title: 'ลบรายการ',
                          onAction: () => {
                            setOpenDeleteModal({
                              status: true,
                              id: params.row.id,
                              poNo: params.row.poNo,
                            });
                          },
                          cssProps: {
                            color: '#D32F2F',
                            '&:hover': {
                              backgroundColor: '#FDE8EF',
                            },
                          },
                        },
                      ]}
                    />
                  ))}
              </div>
            )}
          </>
        );
      },
    },
  ];
  const handleRemovePo = async () => {
    setLoadingDeletePo(true);
    const res = await apiPurchaseOrder.deletePurchaseOrder(openDeleteModal.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ลบ ${openDeleteModal.poNo} สำเร็จ`,
          severity: 'success',
        })
      );
      await getList();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setOpenDeleteModal({
      ...openDeleteModal,
      status: false,
    });
    setLoadingDeletePo(false);
  };
  const onCreatePurchaseOrder = async (data: any) => {
    const res = await apiPurchaseOrder.createPurchaseOrder(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'สร้างรายการจัดซื้อสำเร็จ',
          severity: 'success',
        })
      );
      getList();
      getPurchaseOrderStatus();
    }
  };
  return (
    <>
      <AppModalConfirm
        open={openDeleteModal.status}
        isReason={false}
        onClickClose={() => {
          setOpenDeleteModal(false);
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ลบรายการจัดซื้อ`}
        confirmDescription={`”ยืนยันที่จะลบ ${openDeleteModal.poNo} ออกจากรายการ”`}
        loadingConfirm={loadingDeletePo}
        onConfirm={() => {
          handleRemovePo();
        }}
      />
      <ProductNav title="รายการจัดซื้อ" showBorderBottom>
        <ActionGroupStyle>
          <ModalRequestPurchaseOrder
            onCreatePurchaseOrder={(data: any) => onCreatePurchaseOrder(data)}
          >
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="สร้างรายการซื้อ"
              borderRadius={'20px'}
              disabled={!isAllowed(permissions, 'stock.purchase-order.create')}
            />
          </ModalRequestPurchaseOrder>
          {/* <ModalCreatePurchaseOrder */}
          {/*  handleReloadList={async () => { */}
          {/*    await getList(); */}
          {/*  }} */}
          {/* > */}
          {/*  <ActionButton */}
          {/*    variant="contained" */}
          {/*    color="Hon" */}
          {/*    icon={<AddCircle />} */}
          {/*    text="สร้างรายการซื้อ" */}
          {/*    borderRadius={'20px'} */}
          {/*  /> */}
          {/* </ModalCreatePurchaseOrder> */}
        </ActionGroupStyle>
      </ProductNav>
      {isAllowed(permissions, 'stock.purchase-order.list') ? (
        <AppContentStyle>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div
                style={{
                  minWidth: '800px',
                }}
              >
                <Tabs
                  value={Number(poOrderStatusId)}
                  onChange={handleChangeStatus}
                  variant="standard"
                >
                  {purchaseOrderStatus.map((item: any, index: React.Key) => {
                    return (
                      <Tab
                        key={index}
                        label={
                          <div
                            className="flex items-center"
                            style={{
                              columnGap: '16px',
                              minHeight: '32px',
                            }}
                          >
                            {item.status}
                            <Badge
                              badgeContent={item.count || '0'}
                              color="secondary"
                              sx={{
                                '.MuiBadge-badge': {
                                  backgroundColor:
                                    Number(poOrderStatusId) === item.id
                                      ? '#263238'
                                      : '#607D8B',
                                },
                              }}
                            />
                          </div>
                        }
                        value={item.id}
                        sx={{
                          color:
                            Number(poOrderStatusId) === item.id
                              ? '#263238'
                              : '',
                        }}
                      />
                    );
                  })}
                </Tabs>
              </div>
              <SearchInput
                makeSearchValue={(newValue) =>
                  setFilters({
                    ...filters,
                    search: newValue,
                    page: 0,
                  })
                }
              />
            </FilterWrapStyled>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={124} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    loading={loading}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        </AppContentStyle>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

PurchaseOrderList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query, req, res } = context;
  const { poOrderStatusId } = query;
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
        poOrderStatusId: poOrderStatusId || '1',
      },
    };
  }
  return {
    props: {},
  };
};
export default PurchaseOrderList;
