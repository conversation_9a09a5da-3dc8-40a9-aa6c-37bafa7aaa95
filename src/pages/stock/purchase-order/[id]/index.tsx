import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';

import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { useRouter } from 'next/router';
import { isEmpty, isNull } from 'lodash';
import { IconButton } from '@mui/material';
import PoDetailHeader from '@/components/purchase-order/PoDetailHeader';
import PoInfoDetail from '@/components/purchase-order/PoInfoDetail';
import PoMaterialDetail from '@/components/purchase-order/PoMaterialDetail';
import SideDetail from '@/components/SideDetail';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import styled from 'styled-components';
import Image from 'next/image';
import dayjs from 'dayjs';
import 'dayjs/locale/th'; // นำเข้า locale ภาษาไทย
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

dayjs.extend(customParseFormat);

dayjs.locale('th');
const PoLogStyle = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  row-gap: 22px;
  padding: 24px 0;
  .list {
    display: flex;
    column-gap: 12px;
    .profile {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      min-width: 40px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .text-zone {
      display: flex;
      flex-direction: column;
      margin-top: -4px;
      line-height: 1;
      row-gap: 5px;
      .name {
        font-size: 16px;
        font-weight: 600;
      }
      .action {
        font-weight: 400;
      }
      .timestamp {
        font-size: 12px;
        color: #90a4ae;
      }
    }
  }
`;
const PurchaseOrderEdit = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { id } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [purchaseOrderById, setPurchaseOrderById] = useState<any>({});
  const [loadingCancelPo, setLoadingCancelPo] = useState<boolean>(false);
  const [openSideDetail, setOpenSideDetail] = useState<boolean>(false);
  const [logDetail, setLogDetail] = useState<any>({});
  const getPurchaseOrderById = async () => {
    const res = await apiPurchaseOrder.getPurchaseOrderById(id as string);
    if (!res.isError) {
      setPurchaseOrderById(res.data);
    } else {
      router.push('/stock/purchase-order');
      setPurchaseOrderById({
        id: 1,
        poNo: 'LO-*********',
        poDate: 1712550714414,
        expireDate: 1712550714414,
        creditDay: 15,
        tax: {
          id: 2,
          taxType: 'exclude',
          taxRate: 7,
          nameTax: 'แยกภาษีมูลค่าเพิ่ม 7%',
        },
        contact: {
          id: 1,
          code: 'CU-*********',
          taxNumber: '293-287-8410',
          taxAddress: 'เชียงใหม่',
          province: {
            id: 38,
            geoId: 1,
            provinceCode: '50',
            name: 'เชียงใหม่',
          },
          district: {
            id: 581,
            geoId: 1,
            districtCode: '5014',
            name: 'สันทราย',
          },
          subDistrict: {
            id: 5278,
            geoId: 1,
            subDistrictCode: '501404',
            name: 'สันนาเม็ง',
          },
          zipcode: '501404',
          name: 'Christine Ratke',
          email: '<EMAIL>',
          phoneNumber: '************',
          contactType: {
            id: 1,
            name: 'นิติบุคคล',
          },
          imageUrl: null,
          creditType: {
            id: 2,
            day: 30,
          },
        },
        poOrderList: [
          {
            id: 1,
            rawMaterial: {
              id: 3,
              rawMaterialNo: 'RW001',
              name: 'Fantastic',
              description: null,
              subMaterialDetail: {
                id: 1,
                name: 'กระดาษคราฟท์ A 110 แกรม',
                side: 1,
                subMaterialId: 1,
              },
              brand: {
                id: 2,
                name: 'OFM blog',
                description: null,
                imageUrl: null,
              },
              pickingType: {
                id: 1,
                name: 'FIFO',
              },
              imageUrl:
                'https://cdn.honconnect.co/dev/1/raw-material/3/HON-1724932914910',
              isPurchase: true,
              isSell: false,
              isSerialNumber: true,
              isLotExpirationDate: true,
              itemSize: {
                id: 3,
                name: '635 * 915 มิลิเมตร',
              },
            },
            quantity: 200,
            priceUnit: 20,
            discountUnit: 0,
            stock: {
              id: 1,
              name: 'คลังหลัก',
              description: null,
              address: null,
              isDefault: true,
            },
            totalPrice: 4000,
            poOrderStatus: {
              id: 1,
              status: 'รอสั่งซื้อ',
            },
          },
        ],
        totalSubtotal: 5000,
        totalDiscount: 0,
        totalDiscountRate: 0,
        totalTax: 350,
        shippingPrice: 100,
        totalPrice: 5450,
        description: 'แยกภาษี',
        poOrderStatus: {
          id: 1,
          status: 'รอสั่งซื้อ',
        },
        createdDate: 1724935465101,
        createdUser: {
          id: 2,
          name: 'mock user',
          imageUrl: null,
        },
        isUpdate: false,
        isComplete: false,
        isPayment: false,
        poOrderPayment: [],
      });
    }
  };

  useEffect(() => {
    getPurchaseOrderById();
  }, []);
  const handleRevertStep = async () => {
    const res = await apiPurchaseOrder.revertStep(id as string);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ตีกลับไปแก้ไขสำเร็จ',
          severity: 'success',
        })
      );
      await router.push(
        `/stock/purchase-order?purchaseOrderById=${
          purchaseOrderById.poOrderStatus.id - 1
        }`
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };
  const formatDate = (date: string) => {
    return dayjs(date).add(543, 'year').format('D MMM YYYY, HH.mm น.');
  };
  const handleCancelPo = async (reason: any) => {
    setLoadingCancelPo(true);
    const res = await apiPurchaseOrder.cancelPo(id as string, reason);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ยกเลิกการสั่งซื้อสำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/stock/purchase-order?poOrderStatusId=6');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoadingCancelPo(false);
  };
  const handleOpenLog = async () => {
    const res = await apiPurchaseOrder.getLog(purchaseOrderById.id);
    if (!res.isError) {
      setLogDetail(res.data);
    }
  };
  useEffect(() => {
    if (!isEmpty(logDetail)) {
      setOpenSideDetail(true);
    }
  }, [logDetail]);
  return (
    <>
      <ProductNav
        title={
          !isEmpty(purchaseOrderById)
            ? `${purchaseOrderById.poNo} (${purchaseOrderById.poOrderStatus.status})`
            : ''
        }
        showBorderBottom
        backUrl={`/stock/purchase-order?poOrderStatusId=${
          purchaseOrderById?.poOrderStatus?.id || '1'
        }`}
      />
      {!isEmpty(purchaseOrderById) &&
        isAllowed(permissions, 'stock.purchase-order.list') && (
          <>
            <SideDetail isOpen={openSideDetail}>
              {!isEmpty(logDetail) && (
                <>
                  <div className="header">
                    <div className="topic">ประวัติรายการ</div>
                    <div
                      className="x-close"
                      onClick={() => {
                        setOpenSideDetail(false);
                      }}
                    >
                      <IconButton>
                        <CloseIcon />
                      </IconButton>
                    </div>
                  </div>
                  <div className="content">
                    <div className="body">
                      <PoLogStyle>
                        {logDetail.map((item: any) => {
                          return (
                            <div className="list" key={item.id}>
                              <div className="profile">
                                <Image
                                  src={
                                    item.user.imageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  fill
                                  alt=""
                                />
                              </div>
                              <div className="text-zone">
                                <div className="name">{item.user.name}</div>
                                <div className="action">{item.log}</div>
                                {!isNull(item.remark) && (
                                  <div className="action">
                                    หมายเหตุ: {item.remark}
                                  </div>
                                )}
                                <div className="timestamp">
                                  {formatDate(item.createdDate)}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </PoLogStyle>
                    </div>
                  </div>
                </>
              )}
            </SideDetail>
            <PoDetailHeader
              purchaseOrderById={purchaseOrderById}
              handleRevertStep={handleRevertStep}
              handleCancelPo={(reason: {
                annotationId: any;
                note: any;
                reason: string;
              }) => {
                handleCancelPo(reason);
              }}
              loadingCancelPo={loadingCancelPo}
              handleOpenLog={handleOpenLog}
            />
            <PoInfoDetail purchaseOrderById={purchaseOrderById} />
            <PoMaterialDetail
              purchaseOrderById={purchaseOrderById}
              reloadPurchaseOrderById={() => {
                getPurchaseOrderById();
              }}
            />
          </>
        )}
      {!isAllowed(permissions, 'stock.purchase-order.list') && (
        <NotPermission />
      )}
    </>
  );
};

PurchaseOrderEdit.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default PurchaseOrderEdit;
