import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';

import PurchaseOrderForm from '@/components/purchase-order/PurchaseOrderForm';
import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import apiTax from '@/services/stock/tax';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const PurchaseOrderEdit = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [purchaseOrderById, setPurchaseOrderById] = useState<any>({});
  const [taxOptions, setTaxOptions] = useState<any>([]);
  const getPurchaseOrderById = async () => {
    const res = await apiPurchaseOrder.getPurchaseOrderById(id as string);
    if (!res.isError) {
      setPurchaseOrderById(res.data);
    } else {
      router.push('/stock/purchase-order');
    }
  };
  const getTaxOptions = async () => {
    const res = await apiTax.getOptions();
    if (!res.isError) {
      setTaxOptions(res.data);
    }
  };

  useEffect(() => {
    getPurchaseOrderById();
    getTaxOptions();
  }, []);

  return (
    <>
      <ProductNav
        title={
          !isEmpty(purchaseOrderById)
            ? `${purchaseOrderById.poNo} (${purchaseOrderById.poOrderStatus.status})`
            : ''
        }
        showBorderBottom
        backUrl={'/stock/purchase-order'}
      />
      {!isEmpty(purchaseOrderById) &&
        !isEmpty(taxOptions) &&
        isAllowed(permissions, 'stock.purchase-order.list') && (
          <PurchaseOrderForm
            purchaseOrderById={purchaseOrderById}
            taxOptions={taxOptions}
          />
        )}
      {!isAllowed(permissions, 'stock.purchase-order.list') && (
        <NotPermission />
      )}
    </>
  );
};

PurchaseOrderEdit.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default PurchaseOrderEdit;
