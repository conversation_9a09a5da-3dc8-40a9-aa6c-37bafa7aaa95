import React, { Fragment, ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { ACTION } from '@/utils/helper';
import useSWR from 'swr';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import apiComponentType from '@/services/stock/component-type';
import ComponentTypeForm from '@/components/master/ComponentTypeForm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

const fetcher = async (url: string, params: any) => {
  const response = await apiComponentType.getAll(params);
  return response.data;
};

const key = '/component/type';

const ComponentType = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const title = 'ประเภทค่าแรง';
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [action, setAction] = useState(ACTION.CREATE);
  const [dataSelect, setDataSelect] = useState<any>(null);
  // const [filters, setFilters] = useState<any>({
  //   componentCategoryId: null,
  // });
  const [componentCategoryId] = useState<any>(null);
  const { data, mutate } = useSWR(
    [
      key,
      {
        componentCategoryId: componentCategoryId,
      },
    ],
    ([url, { componentCategoryId }]) =>
      fetcher(url, {
        componentCategoryId,
      })
  );

  function onOpen(value: any, action: ACTION) {
    setAction(action);
    setOpenModal(true);
    setDataSelect(value);
  }

  function onClose() {
    setOpenModal(false);
  }

  function onCloseAction() {
    setOpenDeleteModal(false);
    setDataSelect(null);
  }

  function selectDataAction(value: any, action: ACTION) {
    setAction(action);
    setOpenDeleteModal(true);
    setDataSelect(value);
  }

  async function reloadData() {
    const newData = await fetcher(key, {
      componentCategoryId: componentCategoryId,
    });
    await mutate(newData, { revalidate: false });
  }

  async function onDelete() {
    setLoading(true);
    const res = await apiComponentType.delete(dataSelect.id);
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
      await reloadData();
    }
    onCloseAction();
    setLoading(false);
  }

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'รายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row.description || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'countComponent',
      headerName: 'ค่าแรง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              lineHeight: '1',
            }}
          >
            {params.row.countComponent || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 64,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(
                    permissions,
                    'master.type-component.update'
                  ),
                  IconElement: () => <SvgPencilIcon />,
                  title: 'แก้ไข',
                  onAction: () => {
                    onOpen(params.row, ACTION.UPDATE);
                  },
                },
                {
                  disabled: !isAllowed(
                    permissions,
                    'master.type-component.delete'
                  ),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    selectDataAction(params.row, ACTION.DELETE);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];

  return (
    <Fragment>
      <ComponentTypeForm
        title={`${action === ACTION.CREATE ? 'สร้าง' : 'แก้ไข'} ${title}`}
        openModal={openModal}
        action={action}
        onClose={onClose}
        reloadData={reloadData}
        initialValues={dataSelect}
      />
      <AppModalConfirm
        open={openDeleteModal}
        onClickClose={() => {
          onCloseAction();
        }}
        confirmTitle={`ลบ ${dataSelect?.name || ''}`}
        confirmDescription={`คุณต้องการที่จะลบ ${dataSelect?.name || ''}`}
        loadingConfirm={loading}
        onConfirm={() => {
          onDelete();
        }}
      />
      <ProductNav title={title} showBorderBottom={false}>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text={`สร้าง ${title}`}
            borderRadius={'20px'}
            onClick={() => {
              onOpen(null, ACTION.CREATE);
            }}
            disabled={!isAllowed(permissions, 'master.type-component.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        {isAllowed(permissions, 'master.type-component.list') ? (
          <div className="content-wrap">
            {data ? (
              <AppTableStyle $rows={data}>
                <div className="content-wrap">
                  <ScrollBarStyled>
                    <HeaderColumnAction text="จัดการ" width={224} />
                    <DataGrid
                      hideFooter={true}
                      rows={data || []}
                      columns={columns}
                      paginationMode="server"
                      rowCount={data.length || 0}
                      // pageSize={filters.sizes}
                      disableSelectionOnClick={false}
                      autoHeight={true}
                      sortModel={[]}
                      getRowHeight={() => 56}
                      headerHeight={48}
                      components={{
                        NoRowsOverlay: () => <TableNoRowsOverlay />,
                        LoadingOverlay: () => <TableLoadingOverlay />,
                      }}
                    />
                  </ScrollBarStyled>
                </div>
              </AppTableStyle>
            ) : null}
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </Fragment>
  );
};

ComponentType.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default ComponentType;
