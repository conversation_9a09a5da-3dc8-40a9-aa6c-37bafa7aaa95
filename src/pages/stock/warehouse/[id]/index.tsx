import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Badge, Button, Checkbox, Tab, Tabs } from '@mui/material';
import AppPagination from '@/components/global/AppPagination';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import {
  AppContentStyle,
  AppTableStyle,
  CustomIosSwitchStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ProductNav from '@/components/product/ProductNav';
import { numberWithCommas } from '@/utils/number';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import apiOrder from '@/services/order/order';
import SearchInput from '@/components/SearchInput';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

type LayoutDataListPageProps = {
  status: string;
  authorities: string[];
};
const StockWarehouse = ({ status, authorities }: LayoutDataListPageProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const [rows, setRows] = useState<any>([]);
  const [totalElements, setTotalElements] = useState(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [statusCount] = useState<any>({
    draft: 0,
    laying: 0,
    priceCalculate: 0,
    readyQuotation: 0,
    quotation: 0,
    unused: 0,
  });
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 10,
    searchName: '',
    startDate: null,
    endDate: null,
    orderStatus: 0,
    productType: '',
  });
  const warehouseStatus = [
    {
      name: 'ทั้งหมด',
      status: 'all',
    },
    {
      name: 'รออนุมัติการโอน',
      status: 'waitingTransfer',
    },
    {
      name: 'ตรวจรับสินค้า',
      status: 'inspectProduct',
    },
    {
      name: 'สินค้าชำรุด',
      status: 'damagedProduct',
    },
  ];

  useEffect(() => {
    getList(status);
  }, [status, filters]);
  const getList = async (status: string) => {
    setLoading(true);
    let numberOrderStatus;
    switch (status) {
      case 'all':
        numberOrderStatus = 0;
        break;
      case 'waitingTransfer':
        numberOrderStatus = 1;
        break;
      case 'inspectProduct':
        numberOrderStatus = 2;
        break;
      case 'damagedProduct':
        numberOrderStatus = 3;
        break;
      default:
        numberOrderStatus = 0;
    }
    const res = await apiOrder.getList({
      ...filters,
      orderStatus: numberOrderStatus,
    });
    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
    setLoading(false);
  };

  const handleChangeStatus = (event: React.SyntheticEvent, newStatus: any) => {
    router.push(`/stock/warehouse/1?status=${newStatus}`, undefined);
  };
  // const handleOpenSnackBar = (props: any) => {
  //   setOpenSnackBar(props);
  //   if (props.severity === 'success') {
  //     getList(`${router.query.status}`);
  //   }
  // };
  const columns: GridColDef[] = [
    {
      field: 'checkbox',
      headerName: '',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 52,
      disableColumnMenu: true,
      sortable: false,
      renderHeader: (_params: any) => (
        <Checkbox
          color="primary"
          checked={true}
          onChange={(_event: any) => {}}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
      renderCell: (_params: any) => (
        <Checkbox
          color="primary"
          checked={false}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          onChange={(_e: any) => {}}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
    },
    {
      field: 'name',
      headerName: 'รหัส',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 72,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {params.row.customer.name}
          </div>
        );
      },
    },
    {
      field: 'product',
      headerName: 'ชื่อสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 194,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center MuiDataGrid-cellContent"
            style={{
              columnGap: '14px',
            }}
          >
            <Image
              src={
                params.row.product.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '8px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.product.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'ad',
      headerName: 'แบรนด์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 194,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'ads',
      headerName: 'สเปค',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 144,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'sdf',
      headerName: 'หมวดหมู่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 144,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'fsd',
      headerName: 'ประเภท',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 144,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'fsdd',
      headerName: 'คงเหลือ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 120,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'fsdda',
      headerName: 'พร้อมใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 120,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'ffsdda',
      headerName: 'ชำรุด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 120,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{params.row.product.name}</>;
      },
    },
    {
      field: 'fegn',
      headerName: 'การใช้งาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 64,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <CustomIosSwitchStyle>
            <IOSSwitch
              sx={{ m: 1 }}
              checked={true}
              onClick={(_e: any) => {
                // changeItemStatus(params.row.id, e.target.checked);
              }}
            />
          </CustomIosSwitchStyle>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 170,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <>
            <div className="flex items-center">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                style={{
                  width: '84px',
                  height: '32px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  marginRight: '8px',
                }}
                onClick={() => {}}
                disabled={!isAllowed(permissions, 'stock.warehouse.list')}
              >
                รายละเอียด
              </Button>
              <PopoverAction
                triggerElement={
                  <div className="kebab">
                    <div className="dot" />
                  </div>
                }
                customItems={[
                  {
                    disabled: !isAllowed(permissions, 'stock.warehouse.update'),
                    IconElement: () => <SvgPencilIcon />,
                    title: 'แก้ไข',
                    onAction: () => null,
                  },
                  {
                    disabled: !isAllowed(permissions, 'stock.warehouse.delete'),
                    IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                    title: 'ลบรายการ',
                    onAction: () => null,
                    cssProps: {
                      color: '#D32F2F',
                      '&:hover': {
                        backgroundColor: '#FDE8EF',
                      },
                    },
                  },
                ]}
              />
            </div>
          </>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav
        title="คลังอะไหล่เครื่องจักร"
        showBorderBottom
        backUrl="/stock/warehouse"
      />
      {isAllowed(permissions, 'stock.warehouse.list') ? (
        <AppContentStyle>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div
                style={{
                  minWidth: '718px',
                }}
              >
                <Tabs
                  value={status}
                  onChange={handleChangeStatus}
                  variant="standard"
                >
                  {warehouseStatus.map((item, index) => (
                    <Tab
                      key={index}
                      label={
                        <div
                          className="flex items-center"
                          style={{
                            columnGap: '16px',
                            minHeight: '32px',
                          }}
                        >
                          {item.name}
                          <Badge
                            badgeContent={
                              numberWithCommas(statusCount[item.status]) || '0'
                            }
                            color="secondary"
                            sx={{
                              '.MuiBadge-badge': {
                                backgroundColor:
                                  status !== item.status ? '#78909C' : '',
                              },
                            }}
                          ></Badge>
                        </div>
                      }
                      value={item.status}
                      sx={{
                        color: status !== item.status ? '#78909C' : '',
                      }}
                    />
                  ))}
                </Tabs>
              </div>
              <div className="order-date">
                {/* <Divider orientation="vertical" variant="middle" flexItem /> */}
                <SearchInput
                  makeSearchValue={(newValue) =>
                    setFilters({
                      ...filters,
                      searchName: newValue,
                      page: 0,
                    })
                  }
                />
              </div>
            </FilterWrapStyled>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap check-box-table">
                <ScrollBarStyled>
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    loading={loading}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
              </div>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={totalElements || 0}
                  handleChangeFilters={(newValues: any) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </AppTableStyle>
          </div>
        </AppContentStyle>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

StockWarehouse.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query, req, res } = context;
  const { status } = query;
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
        status: status || 'all',
      },
    };
  }
  return {
    props: {},
  };
};
export default StockWarehouse;
