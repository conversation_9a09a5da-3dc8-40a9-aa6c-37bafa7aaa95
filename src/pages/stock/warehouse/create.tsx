import React, { ReactElement, useEffect, useState } from 'react';
import 'dayjs/locale/th';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import apiWarehouse from '@/services/stock/warehouse';
import WarehouseForm from '@/components/warehouse/WarehouseForm';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CreateWarehouse = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);

  const createWarehouse = async (value: any) => {
    setSubmitting(true);
    const res = await apiWarehouse.createWarehouse(value);
    if (!res.isError) {
      setDisable(true);
      dispatch(
        setSnackBar({
          status: true,
          text: 'สร้างคลังสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/stock/warehouse');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };

  return (
    <>
      <ProductNav
        title="สร้างคลังสินค้า"
        showBorderBottom={true}
        showAvatar={false}
        backUrl={'/stock/warehouse'}
      />
      {isAllowed(permissions, 'stock.warehouse.create') ? (
        <WarehouseForm
          submitting={submitting}
          disable={disable}
          handleSubmitForm={createWarehouse}
        />
      ) : (
        <NotPermission />
      )}
    </>
  );
};

CreateWarehouse.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default CreateWarehouse;
