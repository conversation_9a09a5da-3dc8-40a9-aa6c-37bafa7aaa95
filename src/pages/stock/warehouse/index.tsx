import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import AppPagination from '@/components/global/AppPagination';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { OpenDeleteType } from '@/types/category';
import { Button } from '@mui/material';
import { numberWithCommas } from '@/utils/number';
import apiWarehouse from '@/services/stock/warehouse';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';
import PopoverAction from '@/components/PopoverActionn';
import NotPermission from '@/components/NotPermission';

const LabelMainStockStyle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  border: 1px solid #00c1af;
  color: #00c1af;
  font-size: 12px;
  line-height: 1;
  padding: 0 8px;
`;

const WarehouseList = ({ authorities }: { authorities: string[] }) => {
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const router = useRouter();
  const [rows, setRows] = useState([]);
  const [totalElements, setTotalElements] = useState(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
  });
  const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
    status: false,
    name: '',
    id: null,
  });
  const [loadingRows, setLoadingRows] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    getWarehouse();
  }, [filters]);

  const getWarehouse = async () => {
    setLoadingRows(true);
    const res = await apiWarehouse.getList(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
      setTotalElements(res.data.totalElements);
    }
    setLoadingRows(false);
  };
  const handleDelete = async () => {
    setLoading(true);
    if (openDelete.id) {
      const res = await apiWarehouse.deleteWarehouse(openDelete.id);
      if (!res.isError) {
        getWarehouse();
        dispatch(
          setSnackBar({
            status: true,
            text: `ลบ ${openDelete.name} สำเร็จ`,
            severity: 'success',
          })
        );
      }
      setOpenDelete({
        ...openDelete,
        status: false,
      });
    }
    setLoading(false);
  };
  const handleOpenDelete = async (name: string, id: number) => {
    setOpenDelete({
      name,
      status: true,
      id,
    });
  };
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              columnGap: '14px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.name}
            </div>
            {params.row.isDefault && (
              <LabelMainStockStyle>คลังหลัก</LabelMainStockStyle>
            )}
          </div>
        );
      },
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {params.row.description || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'address',
      headerName: 'ที่อยู่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {params.row.address || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'countLot',
      headerName: 'มูลค่าสินค้าคงเหลือ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 188,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <>{numberWithCommas(params.row.lotCount)}</>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 188,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '84px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              onClick={() => {
                router.push(`/stock/warehouse/${params.row.id}`);
              }}
              disabled={!isAllowed(permissions, 'stock.warehouse.list')}
            >
              รายละเอียด
            </Button>
            {/* <KebabTable */}
            {/*  item={params.row} */}
            {/*  handleRemove={(item: any) => { */}
            {/*    handleOpenDelete(item.name, item.id); */}
            {/*  }} */}
            {/*  isRemove={params.row.lotCount === 0 && !params.row.isDefault} */}
            {/*  isEdit={{ */}
            {/*    status: true, */}
            {/*    url: `/stock/warehouse/${params.row.id}/edit`, */}
            {/*  }} */}
            {/* /> */}
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: !isAllowed(permissions, 'stock.warehouse.update'),
                  IconElement: () => <SvgPencilIcon />,
                  title: 'แก้ไข',
                  onAction: () => {
                    router.push(`/stock/warehouse/${params.row.id}/edit`);
                  },
                },
                {
                  disabled:
                    (params.row.lotCount === 0 && !params.row.isDefault) ||
                    !isAllowed(permissions, 'stock.warehouse.delete'),
                  IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                  title: 'ลบรายการ',
                  onAction: () => {
                    handleOpenDelete(params.row.name, params.row.id);
                  },
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </div>
        );
      },
    },
  ];
  return (
    <>
      <ProductNav title="คลังสินค้า" showBorderBottom>
        <ActionGroupStyle>
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างคลังสินค้า"
            borderRadius={'20px'}
            onClick={() => {
              router.push('/stock/warehouse/create');
            }}
            disabled={!isAllowed(permissions, 'stock.warehouse.create')}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppModalConfirm
        open={openDelete.status}
        onClickClose={() => {
          setOpenDelete({
            ...openDelete,
            status: false,
            id: null,
          });
        }}
        confirmTitle={`ลบ ${openDelete.name}`}
        confirmDescription={'คุณต้องการที่จะลบคลังสินค้านี้?'}
        loadingConfirm={loading}
        onConfirm={() => {
          handleDelete();
        }}
      />
      <AppContentStyle>
        {isAllowed(permissions, 'stock.warehouse.list') ? (
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={188} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows || []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements || 0}
                    // pageSize={filters.sizes}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    loading={loadingRows}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        ) : (
          <NotPermission />
        )}
      </AppContentStyle>
    </>
  );
};

WarehouseList.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default WarehouseList;
