import React from 'react';
import getConfig from 'next/config';
import { GetServerSideProps } from 'next';
import { setCookie } from 'cookies-next';

const { publicRuntimeConfig } = getConfig();

const RedirectAuth = () => {
  return <div />;
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const client = publicRuntimeConfig.AUTH_CLIENT;
  const secret = publicRuntimeConfig.AUTH_SECRET;
  const headers = new Headers();
  headers.append('Content-type', 'application/json');
  headers.append(
    'Authorization',
    `Basic ${Buffer.from(`${client}:${secret}`).toString('base64')}`
  );

  const authUrl = publicRuntimeConfig.AUTH_ENDPOINT;
  const redirectUrl = publicRuntimeConfig.REDIRECT_URL;
  const initialUrl = `${authUrl}/oauth2/token?client_id=${client}&scope=openid%20profile%20user.read&redirect_uri=${redirectUrl}&grant_type=authorization_code`;
  // const url = `${initialUrl}&code=${code}&code_verifier=${verifier}`;
  const url = `${initialUrl}&code=${query.code}`;

  const response = await fetch(url, {
    method: 'POST',
    mode: 'cors',
    headers,
  });

  const data = await response.json();
  if (!data.error) {
    setCookie('access_token', data.access_token, {
      req,
      res,
      maxAge: 60 * 60 * 24,
    });
    setCookie('refresh_token', data.refresh_token, {
      req,
      res,
      maxAge: 60 * 60 * 24,
    });
    setCookie('selected-company-id', '0', {
      req,
      res,
      maxAge: 60 * 60 * 24,
    });
    return {
      redirect: {
        destination: '/company',
        permanent: true,
      },
    };
  }
  return {
    redirect: {
      destination: '/',
      permanent: true,
    },
  };
};

export default RedirectAuth;
