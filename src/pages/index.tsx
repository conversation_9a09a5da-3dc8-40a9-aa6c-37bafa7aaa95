import styled, { css } from 'styled-components';
import { ReactElement, useEffect, useState } from 'react';
import AuthLayout from '@/layouts/AuthLayout';
import { LoadingFadein } from '@/styles/share.styled';
import { getCookie, setCookie } from 'cookies-next';
import { userSelector } from '@/store/features/user';
import { useAppSelector } from '@/store';
import { isEmpty } from 'lodash';
import Cookies from 'js-cookie';
import { GetServerSideProps } from 'next';

const LoginFormStyle = styled.div<{ $isCheck: boolean }>`
  position: relative;
  height: calc(100vh - 124px);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  animation: ${LoadingFadein} 0.3s ease-in;
  margin-top: -100px;
  @media screen and (max-width: 480px) {
    height: calc(100vh - 104px);
    margin-top: -152px;
  }
  .topic-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    @media screen and (max-width: 575px) {
      margin-bottom: 0;
    }
  }
  .create-account {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 800px;
    width: 480px;
    @media screen and (max-width: 575px) {
      width: 85%;
    }
  }
  .third-party {
    display: flex;
    column-gap: 24px;
    @media screen and (max-width: 575px) {
      flex-direction: column;
      row-gap: 14px;
    }
    button {
      display: flex;
      align-items: center;
      justify-content: start;
      column-gap: 12px;
      position: relative;
      @media screen and (max-width: 575px) {
        justify-content: center;
      }
      span {
        margin: 0;
        @media screen and (max-width: 575px) {
          position: absolute;
          left: 12px;
        }
      }
      .text {
        white-space: nowrap;
      }
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  h1 {
    margin-top: 0;
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
  }
  .check-box-remember {
    label {
      display: flex;
      align-items: center;
      column-gap: 8px;
      position: relative;
      cursor: pointer;
      user-select: none;
      font-size: 14px;
      @media screen and (max-width: 480px) {
        font-size: 14px;
      }
      .icon {
        opacity: 0;
        justify-content: center;
        align-items: center;
        position: absolute;
        color: white;
        height: 24px;
        aspect-ratio: 1/1;
        display: flex;
        transition: 0.15s;
        @media screen and (max-width: 480px) {
          height: 20px;
        }
        ${({ $isCheck }) =>
          $isCheck &&
          css`
            opacity: 1 !important;
          `}
        svg {
          font-size: 18px;
        }
      }
      input {
        margin: 0;
        height: 24px;
        aspect-ratio: 1/1;
        border-radius: 6px;
        appearance: none;
        transition: 0.15s;
        border: 2px solid #dbe2e5;
        @media screen and (max-width: 480px) {
          height: 20px;
        }
        ${({ $isCheck }) =>
          $isCheck &&
          css`
            background-color: #263238 !important;
            border: none;
          `}
      }
    }
  }
`;

// const validationSchema = yup.object({
//   username: yup.string().email().required('กรุณากรอกอีเมล'),
//   password: yup.string().required('กรุณากรอกรหัสผ่าน'),
// });

const LoginPage = () => {
  // const router = useRouter();
  // const { ref } = router.query;
  // // const [loading, setLoading] = useState(false);
  // const [showPassword, setShowPassword] = useState(false);
  const [isCheck] = useState<boolean>(false);
  // const dispatch = useAppDispatch();
  const { user } = useAppSelector(userSelector);
  // const client = publicRuntimeConfig.AUTH_CLIENT;
  // const authUrl = publicRuntimeConfig.AUTH_ENDPOINT;
  // const redirectUrl = publicRuntimeConfig.REDIRECT_URL;
  //
  // // const token = getCookie('access_token');
  // // const formik = useFormik({
  // //   initialValues: {
  // //     username: '',
  // //     password: '',
  // //   },
  // //   validationSchema,
  // //   onSubmit: (values: any) => {
  // //     loginWithPassword(values);
  // //   },
  // // });
  // const handleChange = (e: boolean) => {
  //   const isCheck = e;
  //   setIsCheck(isCheck);
  // };

  useEffect(() => {
    if (user && !isEmpty(user.company)) {
      setCookie('selected-company-id', user.company.id);
    } else {
      Cookies.remove('selected-company-id');
    }
  }, [user]);

  // const loginWithPassword = async (values: LoginWithPasswordType) => {
  //   setLoading(true);
  //   const res = await apiUser.login(values);
  //   if (res && !res.isError) {
  //     setCookie('access_token', res.token);
  //     await dispatch(getUserProfile(token));
  //     router.push('/company');
  //   } else {
  //     Swal.fire({
  //       title: 'ไม่สามารถเข้าสู่ระบบได้',
  //       text: 'กรุณาตรวจสอบ อีเมล และ รหัสผ่าน',
  //       icon: 'error',
  //     });
  //   }
  //
  //   setLoading(false);
  // };

  return (
    <LoginFormStyle $isCheck={isCheck}>
      {/* <div> */}
      {/*  <div className="topic-group"> */}
      {/*    <span className="text-[28px] font-[600]">เข้าสู่ระบบ</span> */}
      {/*    <span className="text-[12px]">Printing Factory Management</span> */}
      {/*    <span className="text-[12px]">Version 1.0.0</span> */}
      {/*  </div> */}
      {/*  <p className="field-title">อีเมล</p> */}
      {/*  <TextField */}
      {/*    type="email" */}
      {/*    name="username" */}
      {/*    placeholder="Email" */}
      {/*    // value={formik.values.username} */}
      {/*    // onChange={formik.handleChange} */}
      {/*    // error={formik.touched.username && Boolean(formik.errors.username)} */}
      {/*    // helperText={formik.touched.username && formik.errors.username} */}
      {/*  /> */}
      {/*  <p className="field-title">รหัสผ่าน</p> */}
      {/*  <TextField */}
      {/*    type={showPassword ? 'text' : 'password'} */}
      {/*    name="password" */}
      {/*    placeholder="Password" */}
      {/*    // value={formik.values.password} */}
      {/*    // onChange={formik.handleChange} */}
      {/*    InputProps={{ */}
      {/*      endAdornment: ( */}
      {/*        <InputAdornment */}
      {/*          className="cursor-pointer" */}
      {/*          position="end" */}
      {/*          onClick={() => setShowPassword(!showPassword)} */}
      {/*        > */}
      {/*          {showPassword ? <VisibilityOff /> : <Visibility />} */}
      {/*        </InputAdornment> */}
      {/*      ), */}
      {/*    }} */}
      {/*    // error={formik.touched.password && Boolean(formik.errors.password)} */}
      {/*    // helperText={formik.touched.password && formik.errors.password} */}
      {/*  /> */}
      {/*  <div className="flex flex-row justify-between items-center py-10"> */}
      {/*    <div className="check-box-remember"> */}
      {/*      <label> */}
      {/*        <div className="icon"> */}
      {/*          <CheckIcon /> */}
      {/*        </div> */}
      {/*        <input */}
      {/*          type="checkbox" */}
      {/*          onChange={(event) => handleChange(event.target.checked)} */}
      {/*        /> */}
      {/*        จำข้อมูลของฉัน */}
      {/*      </label> */}
      {/*    </div> */}
      {/*    <Link */}
      {/*      href="/forgot-password" */}
      {/*      style={{ */}
      {/*        fontSize: '14px', */}
      {/*        color: '#B0BEC5', */}
      {/*      }} */}
      {/*    > */}
      {/*      ลืมรหัสผ่าน? */}
      {/*    </Link> */}
      {/*  </div> */}
      {/*  <Button */}
      {/*    variant="contained" */}
      {/*    color="dark" */}
      {/*    fullWidth */}
      {/*    sx={{ fontSize: '16px' }} */}
      {/*    onClick={() => login()} */}
      {/*  > */}
      {/*    เข้าสู่ระบบ */}
      {/*  </Button> */}
      {/*  <TextDivider text="หรือ" /> */}
      {/*  <div className="third-party"> */}
      {/*    <Button */}
      {/*      variant="outlined" */}
      {/*      fullWidth */}
      {/*      color="blueGrey" */}
      {/*      startIcon={ */}
      {/*        <Image src="/icons/facebook.png" width={32} height={32} alt="" /> */}
      {/*      } */}
      {/*    > */}
      {/*      <div className="text">เข้าสู่ระบบด้วย Facebook</div> */}
      {/*    </Button> */}
      {/*    <Button */}
      {/*      variant="outlined" */}
      {/*      fullWidth */}
      {/*      color="blueGrey" */}
      {/*      startIcon={ */}
      {/*        <Image src="/icons/google.png" width={32} height={32} alt="" /> */}
      {/*      } */}
      {/*    > */}
      {/*      <div className="text">เข้าสู่ระบบด้วย Google</div> */}
      {/*    </Button> */}
      {/*  </div> */}
      {/* </div> */}
      {/* <div className="create-account gap-1"> */}
      {/*  ยังไม่มีบัญชีกับเรา? */}
      {/*  <b */}
      {/*    onClick={() => { */}
      {/*      if (!isEmpty(ref)) { */}
      {/*        router.push(`/register?ref=${ref}`); */}
      {/*      } else { */}
      {/*        router.push('/register'); */}
      {/*      } */}
      {/*    }} */}
      {/*  > */}
      {/*    <div className="cursor-pointer">สร้างบัญชี</div> */}
      {/*  </b> */}
      {/* </div> */}
    </LoginFormStyle>
  );
};

LoginPage.getLayout = function getLayout(page: ReactElement) {
  return <AuthLayout>{page}</AuthLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    return {
      redirect: {
        destination: '/dashboard',
        permanent: false,
      },
    };
  }
  return {
    props: {},
  };
};

export default LoginPage;
