import React, { ReactElement } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import usePacdoraScript from '@/hooks/usePacdoraScript';
import usePacdoraInitialization from '@/hooks/usePacdoraInitialization';

function ModelPreview() {
  const modelId = 100010;
  const scriptLoaded = usePacdoraScript();
  const isInitialized = usePacdoraInitialization(scriptLoaded, modelId);

  return (
    <Container>
      <h1>Model Preview</h1>

      {!isInitialized && (
        <LoadingIndicator>
          <p>Loading 3D model...</p>
        </LoadingIndicator>
      )}

      <PreviewSection>
        <PreviewCard>
          <h2>3D View</h2>
          <ViewContainer data-pacdora-ui="3d" data-pacdora-id="view3d" />
        </PreviewCard>

        <PreviewCard>
          <h2>Die Line</h2>
          <ViewContainer data-pacdora-ui="dieline" />
        </PreviewCard>
      </PreviewSection>

      <PreviewSection>
        <PreviewCard>
          <h2>2D Screenshot</h2>
          <ViewContainer data-pacdora-ui="preview" />
        </PreviewCard>
        <PreviewCard>
          <h2>3D Screenshot</h2>
          <ViewContainer data-pacdora-ui="3d-preview" />
        </PreviewCard>
      </PreviewSection>
      <ActionSection>
        <ActionButton
          data-save-screenshot="true"
          data-screenshot-width="800"
          data-pacdora-id="design-btn"
          data-pacdora-ui="design-btn"
          disabled={!isInitialized}
        >
          Design Online
        </ActionButton>
        <ActionButton
          data-pacdora-ui="download"
          data-app-key="b2102111c3d24e66"
          disabled={!isInitialized}
        >
          Download Die-line
        </ActionButton>

        <ActionButton data-pacdora-ui="contact-us" disabled={!isInitialized}>
          Contact Us
        </ActionButton>
      </ActionSection>
    </Container>
  );
}

ModelPreview.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export default ModelPreview;

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const LoadingIndicator = styled.div`
  text-align: center;
  padding: 20px;
  font-size: 16px;
  color: #666;
`;

const PreviewSection = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
`;

const PreviewCard = styled.div`
  flex: 1;
  min-width: 300px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
  }
`;

const ViewContainer = styled.div`
  width: 100%;
  height: 300px;
  background-color: #f5f5f5;
  border-radius: 4px;
`;

const ActionSection = styled.div`
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  padding: 10px 20px;
  background-color: #3300ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    background-color: #2200cc;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;
