import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';

import { AddCircle } from '@mui/icons-material';
import ProductNav from '@/components/product/ProductNav';
import {
  AppContentStyle,
  BadgeStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { Avatar, Tab, Tabs } from '@mui/material';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import JobViewSwitcher from '@/components/job/JobViewSwitcher';
import JobTask from '@/components/job/JobTask';
import ModalCreateJob from '@/components/job/modal/ModalCreateJob';
import DataProductsOrderPanel from '@/components/job/DataProductsOrderPanel';
import DataTableAwaitingProduction from '@/components/job/DataTableAwaitingProduction';
import apiJob from '@/services/order/job';
import DataTableProductionStatus from '@/components/job/DataTableProductionStatus';
import apiWorkSchedule from '@/services/order/work-schedule';
import dayjs from 'dayjs';
import AppDateCalendar from '@/components/global/AppDateCalendar';

export type JobSwitcherType = 'lists' | 'tasks';

const JobPage = () => {
  const [dataWorkSchedule, setDataWorkSchedule] = useState<any>([]);
  const [filtersWork, setFiltersWork] = useState<any>(new Date());
  const { user } = useAppSelector(userSelector);
  const [totalElementStatus, setTotalElementStatus] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [rows, setRows] = useState<any[]>([]);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
    productionOrderStatusId: 1,
    isDesc: true,
  });
  const [openConfirmDelete, setOpenConfirmDelete] = useState<any>({
    open: false,
    title: '',
    description: '',
    id: null,
  });
  const [loadingTasks, setLoadingTasks] = useState<boolean>(false);
  const [loadingConfirm] = useState<boolean>(false);
  const [switcherValue, setSwitcherValue] = useState<JobSwitcherType>('lists');
  const [tabValue, setTabValue] = useState<number>(0);
  const [openCreateJob, setOpenCreateJob] = useState<boolean>(false);
  const [jobTabs, setJobTabs] = useState<any[]>([]);
  const [selectedLayDataId, setSelectedLayDataId] = useState<number | null>(
    null
  );

  const handleConfirmDelete = async () => {
    // setLoadingConfirm(true);
    // const res = await apiProduct.remove(openConfirmDelete.id);
    // dispatch(
    //   setSnackBar({
    //     status: true,
    //     text: res.message,
    //     severity: !res.isError ? 'success' : 'error',
    //   })
    // );
    // setLoadingConfirm(false);
    // setOpenConfirmDelete({
    //   ...openConfirmDelete,
    //   open: false,
    // });
    // if (res && !res.isError) {
    //   await getProducts();
    // }
  };

  const getWorkScheduleDashboard = async (date: any) => {
    setLoadingTasks(true);
    const params = {
      dateSearch: dayjs(date).format('YYYY-MM-DD'),
    };
    const res = await apiWorkSchedule.getWorkScheduleDashboard(params);
    if (res && !res.isError) {
      setDataWorkSchedule(res.data);
    }
    setLoadingTasks(false);
  };

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: number) => {
    setFilters({ ...filters, productionOrderStatusId: newValue });
    setTabValue(newValue);
  };

  const getListProductionOrderStatus = async () => {
    const res = await apiJob.getListProductionOrderStatus();
    if (res.status === true) {
      setJobTabs(res.data);
    }
  };

  const getProductionOrderPage = async (params: any) => {
    const res = await apiJob.getProductionOrderPage(params);
    if (res.status === true) {
      setRows(res.data.content);
      setTotalElementStatus(res.data.totalElements);
    }
  };

  const mockData = false;

  useEffect(() => {
    getWorkScheduleDashboard(filtersWork).then();
  }, [filtersWork]);

  useEffect(() => {
    getProductionOrderPage(filters).then();
  }, [filters]);

  useEffect(() => {
    getListProductionOrderStatus().then();
  }, []);

  const handleCreateWithRow = async (row: any) => {
    setSelectedLayDataId(row.id);
    setOpenCreateJob(true);
  };

  useEffect(() => {
    if (!openCreateJob) {
      setSelectedLayDataId(null);
    }
  }, [openCreateJob]);

  return (
    <>
      <ModalCreateJob
        open={openCreateJob}
        handleClose={() => {
          setOpenCreateJob(false);
        }}
        fetchData={async () => {
          await getListProductionOrderStatus();
          await getProductionOrderPage(filters);
          setTabValue(1);
        }}
        selectedLayDataId={selectedLayDataId}
      />
      <AppModalConfirm
        open={openConfirmDelete.open}
        onClickClose={() => {
          setOpenConfirmDelete({
            ...openConfirmDelete,
            open: false,
          });
        }}
        confirmTitle={openConfirmDelete.title}
        confirmDescription={openConfirmDelete.description}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleConfirmDelete();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="280px"
      />
      <ProductNav
        title={switcherValue === 'lists' ? 'รายการผลิตสินค้า' : 'แผนการผลิต'}
      >
        <div className="flex flex-row gap-2">
          <JobViewSwitcher
            switcherValue={switcherValue}
            handleClick={(value: JobSwitcherType) => {
              setSwitcherValue(value);
            }}
          />
          <AppDateCalendar
            data={filtersWork}
            handleChange={(date: any) => {
              setFiltersWork(date);
            }}
          />
          <ActionButton
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างรายการ"
            borderRadius={'20px'}
            onClick={async () => {
              setOpenCreateJob(true);
            }}
          />
          <Avatar
            src={user.imageUrl || '#'}
            style={{ width: '40px', height: '40px' }}
          >
            {user.name[0]}
          </Avatar>
        </div>
      </ProductNav>
      <AppContentStyle>
        {switcherValue !== 'tasks' && (
          <>
            {mockData && <DataProductsOrderPanel />}
            <ScrollBarStyled>
              <FilterWrapStyled>
                <div
                  style={{
                    minWidth: '472px',
                  }}
                >
                  <Tabs
                    value={tabValue}
                    onChange={handleChangeTab}
                    variant="standard"
                  >
                    <Tab
                      label={
                        <div
                          className="flex items-center"
                          style={{
                            columnGap: '8px',
                            minHeight: '32px',
                          }}
                        >
                          สินค้ารอผลิต
                          <BadgeStyle
                            className={`${tabValue !== 0 ? '' : 'active'} `}
                          >
                            {totalElements}
                          </BadgeStyle>
                        </div>
                      }
                      value={0}
                      sx={{
                        color: tabValue !== 0 ? '#78909C' : '',
                      }}
                    />
                    {jobTabs.map((item: any, _index: number) => {
                      return (
                        <Tab
                          key={item.id}
                          label={
                            <div
                              className="flex items-center"
                              style={{
                                columnGap: '8px',
                                minHeight: '32px',
                              }}
                            >
                              {item.name}
                              <BadgeStyle
                                className={`${
                                  tabValue !== item.id ? '' : 'active'
                                }`}
                              >
                                {item.count}
                              </BadgeStyle>
                            </div>
                          }
                          value={item.id}
                          sx={{
                            color: tabValue !== item.id ? '#78909C' : '',
                          }}
                        />
                      );
                    })}
                  </Tabs>
                </div>
              </FilterWrapStyled>
            </ScrollBarStyled>
          </>
        )}

        <div className="content-wrap">
          {switcherValue === 'lists' && (
            <>
              {tabValue === 0 && (
                <DataTableAwaitingProduction
                  totalElements={totalElements}
                  setTotalElements={(value: number) => setTotalElements(value)}
                  handleCreateWithRow={handleCreateWithRow}
                  // setOpenConfirmDelete={(value: any) =>
                  //   setOpenConfirmDelete({ ...value })
                  // }
                />
              )}
              {tabValue !== 0 && (
                <DataTableProductionStatus
                  rows={rows}
                  totalElements={totalElementStatus}
                  filters={filters}
                  setFilters={(value: number) =>
                    setFilters({ ...filters, page: value })
                  }
                />
              )}
            </>
          )}
          {switcherValue === 'tasks' && (
            <JobTask
              dataWorkSchedule={dataWorkSchedule}
              loadingTasks={loadingTasks}
              filtersWork={filtersWork}
              setFiltersWork={(date: any) => {
                setFiltersWork(date);
              }}
            />
          )}
        </div>
      </AppContentStyle>
    </>
  );
};

JobPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default JobPage;
