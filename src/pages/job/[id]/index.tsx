import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import { ActionGroupStyle } from '@/styles/share.styled';
import JobStatusChip from '@/styles/styledComponents/JobStatusChip.styled';
import WatchLaterRoundedIcon from '@mui/icons-material/WatchLaterRounded';
import { steps } from '@/services/stock/steps';
import JobNav from '@/components/job/JobNav';
import JobStep from '@/components/job/JobStep';
import ProductsListDetail from '@/components/job/ProductsListDetail';
import ProductDetailPanel from '@/components/job/ProductDetailPanel';
import PrintInformationDetail from '@/components/job/PrintInformationDetail';
import { TProductionOrder } from '@/types/prepare-material';
import apiJob from '@/services/order/job';
import JobStatusPrepareMaterials from '@/components/job/JobStatusPrepareMaterials';
import JobStatusStyles from '@/styles/styledComponents/JobStatusStyles.styled';
import ModalHistory from '@/components/job/modal/ModalHistory';
import JobStatusProduction from '@/components/job/JobStatusProduction';
import ModalStopProduction from '@/components/job/modal/ModalStopProduction';
import Image from 'next/image';
import ProductDetailCard from '@/components/job/ProductDetailCard';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { LineTop } from '@/styles/LineTop.styled';
import styled from 'styled-components';

const JobContentWrapperStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100dvh - 64px);
  overflow: auto;
  @media (max-width: 820px) {
    margin-top: 64px;
    height: calc(100dvh - 128px);
  }
`;
const Index = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id, step } = router.query;
  const [dataJob, setDataJob] = useState<TProductionOrder>();
  const [dataProductionOrderStage, setDataProductionOrderStage] =
    useState<any>();
  const [isFormStop, setIsFormStop] = useState(false);
  const [openHistory, setOpenHistory] = useState(false);
  const [openStop, setOpenStop] = useState(false);
  const [openRejectLayout, setOpenRejectLayout] = useState(false);
  const [loadingReject, setLoadingReject] = useState(false);
  const getProductionOrder = async (id: number) => {
    const res = await apiJob.getProductionOrder(id);
    if (res.status === true) {
      setDataJob(res.data);
    }
  };
  const getProductionOrderStageByProductionOrderId = async (
    productionOrderId: number
  ) => {
    const res = await apiJob.getProductionOrderStageByProductionOrderId(
      productionOrderId
    );
    if (!res.isError) {
      setDataProductionOrderStage(res.data);
    }
  };
  const approveProductionOrder = async (id: number) => {
    const res = await apiJob.approveProductionOrder(id);
    if (res && !res.isError) {
      getProductionOrder(id);
      dispatch(
        setSnackBar({
          status: true,
          text: 'ส่งคำขอเรียบร้อยแล้ว',
          severity: 'success',
        })
      );
    }
  };
  const handleRejectLayout = async (values: any) => {
    setLoadingReject(true);
    try {
      const res = await apiJob.rejectProductionOrder({
        productionOrderId: Number(id),
        reason: values.reason || 'แจ้งแก้ไขเลย์เอาท์',
      });
      if (res && !res.isError) {
        setOpenRejectLayout(false);
        getProductionOrder(Number(id));
        dispatch(
          setSnackBar({
            status: true,
            text: 'แจ้งแก้ไขเลย์เอาท์เรียบร้อยแล้ว',
            severity: 'success',
          })
        );
      }
    } catch (error) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    } finally {
      setLoadingReject(false);
    }
  };
  const currentStep = steps.find(
    (step: any) => step.id === Number(dataJob?.productionOrderStatus?.id)
  );
  const activeStep = steps.find((item: any) => item.step === step) || steps[0];
  // Base menu items for all steps
  const baseMenus = [
    {
      IconElement: () => (
        <Image src={'/icons/icon-detail.svg'} width={24} height={24} alt="" />
      ),
      title: 'ประวัติรายการ',
      disabled: false,
      onAction: () => {
        setOpenHistory(true);
      },
    },
    {
      IconElement: () => <InfoOutlinedIcon />,
      title: 'วิธีใช้งาน',
      disabled: false,
      onAction: () => {
        // Handle how to action
      },
    },
    {
      IconElement: () => (
        <Image src={'/icons/ic-cancel.svg'} width={24} height={24} alt="" />
      ),
      title: 'ยกเลิกรายการ',
      disabled: false,
      onAction: () => {
        setOpenStop(true);
      },
      cssProps: {
        color: '#D32F2F',
        '&:hover': {
          backgroundColor: '#FDE8EF',
        },
      },
    },
  ];

  // Menu for prePrint step only
  const prePrintMenus = [
    {
      IconElement: () => (
        <Image src={'/icons/icon-detail.svg'} width={24} height={24} alt="" />
      ),
      title: 'ประวัติรายการ',
      disabled: false,
      onAction: () => {
        setOpenHistory(true);
      },
    },
    {
      IconElement: () => (
        <Image src={'/icons/ic-draft.svg'} width={24} height={24} alt="" />
      ),
      title: 'แจ้งแก้ไขเลย์เอาท์',
      disabled: false,
      onAction: () => {
        setOpenRejectLayout(true);
      },
    },
    {
      IconElement: () => <InfoOutlinedIcon />,
      title: 'วิธีใช้งาน',
      disabled: false,
      onAction: () => {
        // Handle how to action
      },
    },
    {
      IconElement: () => (
        <Image src={'/icons/ic-cancel.svg'} width={24} height={24} alt="" />
      ),
      title: 'ยกเลิกรายการ',
      disabled: false,
      onAction: () => {
        setOpenStop(true);
      },
      cssProps: {
        color: '#D32F2F',
        '&:hover': {
          backgroundColor: '#FDE8EF',
        },
      },
    },
  ];
  const menuProduction = [
    ...baseMenus,
    {
      IconElement: () => (
        <Image
          src={'/icons/ic_pause_circle.svg'}
          width={24}
          height={24}
          alt=""
        />
      ),
      title: 'พักงานผลิต',
      disabled: false,
      onAction: () => {
        setOpenStop(true);
        setIsFormStop(true);
      },
    },
  ];
  const actionMenu = (stepName: string) => {
    switch (stepName) {
      case 'layout':
        return baseMenus;
      case 'prePrint':
        return prePrintMenus;
      case 'material':
        return baseMenus;
      case 'production':
        return menuProduction;
      default:
        return baseMenus;
    }
  };
  useEffect(() => {
    if (!step) {
      if (!currentStep) {
        if (step === 'undefined' || step === undefined) {
          router.push(`/job/${id}?step=layout`);
        }
      } else {
        router.push(`/job/${id}?step=${currentStep?.step}`);
      }
    }
  }, [currentStep, step]);
  useEffect(() => {
    if (dataJob?.productionOrderStatus.id) {
      router.push(`/job/${id}?step=${currentStep?.step}`);
    }
  }, [dataJob?.productionOrderStatus.id]);
  useEffect(() => {
    if (id) {
      getProductionOrderStageByProductionOrderId(Number(id));
      getProductionOrder(Number(id));
    }
  }, [id]);
  const statusName = (step?: number) => {
    switch (step) {
      case 1:
        return 'รอวางเลย์เอาท์';
      case 2:
        return 'ตรวจสอบก่อนพิมพ์';
      case 3:
        return 'รอส่งเบิก';
      default:
        return '';
    }
  };

  return (
    <>
      <JobNav
        title={dataJob?.jobNo}
        backUrl="/job"
        showBorderBottom
        actionMenuList={(() => {
          console.log('activeStep:', activeStep);
          console.log('activeStep.step:', activeStep.step);
          return actionMenu(activeStep.step);
        })()}
        productOrderId={Number(id)}
        approveProductionOrder={(id: number) => approveProductionOrder(id)}
        currentStepId={currentStep?.id}
      >
        {statusName(dataJob?.productionOrderStatus.id) && (
          <ActionGroupStyle>
            <JobStatusChip className="pending">
              <WatchLaterRoundedIcon />
              {statusName(dataJob?.productionOrderStatus.id)}
            </JobStatusChip>
          </ActionGroupStyle>
        )}
      </JobNav>
      <JobContentWrapperStyled>
        <JobStep
          currentStepId={currentStep?.id}
          isRemainingProduction={step === 'production'}
          dataDate={{
            scheduleDate: dataJob?.scheduleDate,
            scheduledStartDate: dataJob?.scheduledStartDate,
          }}
          groupLayData={dataJob?.groupLayData}
          dataJob={dataJob}
        />
        {activeStep.step === 'layout' && (
          <>
            <ProductsListDetail>
              <ProductDetailPanel groupLayData={dataJob?.groupLayData} />
            </ProductsListDetail>
            <PrintInformationDetail
              dataJob={dataJob}
              dataPrintInformationDetail={{
                dataRawMaterial: dataJob?.rawMaterial,
                dataArtwork: dataJob?.artwork,
                dataProductionOrderQuantity: dataJob?.productionOrderQuantity,
                dataItemSize: {
                  itemSize: dataJob?.itemSize,
                  subItemSize: dataJob?.subItemSize,
                },
                dataMachine: dataJob?.machine,
                dataPlateRawMaterial: dataJob?.plateRawMaterial,
                dataDieCutRawMaterial: dataJob?.dieCutRawMaterial,
              }}
              layoutBackUrl={dataJob?.layoutBackUrl}
              layoutFrontUrl={dataJob?.layoutFrontUrl}
              layDataDetailFront={dataJob?.layDataDetailFront}
              layDataDetailBack={dataJob?.layDataDetailBack}
            />
          </>
        )}
        {activeStep.step === 'prePrint' && (
          <>
            <PrintInformationDetail
              dataPrintInformationDetail={{
                dataRawMaterial: dataJob?.rawMaterial,
                dataArtwork: dataJob?.artwork,
                dataProductionOrderQuantity: dataJob?.productionOrderQuantity,
                dataItemSize: {
                  itemSize: dataJob?.itemSize,
                  subItemSize: dataJob?.subItemSize,
                },
                dataMachine: dataJob?.machine,
                dataPlateRawMaterial: dataJob?.plateRawMaterial,
                dataDieCutRawMaterial: dataJob?.dieCutRawMaterial,
              }}
              dataJob={dataJob}
              layoutBackUrl={dataJob?.layoutBackUrl}
              layoutFrontUrl={dataJob?.layoutFrontUrl}
              layDataDetailFront={dataJob?.layDataDetailFront}
              layDataDetailBack={dataJob?.layDataDetailBack}
            />
            <ProductsListDetail>
              <ProductDetailCard
                groupLayData={dataJob?.groupLayData}
                detail={dataJob?.layData}
              />
            </ProductsListDetail>
          </>
        )}
        {activeStep.step === 'material' && (
          <JobStatusPrepareMaterials
            dataJob={dataJob}
            getProductionOrder={(id: number) => getProductionOrder(id)}
            approveProductionOrder={(id: number) => approveProductionOrder(id)}
          />
        )}
        {activeStep.step === 'production' && (
          <>
            {/* <JobProductionPanel /> */}
            <div className={'p-[24px]'}>
              <ProductDetailPanel
                groupLayData={dataProductionOrderStage?.groupLayData}
              />
            </div>
            <LineTop />
            <JobStatusStyles>
              <JobStatusProduction
                dataProductionOrderStage={dataProductionOrderStage}
              />
            </JobStatusStyles>
          </>
        )}
        <ModalHistory
          open={openHistory}
          handleClose={() => setOpenHistory(false)}
        />
        <ModalStopProduction
          open={openStop}
          handleClose={() => {
            setOpenStop(false);
            setIsFormStop(false);
          }}
          isFormStop={isFormStop}
        />
        <AppModalConfirm
          open={openRejectLayout}
          onClickClose={() => setOpenRejectLayout(false)}
          confirmTitle="แจ้งแก้ไขเลย์เอาท์"
          confirmDescription="คุณต้องการแจ้งแก้ไขเลย์เอาท์หรือไม่?"
          loadingConfirm={loadingReject}
          onConfirm={handleRejectLayout}
          isReason={true}
          confirmText="ยืนยัน"
          cancelText="ยกเลิก"
          icon={
            <Image
              src={'/icons/icon-export-notes.svg'}
              width={40}
              height={40}
              alt=""
            />
          }
        />
      </JobContentWrapperStyled>
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Index;
