import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import TitlePage from '@/components/common/TitlePage';
import { useRouter } from 'next/router';
import JobRef from '@/components/job/JobRef';
import JobMadeToOrderDataTable from '@/components/job/JobMadeToOrderDataTable';
import JobPrintedSheetsDataTable from '@/components/job/JobPrintedSheetsDataTable';
import JobArtWorkDataTable from '@/components/job/JobArtWorkDataTable';
import JobDataSheet from '@/components/job/JobDataSheet';
import JobProductionPlan from '@/components/job/JobProductionPlan';
import JobDetailNote from '@/components/job/JobDetailNote';
import apiJob from '@/services/order/job';
import {
  TDataLayoutFile,
  TDataUpdateProductOrder,
  TProductionOrder,
} from '@/types/prepare-material';
import apiRawMaterial from '@/services/stock/raw-material';
import apiMachine from '@/services/stock/machine';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import styled from 'styled-components';

const JobSettingContentStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100dvh - 172px);
  overflow: auto;
  margin-top: 108px;
`;
const initialLayoutFile = {
  layoutFrontFile: null,
  layoutBackFile: null,
};
const validationSchema = yup.object({
  rawMaterialId: yup.string().required('กรุณาเลือกวัสดุพิมพ์'),
  itemSizeId: yup.string().required('กรุณาเลือกขนาดใบเต็ม'),
  subItemSizeId: yup.string().required('กรุณาเลือกขนาดใบพิมพ์'),
  dieCutRawMaterialId: yup.string().required('กรุณาเลือกบล๊อกไดคัท'),
  plateRawMaterialId: yup.string().required('กรุณาเลือกเพลท'),
  machineId: yup.string().required('กรุณาเลือกเครื่องพิมพ์'),
  machineModelId: yup.string().required('กรุณาเลือกโมเดล'),
});
const Index = () => {
  const dispatch = useAppDispatch();
  const [layoutFile, setLayoutFile] =
    useState<TDataLayoutFile>(initialLayoutFile);
  const [dataItemSize, setDataItemSize] = useState<any>({});
  const [dieCutList, setDieCutList] = useState<any>([]);
  const [plateList, setPlateList] = useState<any>([]);
  const [machineList, setMachineList] = useState<any>([]);
  const [filters, setFilters] = useState<any>({
    limit: 1000,
    subMaterialDetailId: 0,
  });
  const [rawMaterialList, setRawMaterialList] = useState<any>([]);
  const [dataJob, setDataJob] = useState<TProductionOrder>();
  const [loading, setLoading] = useState<boolean>(false);
  const router = useRouter();
  const { id } = router.query;
  const formik = useFormik({
    initialValues: {
      productionOrderId: Number(id),
      scheduledStartDate: '',
      scheduleDate: '',
      priorityLevel: '',
      rawMaterialId: '',
      itemSizeId: '',
      subItemSizeId: '',
      dieCutRawMaterialId: '',
      plateRawMaterialId: '',
      machineId: '',
      machineModelId: '',
      stage: [],
    },
    validationSchema,
    onSubmit: (values: any) => {
      const newDataStage = values.stage.map((item: any) => {
        return {
          id: item.id,
          sortIndex: item.sortIndex,
          isActive: item.isActive,
          productionTimeId: item.productionTime.id,
          productionPlanStageId: item.productionPlanStage.id,
        };
      });
      const dataPayload = {
        ...values,
        stage: newDataStage,
      };
      if (layoutFile.layoutFrontFile) {
        const formDataFront = new FormData();
        formDataFront.append('file', layoutFile.layoutFrontFile.file);
        formDataFront.append(
          'typeLayOut',
          String(layoutFile.layoutFrontFile.typeLayOut)
        );
        formDataFront.append(
          'productionOrderId',
          String(layoutFile.layoutFrontFile.productionOrderId)
        );
        saveUploadLayoutFile(formDataFront);
      }
      if (layoutFile.layoutBackFile) {
        const formDataBack = new FormData();
        formDataBack.append('file', layoutFile.layoutBackFile.file);
        formDataBack.append(
          'typeLayOut',
          String(layoutFile.layoutBackFile.typeLayOut)
        );
        formDataBack.append(
          'productionOrderId',
          String(layoutFile.layoutBackFile.productionOrderId)
        );
        saveUploadLayoutFile(formDataBack);
      }
      saveProductionOrder(dataPayload);
    },
  });
  const saveProductionOrder = async (data: TDataUpdateProductOrder) => {
    setLoading(true);
    const res = await apiJob.saveProductionOrder(data);
    if (res && !res.isError) {
      await dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกข้อมูลการผลิตสำเร็จ',
          severity: 'success',
        })
      );
      await router.push(`/job/${id}`);
    }
    setLoading(false);
  };
  const saveUploadLayoutFile = async (data: any) => {
    const res = await apiJob.saveUploadLayoutFile(data);
    if (res && !res.isError) {
      //
    }
  };
  const subMaterialDetailId = dataJob?.rawMaterial?.subMaterialDetail.id;
  const getProductionOrder = async (id: number) => {
    const res = await apiJob.getProductionOrder(id);
    if (res.status === true) {
      setDataJob(res.data);
    }
  };
  const getListRawMatCheckMaster = async (id: number) => {
    const res = await apiRawMaterial.getListRawMatCheckMaster(id);
    if (res && !res.isError) {
      if (id === 5) {
        setDieCutList(res.data);
      } else {
        setPlateList(res.data);
      }
    }
  };
  const getRawMaterialList = async (params: any) => {
    const res = await apiRawMaterial.getOptions(params);
    if (res && !res.isError) {
      setRawMaterialList(res.data);
    }
  };
  const getMachineList = async () => {
    const res = await apiMachine.getMachineList();
    if (res && !res.isError) {
      setMachineList(res.data);
    }
  };
  const findDataMaterial = (data: any, value: number) => {
    const dataMaterial = data?.find((item: any) => {
      return item.id === value;
    });
    const itemSizeId = dataMaterial?.itemSize.id;
    const subItemSizeId = dataMaterial?.itemSize.subItemSizeDto[0]?.id;
    formik.setFieldValue('itemSizeId', itemSizeId);
    formik.setFieldValue('subItemSizeId', subItemSizeId);
    setDataItemSize(dataMaterial);
  };

  useEffect(() => {
    getListRawMatCheckMaster(5);
    getListRawMatCheckMaster(6);
    getMachineList();
  }, []);
  useEffect(() => {
    if (subMaterialDetailId) {
      setFilters({
        ...filters,
        subMaterialDetailId: subMaterialDetailId,
      });
    }
  }, [subMaterialDetailId]);
  useEffect(() => {
    getRawMaterialList(filters);
  }, [filters]);
  useEffect(() => {
    if (id) {
      getProductionOrder(Number(id));
    }
  }, [id]);
  useEffect(() => {
    if (dataJob?.rawMaterialId) {
      findDataMaterial(rawMaterialList, Number(dataJob?.rawMaterialId));
    }
  }, [rawMaterialList, dataJob?.rawMaterialId]);
  useEffect(() => {
    if (formik.values?.rawMaterialId) {
      findDataMaterial(rawMaterialList, Number(formik.values?.rawMaterialId));
    }
  }, [rawMaterialList, formik.values?.rawMaterialId]);

  // Map machineId and machineModelId from dataJob to form
  useEffect(() => {
    if (dataJob?.machineId) {
      formik.setFieldValue('machineId', dataJob.machineId);
    }
  }, [dataJob?.machineId]);

  useEffect(() => {
    if (dataJob?.machineModel?.id) {
      formik.setFieldValue('machineModelId', dataJob.machineModel.id);
    }
  }, [dataJob?.machineModel?.id]);
  // console.log('dataJob', dataJob);
  return (
    <>
      <TitlePage title={'ตั้งค่าการผลิต'} backUrl={`/job/${id}`} />
      <div
        className={`flex flex-col gap-2 bg-white relative ${
          dataJob?.productionOrderStatus.id !== 1 ? 'pointer-events-none' : ''
        }`}
      >
        <JobSettingContentStyled>
          <JobMadeToOrderDataTable
            groupLayData={dataJob?.groupLayData}
            fetchData={() => getProductionOrder(Number(id))}
            layDataOrderId={dataJob?.layDataOrderId || null}
          />
          <JobPrintedSheetsDataTable
            productionOrderQuantity={dataJob?.productionOrderQuantity}
            fetchData={() => getProductionOrder(Number(id))}
          />
          <JobArtWorkDataTable
            artwork={dataJob?.artwork}
            fetchData={() => getProductionOrder(Number(id))}
          />
          <form onSubmit={formik.handleSubmit}>
            <JobRef
              jobRefLabel={dataJob?.jobNo || ''}
              priorityLevel={dataJob?.priorityLevel}
              scheduleDate={dataJob?.scheduleDate}
              scheduledStartDate={dataJob?.scheduledStartDate}
              formik={formik}
            />
            <JobDataSheet
              allData={{
                dataRawMaterial: rawMaterialList,
                dataDieCutList: dieCutList,
                dataPlateList: plateList,
                dataMachineList: machineList,
                dataItemSize: [{ ...dataItemSize?.itemSize }],
                dataSubItemSize: dataItemSize?.itemSize?.subItemSizeDto,
              }}
              dataJob={dataJob}
              layoutBackUrl={dataJob?.layoutBackUrl}
              layoutFrontUrl={dataJob?.layoutFrontUrl}
              layDataDetailFront={dataJob?.layDataDetailFront}
              layDataDetailBack={dataJob?.layDataDetailBack}
              formik={formik}
              productionOrderId={Number(id)}
              layoutFile={layoutFile}
              setLayoutFile={(data: TDataLayoutFile) => setLayoutFile(data)}
              isSetting={true}
            />
            <JobProductionPlan dataStage={dataJob?.stage} formik={formik} />
            <JobDetailNote loading={loading} />
          </form>
        </JobSettingContentStyled>
      </div>
    </>
  );
};

Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Index;
