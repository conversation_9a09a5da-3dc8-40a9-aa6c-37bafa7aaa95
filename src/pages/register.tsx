'use client';

import {
  <PERSON><PERSON>,
  CircularProgress,
  InputAdornment,
  TextField,
} from '@mui/material';
import Link from 'next/link';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import React, { ReactElement, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import AuthLayout from '@/layouts/AuthLayout';
import styled, { css } from 'styled-components';
import CheckIcon from '@mui/icons-material/Check';
import { LoadingFadein } from '@/styles/share.styled';
import { useRouter } from 'next/router';
import { LoginWithPasswordType } from '@/types/auth';
import apiUser from '@/services/core/user';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const RegisterFormStyle = styled.div<{ $isCheck: boolean }>`
  position: relative;
  height: calc(100vh - 124px);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  animation: ${LoadingFadein} 0.3s ease-in;
  margin-top: -100px;
  @media screen and (max-width: 480px) {
    height: calc(100vh - 104px);
    margin-top: -152px;
  }
  .topic-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    @media screen and (max-width: 575px) {
      margin-bottom: 0;
    }
  }
  .login-account {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 800px;
    width: 480px;
    @media screen and (max-width: 575px) {
      width: 85%;
    }
  }
  .third-party {
    display: flex;
    column-gap: 24px;
    @media screen and (max-width: 575px) {
      flex-direction: column;
      row-gap: 14px;
    }
    button {
      display: flex;
      align-items: center;
      justify-content: start;
      column-gap: 12px;
      position: relative;
      @media screen and (max-width: 575px) {
        justify-content: center;
      }
      span {
        margin: 0;
        @media screen and (max-width: 575px) {
          position: absolute;
          left: 12px;
        }
      }
      .text {
        white-space: nowrap;
      }
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  h1 {
    margin-top: 0;
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
  }
  .check-policy {
    label {
      display: flex;
      align-items: start;
      column-gap: 8px;
      position: relative;
      cursor: pointer;
      user-select: none;
      font-size: 16px;
      @media screen and (max-width: 480px) {
        font-size: 14px;
      }
      .icon {
        opacity: 0;
        justify-content: center;
        align-items: center;
        position: absolute;
        color: white;
        height: 24px;
        aspect-ratio: 1/1;
        display: flex;
        transition: 0.15s;
        @media screen and (max-width: 480px) {
          height: 20px;
        }
        ${({ $isCheck }) =>
          $isCheck &&
          css`
            opacity: 1 !important;
          `}
        svg {
          font-size: 18px;
        }
      }
      input {
        margin: 0;
        height: 24px;
        aspect-ratio: 1/1;
        border-radius: 6px;
        appearance: none;
        transition: 0.15s;
        border: 2px solid #dbe2e5;
        @media screen and (max-width: 480px) {
          height: 20px;
        }
        ${({ $isCheck }) =>
          $isCheck &&
          css`
            background-color: #263238 !important;
            border: none;
          `}
      }
    }
  }
`;

const validationSchema = yup.object({
  email: yup.string().required('กรุณากรอกอีเมล'),
  password: yup
    .string()
    .min(8, 'กรอกรหัสผ่านอย่างน้อย 8 ตัวอักษร')
    .required('กรุณากรอกรหัสผ่าน'),
  confirmPassword: yup
    .string()
    .required('กรุณากรอกยืนยันรหัสผ่าน')
    .oneOf([yup.ref('password')], 'รหัสผ่านไม่ตรงกัน'),
});
const RegisterPage = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      registerWithEmail(values);
    },
  });
  const registerWithEmail = async (values: LoginWithPasswordType) => {
    setLoading(true);
    const res = await apiUser.register(values);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'สร้างบัญชีสำเร็จ',
          severity: 'success',
        })
      );
      await router.push('/');
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoading(false);
  };
  const handleChange = (e: boolean) => {
    const isCheck = e;
    setIsCheck(isCheck);
  };
  return (
    <>
      <RegisterFormStyle $isCheck={isCheck}>
        <form onSubmit={formik.handleSubmit}>
          <div className="topic-group">
            <span className="text-[28px] font-[600]">สร้างบัญชี</span>
            <span className="text-[12px]">Printing Factory Management</span>
            <span className="text-[12px]">Version 1.0.0</span>
          </div>
          <p>อีเมล</p>
          <TextField
            type="email"
            name="email"
            placeholder="Email"
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
          />
          <p>รหัสผ่าน</p>
          <TextField
            type={showPassword ? 'text' : 'password'}
            name="password"
            placeholder="Password"
            value={formik.values.password}
            onChange={formik.handleChange}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  className="cursor-pointer"
                  position="end"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </InputAdornment>
              ),
            }}
            error={formik.touched.password && Boolean(formik.errors.password)}
            helperText={formik.touched.password && formik.errors.password}
          />
          <p>ยืนยันรหัสผ่าน</p>
          <TextField
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm Password"
            name="confirmPassword"
            value={formik.values.confirmPassword}
            onChange={formik.handleChange}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  className="cursor-pointer"
                  position="end"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                </InputAdornment>
              ),
            }}
            error={
              formik.touched.confirmPassword &&
              Boolean(formik.errors.confirmPassword)
            }
            helperText={
              formik.touched.confirmPassword && formik.errors.confirmPassword
            }
          />
          <div className="check-policy py-10">
            <label>
              <div className="icon">
                <CheckIcon />
              </div>
              <input
                type="checkbox"
                onChange={(event) => handleChange(event.target.checked)}
              />
              <div className="ml-1">
                การสร้างบัญชีหมายความว่าคุณยอมรับ{' '}
                <Link href="/" className="underline">
                  เงื่อนไขการใช้บริการ
                </Link>{' '}
                และ{' '}
                <Link href="/" className="underline">
                  นโยบายความเป็นส่วนตัว
                </Link>
              </div>
            </label>
          </div>
          <Button
            type="submit"
            disabled={!isCheck}
            variant="contained"
            color="dark"
            fullWidth
            sx={{ fontSize: '16px' }}
          >
            {loading ? <CircularProgress size={25} /> : 'สร้างบัญชี'}
          </Button>
        </form>
        <div className="login-account gap-1">
          มีบัญชีผู้ใช้งานแล้ว?
          <b onClick={() => router.push('/')}>
            <div className="cursor-pointer">เข้าสู่ระบบ</div>
          </b>
        </div>
      </RegisterFormStyle>
    </>
  );
};

RegisterPage.getLayout = function getLayout(page: ReactElement) {
  return <AuthLayout>{page}</AuthLayout>;
};

export default RegisterPage;
