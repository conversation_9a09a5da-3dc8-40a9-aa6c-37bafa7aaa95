import Preview from '@/components/Preview';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

type MaterialType = 'white' | 'kraft' | 'gray';
type ModelType = '3d' | 'dieline';

export default function PreviewPacdora() {
  const router = useRouter();
  const { modelId, modelType, width, height, length, inside, outside } =
    router.query;
  const [widthArea, setWidthArea] = useState<number>(0);
  const [heightArea, setHeightArea] = useState<number>(0);

  // Send iframe loaded message to parent
  useEffect(() => {
    window.parent.postMessage(
      {
        type: 'IFRAME_LOADED',
        data: {
          status: 'ready',
          params: {
            modelId,
            modelType,
            width,
            height,
            length,
            inside,
            outside,
          },
        },
        timestamp: Date.now(),
      },
      '*'
    );

    console.log('Iframe loaded and ready');
  }, [modelId, modelType, width, height, length, inside, outside]);

  // Function to send messages to parent
  const sendToParent = (type: string, data: any) => {
    const message = {
      type,
      data,
      timestamp: Date.now(),
    };

    console.log(`Attempting to send ${type} to parent:`, message);

    try {
      window.parent.postMessage(message, '*');
      console.log(`Successfully sent ${type} to parent`);
    } catch (error) {
      console.error(`Failed to send ${type} to parent:`, error);
    }
  };

  useEffect(() => {
    receiveData();
    console.log('AREA', widthArea, heightArea);
  }, [widthArea, heightArea]);

  const receiveData = () => {
    const data = { widthArea, heightArea };
    sendToParent('PREVIEW_DATA', data);
  };

  return (
    <div>
      <Container>
        <Preview
          modelId={Number(modelId) || 100010}
          modelType={(modelType as ModelType) || '3d'}
          width={Number(width) || 50}
          height={Number(height) || 100}
          length={Number(length) || 20}
          material={{
            inside: (inside as MaterialType) || 'white',
            outside: (outside as MaterialType) || 'kraft',
          }}
          onInfo={(info: any) => {
            const { widthOfArea, heightOfArea } = info;
            console.log('INFO', info);
            setWidthArea(widthOfArea);
            setHeightArea(heightOfArea);
          }}
        />
      </Container>
    </div>
  );
}

const Container = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: calc(100vh - 80px);
  @media only screen and (max-width: 430px) {
    width: 100%;
    display: none;
  }
  .name-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 0;
    z-index: 2;
    .product {
      font-size: 18px;
      font-weight: 700;
    }
    .model {
      font-size: 12px;
      font-weight: 500;
    }
  }
  .display-mode {
    position: absolute;
    top: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 2;
  }
`;
