import React, { ReactElement, useEffect, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import BlankLayout from '@/layouts/BlankLayout';
import { Badge, Button } from '@mui/material';
import { menuNavigationDashboard } from '@/utils/menuNavigationDashboard';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import { getUserProfile, userSelector } from '@/store/features/user';
import { getCookie, setCookie } from 'cookies-next';
import { AsideChangeCompany, LoadingFadein } from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import apiCompany from '@/services/core/company';
import { isNull } from 'lodash';
import SwitchCompanyCard from '@/components/dashboard/SwitchCompanyCard';
import {
  setPlaySwitchAnimation,
  showAsideSelector,
} from '@/store/features/layout';
import UserMenuDashboard from '@/components/dashboard/UserMenuDashboard';
import { menuDashboard } from '@/utils/menuDashboard';
import { GetServerSideProps } from 'next';
import getConfig from 'next/config';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';

const { serverRuntimeConfig } = getConfig();

const DashboardStyled = styled.div<{
  $isEmptyCover: boolean;
  $isOpenSwitch: boolean;
}>`
  display: flex;
  flex-direction: column;
  height: 100dvh;
  width: 100%;
  align-items: center;
  position: relative;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  .logo-sm {
    width: 100%;
    height: 48px;
    display: none;
    @media screen and (max-width: 820px) {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .content-dashboard {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;
    .cover-wrap {
      width: 100%;
      background-color: #dbe2e5;
      ${({ $isEmptyCover }) =>
        $isEmptyCover
          ? css`
              background-color: #dbe2e5;
            `
          : css`
              background-color: transparent;
            `}
      height: 30vw;
      max-height: 200px;
      min-height: 100px;
      position: relative;
      .cover-image {
        width: 100%;
        height: 100%;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .profile-company {
        position: absolute;
        left: 50%;
        bottom: -117px;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 12px;
        animation: ${LoadingFadein} 0.3s ease-in;
        .image-company {
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 16px;
          border: 4px solid white;
          overflow: hidden;
          box-shadow: 0px 0px 8px 0px #26323840;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name-company {
          font-size: 22px;
          font-weight: 600;
          max-width: 70vw;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .switch {
          color: #cfd8dc;
          display: flex;
          align-items: center;
          margin-top: -4px;

          button {
            height: 24px !important;
            width: 74px !important;
            border: 1px solid #dbe2e5 !important;
            box-shadow: 0px 0px 0px 2px #f5f7f8 !important;
            .group {
              column-gap: 6px !important;
              .icon {
                transition: 0.2s ease-in;
                ${({ $isOpenSwitch }) =>
                  $isOpenSwitch
                    ? css`
                        transform: rotate(180deg);
                      `
                    : css`
                        transform: rotate(0deg);
                      `}
              }
              .text {
                font-size: 12px !important;
              }
            }
          }
        }
        &.animate {
          animation: ${AsideChangeCompany} ease-in 0.8s;
        }
      }
    }
    .menu-group {
      display: grid;
      grid-gap: 40px;
      position: relative;
      grid-template-columns: repeat(auto-fill, minmax(152px, 1fr));
      width: 1360px;
      margin: 164px 0 40px 0;
      max-width: 100%;
      padding: 0 40px;
      @media screen and (max-width: 820px) {
        margin: 164px 0 84px 0;
        grid-gap: 24px;
        padding: 0 24px;
      }
      @media screen and (max-width: 580px) {
        grid-template-columns: repeat(auto-fill, minmax(128px, 1fr));
      }
      @media screen and (max-width: 480px) {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      }
      .card {
        aspect-ratio: 1;
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        box-shadow: 0px 0px 0px 4px #f5f7f8;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        cursor: pointer;
        transition: 0.15s ease-out;
        //background-color: white;
        &:hover {
          filter: brightness(0.9);
        }
        @media screen and (max-width: 580px) {
          img {
            width: 32px;
            height: 32px;
          }
        }
        .name {
          position: absolute;
          bottom: 16px;
          font-size: 14px;
          left: 50%;
          transform: translateX(-50%);
          white-space: nowrap;
          text-align: center;
          @media screen and (max-width: 572px) {
            font-size: 12px;
            bottom: 12px;
          }
          @media screen and (max-width: 500px) {
            font-size: 10px;
          }
          @media screen and (max-width: 420px) {
            bottom: 8px;
          }
        }
      }
    }
    //.menu-group .card:hover {
    //  transform: scale(1.08);
    //}
    //.menu-group:hover > .card:not(:hover) {
    //  filter: brightness(0.8);
    //}
  }
`;
const NavigationBarDashboardStyled = styled.div`
  height: 88px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 24px;
  background-color: white;
  @media screen and (max-width: 820px) {
    border-top: 1px solid #dbe2e5;
    box-shadow: 0px 0px 8px 0px #2632381f;
    position: fixed;
    z-index: 9;
    bottom: 0;
    height: 64px;
  }

  .logo {
    display: flex;
    align-items: center;
    @media screen and (max-width: 820px) {
      display: none;
    }
  }
  .menu-wrap {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    column-gap: 4px;
    @media screen and (max-width: 820px) {
      transform: none;
      left: initial;
      position: relative;
      width: 100%;
      justify-content: space-between;
    }

    .menu {
      display: flex;
      align-items: center;
      justify-content: center;
      @media screen and (max-width: 820px) {
        flex: 1 1 0% !important;
      }
      button {
        background-color: transparent !important;
        width: 80px;
        height: 40px;
        min-height: 40px;
        max-height: 40px;
        border-radius: 20px;
        transition: 0.3s ease-out;
        box-shadow: none !important;
        min-width: 0px;
        @media screen and (max-width: 820px) {
          flex: 1 1 0% !important;
          width: auto;
        }
        &:hover {
          background-color: #dbe2e5 !important;
        }
      }
      &.active {
        button {
          background-color: #263238 !important;
          path {
            fill: white !important;
          }
        }
      }
    }
  }

  .profile {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #dbe2e5;
    cursor: pointer;
    transition: 0.15s ease-out;
    &:hover {
      filter: brightness(0.8);
    }
    @media screen and (max-width: 820px) {
      display: none;
    }
  }
`;
const Dashboard = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { pathname } = router;
  // const token = getCookie('access_token');
  const { user } = useAppSelector(userSelector);
  const dispatch = useAppDispatch();
  const [companyList, setCompanyList] = useState<any>(null);
  const [invite, setInvite] = useState<boolean>(false);
  const [openSwitch, setOpenSwitch] = useState<boolean>(false);
  const [openProfileMenu, setOpenProfileMenu] = useState<boolean>(false);
  const wrapperRef = useRef(null);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const { playSwitchAnimation } = useAppSelector(showAsideSelector);
  useEffect(() => {
    dispatch(getUserProfile());
  }, []);
  const getCompanyList = async (data?: string) => {
    if (data === 'switch') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        await dispatch(getUserProfile());
        setCompanyList(res.data);
        await dispatch(setPlaySwitchAnimation(true));
        setTimeout(() => {
          dispatch(setPlaySwitchAnimation(false));
        }, 800);
      }
    } else if (data === 'reload') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        await dispatch(getUserProfile());
        setCompanyList(res.data);
        await dispatch(setPlaySwitchAnimation(true));
        setTimeout(() => {
          dispatch(setPlaySwitchAnimation(false));
        }, 800);
      }
    } else if (data === 'fetch') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        setCompanyList(res.data);
      }
    } else if (!openSwitch) {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        setCompanyList(res.data);
        setOpenSwitch(true);
      } else {
        setCompanyList(null);
      }
    } else {
      setOpenSwitch(false);
    }
  };
  useEffect(() => {
    if (!isNull(companyList)) {
      const checkInvite = companyList.some((item: any) => !item.inviteVerified);
      setInvite(checkInvite);
    }
  }, [companyList]);
  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = [
            'switch',
            'MuiButtonBase-root',
            'group',
            'icon',
            'text',
            'MuiTouchRipple-root',
            'img',
          ];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (!checkValue) {
            setOpenSwitch(false);
          }
        }
      }

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  const handleClickUserProfile = () => {
    setOpenProfileMenu(!openProfileMenu);
  };

  return (
    <>
      <DashboardStyled $isEmptyCover={true} $isOpenSwitch={openSwitch}>
        <div className="logo-sm">
          <Image
            src={'/icons/icon-hon-black.svg'}
            width={72}
            height={24}
            alt=""
            draggable={false}
          />
        </div>
        <NavigationBarDashboardStyled>
          <div className="logo">
            <Image
              src={'/icons/icon-hon-black.svg'}
              width={72}
              height={24}
              alt=""
              draggable={false}
            />
          </div>
          <div className="menu-wrap">
            {menuNavigationDashboard.map((item: any, index: number) => {
              return (
                <div
                  className={`menu ${pathname === item.url ? 'active' : ''}`}
                  key={index}
                >
                  <Button type="button" variant="contained">
                    {item.icon}
                  </Button>
                </div>
              );
            })}
          </div>
          <div className="profile" onClick={handleClickUserProfile}>
            <Image
              src={
                !isNull(user.imageUrl)
                  ? user.imageUrl
                  : '/images/empty-contact.svg'
              }
              width={40}
              height={40}
              alt=""
              draggable={false}
            />
          </div>
        </NavigationBarDashboardStyled>
        <div className="content-dashboard">
          <div className="cover-wrap">
            <div className="cover-image">
              {/* <Image */}
              {/*    src="" */}
              {/*    width={160} */}
              {/*    height={160} */}
              {/*    alt="" */}
              {/*    draggable={false} */}
              {/* /> */}
            </div>
            {user.company.id !== 0 && (
              <div
                className={`profile-company ${
                  playSwitchAnimation ? 'animate' : ''
                }`}
              >
                <div className="image-company">
                  <Image
                    src={
                      user.company.logo !== null
                        ? user.company.logo
                        : '/images/product/empty-product.svg'
                    }
                    width={160}
                    height={160}
                    alt=""
                    draggable={false}
                  />
                </div>
                <div className="name-company">{user.company.name}</div>
                <div
                  className="switch"
                  onClick={() => {
                    getCompanyList();
                  }}
                >
                  <Badge
                    color="primary"
                    variant="dot"
                    invisible={!invite}
                    sx={{
                      '>span': {
                        backgroundColor: '#30D5C7',
                        height: '12px',
                        width: '12px',
                        borderRadius: '50%',
                      },
                    }}
                  >
                    <ActionButton
                      variant="outlined"
                      color="blueGrey"
                      icon={
                        <img
                          className="img"
                          src="/icons/aside/icon-switch.png"
                          alt=""
                        />
                      }
                      text="Switch"
                    />
                  </Badge>
                </div>
              </div>
            )}
          </div>
          <div
            ref={wrapperRef}
            style={{
              zIndex: 9,
            }}
          >
            <SwitchCompanyCard
              open={openSwitch}
              data={companyList}
              currentCompany={user.company}
              handleFetchUserCompany={(data: string) => {
                getCompanyList(data);
              }}
              handleClose={() => {
                setOpenSwitch(false);
              }}
            />
          </div>
          <div className="menu-group">
            {menuDashboard.map((item: any, index: number) => {
              return (
                <>
                  <div
                    className="card"
                    key={index}
                    onClick={() => {
                      router.push(`${item.path}`);
                    }}
                  >
                    <Image src={item.iconPath} width={48} height={48} alt="" />
                    <div className="name">{item.name}</div>
                  </div>
                </>
              );
            })}
          </div>
          <UserMenuDashboard
            open={openProfileMenu}
            handleOpen={(val: boolean) => {
              setOpenProfileMenu(val);
            }}
          />
        </div>
      </DashboardStyled>
    </>
  );
};

Dashboard.getLayout = function getLayout(page: ReactElement) {
  return <BlankLayout>{page}</BlankLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const client = serverRuntimeConfig.AUTH_CLIENT;
  const secret = serverRuntimeConfig.AUTH_SECRET;
  const headers = new Headers();
  headers.append('Content-type', 'application/x-www-form-urlencoded');
  headers.append(
    'Authorization',
    `Basic ${Buffer.from(`${client}:${secret}`).toString('base64')}`
  );
  const refreshToken = getCookie('refresh_token', { req, res });

  const authUrl = serverRuntimeConfig.AUTH_ENDPOINT;
  const url = `${authUrl}/oauth2/token`;

  const response = await fetch(url, {
    method: 'POST',
    mode: 'cors',
    headers,
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: `${refreshToken}`,
    }),
  });

  const data = await response.json();

  if (!data.error) {
    setCookie('access_token', data.access_token, {
      req,
      res,
      maxAge: 60 * 60 * 24,
    });
    setCookie('refresh_token', data.refresh_token, {
      req,
      res,
      maxAge: 60 * 60 * 24,
    });
    const response = await apiUser.getProfile(data.access_token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
    // redirect: {
    //   destination: '/',
    //   permanent: true,
    // },
  };
};

export default Dashboard;
