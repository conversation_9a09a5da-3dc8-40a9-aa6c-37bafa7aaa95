import React, { useEffect } from 'react';
import type { ReactElement, ReactNode } from 'react';
import type { NextPage } from 'next';
import type { AppProps, AppContext, AppInitialProps } from 'next/app';
import { ThemeProvider } from '@mui/material/styles';
import '@fontsource/prompt/400.css';
import '@fontsource/prompt/500.css';
import '@fontsource/prompt/600.css';

import { theme } from '@/styles/theme';
import '@/styles/globals.scss';
import { wrapper } from '@/store';
import { Provider } from 'react-redux';
import { createGlobalStyle } from 'styled-components';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css';
import { LoadingFadein } from '@/styles/share.styled';
import App from 'next/app';
import { useRouter } from 'next/router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getAppTitle } from '@/utils/appTitle';
import { isEmpty } from 'lodash';
import Head from 'next/head';

export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout | any;
};

const GlobalStyle = createGlobalStyle`
    .text-error {
        font-size: 0.75rem;
        color: #E91E63;
        margin-left: 16px;
        margin-top: 5px;
    }
 
    /* Start MUI Badge zone */
    .MuiBadge-badge {
        background-color: #263238;
    }
    /* End MUI Badge zone */

    /* Start Snackbar zone */
    .MuiSnackbar-root {
        top: 4px !important;
        right: 8px !important;
        left: auto !important;
        
        @media screen and (max-width: 820px) {
            top: 14px !important;
            .MuiPaper-root {
                padding: 8px 16px !important;
            }
        }

        @media screen and (max-width: 440px) {
            left: 8px !important;
        }
    }
    /* End Snackbar zone */

    /* Start MUI Dialog zone */
    .MuiDialog-root {
      z-index: 106 !important;
    }
    .MuiDialog-paper {
        border-radius: 16px !important;
        max-width: calc(100% - 48px) !important;
        margin: 0 !important;
        ::-webkit-scrollbar {
            width: 0;
            height: 0;
        }
    }
    /* End MUI Dialog zone */

    .MuiPaper-root {
        border-radius: 8px;
        box-shadow: 0px 0px 16px 0px #26323820;
    }

    .MuiToolbar-root {
        position: relative;
        display: flex;
        width: 100%;
        justify-content: end;
        
        .title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            color: #263238;
            font-size: 22px;
            margin: 0;
            font-weight: 600;

            @media screen and (max-width: 650px) {
                font-size: 1.2em;
            }
        }

        .MuiIconButton-root {
            transition: .3s ease-in-out;

            &:hover {
                rotate: 90deg;
            }
        }
    }
    
    .MuiInputBase-root {
       //background: white !important;
        &.Mui-error {
            box-shadow: 0 0 0 1px #E91E63 !important;
        }
    }
    .select-job {
        &.Mui-error {
            box-shadow: unset !important;
        }
    }
    .drop-menu {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        height: 100%;
      min-height: 40px;
    }

    .MuiAutocomplete-popper {
        ::-webkit-scrollbar {
            width: 0;
            height: 0;
        }
    }

    .MuiAutocomplete-paper {
        padding: 0 8px;
    }

    .MuiAutocomplete-option {
        border-radius: 8px;
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .swal2-container {
        z-index: 9999;
    }

    /* Start sweetalert2 zone */
    .swal2-popup {
        border-radius: 16px;
    }

    .swal2-popup button {
        border-radius: 8px !important;
        font-family: Prompt, sans-serif;
    }

    .swal-close {
        position: absolute;
        box-shadow: none !important;
        transition: .3s;
        transform-origin: center;
    }

    .swal-close:hover {
        filter: brightness(0.2);
        transform: rotate(45deg);
    }

    .swal2-confirm {
        background: #222 !important;
    }

    .swal2-title {
        font-size: 1.3em;
    }

    .swal2-html-container {
        font-size: 1em !important;
    }

    .swal2-icon {
        border: none !important;
        background-color: #F5F7F8;
        margin: 32px auto 16px;
    }

    .swal2-custom-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
    }

    .swal2-custom-icon img {
        width: 100%;
        height: 100%;
    }

    .swal2-custom-icon.delete {
        background-image: url('/icons/icon-modal-delete.svg');
        background-size: cover;
        background-position: center;
    }

    .swal2-custom-icon.success {
        background-image: url('/icons/icon-modal-success.svg');
        background-size: cover;
        background-position: center;
    }
    /* End sweetalert2 zone */

    .kebab-wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        transition: .3s ease-out;
        cursor: pointer;

        &:hover {
            background-color: #F5F7F8;
        }

        .kebab {
            height: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: column;

            &:before {
                content: '';
                width: 4px;
                height: 4px;
                background-color: #CFD8DC;
                border-radius: 50%;
            }

            &:after {
                content: '';
                width: 4px;
                height: 4px;
                background-color: #CFD8DC;
                border-radius: 50%;
            }

            .dot {
                width: 4px;
                height: 4px;
                background-color: #CFD8DC;
                border-radius: 50%;
            }
        }
    }

    .action-dot {
        width: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        rotate: 90deg;

        &:before {
            content: '';
            width: 4px;
            height: 4px;
            background-color: #838585;
            border-radius: 50%;
        }

        &:after {
            content: '';
            width: 4px;
            height: 4px;
            background-color: #838585;
            border-radius: 50%;
        }

        .dot {
            width: 4px;
            height: 4px;
            background-color: #838585;
            border-radius: 50%;
        }
    }

    .MuiPickersToolbar-root {
        span {
            background: transparent !important;
        }

        button {
            background: transparent !important;
        }

        .Mui-selected {
            color: #30D5C7 !important;
        }
    }

    .MuiDateCalendar-root {
        height: 288px;
        animation: ${LoadingFadein} 0.3s ease-in;
    }

    .MuiTimeClock-root {
        height: 288px;
        justify-content: end;
        animation: ${LoadingFadein} 0.3s ease-in;
    }

    .MuiDialogActions-root {
        button {
            height: 40px !important;
        }
    }

    .MuiTimeClock-arrowSwitcher {
        top: 12px;
    }

    .Mui-disabled {
        background: #f1f1f1;
        overflow: hidden;
    }

    .MuiButtonBase-root {
        text-transform: capitalize;
    }

    .MuiTabs-flexContainer {
        animation: ${LoadingFadein} 0.6s ease-in;
    }

    .MuiFormControlLabel-root {
        margin-left: -10px !important;
    }

    .MuiTab-root {
        min-width: auto !important;
    }

    b {
        font-weight: 600;
    }

    canvas {
        touch-action: auto !important;
    }

    input[type='number'] {
        -webkit-appearance: textfield;
        -moz-appearance: textfield;
        appearance: textfield;
    }

    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none;
    }

    .MuiAvatar-root {
        background-color: #DBE2E5;
    }

    .modal-address-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;

        .list {
            width: 100%;
            min-height: 80px;
            border-radius: 8px;
            box-shadow: 0 0 0 1px #DBE2E5;
            transition: .15s ease-out;
            padding: 16px 24px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            row-gap: 4px;
            cursor: pointer;

            &:hover {
                box-shadow: 0 0 0 1px #263238;
            }

            .title {
                font-weight: 400;
            }

            .description {
                display: flex;
                align-items: center;
                column-gap: 4px;
                font-size: 12px;
            }

            &.active {
                box-shadow: 0 0 0 2px #30D5C7;
            }
        }
    }

    input {
        //-webkit-box-shadow: 0 0 0 30px #fff inset !important;

        &:-webkit-autofill {
            border-radius: 0px !important;
            -webkit-text-fill-color: #263238 !important;
        }
    }

    form {
        p {
            margin: 24px 0 8px;
          font-weight: 600;
        }
      //label {
      //  margin: 24px 0 8px;
      //}
    }

    .stickyCell {
        position: sticky;
        right: 0;
        background-color: white;
        z-index: 1;
        top: 0;
        overflow: visible !important;

        &:before {
            content: '';
            background: linear-gradient(
                to right,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 1) 100%
            );
            position: absolute;
            z-index: 1;
            height: 100%;
            left: -32px;
            padding: 0 16px;
        }
    }

    #nprogress {
        transition: all .8s ease-out;
        position: absolute;
        z-index: 109;
        top: 0;
        .bar {
            box-shadow: 0 0 10px #16D5C5, 0 0 4px #605DEC;
            height: 2px;
            background: #16D5C5;
            animation: changeColor 2s ease-in-out infinite;
        }
    }

    @keyframes changeColor {
        0% {
            background: #16D5C5;
        }
        50% {
            background: #605DEC;
        }
        100% {
            background: #16D5C5;
        }
    }
    .MuiAutocomplete-root {
      .MuiInputBase-root {
        padding: 0 14px !important;
        input {
          padding: 0 !important;
        }
      }
    }
    .MuiInputAdornment-root {
      margin: 0;
      .MuiTypography-root {
        font-size: 14px !important;
      }
    }
    .MuiList-root {
      .MuiListSubheader-gutters {
        font-size: 12px !important;
        padding: 0 8px!important;
        &:before {
          content: '';
          background: linear-gradient(
              to top,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 1) 100%
          );
          position: absolute;
          z-index: 1;
          height: 16px;
          left: 0;
          width: 100%;
          bottom: -16px;
        }
      }
      *{
        font-size: 14px !important;
        svg {
          font-size: 24px !important;
        }
      }
    }
    .zoom-image {
      //clip-path: inset(0 round 16px);
    }
    .zoom-image-side-detail {
      clip-path: inset(0 round 16px);
    }
    .medium-zoom-image--opened {
      z-index: 102 !important;
    }
    .medium-zoom-overlay {
      z-index: 101;
      height: 100%;
      background: rgb(248 248 248 / 95%) !important;
    }
    .MuiDataGrid-row {
      animation: ${LoadingFadein} 0.3s ease-in;
    }
    .MuiDataGrid-cell {
      * {
        line-height: 1.4;
      }
      div:first-child {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .MuiPaper-root {
      border-radius: 12px !important;
      margin-top: 4px !important;
      box-shadow: 0px 0px 16px 0px rgba(38, 50, 56, 0.20) !important;
      .MuiList-padding li {
        min-height: 34px !important;
        border-radius: 4px !important;
      }
    }
    .MuiMenu-list {
      padding: 8px !important;
      display: flex;
      flex-direction: column;
      row-gap: 4px;
      //max-height: 200px !important;
    }
    .MuiMenuItem-root {
      padding: 0 8px !important;
      min-height: 34px !important;
      border-radius: 4px !important;
    }
    .MuiFormHelperText-root {
      background: transparent !important;
    }
    .MuiFormHelperText-root {
      &.Mui-error {
        margin: 4px 14px 0 !important;
        animation: ${LoadingFadein} 0.3s ease-in;
      }
    }
    .MuiTable-root {
      .MuiTableBody-root {
        .MuiTableRow-root {
          animation: ${LoadingFadein} 0.3s ease-in;
        }
      }
    }
  .MuiFormControlLabel-label {
    font-size: 14px !important;
  }
  .MuiSelect-select {
    display: flex;
    align-items: center;
    line-height: 1;
  }
  .MuiAutocomplete-noOptions {
    font-size: 14px !important;
  }
  .MuiAutocomplete-listbox {
    font-size: 14px !important;
  }
  .pacdora-animation {
    opacity: 0;
  }
`;

function MyApp({ Component, ...rest }: AppPropsWithLayout) {
  const getLayout = Component.getLayout ?? ((page: any) => page);
  const { store, props } = wrapper.useWrappedStore(rest);
  const router = useRouter();

  const GlobalInputBlocker = () => {
    useEffect(() => {
      let isFirstSpace = true;

      const isTextInput = (el: EventTarget | null): el is HTMLElement =>
        !!el &&
        (el as HTMLElement).matches?.(
          'input, textarea, [contenteditable="true"]'
        );

      const getValue = (el: HTMLElement) => {
        if (
          el instanceof HTMLInputElement ||
          el instanceof HTMLTextAreaElement
        ) {
          return el.value;
        }
        return el.textContent || '';
      };

      const handleKeydown = (e: KeyboardEvent) => {
        if (!isTextInput(e.target)) return;

        if (e.key === ' ' || e.code === 'Space') {
          if (isFirstSpace) {
            e.preventDefault();
            return;
          }
        }

        if (e.key !== ' ' && e.key?.length === 1) isFirstSpace = false;
      };

      const handleKeyup = (e: KeyboardEvent) => {
        if (!isTextInput(e.target)) return;
        if (getValue(e.target as HTMLElement).trim().length === 0) {
          isFirstSpace = true;
        }
      };

      const handleWheel = (e: WheelEvent) => {
        if (!isTextInput(e.target)) return;
        e.preventDefault();
      };
      const handleTouchMove = (e: TouchEvent) => {
        if (!isTextInput(e.target)) return;
        e.preventDefault();
      };

      const handleFocusIn = (e: FocusEvent) => {
        if (isTextInput(e.target)) isFirstSpace = true;
      };

      document.addEventListener('keydown', handleKeydown, { passive: false });
      document.addEventListener('keyup', handleKeyup);
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('focusin', handleFocusIn);

      return () => {
        document.removeEventListener('keydown', handleKeydown);
        document.removeEventListener('keyup', handleKeyup);
        window.removeEventListener('wheel', handleWheel);
        window.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('focusin', handleFocusIn);
      };
    }, []);

    return null;
  };
  useEffect(() => {
    NProgress.configure({
      // easing: 'ease-out',
      // speed: 800,
      showSpinner: false,
      trickleRate: 0.02,
      trickleSpeed: 800,
    });

    const routeChangeStartHandler = () => {
      NProgress.start();
    };

    const routeChangeEndHandler = () => {
      NProgress.done();
    };

    router.events.on('routeChangeStart', routeChangeStartHandler);
    router.events.on('routeChangeComplete', routeChangeEndHandler);
    router.events.on('routeChangeError', routeChangeEndHandler);

    return () => {
      router.events.off('routeChangeStart', routeChangeStartHandler);
      router.events.off('routeChangeComplete', routeChangeEndHandler);
      router.events.off('routeChangeError', routeChangeEndHandler);
    };
  }, []);

  const handleRouteChange = (url: string) => {
    const title = getAppTitle(url);
    if (!isEmpty(title)) {
      document.title = `HON Connect - ${title}`;
    } else {
      document.title = `HON Connect`;
    }
  };

  useEffect(() => {
    handleRouteChange(router.pathname);
    router.events.on('routeChangeComplete', handleRouteChange);
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.pathname]);

  return (
    <>
      <Head>
        <meta property="og:site_name" content="Hon" />
        <link rel="icon" type="image/x-icon" href="/icons/favicon.ico" />
        <title>HON Connect</title>
      </Head>
      <GlobalInputBlocker />
      <GlobalStyle />
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          {getLayout(<Component {...props.pageProps} />)}
        </ThemeProvider>
      </Provider>
    </>
  );
}

MyApp.getInitialProps = async (
  context: AppContext
): Promise<AppInitialProps> => {
  const ctx = await App.getInitialProps(context);
  return { ...ctx };
};

export default MyApp;
