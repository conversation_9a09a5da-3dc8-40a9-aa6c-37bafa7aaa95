import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import {
  ArrowBack,
  HighlightOff,
  HistoryOutlined,
  InfoOutlined,
} from '@mui/icons-material';
import SvgArrowDoubleBackIcon from '@/components/svg-icon/SvgArrowDoubleBackIcon';
import SvgPaperCancelIcon from '@/components/svg-icon/SvgPaperCancelIcon';
import SvgDescriptionIcon from '@/components/svg-icon/SvgDescriptionIcon';
import SvgExportNoteIcon from '@/components/svg-icon/SvgExportNoteIcon';
import SvgCalendarIcon from '@/components/svg-icon/SvgCalendarIcon';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import QuotationRenderStatus from '@/components/accounting-finance/quotation/QuotationStatus';
import { useAppDispatch, useAppSelector } from '@/store';
import { quotationSelector } from '@/store/features/quotation/reducer';
import { QuotationStatus } from '@/store/features/quotation/types';
import Image from 'next/image';
import apiQuotation from '@/services/order/qutation';
import { setQuotation } from '@/store/features/quotation/actions';
import PopoverAction from '@/components/PopoverActionn';
import { numberWithCommas } from '@/utils/number';
import moment from 'moment';
import { setSnackBar } from '@/store/features/alert';
import SvgPaperCancelModalIcon from '@/components/svg-icon/SvgPaperCancelModalIcon';
import ModalQuotationConfirm from '@/components/purchase-order/ModalPrConfirm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';
import CountUp from '@/components/CountUp';
import apiPaymentQuotation from '@/services/order/payment-quotation';

export type ConfirmModalQuotationType = {
  open: boolean;
  title: string;
  description: string;
  iconElement: () => any;
  iconBgColor?: string;
  confirmLabel: string;
  confirmAction: () => void;
  confirmBackground?: string;
  confirmColor?: string;
};

export const confirmModalInit: ConfirmModalQuotationType = {
  open: false,
  title: '',
  description: '',
  iconElement: () => null,
  iconBgColor: '#f5f7f8',
  confirmLabel: '',
  confirmAction: () => null,
};

const QuotationDetail = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { quotationId } = router.query;
  const { quotation } = useAppSelector(quotationSelector);
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);
  const [loadingExport, setLoadingExport] = useState<boolean>(false);
  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [confirmModal, setConfirmModal] =
    useState<ConfirmModalQuotationType>(confirmModalInit);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [orderDetail, setOrderDetail] = useState<any>(null);

  useEffect(() => {
    fetchQuotationDetail();
  }, [quotationId]);

  const fetchQuotationDetail = async () => {
    if (quotationId) {
      const response = await apiQuotation.getQuotationDetail({
        paymentQuotationId: Number(quotationId),
      });
      if (response.status) {
        await dispatch(
          setQuotation({
            ...quotation,
            status: response.data.status.id,
            quotationNo: response.data.quotationNo,
            detail: response.data,
          })
        );
      }
    }
  };

  useEffect(() => {
    if (quotation?.detail?.layDataOrder) {
      fetchOrderDetail();
    }
  }, [quotation?.detail]);

  const fetchOrderDetail = async () => {
    const response = await apiQuotation.getLayDataOrderDetail({
      orderId: quotation.detail.layDataOrder.layDataOrderId,
    });
    if (response.status) {
      setOrderDetail(response.data);
    } else {
      setOrderDetail(null);
    }
  };
  const handleConfirm = () => {
    switch (quotation.status) {
      case QuotationStatus.PENDING:
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ยืนยันอนุมัติใบเสนอราคา',
          description:
            `คุณได้ตรวจสอบข้อมูลใบเสนอราคา “${quotation.quotationNo}” เรียบร้อยแล้ว\n` +
            'จะทำการเข้าสู่สถานะ “รอตอบรับ” ใบเสนอราคาในขั้นตอนต่อไป',
          iconElement: () => <SvgExportNoteIcon />,
          confirmLabel: 'ส่งคำขอ',
          confirmAction: async () => {
            const res = await apiQuotation.updateQuotationStatus({
              paymentQuotationId: quotation.detail.id,
            });
            if (res.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: res.message || 'ยืนยันอนุมัติใบเสนอราคาสำเร็จ',
                  severity: 'success',
                })
              );
              fetchQuotationDetail();
              setTimeout(() => onCloseConfirmModal(), 1000);
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: res.message || 'ยืนยันอนุมัติใบเสนอราคาไม่สำเร็จ',
                  severity: 'error',
                })
              );
              setTimeout(() => onCloseConfirmModal(), 1000);
            }
          },
        });
        break;
      case QuotationStatus.WAITING:
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ยืนยันลูกค้าตอบรับแล้ว',
          description:
            `คุณได้ตรวจสอบข้อมูลใบเสนอราคา “${quotation.quotationNo}” เรียบร้อยแล้ว\n` +
            'จะทำการเข้าสู่สถานะ “ตอบรับแล้ว” ใบเสนอราคาในขั้นตอนต่อไป',
          iconElement: () => <SvgExportNoteIcon />,
          confirmLabel: 'ส่งคำขอ',
          confirmAction: async () => {
            const res = await apiQuotation.updateQuotationStatus({
              paymentQuotationId: quotation.detail.id,
            });
            if (res.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: res.message || 'ยืนยันลูกค้าตอบรับสำเร็จ',
                  severity: 'success',
                })
              );
              fetchQuotationDetail();
              setTimeout(() => onCloseConfirmModal(), 1000);
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: res.message || 'ยืนยันลูกค้าตอบรับไม่สำเร็จ',
                  severity: 'error',
                })
              );
              setTimeout(() => onCloseConfirmModal(), 1000);
            }
          },
        });
        break;
      case QuotationStatus.APPROVED:
        router.push('/accounting-finance/invoice');
        break;
      default:
    }
  };

  const KabubCustomItems: any[] = [
    {
      IconElement: () => <SvgArrowDoubleBackIcon />,
      title: 'ตีกลับไปแก้',
      disabled: !isAllowed(permissions, 'accounting-finance.quotation.update'),
      onAction: () => {
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ยืนยันตีกลับไปแก้',
          description: `คุณต้องการตีกลับไปแก้ไขใบเสนอราคา\n“${quotation?.quotationNo}” ใช่หรือไม่`,
          iconElement: () => <SvgArrowDoubleBackIcon />,
          confirmLabel: 'ยืนยัน',
          confirmAction: async () => {
            const response = await apiQuotation.revokeQuotationStatus({
              paymentQuotationId: Number(quotationId),
              statusId: QuotationStatus.DRAFT,
            });
            if (response.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ตีกลับไปแก้สำเร็จ',
                  severity: 'success',
                })
              );
              fetchQuotationDetail();
              setTimeout(
                () =>
                  router.push(
                    `/accounting-finance/quotation/${quotationId}/create`
                  ),
                1000
              );
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ตีกลับไปแก้ไม่สำเร็จ',
                  severity: 'error',
                })
              );
            }
          },
        });
      },
      cssProps: {
        display:
          quotation.status === QuotationStatus.DRAFT ||
          quotation.status === QuotationStatus.PENDING ||
          quotation.status === QuotationStatus.WAITING
            ? ''
            : 'none',
      },
    },
    {
      IconElement: () => <HighlightOff />,
      title: 'ปฏิเสธใบเสนอราคา',
      disabled: !isAllowed(permissions, 'accounting-finance.quotation.update'),
      onAction: () => {
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ปฏิเสธใบเสนอราคา',
          description: `คุณต้องการปฏิเสธใบเสนอราคา\n“${quotation?.quotationNo}” ใช่หรือไม่`,
          iconElement: () => <SvgPaperCancelModalIcon />,
          iconBgColor: '#FDE8EF',
          confirmLabel: 'ยืนยัน',
          confirmBackground: '#D32F2F',
          confirmColor: '#fff',
          confirmAction: async () => {
            const response = await apiQuotation.revokeQuotationStatus({
              paymentQuotationId: Number(quotationId),
              statusId: QuotationStatus.REJECT,
            });
            if (response.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ปฏิเสธใบเสนอราคาสำเร็จ',
                  severity: 'success',
                })
              );
              fetchQuotationDetail();
              setTimeout(() => onCloseConfirmModal(), 1000);
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ปฏิเสธใบเสนอราคาไม่สำเร็จ',
                  severity: 'error',
                })
              );
              setTimeout(() => onCloseConfirmModal(), 1000);
            }
          },
        });
      },
      display:
        quotation.status === QuotationStatus.CANCEL ||
        quotation.status === QuotationStatus.REJECT ||
        quotation.status === QuotationStatus.APPROVED
          ? 'none'
          : '',
    },
    {
      IconElement: () => <HistoryOutlined />,
      title: 'ประวัติรายการ',
      onAction: () => null,
      cssProps: {
        display: 'none',
      },
    },
    {
      IconElement: () => <InfoOutlined />,
      title: 'วิธีใช้งาน',
      onAction: () => null,
      cssProps: {
        display: 'none',
      },
    },
    {
      IconElement: () => <SvgPaperCancelIcon fill={'#D32F2F'} />,
      title: 'ยกเลิกรายการ',
      disabled: !isAllowed(permissions, 'accounting-finance.quotation.update'),
      onAction: () => {
        setConfirmModal({
          ...confirmModal,
          open: true,
          title: 'ยกเลิกรายการ',
          description: `คุณต้องการยกเลิกใบเสนอราคา\n“${quotation?.quotationNo}” ใช่หรือไม่`,
          iconElement: () => <SvgPaperCancelModalIcon />,
          iconBgColor: '#FDE8EF',
          confirmLabel: 'ยืนยัน',
          confirmBackground: '#D32F2F',
          confirmColor: '#fff',
          confirmAction: async () => {
            const response = await apiQuotation.revokeQuotationStatus({
              paymentQuotationId: Number(quotationId),
              statusId: QuotationStatus.CANCEL,
            });
            if (response.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ยกเลิกรายการสำเร็จ',
                  severity: 'success',
                })
              );
              fetchQuotationDetail();
              setTimeout(() => onCloseConfirmModal(), 1000);
            } else {
              dispatch(
                setSnackBar({
                  status: true,
                  text: response.message || 'ยกเลิกรายการไม่สำเร็จ',
                  severity: 'error',
                })
              );
              setTimeout(() => onCloseConfirmModal(), 1000);
            }
          },
        });
      },
      cssProps: {
        color: '#D32F2F',
        '&:hover': {
          backgroundColor: '#FDE8EF',
        },
        display:
          quotation.status === QuotationStatus.CANCEL ||
          quotation.status === QuotationStatus.REJECT ||
          quotation.status === QuotationStatus.APPROVED
            ? 'none'
            : '',
      },
    },
  ];

  const RenderConfirmBtnLabel = () => {
    switch (quotation.status) {
      case QuotationStatus.PENDING:
        return 'ยืนยันอนุมัติใบเสนอราคา';
      case QuotationStatus.WAITING:
        return 'ยืนยันใบเสนอราคา';
      case QuotationStatus.APPROVED:
        return 'สร้างใบแจ้งหนี้';
      default:
        return '';
    }
  };

  const onCloseConfirmModal = () => {
    setConfirmModal({ ...confirmModal, open: false });
    setTimeout(() => {
      setConfirmModal({ ...confirmModalInit });
      setConfirmLoading(false);
    }, 1000);
  };

  const handleDownload = async () => {
    setLoadingExport(true);
    const res = await apiPaymentQuotation.exportQuotation(Number(quotationId));
    if (res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Download failed',
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    setLoadingExport(false);
  };

  console.log('quotation', quotation);
  return (
    <Container>
      <ModalQuotationConfirm
        open={confirmModal.open}
        handleCloseModal={onCloseConfirmModal}
        data={confirmModal}
        loading={confirmLoading}
      />
      {isAllowed(permissions, 'accounting-finance.quotation.list') ? (
        <div className={'flex flex-col gap-[8px]'}>
          <QuotationHeader>
            <div className={'header'}>
              <div className={'header-info'}>
                <ArrowBack
                  className={'cursor-pointer'}
                  onClick={() => router.push('/accounting-finance/quotation')}
                />
                <span className={'quotation-id'}>{quotation.quotationNo}</span>
                <QuotationRenderStatus status={quotation.status} />
              </div>
              <div className={'header-action'}>
                {(quotation.status === QuotationStatus.PENDING ||
                  quotation.status === QuotationStatus.WAITING ||
                  quotation.status === QuotationStatus.APPROVED) && (
                  <ActionButton
                    disabled={
                      !isAllowed(
                        permissions,
                        'accounting-finance.quotation.update'
                      )
                    }
                    variant="contained"
                    color="Hon"
                    text={RenderConfirmBtnLabel()}
                    onClick={() => handleConfirm()}
                  />
                )}
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  text="ดาวน์โหลดไฟล์"
                  borderRadius={'8px'}
                  onClick={handleDownload}
                  disabled={loadingExport}
                />
                {quotation.status !== QuotationStatus.APPROVED &&
                  quotation.status !== QuotationStatus.REJECT &&
                  quotation.status !== QuotationStatus.CANCEL &&
                  !quotation?.detail?.isDefault && (
                    <JobDetailKebabWrap>
                      <PopoverAction
                        triggerElement={<MoreHorizRoundedIcon />}
                        customItems={KabubCustomItems}
                      />
                    </JobDetailKebabWrap>
                  )}
              </div>
            </div>
            <div className={'quotation-info'}>
              <div className={'customer-info'}>
                <div className={'detail-item user-display a'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        quotation?.detail?.customerData?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>
                      {quotation?.detail?.customerData?.name}
                    </div>
                    <div className={'info'}>
                      {`ลูกค้า • ${quotation?.detail?.customerData?.contactType.name} • เครดิต ${quotation?.detail?.customerData?.creditType?.day} วัน`}
                    </div>
                  </div>
                </div>
                <div className={'detail-item b label-value'}>
                  <div className={'title'}>
                    <SvgDescriptionIcon />
                    <span>รายการสั่งผลิต</span>
                  </div>
                  <div className={'value'}>
                    {quotation?.detail?.layDataOrder?.layDataOrderNo || ''}
                  </div>
                </div>
                <div className={'detail-item c label-value'}>
                  <div className={'title'}>
                    <SvgExportNoteIcon />
                    <span>รายการสินค้า</span>
                  </div>
                  <div className={'value'}>{`${
                    orderDetail?.layData.length || ''
                  } รายการ`}</div>
                </div>
              </div>
              <div className={'quotation-detail'}>
                <div className={'detail-item a'}>
                  <span className={'detail'}>
                    <span className={'bold'}>หมายเหตุ</span>
                    {quotation?.detail?.remark || ''}
                    {/* ราคานี้ยังไม่รวมภาษีมูลค่าเพิ่ม 7% เงื่อนไขการเริ่มงาน / */}
                    {/* การชำระเงิน มัดจำค่าสินค้า 80 % ก่อนเริ่มงาน ส่วนที่เหลือ */}
                    {/* ชำระก่อนจัดส่ง ระยะเวลาผลิต หลังมัดจำ และ */}
                    {/* ยืนยันตัวอย่างสินค้าเข้าผลิตประมาณ 12-15 วันทำการ */}
                    {/* กรณียอดไม่ถึง 10000 บาท ชำระเต็ม100 % ก่อนเริ่มงาน */}
                    {/* (ระยะเวลาขึ้นอยู่กับ จำนวนและสเปคงานค่ะ) - ยอดเกิน 15,000 บาท */}
                    {/* การจัดส่ง ฟรี กทม.// ตจว. ขนส่งเก็บปลายทาง (หมายเหตุ : */}
                    {/* ทางบริษัทฯ ขอสงวนสิทธิ์ในการขอคืนเงินมัดจำทุกกรณี) */}
                  </span>
                </div>
                <div className={'detail-item b label-value'}>
                  <div className={'title'}>
                    <SvgCalendarIcon />
                    <span>วันที่สร้าง</span>
                  </div>
                  <div className={'value'}>
                    {moment(quotation?.detail?.createdDate).format(
                      'DD/MM/YYYY, HH:mm น.'
                    )}
                  </div>
                </div>
                <div className={'detail-item c label-value'}>
                  <div className={'title'}>
                    <SvgCalendarIcon />
                    <span>วันที่ใช้ได้ถึง</span>
                  </div>
                  <div className={'value'}>
                    {moment(quotation?.detail?.dueDate).format(
                      'DD/MM/YYYY, HH:mm น.'
                    )}
                  </div>
                </div>
                <div className={'detail-item d user-display'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        quotation?.detail?.createdUser?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>
                      {quotation?.detail?.createdUser?.name}
                    </div>
                    <div className={'info'}>
                      {quotation?.detail?.createdUser?.userTypeName}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </QuotationHeader>
          <QuotationContent>
            <div className="table-container">
              <div className="section-title">รายการเสนอราคา</div>
              <TableWrapper>
                <TableContainer
                  component={Paper}
                  elevation={0}
                  sx={{
                    '& .MuiTable-root': {
                      borderCollapse: 'separate',
                      borderSpacing: '0px',
                    },
                    '& .MuiTableHead-root .MuiTableRow-root': {
                      height: '32px',
                    },
                    '& .MuiTableHead-root .MuiTableCell-root': {
                      padding: '4px 16px',
                      fontSize: '12px',
                    },
                    '& .MuiTableBody-root .MuiTableRow-root': {
                      height: '72px',
                    },
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell align="center" sx={{ width: '50px' }}>
                          #
                        </TableCell>
                        <TableCell>รายการ</TableCell>
                        <TableCell>รายละเอียด</TableCell>
                        <TableCell align="center" sx={{ width: '120px' }}>
                          จำนวน
                        </TableCell>
                        <TableCell align="center" sx={{ width: '120px' }}>
                          ราคา/หน่วย
                        </TableCell>
                        <TableCell align="right" sx={{ width: '120px' }}>
                          ราคาสินค้า (บาท)
                        </TableCell>
                        <TableCell align="right" sx={{ width: '120px' }}>
                          ส่วนลด/หน่วย
                        </TableCell>
                        <TableCell align="right" sx={{ width: '120px' }}>
                          ราคารวม
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {quotation &&
                      quotation.detail &&
                      quotation.detail.quotationItem.length > 0 ? (
                        quotation.detail.quotationItem.map(
                          (field: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell align="center">{index + 1}</TableCell>
                              <TableCell>
                                <div>{field.name}</div>
                              </TableCell>
                              <TableCell>
                                {`${field.layData?.ldCode || 'LD'} • ${
                                  field.description || '-'
                                }`}
                              </TableCell>
                              <TableCell align="center">
                                <div>{numberWithCommas(field.quantity)}</div>
                              </TableCell>
                              <TableCell align="center">
                                <div>
                                  {numberWithCommas(field.pricePerUnit, 2)}
                                </div>
                              </TableCell>
                              <TableCell align="right">
                                <div>
                                  {numberWithCommas(
                                    field.price +
                                      field.discount * field.quantity,
                                    2
                                  )}
                                </div>
                              </TableCell>
                              <TableCell align="right">
                                <div>{numberWithCommas(field.discount, 2)}</div>
                              </TableCell>
                              <TableCell align="right">
                                <div>{numberWithCommas(field.price, 2)}</div>
                              </TableCell>
                            </TableRow>
                          )
                        )
                      ) : (
                        <TableRow>
                          <TableCell
                            align="center"
                            colSpan={9}
                            sx={{ color: '#dbe2e5' }}
                          >
                            ไม่มีรายการเสนอราคา
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </TableWrapper>
            </div>
            <div className={'summary-container'}>
              <div></div>
              <div className="qt-summary">
                <div className="summary-content">
                  <div className="summary-row border">
                    <span className={'label'}>ยอดรวม</span>
                    <span className={'result'}>{`${numberWithCommas(
                      quotation?.detail?.netPrice,
                      2
                    )} บาท`}</span>
                  </div>
                  <div className="summary-row">
                    <div
                      className="flex items-center"
                      style={{
                        columnGap: '4px',
                      }}
                    >
                      ส่วนลด
                      <div className="label">
                        <CountUp
                          from={0}
                          to={quotation?.detail?.discountRate || 0}
                          separator=","
                          duration={0.3}
                          decimals={2}
                        />
                        %
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={'result'}>{`${numberWithCommas(
                        quotation?.detail?.discount,
                        2
                      )} บาท`}</span>
                    </div>
                  </div>
                  <div className="summary-row">
                    <div
                      className="flex items-center"
                      style={{
                        columnGap: '4px',
                      }}
                    >
                      ภาษีมูลค่าเพิ่ม
                      <div className="label">
                        <CountUp
                          from={0}
                          to={quotation?.detail?.vatRate || 0}
                          separator=","
                          duration={0.3}
                          decimals={2}
                        />
                        %
                      </div>
                    </div>
                    <span className={'result'}>{`${numberWithCommas(
                      quotation?.detail?.vatAmount,
                      2
                    )} บาท`}</span>
                  </div>
                  {/* <div className="summary-row"> */}
                  {/*  <span className={'label'}> */}
                  {/*    ค่าจัดส่ง (จัดส่งด้วยตัวแทนจำหน่าย) */}
                  {/*  </span> */}
                  {/*  <div className="flex items-center gap-2"> */}
                  {/*    <span className={'result'}>{`${numberWithCommas( */}
                  {/*      0, */}
                  {/*      2 */}
                  {/*    )} บาท`}</span> */}
                  {/*  </div> */}
                  {/* </div> */}
                  <div className="summary-row final">
                    <span className={'label large bold'}>มูลค่ารวมสุทธิ</span>
                    <span className={'result large'}>{`${numberWithCommas(
                      quotation?.detail?.totalPrice,
                      2
                    )} บาท`}</span>
                  </div>
                </div>
              </div>
            </div>
          </QuotationContent>
        </div>
      ) : (
        <NotPermission />
      )}
    </Container>
  );
};

QuotationDetail.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
export default QuotationDetail;

const Container = styled.div`
  background: #f5f7f8;
`;

const QuotationHeader = styled.div`
  background: #fff;
  border-bottom: 1px solid #dbe2e5;
  padding: 40px;
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-info {
      display: flex;
      align-items: center;
      column-gap: 16px;
      .quotation-id {
        color: #263238;
        font-size: 40px;
        font-weight: 600;
        letter-spacing: 0.4px;
      }
    }
    .header-action {
      display: flex;
      align-items: center;
      column-gap: 16px;
    }
  }
  .quotation-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 40px;
    .user-display {
      .avatar {
        display: flex;
        align-items: center;
      }
      .label {
        .name {
          font-size: 14px;
          font-weight: 600;
        }
        .info {
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
    .customer-info {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item.a {
        grid-column: span 2 / span 2;
        display: flex;
        align-items: center;
        column-gap: 16px;
        padding: 24px;
        border-bottom: 1px solid #dbe2e5;
      }
      .detail-item.b {
        grid-row-start: 2;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.c {
        grid-row-start: 2;
      }
    }
    .label-value {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 24px;
      row-gap: 8px;
      .title {
        display: flex;
        column-gap: 8px;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
      }
      .value {
        font-size: 14px;
        font-weight: 600;
      }
    }
    .quotation-detail {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item.a {
        grid-column: span 3 / span 3;
        color: #263238;
        font-size: 12px;
        font-weight: 400;
        line-height: normal;
        padding: 24px;
        border-bottom: 1px solid #dbe2e5;
        .bold {
          font-weight: 600;
          margin-right: 8px;
          text-decoration: underline;
        }
        .detail {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
      .detail-item.b {
        grid-row-start: 2;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.c {
        grid-row-start: 2;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.d {
        grid-row-start: 2;
        display: flex;
        align-items: center;
        column-gap: 16px;
        padding: 0 24px;
      }
    }
  }
`;
const QuotationContent = styled.div`
  border-top: 1px solid #dbe2e5;
  background: #fff;
  padding: 40px;
  .table-container {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    .section-title {
      color: #263238;
      font-size: 22px;
      font-weight: 600;
      line-height: 1;
    }
  }
  .summary-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 24px 0;
    .qt-summary {
      display: flex;
      justify-content: flex-end;
      .summary-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        .actions {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 24px;
          button {
            font-weight: 600;
            font-size: 14px;
          }
        }
      }

      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        .label {
          color: #263238;
          font-size: 16px;
          font-weight: 400;
        }
        .result {
          color: #263238;
          font-size: 16px;
          font-weight: 600;
        }
        .bold {
          font-weight: 600;
        }
        .large {
          font-size: 22px;
        }
      }
      .summary-row.border {
        border-bottom: 1px solid #dbe2e5;
        padding-bottom: 16px;
      }
      .summary-row.final {
        padding: 16px 0;
        border-top: 2px solid #263238;
        border-bottom: 2px solid #263238;
      }
    }
  }
`;

const JobDetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;

const TableWrapper = styled.div`
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #dbe2e5;
    border-radius: 16px !important;
  }
  .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root {
    border-bottom: none;
  }
`;
