import styled from 'styled-components';
import React, { ReactElement, useEffect } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { ArrowBack, InfoOutlined } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import { quotationSelector } from '@/store/features/quotation/reducer';
import QuotationRenderStatus from '@/components/accounting-finance/quotation/QuotationStatus';
import apiQuotation from '@/services/order/qutation';
import { setQuotation } from '@/store/features/quotation/actions';
import QuotationForm from '@/components/accounting-finance/quotation/QuotationUpdateForm';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CreateQuotation = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { quotationId } = router.query;
  const { quotation } = useAppSelector(quotationSelector);
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  useEffect(() => {
    fetchQuotationDetail().then();
  }, [quotationId]);

  const fetchQuotationDetail = async () => {
    if (quotationId) {
      const response = await apiQuotation.getQuotationDetail({
        paymentQuotationId: Number(quotationId),
      });
      if (response.status) {
        dispatch(
          setQuotation({
            ...quotation,
            status: response.data.status.id,
            quotationNo: response.data.quotationNo,
            detail: response.data,
          })
        );
      }
    }
  };

  return (
    <Container>
      <QuotationNav>
        <ArrowBack
          className={'icon back'}
          onClick={() => router.push('/accounting-finance/quotation')}
        />
        <span>สร้างใบเสนอราคา</span>
        <InfoOutlined className={'icon info'} />
      </QuotationNav>
      {isAllowed(permissions, 'accounting-finance.quotation.create') ? (
        <div className={'flex flex-col gap-[8px]'}>
          <QuotationHeader>
            <span className={'quotation-id'}>{quotation.quotationNo}</span>
            <QuotationRenderStatus status={quotation.status} />
          </QuotationHeader>
          <QuotationContent>
            <QuotationForm />
          </QuotationContent>
        </div>
      ) : (
        <NotPermission />
      )}
    </Container>
  );
};

export default CreateQuotation;
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};
CreateQuotation.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

const Container = styled.div`
  background: #f5f7f8;
`;
const QuotationNav = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px 0 16px;
  border-bottom: 1px solid #dbe2e5;
  background: #fff;

  .icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  span {
    font-size: 22px;
    font-weight: 600;
  }
  .info {
    color: #dbe2e5;
  }
`;
const QuotationHeader = styled.div`
  height: 120px;
  background: #fff;
  border-bottom: 1px solid #dbe2e5;
  display: flex;
  align-items: center;
  column-gap: 16px;
  padding: 0 40px;
  .quotation-id {
    color: #263238;
    font-size: 40px;
    font-weight: 600;
    letter-spacing: 0.4px;
  }
  .quotation-status {
    display: flex;
    height: 32px;
    padding: 8px 16px;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid #dbe2e5;
    background: #f5f7f8;
    color: #263238;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.14px;
  }
`;
const QuotationContent = styled.div`
  border-top: 1px solid #dbe2e5;
  background: #fff;
`;
