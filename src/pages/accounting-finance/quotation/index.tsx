import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import ProductNav from '@/components/product/ProductNav';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';

import { Badge, Tab, Tabs } from '@mui/material';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useDebounceCallback } from 'usehooks-ts';
import AppPagination from '@/components/global/AppPagination';
import ModalCreateQuotation from '@/components/accounting-finance/quotation/modal/ModalCreateQuotation';
import AppDateRange from '@/components/global/AppDateRange';
import apiQuotation from '@/services/order/qutation';
import { QuotationStatus } from '@/store/features/quotation/types';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import styled from 'styled-components';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const DetailButton = styled.div<{ disabled: boolean }>`
  font-size: 12px;
  border-radius: 8px;
  border: 1px solid #dbe2e5;
  padding: 10px 10px;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.3 : 1)};
  &:hover {
    border: 1px solid #263238;
  }
`;

const QuotationPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);
  const [search, setSearch] = useState<string | null>(null);
  const [filters, setFilters] = useState<any>({
    paymentQuotationStatusId: QuotationStatus.DRAFT,
    page: 0,
    size: 10,
    startDate: null,
    endDate: null,
    paymentQuotationNo: '',
    layDataOrderNo: '',
    search: search,
  });
  const [createVisible, setCreateVisible] = useState<boolean>(false);
  const debounced = useDebounceCallback(setSearch, 700);
  const [quotation, setQuotation] = useState<any>(null);
  const [quotationStatus, setQuotationStatus] = useState<
    { id: number; name: string; count: number }[]
  >([]);

  const handleChangeStatus = (_event: React.SyntheticEvent, newStatus: any) => {
    setFilters({ ...filters, paymentQuotationStatusId: newStatus });
  };

  useEffect(() => {
    fetchQuotationStatus();
    fetchQuotations();
  }, [filters]);

  useEffect(() => {
    setFilters({ ...filters, search: search });
  }, [search]);

  const fetchQuotationStatus = async () => {
    const response = await apiQuotation.getStatusList();
    if (response.status) {
      setQuotationStatus(response.data);
    } else {
      setQuotationStatus([]);
    }
  };

  const fetchQuotations = async () => {
    const response = await apiQuotation.getQuotations(filters);
    if (response.status) {
      setQuotation(response.data);
    } else {
      setQuotation(null);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'paymentQuotationNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 180,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'layDataOrderNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 180,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'customerData',
      headerName: 'ลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.customerData.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.customerData.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'productQuantity',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 120,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
    },
    {
      field: 'totalPrice',
      headerName: 'ยอดรวมสินค้า (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 162,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.totalPrice.toLocaleString('th-TH', {
              minimumFractionDigits: 2,
            })}
          </div>
        );
      },
    },
    {
      field: 'paymentQuotationStatus',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 120,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              display: 'flex',
              height: '24px',
              padding: '7px 8px',
              alignItems: 'center',
              gap: '8px',
              background: '#263238',
              borderRadius: '4px',
              color: '#fff',
            }}
          >
            {params.row.paymentQuotationStatus.name || ''}
          </div>
        );
      },
    },
    {
      field: 'admin',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      flex: 2,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.admin.imageUrl || '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.admin.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'dueDate',
      headerName: 'วันที่ครบกำหนด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      renderCell: (params: any) => {
        return <div>{dayjs(params.row.dueDate).format('DD/MM/YYYY')}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <DetailButton
              disabled={
                !isAllowed(permissions, 'accounting-finance.quotation.list')
              }
              onClick={() => {
                if (
                  isAllowed(permissions, 'accounting-finance.quotation.list')
                ) {
                  if (
                    params.row.paymentQuotationStatus.id ===
                    QuotationStatus.DRAFT
                  ) {
                    router.push(
                      `/accounting-finance/quotation/${params.row.id}/create`
                    );
                  } else {
                    router.push(
                      `/accounting-finance/quotation/${params.row.id}`
                    );
                  }
                }
              }}
            >
              รายละเอียด
            </DetailButton>
            {/* <PopoverAction */}
            {/*  triggerElement={ */}
            {/*  <div className="kebab"> */}
            {/*    <div className="dot" /> */}
            {/*  </div> */}
            {/* } */}
            {/*  customItems={[ */}
            {/*    { */}
            {/*      IconElement: () => <SvgPaperIcon />, */}
            {/*      title: 'รายละเอียด', */}
            {/*      onAction: () => { */}
            {/*        if ( */}
            {/*          params.row.paymentQuotationStatus.id === */}
            {/*          QuotationStatus.DRAFT */}
            {/*        ) { */}
            {/*          router.push( */}
            {/*            `/accounting-finance/quotation/${params.row.id}/create` */}
            {/*          ); */}
            {/*        } else { */}
            {/*          router.push( */}
            {/*            `/accounting-finance/quotation/${params.row.id}` */}
            {/*          ); */}
            {/*        } */}
            {/*      }, */}
            {/*    }, */}
            {/*  ]} */}
            {/* /> */}
          </>
        );
      },
    },
  ];

  function onCreateQuotation() {
    setCreateVisible(true);
  }

  return (
    <div>
      <ModalCreateQuotation
        open={createVisible}
        handleCloseModal={() => setCreateVisible(false)}
      />
      <ProductNav title="ใบเสนอราคา" showBorderBottom>
        <ActionGroupStyle>
          <AppDateRange
            data={{
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            handleChange={(dateRange: any) => {
              setFilters({
                ...filters,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              });
            }}
          />
          <ActionButton
            disabled={
              !isAllowed(permissions, 'accounting-finance.quotation.create')
            }
            variant="contained"
            color="Hon"
            icon={<AddCircle />}
            text="สร้างใบเสนอราคา"
            onClick={onCreateQuotation}
          />
        </ActionGroupStyle>
      </ProductNav>
      {isAllowed(permissions, 'accounting-finance.quotation.list') ? (
        <AppContentStyle>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div
                style={{
                  minWidth: '800px',
                }}
              >
                <Tabs
                  value={filters.paymentQuotationStatusId}
                  onChange={handleChangeStatus}
                  variant="standard"
                >
                  {quotationStatus &&
                    quotationStatus.map((item: any, index: React.Key) => (
                      <Tab
                        key={index}
                        label={
                          <div
                            className="flex items-center"
                            style={{
                              columnGap: '16px',
                              minHeight: '32px',
                            }}
                          >
                            {item.name}
                            <Badge
                              badgeContent={item.count || '0'}
                              color="secondary"
                              sx={{
                                '.MuiBadge-badge': {
                                  backgroundColor:
                                    filters.paymentQuotationStatusId === item.id
                                      ? '#263238'
                                      : '#607D8B',
                                },
                              }}
                            />
                          </div>
                        }
                        value={item.id}
                        sx={{
                          color:
                            filters.paymentQuotationStatusId === item.id
                              ? '#263238'
                              : '',
                        }}
                      />
                    ))}
                </Tabs>
              </div>
            </FilterWrapStyled>
            <div className="flex justify-between items-center px-4 py-2">
              <div>{quotation?.totalElements || 0} รายการ</div>
              <div className="flex gap-2 items-center">
                {/* <Image */}
                {/*  className="cursor-pointer" */}
                {/*  src="/icons/icon-filter.svg" */}
                {/*  alt="" */}
                {/*  width={24} */}
                {/*  height={24} */}
                {/* /> */}
                <SearchInput
                  makeSearchValue={(newValue) => {
                    debounced(newValue);
                  }}
                />
              </div>
            </div>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle $rows={quotation?.content}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={124} />
                  <DataGrid
                    hideFooter={true}
                    rows={quotation?.content || []}
                    columns={columns}
                    paginationMode="server"
                    rowCount={quotation?.totalElements || 0}
                    pageSize={filters.size}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    // loading={false}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                      NoResultsOverlay: () => <TableNoRowsOverlay />,
                    }}
                  />
                </ScrollBarStyled>
                <div className="px-[16px]">
                  <AppPagination
                    filters={filters}
                    totalElements={quotation?.totalElements || 0}
                    handleChangeFilters={(newValues: any) =>
                      setFilters(newValues)
                    }
                  />
                </div>
              </div>
            </AppTableStyle>
          </div>
        </AppContentStyle>
      ) : (
        <NotPermission />
      )}
    </div>
  );
};

QuotationPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default QuotationPage;
