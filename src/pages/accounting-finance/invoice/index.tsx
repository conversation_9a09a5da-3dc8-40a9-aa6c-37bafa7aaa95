import { AddCircle } from '@mui/icons-material';
import React, { ReactElement, useEffect, useRef, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Avatar, Badge, Tab, Tabs } from '@mui/material';
import AppPagination from '@/components/global/AppPagination';
import { useRouter } from 'next/router';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import ProductNav from '@/components/product/ProductNav';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import ModalCreateInvoice from '@/components/accounting-finance/invoice/modal/ModalCreateInvoice';
import SearchInput from '@/components/SearchInput';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { numberWithCommas } from '@/utils/number';
import styled from 'styled-components';
import dayjs from 'dayjs';
import AppDateRange from '@/components/global/AppDateRange';
import apiInvoice from '@/services/order/invoice';
import { GetServerSideProps } from 'next';
import PopoverAction from '@/components/PopoverActionn';
import SvgPaperIcon from '@/components/svg-icon/SvgPaperIcon';
import { getCookie } from 'cookies-next';
import { isEmpty } from 'lodash';
import apiUser from '@/services/core/user';
import { useAppDispatch, useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { setPermission } from '@/store/features/permission/actions';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const ChipStyled = styled.div`
  height: 24px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  border-radius: 4px;
`;
export type InvoiceFilterType = {
  page: number;
  size: number;
  searchName: string;
  startDate: string | null;
  endDate: string | null;
  paymentInvoiceStatusId: number;
};

type InvoiceDataListPageProps = {
  status: string;
  authorities: string[];
};

type InvoiceStatusType = {
  id: number;
  name: string;
  sort: number;
  count: number;
};

const InvoiceDataListPage = ({
  status,
  authorities,
}: InvoiceDataListPageProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const scrollBarRef = useRef<HTMLDivElement>(null);
  const [rows, setRows] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalElements, setTotalElements] = useState(0);
  const [filters, setFilters] = useState<InvoiceFilterType>({
    page: 0,
    size: 10,
    searchName: '',
    startDate: null,
    endDate: null,
    paymentInvoiceStatusId: 0,
  });
  const [invoiceStatus, setInvoiceStatus] = useState<InvoiceStatusType[]>([
    {
      id: 1,
      name: 'แบบร่าง',
      sort: 1,
      count: 0,
    },
    {
      id: 2,
      name: 'รออนุมัติ',
      sort: 2,
      count: 0,
    },
    {
      id: 3,
      name: 'รอรับชำระ',
      sort: 3,
      count: 1,
    },
    {
      id: 4,
      name: 'รับชำระแล้ว',
      sort: 4,
      count: 0,
    },
    {
      id: 5,
      name: 'เกินเวลารับชำระ',
      sort: 5,
      count: 0,
    },
    {
      id: 6,
      name: 'ปฏิเสธ',
      sort: 6,
      count: 0,
    },
    {
      id: 7,
      name: 'ยกเลิก',
      sort: 7,
      count: 0,
    },
  ]);

  useEffect(() => {
    getInvoiceStatus().then();
  }, [rows]);

  useEffect(() => {
    getList(status).then();
    setRows([]);
  }, [status, filters]);

  const getInvoiceStatus = async () => {
    const res = await apiInvoice.getInvoiceStatus();
    if (!res.isError) {
      setInvoiceStatus(res.data);
    }
  };

  const getList = async (status: string) => {
    console.log('getList', status);
    setLoading(true);
    let numberInvoiceStatus;
    switch (status) {
      case 'แบบร่าง':
        numberInvoiceStatus = 1;
        break;
      case 'รออนุมัติ':
        numberInvoiceStatus = 2;
        break;
      case 'รอรับชำระ':
        numberInvoiceStatus = 3;
        break;
      case 'รับชำระแล้ว':
        numberInvoiceStatus = 4;
        break;
      case 'เกินเวลารับชำระ':
        numberInvoiceStatus = 5;
        break;
      case 'ปฏิเสธ':
        numberInvoiceStatus = 6;
        break;
      case 'ยกเลิก':
        numberInvoiceStatus = 7;
        break;
      default:
        numberInvoiceStatus = 1;
    }

    console.log('numberInvoiceStatus', numberInvoiceStatus);

    const res = await apiInvoice.getInvoiceList({
      ...filters,
      paymentInvoiceStatusId: numberInvoiceStatus,
    });

    if (res && !res.isError) {
      const { content, totalElements } = res.data;
      setRows(content);
      setTotalElements(totalElements);
    }
    setLoading(false);
  };

  const changeStatus = async (status: string) => {
    await router.push(
      `/accounting-finance/invoice?status=${status}`,
      undefined
    );
  };

  const handleChangeStatus = async (
    event: React.SyntheticEvent,
    newStatus: any
  ) => {
    await changeStatus(newStatus);
  };

  const columns: GridColDef[] = [
    {
      field: 'paymentInvoiceNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'paymentQuotationNo',
      headerName: 'ใบเสนอราคา (อ้างอิง)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return params.row.paymentQuotationNo || '-';
      },
    },
    {
      field: 'customerData',
      headerName: 'ลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 210,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '10px',
              maxWidth: '100%',
            }}
          >
            {/* <Image */}
            {/*  src={ */}
            {/*    params.row.customer.imageUrl || */}
            {/*    '/images/product/empty-product.svg' */}
            {/*  } */}
            {/*  width={32} */}
            {/*  height={32} */}
            {/*  alt="" */}
            {/*  style={{ */}
            {/*    borderRadius: '50%', */}
            {/*    objectFit: 'cover', */}
            {/*    minWidth: '32px', */}
            {/*  }} */}
            {/* /> */}
            <Avatar
              src={params.row.customer.imageUrl}
              sx={{
                height: '32px',
                width: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.customer.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'productQuantity',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.productQuantity);
      },
    },
    {
      field: 'totalPrice',
      headerName: 'ยอดรวมสินค้า (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.totalPrice);
      },
    },
    {
      field: 'paymentInvoiceStatus',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <ChipStyled
            style={{
              background: 'black',
            }}
          >
            {params.row.paymentInvoiceStatus.name || 'n/a'}
          </ChipStyled>
        );
      },
    },
    {
      field: 'createUser',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '10px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.createUser.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={32}
              height={32}
              alt=""
              style={{
                borderRadius: '50%',
                objectFit: 'cover',
                minWidth: '32px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {params.row.createUser.name}
            </div>
          </div>
        );
      },
    },
    {
      field: 'dueDate',
      headerName: 'กำหนดรับชำระ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 164,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        console.log('params.row.dueDate', params.row.dueDate);
        return <div>{dayjs(params.row.dueDate).format('DD/MM/YYYY')}</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 144,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => <SvgPaperIcon />,
                  title: 'รายละเอียด',
                  disabled: !isAllowed(
                    permissions,
                    'accounting-finance.invoice.list'
                  ),
                  onAction: () => {
                    router.push(`/accounting-finance/invoice/${params.row.id}`);
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];

  return (
    <>
      <ProductNav title="ใบแจ้งหนี้" showBorderBottom showAvatar>
        <ActionGroupStyle>
          <AppDateRange
            data={{
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            handleChange={(dateRange: any) => {
              setFilters({
                ...filters,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              });
            }}
          />
          <ModalCreateInvoice
            handleReloadList={async () => {
              await getList(`${router.query.status}`);
            }}
          >
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddCircle />}
              text="สร้างรายการ"
              borderRadius={'8px'}
              disabled={
                !isAllowed(permissions, 'accounting-finance.invoice.create')
              }
            />
          </ModalCreateInvoice>
        </ActionGroupStyle>
      </ProductNav>
      {isAllowed(permissions, 'accounting-finance.invoice.list') ? (
        <AppContentStyle>
          <ScrollBarStyled ref={scrollBarRef}>
            <FilterWrapStyled>
              <div
                style={{
                  minWidth: '718px',
                }}
              >
                <Tabs
                  value={status}
                  onChange={handleChangeStatus}
                  variant="standard"
                >
                  {invoiceStatus.map(
                    (item: InvoiceStatusType, index: number) => (
                      <Tab
                        key={index}
                        label={
                          <div
                            className="flex items-center"
                            style={{
                              columnGap: '16px',
                              minHeight: '32px',
                            }}
                          >
                            {item.name}
                            <Badge
                              badgeContent={numberWithCommas(item.count) || '0'}
                              color="secondary"
                              sx={{
                                '.MuiBadge-badge': {
                                  backgroundColor:
                                    status !== item.name
                                      ? '#78909C'
                                      : '#263238',
                                },
                              }}
                            />
                          </div>
                        }
                        value={item.name}
                        sx={{
                          color: status !== item.name ? '#78909C' : '',
                        }}
                      />
                    )
                  )}
                </Tabs>
              </div>
            </FilterWrapStyled>
            <div className="flex items-center justify-between py-[8px] px-[16px]">
              <div>{rows.length || 0} รายการ</div>
              <SearchInput
                makeSearchValue={(newValue) =>
                  setFilters({
                    ...filters,
                    searchName: newValue,
                    page: 0,
                  })
                }
              />
            </div>
          </ScrollBarStyled>
          <div className="content-wrap">
            <AppTableStyle $rows={rows}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  <HeaderColumnAction text="จัดการ" width={144} />
                  <DataGrid
                    hideFooter={true}
                    rows={rows}
                    columns={columns}
                    paginationMode="server"
                    rowCount={totalElements}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    loading={loading}
                    components={{
                      NoRowsOverlay: TableNoRowsOverlay,
                      LoadingOverlay: TableLoadingOverlay,
                    }}
                  />
                </ScrollBarStyled>
              </div>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={totalElements || 0}
                  handleChangeFilters={(newValues: any) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </AppTableStyle>
          </div>
        </AppContentStyle>
      ) : (
        <NotPermission />
      )}
    </>
  );
};

InvoiceDataListPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { query, req, res } = context;
  const { status } = query;
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
        status: status || 'แบบร่าง',
      },
    };
  }
  return {
    props: {},
  };
};
export default InvoiceDataListPage;
