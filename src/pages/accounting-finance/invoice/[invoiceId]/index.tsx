import React, { ReactElement, useEffect, useRef } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import {
  ActionGroupStyle,
  AppContentStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import InvoiceNav from '@/components/accounting-finance/invoice/InvoiceNav';
import { useRouter } from 'next/router';
import ProductNav from '@/components/product/ProductNav';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import InvoiceHeaderDetails from '@/components/accounting-finance/invoice/InvoiceHeaderDetails';
import InvoicePaymentDetails from '@/components/accounting-finance/invoice/InvoicePaymentDetails';
import InvoiceListDetails from '@/components/accounting-finance/invoice/InvoiceListDetails';
import InvoicePaymentTotal from '@/components/accounting-finance/invoice/InvoicePaymentTotal';
import { isEmpty, isNull } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { getInvoice, invoiceSelector } from '@/store/features/invoice';
import InvoiceHeaderStatus from '@/components/accounting-finance/invoice/InvoiceHeaderStatus';
import InvoiceCustomerDetails from '@/components/accounting-finance/invoice/InvoiceCustomerDetails';
import InvoiceStatusChip from '@/styles/styledComponents/InvoiceStatusChip.styled';
import WatchLaterRoundedIcon from '@mui/icons-material/WatchLaterRounded';
import Image from 'next/image';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';
import { GetServerSideProps } from 'next';
import { getCookie } from 'cookies-next';
import apiUser from '@/services/core/user';
import { setPermission } from '@/store/features/permission/actions';

export const InvoiceListDetailStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 40px 40px 40px;
  margin-top: 8px;
  border-top: 1px solid #dbe2e5;
`;

const InvoiceDataListPage = ({ authorities }: { authorities: string[] }) => {
  const router = useRouter();
  const { invoiceId } = router.query;
  const dispatch = useAppDispatch();
  // const { permissions } = useAppSelector(permissionSelector);

  useEffect(() => {
    if (authorities) {
      dispatch(setPermission(authorities));
    }
  }, [authorities]);

  const scrollBarRef = useRef<HTMLDivElement>(null);
  const { invoice } = useAppSelector(invoiceSelector);

  useEffect(() => {
    fetchInvoice().then();
  }, [invoiceId]);

  const fetchInvoice = async () => {
    await dispatch(getInvoice(Number(invoiceId)));
  };

  return (
    <>
      {!isNull(invoice) && (
        <>
          {invoice.invoicesStatus.id !== 1 ? (
            <InvoiceNav
              title={invoice.invoiceNo}
              backUrl={`/accounting-finance/invoice?status=${invoice.invoicesStatus.name}`}
            >
              <ActionGroupStyle>
                <InvoiceStatusChip $status={invoice.invoicesStatus.id}>
                  {invoice.invoicesStatus.id <= 3 && (
                    <WatchLaterRoundedIcon sx={{ width: 24, height: 24 }} />
                  )}
                  {invoice.invoicesStatus.id === 4 && (
                    <CheckCircleRoundedIcon />
                  )}
                  {invoice.invoicesStatus.id === 5 && (
                    <Image
                      src={'/icons/icon-delete-history.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  )}
                  {invoice.invoicesStatus.id === 6 && <CancelRoundedIcon />}
                  {invoice.invoicesStatus.id === 7 && (
                    <Image
                      src={'/icons/icon-cancel-pr-button.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  )}
                  {invoice.invoicesStatus.name}
                </InvoiceStatusChip>
              </ActionGroupStyle>
            </InvoiceNav>
          ) : (
            <>
              <ProductNav
                title="สร้างใบแจ้งหนี้"
                centerTitle={true}
                showBorderBottom={true}
                backUrl="/accounting-finance/invoice"
              >
                <InfoOutlinedIcon />
              </ProductNav>
              <InvoiceHeaderStatus />
            </>
          )}
          {invoice.invoicesStatus.id >= 2 && <InvoiceHeaderDetails />}
          {invoice.invoicesStatus.id >= 3 && invoice.invoicesStatus.id <= 5 && (
            <InvoicePaymentDetails />
          )}
          <AppContentStyle>
            <ScrollBarStyled ref={scrollBarRef}>
              <InvoiceListDetailStyled>
                {invoice.invoicesStatus.id === 1 && <InvoiceCustomerDetails />}
                <InvoiceListDetails />
                <InvoicePaymentTotal />
              </InvoiceListDetailStyled>
            </ScrollBarStyled>
          </AppContentStyle>
        </>
      )}
    </>
  );
};

InvoiceDataListPage.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const token = getCookie('access_token', { req, res });
  if (!isEmpty(token)) {
    const response = await apiUser.getProfile(token);
    return {
      props: {
        authorities: response.data.authorities,
      },
    };
  }
  return {
    props: {},
  };
};

export default InvoiceDataListPage;
