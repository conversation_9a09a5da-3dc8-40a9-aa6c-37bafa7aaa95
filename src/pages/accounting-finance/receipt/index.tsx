import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import apiInvoice from '@/services/order/invoice';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableTools from '@/components/global/TableTools';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import PaymentNav from '@/components/accounting-finance/receipt/PaymentNav';
import AppPagination from '@/components/global/AppPagination';
import dayjs from 'dayjs';
import Image from 'next/image';
import styled from 'styled-components';
import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import { numberWithCommas } from '@/utils/number';

const ReceiptStyles = styled.div`
  .content-wrap {
    min-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
`;
const Receipt = () => {
  const router = useRouter();
  const [filters, setFilters] = useState<any>({
    size: 10,
    page: 0,
    search: '',
    startDate: null,
    endDate: null,
  });
  const [rows, setRows] = useState<any>([]);
  const [totalElements] = useState(0);
  const columns: GridColDef[] = [
    {
      field: 'receiptNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
    },
    {
      field: 'paymentInvoiceNo',
      headerName: 'ใบแจ้งหนี้ (อ้างอิง)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
    },
    {
      field: 'customer',
      headerName: 'ลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              className={'rounded-full'}
              src={
                params.row.customer?.imageUrl ||
                '/images/product/empty-product.svg'
              }
              alt={'img customer'}
              width={30}
              height={30}
            />
            <div>{params.row.customer.name}</div>
          </div>
        );
      },
    },
    {
      field: 'quantityInvoice',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.7,
    },
    {
      field: 'totalPrice',
      headerName: 'ยอดรวมสินค้า (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.totalPrice, 2);
      },
    },
    {
      field: 'admin',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              className={'rounded-full'}
              src={
                params.row.admin?.imageUrl ||
                '/images/product/empty-product.svg'
              }
              alt={'img admin'}
              width={30}
              height={30}
            />
            <div>{params.row.admin.name}</div>
          </div>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'กำหนดรับชำระ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div>
            {dayjs(params.row.createdDate).format('DD/MM/YYYY HH:mm')} น.
          </div>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 128,
      flex: 0.5,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <Button
            className={'w-full'}
            variant={'outlined'}
            onClick={() =>
              router.push(`/accounting-finance/receipt/${params.row.id}`)
            }
          >
            รายละเอียด
          </Button>
        );
      },
    },
  ];
  const getPaymentReceiptPage = async (params: any) => {
    const res = await apiInvoice.getPaymentReceiptPage(params);
    if (res && !res.isError) {
      setRows(res.data.content);
    }
  };
  useEffect(() => {
    getPaymentReceiptPage(filters);
  }, [filters]);
  return (
    <>
      <PaymentNav
        title={'ใบเสร็จรับเงิน'}
        filters={filters}
        setFilters={(data: any) => setFilters({ ...data })}
      />
      <ReceiptStyles>
        <AppTableStyle $rows={rows}>
          <div className="content-wrap">
            <div>
              <TableTools
                tools={['search']}
                title={`${rows.length || 0} รายการ`}
                makeNewFilter={(newFilter: any) => {
                  setFilters({ ...newFilter, ...filters });
                }}
              />
              <ScrollBarStyled>
                <HeaderColumnAction text="รายละเอียด" width={100} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements || 0}
                  // pageSize={filters.size}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
            <div className="box-pagination px-[16px]">
              <AppPagination
                filters={filters}
                totalElements={totalElements || 0}
                handleChangeFilters={(newValues: any) => {
                  setFilters(newValues);
                }}
              />
            </div>
          </div>
        </AppTableStyle>
      </ReceiptStyles>
    </>
  );
};
Receipt.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Receipt;
