import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import styled from 'styled-components';
import { ArrowBack } from '@mui/icons-material';
import QuotationRenderStatus from '@/components/accounting-finance/quotation/QuotationStatus';
import Image from 'next/image';
import SvgDescriptionIcon from '@/components/svg-icon/SvgDescriptionIcon';
import SvgExportNoteIcon from '@/components/svg-icon/SvgExportNoteIcon';
import SvgCalendarIcon from '@/components/svg-icon/SvgCalendarIcon';
import moment from 'moment/moment';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { numberWithCommas } from '@/utils/number';
import { useRouter } from 'next/router';
import apiInvoice from '@/services/order/invoice';
import KebabTable from '@/components/KebabTable';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import { isEmpty } from 'lodash';
import ActionButton from '@/components/ActionButton';
import apiExportPdf from '@/services/order/export-pdf';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const Index = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;
  const [data, setData] = useState<any>();
  const [loadingExport, setLoadingExport] = useState<boolean>(false);
  const getPaymentReceiptById = async (id: number) => {
    const res = await apiInvoice.getPaymentReceiptById(id);
    if (!res.isError) {
      setData(res.data);
    }
  };
  useEffect(() => {
    if (id) getPaymentReceiptById(Number(id));
  }, [id]);

  const handleDownload = async () => {
    setLoadingExport(true);
    const res = await apiExportPdf.exportReceipt(Number(id));
    if (res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Download failed',
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    setLoadingExport(false);
  };
  return (
    <Container>
      {/* <ModalQuotationConfirm */}
      {/*  open={confirmModal.open} */}
      {/*  handleCloseModal={onCloseConfirmModal} */}
      {/*  data={confirmModal} */}
      {/*  loading={confirmLoading} */}
      {/* /> */}
      <div className={'flex flex-col gap-[8px]'}>
        <QuotationHeader>
          <div className={'header'}>
            <div className={'header-info'}>
              <ArrowBack
                className={'cursor-pointer'}
                onClick={() => router.push('/accounting-finance/receipt')}
              />
              <span className={'quotation-id'}>{data?.receiptNo}</span>
              <QuotationRenderStatus status={data?.status || 'status'} />
            </div>
            <div className={'header-action'}>
              <ActionButton
                variant="outlined"
                color="blueGrey"
                text="ดาวน์โหลดไฟล์"
                borderRadius={'8px'}
                onClick={handleDownload}
                disabled={loadingExport}
              />
              <KebabTable
                item={''}
                isEdit={{ status: true }}
                isDelete={{ status: true }}
                iconHorizonDot={<MoreHorizRoundedIcon />}
              />
            </div>
          </div>
          <div className={'quotation-info'}>
            <div className={'customer-info'}>
              <div className={'top'}>
                <div className={'detail-item user-display'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        data?.detail?.customerData?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>
                      {data?.createUser?.name || '-'}
                    </div>
                    <div className={'info'}>ผู้สร้าง</div>
                  </div>
                </div>
                <div className={'detail-item user-display'}>
                  <div className={'avatar'}>
                    <Image
                      src={
                        data?.detail?.customerData?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'label'}>
                    <div className={'name'}>-</div>
                    <div className={'info'}>ลูกค้า</div>
                  </div>
                </div>
              </div>
              <div className={'bottom'}>
                <div className={'detail-item b label-value'}>
                  <div className={'title'}>
                    <SvgDescriptionIcon />
                    <span>รายการใบแจ้งหนี้</span>
                  </div>
                  <div className={'value'}>{data?.invoiceNo || '-'}</div>
                </div>
                <div className={'detail-item c label-value'}>
                  <div className={'title'}>
                    <SvgExportNoteIcon />
                    <span>รายการใบเสนอราคา</span>
                  </div>
                  <div className={'value'}>{data?.quotationNo || '-'}</div>
                </div>
                <div className={'detail-item d label-value'}>
                  <div className={'title'}>
                    <SvgDescriptionIcon />
                    <span>รายการสั่งผลิต</span>
                  </div>
                  <div className={'value'}>{data?.layOrderNo || '-'}</div>
                </div>
              </div>
              {/* <div className={'detail-item user-display a'}> */}
              {/*  <div className={'avatar'}> */}
              {/*    <Image */}
              {/*      src={ */}
              {/*        data?.detail?.customerData?.imageUrl || */}
              {/*        '/images/product/empty-product.svg' */}
              {/*      } */}
              {/*      width={40} */}
              {/*      height={40} */}
              {/*      alt="" */}
              {/*      style={{ */}
              {/*        borderRadius: '50%', */}
              {/*        objectFit: 'cover', */}
              {/*      }} */}
              {/*    /> */}
              {/*  </div> */}
              {/*  <div className={'label'}> */}
              {/*    <div className={'name'}>-</div> */}
              {/*    <div className={'info'}>ผู้สร้าง</div> */}
              {/*  </div> */}
              {/* </div> */}
              {/* <div className={'detail-item user-display aa'}> */}
              {/*  <div className={'avatar'}> */}
              {/*    <Image */}
              {/*      src={ */}
              {/*        data?.detail?.customerData?.imageUrl || */}
              {/*        '/images/product/empty-product.svg' */}
              {/*      } */}
              {/*      width={40} */}
              {/*      height={40} */}
              {/*      alt="" */}
              {/*      style={{ */}
              {/*        borderRadius: '50%', */}
              {/*        objectFit: 'cover', */}
              {/*      }} */}
              {/*    /> */}
              {/*  </div> */}
              {/*  <div className={'label'}> */}
              {/*    <div className={'name'}>-</div> */}
              {/*    <div className={'info'}>ลูกค้า</div> */}
              {/*  </div> */}
              {/* </div> */}
              {/* <div className={'detail-item b label-value'}> */}
              {/*  <div className={'title'}> */}
              {/*    <SvgDescriptionIcon /> */}
              {/*    <span>รายการใบแจ้งหนี้</span> */}
              {/*  </div> */}
              {/*  <div className={'value'}>{data?.invoiceNo}</div> */}
              {/* </div> */}
              {/* <div className={'detail-item c label-value'}> */}
              {/*  <div className={'title'}> */}
              {/*    <SvgExportNoteIcon /> */}
              {/*    <span>รายการใบเสนอราคา</span> */}
              {/*  </div> */}
              {/*  <div className={'value'}>{data?.quotationNo}</div> */}
              {/* </div> */}
              {/* <div className={'detail-item d label-value'}> */}
              {/*  <div className={'title'}> */}
              {/*    <SvgDescriptionIcon /> */}
              {/*    <span>รายการสั่งผลิต</span> */}
              {/*  </div> */}
              {/*  <div className={'value'}>{data?.layOrderNo}</div> */}
              {/* </div> */}
            </div>
            <div className={'quotation-detail'}>
              <div className={'detail-item a'}>
                <span className={'detail'}>
                  <span className={'bold'}>หมายเหตุ</span>
                  {data?.descriptionQT ||
                    'ราคานี้ยังไม่รวมภาษีมูลค่าเพิ่ม 7% เงื่อนไขการเริ่มงาน / \n' +
                      '                   การชำระเงิน มัดจำค่าสินค้า 80 % ก่อนเริ่มงาน ส่วนที่เหลือ \n' +
                      '                   ชำระก่อนจัดส่ง ระยะเวลาผลิต หลังมัดจำ และ \n' +
                      '                   ยืนยันตัวอย่างสินค้าเข้าผลิตประมาณ 12-15 วันทำการ \n' +
                      '                   กรณียอดไม่ถึง 10000 บาท ชำระเต็ม100 % ก่อนเริ่มงาน \n' +
                      '                   (ระยะเวลาขึ้นอยู่กับ จำนวนและสเปคงานค่ะ) - ยอดเกิน 15,000 บาท \n' +
                      '                   การจัดส่ง ฟรี กทม.// ตจว. ขนส่งเก็บปลายทาง (หมายเหตุ : \n' +
                      '                   ทางบริษัทฯ ขอสงวนสิทธิ์ในการขอคืนเงินมัดจำทุกกรณี) '}
                </span>
              </div>
              <div className={'detail-item b label-value'}>
                <div className={'title'}>
                  <SvgCalendarIcon />
                  <span>วันที่สร้างใบเสร็จรับเงิน</span>
                </div>
                <div className={'value'}>
                  {data?.createdDate
                    ? moment(data?.createdDate).format('DD/MM/YYYY, HH:mm น.')
                    : '-'}
                </div>
              </div>
              <div className={'detail-item c label-value'}>
                <div className={'title'}>
                  <SvgCalendarIcon />
                  <span>วันที่ครบกำหนด</span>
                </div>
                <div className={'value'}>
                  {data?.dueDate
                    ? moment(data?.dueDate).format('DD/MM/YYYY, HH:mm น.')
                    : '-'}
                </div>
              </div>
            </div>
          </div>
        </QuotationHeader>
        <QuotationContent>
          <div className="table-container">
            <div className="section-title">รายการเสนอราคา</div>
            <TableWrapper>
              <TableContainer
                component={Paper}
                elevation={0}
                sx={{
                  '& .MuiTable-root': {
                    borderCollapse: 'separate',
                    borderSpacing: '0px',
                  },
                  '& .MuiTableHead-root .MuiTableRow-root': {
                    height: '32px',
                  },
                  '& .MuiTableHead-root .MuiTableCell-root': {
                    padding: '4px 16px',
                    fontSize: '12px',
                  },
                  '& .MuiTableBody-root .MuiTableRow-root': {
                    height: '72px',
                  },
                }}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell align="center" sx={{ width: '50px' }}>
                        #
                      </TableCell>
                      <TableCell>รายการ</TableCell>
                      <TableCell>รายการสั่งผลิต (อ้างอิง)</TableCell>
                      <TableCell align="center" sx={{ width: '120px' }}>
                        จำนวน
                      </TableCell>
                      <TableCell align="center" sx={{ width: '120px' }}>
                        ราคา/หน่วย
                      </TableCell>
                      <TableCell align="right" sx={{ width: '120px' }}>
                        ราคาสินค้า (บาท)
                      </TableCell>
                      <TableCell align="right" sx={{ width: '120px' }}>
                        ส่วนลด
                      </TableCell>
                      <TableCell align="right" sx={{ width: '120px' }}>
                        ราคารวม
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {!isEmpty(data?.receiptItem) ? (
                      data?.receiptItem.map((field: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell align="center">{index + 1}</TableCell>
                          <TableCell>
                            <div>{field?.name || '-'}</div>
                          </TableCell>
                          <TableCell>
                            <div style={{ fontSize: '0.85em', color: '#666' }}>
                              {`${field.layDataNo ? field.layDataNo : '-'}`}
                            </div>
                          </TableCell>
                          <TableCell align="center">
                            <div>{numberWithCommas(field.quantity, 2)}</div>
                          </TableCell>
                          <TableCell align="center">
                            <div>{numberWithCommas(field.priceUnit, 2)}</div>
                          </TableCell>
                          <TableCell align="right">
                            <div>
                              {numberWithCommas(
                                field.price + field.discount,
                                2
                              )}
                            </div>
                          </TableCell>
                          <TableCell align="right">
                            <div>{numberWithCommas(field.discount, 2)}</div>
                          </TableCell>
                          <TableCell align="right">
                            <div>
                              {numberWithCommas(
                                field.priceUnit * field.quantity,
                                2
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          align="center"
                          colSpan={9}
                          sx={{ color: '#dbe2e5' }}
                        >
                          ไม่มีรายการเสนอราคา
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TableWrapper>
          </div>
          <div className={'summary-container'}>
            <div></div>
            <div className="qt-summary">
              <div className="summary-content">
                <div className="summary-row border">
                  <span className={'label'}>ยอดรวม</span>
                  <span className={'result'}>{`${numberWithCommas(
                    data?.netPrice,
                    2
                  )} บาท`}</span>
                </div>
                <div className="summary-row">
                  <span className={'label'}>{`ส่วนลด ${numberWithCommas(
                    data?.discountRate,
                    2
                  )}%`}</span>
                  <div className="flex items-center gap-2">
                    <span className={'result'}>{`${numberWithCommas(
                      data?.discount,
                      2
                    )} บาท`}</span>
                  </div>
                </div>
                <div className="summary-row">
                  <span
                    className={'label'}
                  >{`ภาษีมูลค่าเพิ่ม ${numberWithCommas(
                    data?.vatRate,
                    2
                  )}%`}</span>
                  <span className={'result'}>{`${numberWithCommas(
                    data?.vatAmount,
                    2
                  )} บาท`}</span>
                </div>
                <div className="summary-row final">
                  <span className={'label large bold'}>มูลค่ารวมสุทธิ</span>
                  <span className={'result large'}>{`${numberWithCommas(
                    data?.totalPrice,
                    2
                  )} บาท`}</span>
                </div>
              </div>
            </div>
          </div>
        </QuotationContent>
      </div>
    </Container>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Index;

const Container = styled.div`
  background: #f5f7f8;
`;
const QuotationHeader = styled.div`
  background: #fff;
  border-bottom: 1px solid #dbe2e5;
  padding: 40px;
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-info {
      display: flex;
      align-items: center;
      column-gap: 16px;
      .quotation-id {
        color: #263238;
        font-size: 40px;
        font-weight: 600;
        letter-spacing: 0.4px;
      }
    }
    .header-action {
      display: flex;
      align-items: center;
      column-gap: 16px;
    }
  }
  .quotation-info {
    display: grid;
    grid-template-columns: 2fr 2fr;
    column-gap: 40px;
    .user-display {
      .avatar {
        display: flex;
        align-items: center;
      }
      .label {
        .name {
          font-size: 14px;
          font-weight: 600;
        }
        .info {
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
    .customer-info {
      display: grid;
      grid-template-rows: auto auto;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item {
        padding: 1.5rem;
      }
      .top {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        border-bottom: 1px solid #dbe2e5;
        .user-display {
          display: flex;
          align-items: center;
          gap: 1rem;
          border-right: 1px solid #dbe2e5;
          &:last-child {
            border-right: none;
          }
        }
      }
      .bottom {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        .detail-item {
          border-right: 1px solid #dbe2e5;
          &:last-child {
            border-right: none;
          }
        }
      }
      //.detail-item.a {
      //  grid-column: span 2;
      //  display: flex;
      //  align-items: center;
      //  column-gap: 16px;
      //  padding: 24px;
      //  border-bottom: 1px solid #dbe2e5;
      //}
      //.detail-item.aa {
      //  grid-column: span 1;
      //  display: flex;
      //  align-items: center;
      //  column-gap: 16px;
      //  padding: 24px;
      //  border-left: 1px solid #dbe2e5;
      //  border-bottom: 1px solid #dbe2e5;
      //}
      //.detail-item.b {
      //  grid-row-start: 2;
      //  border-right: 1px solid #dbe2e5;
      //}
      //.detail-item.c {
      //  grid-row-start: 2;
      //  border-right: 1px solid #dbe2e5;
      //}
      //.detail-item.d {
      //  grid-row-start: 2;
      //}
    }
    .label-value {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 24px;
      row-gap: 8px;
      .title {
        display: flex;
        column-gap: 8px;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
      }
      .value {
        font-size: 14px;
        font-weight: 600;
      }
    }
    .quotation-detail {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      .detail-item.a {
        grid-column: span 3 / span 3;
        color: #263238;
        font-size: 12px;
        font-weight: 400;
        line-height: normal;
        padding: 24px;
        border-bottom: 1px solid #dbe2e5;
        .bold {
          font-weight: 600;
          margin-right: 8px;
          text-decoration: underline;
        }
        .detail {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }

      .detail-item.b {
        grid-row-start: 2;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.c {
        grid-row-start: 2;
        border-right: 1px solid #dbe2e5;
      }
      .detail-item.d {
        grid-row-start: 2;
        display: flex;
        align-items: center;
        column-gap: 16px;
        padding: 0 24px;
      }
    }
  }
`;
const QuotationContent = styled.div`
  border-top: 1px solid #dbe2e5;
  background: #fff;
  padding: 40px;
  .table-container {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    .section-title {
      color: #263238;
      font-size: 22px;
      font-weight: 600;
      line-height: 1;
    }
  }
  .summary-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 24px 0;
    .qt-summary {
      display: flex;
      justify-content: flex-end;
      .summary-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        .actions {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 24px;
          button {
            font-weight: 600;
            font-size: 14px;
          }
        }
      }

      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        .label {
          color: #263238;
          font-size: 16px;
          font-weight: 400;
        }
        .result {
          color: #263238;
          font-size: 16px;
          font-weight: 600;
        }
        .bold {
          font-weight: 600;
        }
        .large {
          font-size: 22px;
        }
      }
      .summary-row.border {
        border-bottom: 1px solid #dbe2e5;
        padding-bottom: 16px;
      }
      .summary-row.final {
        padding: 16px 0;
        border-top: 2px solid #263238;
        border-bottom: 2px solid #263238;
      }
    }
  }
`;

const TableWrapper = styled.div`
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #dbe2e5;
    border-radius: 16px !important;
  }
  .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root {
    border-bottom: none;
  }
`;
