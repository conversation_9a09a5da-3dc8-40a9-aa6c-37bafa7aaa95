import React, { ReactElement, useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import PaymentNav from '@/components/accounting-finance/receipt/PaymentNav';
import PaymentDataTable from '@/components/accounting-finance/receipt/PaymentDataTable';
import ModalCreateProductPeak from '@/components/accounting-finance/receipt/Modal/ModalCreateProductPeak';
import apiProductPeak from '@/services/stock/product-peak';
import { isEmpty } from 'lodash';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const Index = () => {
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<any>();
  const [dataEdit, setDataEdit] = useState<any>();
  const handleClose = () => {
    setOpen(false);
    setDataEdit({});
  };
  const getAllPeakProduct = async () => {
    const res = await apiProductPeak.getAllPeakProduct();
    if (res && !res.isError) {
      setData(res.data);
    }
  };
  const createPeakProduct = async (data: {
    name: string;
    description: string;
  }) => {
    const res = await apiProductPeak.createPeakProduct(data);
    if (res && !res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มข้อมูล Peak product สำเร็จ',
          severity: 'success',
        })
      );
      await getAllPeakProduct();
    }
  };
  const updatePeakProduct = async (data: any) => {
    const res = await apiProductPeak.updatePeakProduct(data);
    if (res && !res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขข้อมูล Peak product สำเร็จ',
          severity: 'success',
        })
      );
      await getAllPeakProduct();
    }
  };
  const deletePeakProduct = async (id: number) => {
    const res = await apiProductPeak.deletePeakProduct(id);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบข้อมูล Peak product สำเร็จ',
          severity: 'success',
        })
      );
      await getAllPeakProduct();
    }
  };
  useEffect(() => {
    getAllPeakProduct();
  }, []);
  return (
    <>
      <PaymentNav title={'รายการสินค้า PEAK'} onAction={() => setOpen(true)} />
      <PaymentDataTable
        data={data}
        handleOpen={() => setOpen(true)}
        onDelete={(id: number) => deletePeakProduct(id)}
        setDataEdit={(data: any) => setDataEdit(data)}
      />
      <ModalCreateProductPeak
        open={open}
        handleClose={() => {
          handleClose();
        }}
        dataEdit={dataEdit}
        onActionPeakProduct={(data: any) =>
          isEmpty(dataEdit) ? createPeakProduct(data) : updatePeakProduct(data)
        }
      />
    </>
  );
};
Index.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default Index;
