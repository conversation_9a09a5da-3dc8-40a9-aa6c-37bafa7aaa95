import React, {
  ReactElement,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import {
  Breadcrumbs,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
  BreadcrumbsAndButtonStyled,
  FormModalStyle,
} from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { FiltersType } from '@/types/app';
import apiCompany from '@/services/core/company';
import AppPagination from '@/components/global/AppPagination';
import { ContentItem, ListType } from '@/types/user';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';

type openCreateModalType = {
  status: boolean;
  type: string;
};
const ItemDetail = () => {
  const { user } = useAppSelector(userSelector);
  const [loading] = useState<boolean>(false);
  const [selectedValue, setSelectedValue] = useState('');
  const [openCreateModal, setOpenCreateModal] = useState<openCreateModalType>({
    status: false,
    type: 'create',
  });
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });
  const [fullSizeList] = useState([
    {
      id: 1,
      name: '24 x 35 in',
    },
    {
      id: 2,
      name: '25 x 36 in',
    },
    {
      id: 3,
      name: '31 x 43 in',
    },
  ]);
  // const [openDelete, setOpenDelete] = useState<OpenDeleteType>({
  //   status: false,
  //   name: '',
  //   id: null,
  // });
  const [userList, setUserList] = useState<ListType>({
    content: [],
    totalElements: null,
  });
  const wrapperRef = useRef(null);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    clearErrors,
  } = useForm<any>({
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const getMemberCompany = async () => {
    const res = await apiCompany.getMemberCompany({
      ...filters,
      name: filters.searchTerm || null,
    });
    if (!res.isError) {
      const addId = res.content.map((item: ContentItem, index: number) => {
        return {
          ...item,
          id: index + 1,
        };
      });
      const newData = {
        ...res,
        content: addId,
      };
      setUserList(newData);
    }
  };

  useEffect(() => {
    getMemberCompany();
  }, [filters, user]);

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อจริง นามสกุล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 172,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.name || '-'}</div>;
      },
    },
    {
      field: 'roleName',
      headerName: 'ตำแหน่ง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 134,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'email',
      headerName: 'อีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 224,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="flex items-center">
            <IconButton
              disabled={!params.row.inviteVerified}
              onClick={() => {
                onEdit(params.row);
              }}
            >
              <Image src={'/icons/edit.svg'} width={24} height={24} alt="" />
            </IconButton>
            <IconButton
              onClick={() => {
                // setOpenDelete({
                //   status: true,
                //   name: params.row.name,
                //   id: params.row.companyUserId,
                // });
              }}
            >
              <Image src={'/icons/delete.svg'} width={24} height={24} alt="" />
            </IconButton>
          </div>
        );
      },
    },
  ];

  // const deleteMember = async () => {
  //   setLoading(true);
  //   if (openDelete.id) {
  //     const res = await apiCompany.deleteMemberCompany(openDelete.id);
  //     if (res.status) {
  //       setOpenSnackBar({
  //         status: true,
  //         text: `ลบ ${openDelete.name} สำเร็จ`,
  //         severity: 'success',
  //       });
  //       getMemberCompany();
  //     } else {
  //       setOpenSnackBar({
  //         status: true,
  //         text: `${res.message}`,
  //         severity: 'error',
  //       });
  //     }
  //   }
  //   setOpenDelete({
  //     ...openDelete,
  //     status: false,
  //     id: null,
  //   });
  //   setLoading(false);
  // };

  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = [
            'MuiBackdrop-root',
            'MuiSelect-select',
            'select-text',
            'MuiMenu-list',
            'MuiButtonBase-root',
          ];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (!checkValue) {
            setOpenCreateModal({
              status: false,
              type: 'create',
            });
          }
        }
      }

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  const onOpen = () => {
    reset();
    setOpenCreateModal({
      status: true,
      type: 'create',
    });
  };
  const onEdit = (_data: any) => {
    setOpenCreateModal({
      status: true,
      type: 'edit',
    });
  };
  const onClose = () => {
    setOpenCreateModal({
      ...openCreateModal,
      status: false,
    });
  };

  const onSubmitModal = async (_values: any, _type: string) => {
    // setLoading(true);
    // if (type === 'create') {
    //   const res = await apiProduct.addCategory(values);
    //   if (!res.isError) {
    //     setOpenSnackBar({
    //       status: true,
    //       text: 'บันทึกสำเร็จ',
    //       severity: 'success',
    //     });
    //     setOpenCreateModal({
    //       ...openCreateModal,
    //       status: false,
    //     });
    //   } else {
    //     setOpenSnackBar({
    //       status: true,
    //       text: 'เกิดข้อผิดพลาด',
    //       severity: 'error',
    //     });
    //   }
    // } else if (type === 'edit') {
    //   const res = await apiProduct.addCategory(values);
    //   if (!res.isError) {
    //     setOpenSnackBar({
    //       status: true,
    //       text: 'บันทึกสำเร็จ',
    //       severity: 'success',
    //     });
    //     setOpenCreateModal({
    //       ...openCreateModal,
    //       status: false,
    //     });
    //   } else {
    //     setOpenSnackBar({
    //       status: true,
    //       text: 'เกิดข้อผิดพลาด',
    //       severity: 'error',
    //     });
    //   }
    // }
    // setLoading(false);
  };

  // useEffect(() => {
  //   if (id) {
  //     setValue('firstName', user.firstName);
  //     setValue('lastName', user.lastName);
  //     setValue('email', user.email);
  //   }
  // }, [orderId]);
  return (
    <>
      <ProductNav
        title={`XXX`}
        backUrl="/setting/product-costs/items-costs"
        showBorderBottom
      ></ProductNav>
      <BreadcrumbsAndButtonStyled>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/company/setting/sizes"
            className="opacity-40"
          >
            Sizes
          </Link>
          <p>XXX</p>
        </Breadcrumbs>
        <ActionGroupStyle>
          <div
            onClick={() => {
              onOpen();
            }}
          >
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="เพิ่มต้นทุน"
              borderRadius={'20px'}
            />
          </div>
        </ActionGroupStyle>
      </BreadcrumbsAndButtonStyled>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={userList?.content}>
            <div className="content-wrap">
              <DataGrid
                hideFooter={true}
                rows={userList?.content ? userList.content : []}
                columns={columns}
                paginationMode="server"
                rowCount={userList?.totalElements || 0}
                // pageSize={filters.sizes}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={
                    userList?.totalElements ? userList.totalElements : 0
                  }
                  handleChangeFilters={(newValues: FiltersType) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
      <Dialog open={openCreateModal.status}>
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {openCreateModal.type === 'create'
                    ? 'เพิ่มต้นทุนรายการสินค้า'
                    : 'แก้ไขต้นทุนรายการสินค้า'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit((value: any) =>
                    onSubmitModal(value, openCreateModal.type)
                  )}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div>
                    <p className="mt-0">ชื่อรายการสินค้า</p>
                    <TextField disabled value="xxx" />
                  </div>
                  <div>
                    <p>ความหนา</p>
                    <TextField
                      disabled
                      value="xxx"
                      InputProps={{
                        endAdornment: <div className="p-[2px]">แกรม</div>,
                      }}
                    />
                  </div>
                  <div>
                    <p>จำนวนหน้า</p>
                    <TextField disabled value="xxx" />
                  </div>
                  <div>
                    <p>ราคาต่อกิโลกรัม</p>
                    <TextField
                      type="number"
                      placeholder="กรอก"
                      {...register('pricePerKilogram', {
                        required: 'กรุณากรอก',
                      })}
                      InputProps={{
                        endAdornment: <div className="p-[2px]">บาท</div>,
                      }}
                      error={Boolean(errors.pricePerKilogram)}
                      helperText={errors.pricePerKilogram?.message as ReactNode}
                    />
                  </div>
                  <div>
                    <p>ขนาดเต็มใบ</p>
                    <FormControl sx={{ width: '100%' }}>
                      <Select
                        sx={{
                          height: '40px',
                          border: '0',
                        }}
                        displayEmpty
                        {...register('fullSize', {
                          required: 'กรุณาเลือก',
                        })}
                        error={Boolean(errors.fullSize)}
                        input={<OutlinedInput />}
                        renderValue={(selected) => {
                          if (!selected) {
                            return (
                              <div className="text-[14px] text-[#B0BEC5]">
                                กรุณาเลือก
                              </div>
                            );
                          }
                          const selectedItem = fullSizeList.find(
                            (item: any) => item.id === selected
                          );
                          return selectedItem ? selectedItem.name : '';
                        }}
                        value={selectedValue}
                        onChange={(e) => {
                          if (e.target.value) {
                            setSelectedValue(e.target.value);
                            clearErrors('fullSize');
                          }
                        }}
                      >
                        {/* <MenuItem disabled value=""> */}
                        {/*  <em>-- Please select --</em> */}
                        {/* </MenuItem> */}
                        {fullSizeList.map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.fullSize && (
                        <FormHelperText error>
                          {errors.fullSize.message as ReactNode}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </div>

                  <div>
                    <p>ราคาต้นทุน/รีม</p>
                    <TextField
                      type="number"
                      placeholder="กรอก"
                      {...register('costPrice', {
                        required: 'กรุณากรอก',
                      })}
                      InputProps={{
                        endAdornment: <div className="p-[2px]">บาท</div>,
                      }}
                      error={Boolean(errors.costPrice)}
                      helperText={errors.costPrice?.message as ReactNode}
                    />
                    <div
                      style={{
                        fontSize: '14px',
                        color: '#B0BEC5',
                        marginTop: '8px',
                      }}
                    >
                      *ราคาต่อรีม = ความหนา (แกรม) x ( ขนาดใบเต็ม A X B) / 3100
                      x ราคาต่อกิโล
                    </div>
                  </div>
                  <div>
                    <p>กำไร</p>
                    <TextField
                      type="number"
                      placeholder="กรอก"
                      {...register('profit', {
                        required: 'กรุณากรอก',
                      })}
                      InputProps={{
                        endAdornment: <div className="p-[2px]">%</div>,
                      }}
                      error={Boolean(errors.profit)}
                      helperText={errors.profit?.message as ReactNode}
                    />
                  </div>
                  <div>
                    <p>ราคาขาย/รีม</p>
                    <TextField
                      type="number"
                      placeholder="กรอก"
                      {...register('sellingPrice', {
                        required: 'กรุณากรอก',
                      })}
                      InputProps={{
                        endAdornment: <div className="p-[2px]">บาท</div>,
                      }}
                      error={Boolean(errors.sellingPrice)}
                      helperText={errors.sellingPrice?.message as ReactNode}
                    />
                  </div>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <LoadingButton
                      type="submit"
                      loading={loading}
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      บันทึก
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      {/* <AppModalConfirm */}
      {/*  open={openDelete.status} */}
      {/*  onClickClose={() => { */}
      {/*    setOpenDelete({ */}
      {/*      ...openDelete, */}
      {/*      status: false, */}
      {/*      id: null, */}
      {/*    }); */}
      {/*  }} */}
      {/*  confirmTitle={`ลบ ${openDelete.name}`} */}
      {/*  confirmDescription={`คุณต้องการที่จะลบ ${openDelete.name}`} */}
      {/*  loadingConfirm={loading} */}
      {/*  onConfirm={() => { */}
      {/*    deleteMember(); */}
      {/*  }} */}
      {/* /> */}
    </>
  );
};
ItemDetail.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ItemDetail;
