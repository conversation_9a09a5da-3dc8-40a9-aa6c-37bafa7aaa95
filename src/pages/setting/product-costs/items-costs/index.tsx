import React, { ReactElement, useEffect, useState } from 'react';
import ProductNav from '@/components/product/ProductNav';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@mui/material';
import {
  ActionGroupStyle,
  AppContentStyle,
  AppTableStyle,
} from '@/styles/share.styled';
import SearchInput from '@/components/SearchInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { FiltersType } from '@/types/app';
import apiCompany from '@/services/core/company';
import AppPagination from '@/components/global/AppPagination';
import { ContentItem, ListType } from '@/types/user';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';

const ItemsCosts = () => {
  const router = useRouter();
  const { user } = useAppSelector(userSelector);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
    searchTerm: '',
  });

  const [userList, setUserList] = useState<ListType>({
    content: [],
    totalElements: null,
  });

  const getMemberCompany = async () => {
    const res = await apiCompany.getMemberCompany({
      ...filters,
      name: filters.searchTerm || null,
    });
    if (!res.isError) {
      const addId = res.content.map((item: ContentItem, index: number) => {
        return {
          ...item,
          id: index + 1,
        };
      });
      const newData = {
        ...res,
        content: addId,
      };
      setUserList(newData);
    }
  };
  useEffect(() => {
    getMemberCompany();
  }, [filters, user]);

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'ชื่อจริง นามสกุล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 172,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.name || '-'}</div>;
      },
    },
    {
      field: 'roleName',
      headerName: 'ตำแหน่ง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 134,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'email',
      headerName: 'อีเมล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 224,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 224,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (_params: any) => {
        return (
          <div className="flex items-center">
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '98px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginLeft: '8px',
              }}
              onClick={() => {
                router.push('/setting/product-costs/items-costs/1');
              }}
            >
              กำหนดต้นทุน
            </Button>
          </div>
        );
      },
    },
  ];

  const handleSearch = (searchTerm: string) => {
    setFilters({ ...filters, searchTerm, page: 0 });
  };

  return (
    <>
      <ProductNav title="ต้นทุนวัสดุ" showBorderBottom={false}>
        <ActionGroupStyle>
          <SearchInput
            makeSearchValue={(val: string) => {
              handleSearch(val);
            }}
          />
        </ActionGroupStyle>
      </ProductNav>
      <AppContentStyle>
        <div className="content-wrap">
          <AppTableStyle $rows={userList?.content}>
            <div className="content-wrap">
              <DataGrid
                hideFooter={true}
                rows={userList?.content ? userList.content : []}
                columns={columns}
                paginationMode="server"
                rowCount={userList?.totalElements || 0}
                // pageSize={filters.sizes}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={
                    userList?.totalElements ? userList.totalElements : 0
                  }
                  handleChangeFilters={(newValues: FiltersType) => {
                    setFilters(newValues);
                  }}
                />
              </div>
            </div>
          </AppTableStyle>
        </div>
      </AppContentStyle>
    </>
  );
};
ItemsCosts.getLayout = function getLayout(page: ReactElement) {
  return <MainLayout>{page}</MainLayout>;
};
export default ItemsCosts;
