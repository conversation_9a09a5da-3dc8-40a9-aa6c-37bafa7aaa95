import React, { useEffect, useRef, useState } from 'react';
import {
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useFormik } from 'formik';
import * as yup from 'yup';
import dayjs, { Dayjs } from 'dayjs';
import { LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import utc from 'dayjs/plugin/utc';
import { isEmpty } from 'lodash';
import FilePicker from '@/components/FilePicker';
import styled from 'styled-components';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import 'dayjs/locale/th';

dayjs.extend(utc);
export const LotInfoStyled = styled.div`
  width: 100%;
  display: flex;
  height: 120px;
  border-radius: 8px;
  border: 1px solid #dbe2e5;
  flex-direction: column;
  .raw-material-group {
    flex: 1 1 0%;
    display: flex;
    align-items: center;
    font-weight: 600;
    padding: 0 4px;
    column-gap: 16px;
    border-bottom: 1px solid #dbe2e5;
    .name {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow-wrap: break-word;
    }
  }
  .detail {
    flex: 1 1 0%;
    display: flex;
    .item {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #dbe2e5;
      &:last-child {
        border-right: none;
      }
      .key {
        font-size: 10px;
      }
      .value {
        font-size: 12px;
      }
    }
  }
`;
type ModalImportLotToWareHouseProps = {
  open: boolean;
  handleClose: () => void;
  handleImport: (value: any, file: any) => void;
  submittingImport: boolean;
  data: any;
};

const validationSchema = yup.object({
  quantity: yup.string().required('กรุณาระบุจำนวนนำเข้า'),
});

const ModalImportLotToWareHouse = ({
  open,
  handleClose,
  handleImport,
  submittingImport,
  data,
}: ModalImportLotToWareHouseProps) => {
  const wrapperRef = useRef(null);
  const formik = useFormik({
    initialValues: {
      poOrderListId: null,
      quantity: '',
      expireDate: null,
    },
    validationSchema: validationSchema,
    onSubmit: (values: any) => {
      handleImport(values, file);
    },
  });

  const [dateValue, setDateValue] = React.useState<Dayjs | null>(dayjs());
  const [file, setFile] = useState<any>(null);
  const [errorFileMsg, setErrorFileMsg] = useState<string>('');
  const handleChangeQuantity = (value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');
    formik.setFieldValue(
      'quantity',
      isEmpty(value) ? newValue : Number(newValue)
    );
  };
  const handleChangeDate = (value: Dayjs) => {
    setDateValue(value);
    const utcValue = value.utc().format('YYYY-MM-DD');
    formik.setFieldValue('expireDate', utcValue);
  };

  useEffect(() => {
    if (open || data.rawMaterial.lotExpirationDate) {
      formik.resetForm();
      setFile(null);
      const utcNow = dayjs().utc();
      const dayjsPushOneYear = utcNow.add(1, 'year');
      const utcPlusOneYear = dayjsPushOneYear.format('YYYY-MM-DD');
      setDateValue(dayjsPushOneYear);
      formik.setFieldValue('expireDate', utcPlusOneYear);
      formik.setFieldValue('poOrderListId', data.id);
    }
  }, [open]);
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="th">
      <Dialog open={open}>
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={489}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">นำสินค้าเข้าคลัง</div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form onSubmit={formik.handleSubmit} style={{ rowGap: '0' }}>
                  <LotInfoStyled
                    style={{
                      marginTop: '28px',
                    }}
                  >
                    <div className="raw-material-group">
                      <Image
                        src={
                          data.rawMaterial.imageUrl ||
                          '/images/product/empty-product.svg'
                        }
                        width={50}
                        height={50}
                        alt=""
                      />
                      <div className="name">
                        <span>{`${data.rawMaterial.name} • `}</span>
                        <span>{`${data.rawMaterial.brand.name} `}</span>
                        {data.rawMaterial.itemSize?.name && (
                          <span>{`• ${data.rawMaterial.itemSize.name} `}</span>
                        )}
                        {data.rawMaterial.subMaterialDetail?.side && (
                          <span>
                            {`• ${data.rawMaterial.subMaterialDetail.side} ด้าน`}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="detail">
                      <div className="item">
                        <div className="key">การจัดเก็บ</div>
                        <div className="value">{data.stock.name}</div>
                      </div>
                      <div className="item">
                        <div className="key">จำนวน</div>
                        <div className="value">
                          {numberWithCommas(data.quantity)}{' '}
                          {data.rawMaterial.countComponent.name}
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">ราคารวม</div>
                        <div className="value">
                          {numberWithCommas(data.totalPrice, 2)} บาท
                        </div>
                      </div>
                    </div>
                  </LotInfoStyled>
                  <p>จำนวนนำเข้า</p>
                  <FormControl>
                    <TextField
                      type="number"
                      name="paymentAmount"
                      placeholder="0"
                      value={formik.values.quantity}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        if (Number(e.target.value) < 0) {
                          e.preventDefault();
                        } else {
                          handleChangeQuantity(Number(e.target.value));
                        }
                      }}
                      onKeyDown={(e: any) => {
                        if (e.key === '-') {
                          e.preventDefault();
                        }
                      }}
                      onPaste={(e) => {
                        e.preventDefault();
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            {data.rawMaterial.countComponent.name}
                          </InputAdornment>
                        ),
                      }}
                      error={
                        formik.touched.quantity &&
                        Boolean(formik.errors.quantity)
                      }
                      helperText={
                        formik.touched.quantity && formik.errors.quantity
                      }
                    />
                  </FormControl>

                  {data.rawMaterial.lotExpirationDate && (
                    <>
                      <p>วันหมดอายุ</p>
                      <MobileDatePicker
                        value={dateValue}
                        onChange={(value: any) => {
                          handleChangeDate(value);
                        }}
                      />
                    </>
                  )}
                  <p>ใบส่งของ/ส่งมอบ</p>
                  <FilePicker
                    makeFile={(formData: any) => {
                      setFile(formData);
                    }}
                    errorFileMsg={errorFileMsg}
                    makeErrorFileMsg={(msg: string) => {
                      setErrorFileMsg(msg);
                    }}
                  />
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      disabled={submittingImport}
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submittingImport ? (
                        <CircularProgress size={20} />
                      ) : (
                        'นำสินค้าเข้าคลัง'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ModalImportLotToWareHouse;
