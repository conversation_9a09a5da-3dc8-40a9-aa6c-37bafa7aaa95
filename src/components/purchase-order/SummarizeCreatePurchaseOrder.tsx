import React from 'react';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { But<PERSON>, CircularProgress, TextField } from '@mui/material';
import ModalImportRawMaterial from '@/components/purchase-order/ModalImportRawMaterial';
import { numberWithCommas } from '@/utils/number';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

const SummarizeCreatePurchaseOrderStyled = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  margin-top: 40px;
  width: 100%;
  @media screen and (max-width: 650px) {
    margin-top: 24px;
  }
  .event-section {
    display: flex;
    column-gap: 40px;
    width: 100%;
    @media screen and (max-width: 1200px) {
      flex-direction: column;
      row-gap: 40px;
    }
    @media screen and (max-width: 650px) {
      row-gap: 24px;
    }
    .left {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      @media screen and (max-width: 1200px) {
        row-gap: 40px;
      }
      @media screen and (max-width: 650px) {
        row-gap: 24px;
      }
      .remark {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        width: 100%;
      }
    }
    .right {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      .title {
        font-size: 22px;
        font-weight: 600;
        height: 40px;
        display: flex;
        align-items: center;
      }
      .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 72px;
        border-bottom: 1px solid;
        column-gap: 24px;
        &:first-child {
          height: 48px;
          align-items: start;
        }
        .key-group {
          display: flex;
          align-items: center;
          .label {
            white-space: nowrap;
          }
          .MuiInputBase-input {
            text-align: center;
          }
        }
        @media screen and (max-width: 480px) {
          .MuiFormControl-root {
            width: 40%;
          }
        }
        .value {
          font-weight: 600;
        }
      }
      .summation {
        width: 100%;
        display: flex;
        margin-top: 16px;
        @media screen and (max-width: 1200px) {
          flex-direction: column;
        }
        .bar {
          min-height: 76px;
          display: flex;
          align-items: center;
          padding: 20px 24px;
          width: 100%;
          justify-content: space-between;
          column-gap: 24px;
          flex-wrap: wrap;
          background: #dbe2e5;
          border-radius: 8px;
        }
        .content {
          flex: 1 1 0%;
          display: flex;
          flex-direction: column;
          .sum {
            font-size: 24px;
            font-weight: 600;
          }
          .btn-submit-group {
            margin-top: 25px;
            display: flex;
            flex-direction: column;
            row-gap: 26px;
            @media screen and (max-width: 1200px) {
              padding: 0;
              label {
                padding: 0 0 0 20px;
              }
            }
          }
        }
      }
    }
  }
`;
type SummarizeCreatePurchaseOrderProps = {
  handleImportMaterial: (pushData: any) => void;
  warehouseOptions: any;
  bodyProps: any;
  handleChangeDiscountBaht: (value: number) => void;
  handleChangeDiscountPercent: (value: number) => void;
  handleChangeShippingPrice: (value: number) => void;
  handleChangeRemark: (value: string) => void;
  handleSubmit: (action: string) => void;
  loadingSubmit: any;
};

const SummarizeCreatePurchaseOrder = ({
  handleImportMaterial,
  warehouseOptions,
  bodyProps,
  handleChangeDiscountBaht,
  handleChangeDiscountPercent,
  handleChangeShippingPrice,
  handleChangeRemark,
  handleSubmit,
  loadingSubmit,
}: SummarizeCreatePurchaseOrderProps) => {
  const { permissions } = useAppSelector(permissionSelector);
  return (
    <>
      <SummarizeCreatePurchaseOrderStyled>
        <div className="event-section">
          <div className="left">
            <ModalImportRawMaterial
              handleImportMaterial={handleImportMaterial}
              warehouseOptions={warehouseOptions}
            >
              <ActionButton
                variant="contained"
                color="dark"
                icon={<AddCircle />}
                text="เพิ่มรายการ"
                disabled={
                  !isAllowed(permissions, 'stock.purchase-order.update')
                }
              />
            </ModalImportRawMaterial>
            <div className="remark">
              หมายเหตุ
              <TextField
                value={bodyProps.description || ''}
                onChange={(e: any) => {
                  handleChangeRemark(e.target.value);
                }}
                multiline
                rows={10}
                placeholder="ระบุข้อความ"
              />
            </div>
          </div>
          <div className="right">
            <div className="title">ยอดชำระเงิน</div>
            <div className="list">
              <div className="key-group">
                <div className="label">ส่วนลด</div>
              </div>
              <div className="flex items-center gap-2 w-full justify-end">
                <TextField
                  type="number"
                  placeholder="0"
                  value={bodyProps.totalDiscountRate || ''}
                  onChange={(e: any) => {
                    const value = Number(e.target.value);
                    if (value < 0 || value > 100) {
                      e.preventDefault();
                    } else {
                      handleChangeDiscountPercent(e.target.value);
                    }
                  }}
                  onKeyDown={(e: any) => {
                    if (e.key === '-') {
                      e.preventDefault();
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                  }}
                  InputProps={{
                    endAdornment: (
                      <div
                        style={{
                          lineHeight: '1',
                        }}
                      >
                        %
                      </div>
                    ),
                  }}
                  sx={{
                    width: '74px',
                  }}
                />
                <TextField
                  type="number"
                  placeholder="0"
                  value={bodyProps.totalDiscount || ''}
                  onChange={(e: any) => {
                    if (Number(e.target.value) < 0) {
                      e.preventDefault();
                    } else {
                      handleChangeDiscountBaht(e.target.value);
                    }
                  }}
                  onKeyDown={(e: any) => {
                    if (e.key === '-') {
                      e.preventDefault();
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                  }}
                  InputProps={{
                    endAdornment: (
                      <div
                        style={{
                          lineHeight: '1',
                        }}
                      >
                        บาท
                      </div>
                    ),
                  }}
                  sx={{
                    width: '120px',
                  }}
                />
              </div>
            </div>
            <div className="list">
              <div className="key-group">
                <div className="label">มูลค่าก่อนรวมภาษี</div>
              </div>
              <div className="value">
                {numberWithCommas(bodyProps.totalSubtotal, 2)} บาท
              </div>
            </div>

            <div className="list">
              <div className="key-group">
                <div className="label">ภาษีมูลค่าเพิ่ม 7%</div>
              </div>
              <div className="value">
                {numberWithCommas(bodyProps.totalTaxPrice, 2)} บาท
              </div>
            </div>
            <div className="list">
              <div className="key-group">
                <div className="label">จัดส่งด้วยตัวแทนจำหน่าย</div>
              </div>
              <TextField
                type="number"
                placeholder="0"
                value={bodyProps.shippingPrice || ''}
                onChange={(e: any) => {
                  if (Number(e.target.value) < 0) {
                    e.preventDefault();
                  } else {
                    handleChangeShippingPrice(e.target.value);
                  }
                }}
                onKeyDown={(e: any) => {
                  if (e.key === '-') {
                    e.preventDefault();
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                }}
                InputProps={{
                  endAdornment: (
                    <div
                      style={{
                        lineHeight: '1',
                      }}
                    >
                      บาท
                    </div>
                  ),
                }}
                sx={{
                  width: '202px',
                }}
              />
            </div>
            <div className="summation">
              <div className="content">
                <div className="bar">
                  <div>มูลค่ารวมสุทธิ</div>
                  <div className="sum">
                    {numberWithCommas(bodyProps.totalPrice, 2)} บาท
                  </div>
                </div>
                <div className="btn-submit-group">
                  <div
                    className="flex"
                    style={{
                      columnGap: '24px',
                    }}
                  >
                    <Button
                      type="button"
                      variant="contained"
                      color="dark"
                      fullWidth
                      disabled={
                        loadingSubmit.draft ||
                        loadingSubmit.submit ||
                        !isAllowed(permissions, 'stock.purchase-order.update')
                      }
                      onClick={() => {
                        handleSubmit('draft');
                      }}
                    >
                      {loadingSubmit.draft ? (
                        <CircularProgress size={20} sx={{ color: 'black' }} />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="contained"
                      color="Hon"
                      fullWidth
                      disabled={
                        loadingSubmit.draft ||
                        loadingSubmit.submit ||
                        !isAllowed(permissions, 'stock.purchase-order.update')
                      }
                      onClick={() => {
                        handleSubmit('validate');
                      }}
                    >
                      {loadingSubmit.submit ? (
                        <CircularProgress size={20} sx={{ color: 'white' }} />
                      ) : (
                        <div
                          className="flex items-center gap-[8px]"
                          style={{
                            maxWidth: '100%',
                          }}
                        >
                          <span
                            style={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            บันทึก และ ขออนุมัติการสั่งซื้อ
                          </span>
                          <KeyboardBackspaceRoundedIcon
                            sx={{
                              rotate: '180deg',
                            }}
                          />
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SummarizeCreatePurchaseOrderStyled>
    </>
  );
};

export default SummarizeCreatePurchaseOrder;
