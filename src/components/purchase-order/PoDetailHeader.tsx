import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import AccessTimeFilledRoundedIcon from '@mui/icons-material/AccessTimeFilledRounded';
import { Button, Menu, MenuItem } from '@mui/material';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import { LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

dayjs.extend(utc);
const PoDetailHeaderStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 42px 24px 28px;
  column-gap: 40px;
  row-gap: 24px;
  flex-wrap: wrap;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 820px) {
    padding: 42px 16px 28px;
  }
  @media screen and (max-width: 650px) {
    row-gap: 16px;
    padding: 38px 16px 24px;
  }
  .po-number {
    position: relative;
    .title {
      font-size: 10px;
      font-weight: 500;
      line-height: 1;
      position: absolute;
      top: -16px;
    }
    .code {
      font-size: 40px;
      font-weight: 600;
      line-height: 1;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      @media screen and (max-width: 650px) {
        font-size: 28px;
      }
      @media screen and (max-width: 350px) {
        font-size: 22px;
      }
    }
  }
  .action-group {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    .status-tag {
      height: 40px;
      padding: 0 16px 0 8px;
      display: flex;
      align-items: center;
      column-gap: 8px;
      border-radius: 20px;
      line-height: 1;
      &.pending {
        color: #fbc02d;
        background-color: #fff9e6;
        border: 1px solid #fbc02d;
      }
    }
  }
  .action-wrap {
    gap: 16px;
    display: flex;
    align-items: center;
    .dropdown-action {
      display: flex;
      align-items: center;
      height: 40px;
      border-radius: 8px;
      border: 1px solid #dbe2e5;
      span {
        padding: 0 16px;
        white-space: nowrap;
      }
      .dropdown-btn {
        width: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #dbe2e5;
        cursor: pointer;
      }
    }
  }
`;
export const HrSpaceStyle = styled.div`
  width: 100%;
  background-color: #f5f7f8;
  border-top: 1px solid #dbe2e5;
  height: 8px;
  min-height: 8px;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
type PoDetailHeaderProps = {
  purchaseOrderById: any;
  handleRevertStep: () => void;
  handleOpenLog: () => void;
  handleCancelPo: (reason: {
    annotationId: any;
    note: any;
    reason: string;
  }) => void;
  loadingCancelPo: boolean;
};
const PoDetailHeader = ({
  purchaseOrderById,
  handleRevertStep,
  handleOpenLog,
  handleCancelPo,
  loadingCancelPo,
}: PoDetailHeaderProps) => {
  const { permissions } = useAppSelector(permissionSelector);
  const [anchorEl, setAnchorEl] = useState(null);
  const [modalCancel, setModalCancel] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const handleOpenModalCancel = () => {
    setModalCancel({
      open: true,
      title: 'ยกเลิกรายการสั่งซื้อ',
      description: `คุณต้องการยกเลิกรายการสั่งซื้อเลขที่ “${purchaseOrderById.poNo}” ต้องระบุเหตุผลของการขอยกเลิก`,
    });
  };
  const onCloseModalCancel = () => {
    setModalCancel({
      ...modalCancel,
      open: false,
    });
  };
  const handleClickKebab = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  useEffect(() => {
    if (!loadingCancelPo) {
      onCloseModalCancel();
    }
  }, [loadingCancelPo]);
  return (
    <>
      <AppModalConfirm
        open={modalCancel.open}
        onClickClose={() => {
          onCloseModalCancel();
        }}
        confirmTitle={modalCancel.title}
        confirmDescription={modalCancel.description}
        loadingConfirm={loadingCancelPo}
        onConfirm={(reason: {
          annotationId: any;
          note: any;
          reason: string;
        }) => {
          handleCancelPo(reason);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="420px"
        isReason={true}
      />
      <PoDetailHeaderStyled>
        <div className="po-number">
          <div className="title">เลขที่</div>
          <div className="code">{purchaseOrderById.poNo}</div>
        </div>
        <div className="action-group">
          {purchaseOrderById.poOrderStatus.id === 2 && (
            <div className="status-tag pending">
              <AccessTimeFilledRoundedIcon />
              <span>
                {purchaseOrderById.poOrderStatus.status} สิ้นสุด{' '}
                {dayjs(purchaseOrderById.expireDate).utc().format('DD/MM/YYYY')}
              </span>
            </div>
          )}
          <div className="action-wrap">
            <div className="dropdown-action">
              <span>พิมพ์เอกสาร</span>
              <div className="dropdown-btn">
                <KeyboardArrowDownRoundedIcon />
              </div>
            </div>
            <div>
              <Button
                size="small"
                onClick={(event: any) => {
                  handleClickKebab(event);
                }}
                sx={{
                  height: '40px',
                  width: '40px',
                  minWidth: '40px',
                  border: '1px solid #DBE2E5',
                }}
              >
                <div
                  className="action-dot"
                  style={{
                    rotate: '180deg',
                  }}
                >
                  <div className="dot" />
                </div>
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleClose}
                sx={{
                  marginTop: '8px',
                  '.MuiList-root': {
                    padding: '8px',
                  },
                }}
              >
                <MenuItem
                  onClick={handleClose}
                  sx={{
                    padding: '0 8px',
                    height: '40px',
                    width: '100%',
                    borderRadius: '8px',
                  }}
                  disabled={
                    purchaseOrderById.poOrderStatus.id !== 2 ||
                    isAllowed(permissions, 'stock.purchase-order.update')
                  }
                >
                  <div
                    className="drop-menu"
                    onClick={() => {
                      handleRevertStep();
                    }}
                  >
                    <Image
                      src={'/icons/icon-reply-all.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                    ตีกลับไปแก้ไข
                  </div>
                </MenuItem>
                <MenuItem
                  onClick={handleClose}
                  sx={{
                    padding: '0 8px',
                    height: '40px',
                    width: '100%',
                    borderRadius: '8px',
                  }}
                >
                  <div
                    className="drop-menu"
                    onClick={() => {
                      handleOpenLog();
                    }}
                  >
                    <Image
                      src={'/icons/aside/user-menu/icon-history.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                    ประวัติรายการ
                  </div>
                </MenuItem>
                <MenuItem
                  onClick={handleClose}
                  sx={{
                    padding: '0 8px',
                    height: '40px',
                    width: '100%',
                    borderRadius: '8px',
                  }}
                >
                  <div className="drop-menu" onClick={() => {}}>
                    <Image
                      src={'/icons/icon-info-black.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                    วิธีใช้งาน
                  </div>
                </MenuItem>
                <MenuItem
                  onClick={handleClose}
                  sx={{
                    padding: '0 8px',
                    height: '40px',
                    width: '100%',
                    borderRadius: '8px',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  }}
                  disabled={
                    purchaseOrderById.poOrderStatus.id !== 2 ||
                    isAllowed(permissions, 'stock.purchase-order.cancel')
                  }
                >
                  <div
                    className="drop-menu text-[#D32F2F]"
                    onClick={handleOpenModalCancel}
                  >
                    <Image
                      src={'/icons/icon-scan-delete.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                    ยกเลิกรายการ
                  </div>
                </MenuItem>
              </Menu>
            </div>
          </div>
        </div>
      </PoDetailHeaderStyled>
      <HrSpaceStyle />
    </>
  );
};

export default PoDetailHeader;
