import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  AppContentStyle,
  AppTableStyle,
  LoadingFadein,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import { isEmpty, isNull } from 'lodash';
import { Button, CircularProgress, IconButton } from '@mui/material';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { useRouter } from 'next/router';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import ModalPayment from '@/components/purchase-order/ModalPayment';
import dayjs from 'dayjs';
import ModalImportLotToWareHouse, {
  LotInfoStyled,
} from '@/components/purchase-order/ModalImportLotToWareHouse';
import apiLot from '@/services/stock/lot';
import SideDetail from '@/components/SideDetail';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import getColumnByStep from '@/pages/stock/purchase-order/dataGridColumns';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const PoMaterialDetailStyled = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  .title-bar {
    width: 100%;
    padding: 24px;
    display: flex;
    align-items: center;
    font-size: 22px;
    font-weight: 600;
  }
  .footer {
    width: 100%;
    display: flex;
    padding: 24px;
    column-gap: 32px;
    margin-bottom: 16px;
    row-gap: 24px;
    @media screen and (max-width: 767px) {
      flex-direction: column-reverse;
    }
    .content {
      flex: 1 1 0%;
      .remark {
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        width: 85%;
        .title {
          font-weight: 600;
        }
      }
      .summarize {
        display: flex;
        flex-direction: column;
        width: 100%;
        font-size: 16px;
        .list {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 24px;
          column-gap: 24px;
          .key {
            font-weight: 400;
            white-space: nowrap;
          }
          .value {
            font-weight: 600;
            white-space: nowrap;
          }
          &.sum {
            border-top: 2px solid #263238;
            border-bottom: 2px solid #263238;
            height: 72px;
            @media screen and (max-width: 424px) {
              height: 64px;
            }
            * {
              font-size: 22px;
              font-weight: 600;
              @media screen and (max-width: 424px) {
                font-size: 18px;
              }
            }
          }
        }
        .hr-custom {
          width: 100%;
          background: #dbe2e5;
          height: 1px;
          margin: 24px 0;
        }
      }
    }
  }
  .submit-btn-wrap {
    display: flex;
    column-gap: 32px;
    padding: 0 24px;
  }
  .payment-slip {
    display: flex;
    flex-direction: column;
    padding: 0 40px;
    .header {
      min-height: 78px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-group {
        display: flex;
        align-items: center;
        column-gap: 24px;
        .title {
          font-size: 22px;
          font-weight: 600;
        }
        .empty-payment {
          height: 40px;
          display: flex;
          align-items: center;
          padding: 0 24px 0 10px;
          color: #f9a925;
          background: #fff9e6;
          border-radius: 20px;
          column-gap: 8px;
        }
      }
    }
    .payment-list-wrap {
      width: 100%;
      display: flex;
      flex-direction: column;
      .list {
        display: flex;
        width: 100%;
        align-items: center;
        min-height: 72px;
        justify-content: space-between;
        border-top: 1px solid #dbe2e5;

        .info {
          display: flex;
          align-items: center;
          position: relative;
          min-height: 40px;

          img {
            width: 40px !important;
            height: 40px !important;
            border-radius: 6px;
            overflow: hidden;
          }
          .text-group {
            margin-left: 56px;
            display: flex;
            flex-direction: column;
            row-gap: 4px;
            line-height: 1;
            .amount {
              font-size: 16px;
              font-weight: 600;
            }
            .description {
              font-size: 12px;
              display: flex;
              align-items: center;
            }
          }
        }
        .remove-btn {
          height: 40px;
          width: 40px;
          border-radius: 6px;
          cursor: pointer;
          transition: 0.3s ease-out;
          background: #d32f2f;
          display: flex;
          align-items: center;
          justify-content: center;
          &:hover {
            filter: brightness(0.9);
          }
        }
      }
    }
  }
`;
const ImportListStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 48px;
  .title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .list {
    min-height: 64px;
    display: flex;
    justify-content: space-between;
    column-gap: 24px;
    align-items: center;
    border-top: 1px solid #dbe2e5;
    &:last-child {
      border-bottom: 1px solid #dbe2e5;
    }
    .key {
      color: #90a4ae;
    }
    .value {
      font-weight: 600;
      &.link {
        cursor: pointer;
        max-width: 50%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
`;
type PoMaterialDetailProps = {
  purchaseOrderById: any;
  reloadPurchaseOrderById: () => void;
};

const PoMaterialDetail = ({
  purchaseOrderById,
  reloadPurchaseOrderById,
}: PoMaterialDetailProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [modalConfirm, setModalConfirm] = useState<any>({
    open: false,
    title: '',
    description: '',
    id: '',
  });
  const [openModalPayment, setOpenModalPayment] = useState<boolean>(false);
  const [openModalImport, setOpenModalImport] = useState<boolean>(false);
  const [modalImportData, setModalImportData] = useState<any>({});
  const [loadingApprove, setLoadingApprove] = useState<boolean>(false);
  const [submittingPayment, setSubmittingPayment] = useState<boolean>(false);
  const [submittingImport, setSubmittingImport] = useState<boolean>(false);
  const [lotDetail, setLotDetail] = useState<any>({});
  const [openSideDetail, setOpenSideDetail] = useState<boolean>(false);
  const handleOpenModalImport = (data: any) => {
    setOpenModalImport(true);
    setModalImportData(data);
  };
  const handleClickDetail = async (data: any) => {
    setLotDetail({});
    const res = await apiLot.getLotDetail(data.id, data.rawMaterial.id);
    if (!res.isError) {
      setLotDetail({
        ...res.data,
        totalPrice: data.totalPrice,
        totalQuantity: data.quantity,
      });
      setOpenSideDetail(true);
    }
  };
  const onCloseModalConfirm = () => {
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
  };
  const handleApprove = async () => {
    setLoadingApprove(true);
    const res = await apiPurchaseOrder.updatePurchaseOrderNextStep(
      purchaseOrderById
    );
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ทำรายการสำเร็จ',
          severity: 'success',
        })
      );
      await router.push(
        `/stock/purchase-order?poOrderStatusId=${
          purchaseOrderById.poOrderStatus.id + 1
        }`
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
    setLoadingApprove(false);
  };
  const handleComplete = async () => {
    setLoadingApprove(true);
    const res = await apiPurchaseOrder.completePo({
      id: purchaseOrderById.id,
    });
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ยืนยันนำสินค้าเข้าคลังสำเร็จ',
          severity: 'success',
        })
      );
      await router.push(
        `/stock/purchase-order?poOrderStatusId=${
          purchaseOrderById.poOrderStatus.id + 1
        }`
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
    setLoadingApprove(false);
  };

  const handleImport = async (value: any, file: any) => {
    setSubmittingImport(true);
    const res = await apiLot.importLot(value);
    if (!res.isError) {
      if (!isNull(file)) {
        const formData = new FormData();
        formData.append('file', file);
        const resUpload = await apiLot.uploadLotSheet(
          res.data.id as string,
          formData
        );
        if (!resUpload.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: 'นำสินค้าเข้าคลังสำเร็จ',
              severity: 'success',
            })
          );
          reloadPurchaseOrderById();
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
              severity: 'warning',
            })
          );
          reloadPurchaseOrderById();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'นำสินค้าเข้าคลังสำเร็จ',
            severity: 'success',
          })
        );
        reloadPurchaseOrderById();
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmittingImport(false);
    setOpenModalImport(false);
  };
  const handleSavePayment = async (value: any, file: any) => {
    setSubmittingPayment(true);
    const res = await apiPurchaseOrder.payment({
      ...value,
      poOrderId: purchaseOrderById.id,
    });
    if (!res.isError) {
      const formData = new FormData();
      formData.append('file', file);
      const resUpload = await apiPurchaseOrder.paymentUpload(
        res.data.id as string,
        formData
      );
      if (!resUpload.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เพิ่มการชำระเงินสำเร็จ',
            severity: 'success',
          })
        );
        reloadPurchaseOrderById();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์',
            severity: 'error',
          })
        );
        reloadPurchaseOrderById();
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setOpenModalPayment(false);
    setSubmittingPayment(false);
  };
  const handleDeletePayment = async () => {
    setLoadingApprove(true);
    const res = await apiPurchaseOrder.deletePayment(modalConfirm.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบการชำระเงินสำเร็จ',
          severity: 'success',
        })
      );
      reloadPurchaseOrderById();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoadingApprove(false);
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
  };
  useEffect(() => {
    if (modalConfirm.status === false) {
      setModalConfirm({
        open: false,
        title: '',
        description: '',
        id: '',
      });
    }
  }, [modalConfirm]);
  return (
    <>
      {!isEmpty(modalImportData) && (
        <ModalImportLotToWareHouse
          open={openModalImport}
          handleClose={() => {
            setOpenModalImport(false);
          }}
          handleImport={handleImport}
          submittingImport={submittingImport}
          data={modalImportData}
        />
      )}
      <ModalPayment
        open={openModalPayment}
        handleClose={() => {
          setOpenModalPayment(false);
        }}
        savePayment={handleSavePayment}
        submittingPayment={submittingPayment}
      />
      <AppModalConfirm
        open={modalConfirm.open}
        onClickClose={() => {
          onCloseModalConfirm();
        }}
        confirmTitle={modalConfirm.title}
        confirmDescription={modalConfirm.description}
        loadingConfirm={loadingApprove}
        onConfirm={() => {
          onCloseModalConfirm();
          if (modalConfirm.id) {
            handleDeletePayment();
          } else if (purchaseOrderById.poOrderStatus.id === 4) {
            handleComplete();
          } else {
            handleApprove();
          }
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="328px"
      />
      <SideDetail isOpen={openSideDetail}>
        {!isEmpty(lotDetail) && (
          <>
            <div className="header">
              <div className="topic">{lotDetail.rawMaterial.rawMaterialNo}</div>
              <div
                className="x-close"
                onClick={() => {
                  setOpenSideDetail(false);
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="content">
              <div className="body">
                <LotInfoStyled
                  style={{
                    marginTop: '24px',
                  }}
                >
                  <div className="raw-material-group">
                    <Image
                      src={
                        lotDetail.rawMaterial.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={50}
                      height={50}
                      alt=""
                    />
                    <div className="name">
                      <span>{`${lotDetail.rawMaterial.name} • `}</span>
                      <span>{`${lotDetail.rawMaterial.brand.name} `}</span>
                      {lotDetail.rawMaterial.itemSize?.name && (
                        <span>{`• ${lotDetail.rawMaterial.itemSize.name} `}</span>
                      )}
                      {lotDetail.rawMaterial.subMaterialDetail?.side && (
                        <span>
                          {`• ${lotDetail.rawMaterial.subMaterialDetail.side} ด้าน`}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="detail">
                    <div className="item">
                      <div className="key">การจัดเก็บ</div>
                      <div className="value">{lotDetail.stock.name}</div>
                    </div>
                    <div className="item">
                      <div className="key">จำนวน</div>
                      <div className="value">
                        {numberWithCommas(lotDetail.totalQuantity)}{' '}
                        {lotDetail.rawMaterial.countComponent.name}
                      </div>
                    </div>
                    <div className="item">
                      <div className="key">ราคารวม</div>
                      <div className="value">
                        {numberWithCommas(lotDetail.totalPrice, 2)} บาท
                      </div>
                    </div>
                  </div>
                </LotInfoStyled>
                <ImportListStyled>
                  <div className="title">รายการนำเข้าคลัง</div>
                  <div className="list">
                    <div className="key">เลขที่ล็อตสินค้า</div>
                    <div className="value">{lotDetail.lotNo}</div>
                  </div>
                  <div className="list">
                    <div className="key">จำนวนนำเข้า</div>
                    <div className="value">
                      {numberWithCommas(lotDetail.quantity)}{' '}
                      {lotDetail.rawMaterial.countComponent.name}
                    </div>
                  </div>
                  <div className="list">
                    <div className="key">วันหมดอายุ</div>
                    <div className="value">
                      {dayjs(lotDetail.expireDate).format('DD/MM/YYYY')}
                    </div>
                  </div>
                  <div className="list">
                    <div className="key">หลักฐานการรับสินค้า</div>
                    <div
                      className={`value ${
                        lotDetail.fileDeliveryUrl ? 'link' : ''
                      }`}
                      onClick={() => {
                        if (lotDetail.fileDeliveryUrl) {
                          window.open(
                            lotDetail.fileDeliveryUrl,
                            '_blank',
                            'noopener,noreferrer'
                          );
                        }
                      }}
                    >
                      {lotDetail.fileDeliveryUrl || 'ไม่มี'}
                    </div>
                  </div>
                </ImportListStyled>
              </div>
            </div>
          </>
        )}
      </SideDetail>
      <PoMaterialDetailStyled>
        <AppContentStyle>
          <div className="content-wrap">
            <div className="title-bar">รายการวัสดุ</div>
            <AppTableStyle $rows={purchaseOrderById.poOrderList}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  {(purchaseOrderById.poOrderStatus.id === 2 ||
                    purchaseOrderById.poOrderStatus.id === 3) && (
                    <HeaderColumnAction text="ราคารวม" width={162} />
                  )}
                  {(purchaseOrderById.poOrderStatus.id === 4 ||
                    purchaseOrderById.poOrderStatus.id === 5) && (
                    <HeaderColumnAction text="จัดการ" width={100} />
                  )}
                  <DataGrid
                    hideFooter={true}
                    rows={purchaseOrderById.poOrderList}
                    columns={getColumnByStep(
                      purchaseOrderById.poOrderStatus.id,
                      handleOpenModalImport,
                      handleClickDetail
                    )}
                    paginationMode="server"
                    rowCount={purchaseOrderById.poOrderList.length || 0}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                  />
                </ScrollBarStyled>
              </div>
            </AppTableStyle>
            <div className="footer">
              <div className="content">
                <div className="remark">
                  <div className="title">หมายเหตุ</div>
                  {!isEmpty(purchaseOrderById.description)
                    ? purchaseOrderById.description
                    : '-'}
                </div>
              </div>
              <div className="content">
                <div className="summarize">
                  <div className="list !m-0">
                    <div className="key">ยอดรวม</div>
                    <div className="value">
                      {numberWithCommas(purchaseOrderById.totalSubtotal)} บาท
                    </div>
                  </div>
                  <div className="hr-custom" />
                  <div className="list !m-0">
                    <div className="key">ส่วนลด</div>
                    <div className="value">
                      {numberWithCommas(purchaseOrderById.totalDiscount)} บาท
                    </div>
                  </div>
                  <div className="list">
                    <div className="key">ภาษีมูลค่าเพิ่ม</div>
                    <div className="value">
                      {numberWithCommas(purchaseOrderById.totalTax)} บาท
                    </div>
                  </div>
                  <div className="list">
                    <div className="key">ค่าจัดส่ง</div>
                    <div className="value">
                      {numberWithCommas(purchaseOrderById.shippingPrice)} บาท
                    </div>
                  </div>
                  <div className="list sum">
                    <div className="key">มูลค่ารวมสุทธิ</div>
                    <div className="value">
                      {numberWithCommas(purchaseOrderById.totalPrice)} บาท
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {(purchaseOrderById.poOrderStatus.id === 3 ||
              purchaseOrderById.poOrderStatus.id === 4 ||
              purchaseOrderById.poOrderStatus.id === 5) && (
              <>
                <HrSpaceStyle />
                <div className="payment-slip">
                  <div className="header">
                    <div className="title-group">
                      <div className="title">การชำระเงิน</div>
                      {!purchaseOrderById.isPayment && (
                        <div className="empty-payment">
                          <Image
                            src={'/icons/icon-warning.svg'}
                            width={24}
                            height={24}
                            alt=""
                          />
                          <span>ยังไม่มีรายการชำระเงิน</span>
                        </div>
                      )}
                    </div>
                    <div
                      onClick={() => {
                        setOpenModalPayment(true);
                      }}
                    >
                      <ActionButton
                        variant="outlined"
                        color="blueGrey"
                        icon={<AddCircle />}
                        text="เพิ่มการชำระเงิน"
                        borderRadius={'20px'}
                      />
                    </div>
                  </div>
                  {!isEmpty(purchaseOrderById.poOrderPayment) && (
                    <div className="payment-list-wrap">
                      {purchaseOrderById.poOrderPayment.map(
                        (item: any, index: React.Key) => {
                          return (
                            <div className="list" key={index}>
                              <div className="info">
                                <Image
                                  src={
                                    item.imagePaymentUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  fill
                                  alt=""
                                />
                                <div className="text-group">
                                  <div className="amount">
                                    {numberWithCommas(item.paymentAmount, 2)}{' '}
                                    บาท
                                  </div>
                                  <div className="description">
                                    {dayjs(item.paymentDate).format(
                                      'DD/MM/YYYY, HH:mm น.'
                                    )}{' '}
                                    {item.remark && `• ${item.remark}`}
                                  </div>
                                </div>
                              </div>
                              <div
                                className="remove-btn"
                                onClick={() => {
                                  setModalConfirm({
                                    open: true,
                                    title: 'ลบการชำระเงินนี้',
                                    description: `ลบการชำระเงิน ${item.remark}`,
                                    id: item.id,
                                  });
                                }}
                              >
                                <Image
                                  src="/icons/delete-white.svg"
                                  width={24}
                                  height={24}
                                  alt=""
                                />
                              </div>
                            </div>
                          );
                        }
                      )}
                    </div>
                  )}
                </div>
                <HrSpaceStyle />
                <div className="mb-[40px]" />
              </>
            )}
            {purchaseOrderById.poOrderStatus.id !== 5 &&
              purchaseOrderById.poOrderStatus.id !== 6 && (
                <div className="submit-btn-wrap">
                  <div
                    style={{
                      flex: '1',
                    }}
                  ></div>
                  <div
                    style={{
                      flex: '1',
                    }}
                  >
                    <Button
                      type="button"
                      variant="contained"
                      color="Hon"
                      fullWidth
                      disabled={
                        purchaseOrderById.poOrderStatus.id === 4
                          ? !purchaseOrderById.isComplete || loadingApprove
                          : loadingApprove
                      }
                      onClick={() => {
                        if (purchaseOrderById.poOrderStatus.id === 3) {
                          setModalConfirm({
                            open: true,
                            title: 'ไปยังขั้นตอนนำสินค้าเข้าคลัง',
                            description:
                              'คุณได้ตรวจสอบความถูกต้องของรายการสั่งซื้อ\n' +
                              `เลขที่ “${purchaseOrderById.poNo}” เรียบร้อยแล้ว`,
                            id: '',
                          });
                        } else if (purchaseOrderById.poOrderStatus.id === 4) {
                          setModalConfirm({
                            open: true,
                            title: 'ยืนยันสำเร็จรายการสั่งซื้อ',
                            description:
                              'คุณได้ตรวจสอบความถูกต้องของรายการสั่งซื้อ\n' +
                              `เลขที่ “${purchaseOrderById.poNo}” เรียบร้อยแล้ว`,
                            id: '',
                          });
                        } else {
                          setModalConfirm({
                            open: true,
                            title: 'อนุมัติใบสั่งซื้อ',
                            description:
                              'คุณได้ตรวจสอบความถูกต้องของรายการสั่งซื้อ\n' +
                              `เลขที่ “${purchaseOrderById.poNo}” เรียบร้อยแล้ว`,
                            id: '',
                          });
                        }
                      }}
                      sx={{
                        marginBottom: '40px',
                      }}
                    >
                      {loadingApprove ? (
                        <CircularProgress size={20} sx={{ color: 'white' }} />
                      ) : (
                        <div
                          className="flex items-center gap-[8px]"
                          style={{
                            maxWidth: '100%',
                          }}
                        >
                          <span
                            style={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {purchaseOrderById.poOrderStatus.id === 2 &&
                              'อนุมัติใบสั่งซื้อ'}
                            {purchaseOrderById.poOrderStatus.id === 3 &&
                              'นำสินค้าเข้าคลัง'}
                            {purchaseOrderById.poOrderStatus.id === 4 &&
                              'ยืนยันการนำสินค้าเข้าคลัง'}
                          </span>
                          <KeyboardBackspaceRoundedIcon
                            sx={{
                              rotate: '180deg',
                            }}
                          />
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              )}
          </div>
        </AppContentStyle>
      </PoMaterialDetailStyled>
    </>
  );
};

export default PoMaterialDetail;
