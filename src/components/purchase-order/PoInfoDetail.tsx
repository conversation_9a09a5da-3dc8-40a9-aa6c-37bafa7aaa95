import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import { LoadingFadein } from '@/styles/share.styled';
import moment from 'moment/moment';

const PoInfoDetailStyled = styled.div`
  width: 100%;
  display: flex;
  padding: 24px;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 1200px) {
    flex-direction: column;
    row-gap: 40px;
  }
  .info {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    row-gap: 40px;
    .title {
      font-size: 22px;
      font-weight: 600;
      line-height: 1;
    }
    .profile-group {
      display: flex;
      column-gap: 16px;
      align-items: center;
      @media screen and (max-width: 424px) {
        flex-direction: column;
        row-gap: 4px;
      }
      .image {
        height: 56px;
        width: 56px;
        overflow: hidden;
        border-radius: 50%;
      }
      .text-group {
        display: flex;
        flex-direction: column;
        line-height: 1;
        row-gap: 8px;
        @media screen and (max-width: 424px) {
          text-align: center;
        }
        .role {
          font-size: 12px;
          @media screen and (max-width: 424px) {
            margin-top: 4px;
          }
        }
        .name {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
    .list-wrap {
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      .list {
        display: flex;
        column-gap: 40px;
        @media screen and (max-width: 424px) {
          flex-direction: column;
          row-gap: 4px;
        }
        .key {
          min-width: 132px;
          max-width: 132px;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #90a4ae;
        }
        .value {
          max-width: 334px;
          @media screen and (max-width: 920px) {
            max-width: 100%;
          }
        }
      }
    }
  }
`;
type PoInfoDetailProps = {
  purchaseOrderById: any;
};
const PoInfoDetail = ({ purchaseOrderById }: PoInfoDetailProps) => {
  return (
    <>
      <PoInfoDetailStyled>
        <div className="info">
          <div className="title">ข้อมูลรายการซื้อ</div>
          <div className="profile-group">
            <div className="image">
              <Image
                src={
                  purchaseOrderById.createdUser.imageUrl ||
                  '/images/product/empty-product.svg'
                }
                width={56}
                height={56}
                alt=""
              />
            </div>
            <div className="text-group">
              <div className="name">{purchaseOrderById.createdUser.name}</div>
              <div className="role">ผู้สร้าง</div>
            </div>
          </div>
          <div className="list-wrap">
            <div className="list">
              <div className="key">วันที่สั่งซื้อ</div>
              <div className="value">
                {moment(purchaseOrderById.poDate).format('DD/MM/YYYY HH:mm')}
              </div>
            </div>
            <div className="list">
              <div className="key">วันหมดอายุ</div>
              <div className="value">
                {moment(purchaseOrderById.expireDate).format('DD/MM/YYYY')}
              </div>
            </div>
            <div className="list">
              <div className="key">ประเภทภาษี</div>
              <div className="value">{purchaseOrderById.tax.nameTax}</div>
            </div>
            <div className="list">
              <div className="key">เครดิต</div>
              <div className="value">{purchaseOrderById.creditDay} วัน</div>
            </div>
          </div>
        </div>
        <div className="info">
          <div className="title">ตัวแทนจำหน่าย</div>
          <div className="profile-group">
            <div className="image">
              <Image
                src={
                  purchaseOrderById.contact.imageUrl ||
                  '/images/product/empty-product.svg'
                }
                width={56}
                height={56}
                alt=""
              />
            </div>
            <div className="text-group">
              <div className="name">{purchaseOrderById.contact.name}</div>
              <div className="role">ID : {purchaseOrderById.contact.code}</div>
            </div>
          </div>
          <div className="list-wrap">
            <div className="list">
              <div className="key">ประเภท</div>
              <div className="value">
                {purchaseOrderById.contact.contactType.name}
              </div>
            </div>
            <div className="list">
              <div className="key">เลขประจำตัวผู้เสียภาษี</div>
              <div className="value">{purchaseOrderById.contact.taxNumber}</div>
            </div>
            <div className="list">
              <div className="key">โทรศัพท์</div>
              <div className="value">
                {purchaseOrderById.contact.phoneNumber}
              </div>
            </div>
            <div className="list">
              <div className="key">อีเมล</div>
              <div className="value">{purchaseOrderById.contact.email}</div>
            </div>
            <div className="list">
              <div className="key">ที่อยู่</div>
              <div className="value">
                {`${purchaseOrderById.contact.taxAddress} ต.${purchaseOrderById.contact.subDistrict.name} อ.${purchaseOrderById.contact.district.name} จ.${purchaseOrderById.contact.province.name} ${purchaseOrderById.contact.zipcode}`}
              </div>
            </div>
          </div>
        </div>
      </PoInfoDetailStyled>
      <HrSpaceStyle />
    </>
  );
};

export default PoInfoDetail;
