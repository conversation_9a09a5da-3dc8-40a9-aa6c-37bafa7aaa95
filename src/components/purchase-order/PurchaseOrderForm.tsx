import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import TableCreatePurchaseOrder from '@/components/purchase-order/TableCreatePurchaseOrder';
import SummarizeCreatePurchaseOrder from '@/components/purchase-order/SummarizeCreatePurchaseOrder';
import ModalCreatePurchaseOrder from '@/components/purchase-order/ModalCreatePurchaseOrder';
import {
  FormControl,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import moment from 'moment/moment';
import { isEmpty, isNull } from 'lodash';
import HonDatePicker from '@/components/HonDatePicker';
import apiWarehouse from '@/services/stock/warehouse';
import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as yup from 'yup';
import dayjs from 'dayjs';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const PurchaseOrderFormStyled = styled.div`
  animation: ${LoadingFadein} 0.3s ease-in;
  display: flex;
  flex-direction: column;
  column-gap: 24px;
  width: 100%;
  padding: 40px 24px 24px;
  max-width: 100%;
  .sheet-header {
    display: flex;
    width: 100%;
    .qt-code {
      font-size: 34px;
      font-weight: 600;
      line-height: 1;
      position: relative;
      @media screen and (max-width: 1100px) {
        font-size: 30px;
      }
      @media screen and (max-width: 650px) {
        font-size: 24px;
      }
      .no {
        font-size: 12px;
        position: absolute;
        top: -18px;
        font-weight: 400;
      }
    }
  }
  .personal-wrap {
    width: 100%;
    display: flex;
    margin-top: 40px;
    column-gap: 40px;
    @media screen and (max-width: 1480px) {
      flex-direction: column;
      row-gap: 40px;
    }
    @media screen and (max-width: 650px) {
      row-gap: 24px;
      margin-top: 24px;
    }
    .left {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      width: 50%;
      @media screen and (max-width: 1480px) {
        width: auto;
      }
      .title {
        font-size: 22px;
        font-weight: 600;
      }
      .card-date {
        width: 100%;
        height: 220px;
        display: flex;
        flex-direction: column;
        border: 1px solid #dbe2e5;
        border-radius: 16px;
        font-size: 14px;
        .list {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #dbe2e5;
          padding: 0 8px 0 16px;
          column-gap: 16px;
          width: 100%;
          overflow: hidden;
          @media screen and (max-width: 424px) {
            //flex-wrap: wrap;
          }
          &:last-child {
            border-bottom: none;
          }
          &.empty {
            @media screen and (max-width: 1480px) {
              display: none;
            }
          }
          .key {
            white-space: nowrap;
          }
          @media screen and (max-width: 480px) {
            .MuiFormControl-root {
              width: 60% !important;
            }
            .MuiInputBase-root {
              width: 100% !important;
            }
          }
          .value {
            font-weight: 600;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 280px;
            border: 1px solid #dbe2e5;
            height: 40px;
            border-radius: 6px;
            padding: 0 16px;
            @media screen and (max-width: 480px) {
              width: 60%;
            }
            .icon {
              width: 32px;
              height: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 50%;
              transition: 0.3s ease-out;
              cursor: pointer;
              margin-right: -8px;
              &:hover {
                background: rgba(48, 213, 199, 0.2);
              }
              &.no-action {
                cursor: default;
                &:hover {
                  background: initial;
                }
              }
            }
          }
        }
      }
    }
    .right {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      width: 50%;
      @media screen and (max-width: 1480px) {
        width: auto;
      }
      .title {
        font-size: 22px;
        font-weight: 600;
      }
      .contact-bar {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        display: flex;
        column-gap: 8px;
        align-items: center;
        padding: 0 14px;
        transition: 0.3s ease-out;
        background: white;
        position: relative;
        font-size: 14px;
        img {
          border-radius: 50%;
        }
        .change {
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          text-decoration: underline;
          cursor: pointer;
        }
      }
      .address-wrap {
        width: 100%;
        height: 220px;
        background-color: #f5f7f8;
        border-radius: 16px;
        position: relative;
        padding: 16px;
        @media screen and (max-width: 1480px) {
          height: auto;
        }
      }
      .address {
        width: 100%;
        overflow: auto;
        font-size: 14px;
        margin-top: 16px;
        .name {
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 6px;
        }
        .address-text {
          display: flex;
          flex-direction: column;
          row-gap: 4px;
        }
        .blank-text {
          color: #cfd8dc;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translateY(-50%) translateX(-50%);
          text-align: center;
          @media screen and (max-width: 360px) {
            max-width: 200px;
          }
        }
      }
    }
  }
`;
type PurchaseOrderFormProps = {
  purchaseOrderById: any;
  taxOptions: any;
};

const validationSchemaDraft = yup.object().shape({});
const validationSchema = yup.object({
  poOrderList: yup
    .array()
    .of(
      yup.object({
        quantity: yup.number().moreThan(0, 'Required').required('Required'),
        priceUnit: yup.number().moreThan(0, 'Required').required('Required'),
      })
    )
    .min(1, 'กรุณาเพิ่มรายการ')
    .required('Required'),
});

const PurchaseOrderForm = ({
  purchaseOrderById,
  taxOptions,
}: PurchaseOrderFormProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [openCalendar, setOpenCalendar] = useState<boolean>(false);
  const [warehouseOptions, setWarehouseOptions] = useState<any>([]);
  const [loadingSubmit, setLoadingSubmit] = useState<any>({
    draft: false,
    submit: false,
  });
  const [submitAction, setSubmitAction] = useState<string>('draft');
  const [newContactData, setNewContactData] = useState<any>({});
  const [modalConfirm, setModalConfirm] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const formik = useFormik({
    initialValues: {
      id: purchaseOrderById.id,
      contactId: purchaseOrderById.contact.id,
      taxId: !isNull(purchaseOrderById.tax)
        ? purchaseOrderById.tax.id
        : taxOptions[0].id,
      totalSubtotal: purchaseOrderById.totalSubtotal,
      totalDiscount: purchaseOrderById.totalDiscount,
      totalDiscountRate: purchaseOrderById.totalDiscountRate,
      totalTaxPrice: purchaseOrderById.totalTax,
      shippingPrice: purchaseOrderById.shippingPrice,
      totalPrice: purchaseOrderById.totalPrice,
      description: purchaseOrderById.description,
      poDate: purchaseOrderById.poDate,
      expireDate: purchaseOrderById.expireDate,
      creditDay: purchaseOrderById.creditDay,
      poOrderList: purchaseOrderById.poOrderList.map((item: any) => {
        return {
          discountUnit: item.discountUnit,
          priceUnit: item.priceUnit,
          quantity: item.quantity,
          rawMaterialId: item.rawMaterial.id,
          stockId: item.stock.id,
          totalPrice: item.totalPrice,
          info: {
            side: item.rawMaterial.subMaterialDetail.side || null,
            rawMaterialNo: item.rawMaterial.rawMaterialNo,
            name: item.rawMaterial.name,
            itemSize: {
              id: item.rawMaterial.itemSize.id,
              name: item.rawMaterial.itemSize.name,
            },
            brand: {
              id: item.rawMaterial.brand?.id,
              name: item.rawMaterial.brand?.name,
              description: item.rawMaterial.brand?.description,
              imageUrl: item.rawMaterial.brand?.imageUrl,
            },
          },
        };
      }),
    },
    validationSchema:
      submitAction === 'draft' ? validationSchemaDraft : validationSchema,
    onSubmit: async (values: any) => {
      if (submitAction === 'validate') {
        handleOpenModalConfirm();
      } else {
        await savePo(values);
      }
    },
  });
  const onCloseModalConfirm = () => {
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
  };
  const handleOpenModalConfirm = () => {
    setModalConfirm({
      open: true,
      title: 'ขออนุมัติการสั่งซื้อ',
      description:
        'คุณได้ติดต่อสอบถามตัวแทนจำหน่ายเกี่ยวกับจำนวนสินค้า\n' +
        'ที่กำลังจะสั่งซื้อและตรวจสอบความถูกต้องของใบสั่งซื้อ\n' +
        `เลขที่ “${purchaseOrderById.poNo}” เรียบร้อยแล้ว`,
    });
  };
  const getWarehouseList = async () => {
    const res = await apiWarehouse.getListOptions();
    if (!res.isError) {
      setWarehouseOptions(res.data);
    }
  };
  useEffect(() => {
    getWarehouseList();
  }, []);
  useEffect(() => {
    if (formik.values.taxId != null) {
      const newTotalSubtotal = formik.values.poOrderList.map(
        (item: any) =>
          item.quantity * item.priceUnit - item.quantity * item.discountUnit
      );
      let sumPriceTable = newTotalSubtotal.reduce(
        (accumulator: number, currentValue: number) =>
          accumulator + currentValue,
        0
      );

      if (formik.values.totalDiscount != null) {
        sumPriceTable -= formik.values.totalDiscount;
      }

      const selectedTax = taxOptions.find(
        (item: any) => item.id === formik.values.taxId
      );
      let totalSubtotal = sumPriceTable;
      let taxPrice = 0;

      switch (selectedTax?.id) {
        case 1:
          totalSubtotal = sumPriceTable / (1 + selectedTax.taxRate / 100);
          taxPrice = sumPriceTable - totalSubtotal;
          break;
        case 2:
          taxPrice = (sumPriceTable / 100) * selectedTax.taxRate;
          break;
        case 3:
          break;
        default:
          console.log('ไม่มีในระบบ');
      }
      formik.setFieldValue('totalSubtotal', totalSubtotal);
      formik.setFieldValue('totalTaxPrice', taxPrice);
      formik.setFieldValue(
        'totalPrice',
        totalSubtotal + taxPrice + formik.values.shippingPrice
      );
    }
  }, [
    formik.values.poOrderList,
    formik.values.taxId,
    formik.values.shippingPrice,
    formik.values.totalDiscount,
  ]);
  const handleChangeTax = (value: any) => {
    formik.setFieldValue('taxId', value);
  };
  const handleChangePoDate = (date: string) => {
    const dateWithoutTime = moment.utc(date, 'DD/MM/YYYY');
    const unixTime = dateWithoutTime.unix();
    const unixTimeMs = unixTime * 1000;
    formik.setFieldValue('poDate', unixTimeMs);
  };

  const handleImportMaterial = (pushData: any) => {
    const newList = formik.values.poOrderList.concat(pushData);
    formik.setFieldValue('poOrderList', newList);
  };

  const handleChangeWarehouse = (idx: number, value: number) => {
    formik.setFieldValue(`poOrderList[${idx}].stockId`, value);
  };
  const handleChangeQuantity = (idx: number, value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');

    const totalPrice =
      Number(newValue) * formik.values.poOrderList[idx].priceUnit -
      Number(newValue) * formik.values.poOrderList[idx].discountUnit;

    formik.setFieldValue(`poOrderList[${idx}].quantity`, Number(newValue));
    formik.setFieldValue(`poOrderList[${idx}].totalPrice`, totalPrice);
  };

  const handleChangePricePerUnit = (idx: number, value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');

    const totalPrice =
      formik.values.poOrderList[idx].quantity * Number(newValue) -
      formik.values.poOrderList[idx].quantity *
        formik.values.poOrderList[idx].discountUnit;
    if (formik.values.poOrderList[idx].discountUnit > Number(newValue)) {
      formik.setFieldValue(`poOrderList[${idx}].discountUnit`, 0);
    }
    formik.setFieldValue(`poOrderList[${idx}].priceUnit`, Number(newValue));
    formik.setFieldValue(`poOrderList[${idx}].totalPrice`, totalPrice);
  };
  const handleChangeDiscountPerUnit = (idx: number, value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');

    if (Number(newValue) <= formik.values.poOrderList[idx].priceUnit) {
      const totalPrice =
        formik.values.poOrderList[idx].quantity *
          formik.values.poOrderList[idx].priceUnit -
        formik.values.poOrderList[idx].quantity * Number(newValue);

      formik.setFieldValue(
        `poOrderList[${idx}].discountUnit`,
        Number(newValue)
      );
      formik.setFieldValue(`poOrderList[${idx}].totalPrice`, totalPrice);
    }
  };
  const handleChangeDiscountBaht = async (value: number) => {
    const valueAsString = value.toString().replace(/^0+/, '');

    const totalPriceArray = await formik.values.poOrderList.map(
      (item: any) =>
        item.quantity * item.priceUnit - item.quantity * item.discountUnit
    );

    const totalPrice = await totalPriceArray.reduce(
      (acc: number, current: number) => acc + current,
      0
    );
    if (Number(valueAsString) <= totalPrice) {
      await formik.setFieldValue('totalDiscount', Number(valueAsString));
      await formik.setFieldValue('totalDiscountRate', null);
    }
  };

  useEffect(() => {
    if (formik.values.totalDiscountRate) {
      handleChangeDiscountPercent(formik.values.totalDiscountRate);
    }
  }, [formik.values]);
  const handleChangeDiscountPercent = (value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');
    const allPriceInTable = formik.values.poOrderList.map(
      (item: any) =>
        item.quantity * item.priceUnit - item.quantity * item.discountUnit
    );
    const sumPriceTable = allPriceInTable.reduce(
      (accumulator: number, currentValue: number) => accumulator + currentValue,
      0
    );
    const totalDiscount = (sumPriceTable * Number(newValue)) / 100;
    formik.setFieldValue('totalDiscount', totalDiscount);
    formik.setFieldValue('totalDiscountRate', Number(newValue));
  };
  const handleChangeShippingPrice = (value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');
    formik.setFieldValue('shippingPrice', Number(newValue));
  };
  const handleChangeRemark = (value: string) => {
    formik.setFieldValue('description', value);
  };
  const handleRemoveRawMaterial = (idx: number) => {
    formik.setFieldValue(
      'poOrderList',
      formik.values.poOrderList.filter(
        (item: any, index: number) => index !== idx
      )
    );
  };
  const updatePurchaseOrder = async (apiFunction: any, sendData: any) => {
    const res = await apiFunction(sendData);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกสำเร็จ',
          severity: 'success',
        })
      );
      if (submitAction === 'draft') {
        await router.push(
          `/stock/purchase-order?poOrderStatusId=${purchaseOrderById.poOrderStatus.id}`
        );
      } else {
        await router.push(
          `/stock/purchase-order?poOrderStatusId=${
            purchaseOrderById.poOrderStatus.id + 1
          }`
        );
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };
  const savePo = async (values: any) => {
    const sendData = {
      ...values,
      contactId: !isEmpty(newContactData)
        ? newContactData.id
        : values.contactId,
    };
    setLoadingSubmit({
      ...loadingSubmit,
      [submitAction]: true,
    });
    if (submitAction === 'submit') {
      await updatePurchaseOrder(
        apiPurchaseOrder.updatePurchaseOrderNextStep,
        sendData
      );
    } else if (submitAction === 'draft') {
      await updatePurchaseOrder(apiPurchaseOrder.updatePurchaseOrder, sendData);
    }
    setLoadingSubmit({
      ...loadingSubmit,
      [submitAction]: false,
    });
  };
  const handleChangeCreditDay = (value: string) => {
    const newValue = value.replace(/^0+/, '');
    if (Number(newValue) <= 365) {
      if (Number(newValue) !== 0) {
        formik.setFieldValue('creditDay', Number(newValue));
      } else {
        formik.setFieldValue('creditDay', newValue);
      }
      const expDate = dayjs(formik.values.poDate)
        .add(Number(value), 'day')
        .format('DD/MM/YYYY');
      const dateWithoutTime = moment.utc(expDate, 'DD/MM/YYYY');
      const unixTime = dateWithoutTime.unix();
      const unixTimeMs = unixTime * 1000;
      formik.setFieldValue('expireDate', unixTimeMs);
    }
  };
  useEffect(() => {
    if (isNull(formik.values.expireDate)) {
      const expDate = dayjs(formik.values.poDate)
        .add(formik.values.creditDay, 'day')
        .format('DD/MM/YYYY');
      const dateWithoutTime = moment.utc(expDate, 'DD/MM/YYYY');
      const unixTime = dateWithoutTime.unix();
      const unixTimeMs = unixTime * 1000;
      formik.setFieldValue('expireDate', unixTimeMs);
    }
  }, []);
  return (
    <>
      <AppModalConfirm
        open={modalConfirm.open}
        onClickClose={() => {
          onCloseModalConfirm();
        }}
        confirmTitle={modalConfirm.title}
        confirmDescription={modalConfirm.description}
        loadingConfirm={loadingSubmit.submit}
        onConfirm={async () => {
          onCloseModalConfirm();
          await setSubmitAction('submit');
          await formik.submitForm();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="382px"
      />
      <HonDatePicker
        open={openCalendar}
        dateValue={moment(formik.values.poDate).format('MM/DD/YYYY')}
        handleChangeDate={(date: string) => {
          handleChangePoDate(date);
        }}
        handleOpen={(value: any) => setOpenCalendar(value)}
      />
      <PurchaseOrderFormStyled>
        <div className="sheet-header">
          <div className="qt-code">
            {purchaseOrderById.poNo} <div className="no">เลขที่</div>
          </div>
        </div>
        <div className="personal-wrap">
          <div className="left">
            <div className="title">ข้อมูลใบสั่งซื้อ</div>
            <div className="card-date">
              <div className="list">
                <div className="key">วันที่สั่งซื้อ</div>
                <div className="value">
                  {moment(formik.values.poDate).format('DD/MM/YYYY')}
                  <div
                    className={`icon`}
                    onClick={() => {
                      setOpenCalendar(true);
                    }}
                  >
                    <Image
                      src="/icons/icon-calendar.svg"
                      width={24}
                      height={24}
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <div className="list">
                <div className="key">เครดิต</div>
                <TextField
                  type="number"
                  name="creditDay"
                  placeholder="0"
                  value={formik.values.creditDay}
                  onChange={(e: any) => {
                    handleChangeCreditDay(e.target.value);
                  }}
                  onKeyDown={(e: any) => {
                    if (e.key === '-') {
                      e.preventDefault();
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                  }}
                  sx={{
                    width: '280px',
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment className="cursor-pointer" position="end">
                        <span>วัน</span>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="list">
                <div className="key">ครบกำหนด</div>
                <div className="value">
                  {dayjs(formik.values.poDate)
                    .add(formik.values.creditDay, 'day')
                    .format('DD/MM/YYYY')}
                  <div className="icon no-action">
                    <Image
                      src="/icons/icon-calendar.svg"
                      width={24}
                      height={24}
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <div className="list">
                <div className="key">ประเภทภาษี</div>
                <FormControl>
                  <Select
                    value={
                      !isNull(formik.values.taxId) ? formik.values.taxId : ''
                    }
                    onChange={(e: any) => {
                      handleChangeTax(e.target.value);
                    }}
                    displayEmpty
                    sx={{
                      fontSize: '14px',
                      fontWeight: '600',
                      height: '40px',
                      width: '280px',
                    }}
                  >
                    <MenuItem
                      disabled
                      value=""
                      sx={{
                        fontSize: '14px',
                        fontWeight: '600',
                      }}
                    >
                      <div className="text-[#78909C]">เลือกประเภทภาษี</div>
                    </MenuItem>
                    {taxOptions.map((item: any, index: React.Key) => (
                      <MenuItem
                        key={index}
                        value={item.id}
                        sx={{
                          fontSize: '14px',
                          fontWeight: '600',
                        }}
                      >
                        {item.nameTax}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>
          </div>
          <div className="right">
            <div className="title">ตัวแทนจำหน่าย</div>
            <div className="address-wrap">
              <div className="contact-bar">
                <Image
                  src={
                    newContactData.imageUrl ||
                    purchaseOrderById.contact.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={24}
                  height={24}
                  alt=""
                />
                {newContactData.name || purchaseOrderById.contact.name}
                <ModalCreatePurchaseOrder
                  isChangeDealer={true}
                  makeSelectedContactData={(contactData: any) => {
                    setNewContactData(contactData);
                  }}
                >
                  <div className="change" onClick={(_event: any) => {}}>
                    เปลี่ยน
                  </div>
                </ModalCreatePurchaseOrder>
              </div>
              <div className="address">
                <>
                  <div className="name">
                    {newContactData.name || purchaseOrderById.contact.name}
                  </div>
                  <div className="address-text">
                    <div>
                      {newContactData.contactType?.name ||
                        purchaseOrderById.contact.contactType.name}{' '}
                      • เลขประจำตัวผู้เสียภาษี •{' '}
                      {!isEmpty(newContactData)
                        ? newContactData.taxNumber
                        : purchaseOrderById.contact.taxNumber}
                    </div>
                    <div>
                      โทร.{' '}
                      {newContactData.phoneNumber ||
                        purchaseOrderById.contact.phoneNumber}{' '}
                      •{' '}
                      {newContactData.email || purchaseOrderById.contact.email}{' '}
                    </div>
                    {!isEmpty(newContactData)
                      ? newContactData.taxAddress
                      : purchaseOrderById.contact.contactAddress.Address}{' '}
                    ต.
                    {!isEmpty(newContactData)
                      ? newContactData.subDistrict.name
                      : purchaseOrderById.contact.contactAddress.subDistrict
                          .name}{' '}
                    อ.
                    {!isEmpty(newContactData)
                      ? newContactData.district.name
                      : purchaseOrderById.contact.contactAddress.district
                          .name}{' '}
                    จ.
                    {!isEmpty(newContactData)
                      ? newContactData.province.name
                      : purchaseOrderById.contact.contactAddress.province
                          .name}{' '}
                    {!isEmpty(newContactData)
                      ? newContactData.zipcode
                      : purchaseOrderById.contact.contactAddress.zipcode}
                  </div>
                </>
              </div>
            </div>
          </div>
        </div>
        <TableCreatePurchaseOrder
          rawMaterialItemTable={formik.values.poOrderList}
          handleChangeWarehouse={handleChangeWarehouse}
          warehouseOptions={warehouseOptions}
          handleChangeQuantity={handleChangeQuantity}
          handleChangePricePerUnit={handleChangePricePerUnit}
          handleChangeDiscountPerUnit={handleChangeDiscountPerUnit}
          handleRemoveRawMaterial={handleRemoveRawMaterial}
          formik={formik}
        />
        <SummarizeCreatePurchaseOrder
          handleImportMaterial={handleImportMaterial}
          warehouseOptions={warehouseOptions}
          bodyProps={formik.values}
          handleChangeDiscountBaht={handleChangeDiscountBaht}
          handleChangeDiscountPercent={handleChangeDiscountPercent}
          handleChangeShippingPrice={handleChangeShippingPrice}
          handleChangeRemark={handleChangeRemark}
          handleSubmit={async (action: string) => {
            await setSubmitAction(action);
            await formik.submitForm();
          }}
          loadingSubmit={loadingSubmit}
        />
      </PurchaseOrderFormStyled>
    </>
  );
};

export default PurchaseOrderForm;
