import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent, IconButton, Button } from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { ConfirmModalQuotationType } from '@/pages/accounting-finance/quotation/[quotationId]';
import styled from 'styled-components';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  data: ConfirmModalQuotationType;
  loading: boolean;
  width?: number;
};

const ModalQuotationConfirm = ({
  open,
  handleCloseModal,
  data,
  loading,
  width = 640,
}: Props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setIsSubmitting(false);
  }, [open]);

  useEffect(() => {
    setIsSubmitting(loading);
  }, [loading]);

  const submit = () => {
    setIsSubmitting(true);
    data.confirmAction();
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent className="p-0">
        <FormModalStyle $width={width}>
          <div className="content-wrap">
            <div
              className="header"
              style={{ transform: 'none', boxShadow: 'none' }}
            >
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <ConfirmModalContainer iconBg={data.iconBgColor || '#f5f7f8'}>
              <div className={'icon'}>{data.iconElement()}</div>
              <div className={'text'}>
                <div className={'title'}>{data.title}</div>
                <p className={'description'}>{data.description}</p>
              </div>
              <div className={`action`}>
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleCloseModal}
                >
                  <span>ยกเลิก</span>
                </Button>
                <LoadingButton
                  type="submit"
                  loading={isSubmitting}
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                    background: data?.confirmBackground || '#000',
                    color: data?.confirmColor || '#fff',
                  }}
                  fullWidth
                  onClick={submit}
                >
                  {(data && data.confirmLabel) || 'บันทึก'}
                </LoadingButton>
              </div>
            </ConfirmModalContainer>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalQuotationConfirm;

const ConfirmModalContainer = styled.div<{ iconBg: string }>`
  padding: 24px 28px 28px 28px;
  display: flex;
  flex-direction: column;
  row-gap: 28px;
  .icon {
    width: 100%;
    display: flex;
    justify-content: center;
    svg {
      width: 100px;
      height: 100px;
      background: ${({ iconBg }) => iconBg};
      border-radius: 50%;
      padding: 26px;
    }
  }
  .text {
    color: #263238;
    display: flex;
    flex-direction: column;
    .title {
      text-align: center;
      font-size: 28px;
      font-weight: 600;
    }
    .description {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      white-space: pre-line;
    }
  }
  .action {
    width: 100%;
    display: flex;
    justify-content: space-between;
    column-gap: 24px;
  }
`;
