import {
  Circular<PERSON><PERSON>ress,
  Dialog,
  Di<PERSON><PERSON>ontent,
  Icon<PERSON>utton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { AddCircle, Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import ActionButton from '@/components/ActionButton';
import Image from 'next/image';
import apiRawMaterial from '@/services/stock/raw-material';

const ModalImportRawMaterialContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 500px;
  min-height: 174px;
  align-items: center;
  .empty-contact {
    width: 282px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    row-gap: 16px;
    text-align: center;
    color: #cfd8dc;
    height: 400px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .item-wrap {
    min-height: 438px;
    max-height: 438px;
    width: 100%;
    overflow: auto;
    padding: 8px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }

      h4 {
        margin: 0;
        line-height: 1.3;
      }

      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }
`;

type ModalImportRawMaterialProps = {
  children: React.ReactNode;
  handleImportMaterial: (pushData: any) => void;
  warehouseOptions: any;
};
const initialFilters = {
  searchName: '',
};
const ModalImportRawMaterial = ({
  children,
  handleImportMaterial,
  warehouseOptions,
}: ModalImportRawMaterialProps) => {
  const [rawMaterialList, setRawMaterialList] = useState<any>([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [open, setOpen] = useState<boolean>(false);
  useEffect(() => {
    getRawMaterialList();
  }, [filters.searchName]);
  const getRawMaterialList = async () => {
    const res = await apiRawMaterial.getOptions(filters);
    if (res && !res.isError) {
      setRawMaterialList(res.data);
    }
  };
  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        searchName: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleOpen = () => {
    setFilters(initialFilters);
    setSearchInput('');
    setOpen(true);
  };
  const handleSelectMaterial = async (item: any) => {
    const pushData = {
      rawMaterialId: item.id,
      quantity: 0,
      priceUnit: 0,
      discountUnit: 0,
      totalPrice: 0,
      stockId: warehouseOptions[0].id || null,
      info: {
        rawMaterialNo: item.rawMaterialNo,
        name: item.name,
        brand: item.brand,
        itemSize: item.itemSize,
        side: item.subMaterialDetail.side,
      },
    };
    handleImportMaterial(pushData);
    handleClose();
  };

  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
        style={{
          width: 'fit-content',
        }}
      >
        {children}
      </div>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  เพิ่มรายการวัสดุ
                </div>
                <div className="x-close" onClick={() => setOpen(false)}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหา"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                    }}
                  />
                  <ModalImportRawMaterialContentStyled>
                    {isEmpty(rawMaterialList) && (
                      <div className="empty-contact">
                        <div className="flex flex-col gap-[4px]">
                          <h4>ไม่พบการวัสดุ</h4>
                          <div>ไม่มีการวัสดุ</div>
                        </div>
                        <div onClick={() => {}}>
                          <ActionButton
                            variant="outlined"
                            color="blueGrey"
                            icon={<AddCircle />}
                            text="เพิ่มรายการวัสดุ"
                            borderRadius={'20px'}
                          />
                        </div>
                      </div>
                    )}
                    {!isEmpty(rawMaterialList) && (
                      <div className="item-wrap">
                        {rawMaterialList.map((item: any, index: number) => (
                          <div
                            key={index}
                            className="item"
                            onClick={() => {
                              handleSelectMaterial(item);
                            }}
                          >
                            <Image
                              src={
                                item.imageUrl ||
                                '/images/product/empty-product.svg'
                              }
                              width={40}
                              height={40}
                              alt=""
                              style={{
                                borderRadius: '4px',
                              }}
                            />
                            <div className="">
                              <h4>{item.name}</h4>
                              <p>
                                <span>{item.brand?.name || '-'}</span>
                                {item.itemSize?.name && (
                                  <span>{`• ${item.itemSize.name}`}</span>
                                )}
                                {item.subMaterialDetail?.side && (
                                  <span>
                                    {`• ${item.subMaterialDetail.side} ด้าน`}
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ModalImportRawMaterialContentStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalImportRawMaterial;
