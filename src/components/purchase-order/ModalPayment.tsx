import React, { useEffect, useRef, useState } from 'react';
import {
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useFormik } from 'formik';
import * as yup from 'yup';
import dayjs, { Dayjs } from 'dayjs';
import {
  LocalizationProvider,
  MobileDateTimePicker,
} from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import utc from 'dayjs/plugin/utc';
import { isEmpty, isNull } from 'lodash';
import FilePicker from '@/components/FilePicker';
import 'dayjs/locale/th';

dayjs.extend(utc);
type ModalPaymentProps = {
  open: boolean;
  handleClose: () => void;
  savePayment: (value: any, file: any) => void;
  submittingPayment: boolean;
};

const validationSchema = yup.object({
  paymentDate: yup.string().required('กรุณาระบุวันที่ชำระเงิน'),
  paymentAmount: yup.string().required('กรุณากรอกยอดที่ชำระ'),
});

const ModalPayment = ({
  open,
  handleClose,
  savePayment,
  submittingPayment,
}: ModalPaymentProps) => {
  const wrapperRef = useRef(null);
  const formik = useFormik({
    initialValues: {
      paymentDate: '',
      paymentAmount: '',
      remark: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values: any) => {
      if (!isNull(file)) {
        savePayment(values, file);
      } else {
        setErrorFileMsg('กรุณาอัปโหลดไฟล์');
      }
    },
  });

  const [dateValue, setDateValue] = React.useState<Dayjs | null>(dayjs());
  const [file, setFile] = useState<any>(null);
  const [errorFileMsg, setErrorFileMsg] = useState<string>('');
  const handleChangePaymentAmount = (value: number) => {
    const valueAsString = value.toString();
    const newValue = valueAsString.replace(/^0+/, '');
    formik.setFieldValue(
      'paymentAmount',
      isEmpty(value) ? newValue : Number(newValue)
    );
  };
  const handleChangeDate = (value: Dayjs) => {
    setDateValue(value);
    const utcValue = value.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    formik.setFieldValue('paymentDate', utcValue);
  };
  useEffect(() => {
    if (open) {
      formik.resetForm();
      setDateValue(dayjs());
      const utcValue = dayjs().utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      formik.setFieldValue('paymentDate', utcValue);
    }
  }, [open]);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="th">
      <Dialog open={open}>
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={489}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เพิ่มการชำระเงิน</div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form onSubmit={formik.handleSubmit} style={{ rowGap: '0' }}>
                  <p>ยอดที่ชำระ</p>
                  <TextField
                    type="number"
                    name="paymentAmount"
                    placeholder="0"
                    value={formik.values.paymentAmount}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      if (Number(e.target.value) < 0) {
                        e.preventDefault();
                      } else {
                        handleChangePaymentAmount(Number(e.target.value));
                      }
                    }}
                    onKeyDown={(e: any) => {
                      if (e.key === '-') {
                        e.preventDefault();
                      }
                    }}
                    onPaste={(e) => {
                      e.preventDefault();
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">บาท</InputAdornment>
                      ),
                    }}
                    error={
                      formik.touched.paymentAmount &&
                      Boolean(formik.errors.paymentAmount)
                    }
                    helperText={
                      formik.touched.paymentAmount &&
                      formik.errors.paymentAmount
                    }
                  />
                  <p>หมายเหตุ</p>
                  <TextField
                    type="text"
                    name="remark"
                    placeholder="ระบุหมายเหตุ"
                    value={formik.values.remark}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.remark && Boolean(formik.errors.remark)
                    }
                    helperText={formik.touched.remark && formik.errors.remark}
                  />
                  <p>วันที่ชำระเงิน</p>
                  <MobileDateTimePicker
                    value={dateValue}
                    onChange={(value: any) => {
                      handleChangeDate(value);
                    }}
                  />
                  <p>หลักฐานการชำระเงิน</p>
                  <FilePicker
                    makeFile={(formData: any) => {
                      setFile(formData);
                    }}
                    errorFileMsg={errorFileMsg}
                    makeErrorFileMsg={(msg: string) => {
                      setErrorFileMsg(msg);
                    }}
                  />
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      disabled={submittingPayment}
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submittingPayment ? (
                        <CircularProgress size={20} />
                      ) : (
                        'ยืนยัน'
                      )}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ModalPayment;
