import {
  Ava<PERSON>,
  <PERSON>ton,
  CircularProgress,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { AddCircle, Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import ActionButton from '@/components/ActionButton';
import { useRouter } from 'next/router';
import apiContact from '@/services/core/contact';
import apiPurchaseOrder from '@/services/stock/purchaseOrder';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

export const ModalCreatePurchaseContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 500px;
  min-height: 174px;
  align-items: center;
  .empty-contact {
    width: 282px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    row-gap: 16px;
    text-align: center;
    color: #cfd8dc;
    height: 400px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .contact-item-wrap {
    min-height: 400px;
    max-height: 400px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .contact-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }
      h4 {
        margin: 0;
        line-height: 1.3;
      }
      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
        font-weight: 400;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }
`;

type ContactDialogProps = {
  children: React.ReactNode;
  handleReloadList?: () => void;
  isChangeDealer?: boolean;
  makeSelectedContactData?: (contactData: any) => void;
};
const initialFilters = {
  search: '',
  contactRoleId: 2,
};
const ModalCreatePurchaseOrder = ({
  children,
  handleReloadList,
  isChangeDealer,
  makeSelectedContactData,
}: ContactDialogProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [contactList, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingCreatePurchaseOrder, setLoadingCreatePurchaseOrder] =
    useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [isShowContactModal, setShowContactModal] = useState(false);
  const [selectedContact, setSelectedContact] = useState<any>({});
  useEffect(() => {
    getContactOptions();
  }, [filters.search]);

  const getContactOptions = async () => {
    const res = await apiContact.getContactOptions(filters);
    if (res && !res.isError) {
      setContactList(res.data);
    }
  };
  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        search: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };

  const handleClose = () => {
    setShowContactModal(false);
  };
  const handleOpen = async () => {
    setContactList([]);
    setFilters(initialFilters);
    setSearchInput('');
    await getContactOptions();
    setShowContactModal(true);
  };

  const handleSelectContact = (item: any) => {
    if (selectedContact.id === item.id) {
      setSelectedContact(null);
    } else {
      setSelectedContact(item);
    }
  };
  const handleSubmit = async () => {
    setLoadingCreatePurchaseOrder(true);
    if (isChangeDealer && makeSelectedContactData) {
      makeSelectedContactData(selectedContact);
    } else {
      const res = await apiPurchaseOrder.createPurchaseOrder({
        contactId: selectedContact.id,
      });
      if (!res.isError) {
        if (handleReloadList) {
          await handleReloadList();
        }
        dispatch(
          setSnackBar({
            status: true,
            text: 'สร้างรายการสำเร็จ',
            severity: 'success',
          })
        );
        handleClose();
        await router.push(`/stock/purchase-order/${res.data.id}/edit/`);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    }
    setLoadingCreatePurchaseOrder(false);
    handleClose();
  };

  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  ตัวแทนจำหน่าย
                </div>
                <div
                  className="x-close"
                  onClick={() => setShowContactModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหาตัวแทนจำหน่าย"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                    }}
                  />
                  <ModalCreatePurchaseContentStyled>
                    {isEmpty(contactList) && (
                      <div className="empty-contact">
                        <div className="flex flex-col gap-[4px]">
                          <h4>ไม่พบตัวแทนจำหน่าย</h4>
                          <div>
                            ค้นหาตัวแทนจำหน่ายจากหมายเลขโทรศัพท์, ชื่อ-นามสกุล
                            ชื่อบริษัท หรือ เพิ่มตัวแทนจำหน่ายใหม่
                          </div>
                        </div>
                        <div
                          onClick={() => {
                            router.push('/company/contact');
                          }}
                        >
                          <ActionButton
                            variant="outlined"
                            color="blueGrey"
                            icon={<AddCircle />}
                            text="เพิ่มตัวแทนจำหน่าย"
                            borderRadius={'20px'}
                          />
                        </div>
                      </div>
                    )}
                    {!isEmpty(contactList) && (
                      <div className="contact-item-wrap">
                        {contactList.map((item: any, index: number) => (
                          <div
                            key={index}
                            className={`contact-item ${
                              selectedContact.id === item.id ? 'active' : ''
                            }`}
                            onClick={() => {
                              handleSelectContact(item);
                            }}
                          >
                            <Avatar
                              src={item.imageUrl}
                              sx={{
                                height: '40px',
                                width: '40px',
                              }}
                            />
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'column',
                                rowGap: '2px',
                              }}
                            >
                              <h4>{item.name}</h4>
                              <p>
                                <span>{item.contactType.name}</span>
                                {!isEmpty(item.phoneNumber) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.phoneNumber}</span>
                                  </>
                                )}
                                {!isEmpty(item.email) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.email}</span>
                                  </>
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    <Button
                      type="button"
                      variant="contained"
                      color="dark"
                      fullWidth
                      disabled={isEmpty(selectedContact)}
                      onClick={handleSubmit}
                      sx={{
                        fontSize: '16px',
                      }}
                    >
                      {loadingCreatePurchaseOrder ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        <div>{isChangeDealer ? 'เปลี่ยน' : 'สร้าง'}</div>
                      )}
                    </Button>
                  </ModalCreatePurchaseContentStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalCreatePurchaseOrder;
