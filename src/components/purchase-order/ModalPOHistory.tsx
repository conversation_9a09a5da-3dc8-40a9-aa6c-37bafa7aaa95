import React, { useState } from 'react';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import styled from 'styled-components';
import { Button } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { JobDataTable } from '@/styles/job-data-table.styled';
import AppPagination from '@/components/global/AppPagination';
import Image from 'next/image';

const ModalPOHistoryStyles = styled.div`
  min-width: 1200px;
  max-width: 1200px;
  header {
    border-bottom: 1px solid #dbe2e5;
    position: relative;
    button {
      min-width: unset;
      position: absolute;
      top: 12px;
      right: 10px;
      z-index: 5;
    }
    h2 {
      text-align: center;
    }
  }
  .box-data-panel {
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    .data {
      max-width: 50%;
      width: 100%;
      padding: 16px 32px;
      border-right: 1px solid #dbe2e5;
      &:last-child {
        border-right: none;
      }
      &.logo {
        display: flex;
        align-items: center;
        gap: 1rem;
      }
      .key {
        font-size: 12px;
      }
      .value {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
`;
type Props = {
  open: boolean;
  handleClose: () => void;
};
const ModalPOHistory = ({ open, handleClose }: Props) => {
  const [filters, setFilters] = useState<any>({
    page: 0,
    size: 5,
  });
  const [rows] = useState<any>([]);
  const columns = [
    {
      field: 'date',
      headerName: 'วันที่สั่งซื้อ',
      headerAlign: 'left',
      flex: 2,
      align: 'left',
      width: 80,
    },
    {
      field: 'no',
      headerName: 'เลขที่',
      minWidth: 150,
      flex: 0.5,
    },
    {
      field: 'amount',
      headerName: 'จำนวน',
      minWidth: 150,
      flex: 0.5,
    },
    {
      field: 'price',
      headerName: 'สราคา/หน่วย',
      flex: 0.5,
      minWidth: 150,
    },
    {
      field: 'discount',
      headerName: 'ส่วนสด/หน่วย',
      flex: 1,
      minWidth: 150,
    },
    {
      field: 'total',
      headerName: 'ราคารวม',
      flex: 0.5,
      minWidth: 130,
    },
    {
      field: 'paymentOption',
      headerName: 'ช่องทางการชำระเงิน',
      flex: 0.5,
      minWidth: 130,
    },
    {
      field: 'customer',
      headerName: 'ผู้สั่งซื้อ',
      flex: 0.5,
      minWidth: 130,
    },
  ];
  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <ModalPOHistoryStyles>
          <header>
            <DialogTitle>ประวัติการสั่งซื้อ</DialogTitle>
            <Button onClick={handleClose}>
              <CloseRoundedIcon />
            </Button>
          </header>
          <JobDataTable>
            <div className="detail">
              <div className={'box-data-panel'}>
                <div className={'data'}>
                  <div className={'key'}>รายการสินค้า</div>
                  <div className={'value'}>
                    กระดาษคราฟต์ 250 แกรม • 1 ด้าน • A-123 • Starlight
                  </div>
                </div>
                <div className={'data logo'}>
                  <Image
                    className={'rounded-full'}
                    src={'/images/product/empty-product.svg'}
                    alt={'logo'}
                    width={35}
                    height={35}
                  />
                  <div>
                    <div className={'key'}>ตัวแทนจำหน่าย</div>
                    <div className={'value'}>บริษัท อิมแพลนเทียม จำกัด</div>
                  </div>
                </div>
              </div>
              <div className={'box-data-table mt-0'}>
                <AppTableStyle $rows={rows}>
                  <div className="content-wrap">
                    <ScrollBarStyled>
                      {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
                      <DataGrid
                        hideFooter={true}
                        rows={rows}
                        columns={columns as any}
                        paginationMode="server"
                        disableSelectionOnClick={false}
                        autoHeight={true}
                        components={{
                          NoRowsOverlay: () => <TableNoRowsOverlay />,
                          LoadingOverlay: () => <TableLoadingOverlay />,
                        }}
                      />
                    </ScrollBarStyled>
                  </div>
                </AppTableStyle>
              </div>
              <div className="px-[16px]">
                <AppPagination
                  filters={filters}
                  totalElements={0}
                  handleChangeFilters={(newValues: any) =>
                    setFilters(newValues)
                  }
                />
              </div>
            </div>
          </JobDataTable>
        </ModalPOHistoryStyles>
      </Dialog>
    </>
  );
};

export default ModalPOHistory;
