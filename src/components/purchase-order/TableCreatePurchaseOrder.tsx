import React, { useState } from 'react';
import {
  FormHelperText,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import styled from 'styled-components';
import { isEmpty, isNull } from 'lodash';
import { ScrollBarStyled } from '@/styles/share.styled';
import KebabTable from '@/components/KebabTable';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import ModalPOHistory from '@/components/purchase-order/ModalPOHistory';

const TableCreatePurchaseOrderStyled = styled.div`
  width: 100%;
  overflow: auto;
  thead {
    tr {
      th:last-child {
        position: absolute !important;
        right: 0;
        top: 0;
        height: 73px !important;
        &:before {
          content: '';
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 1) 100%
          );
          position: absolute;
          z-index: 1;
          height: 100%;
          left: -32px;
          padding: 0 16px;
          top: 0;
        }
      }
    }
  }
  tbody {
    tr {
      td {
        padding: 0.5rem !important;
      }
      td:last-child {
        position: absolute !important;
        right: 0;
        border-bottom: unset !important;
        &:before {
          content: '';
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 1) 100%
          );
          position: absolute;
          z-index: 1;
          height: 100%;
          left: -32px;
          padding: 0 16px;
          top: 0;
        }
      }
    }
  }
  fieldset {
    border-width: 1px !important;
  }
  .MuiFormHelperText-root {
    position: absolute;
    bottom: -24px;
  }
  .MuiTable-root {
    border-collapse: initial;
    border-spacing: initial;
    overflow: hidden;
    .MuiTableHead-root {
      .MuiTableRow-head {
        .MuiTableCell-head {
          font-weight: 400;
          white-space: nowrap;
          height: 64px;
          font-size: 14px;
        }
      }
    }
    .MuiTableCell-root {
      color: #263238;
      background: white;
      vertical-align: center;
      position: relative;
      font-size: 14px;
      padding: 24px 16px;
      &:first-child {
        text-align: center;
      }
      &:last-child {
        text-align: end;
      }

      .code {
        white-space: nowrap;
      }
      .material-wrap {
        display: flex;
        flex-direction: column;
        width: 318px;
        .name {
          font-weight: 600;
        }
        .spec {
          font-size: 12px;
        }
      }
      .sum {
        font-weight: 600;
      }
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.3s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
    .MuiTableBody-root {
      .table-space {
        height: 8px;
        padding: 0;
        .MuiTableCell-root {
          padding: 0;
          background: #f5f7f8;
        }
      }
      .MuiTableRow-root {
        &:last-child {
          .MuiTableCell-root {
            border-bottom: 0;
          }
        }
      }
      .blank {
        height: 72px;
        td {
          position: initial !important;
        }
      }
      .blank-text {
        position: absolute;
        top: 78%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #cfd8dc;
      }
    }
  }
`;
const TableCreatePurchaseOrderWrapStyled = styled.div`
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  margin-top: 40px;
  width: 100%;
  position: relative;
  @media screen and (max-width: 650px) {
    margin-top: 24px;
  }
`;

type TableCreatePurchaseOrderProps = {
  rawMaterialItemTable: any;
  handleChangeWarehouse: (index: number, value: number) => void;
  handleChangeQuantity: (index: number, value: number) => void;
  handleChangePricePerUnit: (index: number, value: number) => void;
  handleChangeDiscountPerUnit: (index: number, value: number) => void;
  handleRemoveRawMaterial: (index: number) => void;
  warehouseOptions: any;
  formik: any;
};

const TableCreatePurchaseOrder = ({
  rawMaterialItemTable,
  handleChangeWarehouse,
  warehouseOptions,
  handleChangeQuantity,
  handleChangePricePerUnit,
  handleChangeDiscountPerUnit,
  handleRemoveRawMaterial,
  formik,
}: TableCreatePurchaseOrderProps) => {
  const [openHistory, setOpenHistory] = useState<boolean>(false);
  return (
    <>
      <ScrollBarStyled>
        <ModalPOHistory
          open={openHistory}
          handleClose={() => setOpenHistory(false)}
        />
        <TableCreatePurchaseOrderWrapStyled>
          <TableCreatePurchaseOrderStyled>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>#</TableCell>
                  <TableCell>รหัส</TableCell>
                  <TableCell>ราการสินค้า</TableCell>
                  <TableCell>การจัดเก็บ</TableCell>
                  <TableCell>จำนวน</TableCell>
                  <TableCell>ราคา/หน่วย</TableCell>
                  <TableCell>ส่วนลด/หน่วย</TableCell>
                  <TableCell
                    sx={{
                      paddingRight: '100px !important',
                    }}
                  >
                    ราคารวม
                  </TableCell>
                  <TableCell>จัดการ</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow className="table-space">
                  {[...Array(9)].map((_, index) => (
                    <TableCell key={index} />
                  ))}
                </TableRow>
                {isEmpty(rawMaterialItemTable) ? (
                  <TableRow className="blank">
                    <TableCell>
                      <div className="blank-text">ไม่มีรายการ</div>
                    </TableCell>
                  </TableRow>
                ) : (
                  <>
                    {rawMaterialItemTable.map((item: any, index: number) => {
                      console.log(item);
                      return (
                        <TableRow key={index}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>
                            <div className="code">
                              {item.info.rawMaterialNo}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="material-wrap">
                              <div className="name">{item.info.name}</div>
                              <div className="spec">
                                {item.info.brand?.name
                                  ? `${item.info.brand.name} `
                                  : ''}
                                {item.info.itemSize?.name
                                  ? `• ${item.info.itemSize.name} `
                                  : ''}
                                {item.info.side
                                  ? `• ${item.info.side} ด้าน`
                                  : ''}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Select
                              value={!isNull(item.stockId) ? item.stockId : ''}
                              onChange={(e: any) => {
                                handleChangeWarehouse(index, e.target.value);
                              }}
                              displayEmpty
                              sx={{
                                fontSize: '14px',
                                height: '40px',
                              }}
                            >
                              <MenuItem
                                disabled
                                value=""
                                sx={{
                                  fontSize: '14px',
                                }}
                              >
                                <div className="text-[#78909C]">
                                  เลือกคลังสินค้า
                                </div>
                              </MenuItem>
                              {!isEmpty(warehouseOptions) &&
                                warehouseOptions.map(
                                  (item: any, index: React.Key) => (
                                    <MenuItem
                                      key={index}
                                      value={item.id}
                                      sx={{
                                        fontSize: '14px',
                                      }}
                                    >
                                      {item.name}
                                    </MenuItem>
                                  )
                                )}
                            </Select>
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              placeholder="0"
                              value={item.quantity || ''}
                              onChange={(e: any) => {
                                if (Number(e.target.value) < 0) {
                                  e.preventDefault();
                                } else {
                                  handleChangeQuantity(index, e.target.value);
                                }
                              }}
                              onKeyDown={(e: any) => {
                                if (e.key === '-') {
                                  e.preventDefault();
                                }
                              }}
                              onPaste={(e) => {
                                e.preventDefault();
                              }}
                              error={
                                formik.touched.poOrderList &&
                                formik.touched.poOrderList[index] &&
                                Boolean(
                                  formik.errors.poOrderList &&
                                    formik.errors.poOrderList[index]?.quantity
                                )
                              }
                              helperText={
                                formik.touched.poOrderList &&
                                formik.touched.poOrderList[index] &&
                                formik.errors.poOrderList &&
                                formik.errors.poOrderList[index]?.quantity
                              }
                              sx={{
                                width: '88px',
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              placeholder="0"
                              value={item.priceUnit || ''}
                              onChange={(e: any) => {
                                if (Number(e.target.value) < 0) {
                                  e.preventDefault();
                                } else {
                                  handleChangePricePerUnit(
                                    index,
                                    e.target.value
                                  );
                                }
                              }}
                              onKeyDown={(e: any) => {
                                if (e.key === '-') {
                                  e.preventDefault();
                                }
                              }}
                              onPaste={(e) => {
                                e.preventDefault();
                              }}
                              error={
                                formik.touched.poOrderList &&
                                formik.touched.poOrderList[index] &&
                                Boolean(
                                  formik.errors.poOrderList &&
                                    formik.errors.poOrderList[index]?.priceUnit
                                )
                              }
                              helperText={
                                formik.touched.poOrderList &&
                                formik.touched.poOrderList[index] &&
                                formik.errors.poOrderList &&
                                formik.errors.poOrderList[index]?.priceUnit
                              }
                              sx={{
                                width: '88px',
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              placeholder="0"
                              value={item.discountUnit || ''}
                              onChange={(e: any) => {
                                if (Number(e.target.value) < 0) {
                                  e.preventDefault();
                                } else {
                                  handleChangeDiscountPerUnit(
                                    index,
                                    e.target.value
                                  );
                                }
                              }}
                              onKeyDown={(e: any) => {
                                if (e.key === '-') {
                                  e.preventDefault();
                                }
                              }}
                              onPaste={(e) => {
                                e.preventDefault();
                              }}
                              sx={{
                                width: '100px',
                              }}
                            />
                          </TableCell>
                          <TableCell
                            sx={{
                              paddingRight: '88px !important',
                            }}
                          >
                            <div className="sum">
                              {(
                                item.quantity * item.priceUnit -
                                item.quantity * item.discountUnit
                              ).toFixed(2)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex w-full justify-end">
                              <KebabTable
                                item={''}
                                iconHorizonDot={<MoreHorizRoundedIcon />}
                                isHistoryPO={{
                                  status: true,
                                  action: () => setOpenHistory(true),
                                }}
                                isDelete={{
                                  status: true,
                                  action: () => handleRemoveRawMaterial(index),
                                }}
                              />

                              {/* <div */}
                              {/*  className="remove-btn" */}
                              {/*  onClick={() => { */}
                              {/*    handleRemoveRawMaterial(index); */}
                              {/*  }} */}
                              {/* > */}
                              {/*  <Image */}
                              {/*    src="/icons/delete-white.svg" */}
                              {/*    width={24} */}
                              {/*    height={24} */}
                              {/*    alt="" */}
                              {/*  /> */}
                              {/* </div> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </>
                )}
              </TableBody>
            </Table>
          </TableCreatePurchaseOrderStyled>
        </TableCreatePurchaseOrderWrapStyled>
      </ScrollBarStyled>

      <FormHelperText
        error
        sx={{
          marginTop: '8px',
        }}
      >
        {formik.touched.poOrderList &&
        formik.errors.poOrderList &&
        Array.isArray(formik.errors.poOrderList)
          ? ''
          : formik.errors.poOrderList}
      </FormHelperText>
    </>
  );
};

export default TableCreatePurchaseOrder;
