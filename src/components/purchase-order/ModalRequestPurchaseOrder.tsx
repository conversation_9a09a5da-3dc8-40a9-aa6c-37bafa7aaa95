import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormHelperText,
  Radio,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import { Check } from '@mui/icons-material';
import { RadioStyle } from '@/components/raw-material/RawMaterialForm';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import { CreateJobModalStyled } from '@/styles/styledComponents/CreateJobModal.styled';
import dayjs from 'dayjs';

const validationSchema = yup.object({
  prOrderId: yup.number().required('กรุณาเลือก'),
});

type Props = {
  children: React.ReactNode;
  onCreatePurchaseOrder: (data: any) => void;
};
const ModalRequestPurchaseOrder = ({
  children,
  onCreatePurchaseOrder,
}: Props) => {
  const [filters] = useState<any>({
    status: 2,
    isPoOrder: false,
  });
  const [open, setOpen] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [dataList, setDataList] = useState<any[]>([]);
  const handleClose = () => {
    setOpen(false);
  };
  const {
    // register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors, isDirty },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      prOrderId: null,
    },
  } as any);
  const handleClickSelector = (prOrderId: number) => {
    setValue('prOrderId', prOrderId);
  };
  const getListPrOrder = async (params: any) => {
    const res = await apiPurchaseRequest.getListPrOrder(params);
    if (!res.isError) {
      console.log(res.data);
      setDataList(res.data);
    }
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    console.log('values', values);
    onCreatePurchaseOrder(values);
    handleClose();
    reset();
    setSubmitting(false);
  };

  useEffect(() => {
    getListPrOrder(filters);
  }, [filters]);
  return (
    <>
      <div onClick={() => setOpen(true)}>{children}</div>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
          reset();
        }}
      >
        <DialogContent>
          <FormModal
            title={`เลือกรายการขอซื้อ`}
            handleClose={() => {
              handleClose();
              reset();
            }}
            width={520}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <CreateJobModalStyled className={'box-request-purchase-order'}>
                {!isEmpty(dataList) && (
                  <div className="count">{dataList.length} รายการขอซื้อ</div>
                )}

                {!isEmpty(dataList) ? (
                  dataList.map((item, index) => {
                    return (
                      <div
                        className={`selector`}
                        key={index}
                        onClick={() => {
                          handleClickSelector(index + 1);
                        }}
                      >
                        {watch('prOrderId') === index + 1 && (
                          <div className="selected" />
                        )}
                        <div className="header-create-job-modal">
                          <div className="left-side">
                            <div className="image">
                              <Image
                                src={'/images/product/empty-product.svg'}
                                width={120}
                                height={120}
                                alt=""
                              />
                            </div>
                            <div className="text-group">
                              <div className="text-top">{item.prOrderNo}</div>
                              <div className="text-bottom">
                                {/* <div className="user"> */}
                                {/*  <Image */}
                                {/*    src={ */}
                                {/*      item?.imageUrl || */}
                                {/*      '/images/product/empty-product.svg' */}
                                {/*    } */}
                                {/*    width={40} */}
                                {/*    height={40} */}
                                {/*    alt="" */}
                                {/*  /> */}
                                {/* </div> */}
                                <span className="od-code">
                                  {item.contact.name} •{' '}
                                  {item.contact.contactType.name}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="right-side">
                            <Radio
                              icon={<Check />}
                              checkedIcon={<Check className={'p-1'} />}
                              sx={RadioStyle}
                              checked={watch('prOrderId') === index + 1}
                              value={index + 1}
                            />
                          </div>
                        </div>
                        <div className="detail-wrap">
                          <div className="detail">
                            <div className="key">
                              <Image
                                src={'/icons/ic-calendar.svg'}
                                alt={'icon'}
                                width={15}
                                height={15}
                              />
                              รายการขอซื้อ
                            </div>
                            <div className="value">{item.countPrList}</div>
                          </div>
                          <div className="detail">
                            <div className="key">
                              <Image
                                src={'/icons/ic-calendar.svg'}
                                alt={'icon'}
                                width={15}
                                height={15}
                              />
                              วันที่ต้องการสินค้า
                            </div>
                            <div className="value">
                              {item?.requestedDeliveryDate
                                ? dayjs(item.requestedDeliveryDate).format(
                                    'DD/MM/YYYY HH:mm'
                                  )
                                : '-'}
                            </div>
                          </div>
                          <div className="detail">
                            <div className="key">
                              <Image
                                src={'/icons/ic-calendar.svg'}
                                alt={'icon'}
                                width={15}
                                height={15}
                              />
                              ต้องการภายในเวลา
                            </div>
                            <div className="value">-</div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className={'text-not-found'}>ไม่พบรายการขอซื้อ</div>
                )}
                {hookFormErrors.prOrderId && (
                  <FormHelperText error>
                    {hookFormErrors.prOrderId.message as ReactNode}
                  </FormHelperText>
                )}
              </CreateJobModalStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    handleClose();
                    reset();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  fullWidth
                  disabled={!isDirty && !watch('prOrderId')}
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'สร้าง'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalRequestPurchaseOrder;
