import React, { useState } from 'react';
import { Menu, MenuItem } from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/router';
import TaskAltRoundedIcon from '@mui/icons-material/TaskAltRounded';
import SettingsIcon from '@mui/icons-material/Settings';
import RestoreRoundedIcon from '@mui/icons-material/RestoreRounded';
import { KebabMenu } from '@/types/kebab-menu';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import RestoreIcon from '@mui/icons-material/Restore';

type KebabProps = KebabMenu & {
  item: any;
  handleRemove?: (item: any) => void;
  handleEdit?: (item: any) => void;
  isEdit?: any;
  isRemove?: boolean;
  isImportItem?: boolean;
  isTransferItem?: boolean;
  isProductConfig?: boolean;
  isDetail?: boolean | any;
  handleClickDetail?: (data: any) => void;
  handleConfirmArtwork?: () => void;
  handleConfirmExample?: () => void;
  handleReportEditArtwork?: () => void;
  handleCancelPr?: () => void;
  handleReject?: () => void;
  iconHorizonDot?: React.ReactNode;
  handleReplyEdit?: () => void;
  handleCancelInvoice?: () => void;
  handleRejectInvoice?: () => void;
};
const KebabTable = ({
  item,
  handleRemove,
  handleEdit,
  isEdit,
  isRemove,
  isImportItem,
  isTransferItem,
  isCreateProduct,
  isProductConfig,
  isDetail,
  isCancel,
  handleClickDetail,
  handleConfirmArtwork,
  handleConfirmExample,
  handleReportEditArtwork,
  handleCancelPr,
  handleReject,
  isMeatBall,
  isCancelPr,
  isHistory,
  isReportLayout,
  isHowTo,
  isCustom,
  customText,
  iconHorizonDot,
  isStop,
  isReject,
  isReplyEdit,
  isRejectInvoice,
  isCancelInvoice,
  isDetailOrder,
  handleReplyEdit,
  handleCancelInvoice,
  handleRejectInvoice,
  isDelete,
  isHistoryPO,
  isApprove,
  isNotApproved,
}: KebabProps) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const router = useRouter();
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <div>
      <div className="kebab-wrap" onClick={handleClick}>
        {iconHorizonDot ? (
          <div className={'flex items-center justify-center'}>
            {iconHorizonDot}
          </div>
        ) : (
          <div
            style={{
              rotate: isMeatBall ? '180deg' : 'none',
            }}
          >
            <div className="kebab">
              <div className="dot" />
            </div>
          </div>
        )}
      </div>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        sx={{
          marginTop: '12px',
          '.MuiList-root': {
            padding: '8px',
            minWidth: '150px',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            rowGap: '4px',
          },
          li: {
            width: 'auto',
          },
        }}
      >
        {isApprove?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={async () => isApprove?.action && isApprove?.action()}
            >
              <Image
                src={'/icons/ic_approve.svg'}
                width={24}
                height={24}
                alt=""
              />
              อนุมัติใบพิมพ์
            </div>
          </MenuItem>
        )}
        {isNotApproved?.status && (
          <MenuItem
            disabled={isNotApproved?.disabled || false}
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FDE8EF',
              },
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
              style={{ color: '#D32F2F' }}
            >
              <Image
                src={'/icons/ic_not_cancel.svg'}
                width={24}
                height={24}
                alt=""
              />
              ไม่อนุมัติใบพิมพ์
            </div>
          </MenuItem>
        )}
        {isCancelPr && <div></div>}
        {isStop?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div className="drop-menu" onClick={isStop?.action}>
              <Image
                src={'/icons/ic_pause_circle.svg'}
                width={24}
                height={24}
                alt=""
              />
              พักงานผลิต
            </div>
          </MenuItem>
        )}
        {isReportLayout && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <Image
                src={'/icons/ic-draft.svg'}
                width={24}
                height={24}
                alt=""
              />
              แจ้งแก้ไขเลย์เอาท์
            </div>
          </MenuItem>
        )}
        {isReplyEdit && (
          <MenuItem
            onClick={handleReplyEdit}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <Image
                src={'/icons/icon-reply-all.svg'}
                width={24}
                height={24}
                alt=""
              />
              ตีกลับไปแก้ไข
            </div>
          </MenuItem>
        )}
        {isRejectInvoice && (
          <MenuItem
            onClick={handleRejectInvoice}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <CancelOutlinedIcon />
              ปฏิเสธใบแจ้งหนี้
            </div>
          </MenuItem>
        )}
        {isHistory?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div className="drop-menu" onClick={isHistory?.action}>
              <RestoreRoundedIcon style={{ fontSize: '24px' }} />
              ประวัติรายการ
            </div>
          </MenuItem>
        )}
        {isHowTo && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <InfoOutlinedIcon />
              วิธีใช้งาน
            </div>
          </MenuItem>
        )}
        {isCancelInvoice && (
          <MenuItem
            onClick={handleCancelInvoice}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <Image
                src={'/icons/icon-cancel-pr-button.svg'}
                width={24}
                height={24}
                alt=""
              />
              ยกเลิกรายการ
            </div>
          </MenuItem>
        )}
        {isCancel?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
              color: '#D32F2F',
              '&:hover': {
                backgroundColor: '#FDE8EF',
              },
            }}
          >
            <div className="drop-menu" onClick={isCancel?.action}>
              <Image
                src={'/icons/ic-cancel.svg'}
                width={24}
                height={24}
                alt=""
              />
              ยกเลิกรายการ
            </div>
          </MenuItem>
        )}
        {isCreateProduct && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                //
              }}
            >
              <AddRoundedIcon />
              สร้างใบสั่งผลิตสินค้า
            </div>
          </MenuItem>
        )}
        {isDetailOrder?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (isDetailOrder?.url) router.push(isDetailOrder?.url);
              }}
            >
              <DescriptionOutlinedIcon />
              รายละเอียด
            </div>
          </MenuItem>
        )}
        {isDetail && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (handleClickDetail) {
                  handleClickDetail(item);
                }
              }}
            >
              {/* <FormatListBulletedRoundedIcon style={{ fontSize: '24px' }} /> */}
              <Image
                src={'/icons/icon-detail.svg'}
                width={24}
                height={24}
                alt=""
              />
              รายละเอียด
              {/* <FormatListBulletedRoundedIcon style={{ fontSize: '24px' }} /> */}
              ประวัติรายการ
            </div>
          </MenuItem>
        )}
        {isCustom && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (handleClickDetail) {
                  handleClickDetail(item);
                }
              }}
            >
              {/* <FormatListBulletedRoundedIcon style={{ fontSize: '24px' }} /> */}
              {customText}
            </div>
          </MenuItem>
        )}
        {isImportItem && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div className="drop-menu" onClick={() => {}}>
              <Image
                src={'/icons/icon-move-down.svg'}
                width={24}
                height={24}
                alt=""
              />
              นำเข้าสินค้า
            </div>
          </MenuItem>
        )}
        {isTransferItem && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div className="drop-menu" onClick={() => {}}>
              <Image
                src={'/icons/icon-move-up.svg'}
                width={24}
                height={24}
                alt=""
              />
              โอนสินค้า
            </div>
          </MenuItem>
        )}
        {handleConfirmArtwork && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                handleConfirmArtwork();
              }}
            >
              <TaskAltRoundedIcon />
              ยืนยันอาร์ตเวิร์ก
            </div>
          </MenuItem>
        )}
        {handleConfirmExample && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                handleConfirmExample();
              }}
            >
              <TaskAltRoundedIcon />
              ยืนยันตัวอย่างสินค้า
            </div>
          </MenuItem>
        )}
        {handleReportEditArtwork && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                handleReportEditArtwork();
              }}
            >
              <Image
                src={'/icons/icon-edit-artwork.svg'}
                width={24}
                height={24}
                alt=""
              />
              แจ้งแก้ไขอาร์ตเวิร์ก
            </div>
          </MenuItem>
        )}
        {isEdit?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={async () => isEdit?.action && isEdit?.action()}
            >
              <Image
                src={'/icons/edit-black.svg'}
                width={24}
                height={24}
                alt=""
              />
              แก้ไข
            </div>
          </MenuItem>
        )}
        {isHistoryPO?.status && (
          <MenuItem onClick={handleClose}>
            <div
              className="drop-menu"
              onClick={async () => isHistoryPO?.action && isHistoryPO?.action()}
            >
              <RestoreIcon />
              ประวัติการสั่งซื้อ
            </div>
          </MenuItem>
        )}
        {isDelete?.status && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FDE8EF',
              },
            }}
          >
            <div
              style={{ color: '#D32F2F' }}
              className="drop-menu"
              onClick={async () => isDelete?.action && isDelete?.action()}
            >
              <Image
                src={'/icons/ic-bin-delete.svg'}
                width={24}
                height={24}
                alt=""
              />
              ลบรายการ
            </div>
          </MenuItem>
        )}
        {isProductConfig && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={async () => {
                if (handleEdit) {
                  handleEdit(item);
                } else {
                  await router.push(`/product/${item.id}/setting`);
                }
              }}
            >
              <SettingsIcon />
              Config
            </div>
          </MenuItem>
        )}
        {isRemove && (
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (handleRemove) {
                  handleRemove(item);
                }
              }}
            >
              <Image
                src={'/icons/delete-black.svg'}
                width={24}
                height={24}
                alt=""
              />
              ลบ
            </div>
          </MenuItem>
        )}
        {isCancel && (
          <MenuItem
            onClick={handleCancelPr}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (handleCancelPr) {
                  handleCancelPr();
                }
              }}
            >
              <Image
                src={'/icons/icon-cancel-pr-button.svg'}
                width={24}
                height={24}
                alt=""
              />
              ยกเลิกใบขอซื้อ
            </div>
          </MenuItem>
        )}
        {isReject && (
          <MenuItem
            onClick={handleReject}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                if (handleReject) {
                  handleReject();
                }
              }}
            >
              <Image
                src={'/icons/icon-cancel-pr-button.svg'}
                width={24}
                height={24}
                alt=""
              />
              ไม่อนุมัติ
            </div>
          </MenuItem>
        )}
      </Menu>
    </div>
  );
};

export default KebabTable;
