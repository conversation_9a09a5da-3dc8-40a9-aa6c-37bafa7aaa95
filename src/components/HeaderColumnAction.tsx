import React from 'react';
import styled, { css } from 'styled-components';

const HeaderColumnActionStyle = styled.div<{ $width: number }>`
  position: absolute;
  ${({ $width }) =>
    $width &&
    css`
      width: ${$width}px;
    `};
  z-index: 1;
  height: 48px;
  background: #f5f7f8;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: end;
  font-size: 12px;
  padding: 0 16px;
  &:before {
    content: '';
    background: linear-gradient(
      to right,
      rgba(245, 247, 248, 0) 0%,
      rgba(245, 247, 248, 1) 100%
    );
    position: absolute;
    z-index: 1;
    height: 100%;
    top: 0;
    ${({ $width }) =>
      $width &&
      css`
        right: ${$width}px;
      `};
    padding: 0 16px;
  }
`;
type HeaderColumnActionProps = {
  text: string;
  width: number;
};
const HeaderColumnAction = ({ text, width }: HeaderColumnActionProps) => {
  return (
    <HeaderColumnActionStyle $width={width}>{text}</HeaderColumnActionStyle>
  );
};

export default HeaderColumnAction;
