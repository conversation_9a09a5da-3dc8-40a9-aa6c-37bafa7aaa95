import { FilterRoleStyled } from '@/styles/share.styled';
import {
  Collapse,
  InputAdornment,
  MenuItem,
  Select,
  Skeleton,
  TextField,
} from '@mui/material';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { Search } from '@mui/icons-material';
import SvgFilterIcon from '@/components/svg-icon/SvgFilterIcon';
import styled from 'styled-components';
import apiProductCategory from '@/services/stock/productCategory';
import apiProductType from '@/services/stock/productType';
import apiProduct from '@/services/stock/product';

type propsType = {
  onSelect: (product: any) => void;
};

const ProductGridSelect = ({ onSelect }: propsType) => {
  const [searchInput, setSearchInput] = useState<string>('');
  const [isFilter, setIsFilter] = useState<boolean>(false);
  const [categories, setCategories] = useState<any>([]);
  const [categorySlc, setCategorySlc] = useState<any>(0);
  const [productType, setProductType] = useState<any>([]);
  const [productTypeSlc, setProductTypeSlc] = useState<any>(0);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const handleSearchChange = (text: string) => {
    setSearchInput(text);
  };

  useEffect(() => {
    fetchCategory();
  }, []);

  const fetchCategory = async () => {
    const response: any = await apiProductCategory.getList();
    if (response?.status) {
      await setCategories(response?.data?.content);
    }
  };

  useEffect(() => {
    fetchProductType();
  }, [categorySlc]);

  const fetchProductType = async () => {
    if (categorySlc && categorySlc !== 0) {
      const response: any = await apiProductType.getListByCategoryId(
        categorySlc
      );
      if (response?.status) {
        await setProductType(response?.data);
      }
    } else {
      await setProductType([]);
      await setProductTypeSlc(0);
    }
  };

  useEffect(() => {
    fetchProduct();
  }, [productTypeSlc, searchInput]);

  const fetchProduct = async () => {
    await setLoading(true);
    const response: any = await apiProduct.getProductAllList({
      productTypeId: productTypeSlc || null,
      search: searchInput || null,
    });
    if (response?.status) {
      await setProducts(response?.data || []);
      await setTimeout(onDisableLoading, 1000);
    } else {
      await setTimeout(onDisableLoading, 1000);
    }
  };

  const onDisableLoading = () => {
    setLoading(false);
  };

  return (
    <>
      <div className="form-wrap data-grid-row-pointer">
        <FilterRoleStyled $openFilter={false}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search product"
            value={searchInput}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              handleSearchChange(e.target.value);
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            style={{
              height: '40px',
            }}
          />
          <FilterButton onClick={() => setIsFilter(!isFilter)}>
            <SvgFilterIcon />
          </FilterButton>
        </FilterRoleStyled>
      </div>
      <Collapse in={isFilter}>
        <FilterForm>
          <div className="filter-select">
            <div className={'filter-header'}>
              <div className={'start'}>หมวดหมู่</div>
              <div
                className={'end'}
                onClick={() => {
                  setCategorySlc(0);
                  setProductTypeSlc(0);
                  setProductType(null);
                }}
              >
                ล้าง
              </div>
            </div>
            <Select
              value={categorySlc}
              onChange={(e) => {
                setCategorySlc(e.target.value);
              }}
              fullWidth
              size="small"
              displayEmpty
            >
              <MenuItem value={0}>ทั้งหมด</MenuItem>
              {categories &&
                categories.map((item: any, index: React.Key) => (
                  <MenuItem key={index} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
            </Select>
          </div>
          <div className="filter-select">
            <div className={'filter-header'}>
              <div className={'start'}>ประเภท</div>
            </div>
            <Select
              value={productTypeSlc}
              onChange={(e) => {
                setProductTypeSlc(e.target.value);
              }}
              fullWidth
              size="small"
              displayEmpty
            >
              <MenuItem value={0}>ทั้งหมด</MenuItem>
              {productType &&
                productType.map((item: any, index: React.Key) => (
                  <MenuItem key={index} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
            </Select>
          </div>
        </FilterForm>
      </Collapse>
      <ProductGridContainer>
        {loading && (
          <>
            {Array(4)
              .fill('')
              .map((_: any, index) => {
                return (
                  <ProductGridItem key={index}>
                    <Skeleton variant="rounded" width={'w-full'} height={130} />
                    <Skeleton
                      variant="rounded"
                      height={20}
                      className={'mt-2 w-full'}
                    />
                  </ProductGridItem>
                );
              })}
          </>
        )}
        {products &&
          !loading &&
          products.map((pd: any, index) => {
            return (
              <ProductGridItem key={index} onClick={() => onSelect(pd)}>
                <img alt="" src={pd.imageUrl || '/images/digiboxs.svg'} />
                <div
                  className={'name'}
                >{`${pd.name} (${pd.productTypeName})`}</div>
              </ProductGridItem>
            );
          })}
      </ProductGridContainer>
      {products.length === 0 && !loading && (
        <div
          className={
            'h-[150px] bg-[#f5f7f8] text-[#cfd8dc] rounded w-full flex justify-center items-center'
          }
        >
          No Product
        </div>
      )}
    </>
  );
};

export default ProductGridSelect;

const FilterButton = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #dbe2e5;
  cursor: pointer;
`;

const FilterForm = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
  column-gap: 16px;
  margin-top: 16px;
  .filter-select {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    .filter-header {
      display: flex;
      justify-content: space-between;
      .start {
        font-size: 14px;
        font-weight: bold;
      }
      .end {
        font-size: 12px;
        color: #cfd8dc;
      }
    }
  }
  .MuiSelect-select {
    border: 1px solid #dbe2e5;
    border-radius: 8px;
  }
`;

const ProductGridContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 16px;
  margin-top: 24px;
  padding-bottom: 24px;
  @media screen and (max-width: 700px) {
    grid-template-columns: 1fr 1fr;
  }
  @media screen and (max-width: 545px) {
    grid-template-columns: 1fr;
  }
`;
const ProductGridItem = styled.div`
  cursor: pointer;
  img {
    width: 100%;
    aspect-ratio: 1/1;
    border-radius: 4px;
  }
  .name {
    text-align: center;
    font-size: 12px;
  }
`;
