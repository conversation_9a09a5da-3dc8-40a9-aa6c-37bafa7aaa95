import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiProductModel from '@/services/stock/productModel';
import _ from 'lodash';
import { Button, Checkbox, Skeleton } from '@mui/material';

type propsType = {
  productSlc: any;
  onSelect: (model: any[]) => void;
  currentModel: any[];
};

const ModelGridSelect = ({ productSlc, onSelect, currentModel }: propsType) => {
  const [models, setModels] = useState<any[]>([]);
  const [modelSlc, setModelSlc] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    fetchModel();
  }, [productSlc]);

  console.log('productSlc', productSlc);

  useEffect(() => {
    setModelSlc(currentModel);
  }, [currentModel]);

  const fetchModel = async () => {
    await setLoading(true);
    if (productSlc && productSlc?.id) {
      const response: any = await apiProductModel.getList(productSlc?.id);
      if (response?.status) {
        await setModels(response?.data);
        await setTimeout(onDisableLoading, 1000);
      }
    } else {
      await setModels([]);
      await setTimeout(onDisableLoading, 1000);
    }
  };

  const onDisableLoading = () => {
    setLoading(false);
  };

  const isModel = (id: number) => {
    return _.some(modelSlc, (o: any) => {
      return o.id === id;
    });
  };

  const handleSelectModel = async (model: any) => {
    if (isModel(model.id)) {
      const data: any = await _.xor(modelSlc, [model]);
      await setModelSlc(data);
    } else {
      await setModelSlc((prev: any) => [...prev, model]);
    }
  };

  const onInsert = async () => {
    if (modelSlc) {
      onSelect(modelSlc);
    }
  };

  const isCurrent = (id: number) => {
    return _.some(currentModel, (o: any) => {
      return o.id === id;
    });
  };

  return (
    <div className="form-wrap data-grid-row-pointer">
      <ProductGridContainer>
        {loading && (
          <>
            {Array(4)
              .fill('')
              .map((_: any, index) => {
                return (
                  <ProductGridItem key={index}>
                    <Skeleton variant="rounded" width={'w-full'} height={130} />
                    <Skeleton
                      variant="rounded"
                      className={'w-full'}
                      height={20}
                    />
                  </ProductGridItem>
                );
              })}
          </>
        )}
        {models &&
          !loading &&
          models.map((md: any, index) => {
            return (
              <ProductGridItem
                key={index}
                onClick={() => {
                  if (!isCurrent(md.id)) {
                    handleSelectModel(md);
                  }
                }}
                disable={isCurrent(md.id)}
              >
                <div className={`image ${isModel(md.id) ? 'active' : ''}`}>
                  <Checkbox
                    checked={isModel(md.id)}
                    sx={{
                      color: '#ececec',
                      '&.Mui-checked': {
                        color: '#000',
                      },
                    }}
                  />
                  <img alt="" src={md.imageUrl || '/images/digiboxs.svg'} />
                </div>
                <div className={'name'}>{`${md.name}`}</div>
              </ProductGridItem>
            );
          })}
      </ProductGridContainer>
      {models.length === 0 && !loading && (
        <div
          className={
            'h-[150px] bg-[#f5f7f8] text-[#cfd8dc] rounded w-full flex justify-center items-center'
          }
        >
          No Model
        </div>
      )}
      <Button
        type="submit"
        fullWidth
        variant="contained"
        color="dark"
        onClick={onInsert}
      >
        {`นำเข้า (${modelSlc.length - currentModel.length})`}
      </Button>
      <div />
    </div>
  );
};

export default ModelGridSelect;

const ProductGridContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 16px;
  margin-top: 24px;
  @media screen and (max-width: 700px) {
    grid-template-columns: 1fr 1fr;
  }
  @media screen and (max-width: 545px) {
    grid-template-columns: 1fr;
  }
`;
const ProductGridItem = styled.div<{ disable?: boolean }>`
  position: relative;
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  cursor: ${(props) => (props.disable ? 'not-allowed' : 'pointer')};
  opacity: ${(props) => (props.disable ? 0.3 : 1)};

  img {
    width: 100%;
    aspect-ratio: 1/1;
    border-radius: 16px;
  }

  .name {
    text-align: center;
    font-size: 12px;
  }

  .image {
    display: flex;
    align-items: center;
    border-radius: 16px;
    outline: 2px solid #ececec;
    padding: 8px;
  }

  .active {
    outline: 2px solid black;
  }

  .MuiCheckbox-root {
    position: absolute;
    top: 0;
    right: 0;
  }
`;
