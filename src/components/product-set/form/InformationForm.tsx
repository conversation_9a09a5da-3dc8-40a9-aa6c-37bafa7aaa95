import { Button, TextField } from '@mui/material';
import { getIn, useFormik } from 'formik';
import styled from 'styled-components';
import React, { useEffect, useState } from 'react';
import ImageField from '@/components/ImageField';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import ModalSelectModel from '@/components/product-set/ModalSelectModel';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import _ from 'lodash';
import Image from 'next/image';

const ProductFormStyle = styled.div`
  width: 640px;
  max-width: 100%;
  form {
    > p {
      margin-top: 20px;
      margin-bottom: 5px;
    }
    span {
      font-size: 0.9em;
    }
  }
`;

type ProductFormProps = {
  defaultModel: any[];
  initialValues?: {
    name: string;
    description: string;
    thumbnail: any;
    productModelId: any[];
  };
  handleSubmit: (values: any) => void;
};

// const validationSchema = yup.object({
//   name: yup.string().required('กรุณากรอกชื่อเซ็ตสินค้า'),
//   description: yup.string().required('กรุณากรอกคำอธิบายเซ็ตสินค้า'),
//   urlSlug: yup.string().required('กรุณากรอก URL Slug'),
// });

const InformationForm = ({
  defaultModel,
  initialValues,
  handleSubmit,
}: ProductFormProps) => {
  const [file, setFile] = useState<any>(null);
  const [visibleModel, setVisibleModel] = useState<boolean>(false);
  const [modelSelectedList, setModelSelectedList] = useState<any[]>([]);
  const formik = useFormik({
    initialValues: initialValues || {
      name: '',
      description: '',
      urlSlug: '',
      thumbnail: null,
      productModelId: [],
    },
    // validationSchema,
    enableReinitialize: true,
    validate: (values: any) => {
      const errors: any = {};
      if (!values.thumbnail) {
        errors.thumbnail = 'กรุณาเพิ่มรูปภาพ';
      }
      if (!values.name) {
        errors.name = 'กรุณากรอกชื่อเซ็ตสินค้า';
      }
      if (!values.description) {
        errors.description = 'กรุณากรอกคำอธิบายเซ็ตสินค้า';
      }
      if (values.productModelId.length === 0) {
        errors.productModelId = 'กรุณาเพิ่มโมเดล';
      }
      return errors;
    },
    onSubmit: (values) => {
      handleSubmit(values);
    },
  });

  useEffect(() => {
    if (initialValues?.thumbnail) {
      setFile(initialValues?.thumbnail);
    }
  }, [initialValues]);

  useEffect(() => {
    setModelSelectedList(defaultModel);
  }, [defaultModel]);

  const uploadThumbnail = async (file: any) => {
    if (file) {
      formik.setValues({ ...formik.values, thumbnail: file[0] });
      setFile(URL.createObjectURL(file[0]));
    }
  };

  const onCloseModel = () => {
    setVisibleModel(false);
  };

  const onAddModel = (value: any) => {
    const union_data = _.union(modelSelectedList, value);
    const validate_data: any = _.uniqBy(union_data, (o: any) => {
      return o.id;
    });
    setModelSelectedList(validate_data);
    formik.setValues({
      ...formik.values,
      productModelId: validate_data.map((res: any) => res.id),
    });
    onCloseModel();
  };

  const onRemoveModel = async (model: any) => {
    const data: any = await _.xor(modelSelectedList, [model]);
    await setModelSelectedList(data);
    await formik.setFieldValue(
      'productModelId',
      data.map((m: any) => m.id) || []
    );
  };

  return (
    <ProductFormStyle>
      <form onSubmit={formik.handleSubmit}>
        <ThumbnailStyle>
          <div className="img-container">
            <ImageField
              handleChange={(file: any) => {
                uploadThumbnail(file);
              }}
              objectFit="cover"
              defaultBackground={file || '/images/add-image.svg'}
              borderRadius="14px"
            />
            {formik.touched.thumbnail && formik.errors.thumbnail && (
              <p className={'text-[#EE4E85] text-[12px] text-center'}>
                {String(formik.errors.thumbnail)}
              </p>
            )}
          </div>
        </ThumbnailStyle>

        <p>Name</p>
        <TextField
          type="text"
          name="name"
          value={formik.values.name}
          onChange={formik.handleChange}
          placeholder="Product Name"
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && formik.errors.name}
        />
        <p>Description</p>
        <TextField
          type="text"
          name="description"
          multiline
          rows={4}
          value={formik.values.description}
          onChange={formik.handleChange}
          placeholder="About Product Details"
          error={Boolean(
            getIn(formik.touched, 'description') &&
              getIn(formik.errors, 'description')
          )}
          helperText={
            getIn(formik.touched, 'description') &&
            getIn(formik.errors, 'description')
          }
        />
        <ModelContainer>
          <div className="header">
            <h3>Product Model</h3>
            <div onClick={() => setVisibleModel(true)}>
              <ActionButton
                variant="outlined"
                color="blueGrey"
                icon={<AddCircle />}
                text="เพิ่มโมเดล"
              />
            </div>
          </div>
          {modelSelectedList.length !== 0 ? (
            modelSelectedList.map((model: any, index: number) => {
              return (
                <div key={index} className={'model-list'}>
                  <div className={'model-item'}>
                    <div className={'start'}>
                      <div className={'image'}>
                        <div className="img-container">
                          <div className="img-box">
                            <Image
                              src={model?.imageUrl || '/images/digiboxs.svg'}
                              alt=""
                              fill
                            />
                          </div>
                        </div>
                      </div>
                      <div className={'title'}>{model.name}</div>
                    </div>
                    <div className={'end'}>
                      {/* <div className={'icon'}> */}
                      {/*  <SvgEditIcon /> */}
                      {/* </div> */}
                      <div
                        className={'icon'}
                        onClick={() => onRemoveModel(model)}
                      >
                        <SvgDeleteIcon />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className={'empty'}>ไม่มีโมเดล</div>
          )}
          {formik.touched.productModelId && formik.errors.productModelId && (
            <p className={'text-[#EE4E85] text-[12px]'}>
              {String(formik.errors.productModelId)}
            </p>
          )}
        </ModelContainer>
        <div className="py-[24px]">
          <Button type="submit" fullWidth variant="contained" color="dark">
            บันทึก
          </Button>
        </div>
      </form>
      <ModalSelectModel
        open={visibleModel}
        onClose={onCloseModel}
        currentModel={modelSelectedList}
        onSubmitModal={(value: any) => {
          onAddModel(value);
        }}
      />
    </ProductFormStyle>
  );
};

export default InformationForm;

const ThumbnailStyle = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 40px 0 0;
`;

const ModelContainer = styled.div`
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .empty {
    background: #f5f7f8;
    height: 120px;
    border-radius: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #cfd8dc;
  }
  .model-list {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    .model-item {
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      .img-container {
        height: 80px;
        width: 80px;
        padding: 12px;
        position: relative;
        .img-box {
          position: relative;
          height: 100%;
          width: 100%;
          border-radius: 4px;
          overflow: hidden;
        }
      }
      .start {
        display: flex;
        align-items: center;
        .image {
          border-right: 1px solid #dbe2e5;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px;
          img {
            width: 80px;
            height: 80px;
          }
        }
        .title {
          padding-left: 16px;
        }
      }
      .end {
        padding-right: 16px;
        display: flex;
        column-gap: 16px;
        align-items: center;
        .icon {
          cursor: pointer;
        }
      }
    }
  }
`;
