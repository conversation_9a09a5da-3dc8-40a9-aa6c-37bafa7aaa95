import { Dialog, DialogContent, IconButton } from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import React, { useState } from 'react';
import ProductGridSelect from '@/components/product-set/ProductGridSelect';
import ModelGridSelect from '@/components/product-set/ModelGridSelect';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';

type ModalSelectModelProps = {
  open: boolean;
  onClose: () => void;
  onSubmitModal: (value: any) => void;
  currentModel: any[];
};
const ModalSelectModel = ({
  open,
  onClose,
  onSubmitModal,
  currentModel,
}: ModalSelectModelProps) => {
  const [productSlc, setProductSlc] = useState<any>(null);
  const handleSelectProduct = (product: any) => {
    setProductSlc(product);
  };

  return (
    <>
      <Dialog open={open}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                {productSlc && (
                  <div className="back" onClick={() => setProductSlc(null)}>
                    <IconButton
                      sx={{
                        color: '#263238',
                      }}
                    >
                      <KeyboardBackspaceRoundedIcon />
                    </IconButton>
                  </div>
                )}
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  เพิ่มโมเดล
                </div>
                <div className="x-close" onClick={() => onClose()}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              {!productSlc ? (
                <ProductGridSelect
                  onSelect={(pd: any) => handleSelectProduct(pd)}
                />
              ) : (
                <ModelGridSelect
                  currentModel={currentModel || []}
                  productSlc={productSlc}
                  onSelect={(model: any[]) => {
                    onSubmitModal(model);
                    setProductSlc(null);
                  }}
                />
              )}
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalSelectModel;
