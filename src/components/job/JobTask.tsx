import React, { useEffect } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import JobTaskAside from '@/components/job/JobTaskAside';
import TableTools from '@/components/global/TableTools';
import TaskTimeline from '@/components/job/TaskTimeline';
import { setScrollY } from '@/store/features/job';
import { useAppDispatch } from '@/store';

const JobTaskStyled = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
const JobContentStyle = styled.div`
  width: 100%;
  display: flex;
  overflow: hidden;
`;
type Props = {
  loadingTasks: boolean;
  dataWorkSchedule: any;
  filtersWork: any;
  setFiltersWork: (date: any) => void;
};
const JobTask = ({
  loadingTasks,
  dataWorkSchedule,
  filtersWork,
  setFiltersWork,
}: Props) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    return () => {
      dispatch(setScrollY(0));
    };
  }, []);
  return (
    <JobTaskStyled>
      <TableTools
        tools={['search']}
        title={`กำลังดำเนินการ ${dataWorkSchedule?.length || 0} รายการ`}
        makeNewFilter={(_newFilter: any) => {
          //
        }}
      />
      <JobContentStyle>
        <JobTaskAside
          loadingTasks={loadingTasks}
          dataWorkSchedule={dataWorkSchedule}
          filtersWork={filtersWork}
          setFiltersWork={(date: any) => {
            setFiltersWork(date);
          }}
        />
        <TaskTimeline
          // loadingTasks={loadingTasks}
          dataWorkSchedule={dataWorkSchedule}
          filtersWork={filtersWork}
          setFiltersWork={(date: any) => {
            setFiltersWork(date);
          }}
        />
      </JobContentStyle>
    </JobTaskStyled>
  );
};

export default JobTask;
