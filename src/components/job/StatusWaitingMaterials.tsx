import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';

type Props = {
  status: string;
};
const StatusWaitingMaterialsStyles = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  color: #f9a925;
  //&.status-WAITING_REQUEST {
  //  color: #f9a925;
  //}
`;
const StatusWaitingMaterials = ({ status }: Props) => {
  const StatusText = () => {
    switch (status) {
      case 'WAITING_REQUEST':
        return 'รอส่งเบิก';
      case 'WAITING_PURCHASE':
        return 'รอขอซื้อ';
      case undefined:
        return 'ไม่พบสถาณะ';
      default:
        return status;
    }
  };
  return (
    <StatusWaitingMaterialsStyles className={`status-${status}`}>
      <Image src={'/icons/schedule.svg'} alt="" width={24} height={24} />
      <span>{StatusText()}</span>
    </StatusWaitingMaterialsStyles>
  );
};

export default StatusWaitingMaterials;
