import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import ButtonDetailCheck from '@/components/job/ButtonDetailCheck';
import { numberWithCommas } from '@/utils/number';
import { isEmpty } from 'lodash';
import { TGroupLayData } from '@/types/prepare-material';

const ProductDetailCardStyles = styled.div`
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  background: #fff;
  overflow: hidden;
  margin-top: 1rem;

  /* Apply text ellipsis and nowrap to all text elements */
  p,
  span,
  div:not(.group-tags):not(.tag):not(.key):not(.box-key):not(.img):not(
      .label
    ):not(.detail):not(.detail-print):not(header) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .not-found {
    text-align: center;
    color: #cfd8dc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  header {
    padding: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    border-bottom: 1px solid #dbe2e5;
    img {
      border-radius: 6px;
    }
    > div {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex: 1;
      min-width: 0;
      > div {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        min-width: 0;
        p {
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .detail {
    .box-key {
      .key {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        .title {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .value {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          &.not-found {
            color: #cfd8dc;
          }
        }
      }
    }
    .img {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      img {
        width: 34px;
        border-radius: 50%;
        height: 34px;
        min-width: 34px;
        border: 1px solid #dbe2e5;
      }
      > div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .detail-print {
    padding: 0.8rem 0.8rem 1.2rem;
    .label {
      p {
        margin: 0;
        margin-bottom: 0.2rem;
        font-size: 12px;
        color: #90a4ae;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .group-tags {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;
        .tag {
          width: fit-content;
          padding: 0.3rem 0.6rem;
          border-radius: 40px;
          border: 1px solid #dbe2e5;
          > div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            b {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
  .detail {
    padding: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .label {
      flex: 1;
      min-width: 0;
      p {
        margin: 0;
        margin-bottom: 0.2rem;
        font-size: 12px;
        color: #90a4ae;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      > div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
`;
const LayoutCardStyles = styled.div`
  padding-bottom: 3rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;
type Props = {
  groupLayData?: TGroupLayData[];
  detail?: any[];
};
const ProductDetailCard = ({ groupLayData, detail }: Props) => {
  console.log('groupLayData', groupLayData);
  console.log('detail', detail);
  return (
    <LayoutCardStyles>
      {!isEmpty(groupLayData) &&
        groupLayData?.map((item, index) => {
          const { ldCode, productModelImageUrl, productModelName, quantity } =
            item;
          return (
            <ProductDetailCardStyles key={index}>
              <header>
                <div>
                  <Image
                    src={productModelImageUrl}
                    alt={''}
                    width={40}
                    height={40}
                  />
                  <div>
                    <p>{ldCode} </p>{' '}
                    <span>
                      {productModelName} • {numberWithCommas(Number(quantity))}{' '}
                      ตัว
                    </span>
                  </div>
                </div>
                <ButtonDetailCheck isDisabled={true} />
              </header>
              {isEmpty(detail) || !detail?.[index] ? (
                <div
                  className={'not-found'}
                  style={{
                    padding: '62px 24px',
                  }}
                >
                  ไม่พบข้อมูล
                </div>
              ) : (
                (() => {
                  const currentDetail = detail?.[index];
                  const {
                    productModel,
                    width,
                    length,
                    height,
                    subMaterialDetail,
                    coatingRequest,
                    extra,
                    printingRequest,
                  } = currentDetail;

                  // Process coating data
                  const frontCoating =
                    coatingRequest?.finishFront
                      ?.map((f: any) => f.finishSubMaterialDetail?.name)
                      .filter(Boolean)
                      .join(', ') || 'ไม่ระบุ';

                  const backCoating =
                    coatingRequest?.finishBack
                      ?.map((f: any) => f.finishSubMaterialDetail?.name)
                      .filter(Boolean)
                      .join(', ') || 'ไม่ระบุ';

                  // Process extra techniques
                  const frontExtras =
                    extra
                      ?.filter(
                        (e: any) => e.extraSideDimension?.name === 'ด้านหน้า'
                      )
                      .map(
                        (e: any) =>
                          `${e.masterName}${
                            e.extraAreaDimension?.name
                              ? ` • ${e.extraAreaDimension.name}`
                              : ''
                          }`
                      )
                      .join(', ') || 'ไม่ระบุ';

                  const backExtras =
                    extra
                      ?.filter(
                        (e: any) => e.extraSideDimension?.name === 'ด้านหลัง'
                      )
                      .map(
                        (e: any) =>
                          `${e.masterName}${
                            e.extraAreaDimension?.name
                              ? ` • ${e.extraAreaDimension.name}`
                              : ''
                          }`
                      )
                      .join(', ') || 'ไม่ระบุ';

                  // Process printing colors
                  const printingColors: any[] = [];
                  if (printingRequest?.colorFront) {
                    printingRequest.colorFront.forEach((color: any) => {
                      printingColors.push({
                        color: color.printColor?.name || color.colorCode,
                        machine: color.machine?.name,
                        machineModel: color.machineModel?.nameModel,
                        side: 'ด้านหน้า',
                        imageUrl: color.imageUrl || color.printColor?.imageUrl,
                      });
                    });
                  }
                  if (printingRequest?.colorBack) {
                    printingRequest.colorBack.forEach((color: any) => {
                      printingColors.push({
                        color: color.printColor?.name || color.colorCode,
                        machine: color.machine?.name,
                        machineModel: color.machineModel?.nameModel,
                        side: 'ด้านหลัง',
                        imageUrl: color.imageUrl || color.printColor?.imageUrl,
                      });
                    });
                  }

                  return (
                    <>
                      <div className="detail">
                        <div className={'label'}>
                          <p>Model</p>
                          <div>
                            {productModel?.modelCodePacdora || 'ไม่ระบุ'} <br />
                            ขนาดกางออก {length}x{width}x{height} mm
                          </div>
                        </div>
                        <div className={'label'}>
                          <p>ขนาดวัสดุ</p>
                          <div className={'img'}>
                            <Image
                              src={
                                subMaterialDetail?.subMaterialImageUrl ||
                                '/images/product/empty-product.svg'
                              }
                              alt={''}
                              width={40}
                              height={40}
                            />
                            <div>{subMaterialDetail?.name || 'ไม่ระบุ'}</div>
                          </div>
                        </div>
                        <div className={'label'}>
                          <p>เคลือบหลังพิมพ์</p>
                          <div className={'box-key'}>
                            <div className="key">
                              <div className="title">ด้านหน้า :</div>
                              <div
                                className={`value ${
                                  frontCoating === 'ไม่ระบุ' ? 'not-found' : ''
                                }`}
                              >
                                {frontCoating}
                              </div>
                            </div>
                            <div className="key">
                              <div className="title">ด้านหลัง :</div>
                              <div
                                className={`value ${
                                  backCoating === 'ไม่ระบุ' ? 'not-found' : ''
                                }`}
                              >
                                {backCoating}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className={'label'}>
                          <p>เทคนิคพิเศษ</p>
                          <div className={'box-key'}>
                            <div className="key">
                              <div className="title">ด้านหน้า :</div>
                              <div
                                className={`value ${
                                  frontExtras === 'ไม่ระบุ' ? 'not-found' : ''
                                }`}
                              >
                                {frontExtras}
                              </div>
                            </div>
                            <div className="key">
                              <div className="title">ด้านหลัง :</div>
                              <div
                                className={`value ${
                                  backExtras === 'ไม่ระบุ' ? 'not-found' : ''
                                }`}
                              >
                                {backExtras}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className={'detail-print'}>
                        <div className={'label'}>
                          <p>เครื่องพิมพ์</p>
                          <div className={'group-tags'}>
                            {printingColors.map((printColor, i) => (
                              <div className={'tag'} key={i}>
                                <div>
                                  <Image
                                    src={
                                      printColor.imageUrl ||
                                      '/images/icon-tag.svg'
                                    }
                                    alt={''}
                                    width={20}
                                    height={20}
                                    style={{
                                      borderRadius: '50%',
                                    }}
                                  />
                                  <b>{printColor.color}</b>{' '}
                                  {printColor.machineModel} • {printColor.side}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()
              )}
            </ProductDetailCardStyles>
          );
        })}
    </LayoutCardStyles>
  );
};

export default ProductDetailCard;
