import React from 'react';
import { Avatar, AvatarGroup, Button, Menu } from '@mui/material';
import styled from 'styled-components';
import Image from 'next/image';

const JobAvatarDetailsStyles = styled.div`
  #basic-button {
    min-width: unset;
    padding: 0;
    background: transparent;
  }
`;
const DataProfileStyles = styled.div`
  min-width: 250px;
  .box-name {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 0.5rem;
    .name {
      font-weight: 600;
    }
  }
  .box-detail-profile {
    margin-top: 0.5rem;
    padding: 0.5rem 0.7rem;
    border-radius: 8px;
    background: #f5f7f8;
    ul {
      li {
        display: flex;
        align-items: center;
        justify-content: start;
        gap: 0.5rem;
      }
    }
  }
`;
const JobAvatarDetails = () => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <JobAvatarDetailsStyles>
      <AvatarGroup max={4}>
        {[1, 2, 3, 4, 5, 6].map((item: number) => (
          <>
            <Button
              id="basic-button"
              aria-controls={open ? 'basic-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
            >
              <Avatar
                key={item}
                alt="Remy Sharp"
                src="/images/profile-mockup.svg"
              />
            </Button>
            <Menu
              id="basic-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'basic-button',
              }}
            >
              <DataProfileStyles>
                <div className={'box-name'}>
                  <Image
                    src={'/images/profile-mockup.svg'}
                    alt="Remy Sharp"
                    width={40}
                    height={40}
                  />
                  <div>
                    <div className={'name'}>ทนงศักดิ์ ต้นนพรัตน์</div>
                    <div className={'group'}>ฝ่ายผลิต • ตัดกระดาษ</div>
                  </div>
                </div>
                <div className={'box-detail-profile'}>
                  <ul>
                    <li>
                      {' '}
                      <Image
                        src={'/icons/icon-business.svg'}
                        alt="icon"
                        width={25}
                        height={25}
                      />
                      ฝ่ายผลิต
                    </li>
                    <li>
                      <Image
                        src={'/icons/icon-phone.svg'}
                        alt="icon"
                        width={25}
                        height={25}
                      />
                      ************
                    </li>
                    <li>
                      <Image
                        src={'/icons/icon-mail.svg'}
                        alt="icon"
                        width={25}
                        height={25}
                      />
                      <EMAIL>
                    </li>
                  </ul>
                </div>
              </DataProfileStyles>
            </Menu>
          </>
        ))}
      </AvatarGroup>
    </JobAvatarDetailsStyles>
  );
};

export default JobAvatarDetails;
