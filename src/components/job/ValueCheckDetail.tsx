import React from 'react';
import styled from 'styled-components';

const ValueCheckDetailStyles = styled.div`
  header {
    padding: 0.7rem 0 !important;
    border-bottom: 1px solid #263238 !important;
    p {
      font-weight: 600;
      margin: 0;
    }
  }
  ul {
    li {
      padding: 0.7rem 0 !important;
      border-bottom: 1px solid #263238 !important;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .value {
        font-weight: 600;
      }
    }
  }
`;
const ValueCheckDetail = () => {
  return (
    <ValueCheckDetailStyles>
      <header>
        <p>วัสดุ</p>
      </header>
      <ul>
        <li>
          <span>กระดาษ</span>
          <div className={'value'}>กระดาษอาร์ตการ์ดด้าน 300 แกรม</div>
        </li>
        <li>
          <span>ใบเต็ม</span>
          <div className={'value'}>310x430 mm</div>
        </li>
        <li>
          <span>ใบพิมพ์</span>
          <div className={'value'}>ตัด 2 - 155x215 mm</div>
        </li>
      </ul>
    </ValueCheckDetailStyles>
  );
};

export default ValueCheckDetail;
