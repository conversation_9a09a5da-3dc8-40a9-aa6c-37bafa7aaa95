import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { useRouter } from 'next/router';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import { steps } from '@/services/stock/steps';

const JobStepStatusBarStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: 100%;
  gap: 16px;
  .step {
    flex: 1 0 188px;
    height: 40px;
    border: 1px solid #dbe2e5;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: white;
    user-select: none;
    border-radius: 20px;
    color: #cfd8dc;
    &:active {
      box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
      background: #fafbfb;
    }

    .icon {
      position: absolute;
      left: 8px;
      display: flex;
      align-items: center;
    }
    .text {
      font-weight: 600;
    }
    &.active {
      background: #fafbfb;
      box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
      color: #263238;
      pointer-events: unset !important;
    }
    &.disable {
      pointer-events: none;
      color: #cfd8dc;
    }
    &.success {
      border: 1px solid #00c1af !important;
      color: #fff;
      background: #16d5c5 !important;
      pointer-events: unset !important;
    }
  }
`;
type Props = {
  currentStepId?: number;
};
const JobStepStatusBar = ({ currentStepId }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const checkStatusSuccess = (statusId: any, id: number) => {
    return statusId > id;
  };
  return (
    <JobStepStatusBarStyled>
      {steps.map((item, index) => (
        <div
          key={index}
          className={`step  ${
            currentStepId === item.id ? 'active' : 'disable'
          } ${checkStatusSuccess(currentStepId, item.id) ? 'success' : ''}`}
          onClick={() => {
            if (id) {
              router.push(`/job/${id}?step=${item.step}`);
            }
          }}
        >
          <div className="icon">
            {/* {activeStep >= index && ( */}
            {/*  <Image */}
            {/*    src={ */}
            {/*      activeStep > index */}
            {/*        ? '/icons/task-done.svg' */}
            {/*        : '/icons/schedule.svg' */}
            {/*    } */}
            {/*    alt="" */}
            {/*    width={24} */}
            {/*    height={24} */}
            {/*  /> */}
            {/* )} */}
            {checkStatusSuccess(currentStepId, item.id) && (
              <CheckCircleRoundedIcon />
            )}
            {currentStepId === item.id && (
              <Image
                src={
                  item.id === 6 ? '/icons/task-done.svg' : '/icons/schedule.svg'
                }
                alt="icon"
                width={24}
                height={24}
              />
            )}
          </div>
          <div className="text">{item.name}</div>
        </div>
      ))}
    </JobStepStatusBarStyled>
  );
};

export default JobStepStatusBar;
