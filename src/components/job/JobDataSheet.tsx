import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { FormHelperText } from '@mui/material';
import PrintSideDetail from '@/components/job/PrintSideDetail';
import {
  TDataLayoutFile,
  TLayDataDetail,
  TProductionOrder,
} from '@/types/prepare-material';
import SelectJobDataSheet from '@/components/job/SelectJobDataSheet';
import apiMachine from '@/services/stock/machine';

const JobDataSheetStyles = styled.div`
  background: #fff;
  .detail {
    padding: 24px;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        font-size: 18px;
        font-weight: 600;
      }
    }
    .box-setting-sheet {
      margin-top: 1rem;
      padding: 1.5rem;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      overflow: hidden;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      .label-field {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        > .field-select {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          width: 100%;
          p {
            margin: 0;
            font-weight: 600;
            padding-bottom: 0.2rem;
          }
          .select-job {
            .MuiSelect-select {
              padding: 0 !important;
              padding-left: 10px !important;
            }
          }
        }
      }
    }
  }
`;
type Props = {
  allData?: any;
  dataJob?: TProductionOrder;
  layoutBackUrl?: string;
  layoutFrontUrl?: string;
  layDataDetailFront?: TLayDataDetail;
  layDataDetailBack?: TLayDataDetail;
  formik: any;
  productionOrderId?: number;
  layoutFile: TDataLayoutFile;
  setLayoutFile: (data: TDataLayoutFile) => void;
  isSetting: boolean;
};
const JobDataSheet = ({
  allData,
  dataJob,
  layoutBackUrl,
  layoutFrontUrl,
  layDataDetailFront,
  layDataDetailBack,
  formik,
  productionOrderId,
  layoutFile,
  setLayoutFile,
  isSetting,
}: Props) => {
  const [machineModelList, setMachineModelList] = useState<any[]>([]);
  const [loadingMachineModel, setLoadingMachineModel] = useState(false);

  // Fetch machine models when machineId changes
  useEffect(() => {
    const fetchMachineModels = async () => {
      const { machineId } = formik.values;
      if (machineId) {
        setLoadingMachineModel(true);
        try {
          const response = await apiMachine.getMachineModelList(machineId);
          if (response.status) {
            setMachineModelList(response.data || []);
          } else {
            setMachineModelList([]);
          }
        } catch (error) {
          console.error('Error fetching machine models:', error);
          setMachineModelList([]);
        } finally {
          setLoadingMachineModel(false);
        }
      } else {
        setMachineModelList([]);
        // Clear machineModelId when machine changes
        formik.setFieldValue('machineModelId', '');
      }
    };

    fetchMachineModels();
  }, [formik.values.machineId]);

  // Clear print sheet size when full sheet size changes
  useEffect(() => {
    // Only clear if itemSizeId has a value and is different from initial value
    if (
      formik.values.itemSizeId &&
      formik.values.itemSizeId !== dataJob?.itemSizeId
    ) {
      formik.setFieldValue('subItemSizeId', '');
    }
  }, [formik.values.itemSizeId]);

  // Handle machine change to clear model selection
  const handleMachineChange = (event: any) => {
    formik.setFieldValue('machineId', event.target.value);
    formik.setFieldValue('machineModelId', ''); // Clear model when machine changes
  };
  return (
    <JobDataSheetStyles>
      <div className={'detail'}>
        <header>
          <div>ข้อมูลใบพิมพ์</div>
        </header>
        <div className={'box-setting-sheet'}>
          <div className={'label-field'}>
            <SelectJobDataSheet
              title={'วัสดุพิมพ์'}
              id={dataJob?.rawMaterialId}
              name={'rawMaterialId'}
              data={allData?.dataRawMaterial}
              formik={formik}
              isDisabled={true}
            />
            <SelectJobDataSheet
              title={'ขนาดใบเต็ม'}
              id={dataJob?.itemSizeId}
              name={'itemSizeId'}
              data={allData?.dataItemSize}
              isDisabled={false}
              formik={formik}
            />
            <SelectJobDataSheet
              title={'ขนาดใบพิมพ์'}
              id={dataJob?.subItemSizeId}
              name={'subItemSizeId'}
              data={allData?.dataSubItemSize}
              formik={formik}
              isDisabled={dataJob?.isLaySetting}
              displayField={'itemSizeName'}
            />
            <SelectJobDataSheet
              title={'บล๊อกไดคัท'}
              id={dataJob?.dieCutRawMaterialId}
              name={'dieCutRawMaterialId'}
              data={allData?.dataDieCutList}
              formik={formik}
              isDisabled={dataJob?.isLaySetting}
            />
            <SelectJobDataSheet
              title={'เพลท'}
              id={dataJob?.plateRawMaterialId}
              name={'plateRawMaterialId'}
              data={allData?.dataPlateList}
              formik={formik}
              isDisabled={dataJob?.isLaySetting}
            />
            <div className={'field-select'}>
              <p>เครื่องพิมพ์</p>
              <Select
                displayEmpty
                value={formik?.values.machineId || ''}
                className={'select-job'}
                name={'machineId'}
                onChange={handleMachineChange}
                error={
                  formik?.touched.machineId && Boolean(formik.errors.machineId)
                }
              >
                <MenuItem value="" disabled>
                  <em>กรุณาเลือกเครื่องพิมพ์</em>
                </MenuItem>
                {allData?.dataMachineList?.map((item: any, index: number) => (
                  <MenuItem value={item.id} key={index}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              {formik?.touched.machineId && formik.errors.machineId && (
                <FormHelperText error>{formik.errors.machineId}</FormHelperText>
              )}
            </div>
            <div className={'field-select'}>
              <p>โมเดล</p>
              <Select
                displayEmpty
                value={formik?.values.machineModelId || ''}
                className={'select-job'}
                name={'machineModelId'}
                disabled={!formik.values.machineId || loadingMachineModel}
                onChange={(event) => {
                  formik.setFieldValue('machineModelId', event.target.value);
                }}
                error={
                  formik?.touched.machineModelId &&
                  Boolean(formik.errors.machineModelId)
                }
              >
                <MenuItem value="" disabled>
                  <em>
                    {loadingMachineModel ? 'กำลังโหลด...' : 'กรุณาเลือกโมเดล'}
                  </em>
                </MenuItem>
                {machineModelList.map((item: any, index: number) => (
                  <MenuItem value={item.id} key={index}>
                    {item.nameModel}
                  </MenuItem>
                ))}
              </Select>
              {formik?.touched.machineModelId &&
                formik.errors.machineModelId && (
                  <FormHelperText error>
                    {formik.errors.machineModelId}
                  </FormHelperText>
                )}
            </div>
          </div>
          <PrintSideDetail
            layoutBackUrl={layoutBackUrl}
            layoutFrontUrl={layoutFrontUrl}
            layDataDetailFront={layDataDetailFront}
            layDataDetailBack={layDataDetailBack}
            productionOrderId={productionOrderId}
            layoutFile={layoutFile}
            setLayoutFile={(data: TDataLayoutFile) => setLayoutFile(data)}
            isSetting={isSetting}
          />
        </div>
      </div>
    </JobDataSheetStyles>
  );
};

export default JobDataSheet;
