import TextField from '@mui/material/TextField';
import React from 'react';
import styled from 'styled-components';
import Button from '@mui/material/Button';
import { useRouter } from 'next/router';
import { LoadingButton } from '@mui/lab';

const JobDetailNoteStyles = styled.div`
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    max-width: calc(100% / 2);
    width: 100%;
    .form-action-bottom {
      margin-top: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      button {
        width: 100%;
      }
    }
  }
`;
type Props = {
  loading: boolean;
};
const JobDetailNote = ({ loading }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  return (
    <JobDetailNoteStyles>
      <div />
      <div>
        <TextField multiline minRows={8} placeholder={'หมายเหตุ'} />
        <div className={'form-action-bottom'}>
          <Button
            variant={'outlined'}
            onClick={() => router.push(`/job/${id}`)}
          >
            ยกเลิก
          </Button>
          <LoadingButton
            loading={loading}
            variant={'contained'}
            loadingPosition="start"
            type={'submit'}
          >
            บันทึก
          </LoadingButton>
          {/* <FormikErrorFocus /> */}
        </div>
      </div>
    </JobDetailNoteStyles>
  );
};

export default JobDetailNote;
