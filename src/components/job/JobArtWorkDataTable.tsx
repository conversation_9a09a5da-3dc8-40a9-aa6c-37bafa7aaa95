import React, { useEffect, useState } from 'react';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import ModalCreateArtWork from '@/components/job/modal/ModalCreateArtWork';
import { TAddArtwork, TArtwork } from '@/types/prepare-material';
import Image from 'next/image';
import PopoverAction from '@/components/PopoverActionn';
import { JobDataTable } from '@/styles/job-data-table.styled';
import apiJob from '@/services/order/job';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

type Props = {
  artwork?: TArtwork[];
  fetchData: () => void;
};
const JobArtWorkDataTable = ({ artwork, fetchData }: Props) => {
  const dispatch = useAppDispatch();
  const [rows, setRows] = useState<TArtwork[]>(artwork || []);
  const columns = [
    {
      field: 'ldCode',
      headerName: 'รายการสินค้า',
      headerAlign: 'left',
      flex: 1,
      align: 'left',
      width: 80,
      renderCell: (params: any) => {
        return params.row.ldCode || '-';
      },
    },
    {
      field: 'title',
      headerName: 'รายการ',
      minWidth: 200,
      flex: 2,
    },
    {
      field: 'link',
      headerName: 'ไฟล์',
      minWidth: 120,
      flex: 1,
    },
    {
      field: 'user',
      headerName: 'ผู้สร้าง',
      flex: 1,
      minWidth: 130,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              className={'rounded-full'}
              src={
                params.row.user?.imageUrl || '/images/product/empty-product.svg'
              }
              alt={''}
              width={35}
              height={35}
            />
            <div className={'name'}>{params.row.user?.name}</div>
          </div>
        );
      },
    },
    {
      field: 'action',
      headerName: 'จัดการ',
      minWidth: 100,
      headerAlign: 'center',
      align: 'right',
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => (
                    <Image
                      src={'/icons/ic-bin-delete.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  ),
                  title: 'ลบรายการ',
                  onAction: () => deleteArtwork(params.row.id),
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const createArtwork = async (data: TAddArtwork) => {
    const res = await apiJob.createAddArtwork(data);
    if (res && !res.isError) {
      fetchData();
    }
  };

  const deleteArtwork = async (productionOrderArtworkId: number) => {
    const res = await apiJob.deleteProductionOrderArtwork(
      productionOrderArtworkId
    );
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบไฟล์อาร์ตเวิร์กเรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
    }
  };
  useEffect(() => {
    if (artwork) setRows(artwork);
  }, [artwork]);
  return (
    <JobDataTable>
      <div className={'detail'}>
        <header>
          <div>ไฟล์อาร์ตเวิร์ก</div>
          <ModalCreateArtWork
            onSubmit={(data: TAddArtwork) => createArtwork(data)}
          />
        </header>
        <div className={'box-data-table'}>
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
                <DataGrid
                  hideFooter={true}
                  rows={rows}
                  columns={columns as any}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </div>
    </JobDataTable>
  );
};

export default JobArtWorkDataTable;
