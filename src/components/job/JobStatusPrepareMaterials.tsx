import { Button } from '@mui/material';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { DataGrid } from '@mui/x-data-grid';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import apiJob from '@/services/order/job';
import { useRouter } from 'next/router';
import ModalCreateJobMaterial from './modal/ModalCreateJobMaterial';
import PrepareCellQuantity from '@/components/job/PrepareCellQuantity';
import PrepareCellBrand from '@/components/job/PrepareCellBrand';
import ActionDeleteWithdrawalList from '@/components/job/ActionDeleteWithdrawalList';
import { useFormik } from 'formik';
import ModalConfirmWithdrawal from '@/components/job/modal/ModalConfirmWithdrawal';
import {
  TAddMaterial,
  TProductionOrder,
  TWithdrawal,
} from '@/types/prepare-material';
import * as yup from 'yup';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { isEmpty } from 'lodash';
import JobStatusStyles from '@/styles/styledComponents/JobStatusStyles.styled';
import { numberWithCommas } from '@/utils/number';

type Props = {
  dataJob?: TProductionOrder;
  getProductionOrder: (id: number) => void;
  approveProductionOrder: (id: number) => void;
};
const validationSchema = yup.array().of(
  yup.object().shape({
    quantity: yup.number().required(),
  })
);
const JobStatusPrepareMaterials = ({
  dataJob,
  getProductionOrder,
  approveProductionOrder,
}: Props) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;
  const [isUpdate, setIsUpdate] = useState(true);
  const [newDataAddMaterials, setNewDataAddMaterials] = useState<TAddMaterial>({
    rawMaterialId: 0,
    brandId: 0,
  });
  const formik = useFormik({
    initialValues: [],
    validationSchema,
    onSubmit: (values: any) => {
      saveProOrderRawMaterial(values);
    },
  });
  const [open, setOpen] = useState(false);
  const [openConfirmWithdrawal, setOpenConfirmWithdrawal] = useState(false);
  const handleClose = () => {
    setOpen(false);
    setNewDataAddMaterials({
      rawMaterialId: 0,
      brandId: 0,
    });
  };
  const handleCloseConfirmWithdrawal = () => {
    setOpenConfirmWithdrawal(false);
  };
  const dataColumns = () => {
    const columns = [
      {
        field: 'no',
        headerName: '#',
        headerAlign: 'center',
        align: 'center',
        width: 80,
      },
      {
        field: 'name',
        headerName: 'รายการเบิก',
        minWidth: 200,
        flex: isUpdate ? 4 : 2,
        renderCell: (params: any) => (
          <div>
            <div style={{ fontWeight: 600 }}>{params.row.name}</div>
            <div style={{ fontSize: 12 }}>
              {params.row.rawMaterialNo || '-'}{' '}
              {params.row.itemSize?.itemSizeName && '•'}{' '}
              {params.row.itemSize?.itemSizeName || ''}
            </div>
          </div>
        ),
      },
      {
        field: 'materialName',
        headerName: 'Materials',
        minWidth: 120,
        flex: 0.5,
      },
      {
        field: 'brand',
        headerName: 'แบรนด์',
        flex: 1,
        minWidth: 150,
        renderCell: (params: any) => {
          if (isUpdate) {
            return params.row.brand?.name || '';
          }
          return (
            <PrepareCellBrand
              listRawMaterialBrandConfig={params.row.listRawMaterialBrandConfig}
              brandId={params.row.brand?.id || 0}
              index={params.row.no - 1}
            />
          );
        },
      },
      {
        field: 'quantity',
        headerName: 'จำนวน',
        flex: 1,
        minWidth: 130,
        renderCell: (params: any) => {
          if (isUpdate) {
            return numberWithCommas(params.row.quantity);
          }
          return (
            <PrepareCellQuantity formik={formik} index={params.row.no - 1} />
          );
        },
      },
      {
        field: 'action',
        headerName: 'จัดการ',
        minWidth: 100,
        headerAlign: 'center',
        align: 'right',
        cellClassName: 'stickyCell',
        renderCell: (params: any) => {
          return (
            <>
              <ActionDeleteWithdrawalList
                onDelete={() => removeMaterial(params.row.id)}
              />
            </>
          );
        },
      },
    ];
    if (!isUpdate) {
      return columns;
    }
    columns.splice(-1, 1);
    return columns;
  };

  const removeMaterial = (id: number) => {
    const result = formik.values.filter((item: any) => item.id !== id);
    formik.setValues(addNumberRows(result) as any);
  };
  const addNumberRows = (data: TWithdrawal[]) => {
    return data.map((item: any, index: number) => {
      if (item.no) {
        delete item.no;
      }
      return { no: index + 1, ...item };
    });
  };
  const saveProOrderRawMaterial = async (values: any) => {
    const withdrawal = values.map((item: any) => {
      return {
        rawMaterialId: item.rawMaterialId,
        brandId: item.brand.id,
        quantity: item.quantity,
      };
    });
    const data = {
      productionOrderId: Number(id),
      withdrawal,
    };
    const res = await apiJob.saveProOrderRawMaterial(data);
    if (res.status === true) {
      getProductionOrder(Number(id));
      dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกรายการเบิกวัสดุสำเร็จ',
          severity: 'success',
        })
      );
    }
  };
  const addRawMaterialToProductionOrder = async () => {
    const timestamp = Date.now();
    const res = await apiJob.addRawMaterialToProductionOrder(
      newDataAddMaterials
    );
    if (res.status === true) {
      const newData = [...formik.values, { ...res.data, id: timestamp }];
      // setRows(newData);
      formik.setValues(addNumberRows(newData) as any);
    }
  };
  const saveAndSendWithdrawal = async () => {
    const res = await apiJob.sendWithdrawal({ productionOrderId: Number(id) });
    if (res.status === true) {
      saveProOrderRawMaterial(formik.values);
      approveProductionOrder(Number(id));
      handleCloseConfirmWithdrawal();
      dispatch(
        setSnackBar({
          status: true,
          text: 'คำขอส่งเบิกสำเร็จ',
          severity: 'success',
        })
      );
    }
  };
  const hasIsUpdate = () => {
    if (!isEmpty(formik.values)) {
      const allIsUpdate = formik.values.map((item: any) => item.isUpdate);
      setIsUpdate(allIsUpdate.includes(true)); // check isUpdate all true
    }
  };
  useEffect(() => {
    hasIsUpdate();
  }, [formik]);
  useEffect(() => {
    if (dataJob) {
      formik.setValues(addNumberRows(dataJob.withdrawal) as any);
    }
  }, [dataJob]);
  console.log('formik.values', formik.values);
  return (
    <JobStatusStyles>
      <header>
        <div className={'box-text-header'}>
          <h3>รายการเบิกวัสดุ</h3>
          {/* <div className={'box-status'}> */}
          {/*  <Image */}
          {/*    src={'/icons/status-in-progress.svg'} */}
          {/*    alt={'icon'} */}
          {/*    width={24} */}
          {/*    height={24} */}
          {/*  /> */}
          {/*  <span>กำลังดำเนินการ</span> */}
          {/* </div> */}
        </div>
        <div className={'group-buttons'}>
          {/* {router.asPath === `/job/prepare-materials/${id}` && ( */}
          {/*  <Button */}
          {/*    variant={'outlined'} */}
          {/*    className={'btn-withdraw'} */}
          {/*    onClick={() => */}
          {/*      router.push(`/job/prepare-materials/prepare/${id}`) */}
          {/*    } */}
          {/*    startIcon={ */}
          {/*      <Image */}
          {/*        src={'/icons/two_pager_store.svg'} */}
          {/*        alt={'icon'} */}
          {/*        width={24} */}
          {/*        height={24} */}
          {/*      /> */}
          {/*    } */}
          {/*  > */}
          {/*    จัดเตรียมวัสดุ */}
          {/*  </Button> */}
          {/* )} */}
          {!isUpdate && (
            <>
              <Button
                variant={'contained'}
                onClick={() => setOpen(true)}
                startIcon={
                  <Image
                    src={'/icons/add-material.svg'}
                    alt={'icon'}
                    width={24}
                    height={24}
                  />
                }
              >
                เพิ่มวัสดุ
              </Button>
              <ModalCreateJobMaterial
                handleClose={handleClose}
                open={open}
                onSubmit={addRawMaterialToProductionOrder}
                setNewDataAddMaterials={(
                  rawMaterialId: number,
                  brandId: number
                ) =>
                  setNewDataAddMaterials({
                    rawMaterialId,
                    brandId,
                  })
                }
              />{' '}
            </>
          )}
        </div>
      </header>
      <form onSubmit={formik.handleSubmit}>
        <div className={'box-job-status'}>
          <div className={'box-data-table'}>
            <AppTableStyle $rows={formik.values}>
              <div className="content-wrap">
                <ScrollBarStyled>
                  {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
                  <DataGrid
                    hideFooter={true}
                    rows={formik.values || []}
                    columns={dataColumns() as any}
                    paginationMode="server"
                    // checkboxSelection={true}
                    // rowCount={totalElements || 0}
                    // pageSize={filters.size}
                    disableSelectionOnClick={false}
                    autoHeight={true}
                    sortModel={[]}
                    getRowHeight={() => 56}
                    headerHeight={48}
                    components={{
                      NoRowsOverlay: () => <TableNoRowsOverlay />,
                      LoadingOverlay: () => <TableLoadingOverlay />,
                    }}
                  />
                </ScrollBarStyled>
              </div>
            </AppTableStyle>
          </div>
          <div className="px-[16px]">
            {/* <AppPagination */}
            {/*  filters={filters} */}
            {/*  totalElements={totalElements || 0} */}
            {/*  handleChangeFilters={(newValues: any) => setFilters(newValues)} */}
            {/* /> */}
          </div>
        </div>
        {!isUpdate && (
          <div className={'box-send-withdraw'}>
            <Button variant={'outlined'} type={'submit'}>
              บันทึก
            </Button>
            <ModalConfirmWithdrawal
              open={openConfirmWithdrawal}
              handleClose={handleCloseConfirmWithdrawal}
              jobNo={dataJob?.jobNo}
              onSendWithdrawal={saveAndSendWithdrawal}
            />
            <Button
              variant={'contained'}
              onClick={() => setOpenConfirmWithdrawal(true)}
            >
              บันทึก และส่งเบิกวัสดุ
            </Button>
          </div>
        )}
      </form>
    </JobStatusStyles>
  );
};

export default JobStatusPrepareMaterials;
