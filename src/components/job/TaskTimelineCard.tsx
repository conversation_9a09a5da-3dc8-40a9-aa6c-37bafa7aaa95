import React, { useState } from 'react';
import Image from 'next/image';
import ModalTaskTimeline from '@/components/job/modal/ModalTaskTimeline';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { isEmpty, isNull } from 'lodash';
import { convertDateFormat } from '@/utils/date';
import PauseCircleOutlineRoundedIcon from '@mui/icons-material/PauseCircleOutlineRounded';

dayjs.extend(utc);
dayjs.extend(timezone);
type Props = {
  task: any;
};
const TaskTimelineCard = ({ task }: Props) => {
  const [open, setOpen] = useState(false);
  // const getElapsedTimeOneUnit = (startHour: number | null): string | null => {
  //   if (startHour === null) return null;
  //   const now = new Date();
  //   const startHourInt = Math.floor(startHour);
  //   const startMinute = Math.round((startHour - startHourInt) * 60);
  //   const startTime = new Date(now);
  //   startTime.setHours(startHourInt, startMinute, 0, 0);
  //   const elapsedMilliseconds = now.getTime() - startTime.getTime();
  //   if (elapsedMilliseconds < 0) return null; // ถ้ายังไม่ถึงเวลาเริ่มงาน
  //   const totalSeconds = Math.floor(elapsedMilliseconds / 1000);
  //   if (totalSeconds < 60) {
  //     return `${totalSeconds} s`;
  //   }
  //   const totalMinutes = Math.floor(totalSeconds / 60);
  //   if (totalMinutes < 60) {
  //     return `${totalMinutes} ${totalMinutes === 1 ? 'min' : 'mins'}`;
  //   }
  //   const totalHours = Math.floor(totalMinutes / 60);
  //   return `${totalHours} ${totalHours === 1 ? 'hr' : 'hrs'}`;
  // };
  const timeConverter = (totalMinutes: number) => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours}${minutes === 0 ? '' : `:${minutes}`}`;
  };
  const getTimeMinutes = (startTime: number, stopDateTime?: number) => {
    if (stopDateTime) {
      const stop = dayjs(stopDateTime);
      return `${stop.diff(startTime, 'minute')} m`;
    }
    const now = dayjs();
    const diff = now.diff(startTime, 'minute');
    return `${diff} m`;
  };
  return (
    <>
      <ModalTaskTimeline open={open} handleClose={() => setOpen(false)} />
      <div
        className={`task-card ${task.statusEnum} ${
          task?.statusEnum === 'COMPLETED' && task.isDelay ? 'DELAY' : ''
        }`}
        style={{
          position: 'absolute',
          left: `${convertDateFormat(task.startPlan, true) * 100 + 3}px`,
          width: `${
            task?.statusEnum === 'COMPLETED'
              ? (convertDateFormat(task.endTime, true) -
                  convertDateFormat(task.startPlan, true)) *
                100
              : (convertDateFormat(task.endPlan, true) -
                  convertDateFormat(task.startPlan, true)) *
                100
          }px`,
          height: '100%',
        }}
        onClick={() => {
          // setOpen(true)
        }}
      >
        {task.statusEnum === 'IN_PROGRESS' &&
          (() => {
            const now = new Date();
            const nowAbsolute = convertDateFormat(now, true) * 100;
            const taskStartPos = convertDateFormat(task.startPlan, true) * 100;
            const taskWidth =
              convertDateFormat(task.endPlan, true) * 100 -
              convertDateFormat(task.startPlan, true) * 100;
            const progressWidth = nowAbsolute - taskStartPos;
            return (
              <div
                className="background-time-progress"
                style={{
                  width: `${
                    progressWidth > taskWidth ? '100%' : progressWidth + 3
                  }px`,
                }}
              />
            );
          })()}
        <div className="info-group">
          <div className="title">{task.name}</div>
          {task.timeJob && (
            <div className="description">
              เวลาทำงาน: {timeConverter(task.timeJob)} ชั่วโมง
            </div>
          )}
        </div>
        <div className="bottom-group">
          <div
            className={`runner-time${
              !task.startedTime || task.statusEnum === 'COMPLETED'
                ? ' null'
                : ''
            }`}
          >
            {task.startedTime && task.statusEnum !== 'COMPLETED' && (
              <>
                {!isNull(task.stopDateTime) ? (
                  <PauseCircleOutlineRoundedIcon style={{ fontSize: '16px' }} />
                ) : (
                  <Image
                    src="/icons/icon-timelapse.svg"
                    alt=""
                    width={20}
                    height={20}
                    className="icon"
                  />
                )}

                <span className="time">
                  {
                    !isNull(task.startedTime) &&
                      !isNull(task.stopDateTime) &&
                      getTimeMinutes(task.startedTime, task.stopDateTime)
                    // getElapsedTimeOneUnit(
                    //   convertDateFormat(task.startedTime, true)
                    // )
                  }
                </span>
              </>
            )}
          </div>
          {!isEmpty(task.assignees) &&
            task.assignees.map((assignee: any, index: number) => {
              return (
                <Image
                  key={index}
                  src={assignee.imageUrl || '/images/product/empty-product.svg'}
                  alt=""
                  width={20}
                  height={20}
                  className="user-profile"
                />
              );
            })}
        </div>
      </div>
    </>
  );
};

export default TaskTimelineCard;
