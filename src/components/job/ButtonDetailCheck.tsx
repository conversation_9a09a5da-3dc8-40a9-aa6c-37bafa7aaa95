import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog, Slide } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { TransitionProps } from '@mui/material/transitions';
import styled from 'styled-components';
import Image from 'next/image';
import ValueCheckDetail from '@/components/job/ValueCheckDetail';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="left" ref={ref} {...props} />;
});
const DetailStyles = styled.div`
  padding: 2rem 2rem 5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  .box-id {
    display: flex;
    align-items: center;
    gap: 1rem;
    p {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      line-height: 28px;
    }
    span {
      font-size: 18px;
    }
  }
  .box-detail-list {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 8px;
    background: #f5f7f8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .line {
      width: 1px;
      height: 20px;
      background: #cfd8dc;
    }
  }
  .label {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 1rem;
  }
  .box-image-side {
    display: flex;
    align-items: start;
    gap: 1rem;
    .box-side {
      max-width: 100%;
      width: 100%;
      padding: 1rem;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      img {
        width: 100%;
        height: 300px !important;
      }
    }
    .text {
      padding-top: 0.5rem;
      text-align: center;
      font-size: 16px;
    }
  }
`;
type Props = {
  isDisabled: boolean;
};
const ButtonDetailCheck = ({ isDisabled }: Props) => {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        variant={'outlined'}
        onClick={handleClickOpen}
        disabled={isDisabled}
      >
        รายละเอียด
      </Button>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
        className={'dialog-detail-production'}
      >
        <header>
          <h2>รายละเอียด</h2>
          <Button onClick={handleClose}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <DetailStyles>
          <div className={'box-id'}>
            <Image
              src={'/images/mock-tuckend boxes.svg'}
              alt={''}
              width={80}
              height={80}
            />
            <div>
              <p>********** </p> <span>Tuck End Box</span>
            </div>
          </div>
          <div className={'box-detail-list'}>
            <div>ขนาด 200x50x80 mm</div>
            <div className={'line'} />
            <div>จำนวนผลิต 1,000 ชิ้น</div>
            <div className={'line'} />
            <div>จำนวนเผื่อเสีย 30 ชิ้น</div>
          </div>
          {[...Array(7)].map((_, index: number) => (
            <ValueCheckDetail key={index} />
          ))}
          <div>
            <div className={'label'}>รูปขนาดกางออก</div>
            <div className={'box-image-side'}>
              <div>
                <div className={'box-side'}>
                  <Image
                    src={'/images/img-front-side.svg'}
                    alt={''}
                    width={500}
                    height={500}
                  />
                </div>
                <div className={'text'}>ด้านหน้า</div>
              </div>
              <div>
                <div className={'box-side'}>
                  <Image
                    src={'/images/img-back-side.svg'}
                    alt={''}
                    width={500}
                    height={500}
                  />
                </div>
                <div className={'text'}>ด้านหลัง</div>
              </div>
            </div>
          </div>
        </DetailStyles>
      </Dialog>
    </>
  );
};

export default ButtonDetailCheck;
