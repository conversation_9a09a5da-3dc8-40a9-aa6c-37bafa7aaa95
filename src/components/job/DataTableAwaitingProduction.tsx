import React, { useEffect, useState } from 'react';
import TableTools from '@/components/global/TableTools';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import Image from 'next/image';
import apiLayData from '@/services/order/layData';
import dayjs from 'dayjs';
import { numberWithCommas } from '@/utils/number';
import { isNull } from 'lodash';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import PopoverAction from '@/components/PopoverActionn';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { useRouter } from 'next/router';

type Props = {
  totalElements: number;
  setTotalElements: (value: number) => void;
  handleCreateWithRow: (row: any) => void;
};
const DataTableAwaitingProduction = ({
  totalElements,
  setTotalElements,
  handleCreateWithRow,
}: Props) => {
  const router = useRouter();
  const [rows, setRow] = useState<any[]>([]);
  const [filtersLayData, setFiltersLayData] = useState({
    page: 0,
    size: 10,
    isDesc: true,
    search: '',
    layDataStatusId: 3, // สถานะรอการผลิต
    isJob: false,
  });
  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center justify-center"
            style={{
              columnGap: '14px',
              maxWidth: '100%',
            }}
          >
            <Image
              src={
                params.row.productImageUrl ||
                '/images/product/empty-product.svg'
              }
              width={40}
              height={40}
              alt=""
              style={{
                borderRadius: '4px',
                objectFit: 'cover',
                minWidth: '40px',
              }}
            />
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '1',
              }}
            >
              {params.row.ldCode || '-'}
            </div>
          </div>
        );
      },
    },
    {
      field: 'layDataOrderNo',
      headerName: 'เลขที่ออเดอร์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'customer',
      headerName: 'ชื่อลูกค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return params.row.customer.name;
      },
    },
    {
      field: 'quantity',
      headerName: 'จำนวนผลิต',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        if (!isNull(params.row.layDataQuantity)) {
          return (
            <div>{`${numberWithCommas(
              params.row.layDataQuantity?.quantity
            )} ชิ้น`}</div>
          );
        }
        return <span style={{ color: '#CFD8DC' }}>ไม่มี</span>;
      },
    },
    {
      field: 'totalSalePrice',
      headerName: 'ราคาสินค้า (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        if (!isNull(params.row.layDataQuantity)) {
          return numberWithCommas(params.row.layDataQuantity?.totalSalePrice);
        }
        return <span style={{ color: '#CFD8DC' }}>ไม่มี</span>;
      },
    },
    {
      field: 'layDataStatus',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      flex: 0.8,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        if (params.row.layDataStatus) {
          return (
            <div className={`lay-data-status`}>
              {params.row.layDataStatus.name}
            </div>
          );
        }
        return <span style={{ color: '#CFD8DC' }}>ไม่มี</span>;
      },
    },
    {
      field: 'created',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.created.name || (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: 'modifiedDate',
      headerName: 'อัปเดตล่าสุด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.modifiedDate ? (
              `${dayjs(params.row.modifiedDate).format('DD/MM/YYYY HH:mm')} น.`
            ) : (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 88,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  disabled: false,
                  IconElement: () => <AddRoundedIcon />,
                  title: 'สร้างใบสั่งผลิตสินค้า',
                  onAction: () => {
                    handleCreateWithRow(params.row);
                  },
                },
                {
                  disabled: false,
                  IconElement: () => <DescriptionOutlinedIcon />,
                  title: 'รายละเอียด',
                  onAction: async () => {
                    await router.push(
                      `/orders/${params.row.layDataOrderId}/spec?step=การผลิต`
                    );
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const getLayDataList = async (params: any) => {
    const res = await apiLayData.getLayDataPage(params);
    if (res.status === true) {
      setRow(res.data.content);
      setTotalElements(res.data.totalElements);
    }
  };
  useEffect(() => {
    getLayDataList(filtersLayData).then();
  }, [filtersLayData]);
  return (
    <AppTableStyle $rows={rows}>
      <div className="content-wrap job-list">
        <div>
          <TableTools
            tools={['search']}
            title={`${totalElements} รายการ`}
            makeNewFilter={(_newFilter: any) => {
              // console.log(newFilter);
            }}
          />
          <ScrollBarStyled>
            <HeaderColumnAction text="จัดการ" width={88} />
            <DataGrid
              hideFooter={true}
              rows={rows || []}
              columns={columns}
              paginationMode="server"
              // checkboxSelection={true}
              rowCount={totalElements || 0}
              // pageSize={filters.size}
              disableSelectionOnClick={false}
              autoHeight={true}
              sortModel={[]}
              getRowHeight={() => 56}
              headerHeight={48}
              components={{
                NoRowsOverlay: () => <TableNoRowsOverlay />,
                LoadingOverlay: () => <TableLoadingOverlay />,
              }}
            />
          </ScrollBarStyled>
        </div>

        <div className="px-[16px] pb-[10px]">
          <AppPagination
            filters={filtersLayData}
            totalElements={totalElements || 0}
            handleChangeFilters={(newValues: any) => {
              setFiltersLayData(newValues);
            }}
          />
        </div>
      </div>
    </AppTableStyle>
  );
};

export default DataTableAwaitingProduction;
