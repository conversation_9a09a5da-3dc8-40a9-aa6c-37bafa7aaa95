import React from 'react';
import Image from 'next/image';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionDetails from '@mui/material/AccordionDetails';

const WorkTimeDetail = () => {
  return (
    <div className="box-work">
      <h3>การลงเวลางาน</h3>
      <div className={'box-work-time'}>
        {[1, 2, 3].map((data: number, index: number) => {
          return (
            <Accordion defaultExpanded={index === 0} key={data}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <header>
                  <div>
                    <Image
                      src={'/images/profile-mockup.svg'}
                      alt={'image'}
                      width={40}
                      height={40}
                    />
                    <div>
                      <p>ธนันท์ศักดิ์ วงศรีสวัสดิ์</p>
                      <span>ฝ่ายผลิต • หัวหน้าฝ่ายผลิต</span>
                    </div>
                  </div>
                  <div className={'time'}>00 : 23 : 59 : 08</div>
                </header>
              </AccordionSummary>
              <AccordionDetails>
                <div className={'details'}>
                  <div className={'point'}>
                    <div className={'dot'} />
                    <div className={'line'} />
                  </div>
                  <div className={'work-list'}>
                    <div className={'date'}>10 ม.ค. 2568</div>
                    <ul>
                      <li>
                        <span>08:10 : </span>
                        มอบหมายงานจาก ทนงศักดิ์ ต้นนพรัตน์
                      </li>
                      <li className={'text-red'}>
                        <span>13:08 : </span>
                        กำลังบันทึกเวลาทำงาน...
                      </li>
                    </ul>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </div>
    </div>
  );
};

export default WorkTimeDetail;
