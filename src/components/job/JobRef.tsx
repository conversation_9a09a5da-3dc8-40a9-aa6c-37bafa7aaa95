import React, { useEffect } from 'react';
import styled from 'styled-components';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import AppDateCalendar from '@/components/global/AppDateCalendar';
import DragHandleRoundedIcon from '@mui/icons-material/DragHandleRounded';
import KeyboardArrowUpRoundedIcon from '@mui/icons-material/KeyboardArrowUpRounded';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import dayjs from 'dayjs';

const JobRefStyles = styled.div`
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 10;
  background: #fff;

  .line-bottom {
    width: 100%;
    min-height: 8px;
    border-top: 1px solid #dbe2e5;
    background: #f5f7f8;
  }

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 24px;
  }

  .ref {
    font-size: 32px;
    font-weight: 600;
  }
  .action {
    display: flex;
    align-items: center;
    gap: 1rem;
    .label {
      p {
        margin: 0;
        margin-bottom: 0.2rem;
      }
      > .MuiInputBase-root,
      > .MuiFormControl-root {
        width: 200px;
      }
    }
  }
`;
type Props = {
  jobRefLabel: string;
  priorityLevel?: number;
  scheduleDate?: number;
  scheduledStartDate?: number;
  formik: any;
};
const JobRef = ({
  jobRefLabel,
  priorityLevel,
  scheduleDate,
  scheduledStartDate,
  formik,
}: Props) => {
  const dataPriorityLevel = [
    {
      id: 1,
      name: 'ด่วน',
      icon: (
        <KeyboardArrowUpRoundedIcon style={{ color: 'rgb(255, 86, 48)' }} />
      ),
    },
    {
      id: 2,
      name: 'ปกติ',
      icon: <DragHandleRoundedIcon style={{ color: '#FFAB00' }} />,
    },
    {
      id: 3,
      name: 'ไม่ด่วน',
      icon: <KeyboardArrowDownRoundedIcon style={{ color: '#0065ff' }} />,
    },
  ];
  useEffect(() => {
    if (scheduleDate) {
      formik.setFieldValue(
        'scheduleDate',
        dayjs(scheduleDate).format('YYYY-MM-DD')
      );
    }
    if (scheduledStartDate) {
      formik.setFieldValue(
        'scheduledStartDate',
        dayjs(scheduledStartDate).format('YYYY-MM-DD')
      );
    }
  }, [scheduleDate, scheduledStartDate]);
  useEffect(() => {
    if (priorityLevel) formik.setFieldValue('priorityLevel', priorityLevel);
  }, [priorityLevel]);
  useEffect(() => {
    if (formik.values.scheduledStartDate > formik.values.scheduleDate) {
      formik.setFieldValue(
        'scheduleDate',
        dayjs(formik.values.scheduledStartDate).format('YYYY-MM-DD')
      );
    }
  }, [formik.values]);

  return (
    <>
      <JobRefStyles>
        <div className={'content'}>
          <div className={'ref'}>{jobRefLabel}</div>
          <div className={'action'}>
            <div className={'label'}>
              <p>กำหนดรับสินค้า</p>
              <AppDateCalendar
                minDate={new Date(formik.values.scheduledStartDate)}
                data={formik.values.scheduleDate}
                handleChange={(date: any) => {
                  formik.setFieldValue(
                    'scheduleDate',
                    dayjs(date).format('YYYY-MM-DD')
                  );
                }}
              />
              {/* {messageError && ( */}
              {/*  <FormHelperText error>{messageError}</FormHelperText> */}
              {/* )} */}
            </div>
            <div className={'label'}>
              <p>กำหนดการผลิต</p>
              <AppDateCalendar
                // maxDate={new Date(formik.values.scheduleDate)}
                data={formik.values.scheduledStartDate}
                handleChange={(date: any) => {
                  formik.setFieldValue(
                    'scheduledStartDate',
                    dayjs(date).format('YYYY-MM-DD')
                  );
                }}
              />
              {/* {formik.errors.scheduledStartDate && ( */}
              {/*  <FormHelperText error> */}
              {/*    {formik.errors.scheduledStartDate} */}
              {/*  </FormHelperText> */}
              {/* )} */}
            </div>
            <div className={'label'}>
              <p>ความสำคัญ</p>
              <Select
                value={formik.values.priorityLevel}
                onChange={formik.handleChange}
                id={'priorityLevel'}
                name={'priorityLevel'}
              >
                {dataPriorityLevel.map((item: any) => (
                  <MenuItem value={item.id} key={item.id}>
                    <div
                      className={'box-menu'}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '.3rem',
                      }}
                    >
                      {item.icon}
                      <span>{item.name}</span>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </div>
          </div>
        </div>
        <div className={'line-bottom'} />
      </JobRefStyles>
    </>
  );
};

export default JobRef;
