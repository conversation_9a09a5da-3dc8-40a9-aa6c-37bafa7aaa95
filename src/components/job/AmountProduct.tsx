import React from 'react';
import { Box, Grid } from '@mui/material';
import Image from 'next/image';

const AmountProduct = () => {
  return (
    <div className="box-amount">
      <header>
        <div className={'title'}>จำนวนสินค้า</div>
      </header>
      <Box className={'details'} sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          {[...Array(4)].map((_item, i: number) => (
            <Grid item xs={6} key={i}>
              <div className={'item'}>
                <div>
                  <Image
                    src={'/icons/package_2.svg'}
                    alt={''}
                    width={20}
                    height={20}
                  />{' '}
                  สั่งผลิต
                </div>
                <p>4,000 ใบพิมพ์</p>
              </div>
            </Grid>
          ))}
        </Grid>
      </Box>
      <div className={'products'}>
        {[...Array(3)].map((_item, i: number) => (
          <div className="item" key={i}>
            <Image
              src={'/images/tuckend-boxes.svg'}
              alt={''}
              width={40}
              height={40}
            />
            <div>
              <p>LD-2023003 • Tuck End Box</p>
              <span>1,000 สั่งซื้อ | 50 เผื่อเสีย | 0 ผลิตสำเร็จ</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AmountProduct;
