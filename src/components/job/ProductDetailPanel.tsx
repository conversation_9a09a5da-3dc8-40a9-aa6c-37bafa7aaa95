import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import ProductLabelValue from '@/components/job/ProductLabelValue';
import { numberWithCommas } from '@/utils/number';
import { Button } from '@mui/material';
import { TGroupLayData } from '@/types/prepare-material';
import { isEmpty } from 'lodash';

const ProductDetailPanelStyles = styled.div`
  border-radius: 12px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  background: #fff;
  margin-top: 1rem;
  min-height: 90px;
  &.not-found {
    display: flex;
    align-items: center;
    justify-content: center;
    .box-not-found {
      text-align: center;
      height: 100%;
      color: #cfd8dc;
    }
  }
  .box-product-detail-panel {
    border-bottom: 1px solid #dbe2e5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 42px;
    padding: 24px;
    flex-wrap: wrap;
    row-gap: 24px;
    &:last-child {
      border-bottom: none;
    }
    label {
      font-size: 12px;
      color: #90a4ae;
    }
    .value {
      font-size: 16px;
    }
    p {
      margin: 0;
    }
    .left-side-wrapper {
      display: flex;
      align-items: center;
      column-gap: 40px;
      flex-wrap: wrap;
      row-gap: 24px;
      .box-img-name {
        display: flex;
        align-items: center;
        gap: 16px;
        img {
          border-radius: 8px;
        }
        .name {
          overflow: hidden;
          p {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
        }
      }
      .value-panel {
        display: flex;
        align-items: center;
        column-gap: 42px;
        max-width: 100%;
        overflow: hidden;
        //width: 100%;
        flex-wrap: wrap;
        row-gap: 24px;
        .amount-order {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          max-width: 100%;
          label {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
          .value {
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
        }
      }
    }

    .box-btn-detail {
      //
    }
  }
`;
type Props = {
  groupLayData?: TGroupLayData[];
};
const ProductDetailPanel = ({ groupLayData }: Props) => {
  return (
    <ProductDetailPanelStyles
      className={`${isEmpty(groupLayData) ? 'not-found' : ''}`}
    >
      {isEmpty(groupLayData) ? (
        <div className={'box-not-found'}>ไม่พบข้อมูล</div>
      ) : (
        groupLayData?.map((item: TGroupLayData, index: number) => {
          const {
            ldCode,
            productModelImageUrl,
            productModelName,
            quantity,
            quantityAllowance,
            totalSalePrice,
            quantityPerSheet,
          } = item;
          return (
            <div className={'box-product-detail-panel'} key={index}>
              <div className="left-side-wrapper">
                <div className={'box-img-name'}>
                  <Image
                    src={productModelImageUrl}
                    alt={''}
                    width={70}
                    height={70}
                  />
                  <div className={'name'}>
                    <p>{ldCode}</p>
                    <span>{productModelName}</span>
                  </div>
                </div>
                <div className={'value-panel'}>
                  <ProductLabelValue
                    label={'จำนวนสั่งซื้อ'}
                    value={`${numberWithCommas(Number(quantity))} ตัว`}
                  />
                  <ProductLabelValue
                    label={'จำนวนเผื่อเสีย'}
                    value={`${numberWithCommas(Number(quantityAllowance))} ตัว`}
                  />
                  <ProductLabelValue
                    label={'สั่งซื้อ+เผื่อเสีย'}
                    value={`${numberWithCommas(
                      Number(quantityAllowance) + Number(quantity)
                    )} ตัว`}
                  />
                  <ProductLabelValue
                    label={'มูลค่าสินค้า'}
                    value={`${numberWithCommas(Number(totalSalePrice))} บาท`}
                  />
                  <ProductLabelValue label={'กำหนดรับสินค้า'} value={`-`} />
                  <ProductLabelValue
                    label={'จำนวนผลิต/ใบพิมพ์'}
                    value={`${numberWithCommas(Number(quantityPerSheet))} ตัว`}
                  />
                </div>
              </div>
              <div className={'box-btn-detail'}>
                <Button variant={'outlined'} disabled={true}>
                  รายละเอียด
                </Button>
              </div>
            </div>
          );
        })
      )}
    </ProductDetailPanelStyles>
  );
};

export default ProductDetailPanel;
