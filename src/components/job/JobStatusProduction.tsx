import React from 'react';
import { isEmpty } from 'lodash';
import LdAccordionWrapper from '@/components/order/spec/LdAccordionWrapper';
import JobProductionAccordion from '@/components/job/JobProductionAccordion';

type Props = {
  dataProductionOrderStage?: any;
};
const JobStatusProduction = ({ dataProductionOrderStage }: Props) => {
  return (
    <>
      <div className={'box-job-status'}>
        <header className={'box-header-production'}>
          <div className={'box-text-header'}>
            <h3>ขั้นตอนการผลิต</h3>
          </div>
          {/* <div className={'group-buttons'}> */}
          {/*  <Button */}
          {/*    variant={'outlined'} */}
          {/*    className={'btn-withdraw'} */}
          {/*    startIcon={ */}
          {/*      <Image */}
          {/*        src={'/icons/ic-news.svg'} */}
          {/*        alt={'icon'} */}
          {/*        width={24} */}
          {/*        height={24} */}
          {/*      /> */}
          {/*    } */}
          {/*  > */}
          {/*    บันทึกการทำงาน */}
          {/*  </Button> */}
          {/* </div> */}
        </header>
        <div className={'box-production'}>
          {!isEmpty(dataProductionOrderStage?.stage) && (
            <LdAccordionWrapper>
              {dataProductionOrderStage?.stage?.map(
                (item: any, idx: number) => {
                  return (
                    <JobProductionAccordion
                      key={idx}
                      indexMockStart={idx}
                      stageData={item}
                    />
                  );
                }
              )}
            </LdAccordionWrapper>
          )}
          {/* {!isEmpty(dataJob?.stage) && */}
          {/*  dataJob?.stage?.map((item: TStage, index: number) => { */}
          {/*    console.log(item); */}
          {/*    return ( */}
          {/*      <Accordion */}
          {/*        key={index} */}
          {/*        defaultExpanded={index === 0} */}
          {/*        className={`card-accordion ${index === 0 ? 'active' : ''}`} */}
          {/*      > */}
          {/*        <AccordionSummary */}
          {/*          expandIcon={<ExpandMoreIcon />} */}
          {/*          className={'box-accordion-summary'} */}
          {/*        > */}
          {/*          <div className={'text-header'}> */}
          {/*            <div className={'zone-point-and-line'}> */}
          {/*              <div className={'line-top'} /> */}
          {/*              <div className="box-point"> */}
          {/*                <div /> */}
          {/*              </div> */}
          {/*              <div className={'line-bottom'} /> */}
          {/*            </div> */}
          {/*            <span>{item.productionPlanStage.name}</span> */}
          {/*            {index === 0 && ( */}
          {/*              <div className={'tag-status'}> */}
          {/*                <Image */}
          {/*                  src="/icons/status-in-progress.svg" */}
          {/*                  alt={'icon'} */}
          {/*                  width={20} */}
          {/*                  height={20} */}
          {/*                /> */}
          {/*                <span>กำลังดำเนินการ</span> */}
          {/*              </div> */}
          {/*            )} */}
          {/*          </div> */}
          {/*        </AccordionSummary> */}
          {/*        <AccordionDetails className={'box-accordion-detail'}> */}
          {/*          <div className={'text-details'}> */}
          {/*            <div className={'zone-point-and-line'}> */}
          {/*              <div className={'box-point-child'}> */}
          {/*                /!* <div className={'line-top'} /> *!/ */}
          {/*                <div className={'bg-dot'}> */}
          {/*                  <div className={'dot'} /> */}
          {/*                </div> */}
          {/*                <div className="line"></div> */}
          {/*                /!* <div className={'line-bottom'} /> *!/ */}
          {/*              </div> */}
          {/*              <div className={'details'}> */}
          {/*                <div> */}
          {/*                  <div className={'result'}> */}
          {/*                    <span className={'text'}> */}
          {/*                      บันทึกข้อมูล{item.productionPlanStage.name} */}
          {/*                    </span> */}
          {/*                    <span className={'value'}> */}
          {/*                      มอบหมายงาน{' '} */}
          {/*                      {dayjs(dataJob?.scheduledStartDate).format( */}
          {/*                        'DD/MM/YYYY HH:mm' */}
          {/*                      )}{' '} */}
          {/*                      น. */}
          {/*                    </span> */}
          {/*                  </div> */}
          {/*                  <div className={'btn'}> */}
          {/*                    /!* <JobAvatarDetails /> *!/ */}
          {/*                    /!* <ButtonDetailProduction /> *!/ */}
          {/*                  </div> */}
          {/*                </div> */}
          {/*                /!* <div className={'result-table'}> *!/ */}
          {/*                /!*  <ul> *!/ */}
          {/*                /!*    {dataPaperCut.map((item: any, index: number) => { *!/ */}
          {/*                /!*      return ( *!/ */}
          {/*                /!*        <li key={index}> *!/ */}
          {/*                /!*          <header> *!/ */}
          {/*                /!*            <Image *!/ */}
          {/*                /!*              src={item?.imgUrl} *!/ */}
          {/*                /!*              alt={'icon'} *!/ */}
          {/*                /!*              width={20} *!/ */}
          {/*                /!*              height={20} *!/ */}
          {/*                /!*            /> *!/ */}
          {/*                /!*            <span>{item?.label}</span> *!/ */}
          {/*                /!*          </header> *!/ */}
          {/*                /!*          {item?.value ? ( *!/ */}
          {/*                /!*            <div className={'value'}>{item?.value}</div> *!/ */}
          {/*                /!*          ) : ( *!/ */}
          {/*                /!*            <div className={'value not-found'}> *!/ */}
          {/*                /!*              ไม่ระบุ *!/ */}
          {/*                /!*            </div> *!/ */}
          {/*                /!*          )} *!/ */}
          {/*                /!*        </li> *!/ */}
          {/*                /!*      ); *!/ */}
          {/*                /!*    })} *!/ */}
          {/*                /!*  </ul> *!/ */}
          {/*                /!* </div> *!/ */}
          {/*                /!* <div className={'note'}> *!/ */}
          {/*                /!*  <span>หมายเหตุ: </span> *!/ */}
          {/*                /!*  <span> *!/ */}
          {/*                /!*    เกิดความล่าช้าเนื่องจากไปช่วยขนของ (ช่วงเวลา *!/ */}
          {/*                /!*    08:30-09:30) *!/ */}
          {/*                /!*  </span> *!/ */}
          {/*                /!* </div> *!/ */}
          {/*              </div> */}
          {/*            </div> */}
          {/*          </div> */}
          {/*        </AccordionDetails> */}
          {/*      </Accordion> */}
          {/*    ); */}
          {/*  })} */}
        </div>
      </div>
    </>
  );
};

export default JobStatusProduction;
