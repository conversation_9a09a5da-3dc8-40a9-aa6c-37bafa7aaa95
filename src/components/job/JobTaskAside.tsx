import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import ToggleButtonGroup from '@/components/common/ToggleButtonGroup';
import JobCardWrapper from '@/components/job/JobCardWrapper';
import { useAppDispatch, useAppSelector } from '@/store';
import { jobSelector, setScrollY } from '@/store/features/job';
import { dateStringDayNow } from '@/utils/date';

const JobAsideWrapper = styled.div`
  position: relative;
  height: calc(100dvh - (56px + 57px + 64px));
  min-width: 448px;
  max-width: 448px;
  z-index: 3;
  background: white;
  &:before {
    content: '';
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    bottom: 0px;
  }
`;
const ToggleWrapStyled = styled.div`
  height: 88px;
  width: 100%;
  position: relative;
  padding: 24px;
  border-right: 1px solid #dbe2e5;
  &:before {
    content: '';
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    bottom: -24px;
  }
`;
const JobTaskAsideStyled = styled.div`
  width: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #dbe2e5;
  height: calc(100dvh - (56px + 57px + 64px + 88px));
  overflow: auto;
  row-gap: 24px;
  position: relative;
  .gender-wrap {
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: #f5f7f8;
    .MuiToggleButtonGroup-root {
      height: 40px;
      margin: 0 3px;
      .Mui-selected {
        background-color: #263238 !important;
        color: #fff !important;
        border-radius: 4px;
        z-index: 1;
        &:before {
          content: '';
          display: none;
        }
        &:after {
          content: '';
          display: none;
        }
      }
      button {
        background-color: transparent;
        color: #263238;
        font-size: 16px;
        font-weight: 600;
        border: 0;
        &:nth-child(2) {
          &:before {
            content: '';
            height: 24px;
            width: 1px;
            background-color: #dbe2e5;
            position: absolute;
            left: 0;
          }
          &:after {
            content: '';
            height: 24px;
            width: 1px;
            background-color: #dbe2e5;
            position: absolute;
            right: 0;
          }
        }
      }
    }
  }
`;
type Props = {
  loadingTasks: boolean;
  dataWorkSchedule: any;
  filtersWork: any;
  setFiltersWork: (date: any) => void;
};
const JobTaskAside = ({
  loadingTasks,
  dataWorkSchedule,
  filtersWork,
  setFiltersWork,
}: Props) => {
  const dispatch = useAppDispatch();
  const [asideTabValue, setAsideTabValue] = useState<string>('วันนี้');
  const asideRef = useRef<HTMLDivElement>(null);
  const { layout } = useAppSelector(jobSelector);

  const handleScroll = (scrollTop: number) => {
    dispatch(setScrollY(scrollTop));
  };
  const getTomorrowDate = (value: string) => {
    const date = new Date();
    switch (value) {
      case 'พรุ่งนี้':
        date.setDate(date.getDate() + 1);
        return date;
      case 'เมื่อวาน':
        date.setDate(date.getDate() - 1);
        return date;
      default:
        return date;
    }
  };

  useEffect(() => {
    if (filtersWork) {
      setAsideTabValue(dateStringDayNow(filtersWork));
    }
  }, [filtersWork]);
  useEffect(() => {
    if (asideRef.current) {
      asideRef.current.scrollTop = layout.scrollY;
    }
  }, [layout]);

  return (
    <JobAsideWrapper>
      <ToggleWrapStyled>
        <ToggleButtonGroup
          buttonItem={['เมื่อวาน', 'วันนี้', 'พรุ่งนี้']}
          value={asideTabValue}
          handleToggle={(value: string) => {
            setAsideTabValue(value);
            const valueDate = getTomorrowDate(value);
            setFiltersWork(valueDate);
          }}
        />
      </ToggleWrapStyled>
      <JobTaskAsideStyled
        ref={asideRef}
        onScroll={(e: React.UIEvent<HTMLDivElement>) => {
          const { scrollTop } = e.currentTarget;
          handleScroll(scrollTop);
        }}
      >
        <JobCardWrapper
          dataWorkSchedule={dataWorkSchedule}
          loadingTasks={loadingTasks}
        />
      </JobTaskAsideStyled>
    </JobAsideWrapper>
  );
};

export default JobTaskAside;
