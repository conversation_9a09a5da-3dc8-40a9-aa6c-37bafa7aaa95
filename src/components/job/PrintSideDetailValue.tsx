import React from 'react';
import UploadImageSide from '@/components/job/UploadImageSide';
import Image from 'next/image';
import {
  ECoatingOrderEnum,
  TDataLayoutFile,
  TLayDataDetail,
} from '@/types/prepare-material';
import { isEmpty } from 'lodash';

type Props = {
  title: string;
  imageUrl?: string;
  layDataDetail?: TLayDataDetail;
  typeLayOut: number;
  productionOrderId?: number;
  layoutFile?: TDataLayoutFile;
  setLayoutFile?: (data: TDataLayoutFile) => void;
  isSetting?: boolean;
};
const PrintSideDetailValue = ({
  title,
  layDataDetail,
  imageUrl,
  typeLayOut,
  productionOrderId,
  layoutFile,
  setLayoutFile,
  isSetting,
}: Props) => {
  const text = (coatingOrderEnum: string) => {
    switch (coatingOrderEnum) {
      case ECoatingOrderEnum.BEFORE:
        return 'ก่อน';
      case ECoatingOrderEnum.AFTER:
        return 'หลัง';
      default:
    }
  };
  return (
    <div className="box-side">
      <header>
        <h3>{title}</h3>
      </header>
      <div className={'detail-side'}>
        {isSetting ? (
          <UploadImageSide
            imageUrl={imageUrl}
            productionOrderId={productionOrderId}
            typeLayOut={typeLayOut}
            layoutFile={layoutFile}
            setLayoutFile={(data: TDataLayoutFile) =>
              setLayoutFile && setLayoutFile(data)
            }
          />
        ) : !imageUrl ? (
          <div className={'box-not-found-img-layout'}>
            <div>
              <div className={'image'}>
                <Image
                  src={'/icons/ic-photo.svg'}
                  alt={'ic photo'}
                  width={20}
                  height={20}
                />
              </div>
              <p>ไม่มีรูปเลย์เอาท์</p>
            </div>
          </div>
        ) : (
          <div className="image detail-url">
            <Image src={imageUrl} alt={''} width={800} height={500} />
          </div>
        )}
        <div className={'box-data-detail'}>
          <div className={'label-value'}>
            <div className={'title'}>
              การพิมพ์ {layDataDetail?.coating?.printSystemName || '-'}
            </div>
            <div
              className={`value ${
                (layDataDetail?.coating?.layDataProductColor?.length ?? 0) > 0
                  ? 'has-tags'
                  : ''
              }`}
            >
              {(layDataDetail?.coating?.layDataProductColor?.length ?? 0) >
              0 ? (
                layDataDetail?.coating?.layDataProductColor?.map((item, i) => {
                  return (
                    <div className="tag" key={i}>
                      <Image
                        className={'rounded-full'}
                        src={item.printColorImageUrl || '/images/icon-tag.svg'}
                        alt={''}
                        width={20}
                        height={20}
                      />
                      {!isEmpty(item.colorName) && (
                        <span>{item.colorName}</span>
                      )}
                    </div>
                  );
                })
              ) : (
                <span className="not-found">-</span>
              )}
            </div>
          </div>
          <div className={'label-value finish'}>
            <div className={'title'}>เคลือบ</div>
            <div className={'col-coating'}>
              {(layDataDetail?.finish?.length ?? 0) > 0 ? (
                layDataDetail?.finish?.map((item, i) => {
                  const coatingEnum =
                    item.coatingOrderEnum || (item as any).coatingPositionEnum;
                  return (
                    <div className={'value'} key={i}>
                      <div className={'label-value-group'}>
                        <span>{text(coatingEnum)}: </span>{' '}
                        <div>
                          {item.finishName || '-'} •{' '}
                          {item.coatingMasterName || '-'}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className={'value not-found'}>-</div>
              )}
            </div>
          </div>
          {(layDataDetail?.extra?.length ?? 0) > 0 ? (
            layDataDetail?.extra?.map((item, i) => {
              return (
                <div className={'label-value extra'} key={i}>
                  <div className={'title'}>เทคนิคพิเศษ</div>
                  <div className={'value'}>
                    {item.extraMaterName || '-'} •{' '}
                    {item.extraSubMaterial || '-'} •{' '}
                    {item.extraAreaDimension || '-'}
                  </div>
                </div>
              );
            })
          ) : (
            <div className={'label-value extra'}>
              <div className={'title'}>เทคนิคพิเศษ</div>
              <div className={'value not-found'}>-</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PrintSideDetailValue;
