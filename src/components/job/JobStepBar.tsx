import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { useRouter } from 'next/router';

const JobStepBarStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: 100%;
  gap: 16px;
  .step {
    flex: 1 0 124px;
    height: 40px;
    border: 1px solid #dbe2e5;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: white;
    user-select: none;
    border-radius: 20px;
    &:active {
      box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
      background: #fafbfb;
    }
    .icon {
      position: absolute;
      left: 8px;
      display: flex;
      align-items: center;
    }
    .text {
      font-weight: 600;
    }
    &.active {
      background: #fafbfb;
      box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
      cursor: default;
    }
    &.disable {
      pointer-events: none;
      color: #cfd8dc;
    }
  }
`;

const JobStepBar = () => {
  const steps = [
    {
      name: 'เลย์เอาท์',
      step: 'layout',
    },
    {
      name: 'ตรวจสอบก่อนพิมพ์',
      step: 'prePrint',
    },
    {
      name: 'จัดเตรียมวัสดุ',
      step: 'material',
    },
    {
      name: 'การผลิต',
      step: 'production',
    },
    {
      name: 'ตรวจสอบหลังผลิต',
      step: 'postProduction',
    },
    {
      name: 'ผลิตสำเร็จ',
      step: 'completed',
    },
  ];

  const router = useRouter();
  const { id, step: queryStep } = router.query;
  const currentStep = typeof queryStep === 'string' ? queryStep : 'layout';

  let activeStep = steps.findIndex((s) => s.step === currentStep);
  if (activeStep === -1) {
    activeStep = 0;
  }

  return (
    <JobStepBarStyled>
      {steps.map((item, index) => (
        <div
          key={index}
          className={`step ${activeStep < index ? 'disable' : ''} ${
            activeStep === index ? 'active' : ''
          }`}
          onClick={() => {
            if (id) {
              router.push(`/job/${id}?step=${item.step}`);
            }
          }}
        >
          <div className="icon">
            {activeStep >= index && (
              <Image
                src={
                  activeStep > index
                    ? '/icons/task-done.svg'
                    : '/icons/schedule.svg'
                }
                alt=""
                width={24}
                height={24}
              />
            )}
          </div>
          <div className="text">{item.name}</div>
        </div>
      ))}
    </JobStepBarStyled>
  );
};

export default JobStepBar;
