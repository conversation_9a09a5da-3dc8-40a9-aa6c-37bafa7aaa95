import React from 'react';
import styled from 'styled-components';
import { Skeleton } from '@mui/material';

const SkeletonJobCardWrapperStyle = styled.div`
  width: 100%;
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  background: white;
  cursor: pointer;
  min-height: 146px;
  .tag {
    display: flex;
    align-items: center;
    gap: 5px;
    width: 100%;
    span {
      width: calc(100% / 3) !important;
    }
  }
`;
const SkeletonJobCardWrapper = () => {
  return (
    <>
      {[...Array(8)].map((_, index) => (
        <SkeletonJobCardWrapperStyle key={index}>
          <div>
            <Skeleton variant="text" height={'20px'} width={'100px'} />
            <Skeleton variant="text" height={'20px'} width={'100%'} />
            <Skeleton variant="text" height={'20px'} width={'100%'} />
          </div>
          <div className={'tag'}>
            {[...Array(3)].map((_, index) => (
              <Skeleton
                key={index}
                variant="rounded"
                height={'30px'}
                width={'50px'}
              />
            ))}
          </div>
        </SkeletonJobCardWrapperStyle>
      ))}
    </>
  );
};

export default SkeletonJobCardWrapper;
