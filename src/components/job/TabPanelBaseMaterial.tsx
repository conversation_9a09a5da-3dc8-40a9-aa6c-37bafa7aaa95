import React, { useEffect, useState } from 'react';
import { MenuItem, Select, SelectChangeEvent } from '@mui/material';
import styled from 'styled-components';
import Image from 'next/image';
import apiMaterial from '@/services/stock/material';
import { isEmpty } from 'lodash';

type Props = {
  setMaterialData: (materialId: number) => void;
  valueTab: number;
  onSelectMaterialValue: (subMaterialId: number) => void;
};
const TabPanelBaseMaterialStyles = styled.div`
  padding-top: 1rem;
  > .MuiInputBase-root {
    width: 100%;
    margin-bottom: 1rem;
  }
  .box-result {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 330px;
    overflow: auto;
    .result {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem;
      cursor: pointer;
      transition: all 0.4s ease-in-out;
      &:hover {
        background: #e8e8e899;
        border-radius: 6px;
      }
      .img-name {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        img {
          border-radius: 6px;
        }
      }
      .value {
        font-weight: bold;
      }
    }
  }
`;
const TabPanelBaseMaterial = ({
  onSelectMaterialValue,
  valueTab,
  setMaterialData,
}: Props) => {
  const [subMaterialList, setSubMaterialList] = useState<any>([]);
  const [subMaterialData, setSubMaterialData] = useState<any>([]);
  const [materialId, setMaterialId] = React.useState<string>('');
  const handleChange = (event: SelectChangeEvent) => {
    setMaterialId(event.target.value);
    setMaterialData(Number(event.target.value));
    const data = subMaterialData.filter(
      (item: any) => item.id === event.target.value
    );
    setSubMaterialList(data[0]?.subMaterial);
  };
  const getListMaterialOrder = async (tab: number) => {
    const res = await apiMaterial.getListMaterialOrder();
    const data = res.data.filter((item: any) => item.materialType.id === tab);
    setSubMaterialData(data);
    setMaterialId(data[0]?.id.toString());
    setSubMaterialList(data[0]?.subMaterial);
    setMaterialData(data[0]?.id);
  };
  useEffect(() => {
    getListMaterialOrder(valueTab);
  }, [valueTab]);
  return (
    <TabPanelBaseMaterialStyles>
      <Select value={materialId} onChange={handleChange}>
        {!isEmpty(subMaterialData) &&
          subMaterialData.map((item: any, index: number) => {
            return (
              <MenuItem value={item.id} key={index}>
                {item.name}
              </MenuItem>
            );
          })}
      </Select>

      <div className={'box-result'}>
        {!isEmpty(subMaterialList) ? (
          subMaterialList?.map((item: any, index: number) => {
            // console.log(item);
            return (
              <div
                className="result"
                key={index}
                onClick={() => onSelectMaterialValue(item.id)}
              >
                <div className={'img-name'}>
                  {item.imageUrl ? (
                    <Image
                      src={item.imageUrl}
                      alt={'img material'}
                      width={45}
                      height={45}
                    />
                  ) : (
                    <Image
                      src={'/images/product/empty-product.svg'}
                      alt={'img material'}
                      width={45}
                      height={45}
                    />
                  )}
                  <span className={'name'}>{item.name}</span>
                </div>
              </div>
            );
          })
        ) : (
          <p className={'not-found-material'}>ไม่พบวัสดุ</p>
        )}
      </div>
    </TabPanelBaseMaterialStyles>
  );
};

export default TabPanelBaseMaterial;
