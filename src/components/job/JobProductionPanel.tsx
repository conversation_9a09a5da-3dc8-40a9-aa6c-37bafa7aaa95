import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';

const JobProductionPanelStyles = styled.div`
  border-radius: 12px;
  border: 1px solid #dbe2e5;
  margin: 24px;
  .box-production-panel {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    border-bottom: 1px solid #dbe2e5;
    &:last-child {
      border-bottom: none;
    }
    label {
      font-size: 12px;
      color: #90a4ae;
    }
    .value {
      font-size: 16px;
    }
    p {
      margin: 0;
    }
    .box-img-name {
      display: flex;
      align-items: center;
      gap: 1rem;
      .name {
        p {
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
    .row {
      display: flex;
      align-items: center;
      gap: 12rem;
      margin-left: 4rem;
      .box-value-label {
        .value-panel {
          display: flex;
          align-items: center;
          justify-content: start;
          .label {
            font-size: 14px;
            min-width: 130px;
          }
          .value {
            font-size: 14px;
            &.not-found {
              color: #cfd8dc;
            }
          }
        }
      }
    }
  }
`;
const JobProductionPanel = () => {
  return (
    <JobProductionPanelStyles>
      {[...Array(3)].map((_, index) => (
        <div className={'box-production-panel'} key={index}>
          <div className={'box-img-name'}>
            <Image
              src={'/images/mock-tuckend boxes.svg'}
              alt={''}
              width={70}
              height={70}
            />
            <div className={'name'}>
              <p>LD-2023003</p>
              <span>Tuck End Box • 1523x896 mm</span>
            </div>
          </div>
          <div className={'row'}>
            <div className={'box-value-label'}>
              <div className={'value-panel'}>
                <div className={'label'}>จำนวนสั่งซื้อ</div>
                <div className={'value'}>: 1,000 ชิ้น</div>
              </div>
              <div className={'value-panel'}>
                <div className={'label'}>จำนวน/เลย์เอาท์</div>
                <div className={'value'}>: 2 ชิ้น</div>
              </div>
            </div>
            <div className={'box-value-label'}>
              <div className={'value-panel'}>
                <div className={'label'}>จำนวนผลิต</div>
                <div className={'value'}>: 1,205 ชิ้น</div>
              </div>
              <div className={'value-panel'}>
                <div className={'label'}>ผลิตเสีย</div>
                <div className={'value'}>: 16 ชิ้น</div>
              </div>
            </div>
            <div className={'box-value-label'}>
              <div className={'value-panel'}>
                <div className={'label'}>ผลิตสำเร็จ</div>
                <div className={'value not-found'}>: ไม่ระบุ</div>
              </div>
              <div className={'value-panel'}>
                <div className={'label'}>เผื่อเสีย</div>
                <div className={'value not-found'}>: ไม่ระบุ</div>
              </div>
            </div>
          </div>
          <div></div>
        </div>
      ))}
    </JobProductionPanelStyles>
  );
};

export default JobProductionPanel;
