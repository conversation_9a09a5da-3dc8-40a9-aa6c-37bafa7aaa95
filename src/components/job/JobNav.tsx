import styled, { css } from 'styled-components';
import { KeyboardBackspace } from '@mui/icons-material';
import Image from 'next/image';
import React from 'react';
import { ActionGroupStyle, LoadingFadein } from '@/styles/share.styled';
import Link from 'next/link';
import { isEmpty } from 'lodash';
import { IconButton } from '@mui/material';
import ActionButton from '@/components/ActionButton';
import PopoverAction from '@/components/PopoverActionn';
import { useRouter } from 'next/router';
import ModalConfirmPrepareMaterial from '@/components/job/modal/ModalConfirmPrepareMaterial';

import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';

const JobDetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  flex-shrink: 0;
`;
const JobNavStyle = styled.div<{
  $borderBottom?: boolean;
  $backUrl?: string;
}>`
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 105;
  animation: ${LoadingFadein} 0.3s ease-in;
  padding: 0 16px;
  display: flex;
  align-items: center;
  column-gap: 16px;
  background: white;
  min-height: 64px;
  /* Desktop: horizontal layout */
  @media screen and (min-width: 1200px) {
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  /* Tablet and mobile: wrap layout */
  @media screen and (max-width: 1199px) {
    flex-wrap: wrap;
    row-gap: 12px;
    align-items: flex-start;
    padding: 16px;
  }
  @media screen and (max-width: 520px) {
    height: 196px;
  }
  @media screen and (max-width: 334px) {
    height: 268px;
  }
  > div {
    display: flex;
    align-items: center;
    gap: 12px;

    &:first-child {
      flex: 1;
      min-width: 0;

      @media screen and (max-width: 1199px) {
        width: 100%;
        justify-content: space-between;
      }
    }

    &:last-child {
      flex-shrink: 0;

      @media screen and (max-width: 1199px) {
        width: 100%;
      }
    }
  }

  ${({ $backUrl }) =>
    !isEmpty($backUrl)
      ? css`
          justify-content: space-between;
        `
      : css`
          justify-content: flex-end;
        `}
  ${({ $borderBottom }) =>
    !isEmpty($borderBottom) &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  h1 {
    font-size: 22px;
    margin: 0;
    font-weight: 600;
  }
  @media screen and (max-width: 820px) {
    top: 64px;
  }
  a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #000 !important;
    height: 40px;
    width: 40px;
    svg {
      width: 24px;
      height: 24px;
    }
  }
  ${({ $borderBottom }) =>
    $borderBottom &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
  }
  .back-button {
    flex-shrink: 0;
    a {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    svg {
      width: 24px;
      height: 24px;
      color: #263238;
    }
  }
  .print-btn {
    height: 40px;
    width: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s ease-out;
    background: white;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    position: relative;
    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateY(-50%) translateX(-50%);
    }
    &:hover {
      filter: brightness(0.9);
    }
  }
  .nav-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    //flex: 1;
    min-width: 0;
    animation: ${LoadingFadein} 0.3s ease-in;
  }
  .children {
    display: flex;
    column-gap: 12px;
    flex-shrink: 0;

    @media screen and (max-width: 640px) {
      column-gap: 8px;
    }
  }

  /* ActionGroupStyle responsive overrides */
  ${ActionGroupStyle} {
    display: flex;
    align-items: center;
    column-gap: 8px;
    flex-wrap: nowrap;

    @media screen and (max-width: 1199px) {
      flex-wrap: wrap;
      row-gap: 8px;
    }

    @media screen and (max-width: 640px) {
      column-gap: 6px;
      row-gap: 6px;
    }
  }
`;

type JobNavProps = {
  title?: string;
  backUrl?: string;
  children?: React.ReactNode;
  showBorderBottom?: boolean;
  showAvatar?: boolean;
  centerTitle?: boolean;
  animate?: boolean;
  backUrlEvent?: () => void;
  actionMenuList?: {
    IconElement?: () => any;
    title: string;
    disabled?: boolean;
    onAction: () => any;
    cssProps?: any;
  }[];
  productOrderId: number;
  approveProductionOrder: (id: number) => void;
  currentStepId?: number;
};
const JobNav = ({
  title,
  backUrl,
  children,
  showBorderBottom,
  showAvatar,
  backUrlEvent,
  actionMenuList,
  productOrderId,
  approveProductionOrder,
  currentStepId,
}: JobNavProps) => {
  const router = useRouter();
  const { id } = router.query;
  return (
    <JobNavStyle $borderBottom={showBorderBottom} $backUrl={backUrl}>
      <div>
        {backUrl && !backUrlEvent && (
          <div className="back-button">
            <IconButton size={'small'}>
              <Link href={backUrl}>
                <KeyboardBackspace />
              </Link>
            </IconButton>
          </div>
        )}
        {backUrlEvent && (
          <div className="back-button">
            <IconButton size={'small'}>
              <Link
                href={'#'}
                onClick={(event: any) => {
                  event.preventDefault();
                  backUrlEvent();
                }}
              >
                <KeyboardBackspace />
              </Link>
            </IconButton>
          </div>
        )}
        {!isEmpty(title) && <h1 className="nav-title">{title}</h1>}
        {showAvatar && (
          <div className="avatar">
            <Image src={'/icons/icon-blank-profile.svg'} alt="" fill />
          </div>
        )}
        <div className="children">
          {children}
          {/* <BentoBtn url={'/dashboard'} /> */}
        </div>
      </div>
      <div>
        <ActionGroupStyle>
          {/* <div */}
          {/*  onClick={() => { */}
          {/*    // */}
          {/*  }} */}
          {/* > */}
          {/*  <ActionButton */}
          {/*    variant="contained" */}
          {/*    color="Hon" */}
          {/*    text="นำส่งตรวจสอบก่อนพิมพ์" */}
          {/*    borderRadius={'8px'} */}
          {/*  /> */}
          {/* </div> */}
          <ModalConfirmPrepareMaterial
            approveProductionOrder={(id: number) => approveProductionOrder(id)}
            productOrderId={productOrderId}
            currentStepId={currentStepId}
          />
          <div
            onClick={() => {
              //
            }}
          >
            <ActionButton
              variant="outlined"
              color="blueGrey"
              text="ดาวน์โหลดไฟล์"
              borderRadius={'8px'}
            />
          </div>
          {currentStepId === 1 && (
            <ActionButton
              onClick={() => {
                router.push(`/job/setting/${id}`);
              }}
              variant="contained"
              color="dark"
              icon={
                <Image
                  src="/icons/icon-deployed-code.svg"
                  alt=""
                  width={20}
                  height={20}
                />
              }
              text="ตั้งค่าการผลิต"
              borderRadius={'8px'}
            />
          )}
          <JobDetailKebabWrap>
            <PopoverAction
              triggerElement={<MoreHorizRoundedIcon />}
              customItems={actionMenuList || []}
            />
          </JobDetailKebabWrap>
        </ActionGroupStyle>
      </div>
    </JobNavStyle>
  );
};

export default JobNav;
