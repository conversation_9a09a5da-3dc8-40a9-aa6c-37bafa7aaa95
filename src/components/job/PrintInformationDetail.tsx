import React from 'react';
import styled from 'styled-components';
import PrintDetail from '@/components/job/PrintDetail';
import PrintSideDetail from '@/components/job/PrintSideDetail';
import { LineTop } from '@/styles/LineTop.styled';
import { TLayDataDetail } from '@/types/prepare-material';

const PrintInformationDetailStyles = styled.div`
  .detail {
    padding: 24px;
    background: #fff;
    h3 {
      margin: 0;
    }
  }
`;
type Props = {
  dataPrintInformationDetail?: any;
  layoutBackUrl?: string;
  layoutFrontUrl?: string;
  layDataDetailFront?: TLayDataDetail;
  layDataDetailBack?: TLayDataDetail;
  dataJob: any;
};
const PrintInformationDetail = ({
  dataPrintInformationDetail,
  layoutBackUrl,
  layoutFrontUrl,
  layDataDetailFront,
  layDataDetailBack,
  dataJob,
}: Props) => {
  return (
    <PrintInformationDetailStyles>
      <LineTop />
      <div className={'detail'}>
        <header>
          <h3>ข้อมูลใบพิมพ์</h3>
        </header>
        <div>
          <PrintDetail
            dataPrintInformationDetail={dataPrintInformationDetail}
            dataJob={dataJob}
          />
          <PrintSideDetail
            layoutBackUrl={layoutBackUrl}
            layoutFrontUrl={layoutFrontUrl}
            layDataDetailFront={layDataDetailFront}
            layDataDetailBack={layDataDetailBack}
          />
        </div>
      </div>
    </PrintInformationDetailStyles>
  );
};

export default PrintInformationDetail;
