import React, { useState } from 'react';
import styled, { css } from 'styled-components';
import { Button } from '@mui/material';
import FormatListBulletedRoundedIcon from '@mui/icons-material/FormatListBulletedRounded';
import DashboardRoundedIcon from '@mui/icons-material/DashboardRounded';
import { JobSwitcherType } from '@/pages/job';

const JobViewSwitcherStyled = styled.div<{
  $switcherValue: JobSwitcherType;
  $init: boolean;
}>`
  height: 40px;
  background: #f5f7f8;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  position: relative;
  border: 1px solid #dbe2e5;
  @keyframes changeToTask {
    0% {
      border-radius: 4px;
    }
    50% {
      border-radius: 4px 50% 50% 4px;
    }
    100% {
      border-radius: 4px;
    }
  }
  @keyframes changeToList {
    0% {
      border-radius: 4px;
    }
    50% {
      border-radius: 50% 4px 4px 50%;
    }
    100% {
      border-radius: 4px;
    }
  }
  .dice {
    min-width: 32px;
    max-width: 32px;
    min-height: 32px;
    max-height: 32px;
    position: absolute;
    border: 1px solid #dbe2e5;
    border-radius: 50% !important;
    transition: 0.3s ease-out !important;
    background: #263238;
    ${({ $switcherValue, $init }) =>
      $switcherValue === 'lists'
        ? css`
            transform: translateX(-50%);
            animation: ${$init ? 'changeToList .6s ease-in ' : 'none'};
          `
        : css`
            transform: translateX(50%);
            animation: ${$init ? 'changeToTask .6s ease-in' : 'none'};
          `}
  }
  .switch-bth {
    border-radius: 50% !important;
    min-width: 32px;
    max-width: 32px;
    min-height: 32px;
    max-height: 32px;
    border: 0 !important;
    z-index: 1;
    span {
      background: transparent !important;
    }
    svg {
      color: #b0bec5;
      font-size: 22px;
    }
    &.active {
      svg {
        color: #fff;
      }
    }
  }
`;
type Props = {
  switcherValue: JobSwitcherType;
  handleClick: (value: JobSwitcherType) => void;
};
const JobViewSwitcher = ({ switcherValue, handleClick }: Props) => {
  const [init, setInit] = useState<boolean>(false);
  return (
    <JobViewSwitcherStyled $switcherValue={switcherValue} $init={init}>
      <Button
        type="button"
        variant="outlined"
        color="blueGrey"
        className={`switch-bth ${switcherValue === 'lists' ? 'active' : ''}`}
        onClick={() => {
          setInit(true);
          handleClick('lists');
        }}
      >
        <FormatListBulletedRoundedIcon />
      </Button>
      <div className="dice" />
      <Button
        type="button"
        variant="outlined"
        color="blueGrey"
        className={`switch-bth ${switcherValue === 'tasks' ? 'active' : ''}`}
        onClick={() => {
          setInit(true);
          handleClick('tasks');
        }}
      >
        <DashboardRoundedIcon
          sx={{
            rotate: '-90deg',
          }}
        />
      </Button>
    </JobViewSwitcherStyled>
  );
};

export default JobViewSwitcher;
