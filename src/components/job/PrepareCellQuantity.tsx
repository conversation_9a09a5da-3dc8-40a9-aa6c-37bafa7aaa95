import React from 'react';
import styled from 'styled-components';
import { TextField } from '@mui/material';

type Props = {
  formik: any;
  index: number;
};
const TextFieldStyles = styled.div``;

const PrepareCellQuantity = ({ formik, index }: Props) => {
  return (
    <>
      <TextFieldStyles>
        <TextField
          variant="outlined"
          type="number"
          id={`${index}.quantity`}
          name={`${index}.quantity`}
          placeholder={'กรุณากรอกจำนวน'}
          value={formik.values[index]?.quantity}
          onChange={formik.handleChange}
          onFocus={(e) =>
            e.target.addEventListener(
              'wheel',
              function (e) {
                e.preventDefault();
              },
              { passive: false }
            )
          }
          error={
            formik.touched?.[index]?.quantity &&
            Boolean(formik.errors?.[index]?.quantity)
          }
          // helperText={
          //   formik.touched?.[index]?.quantity &&
          //   formik.errors?.[index]?.quantity
          // }
          style={{
            padding: '1px',
          }}
        />
      </TextFieldStyles>
    </>
  );
};

export default PrepareCellQuantity;
