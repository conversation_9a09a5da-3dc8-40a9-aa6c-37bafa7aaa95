import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import DragIndicatorSharpIcon from '@mui/icons-material/DragIndicatorSharp';
import IOSSwitch from '@/components/job/IOSSwitch';
import apiTime from '@/services/stock/time';
import { isEmpty } from 'lodash';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';

const JobProductionPlanStyles = styled.div`
  background: #fff;
  .detail {
    padding: 24px;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        font-size: 18px;
        font-weight: 600;
      }
    }
    .box-drag {
      margin-top: 1rem;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      overflow: hidden;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      .size-mini {
        display: flex;
        align-items: center;
        justify-content: end !important;
        text-align: right;
        max-width: 140px !important;
        label {
          margin: auto;
        }
      }
      .card-plan {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
        border-bottom: 1px solid #dbe2e5;
        &:last-child {
          border: none;
        }
        > div {
          padding: 0.6rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          column-gap: 6px;
          overflow: hidden;
          flex: 1 1 0%;
          &.name {
            justify-content: unset;
            max-width: 200px !important;
          }
          &.select-job {
            max-width: 200px !important;
          }
          svg {
            color: #cfd8dc;
          }
          > .MuiInputBase-root {
            min-width: 200px;
          }
        }
      }
      .header-column {
        background: #f5f7f8;
        min-height: 34px !important;
        max-height: 34px !important;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 1rem;
        > div {
          padding: 0 1rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          column-gap: 12px;
          overflow: hidden;
          //width: calc(100% - 550px);
          flex: 1 1 0%;
          color: #90a4ae;
          font-size: 12px;
        }
      }
    }
  }
`;
type Props = {
  dataStage?: any[];
  formik: any;
};
const JobProductionPlan = ({ dataStage, formik }: Props) => {
  const [dataSelectMenu, setDataSelectMenu] = useState<any>([]);
  const sortedData = (data: any) => {
    return data.sort((a: any, b: any) => {
      return a.isActive === b.isActive ? 0 : a.isActive ? -1 : 1;
    });
  };
  const handleOnDragEnd = (result: any) => {
    if (!result.destination) return;
    // setDisabledDragDrop(true);
    const items = Array.from(formik.values.stage);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    const newSortItems = items.map((item: any, idx: number) => {
      return {
        ...item,
        sortIndex: idx + 1,
      };
    });
    formik.setFieldValue('stage', newSortItems);
    // setDisabledDragDrop(false);
  };
  const getProductionTime = async () => {
    const res = await apiTime.getAllProductionTime();
    if (res.status === true) {
      setDataSelectMenu(res.data);
    }
  };
  useEffect(() => {
    getProductionTime();
  }, []);
  useEffect(() => {
    if (dataStage) formik.setFieldValue('stage', sortedData(dataStage));
  }, [dataStage]);
  useEffect(() => {
    if (!isEmpty(formik.values?.stage)) {
      setTimeout(() => {
        formik.setFieldValue('stage', sortedData(formik.values?.stage));
      }, 500);
    }
  }, [formik.values?.stage]);
  return (
    <JobProductionPlanStyles>
      <div className={'detail'}>
        <header>
          <div>แผนการผลิต {formik.values.stage?.length} ขั้นตอน</div>
        </header>
        <div className={'box-drag'}>
          <div className={'header-column'}>
            <div>ขั้นตอนการผลิต</div>
            <div>กำหนดเวลาทำงาน</div>
            <div className={'size-mini'}>การใช้งาน</div>
          </div>
          {!isEmpty(formik.values?.stage) && (
            <DragDropContext onDragEnd={handleOnDragEnd}>
              <Droppable droppableId={'properties'}>
                {(provided: any) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {formik.values.stage?.map((item: any, index: number) => {
                      const { isActive, productionPlanStage } = item;
                      return (
                        <Draggable
                          key={item.id}
                          draggableId={item.id.toString()}
                          index={index}
                        >
                          {(provided: any) => (
                            <div
                              className={'card-plan'}
                              key={index}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div className={'name'}>
                                <DragIndicatorSharpIcon />
                                {productionPlanStage?.name}
                              </div>
                              <Select
                                displayEmpty
                                value={
                                  formik?.values.stage[index].productionTime.id
                                }
                                className={'select-job'}
                                name={`stage[${index}].productionTime.id`}
                                onChange={(event: SelectChangeEvent) => {
                                  const dataProductionTime =
                                    dataSelectMenu.find(
                                      (item: any) =>
                                        item.id === event.target.value
                                    );
                                  formik.setFieldValue(
                                    `stage[${index}].productionTime`,
                                    dataProductionTime
                                  );
                                }}
                              >
                                <MenuItem value="" disabled>
                                  <em>{`กรุณาเลือกกำหนดเวลาทำงาน`}</em>
                                </MenuItem>
                                {!isEmpty(dataSelectMenu) &&
                                  dataSelectMenu.map(
                                    (item: any, index: number) => {
                                      return (
                                        <MenuItem
                                          value={item.id || item.machine?.id}
                                          key={index}
                                        >
                                          {item.name}
                                        </MenuItem>
                                      );
                                    }
                                  )}
                              </Select>

                              <div className={'size-mini'}>
                                <IOSSwitch
                                  value={isActive}
                                  onChangeActive={(checked: boolean) => {
                                    formik.setFieldValue(
                                      `stage[${index}].isActive`,
                                      checked
                                    );
                                  }}
                                />
                              </div>
                            </div>
                          )}
                        </Draggable>
                      );
                    })}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </div>
      </div>
    </JobProductionPlanStyles>
  );
};

export default JobProductionPlan;
