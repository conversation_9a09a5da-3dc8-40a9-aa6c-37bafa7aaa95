import React from 'react';
import Timeline from '@mui/lab/Timeline';
import TimelineItem from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';

const ProcessProduction = () => {
  return (
    <div className="box-process-production">
      <header>
        <div className={'title'}>ขั้นตอนการผลิต</div>
        <p>เวลาทำงาน • 8 ชั่วโมง</p>
      </header>
      <div>
        <Timeline>
          <TimelineItem className={'done'}>
            <TimelineSeparator>
              <TimelineDot>
                <CheckRoundedIcon />
              </TimelineDot>
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              ตัดกระดาษ <br /> Lorem ipsum dolor sit amet, consectetur
              adipisicing elit. Accusamus, alias aliquam blanditiis cum dicta,
              dignissimos dolorem eaque, eius eveniet expedita hic in
              perferendis quaerat quam quia quod reprehenderit sapiente vero!
            </TimelineContent>
          </TimelineItem>
          <TimelineItem className={'loading'}>
            <TimelineSeparator>
              <TimelineDot>
                <FiberManualRecordRoundedIcon />
              </TimelineDot>
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              การพิมพ์ <br />
              <ul>
                <li>08:30 : กำลังผลิต...</li>
              </ul>
            </TimelineContent>
          </TimelineItem>
          <TimelineItem className={'waiting'}>
            <TimelineSeparator>
              <TimelineDot variant={'outlined'}>
                <FiberManualRecordRoundedIcon />
              </TimelineDot>
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>เคลือบหลังพิมพ์</TimelineContent>
          </TimelineItem>
          <TimelineItem className={'waiting'}>
            <TimelineSeparator>
              <TimelineDot variant={'outlined'}>
                <FiberManualRecordRoundedIcon />
              </TimelineDot>
            </TimelineSeparator>
            <TimelineContent>เคลือบหลังพิมพ์</TimelineContent>
          </TimelineItem>
        </Timeline>
      </div>
    </div>
  );
};

export default ProcessProduction;
