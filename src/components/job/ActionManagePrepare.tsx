import React from 'react';
import {
  Button,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Image from 'next/image';

const ActionManagePrepare = () => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = <PERSON>olean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <>
      <Button
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        <MoreVertIcon />
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <MenuItem onClick={handleClose}>
          <ListItemIcon>
            <Image
              src={'/icons/add-PR.svg'}
              alt={'icon'}
              width={24}
              height={24}
            />
          </ListItemIcon>
          <ListItemText>สร้างใบขอซื้อ (PR)</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleClose}>
          <ListItemIcon>
            <Image
              src={'/icons/two_pager_store.svg'}
              alt={'icon'}
              width={24}
              height={24}
            />
          </ListItemIcon>
          <ListItemText>ดูรายการเบิก</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleClose} className={'btn-delete-withdraw'}>
          <ListItemIcon>
            <Image
              src={'/icons/delete-withdraw.svg'}
              alt={'icon'}
              width={24}
              height={24}
            />
          </ListItemIcon>
          <ListItemText>ลบ</ListItemText>
        </MenuItem>
      </Menu>
    </>
  );
};

export default ActionManagePrepare;
