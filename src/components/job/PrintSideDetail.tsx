import React from 'react';
import styled from 'styled-components';
import { TDataLayoutFile, TLayDataDetail } from '@/types/prepare-material';
import PrintSideDetailValue from '@/components/job/PrintSideDetailValue';

const PrintSideDetailStyles = styled.div`
  width: 100%;
  display: flex;
  gap: 24px;
  margin-top: 24px;
  @media (max-width: 1060px) {
    flex-direction: column;
  }
  .box-side {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    display: flex;
    flex-direction: column;
    position: relative;
    header {
      padding: 24px 24px 0 24px;
      h3 {
        font-size: 14px;
        font-weight: 600;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .detail-side {
      padding: 24px;
      display: flex;
      flex-direction: column;
      height: 100%;
      .box-not-found-img-layout {
        width: 100%;
        height: 300px;
        border-radius: 16px;
        background: #f5f7f8;
        display: flex;
        align-items: center;
        justify-content: center;
        > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          .image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #dbe2e5;
          }
          p {
            margin: 0;
            color: #b0bec5;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .image {
        img {
          width: 100%;
          border-radius: 8px;
        }
      }
      .detail-url {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        overflow: hidden;
        img {
          height: 300px !important;
          object-fit: cover;
        }
      }
      .box-data-detail {
        margin-top: 24px;
        flex: 1;
        .label-value {
          padding: 16px 0;
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #dbe2e5;
          gap: 16px;
          overflow: hidden;
          &:first-child {
            .title {
              margin-top: 6px;
            }
          }
          .title {
            font-size: 12px;
            color: #546e7a;
            flex-shrink: 0;
            min-width: 80px;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            height: fit-content;
            margin-top: 1px;
          }
          &.finish {
            .col-coating {
              display: flex;
              flex-direction: column;
              gap: 8px;
              flex: 1;
              min-width: 0;
              .value {
                font-weight: 400 !important;
                .label-value-group {
                  display: flex;
                  align-items: flex-start;
                  gap: 8px;
                  overflow: hidden;
                  span {
                    font-weight: 600 !important;
                    flex-shrink: 0;
                  }
                  > div {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    flex: 1;
                  }
                }
              }
            }
          }
          .value {
            font-weight: 600;
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &.not-found {
              color: #cfd8dc;
              font-weight: 400 !important;
            }
            &.has-tags {
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;
              white-space: normal;
            }
            .tag {
              padding: 4px 10px;
              border-radius: 20px;
              border: 1px solid #dbe2e5;
              background: #fff;
              display: inline-flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
              overflow: hidden;
              flex-shrink: 0;
              max-width: 150px;
              span {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
`;

type Props = {
  layoutBackUrl?: string;
  layoutFrontUrl?: string;
  layDataDetailFront?: TLayDataDetail;
  layDataDetailBack?: TLayDataDetail;
  productionOrderId?: number;
  layoutFile?: TDataLayoutFile;
  setLayoutFile?: (data: TDataLayoutFile) => void;
  isSetting?: boolean;
};
const PrintSideDetail = ({
  layoutBackUrl,
  layoutFrontUrl,
  layDataDetailFront,
  layDataDetailBack,
  productionOrderId,
  layoutFile,
  setLayoutFile,
  isSetting,
}: Props) => {
  return (
    <PrintSideDetailStyles>
      <PrintSideDetailValue
        imageUrl={layoutFrontUrl}
        title={'ด้านหน้า'}
        layDataDetail={layDataDetailFront}
        typeLayOut={1}
        productionOrderId={productionOrderId}
        layoutFile={layoutFile}
        setLayoutFile={(data: TDataLayoutFile) =>
          setLayoutFile && setLayoutFile(data)
        }
        isSetting={isSetting}
      />
      <PrintSideDetailValue
        imageUrl={layoutBackUrl}
        title={'ด้านหลัง'}
        layDataDetail={layDataDetailBack}
        typeLayOut={2}
        productionOrderId={productionOrderId}
        layoutFile={layoutFile}
        setLayoutFile={(data: TDataLayoutFile) =>
          setLayoutFile && setLayoutFile(data)
        }
        isSetting={isSetting}
      />
    </PrintSideDetailStyles>
  );
};

export default PrintSideDetail;
