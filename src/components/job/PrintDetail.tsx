import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { numberWithCommas } from '@/utils/number';

const PrintDetailStyles = styled.div`
  width: 100%;
  display: flex;
  gap: 24px;
  margin-top: 24px;
  .selector {
    flex: 1 1 0%;
    overflow: hidden;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    display: flex;
    flex-direction: column;
    position: relative;
    .top-card {
      width: 100%;
      display: table;
      table-layout: fixed;
      .left-side,
      .right-side {
        display: table-cell;
        vertical-align: top;
        padding: 24px;
        width: 50%;
        overflow: hidden;
        .title {
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 8px;
          margin-right: 4px;
          white-space: nowrap;
        }
        .value {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }
        .image {
          width: 40px;
          min-width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 6px;
          justify-content: center;
          overflow: hidden;
          .key {
            margin-bottom: 0.2rem;
            font-weight: 600;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .label {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            .title {
              font-size: unset;
              font-weight: unset;
              margin-bottom: unset;
              margin-right: 4px;
              display: inline;
            }
            .value {
              display: inline;
            }
          }
          * {
            line-height: 1;
          }
          .text-top {
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .text-bottom {
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .left-side {
        border-right: 1px solid #dbe2e5;
      }
      .right-side {
        .title {
          display: block;
          margin-bottom: 8px;
        }
        .value {
          display: block;
        }
      }
    }
    .detail-wrap {
      width: 100%;
      display: flex;
      border-top: 1px solid #dbe2e5;
      .detail {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 24px;
        justify-content: center;
        overflow: hidden;
        border-right: 1px solid #dbe2e5;
        &:last-child {
          border: none;
        }
        .label {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .title {
            margin-right: 4px;
            display: inline;
          }
          .value {
            display: inline;
          }
        }
        .key {
          margin-bottom: 4px;
          font-weight: 600;
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
`;
type Props = {
  dataPrintInformationDetail: any;
  dataJob: any;
};
const PrintDetail = ({ dataPrintInformationDetail, dataJob }: Props) => {
  const totalQuantityAll =
    dataPrintInformationDetail.dataProductionOrderQuantity?.reduce(
      (sum: any, item: any) => sum + (item.printSheetQuantity || 0),
      0
    );
  const totalQuantityAllowanceAll =
    dataPrintInformationDetail.dataProductionOrderQuantity?.reduce(
      (sum: any, item: any) => sum + (item.printSheetAllowance || 0),
      0
    );
  return (
    <PrintDetailStyles>
      <div
        className="selector"
        onClick={() => {
          //
        }}
      >
        <div className="top-card">
          <div className="left-side">
            <div className={'title'}>วัสดุ</div>
            <div className={'value'}>
              <div className="image">
                <Image
                  src={
                    dataPrintInformationDetail.dataRawMaterial?.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-bottom">
                  {dataPrintInformationDetail.dataRawMaterial?.name}
                </div>
              </div>
            </div>
          </div>
          <div className="right-side">
            <div className={'title'}>ไฟล์อาร์ตเวิร์ก</div>
            <div className={'value'}>
              <div className="text-group">
                <div className={'label'}>
                  <div className="title">จำนวนไฟล์ :</div>
                  <div className={'value'}>
                    {!isEmpty(dataPrintInformationDetail?.dataArtwork)
                      ? dataPrintInformationDetail?.dataArtwork?.length
                      : '-'}{' '}
                    ไฟล์
                  </div>
                </div>
                <div className={'label'}>
                  <div className="title">อัปเดตล่าสุด :</div>
                  <div className={'value'}>
                    {!isEmpty(dataPrintInformationDetail?.dataArtwork) &&
                      dayjs(
                        dataPrintInformationDetail?.dataArtwork[
                          dataPrintInformationDetail.dataArtwork.length - 1
                        ].artworkDetailModifiedDate
                      ).format('DD/MM/YYYY HH:mm น.')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="detail-wrap">
          <div className="detail">
            <div className="key">จำนวนผลิต</div>
            <div className={'label'}>
              <div className="title">จำนวนใบพิมพ์ :</div>
              <div className={'value'}>
                {numberWithCommas(totalQuantityAll || 0)} ใบพิมพ์
              </div>
            </div>
            <div className={'label'}>
              <div className="title">เผื่อเสีย :</div>
              <div className={'value'}>
                {numberWithCommas(totalQuantityAllowanceAll || 0)} ใบพิมพ์
              </div>
            </div>
          </div>
          <div className="detail">
            <div className="key">ขนาด</div>
            <div className={'label'}>
              <div className="title">ใบเต็ม :</div>
              <div className={'value'}>
                {dataPrintInformationDetail.dataItemSize?.itemSize
                  ?.itemSizeName || '-'}
              </div>
            </div>
            <div className={'label'}>
              <div className="title">ใบพิมพ์ :</div>
              <div className={'value'}>
                {dataPrintInformationDetail.dataItemSize?.subItemSize
                  ?.itemSizeName || '-'}
              </div>
            </div>
          </div>
          <div className="detail">
            <div className="key">การพิมพ์</div>
            <div className={'label'}>
              <div className="title">ระบบพิมพ์ :</div>
              <div className={'value'}>
                {dataJob?.layDataDetailFront?.coating?.printSystemName ||
                  dataJob?.layDataDetailBack?.coating?.printSystemName ||
                  'ไม่พิมพ์'}
              </div>
            </div>
            <div className={'label'}>
              <div className="title">เครื่องพิมพ์ :</div>
              <div className={'value'}>
                {dataPrintInformationDetail.dataMachine?.name || '-'}
              </div>
            </div>
          </div>
          <div className="detail">
            <div className="key">ตั้งค่าใบพิมพ์</div>
            <div className={'label'}>
              <div className="title">เพลท :</div>
              <div className={'value'}>
                {dataPrintInformationDetail.dataPlateRawMaterial?.name || '-'}
              </div>
            </div>
            <div className={'label'}>
              <div className="title">บล็อคไดคัท :</div>
              <div className={'value'}>
                {dataPrintInformationDetail.dataDieCutRawMaterial?.name || '-'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </PrintDetailStyles>
  );
};

export default PrintDetail;
