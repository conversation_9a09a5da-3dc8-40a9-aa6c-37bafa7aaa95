import React from 'react';
import styled from 'styled-components';

type Props = {
  status: number;
};
const StatusProductStyles = styled.div`
  padding: 0.3rem 0.7rem;
  border-radius: 4px;
  &.status-1 {
    background: #e6f8cf;
    color: #8bc34a;
  }
  &.status-2 {
    background: #fde8ef;
    color: #d32f2f;
  }
`;
const StatusProduct = ({ status }: Props) => {
  return (
    <StatusProductStyles className={`status-${status ? 1 : 2}`}>
      {status ? 'มีสินค้า' : 'สินค้าหมด'}
    </StatusProductStyles>
  );
};

export default StatusProduct;
