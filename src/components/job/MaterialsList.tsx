import React, { useState } from 'react';
import { FormControl, InputAdornment, Input, Button } from '@mui/material';
import SearchRoundedIcon from '@mui/icons-material/SearchRounded';
import styled from 'styled-components';
import { isEmpty } from 'lodash';

type Props = {
  handleClose: () => void;
  data: any[];
  setNewDataAddMaterials: (rawMaterialId: number, brandId: number) => void;
  onSubmit: () => void;
};
const MaterialsListStyles = styled.div`
  .MuiFormControl-root {
    width: 100%;
  }
  .MuiInputBase-root {
    padding-left: 0.5rem;
    box-shadow: 0 0 0 1px #cfd8dc !important;
    &:before {
      content: unset !important;
    }
    .MuiInputAdornment-root {
      svg {
        color: #cfd8dc !important;
      }
    }
    input {
      padding-left: 0.5rem;
      &::placeholder {
        color: #cfd8dc !important;
      }
    }
  }
  .box-data-amount-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .box-data-amount-list {
      .amount-item {
        padding: 0.7rem 0;
        border-bottom: 1px solid #cfd8dc !important;
        margin-bottom: 0.5rem;
      }
      .item-list {
        display: flex;
        flex-direction: column;
        justify-content: start;
        gap: 0.3rem;
        overflow: auto;
        height: 100%;
        min-height: 330px;
        max-height: 330px;
        .value {
          padding: 0.5rem;
          cursor: pointer;
          border-radius: 6px;
          &:hover {
            background: #e8e8e899;
          }
          &.active {
            background: #d2d2d2;
          }
        }
      }
    }
    .group-button-bottom {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding-top: 1rem;
      button {
        width: calc(100% + 0.5rem);
      }
    }
  }
`;

const MaterialsList = ({
  handleClose,
  data,
  setNewDataAddMaterials,
  onSubmit,
}: Props) => {
  const [valueActive, setValueActive] = useState<number>(0);
  return (
    <MaterialsListStyles>
      <FormControl variant="outlined">
        <Input
          placeholder={'ค้นหา'}
          disabled={isEmpty(data)}
          startAdornment={
            <InputAdornment position="start">
              <SearchRoundedIcon />
            </InputAdornment>
          }
        />
      </FormControl>
      {!isEmpty(data) ? (
        <div className={'box-data-amount-container'}>
          <div className={'box-data-amount-list'}>
            <div className={'amount-item'}>{data?.length} รายการ</div>
            <div className={'item-list'}>
              {data.map((item: any, index: number) => {
                // console.log(item);
                return (
                  <div
                    className={`value ${
                      valueActive === item.id ? 'active' : ''
                    }`}
                    key={index}
                    onClick={() => {
                      setValueActive(item.id);
                      setNewDataAddMaterials(item.id, item.brand[0].id);
                    }}
                  >
                    {item.name}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="group-button-bottom">
            <Button variant={'outlined'} onClick={handleClose}>
              ยกเลิก
            </Button>
            <Button
              variant={'contained'}
              onClick={() => {
                onSubmit();
                handleClose();
              }}
              disabled={valueActive === 0}
            >
              บันทึก
            </Button>
          </div>
        </div>
      ) : (
        <p className={'not-found-material'}>ไม่พบข้อมูล</p>
      )}
    </MaterialsListStyles>
  );
};

export default MaterialsList;
