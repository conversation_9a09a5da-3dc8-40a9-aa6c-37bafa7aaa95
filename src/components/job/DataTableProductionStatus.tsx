import React from 'react';
import TableTools from '@/components/global/TableTools';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import { numberWithCommas } from '@/utils/number';
import dayjs from 'dayjs';
import Image from 'next/image';
import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import KeyboardArrowUpRoundedIcon from '@mui/icons-material/KeyboardArrowUpRounded';
import DragHandleRoundedIcon from '@mui/icons-material/DragHandleRounded';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';

type Props = {
  rows: any[];
  totalElements: number;
  filters: any;
  setFilters: (value: number) => void;
};
const DataTableProductionStatus = ({
  rows,
  totalElements,
  filters,
  setFilters,
}: Props) => {
  const router = useRouter();
  const columns: GridColDef[] = [
    {
      field: 'jobNo',
      headerName: 'เลขที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'countLayData',
      headerName: 'จำนวนสินค้า',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.7,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.countLayData);
      },
    },
    {
      field: 'totalPrice',
      headerName: 'มูลค่ารวม (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.7,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.totalPrice);
      },
    },
    {
      field: 'totalQuantity',
      headerName: 'จำนวนใบพิมพ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return `${numberWithCommas(params.row.totalQuantity)} ชิ้น`;
      },
    },
    {
      field: 'totalQuantityAllowance',
      headerName: 'จำนวนเผื่อเสีย',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return `${numberWithCommas(params.row.totalQuantityAllowance)} ชิ้น`;
      },
    },
    {
      field: 'productionOrderStatus',
      headerName: 'สถานะ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        if (params.row.productionOrderStatus) {
          return (
            <div className={`lay-data-status`}>
              {params.row.productionOrderStatus.name}
            </div>
          );
        }
        return <span style={{ color: '#CFD8DC' }}>ไม่มี</span>;
      },
    },
    {
      field: 'createUser',
      headerName: 'ผู้ดูแล',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              src={
                params.row.createUser.imageUrl ||
                '/images/product/empty-product.svg'
              }
              width={35}
              height={35}
              alt=""
              style={{
                borderRadius: '50%',
              }}
            />
            <div>{params.row.createUser.name}</div>
          </div>
        );
      },
    },
    {
      field: 'priorityLevel',
      headerName: 'ความสำคัญ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
      disableColumnMenu: true,
      sortable: false,
      renderCell: () => {
        const priorityLevel = (level: number) => {
          switch (level) {
            case 1:
              return (
                <div className={'flex items-center gap-1'}>
                  <KeyboardArrowUpRoundedIcon
                    style={{ color: 'rgb(255, 86, 48)' }}
                  />
                  ด่วน
                </div>
              );
            case 2:
              return (
                <div className={'flex items-center gap-1'}>
                  <DragHandleRoundedIcon style={{ color: '#FFAB00' }} />
                  ปกติ
                </div>
              );
            default:
              return (
                <div className={'flex items-center gap-1'}>
                  <KeyboardArrowDownRoundedIcon style={{ color: '#0065ff' }} />
                  ไม่ด่วน
                </div>
              );
          }
        };
        return priorityLevel(2);
      },
    },
    {
      field: 'modifiedDate',
      headerName: 'อัปเดตล่าสุด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div>
            {params.row.modifiedDate ? (
              `${dayjs(params.row.modifiedDate).format('DD/MM/YYYY HH:mm')} น.`
            ) : (
              <span style={{ color: '#CFD8DC' }}>ไม่มี</span>
            )}
          </div>
        );
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 140,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <>
            {/* <KebabTable */}
            {/*  item={params.row} */}
            {/*  isCreateProduct={true} */}
            {/*  isDetailOrder={{ */}
            {/*    status: true, */}
            {/*    url: `/job/${params.row.id}`, */}
            {/*  }} */}
            {/* /> */}
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                width: '84px',
                height: '32px',
                borderRadius: '6px',
                fontSize: '12px',
                marginRight: '8px',
              }}
              onClick={() => router.push(`/job/${params.row.id}`)}
            >
              รายละเอียด
            </Button>
          </>
        );
      },
    },
  ];
  return (
    <AppTableStyle $rows={rows}>
      <div className="content-wrap job-list">
        <div>
          <TableTools
            tools={['search']}
            title={`${totalElements} รายการ`}
            makeNewFilter={(_newFilter: any) => {
              // console.log(newFilter);
            }}
          />
          <ScrollBarStyled>
            <HeaderColumnAction text="รายละเอียด" width={142} />
            <DataGrid
              hideFooter={true}
              rows={rows || []}
              columns={columns}
              paginationMode="server"
              // checkboxSelection={true}
              rowCount={totalElements || 0}
              // pageSize={filters.size}
              disableSelectionOnClick={false}
              autoHeight={true}
              sortModel={[]}
              getRowHeight={() => 56}
              headerHeight={48}
              components={{
                NoRowsOverlay: () => <TableNoRowsOverlay />,
                LoadingOverlay: () => <TableLoadingOverlay />,
              }}
            />
          </ScrollBarStyled>
        </div>

        <div className="px-[16px] pb-[10px]">
          <AppPagination
            filters={filters}
            totalElements={totalElements || 0}
            handleChangeFilters={(newValues: any) => {
              setFilters(newValues);
            }}
          />
        </div>
      </div>
    </AppTableStyle>
  );
};

export default DataTableProductionStatus;
