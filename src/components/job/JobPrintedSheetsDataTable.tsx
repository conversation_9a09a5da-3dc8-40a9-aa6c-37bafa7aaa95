import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import ModalCreatePrintedSheets from '@/components/job/modal/ModalCreatePrintedSheets';
import {
  TAddQuantity,
  TProductionOrderQuantity,
} from '@/types/prepare-material';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import PopoverAction from '@/components/PopoverActionn';
import apiJob from '@/services/order/job';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import WatchLaterRoundedIcon from '@mui/icons-material/WatchLaterRounded';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const JobPrintedSheetsDataTableStyles = styled.div`
  background: #fff;
  .detail {
    padding: 24px;
    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        font-size: 18px;
        font-weight: 600;
      }
    }
    .box-data-table {
      margin-top: 1rem;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      overflow: hidden;
      .MuiDataGrid-columnHeaders {
        min-height: 34px !important;
        max-height: 34px !important;
        color: #90a4ae;
      }
      .MuiDataGrid-virtualScroller {
        margin-top: 34px !important;
      }
      .MuiDataGrid-row {
        &:last-child {
          .MuiDataGrid-cell {
            border: none !important;
          }
        }
      }
    }
  }
`;
type Props = {
  productionOrderQuantity?: TProductionOrderQuantity[];
  fetchData: () => void;
};
const JobPrintedSheetsDataTable = ({
  productionOrderQuantity,
  fetchData,
}: Props) => {
  const dispatch = useAppDispatch();
  const [rows, setRows] = useState<TProductionOrderQuantity[]>(
    productionOrderQuantity || []
  );
  const columns = [
    {
      field: 'name',
      headerName: 'รายการ',
      headerAlign: 'left',
      flex: 1,
      align: 'left',
      width: 80,
    },
    {
      field: 'quantity',
      headerName: 'จำนวนผลิต',
      minWidth: 200,
      flex: 1,
      renderCell: (params: any) => {
        return params.row.printSheetQuantity
          ? `${numberWithCommas(Number(params.row.printSheetQuantity))} ใบพิมพ์`
          : '-';
      },
    },
    {
      field: 'quantityAllowance',
      headerName: 'จำนวนเผื่อเสีย',
      minWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        return params.row.printSheetAllowance
          ? `${numberWithCommas(
              Number(params.row.printSheetAllowance)
            )} ใบพิมพ์`
          : '-';
      },
    },
    {
      field: 'totalQuantity',
      headerName: 'รวม',
      flex: 1,
      minWidth: 150,
      renderCell: (params: any) => {
        return params.row.totalPrintSheet
          ? `${numberWithCommas(Number(params.row.totalPrintSheet))} ใบพิมพ์`
          : '-';
      },
    },
    {
      field: 'status',
      headerName: 'สถานะ',
      flex: 0.5,
      minWidth: 130,
      renderCell: (params: any) => {
        const statusName = (status: number) => {
          switch (status) {
            case 0:
              return (
                <div
                  className={'flex items-center justify-center gap-1'}
                  style={{ fontSize: '14px', color: '#F9A925' }}
                >
                  <WatchLaterRoundedIcon style={{ fontSize: '16px' }} />
                  รออนุมัติ
                </div>
              );
            case 1:
              return (
                <div
                  className={'flex items-center justify-center gap-1'}
                  style={{ fontSize: '14px', color: '#8BC34A' }}
                >
                  <CheckCircleRoundedIcon style={{ fontSize: '16px' }} />
                  อนุมัติ
                </div>
              );
            default:
              return (
                <div style={{ fontSize: '14px', color: '#B0BEC5' }}>
                  ไม่ระบุ
                </div>
              );
          }
        };
        return statusName(params.row.status);
      },
    },
    {
      field: 'user',
      headerName: 'ผู้สร้าง',
      flex: 1,
      minWidth: 130,
      renderCell: (params: any) => {
        const { imageUrl, name } = params.row.createUser;
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              className={'rounded-full'}
              src={imageUrl || '/images/product/empty-product.svg'}
              alt={''}
              width={35}
              height={35}
            />
            <div className={'name'}>{name}</div>
          </div>
        );
      },
    },
    {
      field: 'action',
      headerName: 'จัดการ',
      minWidth: 100,
      headerAlign: 'center',
      align: 'right',
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        const dataApprove = {
          productionOrderQuantityId: params.row.id,
          status: 1,
        };
        if (params.row.status === 1) return '';
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => (
                    <Image
                      src={'/icons/ic_approve.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  ),
                  title: 'อนุมัติใบพิมพ์',
                  onAction: () => approveProductionOrderQuantity(dataApprove),
                },
                {
                  IconElement: () => (
                    <Image
                      src={'/icons/ic_not_cancel.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  ),
                  title: 'ลบ',
                  disabled: false,
                  onAction: () => deleteProductionOrderQuantity(params.row.id),
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const totalQuantityAll = rows?.reduce(
    (sum, item) => sum + (item.totalPrintSheet || 0),
    0
  );
  const createAddQuantity = async (data: TAddQuantity) => {
    console.log('createAddQuantity', data);
    const res = await apiJob.createAddQuantity(data);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มจำนวนใบพิมพ์เรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
    }
  };
  const approveProductionOrderQuantity = async (data: any) => {
    const res = await apiJob.approveProductionOrderQuantity(data);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'อนุมัติใบพิมพ์เรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
    }
  };
  const deleteProductionOrderQuantity = async (
    productionOrderQuantityId: number
  ) => {
    const res = await apiJob.deleteProductionOrderQuantity(
      productionOrderQuantityId
    );
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบรายการเรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
    }
  };
  useEffect(() => {
    if (productionOrderQuantity) setRows(productionOrderQuantity);
  }, [productionOrderQuantity]);

  console.log('rows', rows);
  return (
    <JobPrintedSheetsDataTableStyles>
      <div className={'detail'}>
        <header>
          <div>
            จำนวนผลิต + เผื่อเสีย {numberWithCommas(totalQuantityAll)} ใบพิมพ์
          </div>
          <ModalCreatePrintedSheets
            onSubmit={(data: TAddQuantity) => createAddQuantity(data)}
          />
        </header>
        <div className={'box-data-table'}>
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
                <DataGrid
                  hideFooter={true}
                  rows={rows}
                  columns={columns as any}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </div>
    </JobPrintedSheetsDataTableStyles>
  );
};

export default JobPrintedSheetsDataTable;
