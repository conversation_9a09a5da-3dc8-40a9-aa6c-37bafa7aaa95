import React, { useState } from 'react';
import {
  MenuItem,
  Select,
  SelectChangeEvent,
  FormControl,
} from '@mui/material';
import styled from 'styled-components';

type Props = {
  listRawMaterialBrandConfig: any[];
  brandId: string;
  index: number;
};

const SelectStyles = styled.div`
  width: 100%;
`;

const PrepareCellBrand = ({ listRawMaterialBrandConfig, brandId }: Props) => {
  const [value, setValue] = useState(brandId);
  const handleChange = (event: SelectChangeEvent) => {
    setValue(event.target.value as string);
  };

  return (
    <>
      <SelectStyles>
        <FormControl
          fullWidth
          style={{
            padding: '1px',
          }}
        >
          <Select
            value={value}
            onChange={handleChange}
            variant="outlined"
            displayEmpty
          >
            <MenuItem value={''} disabled>
              <div className="text-[#78909C]">เลือก</div>
            </MenuItem>
            {listRawMaterialBrandConfig.map((item: any) => (
              <MenuItem value={item.id} key={item.id}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </SelectStyles>
    </>
  );
};

export default PrepareCellBrand;
