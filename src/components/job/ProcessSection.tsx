import React from 'react';
import { Button } from '@mui/material';
import Image from 'next/image';
import Fade from '@mui/material/Fade';
import WatchLaterRoundedIcon from '@mui/icons-material/WatchLaterRounded';

const ProcessSection = () => {
  const [checked, setChecked] = React.useState(false);
  return (
    <div className={'box-process'}>
      <header>
        <div className={'title'}>ขั้นตอนงาน : ไดคัท</div>
        <Button variant={'contained'}>รายการผลิต</Button>
      </header>
      <div className={'img-process'}>
        <Image
          src={'/images/img-process-mock.svg'}
          alt={''}
          width={600}
          height={400}
        />
      </div>
      <div className={'box-detail-process'}>
        <Button
          fullWidth
          variant={'outlined'}
          onClick={() => setChecked(!checked)}
        >
          รายละเอียดใบพิมพ์
        </Button>
        <div className={`detail ${checked ? 'show' : ''}`}>
          <Fade
            in={checked}
            style={{ transitionDelay: checked ? '200ms' : '0ms' }}
          >
            <div>
              <div className="profile">
                <Image
                  src={'/images/img-profile.svg'}
                  alt={'img-profile'}
                  width={40}
                  height={40}
                />
                <div className={'name'}>
                  <p>นายสมชาย ใจดี</p>
                  <span>ผู้จัดการโรงงาน</span>
                </div>
              </div>
              <ul>
                <li>
                  <div>
                    <Image
                      src={'/icons/calendar_month.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>กำหนดการผลิต</p>
                  </div>
                  <span>10/12/2025 - 17/12/2025</span>
                </li>
                <li>
                  <div>
                    <Image
                      src={'/icons/calendar_month.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>เวลาทำงาน</p>
                  </div>
                  <span>8 ชั่วโมง</span>
                </li>
                <li>
                  <div>
                    <Image
                      src={'/icons/ic-timer.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>เริ่มผลิต</p>
                  </div>
                  <span>10/12/2025, 09:24</span>
                </li>
                <li>
                  <div>
                    <Image
                      src={'/icons/ic-stop-circle.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>สิ้นสุด</p>
                  </div>
                  <span className={'not-found'}>ไม่ระบุ</span>
                </li>
                <li>
                  <div>
                    <Image
                      src={'/icons/ic-timer.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>รวมเวลาทำงาน</p>
                  </div>
                  <span className={'not-found'}>ไม่ระบุ</span>
                </li>
                <li>
                  <div>
                    <Image
                      src={'/icons/ic_directions_walk.svg'}
                      alt={'img-profile'}
                      width={20}
                      height={20}
                    />
                    <p>ความสำคัญ</p>
                  </div>
                  <span>ปกติ</span>
                </li>
              </ul>
              <div className={'status'}>
                <p>สถาณะ</p>
                <div className={'status-text'}>
                  <WatchLaterRoundedIcon />
                  กำลังผลิต
                </div>
              </div>
              <div className={'responsible-person'}>
                <p>ผู้รับผิดชอบ</p>
                <div className="imgae-group">
                  {[...Array(3)].map((_item, i: number) => (
                    <Image
                      key={i}
                      src={'/images/img-profile.svg'}
                      alt={'img-profile'}
                      width={33}
                      height={33}
                    />
                  ))}
                </div>
              </div>
            </div>
          </Fade>
        </div>
      </div>
    </div>
  );
};

export default ProcessSection;
