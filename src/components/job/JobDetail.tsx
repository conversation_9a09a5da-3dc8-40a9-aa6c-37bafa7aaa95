import React from 'react';
import Image from 'next/image';
import JobStepBar from '@/components/job/JobStepBar';
import styled from 'styled-components';
import { ActionGroupStyle } from '@/styles/share.styled';
import ActionButton from '@/components/ActionButton';
import KebabTable from '@/components/KebabTable';

const JobDetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;
const JobDetailPageStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  .job-detail-header {
    width: 100%;
    display: flex;
    gap: 24px;
    margin-top: 24px;
    @media (max-width: 1060px) {
      flex-direction: column;
    }
    .not-specified {
      color: #cfd8dc;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .selector {
      flex: 1 1 0%;
      overflow: hidden;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      position: relative;
      .top-card {
        width: 100%;
        padding: 20px 24px;
        display: flex;
        align-items: center;
        column-gap: 12px;
        .left-side {
          display: flex;
          column-gap: 12px;
          overflow: hidden;
          flex: 1;
          min-width: 0;
          .image {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .text-group {
            display: flex;
            flex-direction: column;
            row-gap: 6px;
            justify-content: center;
            overflow: hidden;
            flex: 1;
            min-width: 0;
            * {
              line-height: 1;
            }
            .text-top {
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .text-bottom {
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              .text-remaining {
                margin-left: 0.5rem;
                color: #90a4ae;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .detail-wrap {
        width: 100%;
        display: flex;
        border-top: 1px solid #dbe2e5;
        .detail {
          display: flex;
          flex-direction: column;
          flex: 1;
          min-width: 0;
          position: relative;
          padding: 16px 24px;
          justify-content: center;
          overflow: hidden;
          row-gap: 4px;
          border-right: 1px solid #dbe2e5;
          &:last-child {
            border-right: none;
          }
          .key {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            overflow: hidden;
            .key-icon {
              flex-shrink: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .key-text {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }
          .value {
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &.not-found {
              color: #cfd8dc;
            }
          }
        }
      }
    }
  }
`;

// const JobDetailKebabWrap = styled.div`
//   width: 40px;
//   height: 40px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #dbe2e5;
//   border-radius: 8px;
// `;
const JobDetail = () => {
  return (
    <JobDetailPageStyled>
      <div className="flex w-full justify-end">
        <ActionGroupStyle>
          <div
            onClick={() => {
              //
            }}
          >
            <ActionButton
              variant="contained"
              color="Hon"
              text="นำส่งตรวจสอบก่อนพิมพ์"
              borderRadius={'8px'}
            />
          </div>
          <div
            onClick={() => {
              //
            }}
          >
            <ActionButton
              variant="outlined"
              color="blueGrey"
              text="ดาวน์โหลดไฟล์"
              borderRadius={'8px'}
            />
          </div>
          <div
            onClick={() => {
              //
            }}
          >
            <ActionButton
              variant="contained"
              color="dark"
              icon={
                <Image
                  src="/icons/icon-deployed-code.svg"
                  alt=""
                  width={20}
                  height={20}
                />
              }
              text="ตั้งค่าการผลิต"
              borderRadius={'8px'}
            />
          </div>
          <JobDetailKebabWrap>
            <KebabTable
              item={''}
              handleRemove={(_item: any) => {
                //
              }}
              handleEdit={(_item: any) => {
                //
              }}
              isEdit={{
                status: true,
                url: '',
              }}
              isRemove={true}
            />
          </JobDetailKebabWrap>
        </ActionGroupStyle>
      </div>
      <div
        style={{
          marginTop: '24px',
        }}
      >
        <JobStepBar />
      </div>
      <div className="job-detail-header">
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="image">
                <Image
                  src={'/images/product/empty-product.svg'}
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-top">ทนงศักดิ์ ต้นนพรัตน์</div>
                <div className="text-bottom">หัวหน้าฝ่ายผลิต • ผู้มอบหมาย</div>
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">เริ่มผลิต</div>
              </div>
              <div className="value">10/12/2025, 09:00 น.</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">เวลาที่ใช้งาน</div>
              </div>
              <div className="value">4 วัน 1 ชั่วโมง 45 นาที</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">กำหนดรับสินค้า</div>
              </div>
              <div className="value">20/12/2025, 09:00 น.</div>
            </div>
          </div>
        </div>
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="image">
                <Image
                  src={'/images/product/empty-product.svg'}
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-top">กำหนดการผลิต</div>
                <div className="text-bottom">17/12/2025, 09:00 น.</div>
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">จำนวนผลิต + เผื่อเสีย</div>
              </div>
              <div className="value">1,700 ใบพิมพ์</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">ผลิตเสีย</div>
              </div>
              <div className="value">ไม่ระบุ</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">ผลิตสำเร็จ</div>
              </div>
              <div className="value">ไม่ระบุ</div>
            </div>
          </div>
        </div>
      </div>
    </JobDetailPageStyled>
  );
};

export default JobDetail;
