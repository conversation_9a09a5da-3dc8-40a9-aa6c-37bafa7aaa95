import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import { AnimatePresence, motion } from 'framer-motion';
import { motionFadeDelayConfig } from '@/utils/motion/motion-config';
import Image from 'next/image';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import { isNull } from 'lodash';
import dayjs from 'dayjs';
import ProductionDataPanel from '@/components/job/ProductionDataPanel';

const JobProductionAccordionStyled = styled.div<{ $isExtend: boolean }>`
  width: 100%;
  min-height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #dbe2e5;
  position: relative;
  flex-direction: column;
  .job-stage-detail {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: start;
    .title {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .box-data-panel {
      padding: 16px;
      padding-right: 0;
    }
  }
  @keyframes pulse {
    0% {
      filter: brightness(1);
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.8);
    }
    50% {
      filter: brightness(1.2);
    }
    100% {
      filter: brightness(1);
      box-shadow: 0 0 0 8px rgba(0, 0, 0, 0);
    }
  }

  @keyframes pulseSuccess {
    0% {
      filter: brightness(1);
      box-shadow: 0 0 0 0px rgba(22, 213, 197, 0.8);
    }
    50% {
      filter: brightness(1.2);
    }
    100% {
      filter: brightness(1);
      box-shadow: 0 0 0 8px rgba(22, 213, 197, 0);
    }
  }

  &:last-child {
    .child {
      .child-line {
        height: calc(50% - 12px);
        transform: translate(-50%, calc(-50%));
      }
      .child-progress-dot {
        //
      }
    }
  }

  .accordion-parent {
    display: flex;
    align-items: center;
    column-gap: 10px;
    position: relative;
    width: 100%;
    padding: 12px 16px;
    height: 64px;
    .tag-status {
      padding: 0.3rem 0.5rem;
      background: #fff5d3;
      border-radius: 30px;
      display: flex;
      align-items: center;
      justify-content: start;
      gap: 0.3rem;
      span {
        font-size: 14px;
        color: #f9a925;
        font-weight: 300 !important;
      }
    }
    .name {
      font-weight: 600;
      color: #cfd8dc;
      &.current {
        color: #000;
      }
    }
    .progress-dot {
      height: 16px;
      width: 16px;
      min-width: 16px;
      border-radius: 50%;
      border: 1px solid #cfd8dc;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &.current {
        border: 1px solid black;
        &:before {
          background: black;
        }
        &:after {
          background: black;
        }
      }
      &.success {
        border: none;
        &:before {
          background: #16d5c5;
          width: 16px;
          height: 16px;
        }
        &:after {
          background: #16d5c5;
        }
      }
      &:before {
        content: '';
        position: absolute;
        height: 8px;
        width: 8px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #cfd8dc;
        border-radius: 50%;
      }
      &:after {
        content: '';
        position: absolute;
        height: 8px;
        width: 8px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
      }
    }
    .name {
      font-weight: 600;
      &.disabled {
        color: #cfd8dc;
      }
      &.success {
        color: #000;
      }
    }

    .arrow-wrap {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      * {
        transition: 0.15s ease-out;
      }
      &:hover {
        background: #dbe2e5;
      }
    }
  }

  .child {
    width: 100%;
    padding: 0 24px;
    background: white;
    display: flex;
    align-items: center;
    > div:nth-child(3) {
      min-height: 88px;
    }
    &.current {
      background: #f5f7f8;
    }
    .child-progress-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: black;
      position: absolute;
      transform: translate(-50%, 1px);
      z-index: 3;
      &.current {
        &:after {
          content: '';
          position: absolute;
          height: 8px;
          width: 8px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          animation: pulse 1.8s linear infinite;
        }
      }
      &.success {
        background: #16d5c5;
        &:after {
          content: '';
          position: absolute;
          height: 8px;
          width: 8px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          animation: pulseSuccess 1.8s linear infinite;
        }
      }
    }
    .child-line {
      position: absolute;
      height: calc(100% - 20px);
      width: 2px;
      background: black;
      border-radius: 24px;
      z-index: 1;
      transform: translate(-50%, 0.5px);
      &.disabled {
        background: #dbe2e5;
      }
      &.success {
        background: #16d5c5;
      }
      &.current {
        &:before {
          content: '';
          position: absolute;
          height: 50%;
          top: 50%;
          width: 2px;
          background: #dbe2e5;
        }
      }
      &.success {
        &:before {
          background: #16d5c5 !important;
        }
      }
    }
    .text-no-data {
      color: #90a4ae;
      padding: 0 16px;
      display: flex;
      align-items: center;
      p {
        color: #000;
        font-weight: 600;
      }
      &.spec {
        color: #263238;
      }
    }
    .action-btn-wrap {
      position: absolute;
      right: 24px;
      display: flex;
      align-items: center;
      column-gap: 12px;
      &.production {
        position: unset !important;
      }
    }
  }
`;

type Props = {
  stageData?: any;
  indexMockStart?: number;
};

const JobProductionAccordion = ({ stageData, indexMockStart }: Props) => {
  const [isExtend, setIsExtend] = useState<boolean>(false);
  const isCurrentChild = true;
  const statusName = (status: string) => {
    switch (status) {
      case 'STOP':
        return 'พักการผลิต';
      case 'IN_PROGRESS':
        return 'กำลังผลิต';
      default:
        return '';
    }
  };
  useEffect(() => {
    if (indexMockStart === 0) setIsExtend(true);
  }, [indexMockStart]);
  return (
    <JobProductionAccordionStyled $isExtend={isExtend}>
      <div
        className={`accordion-parent ${
          stageData.workScheduleStatusEnum === 'NOT_STARTED'
            ? 'disabled cursor-default'
            : 'cursor-pointer'
        }`}
        {...(stageData.workScheduleStatusEnum === 'NOT_STARTED'
          ? {}
          : { onClick: () => setIsExtend(!isExtend) })}
      >
        <>
          <div
            className={`progress-dot  ${
              indexMockStart !== 0 ? '' : 'current'
            } ${
              stageData.workScheduleStatusEnum === 'COMPLETED' ? 'success' : ''
            }`}
          >
            {stageData.workScheduleStatusEnum === 'COMPLETED' && (
              <CheckRoundedIcon
                sx={{
                  fontSize: '12px',
                  zIndex: '1',
                  color: 'white',
                }}
              />
            )}
          </div>
          <div
            className={`name ${indexMockStart !== 0 ? '' : 'current'} ${
              stageData.workScheduleStatusEnum === 'COMPLETED' ? 'success' : ''
            }`}
          >
            {stageData.productionPlanStage.name}
          </div>
          {statusName(stageData.workScheduleStatusEnum) && (
            <div className={'tag-status'}>
              {stageData.workScheduleStatusEnum === 'STOP' ? (
                <Image
                  src="/icons/pause_circle.svg"
                  alt={'icon'}
                  width={20}
                  height={20}
                />
              ) : (
                <Image
                  src="/icons/status-in-progress.svg"
                  alt={'icon'}
                  width={20}
                  height={20}
                />
              )}
              <span>{statusName(stageData.workScheduleStatusEnum)}</span>
            </div>
          )}
        </>
      </div>
      <motion.div
        key={'child'}
        initial={{ height: 0 }}
        animate={{
          height: isExtend ? 'auto' : 0,
          transition: {
            height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
          },
        }}
        className={`child ${isCurrentChild ? 'current' : ''}`}
      >
        <AnimatePresence initial={false} mode="sync">
          <motion.div
            key={'child-panel'}
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: isExtend ? 1 : 0,
              scale: isExtend ? 1 : 0,
              x: '-50%',
              transition: {
                opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
              },
            }}
            exit={{
              opacity: 0,
              scale: 0,
              transition: { opacity: { duration: 0.3 } },
            }}
            className={`child-progress-dot current ${
              stageData.workScheduleStatusEnum === 'COMPLETED' ? 'success' : ''
            }`}
          />
          <div
            className={`child-line current ${
              indexMockStart !== 0 ? 'disabled' : ''
            } ${
              stageData.workScheduleStatusEnum === 'COMPLETED' ? 'success' : ''
            }`}
          />
          <div className={'job-stage-detail'}>
            <div className={'title'}>
              {isExtend && (
                <motion.div
                  {...motionFadeDelayConfig}
                  className="text-no-data"
                  key="text-no-data"
                >
                  <div className={'flex items-center gap-2'}>
                    <p>บันทึกข้อมูล{stageData.productionPlanStage.name}</p>
                    {isNull(stageData.stepResult.startDatetime) ? (
                      <span>{'ยังไม่มีข้อมูลการดำเนินการ'}</span>
                    ) : (
                      <div>
                        <span>
                          {' '}
                          อัปเดตล่าสุด{' '}
                          {dayjs(stageData.stepResult.modifiedDate).format(
                            'DD/MM/YYYY, HH:mm น.'
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
              {isExtend && (
                <motion.div
                  {...motionFadeDelayConfig}
                  className="action-btn-wrap production"
                  key="action-btn-wrap"
                >
                  <ActionButton
                    variant="contained"
                    color="dark"
                    text="รายละเอียด"
                    borderRadius="8px"
                    onClick={async () => {
                      //
                    }}
                  />
                </motion.div>
              )}
            </div>
            {isExtend && (
              <div className={'box-data-panel w-full'}>
                <motion.div
                  {...motionFadeDelayConfig}
                  className="data-panel"
                  key="data-panel"
                >
                  <ProductionDataPanel stageData={stageData} />
                </motion.div>
              </div>
            )}
          </div>
        </AnimatePresence>
      </motion.div>
    </JobProductionAccordionStyled>
  );
};

export default JobProductionAccordion;
