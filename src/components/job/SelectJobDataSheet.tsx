import React, { useEffect } from 'react';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { isEmpty } from 'lodash';
import { FormHelperText } from '@mui/material';

type Props = {
  title?: string;
  id?: number;
  name: string;
  data?: any;
  isDisabled?: boolean;
  formik: any;
  displayField?: string; // Add optional display field prop
};
const SelectJobDataSheet = ({
  title,
  id,
  data,
  isDisabled,
  name,
  formik,
  displayField = 'name', // Default to 'name' field
}: Props) => {
  useEffect(() => {
    if (id) {
      formik?.setFieldValue(name, Number(id));
    } else if (!isEmpty(data)) {
      formik?.setFieldValue(
        name,
        Number(data[0]?.id) || Number(data[0].machine?.id)
      );
    }
  }, [id]);
  return (
    <div className={'field-select'}>
      {title && <p>{title}</p>}
      <Select
        displayEmpty
        value={formik?.values[name]}
        className={'select-job'}
        disabled={isDisabled || false}
        name={name}
        onChange={(event: SelectChangeEvent) => {
          formik.setFieldValue(name, event.target.value as string);
        }}
        error={formik?.touched[name] && Boolean(formik.errors[name])}
      >
        <MenuItem value="" disabled>
          <em>{`กรุณาเลือก${title}`}</em>
        </MenuItem>
        {!isEmpty(data) &&
          data.map((item: any, index: number) => {
            return (
              <MenuItem value={item.id} key={index}>
                {item[displayField]}
              </MenuItem>
            );
          })}
      </Select>
      {formik?.touched[name] && formik.errors[name] && (
        <FormHelperText error>{formik.errors[name]}</FormHelperText>
      )}
    </div>
  );
};

export default SelectJobDataSheet;
