import React from 'react';
import styled from 'styled-components';
import AccessTimeRoundedIcon from '@mui/icons-material/AccessTimeRounded';
import LocalAtmOutlinedIcon from '@mui/icons-material/LocalAtmOutlined';
import Image from 'next/image';
import KebabTable from '@/components/KebabTable';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import Countdown from 'react-countdown';
import dayjs from 'dayjs';
import 'dayjs/locale/th';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import { dateThaiFormat } from '@/utils/date';
import SkeletonJobCardWrapper from '@/components/job/skeleton/SkeletonJobCardWrapper';
import { LoadingFadein } from '@/styles/share.styled';

dayjs.extend(buddhistEra);
dayjs.locale('th');

const JobCardWrapperStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  height: 100%;
  .not-found-data {
    min-height: 85%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #90a4ae;
    animation: ${LoadingFadein} 0.3s ease-in;
  }
  .job-card {
    width: 100%;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    display: flex;
    flex-direction: column;
    padding: 20px;
    row-gap: 16px;
    background: white;
    cursor: pointer;
    animation: ${LoadingFadein} 0.3s ease-in;
    .top-card-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .job-no {
        font-size: 16px;
        font-weight: 600;
      }
      .top-right-card {
        display: flex;
        align-items: center;
        column-gap: 8px;
        .time-remaining {
          min-width: 110px;
          display: flex;
          align-items: center;
          justify-content: center;
          column-gap: 4px;
          border-radius: 24px;
          border: 1px solid #dbe2e5;
          padding: 0 8px 0 1px;
          .icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .time {
            font-size: 12px;
          }
        }
      }
    }
    .center-card-content {
      .status-chip {
        height: 24px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        font-size: 12px;
        background: #f5f7f8;
        margin-top: -12px;
        width: fit-content;
        &.success {
          background: #e6f8cf;
          color: #7cb342;
        }
        &.pending {
          background: #fff5d3;
          color: #f9a925;
        }
      }
    }
    .bottom-card-content {
      display: flex;
      align-items: center;
      position: relative;
      .item {
        display: flex;
        align-items: center;
        column-gap: 6px;
        flex: 1 1 0%;
        position: relative;
        overflow: hidden;
        //border-radius: 4px;
        height: 28px;
        //border: 1px solid #dbe2e5;
        padding: 6px 0;
        justify-content: center;
        .icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .text {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }
    }
  }
`;
type Props = {
  loadingTasks: boolean;
  dataWorkSchedule: any;
};
const JobCardWrapper = ({ dataWorkSchedule, loadingTasks }: Props) => {
  return (
    <JobCardWrapperStyled>
      {isEmpty(dataWorkSchedule) && !loadingTasks ? (
        <div className={'not-found-data'}>ไม่พบข้อมูล</div>
      ) : isEmpty(dataWorkSchedule) && loadingTasks ? (
        <SkeletonJobCardWrapper />
      ) : (
        dataWorkSchedule?.map((item: any, index: number) => {
          const difference = item.scheduleDate - Date.now();
          return (
            <div className="job-card" key={index}>
              <div className="top-card-content">
                <div className="job-no">{item.jobNo}</div>
                <div className="top-right-card">
                  <div className="time-remaining">
                    <span className="icon">
                      <AccessTimeRoundedIcon
                        sx={{
                          fontSize: '20px',
                        }}
                      />
                    </span>
                    <Countdown date={Date.now() + difference} />
                  </div>
                  <KebabTable
                    item={{}}
                    handleRemove={(_item: any) => {
                      //
                    }}
                    isEdit={{
                      status: true,
                      url: '#',
                    }}
                    handleEdit={() => {
                      //
                    }}
                    isRemove={true}
                    isMeatBall={true}
                  />
                </div>
              </div>
              <div className="center-card-content">
                <div className="status-chip success">สำเร็จ</div>
              </div>
              <div className="bottom-card-content">
                <div className="item">
                  <span className="icon">
                    <LocalAtmOutlinedIcon
                      sx={{
                        color: '#B0BEC5',
                      }}
                    />
                  </span>
                  <span className="text">
                    {numberWithCommas(Number(item.totalPrice))} บาท
                  </span>
                </div>
                <div className="item">
                  <span className="icon">
                    <Image
                      src="/icons/icon-package-gray.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                  </span>
                  <span className="text">
                    มี {item.workScheduled.length} สินค้า
                  </span>
                </div>
                <div className="item">
                  <span className="icon">
                    <Image
                      src="/icons/icon-quick-reorder.svg"
                      alt=""
                      width={24}
                      height={24}
                    />
                  </span>
                  <span className="text">
                    {dateThaiFormat(item.scheduleDate, true)}
                  </span>
                </div>
              </div>
            </div>
          );
        })
      )}
    </JobCardWrapperStyled>
  );
};

export default JobCardWrapper;
