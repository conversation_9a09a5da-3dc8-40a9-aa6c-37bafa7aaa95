import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import { TDataLayoutFile } from '@/types/prepare-material';

const UploadImageSideStyles = styled.div`
  position: relative;
  cursor: pointer;
  padding: 1rem;
  border-radius: 8px;
  border: 2px dashed #dbe2e5;
  background: #f5f7f8;
  min-height: 320px;
  max-height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  input {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
  }
  > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    .image {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      background: #dbe2e5;
    }
    > div {
      text-align: center;
      p {
        margin: 0;
        span {
          text-decoration: underline;
        }
      }
      > span {
        font-size: 12px;
        color: #90a4ae;
      }
    }
  }
  .upload-img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 4px;
  }
`;
type Props = {
  imageUrl?: string;
  productionOrderId?: number;
  typeLayOut: number;
  layoutFile?: TDataLayoutFile;
  setLayoutFile?: (data: TDataLayoutFile) => void;
};
const UploadImageSide = ({
  imageUrl,
  productionOrderId,
  typeLayOut,
  layoutFile,
  setLayoutFile,
}: Props) => {
  // const [layoutFile, setLayoutFile] = useState<any>(initialLayoutFile);
  // const [layoutFileBlob, setLayoutFileBlob] = useState<any>(initialFileBlob);
  const [errorImageUpload, setErrorImageUpload] = useState<string>('');
  const [displayImageUrl, setDisplayImageUrl] = useState<string>('');
  // const [dataImageUpload, setDataImageUpload] = useState({
  //   productionOrderId: productionOrderId,
  //   file: null,
  //   typeLayOut: typeLayOut,
  // });
  const validateFile = async (value: any) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedSize = 2 * 1024 * 1024; // 2MB in bytes
    if (value.length > 0) {
      if (!allowedTypes.includes(value[0].type)) {
        return 'โปรดเลือกไฟล์รูปนามสกุล (JPEG, PNG, SVG, or GIF)';
      }
      if (value[0].size > allowedSize) {
        return 'ขนาดไฟล์รูปควรน้อยกว่า 2MB';
      }
    }
    return true;
  };
  const handleImageUpload = async (event: any, typeLayOut: number) => {
    const file = event.target.files[0];
    if (file && productionOrderId) {
      setDisplayImageUrl(URL.createObjectURL(file));
      if (typeLayOut === 1 && setLayoutFile) {
        setLayoutFile({
          ...layoutFile,
          layoutFrontFile: {
            productionOrderId: productionOrderId,
            typeLayOut: typeLayOut,
            file: file,
          },
        });
      } else if (typeLayOut === 2 && setLayoutFile) {
        setLayoutFile({
          ...layoutFile,
          layoutBackFile: {
            productionOrderId: productionOrderId,
            typeLayOut: typeLayOut,
            file: file,
          },
        });
      }
    }
  };
  useEffect(() => {
    if (imageUrl) setDisplayImageUrl(imageUrl);
  }, [imageUrl]);

  return (
    <UploadImageSideStyles>
      <input
        type="file"
        onChange={async (event: any) => {
          const validationResult = await validateFile(event.target.files);
          if (validationResult === true) {
            if (!isEmpty(errorImageUpload)) {
              setErrorImageUpload('');
            }
            await handleImageUpload(event, typeLayOut);
          } else {
            setErrorImageUpload(validationResult);
          }
        }}
      />
      {!displayImageUrl && (
        <div>
          <div className={'image'}>
            <Image
              src={'/icons/ic-photo.svg'}
              alt={'ic photo'}
              width={20}
              height={20}
            />
          </div>
          <div>
            <p>
              ลากและวาง หรือ <span>อัปโหลดรูป</span>
            </p>
            <span>ไฟล์อัปโหลดขนาดไม่เกิน 2 mb.</span>
          </div>
          {errorImageUpload && (
            <div
              className={'text-red-500 text-sm mt-2'}
              style={{ fontSize: '12px' }}
            >
              {errorImageUpload}
            </div>
          )}
        </div>
      )}
      {displayImageUrl && (
        <Image
          className={'upload-img'}
          src={displayImageUrl}
          alt={'ic photo'}
          width={400}
          height={400}
        />
      )}
    </UploadImageSideStyles>
  );
};

export default UploadImageSide;
