import React from 'react';
import Image from 'next/image';
import { Button } from '@mui/material';
import styled from 'styled-components';

type Props = {
  onDelete: () => void;
};
const ActionDeleteWithdrawalListStyles = styled.div`
  button {
    min-width: 35px;
    height: 35px;
    border-radius: 6px;
    border: 1px solid #dbe2e5;
    background: #fff;
  }
`;
const ActionDeleteWithdrawalList = ({ onDelete }: Props) => {
  return (
    <ActionDeleteWithdrawalListStyles>
      <Button onClick={onDelete}>
        <Image
          src={'/icons/ic-delete.svg'}
          alt="ic delete"
          width={23}
          height={23}
        />
      </Button>
    </ActionDeleteWithdrawalListStyles>
  );
};

export default ActionDeleteWithdrawalList;
