import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import JobStepStatusBar from '@/components/job/JobStepStatus';
import dayjs from 'dayjs';
import { numberWithCommas } from '@/utils/number';
import { TGroupLayData } from '@/types/prepare-material';

const JobStepPageStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  .job-detail-header {
    width: 100%;
    display: flex;
    gap: 24px;
    margin-top: 24px;
    @media (max-width: 1060px) {
      flex-direction: column;
    }
    .not-specified {
      color: #cfd8dc;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .selector {
      flex: 1 1 0%;
      overflow: hidden;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      display: flex;
      flex-direction: column;
      position: relative;
      .top-card {
        width: 100%;
        padding: 20px 24px;
        display: flex;
        align-items: center;
        column-gap: 12px;
        .left-side {
          display: flex;
          column-gap: 12px;
          overflow: hidden;
          flex: 1;
          min-width: 0;
          .image {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .text-group {
            display: flex;
            flex-direction: column;
            row-gap: 6px;
            justify-content: center;
            overflow: hidden;
            flex: 1;
            min-width: 0;
            * {
              line-height: 1;
            }
            .text-top {
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .text-bottom {
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              .text-remaining {
                margin-left: 0.5rem;
                color: #90a4ae;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .detail-wrap {
        width: 100%;
        display: flex;
        border-top: 1px solid #dbe2e5;
        .detail {
          display: flex;
          flex-direction: column;
          flex: 1;
          min-width: 0;
          position: relative;
          padding: 16px 24px;
          justify-content: center;
          overflow: hidden;
          row-gap: 4px;
          border-right: 1px solid #dbe2e5;
          &:last-child {
            border-right: none;
          }
          .key {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            overflow: hidden;
            .key-icon {
              flex-shrink: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .key-text {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            }
          }
          .value {
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &.not-found {
              color: #cfd8dc;
            }
          }
        }
      }
    }
  }
`;

// const JobStepKebabWrap = styled.div`
//   width: 40px;
//   height: 40px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #dbe2e5;
//   border-radius: 8px;
// `;
type Props = {
  currentStepId?: number;
  isRemainingProduction?: boolean;
  dataDate?: any;
  groupLayData?: TGroupLayData[];
  dataJob: any;
};
const JobStep = ({
  currentStepId,
  isRemainingProduction,
  dataDate,
  groupLayData,
  dataJob,
}: Props) => {
  const totalQuantityAll = groupLayData?.reduce(
    (sum: any, item: any) => sum + item.quantity,
    0
  );
  const totalQuantityAllowanceAll = groupLayData?.reduce(
    (sum: any, item: any) => sum + item.quantityAllowance,
    0
  );
  return (
    <JobStepPageStyled>
      <JobStepStatusBar currentStepId={currentStepId} />
      <div className="job-detail-header">
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="image">
                <Image
                  src={
                    dataJob?.createUser?.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-top">{dataJob?.createUser?.name}</div>
                <div className="text-bottom">Role • Role</div>
                {/* <div className={'not-specified'}>ไม่ระบุ</div> */}
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">เริ่มผลิต</div>
              </div>
              {/* <div className="value">10/12/2025, 09:00 น.</div> */}
              <div className={'not-specified'}>ไม่ระบุ</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">เวลาที่ใช้งาน</div>
              </div>
              {/* <div className="value">4 วัน 1 ชั่วโมง 45 นาที</div> */}
              <div className={'not-specified'}>ไม่ระบุ</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic_calendar_today.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">กำหนดรับสินค้า</div>
              </div>
              <div className="value">
                {dataDate?.scheduleDate
                  ? `${dayjs(dataDate.scheduleDate).format(
                      'DD/MM/YYYY HH:mm:ss'
                    )} น.`
                  : ''}
              </div>
            </div>
          </div>
        </div>
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="image">
                <Image
                  src={'/images/product/empty-product.svg'}
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-top">กำหนดการผลิต</div>
                <div className="text-bottom">
                  {dataDate?.scheduledStartDate
                    ? `${dayjs(dataDate.scheduledStartDate).format(
                        'DD/MM/YYYY HH:mm:ss'
                      )} น.`
                    : ''}
                  {isRemainingProduction && (
                    <span className={'text-remaining'}>
                      เหลือเวลาผลิตอีก 4 วัน 8 ชั่วโมง
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">จำนวนผลิต + เผื่อเสีย</div>
              </div>
              <div className="value">
                {totalQuantityAll && totalQuantityAllowanceAll
                  ? `${numberWithCommas(
                      Number(totalQuantityAllowanceAll) +
                        Number(totalQuantityAll)
                    )} ใบพิมพ์`
                  : ''}
              </div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">ผลิตเสีย</div>
              </div>
              {/* <div className="value not-found">ไม่ระบุ</div> */}
              <div className={'not-specified'}>ไม่ระบุ</div>
            </div>
            <div className="detail">
              <div className="key">
                <div className="key-icon">
                  <Image
                    src={'/icons/ic-package-panel.svg'}
                    alt={'ic'}
                    width={18}
                    height={18}
                  />
                </div>
                <div className="key-text">ผลิตสำเร็จ</div>
              </div>
              {/* <div className="value not-found">ไม่ระบุ</div> */}
              <div className={'not-specified'}>ไม่ระบุ</div>
            </div>
          </div>
        </div>
      </div>
    </JobStepPageStyled>
  );
};

export default JobStep;
