import React, { useState } from 'react';
import Button from '@mui/material/Button';
import { TransitionProps } from '@mui/material/transitions';
import { Box, Dialog, Skeleton, Slide, Grid } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import Image from 'next/image';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="left" ref={ref} {...props} />;
});
const ButtonDetailProduction = () => {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button onClick={handleClickOpen}>รายละเอียด</Button>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
        className={'dialog-detail-production'}
      >
        <header>
          <h2>ตัดกระดาษ</h2>
          <Button onClick={handleClose}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <div className={'content'}>
          <div>
            <h3>หน้าที่รับผิดชอบ</h3>
            <div className={'card-result'}>
              <div className={'img'}>
                <Skeleton variant="rounded" width={80} height={80} />
              </div>
              <div className={'result-details'}>
                <p>
                  ตัดกระดาษ <span>(กระดาษอาร์ตการ์ดด้าน 300 แกรม)</span>
                </p>
                <Box sx={{ flexGrow: 1 }}>
                  <Grid container spacing={2}>
                    {[1, 2, 3, 4].map((data: number) => {
                      return (
                        <Grid item xs={6} key={data}>
                          <div className={'value'}>
                            <span>ใบเต็ม</span>
                            <span>: 310x430 mm.</span>
                          </div>
                        </Grid>
                      );
                    })}
                  </Grid>
                </Box>
              </div>
            </div>
          </div>
          <div>
            <h3>บันทึกการทำงาน</h3>
            <div className={'card-save-work'}>
              <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <div className={'value'}>
                      <div>
                        <Image
                          src={'/icons/ic-notes-work.svg'}
                          alt={'icon'}
                          width={20}
                          height={20}
                        />
                        <span>ผลิตเสีย</span>
                      </div>
                      <span className={'found'}>ไม่ระบุ</span>
                    </div>
                  </Grid>
                  <Grid item xs={6}>
                    <div className={'value'}>
                      <div>
                        <Image
                          src={'/icons/ic-notes-work.svg'}
                          alt={'icon'}
                          width={20}
                          height={20}
                        />
                        <span>ผลิตเสีย</span>
                      </div>
                      <span className={'found'}>ไม่ระบุ</span>
                    </div>
                  </Grid>
                  {[1, 2, 3, 4].map((data: number) => {
                    return (
                      <Grid item xs={3} key={data}>
                        <div className={'value'}>
                          <div>
                            <Image
                              src={'/icons/ic-notes-work.svg'}
                              alt={'icon'}
                              width={20}
                              height={20}
                            />
                            <span>ผลิตเสีย</span>
                          </div>
                          <span className={'found'}>ไม่ระบุ</span>
                        </div>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </div>
          </div>
          <div>
            <h3>การลงเวลางาน</h3>
            <div className={'box-work-time'}>
              {[1, 2, 3].map((data: number, index: number) => {
                return (
                  <Accordion defaultExpanded={index === 0} key={data}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <header>
                        <div>
                          <Image
                            src={'/images/profile-mockup.svg'}
                            alt={'image'}
                            width={40}
                            height={40}
                          />
                          <div>
                            <p>ธนันท์ศักดิ์ วงศรีสวัสดิ์</p>
                            <span>ฝ่ายผลิต • หัวหน้าฝ่ายผลิต</span>
                          </div>
                        </div>
                        <div className={'time'}>00 : 23 : 59 : 08</div>
                      </header>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div className={'details'}>
                        <div className={'point'}>
                          <div className={'dot'} />
                          <div className={'line'} />
                        </div>
                        <div className={'work-list'}>
                          <div className={'date'}>10 ม.ค. 2568</div>
                          <ul>
                            <li>
                              <span>08:10 : </span>
                              มอบหมายงานจาก ทนงศักดิ์ ต้นนพรัตน์
                            </li>
                            <li className={'text-red'}>
                              <span>13:08 : </span>
                              กำลังบันทึกเวลาทำงาน...
                            </li>
                          </ul>
                        </div>
                      </div>
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default ButtonDetailProduction;
