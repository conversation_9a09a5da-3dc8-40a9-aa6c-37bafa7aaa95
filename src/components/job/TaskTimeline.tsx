import React, { useRef, useEffect, useState } from 'react';
import styled from 'styled-components';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import ArrowLeftIcon from '@mui/icons-material/ArrowLeft';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { jobSelector, setScrollY } from '@/store/features/job';
import { Tooltip } from '@mui/material';
import TaskTimelineCard from '@/components/job/TaskTimelineCard';
import {
  convertDateFormat,
  dateStringDayNow,
  dateThaiFormat,
} from '@/utils/date';
import dayjs from 'dayjs';
import { AnimatePresence, motion } from 'framer-motion';

const TaskTimelineStyled = styled.div`
  width: 100%;
  position: relative;
  background: #f5f7f8;
  .date-slide {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    background: white;
    border-bottom: 1px solid #dbe2e5;
    .arrow {
      border-radius: 50%;
      height: 24px;
      width: 24px;
      cursor: pointer;
      transition: 0.15s ease-in-out;
      &:hover {
        background: #e5e9ec;
      }
    }
    .full-date {
      font-weight: 600;
    }
  }
  .time-navigation {
    height: 48px;
    border-bottom: 1px solid #dbe2e5;
    /* position: relative; */
    width: calc(100vw - 772px);
    display: flex;
    overflow: auto;
    padding: 0 0 0 34px;
    cursor: grab;
    background: white;
    &:active {
      cursor: grabbing;
    }
    &::-webkit-scrollbar {
      display: none;
    }
    .time-block {
      width: 100px;
      min-width: 100px;
      height: 100%;
      display: flex;
      align-items: end;
      position: relative;
      padding-bottom: 8px;
      &:before {
        content: '';
        height: 16px;
        width: 1px;
        background: #dbe2e5;
      }
      .time {
        position: absolute;
        top: 4px;
        left: 0;
        transform: translateX(-50%);
        font-size: 12px;
        color: #90a4ae;
        user-select: none;
      }
      .line-wrap {
        height: 100%;
        width: 100%;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: space-evenly;
        align-items: end;
        &:before {
          content: '';
          height: 8px;
          background: #dbe2e5;
          width: 1px;
        }
        .line {
          height: 12px;
          background: #dbe2e5;
          width: 1px;
        }
        &:after {
          content: '';
          height: 8px;
          background: #dbe2e5;
          width: 1px;
        }
      }
    }
  }
  .current-time {
    height: 20px;
    padding: 0 8px;
    font-size: 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #d32f2f;
    color: white;
    position: absolute;
    width: fit-content;
    top: 62px;
    z-index: 2;
    &:before {
      content: '';
      height: calc(100dvh - (64px + 57px + 56px + 40px + 42px));
      width: 1px;
      background: #d32f2f;
      position: absolute;
      top: 20px;
    }
  }
  .task-wrapper {
    padding: 0 0 0 34px;
    height: calc(100dvh - (64px + 57px + 56px + 40px + 48px));
    overflow: auto;
    width: calc(100vw - 772px);
    position: relative;
    &::-webkit-scrollbar {
      display: none;
    }
    .col-wrap {
      display: flex;
      width: 100%;
      height: 100% !important;
      .col-hour {
        width: 100px;
        min-width: 100px;
        background: #f5f7f8;
        height: 100% !important;
        position: relative;
        &.over-time {
          background: url('/images/job-over-time-pattern.png') #0000001d;
          background-size: 100px auto;
          z-index: 1;
        }
        &:before {
          content: '';
          position: absolute;
          width: 1px;
          height: 100%;
          left: 0;
          background: #dbe2e5;
        }
        &:after {
          content: '';
          position: absolute;
          width: 1px;
          height: 100%;
          right: 0;
          background: #dbe2e5;
        }
      }
    }
    .row-wrap {
      width: 100%;
      position: absolute;
      top: 0;
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      padding-bottom: 24px;
      .row {
        position: relative;
        width: 100%;
        height: 150px;
        min-height: 150px;
        &:first-child {
          margin-top: 24px;
        }
        .task-card {
          border-radius: 12px;
          height: 100%;
          padding: 12px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          overflow: hidden;
          cursor: pointer;
          background: rgba(243, 247, 249, 0.6);
          border: 1px solid #dbe2e5;
          &:before {
            content: '';
            background: #16d5c5;
          }
          &.IN_PROGRESS {
            //background: #16d5c5;
            background: #afe8e2;
          }
          &.COMPLETED {
            background: white;
            box-shadow: inset 0 0 0 1px #dbe2e5;
          }
          &.STOP {
            color: #fff !important;
            background: #263238;
          }
          &.DELAY {
            background: #b40b0b;
            .info-group {
              color: white;
            }
          }
          .info-group {
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1;
            .title {
              font-weight: 600;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .description {
              font-size: 10px;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .bottom-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 5px;
            overflow: hidden;
            column-gap: 16px;
            z-index: 1;
            .runner-time {
              height: 24px;
              border-radius: 40px;
              background: #00000080;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 8px 0 2px;
              column-gap: 1px;
              &.null {
                width: 0;
                height: 0;
                background: transparent;
              }
              .icon {
                //
              }
              .time {
                font-size: 10px;
                color: white;
              }
            }
            .user-profile {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
              min-width: 24px;
            }
          }
          .background-time-progress {
            background: #16d5c5;
            height: 100%;
            position: absolute;
            margin: -12px 0 0 -12px;
          }
        }
      }
    }
  }
`;

const TooltipTaskStyled = styled.div`
  padding: 0 12px;
  display: flex;
  flex-direction: column;
  min-height: 58px;
  justify-content: center;
  .label {
    font-weight: 600;
    font-size: 14px;
  }
  .time-range {
    display: flex;
    column-gap: 6px;
    font-size: 12px;
    color: #90a4ae;
    span {
      color: white;
    }
  }
`;

type Props = {
  // loadingTasks: boolean;
  dataWorkSchedule?: any;
  filtersWork?: any;
  setFiltersWork: (date: any) => void;
};
const TaskTimeline = ({
  // loadingTasks,
  dataWorkSchedule,
  filtersWork,
  setFiltersWork,
}: Props) => {
  const [selectDate, setSelectDate] = useState<any>();
  const [hours] = useState([
    { h: 0, isOvertime: true },
    { h: 1, isOvertime: true },
    { h: 2, isOvertime: true },
    { h: 3, isOvertime: true },
    { h: 4, isOvertime: true },
    { h: 5, isOvertime: true },
    { h: 6, isOvertime: true },
    { h: 7, isOvertime: true },
    { h: 8, isStart: true, isOvertime: false },
    { h: 9, isOvertime: false },
    { h: 10, isOvertime: false },
    { h: 11, isOvertime: false },
    { h: 12, isOvertime: true },
    { h: 13, isOvertime: false },
    { h: 14, isOvertime: false },
    { h: 15, isOvertime: false },
    { h: 16, isOvertime: false },
    { h: 17, isOvertime: false },
    { h: 18, isOvertime: true },
    { h: 19, isOvertime: true },
    { h: 20, isOvertime: true },
    { h: 21, isOvertime: true },
    { h: 22, isOvertime: true },
    { h: 23, isOvertime: true },
  ]);

  // const [tasks] = useState([
  //   [
  //     {
  //       id: 1,
  //       name: 'Project Kickoff Meeting',
  //       description: 'Discuss objectives and timelines',
  //       start: 8.0,
  //       end: 9.5,
  //       startedTime: null,
  //       status: 'done',
  //       user: { imageUrl: null },
  //     },
  //   ],
  // ]);

  const containerRef = useRef<HTMLDivElement>(null);
  const taskWrapperRef = useRef<HTMLDivElement>(null);
  const currentTimeRef = useRef<HTMLDivElement>(null);
  const isDownRef = useRef(false);
  const startXRef = useRef(0);
  const scrollLeftRef = useRef(0);
  const rowWrapRef = useRef<HTMLDivElement>(null);
  const colWrapRef = useRef<HTMLDivElement>(null);
  const [, setTick] = useState(0);
  const { layout } = useAppSelector(jobSelector);
  const dispatch = useAppDispatch();

  const handleScroll = (scrollTop: number) => {
    dispatch(setScrollY(scrollTop));
  };

  useEffect(() => {
    if (taskWrapperRef.current) {
      taskWrapperRef.current.scrollTop = layout.scrollY;
    }
  }, [layout]);

  useEffect(() => {
    if (containerRef.current) {
      const startIndex = hours.findIndex((hour) => hour.isStart);
      if (startIndex !== -1) {
        containerRef.current.scrollLeft = startIndex * 100;
      }
    }
  }, [hours]);

  const updateCurrentTime = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const formattedTime = `${hour <= 9 ? `0${hour}` : hour}:${
      minute <= 9 ? `0${minute}` : minute
    }`;
    if (currentTimeRef.current) {
      currentTimeRef.current.innerText = formattedTime;
      // คำนวณตำแหน่ง left ของ current-time ตามเวลา (โดย 1 ชั่วโมง = 100px)
      // ดึงวินาทีปัจจุบันเพื่ออัปเดตตำแหน่งอย่างต่อเนื่อง
      const second = now.getSeconds();
      // คำนวณตำแหน่ง left โดยรวมวินาที เพื่อให้ background-time-progress ตรงกับ current-time อย่างแม่นยำ
      const leftPosition = (hour + minute / 60 + second / 3600) * 100;
      const scrollLeft = containerRef.current
        ? containerRef.current.scrollLeft
        : 0;
      currentTimeRef.current.style.left = `${leftPosition - scrollLeft + 16}px`;
    }
    if (taskWrapperRef.current && containerRef.current) {
      taskWrapperRef.current.scrollLeft = containerRef.current.scrollLeft;
    }
    setTick((prev) => prev + 1);
  };

  useEffect(() => {
    updateCurrentTime();
    const intervalId = setInterval(updateCurrentTime, 1000);
    return () => clearInterval(intervalId);
  }, []);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (containerRef.current) {
      isDownRef.current = true;
      startXRef.current = e.pageX - containerRef.current.offsetLeft;
      scrollLeftRef.current = containerRef.current.scrollLeft;
    }
  };

  const handleMouseLeave = () => {
    isDownRef.current = false;
  };

  const handleMouseUp = () => {
    isDownRef.current = false;
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDownRef.current || !containerRef.current) return;
    const x = e.pageX - containerRef.current.offsetLeft;
    const walk = x - startXRef.current;
    containerRef.current.scrollLeft = scrollLeftRef.current - walk;
  };

  const handleWheel = (e: React.WheelEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    containerRef.current.scrollLeft += e.deltaY;
  };
  const isCurrentTime =
    dayjs(Date.now()).format('DD/MM/YYYY') ===
    dayjs(filtersWork).format('DD/MM/YYYY');

  useEffect(() => {
    if (!isEmpty(hours) && rowWrapRef.current && colWrapRef.current) {
      const rowWrapRefRectHeight =
        rowWrapRef.current.getBoundingClientRect().height;
      if (rowWrapRefRectHeight !== 0) {
        colWrapRef.current.style.height = `${rowWrapRefRectHeight}px`;
      }
    }
  }, [hours]);
  useEffect(() => {
    if (filtersWork) setSelectDate(convertDateFormat(filtersWork));
  }, [filtersWork]);
  return (
    <TaskTimelineStyled>
      <div className="date-slide">
        <div
          className="arrow yesterday"
          onClick={() => {
            setFiltersWork((prev: any) => {
              return new Date(prev.setDate(prev.getDate() - 1));
            });
          }}
        >
          <ArrowLeftIcon />
        </div>
        <span className="full-date">
          {dateStringDayNow(filtersWork) &&
            `${dateStringDayNow(filtersWork)}, `}{' '}
          {dateThaiFormat(filtersWork, true)}
        </span>
        <div
          className="arrow tomorrow"
          onClick={() => {
            setFiltersWork((prev: any) => {
              return new Date(prev.setDate(prev.getDate() + 1));
            });
          }}
        >
          <ArrowRightIcon />
        </div>
      </div>
      {isCurrentTime && (
        <AnimatePresence initial={false} mode="sync">
          <motion.div
            key={'current-time'}
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: 1,
              scale: 1,
            }}
            transition={{
              duration: 0.7,
              scale: { type: 'spring', visualDuration: 0.4, bounce: 0.5 },
            }}
          >
            <div className="current-time" ref={currentTimeRef}>
              15:00
            </div>
          </motion.div>
        </AnimatePresence>
      )}

      <div
        className="time-navigation"
        ref={containerRef}
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeave}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onWheel={handleWheel}
        onScroll={updateCurrentTime}
      >
        {hours.map((hour, index) => (
          <div className="time-block" key={index} data-hour={hour.h}>
            <div className="line-wrap">
              <div className="line" />
            </div>
            <div className="time">
              {hour.h <= 9 ? `0${hour.h}:00` : `${hour.h}:00`}
            </div>
          </div>
        ))}
      </div>
      <div
        className="task-wrapper"
        ref={taskWrapperRef}
        onScroll={(e: React.UIEvent<HTMLDivElement>) => {
          const { scrollTop } = e.currentTarget;
          handleScroll(scrollTop);
        }}
      >
        <div className="col-wrap" ref={colWrapRef}>
          {hours.map((hour, index) => (
            <div
              className={`col-hour ${hour.isOvertime ? 'over-time' : ''}`}
              key={index}
              data-hour={hour.h}
            />
          ))}
        </div>
        <div className="row-wrap" ref={rowWrapRef}>
          {!isEmpty(dataWorkSchedule) &&
            dataWorkSchedule?.map((rowTasks: any, rowIndex: number) => {
              return (
                <div className="row" key={rowIndex}>
                  {rowTasks.workScheduled?.map((task: any, index: number) => {
                    if (selectDate !== convertDateFormat(task.startPlan))
                      return null;
                    return (
                      <Tooltip
                        key={index}
                        placement="bottom"
                        componentsProps={{
                          tooltip: {
                            sx: {
                              backgroundColor: '#263238',
                              padding: 0,
                              borderRadius: '12px',
                              marginBottom: '8px !important',
                              zIndex: '1 !important',
                            },
                          },
                        }}
                        title={
                          <TooltipTaskStyled>
                            <div className="label">เวลาทำงาน</div>
                            <div className="time-range">
                              {(() => {
                                // สร้างฟังก์ชันสำหรับแปลงเวลาจากเลขทศนิยมให้เป็นรูปแบบ hh:mm
                                const formatTime = (time: any) => {
                                  const hour = Math.floor(time);
                                  const minute = Math.round((time - hour) * 60);
                                  // ตรวจสอบกรณีที่ minute เท่ากับ 60 แล้วเพิ่มชั่วโมง
                                  if (minute === 60) {
                                    return `${
                                      hour + 1 <= 9 ? `0${hour + 1}` : hour + 1
                                    }:00`;
                                  }
                                  return `${hour <= 9 ? `0${hour}` : hour}:${
                                    minute <= 9 ? `0${minute}` : minute
                                  }`;
                                };
                                // คำนวณเวลาเริ่มและเวลาสิ้นสุดในรูปแบบ hh:mm จาก task
                                const startTimeFormatted = formatTime(
                                  convertDateFormat(task.startPlan, true)
                                );
                                const endTimeFormatted = formatTime(
                                  convertDateFormat(task.endPlan, true)
                                );
                                // คำนวณระยะเวลาของงาน (duration)
                                const duration =
                                  convertDateFormat(task.endPlan, true) -
                                  convertDateFormat(task.startPlan, true);
                                const durationH = Math.floor(duration);
                                const durationM = Math.round(
                                  (duration - durationH) * 60
                                );
                                return (
                                  <>
                                    {startTimeFormatted} - {endTimeFormatted}{' '}
                                    <span className="hrs">
                                      ({durationH > 0 ? `${durationH}h ` : ''}
                                      {durationM > 0 ? `${durationM}min` : ''})
                                    </span>
                                  </>
                                );
                              })()}
                            </div>
                          </TooltipTaskStyled>
                        }
                      >
                        <TaskTimelineCard task={task} />
                      </Tooltip>
                    );
                  })}
                </div>
              );
            })}
        </div>
      </div>
    </TaskTimelineStyled>
  );
};

export default TaskTimeline;
