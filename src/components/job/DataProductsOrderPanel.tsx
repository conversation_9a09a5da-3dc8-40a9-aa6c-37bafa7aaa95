import React from 'react';
import styled from 'styled-components';
import { isEmpty } from 'lodash';

const ProductOrderPanelStyles = styled.div`
  //padding: 12px 16px;
  display: flex;
  align-items: center;
  .data-panel {
    padding: 25px 30px;
    background: #f5f7f8;
    flex: 1;
    border-right: 3px solid #fff;
    &:first-child {
      border-radius: 16px 0 0 16px;
    }
    &:last-child {
      border-right: none !important;
      border-radius: 0 16px 16px 0;
    }
    .title,
    .per {
      font-size: 12px;
    }
    .value {
      font-size: 24px;
      font-weight: 600;
    }
  }
`;
type Props = {
  data?: any;
};
const DataProductsOrderPanel = ({ data }: Props) => {
  return (
    <ProductOrderPanelStyles>
      {isEmpty(data)
        ? [...Array(6)].map((_, i) => (
            <div className="data-panel" key={i}>
              <div className="title">จำนวนสินค้า (LD)</div>
              <div className="value">105</div>
              <div className="per">รายการ/เดือน</div>
            </div>
          ))
        : data.map((item: any, i: number) => (
            <div className="data-panel" key={i}>
              <div className="title">{item.label}</div>
              <div className="value">{item.value || '-'}</div>
              <div className="per">{item.per}</div>
            </div>
          ))}
    </ProductOrderPanelStyles>
  );
};

export default DataProductsOrderPanel;
