import React, { useState } from 'react';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import DialogContent from '@mui/material/DialogContent';
import Image from 'next/image';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import styled from 'styled-components';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { TextField } from '@mui/material';

type Props = {
  open: boolean;
  handleClose: () => void;
  isFormStop: boolean;
};
const ModalStopProductionStyles = styled.div`
  max-width: 540px;
  min-width: 540px;
  padding: 2rem;
  .MuiDialogContent-root {
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    .icon {
      width: 70px;
      height: 70px;
      margin: auto;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7f8;
    }
    h3 {
      margin: 0;
      font-size: 26px;
      color: #000;
      text-align: center;
    }
    p {
      margin: 0;
      text-align: center;
      font-size: 14px;
    }
    .box-form-select {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      width: 100%;
      margin: auto;
      .field-label {
        p {
          font-weight: 600;
          text-align: left;
          margin-bottom: 0.3rem;
        }
        .field-custom,
        .select-custom {
          border-radius: 8px;
          border: 1px solid #dbe2e5;
          background: #fff;
        }
        .field-custom {
          .MuiInputBase-root {
            padding: 1rem;
          }
        }
      }
    }
  }
  .MuiDialogActions-root {
    padding: 0;
    justify-content: center;
    button {
      width: 100%;
    }
  }
`;
const ModalStopProduction = ({ open, handleClose, isFormStop }: Props) => {
  const [value, setValue] = useState('');

  const handleChange = (event: SelectChangeEvent) => {
    setValue(event.target.value as string);
  };
  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <ModalStopProductionStyles>
          <IconButton
            onClick={handleClose}
            sx={(theme) => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            })}
          >
            <CloseIcon />
          </IconButton>
          <DialogContent>
            <div className={'icon'}>
              <Image
                src="/icons/ic-stop-job.svg"
                alt="confirm"
                width={40}
                height={40}
              />
            </div>
            <div>
              {isFormStop ? (
                <h3>ยืนยันพักงานผลิต</h3>
              ) : (
                <h3>ยืนยันยกเลิกการผลิต</h3>
              )}
              <p>
                คุณต้องการยืนยัน{isFormStop ? 'พักงาน' : 'ยกเลิก'}ผลิตในสถานะ
                “การขึ้นรูป” โดยมีเหตุจำเป็น <br />
                ที่จะต้อง{isFormStop ? 'พักงาน' : 'ยกเลิก'}
                ผลิตเนื่องจากสาเหตุดังนี้
              </p>
            </div>
            <div className={'box-form-select'}>
              <div className={'field-label'}>
                <p>สาเหตุการ{isFormStop ? 'พักงาน' : 'ยกเลิก'}ผลิต</p>
                <Select
                  value={value}
                  onChange={handleChange}
                  className={'select-custom w-full'}
                >
                  <MenuItem value={10}>Ten</MenuItem>
                  <MenuItem value={20}>Twenty</MenuItem>
                  <MenuItem value={30}>Thirty</MenuItem>
                </Select>
              </div>
              <div className={'field-label'}>
                <p>หมายเหตุ</p>
                <TextField
                  placeholder={'อธิบาย'}
                  className={'field-custom'}
                  variant="outlined"
                  size="small"
                  fullWidth
                  multiline
                  minRows={6}
                />
              </div>
            </div>
          </DialogContent>
          <DialogActions>
            <Button variant={'outlined'} onClick={handleClose}>
              ยกเลิก
            </Button>
            <Button variant={'contained'} onClick={handleClose} autoFocus>
              ยืนยัน
            </Button>
          </DialogActions>
        </ModalStopProductionStyles>
      </Dialog>
    </>
  );
};

export default ModalStopProduction;
