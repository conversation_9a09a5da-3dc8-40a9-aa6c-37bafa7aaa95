import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogContent,
  FormHelperText,
  Radio,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import Image from 'next/image';
import { Check } from '@mui/icons-material';
import { RadioStyle } from '@/components/raw-material/RawMaterialForm';
import { useRouter } from 'next/router';
import apiLayData from '@/services/order/layData';
import apiJob from '@/services/order/job';
import { isEmpty } from 'lodash';
import { TAddLayData } from '@/types/prepare-material';
import { CreateJobModalStyled } from '@/styles/styledComponents/CreateJobModal.styled';

const validationSchema = yup.object({
  LdId: yup.number().required('กรุณาเลือก'),
});

type Props = {
  handleClose: () => void;
  open: boolean;
  onCreateAddLayData?: (data: TAddLayData) => void;
  fetchData?: () => void;
  selectedLayDataId?: number | null;
  layDataOrderId?: number | null;
};

const ModalCreateJob = ({
  handleClose,
  open,
  onCreateAddLayData,
  fetchData,
  selectedLayDataId,
  layDataOrderId,
}: Props) => {
  const [filtersLayData, setFiltersLayData] = useState<any>({
    layDataStatusId: 3,
    isJob: false,
  });
  const [totalElement, setTotalElement] = useState<number>(0);
  const [layDataList, setLayDataList] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const router = useRouter();
  const {
    // register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      LdId: null,
    },
  } as any);

  const createProductionOrder = async (data: { layDataId: number }) => {
    const res = await apiJob.createProductionOrder(data);
    if (!res.isError) {
      if (fetchData) fetchData();
      handleClose();
    }
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (!onCreateAddLayData) {
      const data = {
        layDataId: values.LdId,
      };
      createProductionOrder(data);
    } else {
      const data = {
        productionOrderId: Number(router.query.id),
        layDataId: values.LdId,
      };
      onCreateAddLayData(data);
    }
    setSubmitting(false);
  };

  useEffect(() => {
    setFiltersLayData({
      ...filtersLayData,
      layDataOrderId: layDataOrderId || null,
    });
  }, [layDataOrderId]);

  useEffect(() => {
    if (open) {
      reset();
      setLayDataList([]);
    }
  }, [open]);

  const handleClickSelector = (ldId: number) => {
    setValue('LdId', ldId);
  };
  const getLayDataList = async (params: any) => {
    const res = await apiLayData.getListLayData(params);
    if (res.status === true) {
      setLayDataList(res.data);
      setTotalElement(res.data.length);
    }
  };

  useEffect(() => {
    getLayDataList(filtersLayData).then();
  }, [filtersLayData, open]);

  useEffect(() => {
    if (selectedLayDataId !== null) {
      setValue('LdId', selectedLayDataId);
    }
  }, [selectedLayDataId]);

  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`สร้างใบสั่งผลิตสินค้า`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <CreateJobModalStyled>
                {!isEmpty(layDataList) && (
                  <div className="count">{totalElement} รายการสินค้ารอผลิต</div>
                )}

                {!isEmpty(layDataList) ? (
                  layDataList.map((item: any) => {
                    return (
                      <div
                        className={`selector`}
                        key={item.id}
                        onClick={() => handleClickSelector(item.id)}
                      >
                        {watch('LdId') === item.id && (
                          <div className="selected" />
                        )}
                        <div className="header-create-job-modal">
                          <div className="left-side">
                            <div className="image">
                              <Image
                                src={
                                  item.productModel?.imageUrl ||
                                  '/images/product/empty-product.svg'
                                }
                                width={64}
                                height={64}
                                alt=""
                              />
                            </div>
                            <div className="text-group">
                              <div className="text-top">{item.ldCode}</div>
                              <div className="text-bottom">
                                {item.customer?.imageUrl && (
                                  <Image
                                    className="rounded-full"
                                    src={item.customer.imageUrl}
                                    width={20}
                                    height={20}
                                    alt=""
                                  />
                                )}
                                <span className="od-code">
                                  {item.layDataOrderNo}
                                </span>
                              </div>
                            </div>
                          </div>

                          <Radio
                            icon={<Check />}
                            checkedIcon={<Check className={'p-1'} />}
                            sx={RadioStyle}
                            checked={watch('LdId') === item.id}
                            value={item.id}
                          />
                        </div>

                        <div className="detail-wrap">
                          <div className="detail">
                            <div className="key">รุ่นสินค้า</div>
                            <div className="value">
                              {item.productModel?.productModelName || '-'}
                            </div>
                          </div>
                          <div className="detail">
                            <div className="key">สถานะ</div>
                            <div className="value">
                              {item.status?.name || '-'}
                            </div>
                          </div>
                          <div className="detail">
                            <div className="key">รหัสรุ่น</div>
                            <div className="value">
                              {item.productModel?.modelCodePacdora || '-'}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className={'text-not-found'}>
                    ไม่พบข้อมูล <br /> สินค้ารอผลิต
                  </div>
                )}
                {hookFormErrors.LdId && (
                  <FormHelperText error>
                    {hookFormErrors.LdId.message as ReactNode}
                  </FormHelperText>
                )}
              </CreateJobModalStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  fullWidth
                  disabled={isEmpty(layDataList) || !watch('LdId')}
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'สร้าง'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalCreateJob;
