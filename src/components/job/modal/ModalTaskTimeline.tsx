import React from 'react';
import Dialog from '@mui/material/Dialog';
import { TransitionProps } from '@mui/material/transitions';
import { Button, Slide } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { ModalTaskTimelineStyles } from '@/styles/ModalTaskTimeline.styled';
import ProcessSection from '@/components/job/ProcessSection';
import AmountProduct from '@/components/job/AmountProduct';
import WorkTimeDetail from '@/components/job/WorkTimeDetail';
import ProcessProduction from '@/components/job/ProcessProduction';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="left" ref={ref} {...props} />;
});
type Props = {
  open: boolean;
  handleClose: () => void;
};

const ModalTaskTimeline = ({ open, handleClose }: Props) => {
  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={handleClose}
      className={'dialog-task-timeline'}
    >
      <ModalTaskTimelineStyles>
        <header>
          <h3>JOB-230902001</h3>
          <Button>
            <CloseRoundedIcon />
          </Button>
        </header>
        <ProcessSection />
        <AmountProduct />
        <WorkTimeDetail />
        <ProcessProduction />
      </ModalTaskTimelineStyles>
    </Dialog>
  );
};

export default ModalTaskTimeline;
