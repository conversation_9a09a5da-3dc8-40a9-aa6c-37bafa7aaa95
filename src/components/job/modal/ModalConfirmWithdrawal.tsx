import React from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import styled from 'styled-components';
import Image from 'next/image';

type Props = {
  open: boolean;
  handleClose: () => void;
  jobNo?: string;
  onSendWithdrawal: () => void;
};
const ModalConfirmWithdrawalStyles = styled.div`
  padding: 2rem;
  > div {
    padding: 0;
  }
  header {
    text-align: center;
    h2 {
      text-align: center;
      font-size: 24px;
      padding: 0;
      margin-top: 1rem;
    }
  }
  .MuiDialogContent-root {
    text-align: center;
    margin: 0.5rem 0;
  }
  .MuiDialogActions-root {
    padding-top: 1.2rem;
    justify-content: center;
    gap: 0.5rem;
    button {
      width: calc(100% - 20px);
    }
  }
`;
const ModalConfirmWithdrawal = ({
  open,
  handleClose,
  jobNo,
  onSendWithdrawal,
}: Props) => {
  return (
    <Dialog open={open} onClose={handleClose}>
      <ModalConfirmWithdrawalStyles>
        <header>
          <Image
            src={'/icons/ic-withdrawal.svg'}
            alt={''}
            width={90}
            height={90}
          />
          <DialogTitle>ยืนยันส่งสร้างใบเบิก</DialogTitle>
        </header>
        <DialogContent>
          คุณได้ตรวจสอบข้อมูลเบิกวัสดุของ “{jobNo}” เรียบร้อยแล้ว <br />
          จะทำการส่งไปรายการเบิกเพื่อสร้างใบเบิกวัสดุในขั้นตอนถัดไป
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} variant={'outlined'}>
            ยกเลิก
          </Button>
          <Button onClick={onSendWithdrawal} variant={'contained'}>
            ส่งคำขอ
          </Button>
        </DialogActions>
      </ModalConfirmWithdrawalStyles>
    </Dialog>
  );
};

export default ModalConfirmWithdrawal;
