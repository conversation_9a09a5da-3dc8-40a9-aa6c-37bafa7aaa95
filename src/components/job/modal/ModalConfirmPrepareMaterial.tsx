import React, { useEffect, useState } from 'react';
import ActionButton from '@/components/ActionButton';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import styled from 'styled-components';
import Image from 'next/image';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useRouter } from 'next/router';

const ConfirmPrepareMaterialStyles = styled.div`
  max-width: 540px;
  min-width: 540px;
  padding: 2rem;
  .MuiDialogContent-root {
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    .icon {
      width: 70px;
      height: 70px;
      margin: auto;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7f8;
    }
    h3 {
      margin: 0;
      font-size: 26px;
      color: #000;
      text-align: center;
    }
    p {
      margin: 0;
      text-align: center;
      font-size: 14px;
    }
  }
  .MuiDialogActions-root {
    padding: 0;
    justify-content: center;
    button {
      min-width: 200px;
    }
  }
`;
type Props = {
  productOrderId: number;
  approveProductionOrder: (id: number) => void;
  currentStepId?: number;
};
const ModalConfirmPrepareMaterial = ({
  productOrderId,
  approveProductionOrder,
  currentStepId,
}: Props) => {
  const router = useRouter();
  const [stepId, setStepId] = useState(0);
  const { step } = router.query;
  const statusCheck = stepId === 1 || stepId === 2;
  const [open, setOpen] = useState(false);
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const textBtnStep = (step?: number) => {
    switch (step) {
      case 1:
        return 'นำส่งตรวจสอบก่อนพิมพ์';
      case 2:
        return 'นำส่งจัดเตรียมวัสดุ';
      default:
        return '';
    }
  };
  useEffect(() => {
    if (currentStepId) setStepId(currentStepId);
  }, [currentStepId]);
  return (
    <>
      {statusCheck && (
        <div onClick={handleClickOpen}>
          <ActionButton
            variant="contained"
            color="Hon"
            text={textBtnStep(stepId)}
            borderRadius={'8px'}
          />
        </div>
      )}

      <Dialog open={open} onClose={handleClose}>
        <ConfirmPrepareMaterialStyles>
          <IconButton
            onClick={handleClose}
            sx={(theme) => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            })}
          >
            <CloseIcon />
          </IconButton>
          <DialogContent>
            <div className={'icon'}>
              <Image
                src="/icons/confirm-material.svg"
                alt="confirm"
                width={40}
                height={40}
              />
            </div>
            {step === 'prePrint' ? (
              <div>
                <h3>ยืนยันนำส่งจัดเตรียมวัสดุ</h3>
                <p>
                  คุณได้ตรวจสอบข้อมูลจากตัวอย่างแบบเลย์เอาท์เรียบร้อยแล้ว <br />
                  จะทำการเข้าสู่การจัดเตรียมวัสดุต่อไป
                </p>
              </div>
            ) : (
              <div>
                <h3>ยืนยันอนุมัติจำนวนการผลิต</h3>
                <p>คุณได้ตรวจสอบข้อมูลจำนวนใบพิมพ์ในการผลิตเรียบร้อยแล้ว</p>
              </div>
            )}
          </DialogContent>
          <DialogActions className="w-full gap-[12px]">
            <Button variant={'outlined'} onClick={handleClose} fullWidth>
              ยกเลิก
            </Button>
            <Button
              variant={'contained'}
              onClick={() => {
                handleClose();
                approveProductionOrder(productOrderId);
              }}
              fullWidth
            >
              ส่งคำขอ
            </Button>
          </DialogActions>
        </ConfirmPrepareMaterialStyles>
      </Dialog>
    </>
  );
};

export default ModalConfirmPrepareMaterial;
