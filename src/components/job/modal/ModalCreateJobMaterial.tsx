import { Button, Dialog } from '@mui/material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import ToggleButtonGroup from '@/components/common/ToggleButtonGroup';
import TabPanelBaseMaterial from '@/components/job/TabPanelBaseMaterial';
import MaterialsList from '@/components/job/MaterialsList';
import apiMaterial from '@/services/stock/material';
import apiRawMaterial from '@/services/stock/raw-material';

type Props = {
  handleClose: () => void;
  open: boolean;
  setNewDataAddMaterials: (rawMaterialId: number, brandId: number) => void;
  onSubmit: () => void;
};
const ModalCreateJobMaterialStyles = styled.div`
  width: 500px;
  max-height: 600px;
  min-height: 600px;
  header {
    padding: 0.5rem 1rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #dbe2e5;
    button {
      min-width: unset;
      svg {
        color: #000;
      }
    }
    > div {
      width: 40px;
    }
    h3 {
      margin: 0;
    }
  }
  .content {
    padding: 2rem;
    height: 100%;
    .tab-material {
      background: #f5f7f8;
      border-radius: 10px;
      padding: 4px;
      > div {
        min-height: unset !important;
        .MuiTabs-flexContainer {
          display: flex;
          align-items: center;
          justify-content: center;
          button {
            min-width: calc(100% / 3) !important;
            padding: 5px 10px !important;
            min-height: 32px !important;
            &[aria-selected='true'] {
              background: #fff;
              border-radius: 8px;
              border: 1px solid #dbe2e5;
            }
          }
        }
        .MuiTabs-indicator {
          display: none !important;
        }
      }
    }
  }
`;
const ModalCreateJobMaterial = ({
  handleClose,
  open,
  setNewDataAddMaterials,
  onSubmit,
}: Props) => {
  const [materialData, setMaterialData] = useState<any>({
    materialId: '',
    subMaterialId: '',
    limit: 100,
    searchName: '',
  });
  const [dataRawMaterialList, setDataRawMaterialList] = useState<any[]>([]);
  const [dataMaterialType, setDataMaterialType] = useState<any>([]);
  const [isChangePage, setIsChangePage] = useState<boolean>(false);
  const [valueTab, setValueTab] = useState<number>(1);
  const handleChange = (newValue: string) => {
    setValueTab(Number(newValue));
    setDataRawMaterialList([]);
  };
  const onSelectMaterialValue = (subMaterialId: number) => {
    if (subMaterialId) {
      getSubMaterialById({ ...materialData, subMaterialId });
      setIsChangePage(true);
    }
  };
  const onClose = () => {
    handleClose();
    setIsChangePage(false);
  };
  const getSubMaterialById = async (filters: any) => {
    const res = await apiRawMaterial.getSubMaterialById(filters);
    if (res.status === true) {
      setDataRawMaterialList(res.data);
    }
  };
  const getListMaterialType = async () => {
    const res = await apiMaterial.getListMaterialType();
    if (res.status === true) {
      setDataMaterialType(res.data);
    }
  };
  const onChangePage = () => {
    setIsChangePage(false);
    setMaterialData({
      ...materialData,
      subMaterialId: '',
    });
    setDataRawMaterialList([]);
  };
  useEffect(() => {
    getListMaterialType();
  }, []);
  return (
    <>
      <Dialog onClose={onClose} open={open}>
        <ModalCreateJobMaterialStyles>
          <header>
            <Button onClick={isChangePage ? () => onChangePage() : onClose}>
              <KeyboardBackspaceRoundedIcon />
            </Button>
            <h3>เพิ่มรายการวัสดุ</h3>
            <div />
          </header>
          <div className={'content'}>
            {isChangePage ? (
              <MaterialsList
                handleClose={onClose}
                data={dataRawMaterialList}
                onSubmit={onSubmit}
                setNewDataAddMaterials={(
                  rawMaterialId: number,
                  brandId: number
                ) => setNewDataAddMaterials(rawMaterialId, brandId)}
              />
            ) : (
              <>
                <ToggleButtonGroup
                  data={dataMaterialType}
                  buttonItem={dataMaterialType.map((item: any) => item.name)}
                  value={valueTab}
                  handleToggle={(value: string) => {
                    handleChange(value);
                  }}
                />
                <TabPanelBaseMaterial
                  valueTab={valueTab}
                  setMaterialData={(materialId: number) =>
                    setMaterialData({
                      ...materialData,
                      materialId: materialId,
                    })
                  }
                  onSelectMaterialValue={(subMaterialId: number) => {
                    onSelectMaterialValue(subMaterialId);
                  }}
                />
              </>
            )}
          </div>
        </ModalCreateJobMaterialStyles>
      </Dialog>
    </>
  );
};

export default ModalCreateJobMaterial;
