import React from 'react';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import styled from 'styled-components';
import HistoryRoundedIcon from '@mui/icons-material/HistoryRounded';
import Image from 'next/image';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="left" ref={ref} {...props} />;
});
const ModalHistoryStyles = styled.div`
  max-height: 100vh;
  min-width: 600px;
  > header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #dbe2e5;
    padding: 0 24px;
    height: 64px;
    > div {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      h3 {
        font-size: 20px;
        margin: 0;
      }
    }
    button {
      min-width: unset;
    }
  }
  .list {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    .card {
      display: flex;
      align-items: start;
      gap: 0.5rem;
      > div {
        .name {
          font-weight: 600;
          font-size: 18px;
        }
        .date {
          color: #90a4ae;
        }
      }
    }
  }
`;
type Props = {
  open: boolean;
  handleClose: () => void;
};
const ModalHistory = ({ open, handleClose }: Props) => {
  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={handleClose}
      className={'dialog-task-timeline'}
    >
      <ModalHistoryStyles>
        <header>
          <div>
            <HistoryRoundedIcon />
            <h3>ประวัติรายการ</h3>
          </div>
          <Button onClick={handleClose}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <div className={'list'}>
          {[...Array(8)].map((_, i) => (
            <div className={'card'} key={i}>
              <Image
                src="/images/profile-mockup.svg"
                alt="confirm"
                width={40}
                height={40}
              />
              <div>
                <div className={'name'}>ธันยารัต สมมณี</div>
                <div className={'description'}>สร้างรายการใบสั่งผลิตสินค้า</div>
                <div className={'date'}>20 ธ.ค. 2567, 10.59 น.</div>
              </div>
            </div>
          ))}
        </div>
      </ModalHistoryStyles>
    </Dialog>
  );
};

export default ModalHistory;
