import React, { useEffect, useState } from 'react';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import { Button, FormHelperText, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import styled from 'styled-components';
import FormModal from '@/components/global/form/FormModal';
import LinkIcon from '@mui/icons-material/Link';
import { TAddArtwork } from '@/types/prepare-material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useRouter } from 'next/router';

const DialogBodyStyles = styled.div`
  max-width: 640px;
  .box-form-modal {
    padding: 30px 0 10px 0;
    form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      .box-action {
        display: flex;
        align-items: center;
        gap: 0.6rem;
      }
      .label {
        p {
          font-weight: 600;
          font-size: 14px;
          margin: 0;
          padding-bottom: 0.2rem;
        }
        .Mui-error {
          font-size: 12px;
        }
        .field {
          position: relative;
          span {
            padding-left: 0.3rem;
            position: absolute;
            height: 100%;
            display: flex;
            align-items: center;
            background: #fff;
            top: 0;
            right: 10px;
          }
        }
        .row-field {
          display: flex;
          align-items: center;
          .icon {
            min-width: 40px;
            min-height: 42px;
            border: 1px solid rgba(0, 0, 0, 0.23);
            border-right: none;
            border-radius: 8px 0 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .field {
            max-width: 100%;
            width: 100%;
            > .MuiFormControl-root {
              > .MuiInputBase-root {
                border-radius: 0 8px 8px 0 !important;
              }
            }
          }
        }
      }
    }
  }
`;
const validationSchema = yup.object({
  title: yup.string().required('กรุณาระบุหัวข้อ'),
  link: yup.string().required('กรุณาระบุลิงก์ดาวน์โหลด'),
});
type Props = {
  onSubmit: (data: TAddArtwork) => Promise<void>;
};
const ModalCreateArtWork = ({ onSubmit }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const [open, setOpen] = useState(false);
  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const formik = useFormik({
    initialValues: {
      productionOrderId: Number(id),
      title: '',
      link: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      onSubmit(values);
      handleClose();
    },
  });

  useEffect(() => {
    if (open) {
      formik.resetForm();
    }
  }, [open]);
  return (
    <>
      <Button
        variant={'contained'}
        startIcon={<AddOutlinedIcon />}
        onClick={handleClickOpen}
      >
        เพิ่มรายการ
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogBodyStyles>
          <DialogContent>
            <FormModal
              title={`ไฟล์อาร์ตเวิร์ก`}
              handleClose={() => {
                handleClose();
              }}
              width={492}
            >
              <div className={'box-form-modal'}>
                <form onSubmit={formik.handleSubmit}>
                  <div className={'label'}>
                    <p>หัวข้อ</p>
                    <TextField
                      id={'title'}
                      name={'title'}
                      value={formik.values.title}
                      onChange={formik.handleChange}
                      placeholder={'ระบุชื่อหัวข้อ'}
                      error={!!formik.errors.title}
                    />
                    {formik?.touched.title && formik.errors.title && (
                      <FormHelperText error>
                        {formik.errors.title}
                      </FormHelperText>
                    )}
                  </div>
                  <div className={'label'}>
                    <p>ลิงก์ดาวน์โหลด</p>
                    <div className={'row-field'}>
                      <div className={'icon'}>
                        <LinkIcon />
                      </div>
                      <div className={'field'}>
                        <TextField
                          id={'link'}
                          name={'link'}
                          value={formik.values.link}
                          onChange={formik.handleChange}
                          placeholder={'https://'}
                          error={!!formik.errors.link}
                        />
                      </div>
                    </div>
                    {formik?.touched.link && formik.errors.link && (
                      <FormHelperText error>
                        {formik.errors.link}
                      </FormHelperText>
                    )}
                  </div>
                  <div className={'box-action'}>
                    <Button
                      className={'w-1/2'}
                      variant={'outlined'}
                      onClick={handleClose}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      className={'w-1/2'}
                      variant={'contained'}
                      type="submit"
                    >
                      บันทึก
                    </Button>
                  </div>
                </form>
              </div>
            </FormModal>
          </DialogContent>
        </DialogBodyStyles>
      </Dialog>
    </>
  );
};

export default ModalCreateArtWork;
