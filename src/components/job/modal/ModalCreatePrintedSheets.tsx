import React, { ReactNode, useEffect, useState } from 'react';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import {
  Button,
  FormControl,
  FormHelperText,
  InputAdornment,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import styled from 'styled-components';
import FormModal from '@/components/global/form/FormModal';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { TAddQuantity } from '@/types/prepare-material';
import { NumericFormat } from 'react-number-format';
import { numberWithCommas } from '@/utils/number';
import apiAnnotation from '@/services/stock/annotation';

const DialogBodyStyles = styled.div`
  max-width: 640px;
  .box-form-modal {
    padding: 30px 0 10px 0;
    form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      .box-action {
        display: flex;
        align-items: center;
        gap: 0.6rem;
      }
      .label {
        p {
          font-weight: 600;
          font-size: 14px;
          margin: 0;
          padding-bottom: 0.2rem;
        }
        .Mui-error {
          font-size: 12px;
        }
        .field {
          position: relative;
          span {
            padding-left: 0.3rem;
            position: absolute;
            height: 100%;
            display: flex;
            align-items: center;
            background: #fff;
            top: 0;
            right: 10px;
          }
        }
        .row-field {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          .percent {
            position: relative;
            max-width: 75px;
            width: 100%;
            input {
              padding-left: 0.5rem;
            }
            span {
              padding-left: 0.3rem;
              position: absolute;
              height: 100%;
              display: flex;
              align-items: center;
              background: #fff;
              top: 0;
              right: 10px;
            }
          }
          .field {
            max-width: 100%;
            width: 100%;
          }
        }
      }
    }
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณาระบุชื่อรายการ'),
  printSheetAllowance: yup.string().required('กรุณาระบุจำนวนเผื่อเสีย'),
  annotationId: yup.string().required('กรุณาเลือกหมายเหตุ'),
});
type Props = {
  onSubmit: (data: TAddQuantity) => void;
};
const ModalCreatePrintedSheets = ({ onSubmit }: Props) => {
  const router = useRouter();
  const [messageError, setMessageError] = useState<string>('');
  const [open, setOpen] = useState(false);
  const [annotationList, setAnnotationList] = useState<any>([]);
  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const getAnnotation = async () => {
    const res = await apiAnnotation.annotationList(4);
    if (!res.isError) {
      setAnnotationList(res.data);
    }
  };

  useEffect(() => {
    getAnnotation().then();
  }, []);

  const { id } = router.query;
  const formik = useFormik({
    initialValues: {
      productionOrderId: Number(id),
      name: '',
      printSheetAllowance: '',
      annotationId: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      setMessageError('');
      const dataPayload = {
        ...values,
        printSheetAllowance: Number(values.printSheetAllowance) || 0,
        annotationId: Number(values.annotationId),
      };
      onSubmit(dataPayload);
      handleClose();
    },
  });
  useEffect(() => {
    if (!open) {
      formik.resetForm({
        values: {
          productionOrderId: Number(id),
          name: '',
          printSheetAllowance: '',
          annotationId: '',
        },
      });
      setMessageError('');
    }
  }, [open, id]);
  return (
    <>
      <Button
        variant={'contained'}
        startIcon={<AddOutlinedIcon />}
        onClick={handleClickOpen}
      >
        เพิ่มรายการ
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogBodyStyles>
          <DialogContent>
            <FormModal
              title={`จำนวนเผื่อเสีย`}
              handleClose={() => {
                handleClose();
              }}
              width={492}
            >
              <div className={'box-form-modal'}>
                <form onSubmit={formik.handleSubmit}>
                  <div className={'label'}>
                    <p>ชื่อรายการ</p>
                    <TextField
                      id={'name'}
                      name={'name'}
                      onChange={formik.handleChange}
                      placeholder={'ระบุชื่อรายการ'}
                      error={!!formik.errors.name}
                    />
                    {formik?.touched.name && formik.errors.name && (
                      <FormHelperText error>
                        {formik.errors.name}
                      </FormHelperText>
                    )}
                  </div>
                  <div className={'label'}>
                    <p>จำนวนเผื่อเสีย</p>
                    <div className={'row-field'}>
                      <NumericFormat
                        placeholder={'กรุณาระบุจำนวนใบพิมพ์'}
                        customInput={TextField}
                        thousandSeparator=","
                        decimalSeparator="."
                        decimalScale={0}
                        fixedDecimalScale={false}
                        allowNegative={false}
                        allowLeadingZeros
                        id={'printSheetAllowance'}
                        name={'printSheetAllowance'}
                        value={numberWithCommas(
                          Number(formik.values.printSheetAllowance),
                          2
                        )}
                        onValueChange={(values) => {
                          formik.setFieldValue(
                            'printSheetAllowance',
                            values.floatValue
                          );
                        }}
                        error={!!formik.errors.printSheetAllowance}
                        helperText={
                          formik.errors.printSheetAllowance ||
                          (messageError as ReactNode)
                        }
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              ใบพิมพ์
                            </InputAdornment>
                          ),
                        }}
                      />
                    </div>
                  </div>
                  <div className={'label'}>
                    <p>หมายเหตุ</p>
                    <FormControl
                      fullWidth
                      error={Boolean(
                        formik.touched.annotationId &&
                          formik.errors.annotationId
                      )}
                    >
                      <Select
                        id="annotationId"
                        name="annotationId"
                        displayEmpty
                        value={formik.values.annotationId || ''}
                        onChange={(e) => {
                          formik.setFieldValue('annotationId', e.target.value);
                          formik.setFieldTouched('annotationId', true, false);
                        }}
                        onBlur={formik.handleBlur}
                        MenuProps={{ disablePortal: true }}
                        renderValue={(selected) => {
                          if (!selected)
                            return (
                              <span className="text-[#78909C]">กรุณาเลือก</span>
                            );

                          const selectedItem = annotationList.find(
                            (it: any) => String(it.id) === String(selected)
                          );
                          return selectedItem?.description || selected;
                        }}
                      >
                        <MenuItem disabled value="">
                          <div className="text-[#78909C]">กรุณาเลือก</div>
                        </MenuItem>
                        {Object.entries(
                          annotationList.reduce((acc: any, it: any) => {
                            if (!acc[it.name]) acc[it.name] = [];
                            acc[it.name].push(it);
                            return acc;
                          }, {})
                        ).flatMap(([groupName, items]: any, idx: number) => [
                          <ListSubheader key={`header-${idx}`}>
                            {groupName}
                          </ListSubheader>,
                          ...items.map((it: any) => (
                            <MenuItem key={it.id} value={it.id.toString()}>
                              {it.description}
                            </MenuItem>
                          )),
                        ])}
                      </Select>

                      {formik.touched.annotationId &&
                        formik.errors.annotationId && (
                          <FormHelperText>
                            {formik.errors.annotationId as string}
                          </FormHelperText>
                        )}
                    </FormControl>
                  </div>
                  <div className={'box-action'}>
                    <Button
                      className={'w-1/2'}
                      variant={'outlined'}
                      onClick={handleClose}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      className={'w-1/2'}
                      variant={'contained'}
                      type="submit"
                    >
                      บันทึก
                    </Button>
                  </div>
                </form>
              </div>
            </FormModal>
          </DialogContent>
        </DialogBodyStyles>
      </Dialog>
    </>
  );
};

export default ModalCreatePrintedSheets;
