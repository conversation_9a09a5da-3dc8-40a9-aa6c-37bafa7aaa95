import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import dayjs from 'dayjs';
import { numberWithCommas } from '@/utils/number';

const ProductionDataPanelStyles = styled.div`
  width: 100%;
  padding: 16px;
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  background: #fff;
  display: flex;
  align-items: center;
  .text-not-found {
    color: #cfd8dc;
    font-weight: 600;
  }
  .column-data {
    width: calc(100% / 7);
    display: flex;
    flex-direction: column;
    gap: 5px;
    .label {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .value {
      font-weight: 600;
    }
  }
`;
type Props = {
  stageData: any;
};
const ProductionDataPanel = ({ stageData }: Props) => {
  return (
    <ProductionDataPanelStyles>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-schedule.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>เวลาการทำงาน</span>
        </div>
        <div className={'value'}>
          {stageData?.productionTime.nameTime || (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-play-circle.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>เริ่มงาน</span>
        </div>
        <div className={'value'}>
          {stageData?.stepResult.startDatetime ? (
            dayjs(stageData?.stepResult.startDatetime).format(
              'DD/MM/YYYY HH:mm น.'
            )
          ) : (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-stop-circle.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>สิ้นสุด</span>
        </div>
        <div className={'value'}>
          {stageData?.stepResult.endDatetime ? (
            dayjs(stageData?.stepResult.endDatetime).format(
              'DD/MM/YYYY HH:mm น.'
            )
          ) : (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-timer.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>รวมเวลาปฏิบัติงาน</span>
        </div>
        <div className={'value'}>
          <div className={'text-not-found'}>ไม่ระบุ</div>
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-library-books.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>ใบเต็มที่ได้รับ</span>
        </div>
        <div className={'value'}>
          {stageData?.stepResult.quantityReceived ? (
            numberWithCommas(stageData?.stepResult.quantityReceived)
          ) : (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-notes-work.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>ผลิตเสีย</span>
        </div>
        <div className={'value'}>
          {stageData?.stepResult.quantityRejected ? (
            numberWithCommas(stageData?.stepResult.quantityRejected)
          ) : (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
      <div className={'column-data'}>
        <div className={'label'}>
          <Image
            src="/icons/ic-description.svg"
            alt={'icon'}
            width={20}
            height={20}
          />
          <span>ผลิตสำเร็จ</span>
        </div>
        <div className={'value'}>
          {stageData?.stepResult.quantityAccepted ? (
            numberWithCommas(stageData?.stepResult.quantityAccepted)
          ) : (
            <div className={'text-not-found'}>ไม่ระบุ</div>
          )}
        </div>
      </div>
    </ProductionDataPanelStyles>
  );
};

export default ProductionDataPanel;
