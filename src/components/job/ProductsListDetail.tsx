import React from 'react';
import styled from 'styled-components';
import { LineTop } from '@/styles/LineTop.styled';

type Props = {
  children: React.ReactNode;
};
const ProductsListDetailStyles = styled.div`
  .line {
    width: 100%;
    height: 8px;
    border-top: 1px solid #dbe2e5;
    background: #f5f7f8;
  }
  .detail {
    padding: 24px;
    background: #fff;
    h3 {
      margin: 0;
    }
  }
`;
const ProductsListDetail = ({ children }: Props) => {
  return (
    <ProductsListDetailStyles>
      <LineTop />
      <div className={'detail'}>
        <header>
          <h3>รายการสินค้า</h3>
        </header>
        <div>{children}</div>
      </div>
    </ProductsListDetailStyles>
  );
};

export default ProductsListDetail;
