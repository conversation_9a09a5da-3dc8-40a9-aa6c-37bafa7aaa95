import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { TAddLayData, TGroupLayData } from '@/types/prepare-material';
import Image from 'next/image';
import { numberWithCommas } from '@/utils/number';
import PopoverAction from '@/components/PopoverActionn';
import { JobDataTable } from '@/styles/job-data-table.styled';
import TextNotSpecified from '@/components/job/TextNotSpecified';
import ModalCreateJob from '@/components/job/modal/ModalCreateJob';
import apiJob from '@/services/order/job';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

type Props = {
  groupLayData?: TGroupLayData[];
  fetchData: () => void;
  layDataOrderId: number | null;
};
const JobMadeToOrderDataTable = ({
  groupLayData,
  fetchData,
  layDataOrderId,
}: Props) => {
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState(false);
  const [rows, setRows] = useState<TGroupLayData[]>(groupLayData || []);
  const columns = [
    {
      field: 'ldCode',
      headerName: 'รายการสั่งซื้อ',
      headerAlign: 'left',
      flex: 2,
      align: 'left',
      width: 80,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center gap-2'}>
            <Image
              className={'rounded-[8px]'}
              src={
                params.row.productModelImageUrl ||
                '/images/product/empty-product.svg'
              }
              alt={''}
              width={35}
              height={35}
            />
            <span
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {params.row.ldCode} • {params.row.productModelName}
            </span>
          </div>
        );
      },
    },
    {
      field: 'quantity',
      headerName: 'จำนวนสั่งซื้อ',
      minWidth: 150,
      flex: 0.5,
      renderCell: (params: any) => {
        if (!params.row.quantity) {
          return <TextNotSpecified />;
        }
        return `${numberWithCommas(Number(params.row.quantity))} ตัว`;
      },
    },
    {
      field: 'quantityAllowance',
      headerName: 'จำนวนเผื่อเสีย',
      minWidth: 150,
      flex: 0.5,
      renderCell: (params: any) => {
        if (!params.row.quantityAllowance) {
          return <TextNotSpecified />;
        }
        return `${numberWithCommas(Number(params.row.quantityAllowance))} ตัว`;
      },
    },
    {
      field: 'quantity+quantityAllowance',
      headerName: 'สั่งซื้อ+เผื่อเสีย',
      flex: 0.5,
      minWidth: 150,
      renderCell: (params: any) => {
        if (!params.row.quantityAllowance && !params.row.quantity) {
          return <TextNotSpecified />;
        }
        return `${numberWithCommas(
          Number(params.row.quantityAllowance) + Number(params.row.quantity)
        )} ตัว`;
      },
    },
    {
      field: 'totalSalePrice',
      headerName: 'มูลค่าสินค้า',
      flex: 1,
      minWidth: 150,
      renderCell: (params: any) => {
        if (!params.row.totalSalePrice) {
          return <TextNotSpecified />;
        }
        return `${numberWithCommas(Number(params.row.totalSalePrice))} บาท`;
      },
    },
    {
      field: 'quantityPerSheet',
      headerName: 'จำนวนผลิต/ใบพิมพ์',
      flex: 0.5,
      minWidth: 130,
      renderCell: (params: any) => {
        if (!params.row.quantityPerSheet) {
          return <TextNotSpecified />;
        }
        return `${numberWithCommas(Number(params.row.quantityPerSheet))} ตัว`;
      },
    },
    {
      field: 'action',
      headerName: 'จัดการ',
      minWidth: 100,
      headerAlign: 'center',
      align: 'right',
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        if (rows.length <= 1 || params.row.isDefault) return '';
        return (
          <>
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => (
                    <Image
                      src={'/icons/ic-bin-delete.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  ),
                  title: 'ลบรายการ',
                  onAction: () =>
                    onDeleteLayDataToProductionOrder(params.row.id),
                  cssProps: {
                    color: '#D32F2F',
                    '&:hover': {
                      backgroundColor: '#FDE8EF',
                    },
                  },
                },
              ]}
            />
          </>
        );
      },
    },
  ];
  const onCreateAddLayData = async (data: TAddLayData) => {
    const res = await apiJob.createAddLayData(data);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มรายการสินค้าสั่งผลิตเรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
      setOpen(false);
    }
  };
  const onDeleteLayDataToProductionOrder = async (id: number) => {
    const res = await apiJob.deleteLayDataToProductionOrder(id);
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบรายการสินค้าสั่งผลิตเรียบร้อย',
          severity: 'success',
        })
      );
      fetchData();
    }
  };

  useEffect(() => {
    if (groupLayData) setRows(groupLayData);
  }, [groupLayData]);

  return (
    <JobDataTable>
      <div className={'detail'}>
        <header>
          <div>สินค้าสั่งผลิต</div>
          <>
            <Button
              variant={'contained'}
              startIcon={<AddOutlinedIcon />}
              onClick={() => setOpen(true)}
            >
              เพิ่มรายการ
            </Button>
            <ModalCreateJob
              open={open}
              handleClose={() => setOpen(false)}
              onCreateAddLayData={(data: TAddLayData) =>
                onCreateAddLayData(data)
              }
              layDataOrderId={layDataOrderId}
            />
          </>
        </header>
        <div className={'box-data-table'}>
          <AppTableStyle $rows={rows}>
            <div className="content-wrap">
              <ScrollBarStyled>
                {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
                <DataGrid
                  hideFooter={true}
                  rows={rows}
                  columns={columns as any}
                  paginationMode="server"
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  // sortModel={[]}
                  // getRowHeight={() => 56}
                  // headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </div>
    </JobDataTable>
  );
};

export default JobMadeToOrderDataTable;
