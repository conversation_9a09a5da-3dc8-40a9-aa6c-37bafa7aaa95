import Button from '@mui/material/Button';
import React from 'react';
import styled from 'styled-components';
import { KeyboardBackspace } from '@mui/icons-material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useRouter } from 'next/router';

type Props = {
  title: string;
  backUrl?: string;
};
const TitlePageStyles = styled.div`
  padding: 14px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #dbe2e5;
  > div {
    &:last-child {
      button {
        color: #dbe2e5;
      }
    }
  }
  button {
    border-radius: 50%;
    min-width: 35px !important;
    width: 35px !important;
    height: 35px !important;
  }
  .title {
    font-weight: 600;
    font-size: 20px;
  }
`;
const TitlePage = ({ title, backUrl }: Props) => {
  const router = useRouter();
  return (
    <TitlePageStyles>
      <div>
        <Button
          onClick={() => (backUrl ? router.push(backUrl) : router.back())}
        >
          <KeyboardBackspace />
        </Button>
      </div>
      <div className={'title'}>{title}</div>
      <div>
        <Button>
          <InfoOutlinedIcon />
        </Button>
      </div>
    </TitlePageStyles>
  );
};

export default TitlePage;
