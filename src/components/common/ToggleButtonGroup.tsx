import React, { useEffect, useState } from 'react';
import styled, { css } from 'styled-components';

const ToggleButtonGroupStyled = styled.div<{
  $direction: 'left' | 'right' | null;
}>`
  width: 100%;
  height: 40px;
  min-height: 40px;
  background: #f5f7f8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  column-gap: 8px;
  padding: 0 4px;
  position: sticky;
  top: 10px;
  z-index: 2;

  @keyframes directionLeft {
    0% {
      transform: scale(1);
      border-radius: 8px;
    }
    50% {
      transform: scale(1.02);
      border-radius: 8px 16px 16px 8px;
      box-shadow: 0px 4px 12px 0px rgba(38, 50, 56, 0.15);
    }
    100% {
      transform: scale(1);
      border-radius: 8px;
    }
  }

  @keyframes directionRight {
    0% {
      transform: scale(1);
      border-radius: 8px;
    }
    50% {
      transform: scale(1.02);
      border-radius: 16px 8px 8px 16px;
      box-shadow: 0px 4px 12px 0px rgba(38, 50, 56, 0.15);
    }
    100% {
      transform: scale(1);
      border-radius: 8px;
    }
  }

  .toggle {
    height: 32px;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(38, 50, 56, 0.12),
      0px 0px 0px 1px rgba(255, 255, 255, 0.8);
    position: absolute;
    z-index: 1;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid #dbe2e5;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    ${({ $direction }) =>
      $direction === 'left'
        ? css`
            animation: directionLeft 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          `
        : $direction === 'right'
        ? css`
            animation: directionRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          `
        : css`
            animation: none;
          `}
  }

  button {
    all: unset;
    height: 32px;
    color: #607d8b;
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-weight: 500;
    font-size: 14px;
    position: relative;
    z-index: 2;

    &:hover {
      color: #263238;
      transform: translateY(-1px);
    }

    &.active {
      color: #263238;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
`;

type Props = {
  data?: any;
  buttonItem: string[];
  value: string | number;
  handleToggle: (value: string) => void;
};

const ToggleButtonGroup = ({
  data,
  buttonItem,
  value,
  handleToggle,
}: Props) => {
  const [toggleDirection, setToggleDirection] = useState<
    'left' | 'right' | null
  >(null);
  const [init, setInit] = useState<boolean>(false);
  const [currentToggleLeft, setCurrentToggleLeft] = useState<number>(0);

  const handleMakeTogglePosition = () => {
    const toggleWrapper = document.getElementById('toggleWrapper');
    if (!toggleWrapper) return;
    const activeElem: HTMLElement | null =
      toggleWrapper.querySelector('.active');
    const toggleElem: HTMLElement | null =
      toggleWrapper.querySelector('.toggle');
    if (activeElem && toggleElem) {
      const wrapperRect = toggleWrapper.getBoundingClientRect();
      const activeRect = activeElem.getBoundingClientRect();
      const offsetLeft = activeRect.left - wrapperRect.left;
      if (init) {
        if (currentToggleLeft > offsetLeft) {
          setToggleDirection('right');
        } else {
          setToggleDirection('left');
        }
      }
      setCurrentToggleLeft(offsetLeft);
      toggleElem.style.width = `${activeRect.width}px`;
      toggleElem.style.left = `${offsetLeft}px`;
    }
  };

  useEffect(() => {
    handleMakeTogglePosition();
  }, [value]);

  const handleClick = (item: string) => {
    setInit(true);
    setToggleDirection(null);
    handleToggle(item);
  };

  return (
    <ToggleButtonGroupStyled id="toggleWrapper" $direction={toggleDirection}>
      {value && <div className="toggle" />}
      {data
        ? data.map((item: any, index: number) => {
            return (
              <button
                key={index}
                className={value === item.id ? 'active' : ''}
                onClick={() => {
                  handleClick(item.id);
                }}
              >
                {item.name}
              </button>
            );
          })
        : buttonItem.map((item: string, index: number) => {
            return (
              <button
                key={index}
                className={value === item ? 'active' : ''}
                onClick={() => {
                  handleClick(item);
                }}
              >
                {item}
              </button>
            );
          })}
    </ToggleButtonGroupStyled>
  );
};

export default ToggleButtonGroup;
