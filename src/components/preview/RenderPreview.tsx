import { Fragment, useEffect, useState } from 'react';
import styled from 'styled-components';

type PacdoraViewerProps = {
  modelId: number;
  collapseScore: number;
};

const RenderPreview = ({ modelId, collapseScore }: PacdoraViewerProps) => {
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadScript = async () => {
      if (!(window as any).Pacdora) {
        const script = document.createElement('script');
        script.src = 'https://cdn.pacdora.com/Pacdora-v1.1.6.js';
        script.async = true;
        document.body.appendChild(script);

        script.onload = () => {
          initializePacdora();
        };

        return () => {
          document.body.removeChild(script);
        };
      }
      await initializePacdora();
    };

    const initializePacdora = async () => {
      if ((window as any).Pacdora) {
        await (window as any).Pacdora.init({
          userId: undefined,
          appId: '16b560465593ccbf',
          isDelay: true,
          theme: '#fff',
          doneBtn: 'Save',
          localeResource: {
            'Upload & Design': 'Online design',
          },
        });
        await (window as any).Pacdora.createScene({
          modelId: modelId,
          doneBtn: 'Save',
          isShowLoading: true,
        });
        setTimeout(() => onSetBackground(), 500);
      }
    };
    loadScript();
    setTimeout(() => setLoading(false), 5000);
  }, [modelId]);

  const onSetBackground = () => {
    if ((window as any).Pacdora) {
      (window as any).Pacdora.set3DBackground('d3', 'transparent');
    }
  };

  useEffect(() => {
    onCollapse();
  }, [collapseScore]);

  const onCollapse = () => {
    if ((window as any).Pacdora) {
      (window as any).Pacdora.collapse('d3', collapseScore);
    }
  };

  // const onZoom = () => {
  //   if ((window as any).Pacdora) {
  //     (window as any).Pacdora.scaleCanva('d3', 0.9);
  //   }
  // };

  return (
    <Fragment>
      <LoadingContainer disable={!loading}>
        <svg viewBox="25 25 50 50">
          <circle r="20" cy="50" cx="50"></circle>
        </svg>
      </LoadingContainer>
      <PacdoraContainer
        loading={loading}
        className="d3"
        data-pacdora-ui="3d"
        data-pacdora-id="d3"
      />
    </Fragment>
  );
};
export default RenderPreview;

const PacdoraContainer = styled.div<{ loading: boolean }>`
  width: 100%;
  height: 100%;
  display: flex;
  opacity: ${(props) => (props.loading ? '0' : '1')};
  z-index: 2;
  position: relative;
`;

const LoadingContainer = styled.div<{ disable: boolean }>`
  display: ${(props) => (props.disable ? 'none' : 'flex')};
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 99;
  justify-content: center;
  align-items: center;

  svg {
    width: 32px;
    transform-origin: center;
    animation: rotate4 2s linear infinite;
  }

  circle {
    fill: none;
    stroke: hsl(214, 3%, 49%);
    stroke-width: 4;
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: dash4 1.5s ease-in-out infinite;
  }

  @keyframes rotate4 {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash4 {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 90, 200;
      stroke-dashoffset: -35px;
    }

    100% {
      stroke-dashoffset: -125px;
    }
  }
`;
