import React, { useState } from 'react';
import styled from 'styled-components';

interface ToggleButtonProps {
  isActive: boolean;
}

interface ControlPanelProps {
  initialViewMode?: '2D' | '3D';
  // ถ้าไม่ต้องการจำกัดค่า สามารถปรับปรุงได้ตามต้องการ
  minRange?: number;
  maxRange?: number;
  initialValue?: number; // ค่ากลางเริ่มต้น
  onViewModeChange?: (mode: '2D' | '3D') => void;
  onValueChange?: (value: number) => void;
}

// แม่แบบคร่าวๆ สำหรับแถบคอนเทนเนอร์
const ControlPanelContainer = styled.div`
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50px;
  padding: 8px 12px;
  gap: 12px;
  width: fit-content;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ViewToggle = styled.div`
  position: relative;
  width: 50px;
  height: 26px;
  background-color: #dedede;
  border-radius: 50px;
  display: flex;
  align-items: center;
  overflow: hidden;
`;

const ToggleButton = styled.button<ToggleButtonProps>`
  position: ${(props) => (props.isActive ? 'relative' : 'absolute')};
  z-index: ${(props) => (props.isActive ? 2 : 1)};
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background-color: ${(props) => (props.isActive ? '#000' : 'transparent')};
  border: none;
  color: ${(props) => (props.isActive ? 'white' : '#999')};
  font-weight: bold;
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  left: ${(props) =>
    props.isActive && props.children === '3D'
      ? '24px'
      : props.isActive && props.children === '2D'
      ? '0'
      : props.children === '3D'
      ? '24px'
      : '0'};
`;

// RangeSlider ไม่เติมแถบสีตามเปอร์เซ็นต์ แต่ตั้งค่า thumb ให้อยู่กลางค่า min–max
const RangeSlider = styled.input`
  -webkit-appearance: none;
  appearance: none;
  flex: 1;
  height: 4px;
  //background: #000000;
  border-radius: 2px;
  outline: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background-color: white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background-color: white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: none;
  }

  &::-webkit-slider-runnable-track,
  &::-moz-range-track {
    background: #444;
    border-radius: 2px;
    height: 4px;
  }
`;

const ControlPanel: React.FC<ControlPanelProps> = ({
  initialViewMode = '3D',
  minRange = 0,
  maxRange = 100,
  initialValue,
  onViewModeChange,
  onValueChange,
}) => {
  // คำนวณค่าเริ่มต้น ถ้าไม่ได้ส่งมาก็ให้อยู่กึ่งกลางระหว่าง min–max
  const defaultValue =
    initialValue !== undefined
      ? initialValue
      : Math.floor((minRange + maxRange) / 2);

  const [viewMode, setViewMode] = useState<'2D' | '3D'>(initialViewMode);
  const [sliderValue, setSliderValue] = useState(defaultValue);

  const handleViewModeChange = (mode: '2D' | '3D') => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };

  const handleRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = parseInt(e.target.value, 10);
    setSliderValue(val);
    onValueChange?.(val);
  };

  return (
    <ControlPanelContainer>
      <ViewToggle>
        <ToggleButton
          isActive={viewMode === '2D'}
          onClick={() => handleViewModeChange('2D')}
        >
          2D
        </ToggleButton>
        <ToggleButton
          isActive={viewMode === '3D'}
          onClick={() => handleViewModeChange('3D')}
        >
          3D
        </ToggleButton>
      </ViewToggle>
      <RangeSlider
        type="range"
        min={minRange}
        max={maxRange}
        value={sliderValue}
        onChange={handleRangeChange}
      />
    </ControlPanelContainer>
  );
};

export default ControlPanel;
