import React, { useState } from 'react';
import Image from 'next/image';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import EastRoundedIcon from '@mui/icons-material/EastRounded';
import { Button, CircularProgress } from '@mui/material';
import apiCompany from '@/services/core/company';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { setSnackBar } from '@/store/features/alert';

type AsideCompanyCardProps = {
  data: any;
  currentCompany: any;
  onClose: () => void;
  onSwitch: (id: number) => void;
  type: string;
  onFetchUserCompany?: (type: string) => void;
};
const AsideCompanyCard = (props: AsideCompanyCardProps) => {
  const { data, currentCompany, onClose, onSwitch, type, onFetchUserCompany } =
    props;
  const { user } = useAppSelector(userSelector);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const handleAcceptInvite = async (companyId: number, name: string) => {
    setLoading(true);
    const data = {
      email: user.email,
      companyId,
    };
    const res = await apiCompany.acceptInviteCompany(data);
    if (!res.isError) {
      setDisable(true);
      dispatch(
        setSnackBar({
          status: true,
          text: `เข้าร่วม ${name} สำเร็จ`,
          severity: 'success',
        })
      );
      if (onFetchUserCompany) {
        onFetchUserCompany('reload');
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `เกิดข้อผิดพลาด`,
          severity: 'error',
        })
      );
    }
    setLoading(false);
  };

  return (
    <div
      className={`company-list ${
        type === 'myCompany' ? '!cursor-pointer' : '!cursor-default'
      } ${data.id === currentCompany.id ? 'bg-[#F5F7F8]' : ''}`}
      onClick={() => {
        if (type === 'myCompany') {
          onClose();
          if (currentCompany.id !== data.id) {
            onSwitch(data.id);
          }
        }
      }}
    >
      <div className="group">
        <div className="logo">
          <Image
            src={data.logo || '/images/company/default-logo.png'}
            width={40}
            height={40}
            alt=""
          />
        </div>
        <div className="text-wrap">
          <div className="name">{data.name}</div>
          <div className="description">
            {`${data.role} • ${data.businessType.name}`}
          </div>
        </div>
      </div>
      <div className="group">
        {type === 'myCompany' ? (
          <div className="arrow">
            {data.id === currentCompany.id ? (
              <CheckRoundedIcon
                fontSize="small"
                style={{
                  color: '#263238',
                }}
              />
            ) : (
              <EastRoundedIcon fontSize="small" />
            )}
          </div>
        ) : (
          <Button
            type="button"
            variant="contained"
            color="dark"
            fullWidth
            sx={{
              width: '108px !important',
              height: '40px !important',
              fontWeight: '400',
            }}
            disabled={disable}
            onClick={() => {
              handleAcceptInvite(data.id, data.name);
            }}
          >
            {loading ? (
              <CircularProgress size={20} />
            ) : (
              <span className="mt-[2px]">เข้าร่วม</span>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default AsideCompanyCard;
