import { useAppDispatch, useAppSelector } from '@/store';
import styled, { css } from 'styled-components';
import React, { useEffect, useState } from 'react';
import { setShowAside, showAsideSelector } from '@/store/features/layout';
import AsideHeader from '@/components/aside/AsideHeader';
import { LoadingFadein } from '@/styles/share.styled';
import { getAsideMenu } from '@/utils/asideMenu';
import { isEmpty } from 'lodash';
import AsideMenuGroup from '@/components/aside/AsideMenuGroup';
import AsideBrand from '@/components/aside/AsideBrand';

const AsideStyle = styled.div<{ $isShow: boolean }>`
  animation: ${LoadingFadein} 0.3s ease-in;
  height: 100dvh;
  border-right: 1px solid #dbe2e5;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 99;
  flex: 1 1 0%;
  max-width: 324px;
  min-width: 324px;
  position: relative;
  @media screen and (max-width: 820px) {
    transition: 0.3s ease-in-out;
    border: 0;
    width: 100%;
    max-width: 100%;
    position: fixed;
    ${({ $isShow }) =>
      $isShow
        ? css`
            z-index: 106;
            transition: none;
          `
        : css`
            z-index: 0;
            transition: 0.3s ease-in-out 0.3s;
          `}
  }
  &:before {
    content: '';
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgb(255, 255, 255) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    bottom: 48px;
    ${({ $isShow }) =>
      $isShow
        ? css`
            transition: 0.3s ease-in-out 0.3s;
            opacity: 1;
          `
        : css`
            opacity: 0;
          `}
  }
  &:after {
    content: '';
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 0) 0%,
      rgb(255, 255, 255) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    top: 64px;
  }
  ${({ $isShow }) =>
    $isShow
      ? css`
          max-width: 324px;
          min-width: 324px;
        `
      : css`
          max-width: 88px;
          min-width: 88px;
        `}
`;
const AsideMenuListStyle = styled.div<{ $isShow: boolean }>`
  display: flex;
  flex-direction: column;
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  height: calc(100dvh - 112px);
  background: white;
  padding-top: 12px;
  overflow: auto;
  overflow-x: hidden;
`;
const AsideBodyStyle = styled.div<{ $isShow: boolean }>`
  width: 100%;
  transition: 0.3s ease-in-out;
  @media screen and (max-width: 820px) {
    position: absolute;
    top: calc(-100% + 124px);
    ${({ $isShow }) =>
      $isShow &&
      css`
        top: 64px;
      `}
  }
`;
// const fetcher = async () => {
//   const res = await apiServiceType.getAllServiceTypes();
//   if (!res.isError) {
//     return res;
//   }
//   return null;
// };

export default function Aside() {
  // const { data: serviceTypes } = useSWR('/service-type', fetcher);
  const dispatch = useAppDispatch();
  const { showAside } = useAppSelector(showAsideSelector);
  const [asideMenuList, setAsideMenuList] = useState<any>({});

  useEffect(() => {
    // Always set showAside to true
    dispatch(setShowAside(true));
  }, [dispatch]);

  useEffect(() => {
    const asideMenuList = getAsideMenu();
    setAsideMenuList(asideMenuList);
  }, []);

  // console.log(serviceTypes);
  // console.log('showAside', showAside);
  return (
    <>
      <AsideStyle $isShow={showAside}>
        <AsideHeader />
        <AsideBodyStyle $isShow={showAside}>
          <AsideMenuListStyle $isShow={showAside}>
            {!isEmpty(asideMenuList) &&
              asideMenuList.map((menu: any, index: number) => (
                <AsideMenuGroup serviceType={menu} key={index} />
              ))}
          </AsideMenuListStyle>
          {/* <div className="flex justify-center items-center"> */}
          {/* <AsideUserMenu /> */}
          <AsideBrand showAside={showAside} />
          {/* </div> */}
        </AsideBodyStyle>
      </AsideStyle>
    </>
  );
}
