import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import { ChevronRight } from '@mui/icons-material';
import { isEmpty, isEqual } from 'lodash';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppDispatch, useAppSelector } from '@/store';
import { setShowAside, showAsideSelector } from '@/store/features/layout';
import Image from 'next/image';
import styled from 'styled-components';
import { useWindowSize } from 'usehooks-ts';
import {
  useReducedMotion,
  asideSubmenuContainerVariants,
  asideSubmenuItemVariants,
  asideSubmenuVariants,
  getAsideMenuMotionProps,
  useAnimationCleanup,
  useKeyboardNavigation,
  focusManagement,
  withReducedMotion,
} from '@/utils/motion';
import {
  AsideMenuItemProps,
  ServiceGroup,
  ServiceItem,
} from '@/types/aside-menu';

// Motion variants for menu item components
const menuItemVariants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  hover: { scale: 1.02 },
  tap: { scale: 0.98 },
};

const serviceGroupNameVariants = {
  initial: { opacity: 0, x: -10 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -10 },
};

const menuIconVariants = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 },
  hover: { scale: 1.1 },
};

// Remove local expandIconVariants - using centralized version from motion utils

const SubMenuList = styled.div`
  background: #f5f7f8;
  margin: 0 24px;
  border-radius: 12px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  margin-bottom: 16px;
  /* Performance optimizations with reduced motion support */
  ${withReducedMotion(
    `
    will-change: transform, opacity;
    backface-visibility: hidden;
    `,
    `
    will-change: auto;
    `
  )}

  .sub-menu {
    display: flex;
    align-items: center;
    position: relative;
    height: 40px;
    padding-left: 46px;
    cursor: pointer;
    border-radius: 8px;
    font-size: 14px;

    /* Accessibility improvements */
    outline: none;

    &:focus-visible {
      outline: 2px solid #455a64;
      outline-offset: 2px;
    }

    &:hover {
      background: #d6dbde;
    }
    &::before {
      content: '';
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #263238;
      position: absolute;
      transform: translateX(-50%) translateY(-50%);
      left: 22px;
      top: 50%;
    }

    &.active {
      background: linear-gradient(180deg, #455a64 0%, #263238 100%);
      color: white;
      border: 1px solid #37474f;

      &::before {
        background: #ffffff;
      }
    }
  }
`;

// Use the proper typed props interface
type Props = AsideMenuItemProps;
export const AsideMenuItem = (props: Props) => {
  const {
    serviceGroup,
    keyboardNavigation,
    className,
    'data-testid': dataTestId,
  } = props;
  const router = useRouter();
  const { showAside } = useAppSelector(showAsideSelector);
  const { pathname } = router;
  const [isShowSubMenu, setShowSubMenu] = useState(false);
  const [, setFocusedSubmenuIndex] = useState(-1);

  const dispatch = useAppDispatch();
  const size = useWindowSize();
  const prefersReducedMotion = useReducedMotion();
  const { addCleanup } = useAnimationCleanup();
  const menuItemRef = useRef<HTMLDivElement>(null);
  const submenuRef = useRef<HTMLDivElement>(null);
  // const { permissions } = useAppSelector(permissionSelector);
  const clickMenu = async (e: React.MouseEvent, data: ServiceGroup) => {
    e.preventDefault();
    if (!isEmpty(data.serviceItems)) {
      if (showAside) {
        setShowSubMenu(!isShowSubMenu);
      } else {
        setShowSubMenu(true);
      }
      dispatch(setShowAside(true));

      // Focus management for accessibility
      if (!isShowSubMenu && submenuRef.current) {
        setTimeout(() => {
          focusManagement.focusFirst(submenuRef.current);
        }, 100); // Small delay to allow animation to start
      }

      // Update keyboard state is handled by isShowSubMenu state
    } else {
      await router.push(`${data.slug}`, undefined, {
        shallow: true,
      });
      dispatch(setShowAside(false));
    }
  };

  // Keyboard navigation for main menu item
  const mainMenuKeyboardProps = useKeyboardNavigation({
    ...keyboardNavigation,
    onEnter: () =>
      clickMenu({ preventDefault: () => {} } as React.MouseEvent, serviceGroup),
    onSpace: () =>
      clickMenu({ preventDefault: () => {} } as React.MouseEvent, serviceGroup),
    onArrowDown: () => {
      if (!isEmpty(serviceGroup.serviceItems) && isShowSubMenu) {
        setFocusedSubmenuIndex(0);
        setTimeout(() => {
          focusManagement.focusFirst(submenuRef.current);
        }, 50);
      }
    },
    onArrowRight: () => {
      if (!isEmpty(serviceGroup.serviceItems) && !isShowSubMenu) {
        setShowSubMenu(true);
        dispatch(setShowAside(true));
        // isSubmenuOpen state is handled by isShowSubMenu
      }
    },
    onArrowLeft: () => {
      if (isShowSubMenu) {
        setShowSubMenu(false);
        setFocusedSubmenuIndex(-1);
      }
    },
    onEscape: () => {
      if (isShowSubMenu) {
        setShowSubMenu(false);
        setFocusedSubmenuIndex(-1);
        menuItemRef.current?.focus();
      }
    },
  });

  // Submenu keyboard navigation
  const handleSubmenuKeyDown = (
    event: React.KeyboardEvent,
    index: number,
    serviceItem: ServiceItem
  ) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        handleSubmenuClick(serviceItem);
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (index < serviceGroup.serviceItems.length - 1) {
          setFocusedSubmenuIndex(index + 1);
          const nextElement = submenuRef.current?.children[
            index + 1
          ] as HTMLElement;
          nextElement?.focus();
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (index > 0) {
          setFocusedSubmenuIndex(index - 1);
          const prevElement = submenuRef.current?.children[
            index - 1
          ] as HTMLElement;
          prevElement?.focus();
        } else {
          // Focus back to main menu item
          setFocusedSubmenuIndex(-1);
          menuItemRef.current?.focus();
        }
        break;
      case 'Escape':
        event.preventDefault();
        setShowSubMenu(false);
        setFocusedSubmenuIndex(-1);
        menuItemRef.current?.focus();
        break;
      default:
        break;
    }
  };

  const handleSubmenuClick = async (serviceItem: ServiceItem) => {
    if (size.width <= 820) {
      dispatch(setShowAside(false));
    }
    await router.push(`${serviceItem.slug}`);
  };
  useEffect(() => {
    // Check if current pathname matches any submenu item
    const hasMatchingSubmenuItem = serviceGroup.serviceItems.some(
      (serviceItem) =>
        pathname === serviceItem.slug ||
        pathname.startsWith(`${serviceItem.slug}/`)
    );

    // Also check if pathname matches the permission slug pattern
    const permissionSlug = `/${serviceGroup.permissionSlug}`;
    const matchesPermissionSlug = pathname.startsWith(permissionSlug);

    if (hasMatchingSubmenuItem || matchesPermissionSlug) {
      setTimeout(() => {
        setShowSubMenu(true);
      }, 300); // Reduced delay for better UX
    }
  }, [pathname, serviceGroup.serviceItems, serviceGroup.permissionSlug]);

  // Animation cleanup effect
  useEffect(() => {
    if (!prefersReducedMotion) {
      addCleanup(() => {
        // Clear any pending timeouts or animations
        setFocusedSubmenuIndex(-1);
      });
    }
  }, [prefersReducedMotion, addCleanup]);

  const checkActiveMenu = (path: string) => {
    // For exact match
    if (isEqual(path, pathname)) {
      return true;
    }

    // For dynamic routes - check if pathname starts with the menu path
    // This handles cases like /sales-order/19/prepare/31 matching /sales-order menu
    return pathname.startsWith(`${path}/`);
  };

  const handleMenuItemPermission = (_slug: string) => {
    // if (permissions) {
    //   return _.some(permissions, (permission: any) => {
    //     const groupSlug = permission.split('.')[0];
    //     const itemSlug = permission.split('.')[1];
    //     return groupSlug === serviceGroup.permissionSlug && itemSlug === slug;
    //   });
    // }
    // return false;
    return true;
  };
  // Get motion props with accessibility support
  const menuItemMotionProps = prefersReducedMotion
    ? {}
    : {
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        whileHover: 'hover',
        whileTap: 'tap',
        variants: menuItemVariants,
        transition: { type: 'spring', stiffness: 300, damping: 30, mass: 1 },
      };

  const serviceGroupNameMotionProps = prefersReducedMotion
    ? {}
    : {
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        variants: serviceGroupNameVariants,
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 30,
          mass: 1,
          delay: 0.1,
        },
      };

  const menuIconMotionProps = prefersReducedMotion
    ? {}
    : {
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        whileHover: 'hover',
        variants: menuIconVariants,
        transition: { type: 'spring', stiffness: 400, damping: 25, mass: 0.8 },
      };

  const expandIconMotionProps = prefersReducedMotion
    ? {}
    : {
        animate: isShowSubMenu ? 'expanded' : 'collapsed',
        ...getAsideMenuMotionProps('expandIcon', prefersReducedMotion),
      };

  // Get submenu motion props with custom animation timing
  const submenuMotionProps = prefersReducedMotion
    ? {}
    : {
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        variants: asideSubmenuVariants,
        style: {
          overflow: 'hidden',
          willChange: 'height, opacity',
        },
      };

  const submenuContainerMotionProps = prefersReducedMotion
    ? {}
    : {
        initial: 'initial',
        animate: 'animate',
        exit: 'exit',
        variants: asideSubmenuContainerVariants,
      };

  return (
    <>
      <motion.div
        ref={menuItemRef}
        className={`menu-item ${
          isEmpty(serviceGroup.serviceItems)
            ? checkActiveMenu(`${serviceGroup.slug}`)
              ? 'active'
              : ''
            : ''
        } ${className || ''}`}
        onClick={(e) => clickMenu(e, serviceGroup)}
        onKeyDown={mainMenuKeyboardProps.handleKeyDown}
        tabIndex={0}
        role="menuitem"
        aria-expanded={
          !isEmpty(serviceGroup.serviceItems) ? isShowSubMenu : undefined
        }
        aria-haspopup={!isEmpty(serviceGroup.serviceItems) ? 'menu' : undefined}
        data-testid={dataTestId}
        {...menuItemMotionProps}
      >
        <motion.div {...menuIconMotionProps}>
          <Image
            src={
              serviceGroup.imageUrl || '/icons/aside/supervised_user_circle.svg'
            }
            width={24}
            height={24}
            alt=""
          />
        </motion.div>
        <motion.div
          className="flex-1 service-group-name"
          {...serviceGroupNameMotionProps}
        >
          {serviceGroup.name}
        </motion.div>
        {serviceGroup.serviceItems.length > 0 && (
          <motion.div
            className={`expand-icon ${isShowSubMenu ? 'on' : ''}`}
            {...expandIconMotionProps}
          >
            <ChevronRight />
          </motion.div>
        )}
      </motion.div>

      <AnimatePresence mode="wait">
        {!isEmpty(serviceGroup.serviceItems) && isShowSubMenu && showAside && (
          <motion.div key="submenu" {...(submenuMotionProps as any)}>
            <motion.div {...submenuContainerMotionProps}>
              <SubMenuList
                ref={submenuRef}
                role="menu"
                aria-label={`${serviceGroup.name} submenu`}
              >
                {serviceGroup.serviceItems.map((serviceItem, idx: number) => {
                  if (handleMenuItemPermission(serviceItem.permissionSlug)) {
                    return (
                      <motion.div
                        key={idx}
                        className={`sub-menu ${
                          checkActiveMenu(serviceItem.slug) ? 'active' : ''
                        }`}
                        onClick={() => handleSubmenuClick(serviceItem)}
                        onKeyDown={(e) =>
                          handleSubmenuKeyDown(e, idx, serviceItem)
                        }
                        tabIndex={0}
                        role="menuitem"
                        aria-current={
                          checkActiveMenu(serviceItem.slug) ? 'page' : undefined
                        }
                        {...(prefersReducedMotion
                          ? {}
                          : {
                              variants: asideSubmenuItemVariants,
                              initial: 'initial',
                              animate: 'animate',
                              exit: 'exit',
                              custom: idx,
                            })}
                      >
                        {serviceItem.name}
                      </motion.div>
                    );
                  }
                })}
              </SubMenuList>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
