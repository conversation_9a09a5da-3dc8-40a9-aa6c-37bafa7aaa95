import React from 'react';
import styled, { css } from 'styled-components';
import Image from 'next/image';

const AsideBrandStyled = styled.div<{ $isShow: boolean }>`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background: white;
  border-color: #dbe2e5;
  ${({ $isShow }) =>
    $isShow
      ? css`
          transition: 0.3s ease-in-out 0.3s;
          border-top: 1px solid #dbe2e5;
          img {
            transition: 0.3s ease-in-out 0.3s;
            opacity: 1;
          }
          .version {
            transition: 0.3s ease-in-out 0.3s;
            opacity: 1;
          }
        `
      : css`
          img {
            transition: 0.3s ease-in-out;
            opacity: 0;
          }
          .version {
            transition: 0.3s ease-in-out;
            opacity: 0;
          }
        `}
  .version {
    font-size: 10px;
    text-transform: uppercase;
    color: #90a4ae;
  }
`;
type Props = {
  showAside: boolean;
};
const AsideBrand = ({ showAside }: Props) => {
  return (
    <AsideBrandStyled $isShow={showAside}>
      <Image
        src={'/icons/icon-hon-black.svg'}
        width={48}
        height={16}
        alt=""
        draggable={false}
      />
      <div className="version">VERSION 1.0.0</div>
    </AsideBrandStyled>
  );
};

export default AsideBrand;
