import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import styled, { css } from 'styled-components';
import SwitchCompanyCard from '@/components/dashboard/SwitchCompanyCard';
import apiCompany from '@/services/core/company';
import { companyListProps } from '@/pages/company';
import { useAppDispatch, useAppSelector } from '@/store';
import { getUserProfile, userSelector } from '@/store/features/user';
import { isEmpty } from 'lodash';
import { LoadingFadein } from '@/styles/share.styled';
import {
  setPlaySwitchAnimation,
  setShowAside,
  showAsideSelector,
} from '@/store/features/layout';

const AsideHeaderStyle = styled.div<{
  $isOpenSwitch: boolean;
  $isShow: boolean;
}>`
  display: flex;
  width: 100%;
  padding: 24px 16px;
  z-index: 9;
  top: 0;
  user-select: none;
  position: relative;
  height: 64px;
  background: white;
  @media screen and (max-width: 820px) {
    border-bottom: 1px solid #dbe2e5;
    padding: 0 16px;
    align-items: center;
  }
  .arrow-wrap {
    position: absolute;
    top: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: white;
    z-index: 9;
    cursor: pointer;
    transform-origin: center;
    transition: 0.3s ease-in-out;
    right: -12px;
    transform: translateY(-50%);
    border: 1px solid #dbe2e5;
    @media screen and (max-width: 820px) {
      display: none;
    }
    ${({ $isShow }) =>
      $isShow &&
      css`
        transform: translateY(-50%) rotate(180deg);
      `}
  }
  .company {
    display: flex;
    align-items: center;
    width: 100%;
    column-gap: 16px;
    animation: ${LoadingFadein} ease-in 0.3s;
    .logo {
      width: 40px;
      height: 40px;
      min-width: 40px;
      position: relative;
      background: #f5f7f8;
      border-radius: 8px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .company-name {
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 8px;
      opacity: 0;
      transition: 0.3s ease-in;
      max-width: 100%;
      overflow: hidden;
      ${({ $isShow }) =>
        $isShow
          ? css`
              opacity: 1;
            `
          : css`
              display: none;
              @media screen and (max-width: 820px) {
                display: flex;
                opacity: 1;
              }
            `}
    }
    div:first-child {
      overflow: hidden;
      max-width: 100%;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow-wrap: break-word;
      line-height: 1.25;
      .name {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .switch {
      color: #cfd8dc;
      display: flex;
      align-items: center;
      margin-top: 1px;

      button {
        height: 24px !important;
        width: 74px !important;
        .group {
          .icon {
            transition: 0.2s ease-in;
            ${({ $isOpenSwitch }) =>
              $isOpenSwitch
                ? css`
                    transform: rotate(180deg);
                  `
                : css`
                    transform: rotate(0deg);
                  `}
          }
          .text {
            font-size: 12px !important;
          }
        }
      }
    }
    .cube-hamburger {
      position: absolute;
      right: 16px;
      height: 40px;
      width: 40px;
      border-radius: 8px;
      border: 1px solid #dbe2e5;
      align-items: center;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px 0;
      opacity: 0;
      display: flex;
      @media screen and (max-width: 820px) {
        transition: 0.3s ease-in-out 1s;
        opacity: 1;
        visibility: visible;
      }
      ${({ $isShow }) =>
        $isShow &&
        css`
          justify-content: center;
        `}
      * {
        transition: 0.3s ease-in-out;
      }
      .beef {
        height: 2px;
        width: 20px;
        background: #263238;
        border-radius: 4px;
        ${({ $isShow }) =>
          $isShow &&
          css`
            opacity: 0;
            width: 0;
          `}
      }
      &:before {
        content: '';
        height: 2px;
        width: 20px;
        background: #263238;
        border-radius: 4px;
        transition: 0.3s ease-in-out;
        ${({ $isShow }) =>
          $isShow &&
          css`
            transform: rotate(45deg);
            position: absolute;
          `}
      }
      &:after {
        content: '';
        height: 2px;
        width: 20px;
        background: #263238;
        border-radius: 4px;
        transition: 0.3s ease-in-out;
        ${({ $isShow }) =>
          $isShow &&
          css`
            transform: rotate(-45deg);
            position: absolute;
          `}
      }
    }
  }
`;
const AsideHeader = () => {
  const [openSwitch, setOpenSwitch] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const { showAside, playSwitchAnimation } = useAppSelector(showAsideSelector);
  const { user } = useAppSelector(userSelector);
  const [companyList, setCompanyList] = useState<companyListProps[] | null>([]);
  const wrapperRef = useRef(null);
  // const token = getCookie('access_token');
  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = [
            'switch',
            'MuiButtonBase-root',
            'group',
            'icon',
            'text',
            'MuiTouchRipple-root',
          ];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (!checkValue && target.tagName.toLowerCase() !== 'img') {
            setOpenSwitch(false);
          }
        }
      }

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }

  const getCompanyList = async (data?: string) => {
    if (data === 'switch') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        await dispatch(getUserProfile());
        setCompanyList(res.data);
        await dispatch(setPlaySwitchAnimation(true));
        setTimeout(() => {
          dispatch(setPlaySwitchAnimation(false));
        }, 800);
      }
    } else if (data === 'reload') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        await dispatch(getUserProfile());
        setCompanyList(res.data);
        await dispatch(setPlaySwitchAnimation(true));
        setTimeout(() => {
          dispatch(setPlaySwitchAnimation(false));
        }, 800);
      }
    } else if (data === 'fetch') {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        setCompanyList(res.data);
      }
    } else if (!openSwitch) {
      const res = await apiCompany.getMeCompanyList();
      if (!res.isError) {
        setCompanyList(res.data);
        setOpenSwitch(true);
      } else {
        setCompanyList(null);
      }
    } else {
      setOpenSwitch(false);
    }
  };

  useEffect(() => {
    dispatch(getUserProfile());
    getCompanyList('fetch');
  }, []);

  return (
    <>
      <AsideHeaderStyle $isOpenSwitch={openSwitch} $isShow={showAside}>
        {user && !isEmpty(user.company?.name) && (
          <>
            {/* <div */}
            {/*  className="arrow-wrap" */}
            {/*  onClick={() => { */}
            {/*    dispatch(setShowAside(!showAside)); */}
            {/*  }} */}
            {/* > */}
            {/*  <ArrowForwardRoundedIcon */}
            {/*    sx={{ */}
            {/*      fontSize: '14px', */}
            {/*    }} */}
            {/*  /> */}
            {/* </div> */}
            <div className={`company ${playSwitchAnimation ? 'animate' : ''}`}>
              <div className={`logo ${playSwitchAnimation ? 'animate' : ''}`}>
                <Image
                  src={
                    user.company.logo !== null
                      ? user.company.logo
                      : '/images/product/empty-product.svg'
                  }
                  alt=""
                  width={80}
                  height={80}
                />
              </div>
              <div className="company-name">
                <div>
                  <span className="name">{user.company.name}</span>
                </div>
                {/* <div */}
                {/*  className="switch" */}
                {/*  onClick={async () => { */}
                {/*    // await getCompanyList(); */}
                {/*  }} */}
                {/* > */}
                {/*  <Badge */}
                {/*    color="primary" */}
                {/*    variant="dot" */}
                {/*    invisible={false} */}
                {/*    sx={{ */}
                {/*      '>span': { */}
                {/*        backgroundColor: '#30D5C7', */}
                {/*        height: '12px', */}
                {/*        width: '12px', */}
                {/*        borderRadius: '50%', */}
                {/*      }, */}
                {/*    }} */}
                {/*  > */}
                {/*    <ActionButton */}
                {/*      variant="outlined" */}
                {/*      color="blueGrey" */}
                {/*      icon={<img src="/icons/aside/icon-switch.png" alt="" />} */}
                {/*      text="Switch" */}
                {/*    /> */}
                {/*  </Badge> */}
                {/* </div> */}
              </div>
              <div
                className="cube-hamburger"
                onClick={() => {
                  dispatch(setShowAside(!showAside));
                }}
              >
                <div className="beef" />
              </div>
            </div>
            <div ref={wrapperRef}>
              <SwitchCompanyCard
                open={openSwitch}
                data={companyList}
                currentCompany={user.company}
                handleFetchUserCompany={(data: string) => {
                  getCompanyList(data);
                }}
                handleClose={() => {
                  setOpenSwitch(false);
                }}
              />
            </div>
          </>
        )}
      </AsideHeaderStyle>
    </>
  );
};

export default AsideHeader;
