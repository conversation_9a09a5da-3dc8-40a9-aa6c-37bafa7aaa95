import React from 'react';
import styled, { css } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppSelector } from '@/store';
import { showAsideSelector } from '@/store/features/layout';
import { AsideMenuItem } from '@/components/aside/AsideMenuItem';
import {
  getAsideMenuMotionProps,
  useReducedMotion,
  useAnimationCleanup,
  withReducedMotion,
} from '@/utils/motion';
import { AsideMenuGroupProps } from '@/types/aside-menu';

// Motion-compatible styled component with performance optimizations
const MenuItemStyle = styled(motion.div)<{ $isShow: boolean }>`
  user-select: none;

  /* Performance optimizations with reduced motion support */
  ${withReducedMotion(
    `
      will-change: transform, opacity;
      backface-visibility: hidden;
    `,
    `
      will-change: auto;
    `
  )}

  /* Remove CSS transitions - handled by Framer Motion */
  @media screen and (max-width: 820px) {
    /* Responsive behavior maintained, animations handled by motion */
    ${({ $isShow }) =>
      !$isShow &&
      css`
        overflow: hidden;
      `}
  }

  &:nth-child(1) {
    .group-name {
      margin: 0 24px 16px 24px;
    }
  }

  .group-name {
    font-size: 12px;
    margin: 16px 24px;
    color: #b0bec5;
    /* Remove CSS animations - handled by motion children */
  }

  .menu-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 16px 16px 16px;
    padding: 0 8px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    column-gap: 8px;
    position: relative;
    /* &:before {
      content: '';
      background: linear-gradient(
        to top,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 100%
      );
      position: absolute;
      z-index: 1;
      height: 16px;
      left: 0;
      width: 100%;
      bottom: -16px;
    }
    &:after {
      content: '';
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 100%
      );
      position: absolute;
      z-index: 1;
      height: 16px;
      left: 0;
      width: 100%;
      top: -16px;
    } */
    /* Remove CSS transitions - handled by Framer Motion */

    ${({ $isShow }) =>
      !$isShow &&
      css`
        width: 40px;
        min-width: 40px;
        justify-content: center;
      `}

    &:hover {
      background: #d6dbde;
    }

    &.active {
      background: linear-gradient(180deg, #455a64 0%, #263238 100%);
      color: white;
      img {
        filter: grayscale(1) invert(1) brightness(200%);
      }
    }

    .service-group-name {
      /* Remove CSS animations - handled by motion children */
    }

    > div {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .expand-icon {
      /* Remove CSS animations - handled by motion children */
      svg {
        opacity: 0.5;
        /* Remove CSS transitions - handled by Framer Motion */
      }

      &.on {
        svg {
          opacity: 1;
          /* Remove CSS transforms - handled by Framer Motion */
        }
      }
    }
  }
`;
// Use the proper typed props interface
type Props = AsideMenuGroupProps;

const AsideMenuGroup = (props: Props) => {
  const {
    serviceType,
    motionProps,
    className,
    'data-testid': dataTestId,
  } = props;
  const { showAside } = useAppSelector(showAsideSelector);
  const prefersReducedMotion = useReducedMotion();
  const { addCleanup } = useAnimationCleanup();

  // const { permissions } = useAppSelector(permissionSelector);
  const handleMenuGroupPermission = (_slug: string) => {
    // if (permissions) {
    //   return _.some(permissions, (permission: any) => {
    //     const groupSlug = permission.split('.')[0];
    //     return groupSlug === _slug;
    //   });
    // }
    // return false;
    return true;
  };

  // Get motion props for menu group with accessibility support
  const defaultMotionProps = getAsideMenuMotionProps(
    'menuGroup',
    prefersReducedMotion
  );

  // Merge with custom motion props if provided
  const finalMotionProps = {
    ...defaultMotionProps,
    ...motionProps,
  };

  // Add cleanup for any ongoing animations
  React.useEffect(() => {
    if (!prefersReducedMotion) {
      addCleanup(() => {
        // Cleanup any ongoing animations or timers
        // This ensures smooth unmounting without animation artifacts
      });
    }
  }, [prefersReducedMotion, addCleanup]);

  return (
    <AnimatePresence mode="wait">
      {showAside && (
        <MenuItemStyle
          $isShow={showAside}
          key="menu-group"
          className={className}
          data-testid={dataTestId}
          {...(finalMotionProps as any)}
        >
          {/* <div className="group-name">{serviceType.name}</div> */}
          {serviceType.serviceGroups.map((serviceGroup, index: number) => {
            if (handleMenuGroupPermission(serviceGroup.permissionSlug)) {
              return <AsideMenuItem serviceGroup={serviceGroup} key={index} />;
            }
          })}
        </MenuItemStyle>
      )}
    </AnimatePresence>
  );
};
export default AsideMenuGroup;
