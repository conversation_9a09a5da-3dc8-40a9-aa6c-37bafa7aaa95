import React, { useEffect, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import { userMenuList } from '@/utils/asideMenuList';
import Image from 'next/image';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import { IconButton } from '@mui/material';
import apiUser from '@/services/core/user';
import { deleteCookie } from 'cookies-next';

const UserMenuStyle = styled.div<{ $openUserMenu: boolean }>`
  width: 300px;
  height: 64px;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-top: 1px solid #dbe2e5;
  border-right: 1px solid #dbe2e5;
  transition: 0.8s;
  z-index: 2;
  bottom: 0;
  user-select: none;
  padding: 0 4px;

  .card {
    width: 268px;
    border-radius: 8px;
    box-shadow: 0 0 16px 0 #26323833;
    background-color: #fff;
    position: absolute;
    bottom: 72px;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 8px;
    padding-bottom: 8px;
    transition: 0.2s ease-in-out;
    z-index: 1;
    transform-origin: bottom;
    padding-top: 8px;
    ${({ $openUserMenu }) =>
      $openUserMenu
        ? css`
            opacity: 1;
            visibility: visible;
          `
        : css`
            opacity: 0;
            visibility: hidden;
            transform: translateY(8px);
            box-shadow: 0 0 0 0 #26323833;
          `}
    .hr-custom {
      width: 100%;
      height: 1px;
      border: 1px solid #ebedee;
    }
    .user-menu-list {
      width: 252px;
      height: 42px;
      display: flex;
      align-items: center;
      padding: 12px 16px;
      column-gap: 8px;
      background-color: #fff;
      cursor: pointer;
      font-size: 14px;
      &.logout {
        color: #d32f2f;
      }
      &:hover {
        background-color: #f5f7f8;
      }
      .icon {
        width: 24px;
        height: 24px;
        position: relative;
      }
      .text {
        font-weight: 400;
      }
    }
  }
  .user-menu-profile {
    padding: 0 24px 0 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-side {
      display: flex;
      align-items: center;
      column-gap: 16px;
      border-radius: 24px;
      padding: 4px 12px 4px 4px;
      transition: 0.15s ease-out;
      width: 200px;
      cursor: pointer;
      &:hover {
        background-color: rgba(48, 213, 199, 0.2);
      }
      .image {
        width: 40px;
        height: 40px;
        position: relative;
        border-radius: 50%;
        overflow: hidden;
      }
      .text-group {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .user-name {
          font-weight: 600;
          max-width: 124px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .user-role {
          font-weight: 400;
          font-size: 12px;
          color: #90a4ae;
        }
      }
    }
    .right-side {
      display: flex;
      &:hover {
        .dot-style-wrap {
          &:before {
            background-color: #263238;
          }
          &:after {
            background-color: #263238;
          }
          .dot-style {
            background-color: #263238;
          }
        }
      }
      .dot-style-wrap {
        height: 16px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        aspect-ratio: 1;
        align-items: center;
        &:before {
          content: '';
          width: 4px;
          height: 4px;
          background-color: #dbe2e5;
          border-radius: 50%;
          transition: 0.15s ease-out;
        }
        &:after {
          content: '';
          width: 4px;
          height: 4px;
          background-color: #dbe2e5;
          border-radius: 50%;
          transition: 0.15s ease-out;
        }
        .dot-style {
          width: 4px;
          height: 4px;
          background-color: #dbe2e5;
          border-radius: 50%;
          transition: 0.15s ease-out;
        }
      }
    }
  }
`;
const AsideUserMenu = () => {
  const [open, setOpen] = useState<boolean>(false);
  const { user } = useAppSelector(userSelector);
  const router = useRouter();
  const wrapperRefUser = useRef(null);
  useOutsideAlerter(wrapperRefUser);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        if (ref.current && !ref.current.contains(event.target)) {
          setOpen(false);
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  const ClickUserMenu = (url: string) => {
    if (url) {
      setOpen(false);
      router.push(`${url}`);
    }
  };
  const handleLogout = async () => {
    // todo
    deleteCookie('access_token');
    deleteCookie('JSESSIONID');
    try {
      await apiUser.logout();
    } catch (error) {
      console.log('error', error);
    }
    router.push('/', undefined, {
      shallow: true,
    });
  };
  return (
    <UserMenuStyle $openUserMenu={open} ref={wrapperRefUser}>
      {user && !isEmpty(user) && (
        <div className="card">
          {userMenuList &&
            userMenuList.map((item: any, index: number) => (
              <div
                className="user-menu-list"
                key={index}
                onClick={() => {
                  ClickUserMenu(item.url);
                }}
              >
                <div className="icon">
                  <Image src={item.icon} fill alt="" />
                </div>
                <div className="text">{item.name}</div>
              </div>
            ))}
          <div className="hr-custom" />
          <div
            className="user-menu-list logout"
            onClick={() => {
              handleLogout();
            }}
          >
            <LogoutRoundedIcon />
            <div>ออกจากระบบ</div>
          </div>
        </div>
      )}
      <div className="user-menu-profile">
        {user && !isEmpty(user) && (
          <>
            <div
              className="left-side"
              onClick={() => {
                setOpen(!open);
              }}
            >
              <div className="image">
                <Image
                  src={
                    user.imageUrl !== null
                      ? user.imageUrl
                      : '/icons/icon-blank-profile.svg'
                  }
                  alt=""
                  fill
                  quality={100}
                />
              </div>
              <div className="text-group">
                <div className="user-name">{user.name}</div>
                <div className="user-role">{user.company.roleName}</div>
              </div>
            </div>
            <div className="right-side">
              <IconButton
                onClick={() => {
                  setOpen(!open);
                }}
              >
                <div className="dot-style-wrap">
                  <div className="dot-style" />
                </div>
              </IconButton>
            </div>
          </>
        )}
      </div>
    </UserMenuStyle>
  );
};

export default AsideUserMenu;
