import React from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import Image from 'next/image';

const BentoBtnStyled = styled.div`
  button {
    max-width: 40px;
    min-width: 40px;
    min-height: 40px;
    max-height: 40px;
    padding: 8px !important;
    flex-direction: column;
    align-items: center;
    row-gap: 4px;
    //.bento-row {
    //  width: 100%;
    //  display: flex;
    //  align-items: center;
    //  justify-content: center;
    //  column-gap: 4px;
    //  &:before {
    //    content: '';
    //    width: 4px;
    //    height: 4px;
    //    border-radius: 50%;
    //    background-color: #263238;
    //  }
    //  &:after {
    //    content: '';
    //    width: 4px;
    //    height: 4px;
    //    border-radius: 50%;
    //    background-color: #263238;
    //  }
    //  .dot {
    //    width: 4px;
    //    height: 4px;
    //    border-radius: 50%;
    //    background-color: #263238;
    //  }
    //}
  }
`;
type BentoBtnProps = {
  url?: string;
};
const BentoBtn = ({ url }: BentoBtnProps) => {
  const router = useRouter();
  return (
    <BentoBtnStyled>
      <Button
        type="button"
        variant="outlined"
        color="blueGrey"
        onClick={async (_e: any) => {
          if (!isEmpty(url)) {
            await router.push(`${url}`);
          }
        }}
      >
        <Image src={'/icons/icon-home.svg'} width={24} height={24} alt="" />
        {/* {[...Array(3)].map((_, index) => ( */}
        {/*  <div key={index} className="bento-row"> */}
        {/*    <div className="dot" /> */}
        {/*  </div> */}
        {/* ))} */}
      </Button>
    </BentoBtnStyled>
  );
};

export default BentoBtn;
