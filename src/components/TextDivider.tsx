import styled from '@emotion/styled';

const TextDividerStyle = styled.div`
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  font-size: 14px;
  margin: 12px 0;
  > div {
    padding: 5px 15px;
    background: white;
    z-index: 50;
  }
  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background: #dbe2e5;
    position: absolute;
    left: 0;
    top: 50%;
    z-index: 49;
  }
`;

type TextDividerProps = {
  text: string;
};
export default function TextDivider({ text }: TextDividerProps) {
  return (
    <TextDividerStyle>
      <div>{text}</div>
    </TextDividerStyle>
  );
}
