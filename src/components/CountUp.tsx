import { useEffect, useRef } from 'react';
import { animate, useMotionValue } from 'framer-motion';

interface CountUpProps {
  to: number;
  from?: number;
  direction?: 'up' | 'down';
  delay?: number;
  duration?: number;
  className?: string;
  startWhen?: boolean;
  separator?: string;
  decimals?: number;
  onStart?: () => void;
  onEnd?: () => void;
  suffix?: string;
}

export default function CountUp({
  to,
  from = 0,
  direction = 'up',
  delay = 0,
  duration = 2,
  className = '',
  startWhen = true,
  separator = '',
  decimals,
  onStart,
  onEnd,
  suffix = '',
}: CountUpProps) {
  const ref = useRef<HTMLSpanElement>(null);
  const motionValue = useMotionValue(direction === 'down' ? to : from);

  useEffect(() => {
    if (ref.current) {
      ref.current.textContent = String(direction === 'down' ? to : from);
    }
  }, [from, to, direction]);

  useEffect(() => {
    if (startWhen) {
      if (typeof onStart === 'function') {
        onStart();
      }

      const timeoutId = setTimeout(() => {
        animate(motionValue, direction === 'down' ? from : to, {
          duration,
          onUpdate: (latest) => {
            if (ref.current) {
              const raw = Number(latest);
              const isInteger = Number.isInteger(raw);
              const fractionDigits = isInteger ? 0 : decimals ?? 2;

              const options: Intl.NumberFormatOptions = {
                useGrouping: !!separator,
                ...(fractionDigits > 0 && {
                  minimumFractionDigits: fractionDigits,
                  maximumFractionDigits: fractionDigits,
                }),
              };

              const formattedNumber = Intl.NumberFormat(
                'en-US',
                options
              ).format(Number(raw.toFixed(fractionDigits)));

              ref.current.textContent = `${
                separator
                  ? formattedNumber.replace(/,/g, separator)
                  : formattedNumber
              }${suffix ? ` ${suffix}` : ''}`;
            }
          },
          onComplete: () => {
            if (typeof onEnd === 'function') {
              onEnd();
            }
          },
        });
      }, delay * 1000);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [
    startWhen,
    motionValue,
    direction,
    from,
    to,
    delay,
    duration,
    onStart,
    onEnd,
    separator,
    decimals,
    suffix,
  ]);

  return <span className={className} ref={ref} />;
}
