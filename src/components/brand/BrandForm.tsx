import React, { useState } from 'react';
import <PERSON>Field from '@/components/ImageField';
import { Button, CircularProgress, TextField } from '@mui/material';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { isEmpty, isNull } from 'lodash';

const BrandFormStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 40px;
  animation: ${LoadingFadein} 0.3s ease-in;
  width: 100%;
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 640px;
    padding: 0 24px;
    max-width: 100%;
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอก'),
});
type BrandFormProps = {
  handleSubmitBrand: (value: any, file: any) => void;
  submitting: boolean;
  disable: boolean;
  initialValues?: any;
};
const BrandForm = ({
  handleSubmitBrand,
  submitting,
  disable,
  initialValues,
}: BrandFormProps) => {
  const [file, setFile] = useState<any>(null);
  const formik = useFormik({
    initialValues: {
      name: !isEmpty(initialValues) ? initialValues.name : '',
      description: !isEmpty(initialValues) ? initialValues.description : '',
      imageUrl: !isEmpty(initialValues) ? initialValues.imageUrl : null,
      id: !isEmpty(initialValues) ? initialValues.id : null,
    },
    validationSchema,
    onSubmit: (values: any) => {
      handleSubmitBrand(values, file);
    },
  });
  const makeFile = async (imageFile: any) => {
    if (imageFile && imageFile.length === 1) {
      const formData = new FormData();
      formData.append('file', imageFile[0]);
      setFile(formData);
    } else {
      setFile(null);
    }
  };
  return (
    <BrandFormStyle>
      <form onSubmit={formik.handleSubmit}>
        <ImageField
          handleChange={(files: any) => {
            makeFile(files);
          }}
          objectFit="cover"
          defaultBackground={
            !isNull(formik.values.imageUrl)
              ? formik.values.imageUrl
              : '/images/add-image.svg'
          }
          borderRadius="14px"
        />
        <p className="field-title">ชื่อแบรนด์</p>
        <TextField
          type="text"
          name="name"
          placeholder="ชื่อแบรนด์"
          value={formik.values.name}
          onChange={formik.handleChange}
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && (formik.errors.name as string)}
        />
        <p className="field-title">รายละเอียด</p>
        <TextField
          multiline
          rows={4}
          type="text"
          name="description"
          placeholder="ระบุข้อมูล"
          value={formik.values.description}
          onChange={formik.handleChange}
          error={
            formik.touched.description && Boolean(formik.errors.description)
          }
          helperText={
            formik.touched.description && (formik.errors.description as string)
          }
        />
        <Button
          type="submit"
          variant="contained"
          color="dark"
          disabled={disable}
          fullWidth
          sx={{ fontSize: '16px', margin: '40px 0 24px 0' }}
        >
          {submitting ? (
            <CircularProgress
              size={20}
              style={{
                color: 'white',
              }}
            />
          ) : (
            'บันทึก'
          )}
        </Button>
      </form>
    </BrandFormStyle>
  );
};

export default BrandForm;
