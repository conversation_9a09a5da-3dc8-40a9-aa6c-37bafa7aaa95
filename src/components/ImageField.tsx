import React, { ReactNode, useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { Button } from '@mui/material';
import { validateImageFiles } from '@/utils/size';
import { LoadingFadein } from '@/styles/share.styled';

const ImageWrapStyle = styled.div<{
  $imageUrl: string;
  $borderRadius: string | undefined;
}>`
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 16px;
  .image {
    position: relative;
    width: 134px;
    height: 134px;
    background-color: transparent;
    display: flex;
    justify-content: center;
    border-radius: ${({ $borderRadius }) => $borderRadius || '50%'};
    align-items: ${({ $imageUrl }) =>
      $imageUrl === '/images/company/empty-company.svg' ? 'end' : 'center'};
    overflow: hidden;
    img {
      width: ${({ $imageUrl }) =>
        $imageUrl === '/icons/icon-upload-image.svg' ? 'auto' : '100%'};
      height: ${({ $imageUrl }) =>
        $imageUrl === '/icons/icon-upload-image.svg' ? 'auto' : '100%'};
    }
    cursor: pointer;
  }
  button {
    display: flex;
    align-items: center;
    column-gap: 4px;
    font-size: 14px;
    font-weight: 400;
    color: #263238 !important;
  }
  .err {
    width: auto;
    display: flex;
    justify-content: center;
    text-align: center;
    color: #e91e63;
    font-size: 12px;
    margin: -4px 0 -4px 0;
    animation: ${LoadingFadein} 0.3s ease-in;
  }
  .condition-text {
    width: 270px;
    display: flex;
    justify-content: center;
    text-align: center;
    color: #b0bec5;
    font-size: 12px;
  }
`;

type ImageFieldProps = {
  handleChange: (file: any) => void;
  defaultBackground: string;
  objectFit?: string;
  textUploadBtn?: string;
  borderRadius?: string | undefined;
  conditionText?: string | undefined;
  alertRequire?: boolean | undefined;
  iconButton?: ReactNode | undefined;
  disableEvent?: boolean;
  maxSizeInMB?: number;
  isCompress?: boolean;
  ignoreQuality?: boolean;
};

const ImageField = React.forwardRef<HTMLInputElement, ImageFieldProps>(
  (props, ref) => {
    const {
      handleChange,
      defaultBackground,
      textUploadBtn,
      borderRadius,
      conditionText,
      alertRequire,
      iconButton,
      disableEvent,
      maxSizeInMB,
      isCompress,
      ignoreQuality,
    } = props;
    const [imageUrl, setImageUrl] = useState<any>();
    const [errorMsg, setErrorMsg] = useState('');
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [isProcessing, setIsProcessing] = useState<boolean>(false);

    useEffect(() => {
      if (defaultBackground) setImageUrl(defaultBackground);
    }, [defaultBackground]);

    const updateFile = (file: any) => {
      if (file && file[0]) {
        const imageUrl = URL.createObjectURL(file[0]);
        setImageUrl(imageUrl);
        handleChange(file);
      }
    };

    const handleClickUpload = () => {
      if (fileInputRef.current && !disableEvent) {
        fileInputRef.current.click();
      }
    };

    return (
      <ImageWrapStyle $imageUrl={imageUrl} $borderRadius={borderRadius}>
        <div className="image">
          <Image
            onClick={() => {
              if (!isProcessing) {
                handleClickUpload();
              }
            }}
            src={imageUrl ?? defaultBackground}
            width={imageUrl === '/images/company/empty-company.svg' ? 56 : 200}
            height={imageUrl === '/images/company/empty-company.svg' ? 80 : 200}
            alt=""
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex flex-col items-center gap-2">
          {textUploadBtn && (
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              style={{
                color: '#78909C',
                width: '100px',
                height: '32px',
                borderRadius: '16px',
              }}
              disabled={isProcessing}
              onClick={handleClickUpload}
            >
              {!isProcessing ? (
                <>
                  {iconButton}
                  {textUploadBtn}
                </>
              ) : (
                'กำลังบีบอัด'
              )}
            </Button>
          )}
          <input
            style={{ display: 'none' }}
            ref={(e) => {
              fileInputRef.current = e;
              if (typeof ref === 'function') {
                ref(e);
              } else if (ref) {
                (
                  ref as React.MutableRefObject<HTMLInputElement | null>
                ).current = e;
              }
            }}
            type="file"
            onChange={async (event: any) => {
              const { files } = event.target;
              setIsProcessing(true);
              const validationResult = await validateImageFiles(
                files,
                maxSizeInMB ?? 2,
                isCompress ?? true,
                ignoreQuality ?? false
              );
              setIsProcessing(false);
              if (validationResult.status) {
                const newFiles = Array.from(validationResult.files);
                updateFile(newFiles);
                setErrorMsg('');
              } else {
                setImageUrl(defaultBackground);
                setErrorMsg(validationResult.message);
                handleChange(null);
              }
            }}
          />
        </div>
        {errorMsg && <div className="err">{errorMsg}</div>}
        {alertRequire && <div className="err">Please upload image.</div>}
        {conditionText && <div className="condition-text">{conditionText}</div>}
      </ImageWrapStyle>
    );
  }
);

export default ImageField;
