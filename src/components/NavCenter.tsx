import styled from 'styled-components';
import { useRouter } from 'next/router';
import { KeyboardBackspace } from '@mui/icons-material';

const NavCenterStyle = styled.div`
  @keyframes fadeNavIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  width: 100%;
  height: 64px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20px;
  animation: fadeNavIn 0.3s ease;
  border-bottom: 1px solid #eee;
  position: sticky;
  background: white;
  z-index: 10;
  top: 0;
  .back-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    transition: 0.3s;
    svg {
      font-size: 1.5em;
      color: #263238;
      transition: 0.3s;
    }
    &:active {
      transform: scale(0.9);
    }
    &:hover {
      svg {
        color: #555;
      }
    }
  }
  .title {
    flex: 1;
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    @media screen and (max-width: 820px) {
      font-size: 1.1em;
    }
    h2 {
      font-size: 1.2em;
      margin: 0;
      font-weight: 500;
    }
  }
`;

type NavCenterProps = {
  title: string;
  backUrl?: any;
  children?: React.ReactNode;
};
export default function NavCenter({
  title,
  backUrl,
  children,
}: NavCenterProps) {
  const router = useRouter();
  return (
    <NavCenterStyle>
      {backUrl && (
        <div
          className="back-btn"
          onClick={() => router.push(backUrl, '', { scroll: true })}
        >
          <KeyboardBackspace />
        </div>
      )}
      <div className="title">
        <h2>{title}</h2>
      </div>
      <div className="flex items-end w-full justify-end">{children}</div>
    </NavCenterStyle>
  );
}
