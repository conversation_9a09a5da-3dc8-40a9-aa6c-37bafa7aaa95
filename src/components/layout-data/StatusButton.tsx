import { Button } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

type StatusButtonProps = {
  rowId: number;
  status: string;
  loading?: boolean;
  isSalesOrder?: boolean;
};
const StatusButton = ({
  rowId,
  status,
  loading,
  isSalesOrder,
}: StatusButtonProps) => {
  const router = useRouter();
  const [buttonName, setButtonName] = useState('รายละเอียด');
  useEffect(() => {
    if (!loading) {
      switch (status) {
        case 'สเปคสินค้า':
          setButtonName('รายละเอียด');
          break;
        case 'เสนอราคา':
          setButtonName('รายละเอียด');
          break;
        case 'กำลังดำเนินการ':
          setButtonName('รายละเอียด');
          break;
        case 'สำเร็จ':
          setButtonName('รายละเอียด');
          break;
        case 'ยกเลิก':
          setButtonName('รายละเอียด');
          break;
        default:
          setButtonName('รายละเอียด');
      }
    }
  });
  const handleClick = async () => {
    if (isSalesOrder) {
      await router.push(`/sales-order/${rowId}/spec/?step=${status}`);
    } else {
      await router.push(`/orders/${rowId}/spec/?step=${status}`);
    }
  };
  return (
    <>
      <Button
        type="button"
        variant="outlined"
        color="blueGrey"
        onClick={handleClick}
        style={{
          width: 'fit-content',
          height: '32px',
          borderRadius: '6px',
          fontSize: '12px',
          marginRight: '8px',
        }}
      >
        {buttonName}
      </Button>
    </>
  );
};

export default StatusButton;
