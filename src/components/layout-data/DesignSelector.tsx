import { Avatar } from '@mui/material';
import styled from 'styled-components';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 16px;
  .data-item {
    height: 72px;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    padding: 0 14px;
    cursor: pointer;
    &.active {
      border-color: #30d5c7;
      color: #00c1af;
      background-color: #f2fefe;
      .name {
        p {
          color: #00c1af;
        }
      }
    }
    > div {
      display: flex;
      flex-direction: column;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
`;

type GraphicSelectorProps = {
  data: any;
  selectedId: number | null;
  handleSelect: (item: any) => void;
};
const DesignSelector = ({
  data,
  selectedId,
  handleSelect,
}: GraphicSelectorProps) => {
  return (
    <SelectorListStyle>
      {data.map((item: any, index: number) => (
        <div
          key={index}
          className={`data-item ${item.id === selectedId && 'active'}`}
          onClick={() => handleSelect(item)}
        >
          <Avatar src={item.imageUrl} style={{ width: '48px', height: '48px' }}>
            {item.name[0]}
          </Avatar>
          <div className="name">
            <p>{item.name}</p>
            <p style={{ fontSize: '12px', fontWeight: '400' }}>
              {item.description}
            </p>
          </div>
        </div>
      ))}
    </SelectorListStyle>
  );
};

export default DesignSelector;
