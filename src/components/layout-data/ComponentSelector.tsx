import styled from 'styled-components';
import { Avatar, TextField } from '@mui/material';
import React from 'react';
import { isEmpty } from 'lodash';
import { EmptyProductAttr } from '@/styles/share.styled';
import { useRouter } from 'next/router';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 16px;
  .data-item {
    height: 72px;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    padding: 0 14px;
    cursor: pointer;
    justify-content: space-between;
    &.active {
      border-color: #30d5c7;
      color: #00c1af;
      background-color: #f2fefe;
      .name {
        p {
          color: #00c1af;
        }
      }
    }
    > div {
      display: flex;
      flex-direction: column;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
`;

type ComponentSelectorProps = {
  data: any;
  selectedIds: {
    subMaterialDetailId: number;
    value: number;
  }[];
  handleUpdate: (ids: any[]) => void;
  layoutDataById: any;
};

const ComponentSelector = ({
  data,
  selectedIds,
  handleUpdate,
  layoutDataById,
}: ComponentSelectorProps) => {
  const router = useRouter();
  const updateValue = (subMaterialDetailId: number, newValue: number) => {
    const selected = selectedIds || [];
    const newIds = [...selected];
    const index = newIds.findIndex(
      (item) => item.subMaterialDetailId === subMaterialDetailId
    );
    if (index !== -1) {
      newIds[index].value = newValue;
    } else {
      newIds.push({
        subMaterialDetailId: subMaterialDetailId,
        value: newValue,
      });
    }
    handleUpdate(newIds);
  };
  // console.log(selectedIds);
  // console.log(data);
  return (
    <>
      {isEmpty(data.productConfig) && (
        <EmptyProductAttr
          onClick={async () => {
            await router.push(
              `/product/${layoutDataById.productModel.productId}/attributes`
            );
          }}
        >
          Add-on attributes
        </EmptyProductAttr>
      )}
      {data.productConfig.map((productConfigItem: any, index: number) => {
        return (
          <div
            key={index}
            style={{
              display: 'flex',
              flexDirection: 'column',
              rowGap: '16px',
            }}
          >
            <div>
              <div className="mb-[4px]">{productConfigItem.master.name}</div>
              <SelectorListStyle>
                {productConfigItem.configMaterial.map(
                  (configMaterialItem: any, index: number) => (
                    <div key={index} className={`data-item`}>
                      <div className="flex !flex-row items-center gap-[16px]">
                        <Avatar
                          src={
                            configMaterialItem.subMaterialDetail.imageUrl || ''
                          }
                          style={{ width: '48px', height: '48px' }}
                        >
                          {configMaterialItem.subMaterialDetail.name[0]}
                        </Avatar>
                        <div className="name">
                          <p>{configMaterialItem.subMaterialDetail.name}</p>
                        </div>
                      </div>
                      <div className="controller">
                        <TextField
                          sx={{
                            width: '84px',
                            '& input': {
                              textAlign: 'center',
                            },
                          }}
                          value={
                            selectedIds.find(
                              (selectedItem: any) =>
                                selectedItem.subMaterialDetailId ===
                                configMaterialItem.subMaterialDetail.id
                            )?.value ?? ''
                          }
                          type="number"
                          placeholder="จำนวน"
                          onChange={(e) => {
                            const value = parseFloat(e.target.value);
                            if (value < 1) {
                              e.target.value = '';
                            } else {
                              updateValue(
                                configMaterialItem.subMaterialDetail.id,
                                value
                              );
                            }
                          }}
                          onKeyDown={(e: any) => {
                            if (e.key === '-') {
                              e.preventDefault();
                            }
                          }}
                          inputProps={{
                            style: {
                              textAlign: 'center',
                            },
                          }}
                        />
                      </div>
                    </div>
                  )
                )}
              </SelectorListStyle>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default ComponentSelector;
