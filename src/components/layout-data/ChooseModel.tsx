import apiProductModel from '@/services/stock/productModel';
import { useEffect, useState } from 'react';
import styled, { css } from 'styled-components';
import Image from 'next/image';

const ModelListStyle = styled.div`
  display: flex;
  flex-direction: row;
  gap: 20px;
`;

const ModelItemStyle = styled.div<{ $active: boolean }>`
  padding: 10px;
  border-radius: 16px;
  cursor: pointer;
  width: 200px;
  height: 200px;
  transition: 0.2s;
  .img-container {
    width: 100%;
    height: 140px;
    position: relative;
    img {
      object-fit: cover;
    }
  }
  ${({ $active }) =>
    $active
      ? css`
          border: 1px solid #30d5c7;
          box-shadow: 0 0 1px 1px #30d5c7;
        `
      : css`
          border: 1px solid #dbe2e5;
        `}
`;

type ChooseModelProps = {
  productId: number;
  selectedId: number;
  handleSelect: (modelId: number) => void;
};

const ChooseModel = ({
  productId,
  selectedId,
  handleSelect,
}: ChooseModelProps) => {
  const [modelList, setModelList] = useState([]);
  useEffect(() => {
    getModelList();
    console.log('selectedId', selectedId);
  }, [productId]);
  const getModelList = async () => {
    if (productId !== null) {
      const res = await apiProductModel.getList(`${productId}`);
      if (res && !res.isError) {
        setModelList(res.data);
      }
    }
  };

  return (
    <ModelListStyle>
      {modelList &&
        modelList.map((item: any, index: number) => (
          <ModelItemStyle
            $active={item.id === selectedId}
            key={index}
            onClick={() => handleSelect(item.id)}
          >
            <div className="img-container">
              <Image
                src={item.imageUrl || '/images/product/default-img.svg'}
                fill
                alt=""
                style={{ objectFit: 'cover', borderRadius: '8px' }}
              />
            </div>
            <p>{item.name}</p>
          </ModelItemStyle>
        ))}
    </ModelListStyle>
  );
};

export default ChooseModel;
