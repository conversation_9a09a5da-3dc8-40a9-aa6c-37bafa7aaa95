import styled from 'styled-components';
import { Avatar, IconButton } from '@mui/material';
import { Edit } from '@mui/icons-material';
import ContactDialog from '@/components/layout-data/ContactDialog';
import apiContact from '@/services/core/contact';
import { useEffect, useState } from 'react';

type ContactCardProps = {
  data: any;
  handleChangeContact: () => void;
};

const ContactCardStyle = styled.div`
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  .contact-details {
    padding: 20px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid #dbe2e5;
    h2 {
      margin: 0;
      padding: 0 10px;
    }
    .detail-list {
      display: flex;
      flex-direction: row;
      align-items: center;
      > div {
        border-right: 2px solid #dbe2e5;
        padding: 0 10px;
        &:last-child {
          border-right: none;
        }
      }
    }
    .edit-btn {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  .order-product-id {
    padding: 20px;
    flex: 1;
    h2 {
      margin: 0;
      font-size: 1.2em;
    }
    p {
      margin: 0 0 10px 0;
      color: #90a4ae;
      font-size: 0.9em;
    }
  }
  .sale-details {
    flex: 1;
    padding: 20px;
    border-left: 1px solid #dbe2e5;
    h2 {
      margin: 0;
    }
    p {
      margin: 0 0 10px 0;
      color: #90a4ae;
      font-size: 0.9em;
    }
  }
`;
const ContactCard = ({ data }: ContactCardProps) => {
  const [contactInfo, setContactInfo] = useState<any>(null);
  useEffect(() => {
    getContactInfo();
  }, [data.contactId]);
  const getContactInfo = async () => {
    if (data && data.contactId) {
      const contactId = data.contactId.toString();
      const res = await apiContact.getInfo(contactId);
      // console.log('res', res);
      if (res && !res.isError) {
        setContactInfo(res.data);
      }
    }
  };
  console.log(data);
  return (
    <>
      {data && contactInfo && (
        <ContactCardStyle>
          <div className="contact-details">
            <Avatar
              src={contactInfo.imageUrl}
              style={{ width: '64px', height: '64px' }}
            />
            <div>
              <h2>{contactInfo.name}</h2>
              <div className="detail-list">
                <div>บุคคลธรรมดา</div>
                <div>{contactInfo.phoneNumber}</div>
              </div>
            </div>
            <div className="edit-btn">
              <ContactDialog
                handleChooseContact={(contactId: number) =>
                  console.log(contactId)
                }
              >
                <IconButton>
                  <Edit />
                </IconButton>
              </ContactDialog>
            </div>
          </div>
          <div className="flex flex-row">
            <div className="order-product-id">
              <p>รหัสออเดอร์สินค้า</p>
              <h2>{data.ldCode}</h2>
            </div>
            <div className="sale-details">
              <p>พนักงานขาย</p>
              <div className="flex flex-row items-center gap-2">
                <Avatar style={{ width: '32px', height: '32px' }} />
                <span>
                  {/* // todo */}
                  Sahajohn
                  {/* {data.createdUser.firstName} {data.createdUser.lastName} */}
                </span>
              </div>
            </div>
          </div>
        </ContactCardStyle>
      )}
    </>
  );
};

export default ContactCard;
