import styled from 'styled-components';
import { Avatar } from '@mui/material';
import { isEmpty } from 'lodash';
import { EmptyProductAttr } from '@/styles/share.styled';
import { useRouter } from 'next/router';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 16px;
  .data-item {
    height: 72px;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    padding: 0 14px;
    cursor: pointer;
    &.active {
      border-color: #30d5c7;
      color: #00c1af;
      background-color: #f2fefe;
      .name {
        p {
          color: #00c1af;
        }
      }
    }
    > div {
      display: flex;
      flex-direction: column;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
`;

type MaterialSelectorProps = {
  data: any;
  selectedId: number | null;
  handleSelect: (data: any) => void;
  layoutDataById: any;
};
const MaterialSelector = ({
  data,
  selectedId,
  handleSelect,
  layoutDataById,
}: MaterialSelectorProps) => {
  const router = useRouter();
  return (
    <>
      {isEmpty(data.productConfig) && (
        <EmptyProductAttr
          onClick={async () => {
            await router.push(
              `/product/${layoutDataById.productModel.productId}/attributes`
            );
          }}
        >
          Add-on attributes
        </EmptyProductAttr>
      )}
      {data.productConfig.map((productConfigItem: any, index: number) => {
        return (
          <div
            key={index}
            style={{
              display: 'flex',
              flexDirection: 'column',
              rowGap: '16px',
            }}
          >
            <div>
              <div className="mb-[4px]">{productConfigItem.master.name}</div>
              <SelectorListStyle>
                {productConfigItem.configMaterial.map(
                  (configMaterialItem: any, index: number) => (
                    <div
                      key={index}
                      className={`data-item ${
                        configMaterialItem.subMaterialDetail.id ===
                          selectedId && 'active'
                      }`}
                      onClick={() => {
                        handleSelect(configMaterialItem.subMaterialDetail);
                      }}
                    >
                      <Avatar
                        src={configMaterialItem.subMaterialDetail.imageUrl}
                        style={{ width: '48px', height: '48px' }}
                      >
                        {configMaterialItem.subMaterialDetail.name[0]}
                      </Avatar>
                      <div className="name">
                        <p>{configMaterialItem.subMaterialDetail.name}</p>
                      </div>
                    </div>
                  )
                )}
              </SelectorListStyle>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default MaterialSelector;
