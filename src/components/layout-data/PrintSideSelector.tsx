import { Avatar } from '@mui/material';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 16px;
  .data-item {
    height: 72px;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    padding: 0 14px;
    cursor: pointer;
    animation: ${LoadingFadein} 0.3s ease-in;
    &.active {
      border-color: #30d5c7;
      color: #00c1af;
      background-color: #f2fefe;
      .name {
        p {
          color: #00c1af;
        }
      }
    }
    > div {
      display: flex;
      flex-direction: column;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
`;

type PrintSideSelectorProps = {
  data: any;
  paper: any;
  selectedId: number | null;
  handleSelect: (data: any) => void;
};

const PrintSideSelector = ({
  data,
  paper,
  selectedId,
  handleSelect,
}: PrintSideSelectorProps) => {
  // console.log('paper', paper);
  const filteredSides =
    data?.filter(
      (side: any) => paper?.side === 2 || side.name !== 'ด้านหน้า / ด้านหลัง'
    ) || [];

  return (
    <SelectorListStyle>
      {filteredSides.map((side: any, index: any) => (
        <div
          key={index}
          className={`data-item ${side.id === selectedId ? 'active' : ''}`}
          onClick={() => handleSelect(side)}
        >
          <Avatar src={side.imageUrl} style={{ width: '48px', height: '48px' }}>
            {side.name[0]}
          </Avatar>
          <div className="name">
            <p>{side.name}</p>
          </div>
        </div>
      ))}
    </SelectorListStyle>
  );
};

export default PrintSideSelector;
