import React, { useEffect, useState } from 'react';
import { Checkbox, FormControl, MenuItem, Select } from '@mui/material';
import styled from 'styled-components';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import { isEmpty } from 'lodash';
import { EmptyField, FadeInStyled } from '@/styles/share.styled';
import { useRouter } from 'next/router';
import apiColor from '@/services/stock/color';

const ColorChipStyled = styled.div`
  display: flex;
  align-items: center;
  column-gap: 8px;
  border-radius: 20px;
  background: #edf1f2;
  height: 30px;
  padding: 0 8px;
  .color-name {
    font-size: 14px;
    line-height: 1;
  }
`;

const CheckboxWrap = styled.div`
  span {
    &:hover {
      background: none !important;
    }
  }
  .MuiTouchRipple-root {
    display: none !important;
  }
`;

const ColorMultipleStyled = styled.div`
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  column-gap: 16px;
  .color {
    .MuiSelect-select {
      padding: 0 14px 0 4px !important;
    }
    .MuiFormControl-root {
      height: auto;
      min-height: 40px;
      .MuiInputBase-root {
        height: 100%;
        min-height: 40px;
      }
    }
  }
`;

type ColorSelectorProps = {
  data: any;
  handleSelect: (newData: any) => void;
  canSelectColor: string;
  formik: any;
  productPrint: any;
  isSubmitted: boolean;
};

const ColorSelector = ({
  data,
  handleSelect,
  canSelectColor,
  formik,
  productPrint,
  isSubmitted,
}: ColorSelectorProps) => {
  const router = useRouter();
  const [menuItems, setMenuItems] = useState<any>({});

  const selectedProductConfig = productPrint?.productConfig?.find(
    (config: any) => config.master.id === formik.values.printMasterId
  );

  const colorList =
    selectedProductConfig?.configMaterial?.map(
      (material: any) => material.subMaterialDetail
    ) || [];

  const handleClickColorFront = (item: any) => {
    const selectedProductColorId = data.colorFront.map(
      (item: any) => item.colorSubMaterialDetail.id
    );
    if (selectedProductColorId.includes(item.id)) {
      const removeColorFront = data.colorFront.filter(
        (colorFront: any) => colorFront.colorSubMaterialDetail.id !== item.id
      );
      handleSelect({
        ...data,
        colorFront: removeColorFront,
      });
    } else {
      const addColorFront = [
        ...data.colorFront,
        {
          colorSubMaterialDetail: item,
          sideName: 'FRONT',
        },
      ];
      handleSelect({
        ...data,
        colorFront: addColorFront,
      });
    }
  };

  const handleClickColorBack = (item: any) => {
    const selectedProductColorId = data.colorBack.map(
      (item: any) => item.colorSubMaterialDetail.id
    );

    if (selectedProductColorId.includes(item.id)) {
      const removeColorBack = data.colorBack.filter(
        (colorBack: any) => colorBack.colorSubMaterialDetail.id !== item.id
      );

      handleSelect({
        ...data,
        colorBack: removeColorBack,
      });
    } else {
      const addColorBack = [
        ...data.colorBack,
        {
          colorSubMaterialDetail: item,
          sideName: 'BACK',
        },
      ];

      handleSelect({
        ...data,
        colorBack: addColorBack,
      });
    }
  };

  const getMenuItemColorQuantity = async (colorSubMaterialDetail: any) => {
    const res = await apiColor.getListColorsBySubMaterialDetailId(
      colorSubMaterialDetail.id
    );
    if (res && res.status && res.data) {
      return res.data.map((item: any) => ({
        id: item.id,
        name: item.name,
        value: item.value,
        colorSubMaterialDetailId: colorSubMaterialDetail.id,
      }));
    }
    return [];
  };

  const fetchColorQuantities = async () => {
    const quantities: any = {};

    const fetchColors = async (side: string, colors: any[]) => {
      const colorPromises = colors.map(async (color: any) => {
        const res = await getMenuItemColorQuantity(
          color.colorSubMaterialDetail
        );
        if (res && res.length > 0) {
          quantities[
            `color-quantity-${side}-${color.colorSubMaterialDetail.id}`
          ] = res.map((item: any) => (
            <MenuItem
              key={item.id}
              value={item.id}
              onClick={() => {
                handleChangeColorQuantity(`${side}`, item);
              }}
            >
              {item.name}
            </MenuItem>
          ));
        }
      });
      await Promise.all(colorPromises);
    };

    if (canSelectColor === 'both' || canSelectColor === 'front') {
      await fetchColors('front', data.colorFront || []);
    }

    if (canSelectColor === 'both' || canSelectColor === 'back') {
      await fetchColors('back', data.colorBack || []);
    }

    setMenuItems(quantities);
  };

  useEffect(() => {
    fetchColorQuantities();
  }, [canSelectColor, data.colorFront, data.colorBack]);

  const handleChangeColorQuantity = (side: string, color: any) => {
    if (side === 'front') {
      const addColorQuantity = data.colorFront.map((cf: any) => {
        if (cf.colorSubMaterialDetail.id === color.colorSubMaterialDetailId) {
          return {
            ...cf,
            color,
          };
        }
        return cf;
      });
      handleSelect({
        ...data,
        colorFront: addColorQuantity,
      });
    } else if (side === 'back') {
      const addColorQuantity = data.colorBack.map((cb: any) => {
        if (cb.colorSubMaterialDetail.id === color.colorSubMaterialDetailId) {
          return {
            ...cb,
            color,
          };
        }
        return cb;
      });
      handleSelect({
        ...data,
        colorBack: addColorQuantity,
      });
    }
  };
  // console.log('formik', formik.values);
  // console.log('menuItems', menuItems);
  // console.log('data', data);
  return (
    <>
      {canSelectColor !== 'none' && (
        <FadeInStyled>
          {!isEmpty(productPrint.productConfig) && (
            <p className="sub-label">{productPrint.name}</p>
          )}
          {isEmpty(productPrint.productConfig) ? (
            <>
              <p className="sub-label">{productPrint.name}</p>
              <EmptyField
                onClick={async () => {
                  await router.push(
                    `/product/${data.productModel.productId}/attributes`
                  );
                }}
              >
                Add-on attributes
              </EmptyField>
            </>
          ) : (
            <FormControl
              fullWidth
              sx={{
                height: '40px',
              }}
            >
              <Select
                fullWidth
                value={formik.values.printMasterId || ''}
                displayEmpty
                onChange={(event: any) =>
                  handleSelect({
                    ...data,
                    printMasterId: event.target.value,
                    colorFront: [],
                    colorBack: [],
                  })
                }
                sx={{
                  fontSize: '14px',
                }}
              >
                <MenuItem
                  value=""
                  disabled
                  sx={{ fontSize: '14px', minHeight: '40px !important' }}
                >
                  กรุณาเลือก
                </MenuItem>
                {productPrint?.productConfig?.map(
                  (item: any, index: number) => (
                    <MenuItem
                      key={index}
                      value={item.master.id}
                      sx={{
                        fontSize: '14px',
                        minHeight: '40px !important',
                      }}
                    >
                      {item.master.name}
                    </MenuItem>
                  )
                )}
              </Select>
            </FormControl>
          )}
          {formik.touched.printMasterId &&
            Boolean(formik.errors.printMasterId) && (
              <p className="error-text">{formik.errors.printMasterId}</p>
            )}
          {formik.values.printMasterId && (
            <ColorMultipleStyled>
              <>
                {(canSelectColor === 'both' || canSelectColor === 'front') && (
                  <div className="flex-1 color">
                    <p className="sub-label">สีด้านหน้า</p>
                    <FormControl
                      fullWidth
                      sx={{
                        height: '40px',
                      }}
                      data-test-color-side={`front`}
                    >
                      <Select
                        fullWidth
                        multiple
                        value={data.colorFront} // array
                        displayEmpty
                        sx={{
                          fontSize: '14px',
                          fieldset: {
                            borderWidth: '1px !important',
                          },
                        }}
                        renderValue={(selected) => (
                          <div
                            className="flex flex-wrap gap-2"
                            style={{
                              padding: '4px 0',
                            }}
                          >
                            {selected.map((value: any, index: number) => {
                              const color = colorList.find(
                                (c: any) =>
                                  c.id === value.colorSubMaterialDetail.id
                              );
                              return (
                                <ColorChipStyled key={index}>
                                  <div className="color-name">
                                    {color?.name || 'ไม่พบชื่อสี'}
                                  </div>
                                </ColorChipStyled>
                              );
                            })}
                            {isEmpty(selected) && (
                              <ColorChipStyled>
                                <div className="color-name">กรุณาเลือก</div>
                              </ColorChipStyled>
                            )}
                          </div>
                        )}
                      >
                        <MenuItem
                          value=""
                          disabled
                          sx={{
                            fontSize: '14px',
                          }}
                        >
                          กรุณาเลือก
                        </MenuItem>
                        {colorList.map((item: any, index: number) => (
                          <MenuItem
                            key={index}
                            value={item.id}
                            sx={{
                              fontSize: '14px',
                              minHeight: '40px !important',
                              height: '40px !important',
                            }}
                            onClick={() => handleClickColorFront(item)}
                          >
                            <CheckboxWrap>
                              <Checkbox
                                color="primary"
                                checked={data.colorFront
                                  .map(
                                    (item: any) =>
                                      item.colorSubMaterialDetail.id
                                  )
                                  .includes(item.id)}
                                icon={<IconUnCheckbox />}
                                checkedIcon={<IconCheckbox />}
                                style={{ marginLeft: '-8px' }}
                              />
                            </CheckboxWrap>
                            <div className="flex gap-2 items-center">
                              <span>{item.name}</span>
                            </div>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {formik.touched.colorFront &&
                      Boolean(formik.errors.colorFront) && (
                        <p className="error-text !mt-[4px]">
                          กรุณาเลือกสีการพิมพ์ ด้านหน้า
                        </p>
                      )}
                  </div>
                )}
                {(canSelectColor === 'both' || canSelectColor === 'back') && (
                  <div className="flex-1 color">
                    <p className="sub-label">สีด้านหลัง</p>
                    <FormControl
                      fullWidth
                      sx={{
                        height: '40px',
                      }}
                      data-test-color-side={`back`}
                    >
                      <Select
                        fullWidth
                        multiple
                        value={data.colorBack} // array
                        displayEmpty
                        sx={{
                          fontSize: '14px',
                          fieldset: {
                            borderWidth: '1px !important',
                          },
                        }}
                        renderValue={(selected) => (
                          <div
                            className="flex flex-wrap gap-2"
                            style={{
                              padding: '4px 0',
                            }}
                          >
                            {selected.map((value: any, index: number) => {
                              const color = colorList.find(
                                (c: any) =>
                                  c.id === value.colorSubMaterialDetail.id
                              );
                              return (
                                <ColorChipStyled key={index}>
                                  <div className="color-name">
                                    {color?.name || 'ไม่พบชื่อสี'}
                                  </div>
                                </ColorChipStyled>
                              );
                            })}
                            {isEmpty(selected) && (
                              <ColorChipStyled>
                                <div className="color-name">กรุณาเลือก</div>
                              </ColorChipStyled>
                            )}
                          </div>
                        )}
                      >
                        <MenuItem
                          value=""
                          disabled
                          sx={{
                            fontSize: '14px',
                          }}
                        >
                          กรุณาเลือก
                        </MenuItem>
                        {colorList.map((item: any) => (
                          <MenuItem
                            key={item.id}
                            value={item.id}
                            sx={{
                              fontSize: '14px',
                              height: '40px',
                            }}
                            onClick={() => handleClickColorBack(item)}
                          >
                            <CheckboxWrap>
                              <Checkbox
                                color="primary"
                                checked={data.colorBack
                                  .map(
                                    (item: any) =>
                                      item.colorSubMaterialDetail.id
                                  )
                                  .includes(item.id)}
                                icon={<IconUnCheckbox />}
                                checkedIcon={<IconCheckbox />}
                                style={{ marginLeft: '-8px' }}
                              />
                            </CheckboxWrap>
                            <div className="flex gap-2 items-center">
                              <span>{item.name}</span>
                            </div>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    {formik.touched.colorBack &&
                      Boolean(formik.errors.colorBack) && (
                        <p className="error-text !mt-[4px]">
                          กรุณาเลือกสีการพิมพ์ ด้านหลัง
                        </p>
                      )}
                  </div>
                )}
              </>
            </ColorMultipleStyled>
          )}
          {formik.values.printMasterId && (
            <ColorMultipleStyled>
              {/* ด้านหน้า */}
              {(canSelectColor === 'both' || canSelectColor === 'front') &&
                formik.values.colorFront?.map((cf: any, index: number) => (
                  <div className="flex-1" key={`front-${index}`}>
                    <p className="sub-label">
                      จำนวนสีด้านหน้า ({cf.colorSubMaterialDetail.name})
                    </p>
                    <FormControl
                      fullWidth
                      size="small"
                      error={isSubmitted && cf.color === undefined}
                      data-test-color-quantity-side={`front-${cf.colorSubMaterialDetail.name}`}
                    >
                      <Select value={cf.color?.id || ''} displayEmpty>
                        <MenuItem disabled value="">
                          <div className="text-[#78909C]">
                            {'Select Quantity'}
                          </div>
                        </MenuItem>
                        {menuItems[
                          `color-quantity-front-${cf.colorSubMaterialDetail.id}`
                        ]?.map((item: any) => item) || (
                          <MenuItem disabled>No Options</MenuItem>
                        )}
                      </Select>
                    </FormControl>
                    {isSubmitted && cf.color === undefined && (
                      <p className="error-text !mt-[4px]">กรุณาเลือกจำนวนสี</p>
                    )}
                  </div>
                ))}

              {/* ด้านหลัง */}
              {(canSelectColor === 'both' || canSelectColor === 'back') &&
                data.colorBack?.map((cb: any, index: number) => (
                  <div className="flex-1 " key={`back-${index}`}>
                    <p className="sub-label">
                      จำนวนสีด้านหลัง ({cb.colorSubMaterialDetail.name})
                    </p>
                    <FormControl
                      fullWidth
                      size="small"
                      error={isSubmitted && cb.color === undefined}
                      data-test-color-quantity-side={`back-${cb.colorSubMaterialDetail.name}`}
                    >
                      <Select value={cb.color?.id || ''} displayEmpty>
                        <MenuItem disabled value="">
                          <div className="text-[#78909C]">
                            {'Select Quantity'}
                          </div>
                        </MenuItem>
                        {menuItems[
                          `color-quantity-back-${cb.colorSubMaterialDetail.id}`
                        ]?.map((item: any) => item) || (
                          <MenuItem disabled>No Options</MenuItem>
                        )}
                      </Select>
                    </FormControl>
                    {isSubmitted && cb.color === undefined && (
                      <p className="error-text !mt-[4px]">กรุณาเลือกจำนวนสี</p>
                    )}
                  </div>
                ))}
            </ColorMultipleStyled>
          )}
        </FadeInStyled>
      )}
    </>
  );
};

export default ColorSelector;
