import styled from 'styled-components';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  Di<PERSON>,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ChangeEvent, useEffect, useState } from 'react';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import { Search } from '@mui/icons-material';
import { useDebounce } from 'usehooks-ts';
import { isEmpty } from 'lodash';
import { useFormik } from 'formik';
import * as yup from 'yup';

const ExtraSelectorStyle = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  width: 100%;
  .add-card {
    width: 100%;
    height: 80px;
    border: 1px solid #dbe2e5;
    border-radius: 12px;
    padding: 0 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    column-gap: 16px;
    transition: 0.3s ease-out;

    .btn-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48px;
      width: 48px;
      button {
        height: 40px;
        width: 40px;
        min-width: 40px;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .select-extra-item {
    width: 100%;
    height: 80px;
    border: 1px solid #30d5c7;
    border-radius: 12px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    column-gap: 16px;
    justify-content: space-between;
    transition: 0.3s ease-out;
    background-color: #f2fefe;
    .extra-info {
      display: flex;
      align-items: center;
      column-gap: 16px;
      .image {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .name {
        font-weight: 600;
      }
    }
    .action {
      display: flex;
      align-items: center;
      column-gap: 4px;
    }
  }
`;

const ModalExtraStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  align-items: center;
  .item-wrap {
    width: 100%;
    overflow: auto;
    padding: 8px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      justify-content: space-between;
      &:hover {
        background: #f5f7f8;
      }
      .info {
        display: flex;
        align-items: center;
        column-gap: 16px;
        .name {
          font-weight: 400;
        }
      }
    }
  }
  .extra-step-2 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 24px;
    animation: ${LoadingFadein} 0.3s ease-in;
    .input-wrap {
      width: 100%;
      display: flex;
      flex-direction: column;
      column-gap: 24px;
      padding: 0 1px;
      margin-top: 24px;
      animation: ${LoadingFadein} 0.3s ease-in;
      .label {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }
    }
  }
`;

type ExtraSelectorProps = {
  productExtra: any;
  handleReloadExtra: (keyword: string) => void;
  makeNewExtra: (newExtraValue: any) => void;
  selectedExtraList: any;
  handleRemoveExtra: (index: number) => void;
  makeUpdateExtra: (newExtraValue: any, index: number) => void;
};

const ExtraSelector = ({
  productExtra,
  handleReloadExtra,
  makeNewExtra,
  selectedExtraList,
  handleRemoveExtra,
  makeUpdateExtra,
}: ExtraSelectorProps) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [modalStatus, setModalStatus] = useState<'add' | 'edit'>('add');
  const [editIndex, setEditIndex] = useState<number>(0);
  const [step, setStep] = useState(1);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedSearch = useDebounce<string>(keyword, 600);
  const [selectedExtra, setSelectedExtra] = useState<any>({});
  const [
    isRequiredBlogSubMaterialDetailId,
    setIsRequiredBlogSubMaterialDetailId,
  ] = useState<boolean>(false);
  const validationSchema = yup.object({
    width: yup.number().required('กรุณากรอกความกว้าง'),
    height: yup.number().required('กรุณากรอกความสูง'),
    amount: yup.number().required('กรุณากรอกจำนวน'),
    blogSubMaterialDetailId: isRequiredBlogSubMaterialDetailId
      ? yup.number().required('กรุณาเลือกบล็อกพิมพ์')
      : yup.mixed().notRequired(),
    side: yup.number().required('กรุณาเลือกด้าน'),
  });

  const formik = useFormik({
    initialValues: {
      width: '',
      height: '',
      amount: '',
      blogSubMaterialDetailId: '',
      side: 1,
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      const newExtraValue = {
        extraMasterId: selectedExtra.extraMasterId,
        width: values.width,
        height: values.height,
        amount: values.amount,
        blogSubMaterialDetailId: values.blogSubMaterialDetailId,
        side: values.side,
      };
      if (modalStatus === 'add') {
        makeNewExtra(newExtraValue);
      } else {
        makeUpdateExtra(newExtraValue, editIndex);
      }
      handleClose();
    },
  });

  const handleOpen = () => {
    setModalStatus('add');
    setSearchInput('');
    setKeyword('');
    setStep(1);
    setOpenModal(true);
    formik.resetForm();
  };

  const handleClose = () => {
    setOpenModal(false);
  };

  useEffect(() => {
    handleReloadExtra(keyword);
    setLoadingSearch(false);
  }, [debouncedSearch]);

  const handleSearch = (text: any) => {
    setLoadingSearch(true);
    setSearchInput(text);
    setKeyword(text);
  };

  const handleSelectExtra = (item: any) => {
    setSelectedExtra({
      ...item,
      extraMasterId: item.master?.id || '',
    });
    setStep(2);
  };

  const handleEdit = (item: any, index: number) => {
    setEditIndex(index);
    setModalStatus('edit');
    setStep(2);
    setSelectedExtra(item);
    setOpenModal(true);
  };

  useEffect(() => {
    if (modalStatus === 'edit') {
      formik.setValues({
        width: selectedExtra.width || '',
        height: selectedExtra.height || '',
        amount: selectedExtra.amount || '',
        blogSubMaterialDetailId: selectedExtra.blogSubMaterialDetailId || '',
        side: selectedExtra.side || 1,
      });
    }
  }, [modalStatus, selectedExtra]);

  useEffect(() => {
    if (step === 2) {
      const checkBlock = productExtra.productConfig.find(
        (item: any) => item.master.id === selectedExtra.extraMasterId
      )?.configMaterial;
      if (checkBlock) {
        setIsRequiredBlogSubMaterialDetailId(true);
      } else {
        setIsRequiredBlogSubMaterialDetailId(false);
      }
    } else {
      setIsRequiredBlogSubMaterialDetailId(false);
    }
  }, [step]);

  return (
    <>
      <ExtraSelectorStyle>
        {!isEmpty(selectedExtraList) &&
          selectedExtraList.map((item: any, index: number) => {
            const extraMaster = productExtra.productConfig.find(
              (config: any) => config.master.id === item.extraMasterId
            );

            const blogSubMaterialDetail =
              extraMaster?.configMaterial.find(
                (config: any) =>
                  config.subMaterialDetail.id === item.blogSubMaterialDetailId
              )?.subMaterialDetail.name || 'Unknown';

            const sideName = item.side === 1 ? 'ด้านหน้า' : 'ด้านหลัง';

            return (
              <div key={index} className="select-extra-item">
                <div className="extra-info">
                  <Badge badgeContent={item.amount} color="primary">
                    <div className="image">
                      <Image
                        src={
                          item.imageUrl || '/images/product/empty-product.svg'
                        }
                        width={48}
                        height={48}
                        alt=""
                        style={{
                          borderRadius: '8px',
                        }}
                      />
                    </div>
                  </Badge>
                  <div className="name">
                    {`${
                      extraMaster?.master.name || 'Unknown'
                    } | ${blogSubMaterialDetail} | ขนาด ${item.width}x${
                      item.height
                    } | ${sideName}`}
                  </div>
                </div>
                <div className="action">
                  <IconButton onClick={() => handleEdit(item, index)}>
                    <Image
                      src={'/icons/edit.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  </IconButton>
                  <IconButton onClick={() => handleRemoveExtra(index)}>
                    <Image
                      src={'/icons/delete.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  </IconButton>
                </div>
              </div>
            );
          })}

        <div className="add-card" onClick={handleOpen}>
          <div className="btn-wrap">
            <Button variant="contained" color="dark" size="small">
              <AddRoundedIcon />
            </Button>
          </div>
          <div>เพิ่มเทคนิคพิเศษ</div>
        </div>
      </ExtraSelectorStyle>

      <Dialog open={openModal} onClose={handleClose}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                {step !== 1 && (
                  <div className="back" onClick={() => setStep(step - 1)}>
                    <IconButton sx={{ color: '#263238' }}>
                      <KeyboardBackspaceRoundedIcon />
                    </IconButton>
                  </div>
                )}
                <div className="title">
                  {step === 1
                    ? 'เทคนิคพิเศษ'
                    : productExtra.productConfig.find(
                        (pc: any) =>
                          pc.master.id === selectedExtra.extraMasterId
                      ).master.name || 'Unknown'}
                </div>
                <div className="x-close" onClick={handleClose}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>

              <div className="form-wrap">
                <form onSubmit={formik.handleSubmit}>
                  {step === 1 && (
                    <TextField
                      className="fade-in"
                      fullWidth
                      value={searchInput}
                      onChange={(e: ChangeEvent<HTMLInputElement>) =>
                        handleSearch(e.target.value)
                      }
                      placeholder="ค้นหา"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            {loadingSearch ? (
                              <CircularProgress size={20} />
                            ) : (
                              <Search />
                            )}
                          </InputAdornment>
                        ),
                      }}
                      sx={{ marginTop: '24px' }}
                    />
                  )}

                  {step === 1 && (
                    <ModalExtraStyled>
                      <div className="item-wrap">
                        {productExtra.productConfig.map((configItem: any) => (
                          <div
                            key={configItem.id}
                            className="item"
                            onClick={() => handleSelectExtra(configItem)}
                          >
                            <div className="info">
                              <Image
                                src={
                                  configItem.master.imageUrl ||
                                  '/images/product/empty-product.svg'
                                }
                                width={48}
                                height={48}
                                alt=""
                                style={{ borderRadius: '8px' }}
                              />
                              <div className="name">
                                {configItem.master.name}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ModalExtraStyled>
                  )}

                  {step === 2 && !isEmpty(productExtra) && (
                    <ModalExtraStyled>
                      <div className="extra-step-2">
                        <Image
                          src={
                            productExtra.master?.imageUrl ||
                            '/images/product/empty-product.svg'
                          }
                          width={140}
                          height={140}
                          alt=""
                          style={{
                            borderRadius: '16px',
                          }}
                        />
                        <div className="input-wrap">
                          {isRequiredBlogSubMaterialDetailId && (
                            <div className="w-full">
                              <p className="label">บล็อกพิมพ์</p>
                              <FormControl
                                fullWidth
                                error={
                                  formik.touched.blogSubMaterialDetailId &&
                                  Boolean(formik.errors.blogSubMaterialDetailId)
                                }
                                sx={{ minHeight: '40px' }}
                              >
                                <Select
                                  name="blogSubMaterialDetailId"
                                  value={
                                    formik.values.blogSubMaterialDetailId || ''
                                  }
                                  onChange={formik.handleChange}
                                  displayEmpty
                                  sx={{ fontSize: '14px' }}
                                >
                                  <MenuItem
                                    value=""
                                    disabled
                                    sx={{
                                      minHeight: '40px !important',
                                      fontSize: '14px',
                                    }}
                                  >
                                    เลือกบล็อกพิมพ์
                                  </MenuItem>
                                  {productExtra.productConfig
                                    .find(
                                      (item: any) =>
                                        item.master.id ===
                                        selectedExtra.extraMasterId
                                    )
                                    ?.configMaterial?.map((configItem: any) => (
                                      <MenuItem
                                        key={configItem.subMaterialDetail.id}
                                        value={configItem.subMaterialDetail.id}
                                        sx={{ fontSize: '14px' }}
                                      >
                                        {configItem.subMaterialDetail.name}
                                      </MenuItem>
                                    ))}
                                </Select>
                                {formik.touched.blogSubMaterialDetailId &&
                                  formik.errors.blogSubMaterialDetailId && (
                                    <FormHelperText>
                                      {formik.errors.blogSubMaterialDetailId}
                                    </FormHelperText>
                                  )}
                              </FormControl>
                            </div>
                          )}

                          <div className="input-wrap w-full">
                            <div className="w-full">
                              <p className="label">จำนวน</p>
                              <TextField
                                type="number"
                                fullWidth
                                name="amount"
                                placeholder="กรอกจำนวน"
                                value={formik.values.amount}
                                onChange={formik.handleChange}
                                error={
                                  formik.touched.amount &&
                                  Boolean(formik.errors.amount)
                                }
                                helperText={
                                  formik.touched.amount && formik.errors.amount
                                }
                                InputProps={{
                                  endAdornment: (
                                    <div className="p-[2px]">จำนวน</div>
                                  ),
                                }}
                              />
                            </div>
                          </div>

                          <div className="input-wrap w-full">
                            <p className="label">ขนาด</p>
                            <div
                              className="w-full flex"
                              style={{
                                columnGap: '24px',
                              }}
                            >
                              <div className="w-full ">
                                <TextField
                                  type="number"
                                  fullWidth
                                  name="width"
                                  placeholder="กว้าง"
                                  value={formik.values.width}
                                  onChange={formik.handleChange}
                                  error={
                                    formik.touched.width &&
                                    Boolean(formik.errors.width)
                                  }
                                  helperText={
                                    formik.touched.width && formik.errors.width
                                  }
                                />
                              </div>
                              <div className="w-full ">
                                <TextField
                                  type="number"
                                  fullWidth
                                  name="height"
                                  placeholder="สูง"
                                  value={formik.values.height}
                                  onChange={formik.handleChange}
                                  error={
                                    formik.touched.height &&
                                    Boolean(formik.errors.height)
                                  }
                                  helperText={
                                    formik.touched.height &&
                                    formik.errors.height
                                  }
                                />
                              </div>
                            </div>
                          </div>
                          <div className="input-wrap w-full">
                            <div className="w-full">
                              <p className="label">ด้าน</p>
                              <FormControl fullWidth>
                                <Select
                                  name="side"
                                  value={formik.values.side || ''}
                                  onChange={formik.handleChange}
                                  displayEmpty
                                  sx={{ fontSize: '14px' }}
                                >
                                  <MenuItem value={1}>ด้านหน้า</MenuItem>
                                  <MenuItem value={2}>ด้านหลัง</MenuItem>
                                </Select>
                              </FormControl>
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button
                        type="submit"
                        variant="contained"
                        color="dark"
                        fullWidth
                        disabled={
                          formik.values.width === '' ||
                          formik.values.height === '' ||
                          formik.values.amount === ''
                        }
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                          margin: '34px 0 0',
                        }}
                        onClick={() => {}}
                      >
                        {modalStatus === 'add' ? 'เพิ่ม' : 'ยืนยัน'}
                      </Button>
                    </ModalExtraStyled>
                  )}
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ExtraSelector;
