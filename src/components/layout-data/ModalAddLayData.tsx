import {
  <PERSON>ton,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import TuneRoundedIcon from '@mui/icons-material/TuneRounded';
import Image from 'next/image';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import apiProduct from '@/services/stock/product';
import apiProductModel from '@/services/stock/productModel';
import apiProductSet from '@/services/stock/product-set';
import { ContactTypeSelect } from '@/components/company/contact/ContactForm';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import apiLay from '@/services/order/lay';
import { orderSelector } from '@/store/features/order';

const ModalAddLayDataContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;

  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;

    .product-grid {
      display: grid;
      grid-gap: 16px;
      position: relative;
      min-height: 172px;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      &.model {
        overflow: auto;
        .product-wrap {
          &:last-child {
            padding-bottom: 0;
          }
        }
      }
      .product-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 8px;
        animation: ${LoadingFadein} 0.3s ease-in;
        &:last-child {
          padding-bottom: 16px;
        }
        .product-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #f5f7f9;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          transition: 0.3s ease-out;
          position: relative;
          &:before {
            transition: 0.3s ease-out;
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            background-color: transparent;
          }
          &:hover {
            &:before {
              background-color: rgba(139, 217, 255, 0.2);
            }
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
        .product-type-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #aadaff;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          transition: 0.3s ease-out;
          position: relative;
          &:before {
            transition: 0.3s ease-out;
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            z-index: 1;
            box-shadow: transparent 0px 0px 0px 2px inset;
          }
          &:hover {
            &:before {
              box-shadow: #ddd 0px 0px 0px 2px inset;
            }
          }
          &.active {
            &:before {
              box-shadow: #263238 0px 0px 0px 2px inset;
            }
          }
          .check-box-wrap {
            position: absolute;
            top: 0;
            right: 0;
            .MuiButtonBase-root {
              &:hover {
                background: none !important;
              }
            }
            .MuiTouchRipple-root {
              display: none !important;
            }
          }
        }
        .name {
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }
      }
    }
    .filters-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      row-gap: 8px;
      column-gap: 24px;
      padding: 0 1px;
      .label {
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        align-items: baseline;
        .clear {
          font-size: 12px;
          font-weight: 400;
          text-decoration: underline;
          cursor: pointer;
          color: #cfd8dc;
        }
      }
    }
    .product-type-name {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: -6px;
      line-height: 1;
    }
    .product-set-info {
      width: 100%;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      padding: 8px;
      min-height: 96px;
      display: flex;
      align-items: center;
      column-gap: 20px;
      .image {
        width: 80px;
        height: 80px;
        min-width: 80px;
        aspect-ratio: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f6f7f9;
        overflow: hidden;
        border-radius: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .text-group {
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        overflow: hidden;
        .name {
          font-size: 20px;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
        .description {
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
      }
    }
  }
`;

type ModalAddLayDataProps = {
  open: boolean;
  makeCloseModal: () => void;
  handleReloadOrder: () => void;
};

const initialFiltersProduct = {
  // productCategoryId: 1,
  // productTypeId: 1,
  searchName: '',
  page: 0,
  size: 100,
};
const ModalAddLayData = ({
  open,
  makeCloseModal,
  handleReloadOrder,
}: ModalAddLayDataProps) => {
  const dispatch = useAppDispatch();
  const { orderById } = useAppSelector(orderSelector);
  const [searchInputProduct, setSearchInputProduct] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingAddLayData, setLoadingAddLayData] = useState(false);
  const [productType, setProductType] = useState<number>(1);
  const [step, setStep] = useState<number>(1);
  const [openFilters, setOpenFilters] = useState<boolean>(false);
  const [timer, setTimer] = useState<any>(null);
  const [selectProductModel, setSelectProductModel] = useState<number | null>();
  const [filterProduct, setFilterProduct] = useState<any>(
    initialFiltersProduct
  );
  const [productList, setProductList] = useState<any>({});
  const [selectedProduct, setSelectedProduct] = useState<any>({});
  const [productModelList, setProductModelList] = useState<any>({});
  const [productCategory, setProductCategory] = useState<any>({});
  const [productTypeByCategory, setProductTypeByCategory] = useState<any>({});
  const [productSetList, setProductSetList] = useState<any>([]);
  const [selectedProductSet, setSelectedProductSet] = useState<any>({});
  const [productSetModelList, setProductSetModelList] = useState<any>({});
  const getProductList = async () => {
    const res = await apiProduct.getProductListOrder(filterProduct);
    if (!res.isError) {
      setProductList(res.data);
    }
  };
  const getProductSet = async () => {
    const res = await apiProductSet.getProductSetListOrder(filterProduct);
    if (!res.isError) {
      setProductSetList(res.data);
    }
  };

  useEffect(() => {
    getProductList();
    getProductSet();
  }, [filterProduct]);
  const handleSearch = (event: any) => {
    setSearchInputProduct(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);

    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilterProduct({
        ...filterProduct,
        search: event.target.value,
        searchName: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };

  const handleClearProductFilter = () => {
    const clearFilterProduct = {
      page: filterProduct.page,
      searchName: filterProduct.searchName,
      size: filterProduct.size,
    };
    setFilterProduct(clearFilterProduct);
  };
  const handleSelectProduct = async (item: any) => {
    const res = await apiProductModel.getList(item.id as string);
    if (!res.isError) {
      setSelectedProduct(item);
      setProductModelList(res.data);
      setStep(2);
    }
  };
  const handleSelectProductSet = async (item: any) => {
    const res = await apiProductSet.getProductBySetId(item.id as string);
    if (!res.isError) {
      setSelectedProductSet(item);
      setProductSetModelList(res.data.productModel);
      setStep(2);
    }
  };

  const handleClose = () => {
    makeCloseModal();
  };
  useEffect(() => {
    if (open) {
      setFilterProduct(initialFiltersProduct);
      setSearchInputProduct('');
      setStep(1);
      setProductType(1);
      setOpenFilters(false);
      setSelectProductModel(null);
    }
  }, [open]);
  const getProductCategory = async () => {
    const res = await apiProduct.getCategory({
      size: 100,
      page: 0,
      searchTerm: '',
    });
    if (!res.isError) {
      setProductCategory(res.data.content);
    }
  };
  useEffect(() => {
    getProductCategory();
  }, []);
  const handleChangeCategory = async (value: any) => {
    const res = await apiProduct.getTypeByCategoryId(value);
    if (!res.isError) {
      setProductTypeByCategory(res.data);
      setFilterProduct({
        ...filterProduct,
        productCategoryId: value,
        productTypeId: null,
      });
    }
  };
  const handleChangeAddLayData = async () => {
    setLoadingAddLayData(true);
    const sendData = {
      layDataOrdersId: orderById.id,
      productType: productType === 1 ? 0 : 1,
      productModelId:
        productType === 1 ? [selectProductModel] : [selectedProductSet.id],
    };
    const res = await apiLay.addLayData(sendData);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      makeCloseModal();
      handleReloadOrder();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoadingAddLayData(false);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                {step !== 1 && (
                  <div
                    className="back"
                    onClick={() => {
                      setStep(step - 1);
                      setSelectProductModel(null);
                    }}
                  >
                    <IconButton
                      sx={{
                        color: '#263238',
                      }}
                    >
                      <KeyboardBackspaceRoundedIcon />
                    </IconButton>
                  </div>
                )}
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  เพิ่มสินค้า
                </div>
                <div className="x-close" onClick={() => makeCloseModal()}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <ModalAddLayDataContentStyled>
                    {step === 1 && (
                      <div className="content fade-in">
                        <ContactTypeSelect
                          style={{
                            marginTop: '24px',
                          }}
                        >
                          <div
                            className={`btn-type ${
                              productType === 1 && 'active'
                            }`}
                            onClick={() => {
                              setProductType(1);
                            }}
                          >
                            สินค้า
                          </div>
                          <div
                            className={`btn-type ${
                              productType === 2 && 'active'
                            }`}
                            onClick={() => {
                              setProductType(2);
                            }}
                          >
                            เซ็ตสินค้า
                          </div>
                        </ContactTypeSelect>
                        <TextField
                          fullWidth
                          value={searchInputProduct}
                          onChange={(event: any) => {
                            handleSearch(event);
                          }}
                          placeholder="ค้นหา"
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                {loadingSearch ? (
                                  <div className="h-[24px] w-[24px] flex items-center justify-center">
                                    <CircularProgress size={20} />
                                  </div>
                                ) : (
                                  <Search />
                                )}
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment
                                className="cursor-pointer"
                                position="end"
                                onClick={() => {
                                  setOpenFilters(!openFilters);
                                }}
                              >
                                <TuneRoundedIcon />
                              </InputAdornment>
                            ),
                          }}
                          sx={{
                            padding: '0 1px',
                            '.MuiInputBase-root': {
                              borderRadius: '20px !important',
                            },
                          }}
                        />
                        {openFilters && (
                          <div className="filters-wrap">
                            <div className="w-full">
                              <div className="label">
                                หมวดหมู่{' '}
                                <div
                                  className="clear"
                                  onClick={handleClearProductFilter}
                                >
                                  ล้าง
                                </div>
                              </div>
                              <FormControl fullWidth size="small">
                                <Select
                                  name="categoty"
                                  value={filterProduct.productCategoryId || ''}
                                  onChange={(e: any) => {
                                    handleChangeCategory(e.target.value);
                                  }}
                                  displayEmpty
                                  sx={{
                                    fontSize: '14px',
                                    height: '40px',
                                  }}
                                >
                                  <MenuItem
                                    disabled
                                    value=""
                                    sx={{
                                      fontSize: '14px',
                                    }}
                                  >
                                    <div className="text-[#78909C]">เลือก</div>
                                  </MenuItem>
                                  {productCategory.map(
                                    (item: any, index: React.Key) => (
                                      <MenuItem
                                        key={index}
                                        value={item.id}
                                        sx={{
                                          fontSize: '14px',
                                        }}
                                      >
                                        {item.name}
                                      </MenuItem>
                                    )
                                  )}
                                </Select>
                              </FormControl>
                            </div>
                            <div className="w-full relative">
                              <div className="label">ประเภท</div>
                              {!filterProduct.productCategoryId && (
                                <div
                                  style={{
                                    position: 'absolute',
                                    width: '100%',
                                    height: '100%',
                                    zIndex: '9',
                                  }}
                                />
                              )}
                              <FormControl fullWidth size="small">
                                <Select
                                  name="type"
                                  value={filterProduct.productTypeId || ''}
                                  onChange={(e: any) => {
                                    setFilterProduct({
                                      ...filterProduct,
                                      productTypeId: e.target.value,
                                    });
                                  }}
                                  displayEmpty
                                  sx={{
                                    fontSize: '14px',
                                    height: '40px',
                                  }}
                                >
                                  <MenuItem
                                    disabled
                                    value=""
                                    sx={{
                                      fontSize: '14px',
                                    }}
                                  >
                                    <div className="text-[#78909C]">เลือก</div>
                                  </MenuItem>
                                  {!isEmpty(productTypeByCategory) &&
                                    productTypeByCategory.map(
                                      (item: any, index: React.Key) => (
                                        <MenuItem
                                          key={index}
                                          value={item.id}
                                          sx={{
                                            fontSize: '14px',
                                          }}
                                        >
                                          {item.name}
                                        </MenuItem>
                                      )
                                    )}
                                </Select>
                              </FormControl>
                            </div>
                          </div>
                        )}
                        <div className="product-grid">
                          {!isEmpty(productList) &&
                            productType === 1 &&
                            productList.map((item: any, index: number) => (
                              <div className="product-wrap" key={index}>
                                <div
                                  className="product-card"
                                  onClick={async () => {
                                    await handleSelectProduct(item);
                                  }}
                                >
                                  <Image
                                    src={
                                      item.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    width={124}
                                    height={124}
                                    alt=""
                                  />
                                </div>
                                <div className="name">{item.name}</div>
                              </div>
                            ))}
                          {!isEmpty(productSetList) &&
                            productType === 2 &&
                            productSetList.map((item: any, index: number) => (
                              <div className="product-wrap" key={index}>
                                <div
                                  className="product-card"
                                  onClick={async () => {
                                    await handleSelectProductSet(item);
                                  }}
                                >
                                  <Image
                                    src={
                                      item.thumbnail ||
                                      item.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    width={124}
                                    height={124}
                                    alt=""
                                  />
                                </div>
                                <div className="name">{item.name}</div>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}
                    {step === 2 && (
                      <>
                        <div className="content fade-in">
                          {productType === 1 && (
                            <div className="mt-[24px] product-type-name">
                              {selectedProduct.name}
                            </div>
                          )}
                          {productType === 2 && (
                            <div className="mt-[24px] product-set-info">
                              <div className="image">
                                <Image
                                  src={
                                    `${selectedProductSet.thumbnail}` ||
                                    `${selectedProductSet.imageUrl}` ||
                                    '/images/Mailer_Box.png'
                                  }
                                  width={120}
                                  height={120}
                                  alt=""
                                />
                              </div>
                              <div className="text-group">
                                <div className="name">
                                  {selectedProductSet.name}
                                </div>
                                <div className="description">
                                  มีทั้งหมด {productSetModelList.length}{' '}
                                  โมเดลให้เลือกจัดเซ็ทสินค้า
                                </div>
                              </div>
                            </div>
                          )}
                          {!isEmpty(productModelList) && productType === 1 && (
                            <div className="product-grid model">
                              {productModelList.map((item: any) => {
                                return (
                                  <div className="product-wrap" key={item.id}>
                                    <div
                                      className={`product-type-card ${
                                        selectProductModel === item.id
                                          ? 'active'
                                          : ''
                                      }`}
                                      onClick={() => {
                                        if (selectProductModel === item.id) {
                                          setSelectProductModel(null);
                                        } else {
                                          setSelectProductModel(item.id);
                                        }
                                      }}
                                    >
                                      <div className="check-box-wrap">
                                        <Checkbox
                                          color="primary"
                                          checked={
                                            selectProductModel === item.id
                                          }
                                          onChange={(_event: any) => {
                                            // handleChangeDiscount(event);
                                          }}
                                          icon={<IconUnCheckbox />}
                                          checkedIcon={<IconCheckboxBlack />}
                                        />
                                      </div>
                                      <Image
                                        src={
                                          item.imageUrl ||
                                          '/images/product/empty-product.svg'
                                        }
                                        width={122}
                                        height={122}
                                        alt=""
                                      />
                                    </div>
                                    <div className="name">{item.name}</div>
                                  </div>
                                );
                              })}
                            </div>
                          )}
                          {!isEmpty(productSetModelList) &&
                            productType === 2 && (
                              <div className="product-grid model">
                                {productSetModelList.map((item: any) => {
                                  return (
                                    <div className="product-wrap" key={item.id}>
                                      <div className={`product-type-card`}>
                                        <Image
                                          src={
                                            item.imageUrl ||
                                            '/images/product/empty-product.svg'
                                          }
                                          width={122}
                                          height={122}
                                          alt=""
                                        />
                                      </div>
                                      <div className="name">{item.name}</div>
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          <Button
                            type="button"
                            variant="contained"
                            color="dark"
                            fullWidth
                            disabled={loadingAddLayData}
                            sx={{
                              fontSize: '16px',
                              maxHeight: '40px',
                            }}
                            onClick={async () => {
                              await handleChangeAddLayData();
                            }}
                          >
                            {loadingAddLayData ? (
                              <CircularProgress size={20} />
                            ) : (
                              <div>เพิ่มสินค้า</div>
                            )}
                          </Button>
                        </div>
                      </>
                    )}
                  </ModalAddLayDataContentStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalAddLayData;
