// import styled from 'styled-components';
//
// const StatusTabStyle = styled.div`
//   display: flex;
//   flex-direction: row;
//   border-bottom: 1px solid #dbe2e5;
//   > div {
//     padding: 10px 15px;
//     border-bottom: 2px solid #fff;
//     cursor: pointer;
//     white-space: nowrap;
//     &.active {
//       border-color: #222222;
//     }
//   }
// `;
//
// type StatusTabProps = {
//   activeStatus: string;
//   handleClick: (orderStatus: string) => void;
// };
// const LayoutDataStatusTab = ({ activeStatus, handleClick }: StatusTabProps) => {
//   return (
//     <div className="overflow-y-auto w-full">
//       {/* <StatusTabStyle> */}
//       {/*  {orderDataStatus.map((item: any, index: number) => ( */}
//       {/*    <div */}
//       {/*      key={index} */}
//       {/*      className={activeStatus === item.orderStatus ? 'active' : ''} */}
//       {/*      onClick={() => handleClick(item.orderStatus)} */}
//       {/*    > */}
//       {/*      {item.name}{' '} */}
//       {/*      <Chip */}
//       {/*        label="10" */}
//       {/*        size="small" */}
//       {/*        style={{ background: '#263238', color: 'white' }} */}
//       {/*      /> */}
//       {/*    </div> */}
//       {/*  ))} */}
//       {/* </StatusTabStyle> */}
//     </div>
//   );
// };
//
// export default LayoutDataStatusTab;
