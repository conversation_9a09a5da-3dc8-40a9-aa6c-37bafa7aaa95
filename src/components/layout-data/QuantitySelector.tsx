import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiProductQuantity from '@/services/stock/productQuantity';
import { Button, Dialog, DialogActions, TextField } from '@mui/material';
import { Trash2 } from 'react-feather';
import Swal from 'sweetalert2';
import { isEmpty } from 'lodash';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 15px;
  .data-item {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    cursor: pointer;
    transition: 0.3s;
    justify-content: center;
    position: relative;
    .quantity-text {
      width: 100%;
      display: flex;
      justify-content: center;
      height: 100%;
      padding: 10px 15px;
      z-index: 1;
    }
    .del-btn {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 5;
      color: #ccc;
      &:hover {
        color: #e81621;
      }
    }
    p {
      margin: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow-wrap: break-word;
    }
    &.active {
      border: 1px solid #30d5c7;
    }
  }
`;

type QuantitySelectorProps = {
  productId: number;
  selectedIds: number[];
  handleSelect: (ids: number[]) => void;
};
const QuantitySelector = ({
  productId,
  selectedIds,
  handleSelect,
}: QuantitySelectorProps) => {
  const [rows, setRows] = useState<any>([]);
  const [openDialogQuantity, setOpenDialogQuantity] = useState(false);
  const [newQuantity, setNewQuantity] = useState('');
  const [showDuplicate, setShowDuplicate] = useState(false);
  const [requiredQuantity, setRequiredQuantity] = useState<boolean>(false);
  useEffect(() => {
    getRows();
  }, [productId]);
  const getRows = async () => {
    const res = await apiProductQuantity.getList(`${productId}`);
    if (res && !res.isError) {
      setRows(res.data);
      merge(res.data);
    }
  };

  function merge(rows: any) {
    const mergedResult = [];
    for (const row of rows) {
      const isCustom = selectedIds.includes(row.quantity);
      mergedResult.push({
        quantity: row.quantity,
        custom: isCustom,
      });
    }

    for (const id of selectedIds) {
      const isNotInMergedResult = !mergedResult.some(
        (row) => row.quantity === id
      );
      if (isNotInMergedResult) {
        mergedResult.push({
          quantity: id,
          isCustom: true,
        });
      }
    }

    setRows(mergedResult);
  }

  const updateIds = (checkId: number) => {
    const isInList = selectedIds.includes(checkId);
    if (isInList) {
      handleSelect(selectedIds.filter((id) => id !== checkId));
    } else if (selectedIds.length < 3) {
      handleSelect([...selectedIds, checkId]);
    }
  };

  const addNewQuantity = () => {
    if (!isEmpty(newQuantity)) {
      const isDuplicateQuantity = rows.find(
        (item: any) => item.quantity === Number(newQuantity)
      );
      if (!isDuplicateQuantity) {
        setRows([
          ...rows,
          {
            quantity: Number(newQuantity),
            isCustom: true,
          },
        ]);
        setOpenDialogQuantity(false);
        setNewQuantity('');
        setShowDuplicate(false);
      } else {
        setShowDuplicate(true);
      }
    } else {
      setRequiredQuantity(true);
    }
  };

  const confirmDelete = (quantity: number) => {
    Swal.fire({
      title: 'ลบจำนวน',
      text: 'ยืนยันการลบจำนวน ใช่หรือไม่',
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then((result: any) => {
      if (result.isConfirmed) {
        setRows(rows.filter((item: any) => item.quantity !== quantity));
        handleSelect(
          selectedIds.filter((idSelected) => idSelected !== quantity)
        );
      }
    });
  };

  return (
    <>
      <h3>จำนวน ({selectedIds.length}/3)</h3>
      <SelectorListStyle>
        {rows.map((item: any, index: number) => (
          <div
            key={index}
            className={`data-item ${
              selectedIds.includes(item.quantity) && 'active'
            }`}
          >
            <div
              className="quantity-text"
              onClick={() => updateIds(item.quantity)}
            >
              <p>{item.quantity}</p>
            </div>
            {item.isCustom && (
              <Trash2
                className="del-btn w-[20px]"
                onClick={() => confirmDelete(item.quantity)}
              />
            )}
          </div>
        ))}
        <div className="data-item" onClick={() => setOpenDialogQuantity(true)}>
          <div className="quantity-text">
            <p>กำหนดเอง</p>
          </div>
        </div>
        <Dialog
          open={openDialogQuantity}
          onClose={() => setOpenDialogQuantity(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <div className="w-[400px] px-6 mb-4">
            <p>เพิ่มจำนวน</p>
            <TextField
              fullWidth
              type="number"
              value={newQuantity}
              onChange={(event) => {
                setNewQuantity(event.target.value);
                setRequiredQuantity(false);
              }}
            />
            {showDuplicate && (
              <p className="text-red-500 text-[0.8em] mb-0">
                จำนวนซ้ำกันกับที่มีอยู่แล้ว
              </p>
            )}
            {requiredQuantity && (
              <p className="text-red-500 text-[0.8em] mb-0">Required</p>
            )}
          </div>

          <DialogActions>
            <Button onClick={() => setOpenDialogQuantity(false)} size="small">
              ยกเลิก
            </Button>
            <Button
              onClick={() => addNewQuantity()}
              autoFocus
              size="small"
              variant="contained"
            >
              เพิ่ม
            </Button>
          </DialogActions>
        </Dialog>
      </SelectorListStyle>
    </>
  );
};

export default QuantitySelector;
