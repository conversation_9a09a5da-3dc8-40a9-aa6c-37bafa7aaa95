import { Avatar } from '@mui/material';
import styled from 'styled-components';
import { isEmpty } from 'lodash';
import { EmptyProductAttr } from '@/styles/share.styled';
import React from 'react';
import { useRouter } from 'next/router';

const SelectorListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 16px;
  .data-item {
    height: 72px;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    border: 1px solid #dbe2e0;
    border-radius: 12px;
    padding: 0 14px;
    cursor: pointer;
    &.active {
      border-color: #30d5c7;
      color: #00c1af;
      background-color: #f2fefe;
      .name {
        p {
          color: #00c1af;
        }
      }
    }
    > div {
      display: flex;
      flex-direction: column;
      p {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
`;

type CoatingSelectorProps = {
  data: any;
  selectedId: any;
  handleSelect: (name: any, ids: any) => void;
  layoutDataById: any;
  side: string;
  isSpecial: boolean;
};
const CoatingSelector = ({
  data,
  selectedId,
  handleSelect,
  layoutDataById,
  side,
  isSpecial,
}: CoatingSelectorProps) => {
  const router = useRouter();
  // console.log(data);
  // console.log('selectedId', selectedId);
  return (
    <SelectorListStyle
      data-test-coating-side={`${side}`}
      data-test-coating-before-print={`${isSpecial ? 'false' : 'true'}`}
    >
      <div
        className={`data-item ${isEmpty(selectedId) ? 'active' : ''}`}
        onClick={() => handleSelect({}, {})}
      >
        <Avatar
          src={`/images/product/empty-product.svg`}
          style={{ width: '48px', height: '48px' }}
        >
          n
        </Avatar>
        <div className="name">
          <p>{`ไม่เคลือบ`}</p>
          <p style={{ fontSize: '12px', fontWeight: '400' }}>{`ไม่เคลือบ`}</p>
        </div>
      </div>
      {data.productConfig?.map((item: any) =>
        item.configMaterial.map((material: any) => (
          <div
            key={material.subMaterialDetail.id}
            className={`data-item ${
              selectedId?.some(
                (selectedItem: any) =>
                  selectedItem.finishSubMaterialDetailId ===
                    material.subMaterialDetail.id &&
                  selectedItem.coatingMasterId === item.id
              ) && 'active'
            }`}
            onClick={() =>
              handleSelect(
                {
                  coatingName: item.master.name,
                  finishName: material.subMaterialDetail.name,
                },
                {
                  coatingMasterId: item.id,
                  finishSubMaterialDetailId: material.subMaterialDetail.id,
                }
              )
            }
          >
            <Avatar
              src={material.subMaterialDetail.imageUrl}
              style={{ width: '48px', height: '48px' }}
            >
              {material.subMaterialDetail.name[0]}
            </Avatar>
            <div className="name">
              <p>{item.master.name}</p>
              <p style={{ fontSize: '12px', fontWeight: '400' }}>
                {material.subMaterialDetail.name}
              </p>
            </div>
          </div>
        ))
      )}
      <EmptyProductAttr
        onClick={async () => {
          await router.push(
            `/product/${layoutDataById.productModel.productId}/attributes`
          );
        }}
      >
        Add-on attributes
      </EmptyProductAttr>
    </SelectorListStyle>
  );
};
export default CoatingSelector;
