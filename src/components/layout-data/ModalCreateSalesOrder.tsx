import {
  Autocomplete,
  Avatar,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { AddCircle, Search } from '@mui/icons-material';
import React, { ChangeEvent, useEffect, useState } from 'react';
import styled from 'styled-components';
import apiContact from '@/services/core/contact';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import TuneRoundedIcon from '@mui/icons-material/TuneRounded';
import Image from 'next/image';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import ActionButton from '@/components/ActionButton';
import { useRouter } from 'next/router';
import apiProduct from '@/services/stock/product';
import apiProductModel from '@/services/stock/productModel';
import apiProductSet from '@/services/stock/product-set';
import { ContactTypeSelect } from '@/components/company/contact/ContactForm';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { AnimatePresence, motion } from 'framer-motion';
import apiProject from '@/services/order/project';
import { motionFadeConfig } from '@/utils/motion/motion-config';
import apiEstimate from '@/services/order/estimate';

export const ModalCreateProductContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;
  .empty-contact {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .contact-item-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .contact-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }

      h4 {
        margin: 0;
        line-height: 1.3;
      }

      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
        font-weight: 400;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }

  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;

    .contact-detail-wrap {
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      column-gap: 24px;

      .contact-group {
        display: flex;
        align-items: center;
        column-gap: 14px;
        overflow: hidden;

        .profile-image {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .text-group {
          display: flex;
          flex-direction: column;
          width: 300px;
          overflow: auto;

          .name {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .detail {
            font-size: 12px;
            gap: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .change-contact {
        font-size: 12px;
        text-decoration: underline;
        cursor: pointer;
        white-space: nowrap;
      }
    }

    .product-grid {
      display: grid;
      grid-gap: 16px;
      position: relative;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      min-height: 226px;
      &.model {
        overflow: auto;
        min-height: 296px;
        .product-wrap {
          &:last-child {
            padding-bottom: 0;
          }
        }
        &.set {
          min-height: 220px;
        }
      }
      .product-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 8px;
        animation: ${LoadingFadein} 0.3s ease-in;
        position: relative;
        &:last-child {
          padding-bottom: 16px;
        }
        .product-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #f5f7f9;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          transition: 0.3s ease-out;
          position: relative;
          &:before {
            transition: 0.3s ease-out;
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            background-color: transparent;
          }
          &:hover {
            &:before {
              background-color: rgba(139, 217, 255, 0.2);
            }
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
        .product-type-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #aadaff;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          position: relative;
          user-select: none;
          &:before {
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            z-index: 1;
            box-shadow: transparent 0px 0px 0px 2px inset;
          }
          &:hover {
            &:before {
              box-shadow: #ddd 0px 0px 0px 2px inset;
            }
          }
          &.active {
            &:before {
              box-shadow: #263238 0px 0px 0px 2px inset;
            }
          }
          .check-box-wrap {
            position: absolute;
            top: 0;
            right: 0;
            .MuiButtonBase-root {
              &:hover {
                background: none !important;
              }
            }
            .MuiTouchRipple-root {
              display: none !important;
            }
          }
        }
        .name {
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }
      }
      .disabled {
        cursor: not-allowed;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
        background: #ffffff90;
      }
    }
    .filters-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      row-gap: 8px;
      column-gap: 24px;
      padding: 0 1px;
      .label {
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        align-items: baseline;
        .clear {
          font-size: 12px;
          font-weight: 400;
          text-decoration: underline;
          cursor: pointer;
          color: #cfd8dc;
        }
      }
    }
    .product-type-name {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: -8px;
    }
    .product-set-info {
      width: 100%;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      padding: 8px;
      min-height: 96px;
      display: flex;
      align-items: center;
      column-gap: 20px;
      .image {
        width: 80px;
        height: 80px;
        min-width: 80px;
        aspect-ratio: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f6f7f9;
        overflow: hidden;
        border-radius: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .text-group {
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        overflow: hidden;
        .name {
          font-size: 20px;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
        .description {
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
      }
    }
  }
`;

type ModalCreateSalesOrderProps = {
  children: React.ReactNode;
  handleReloadList: () => void;
};
const initialFilters = {
  search: '',
  contactRoleId: 1,
};
const initialFiltersProduct = {
  // productCategoryId: 1,
  // productTypeId: 1,
  searchName: '',
  page: 0,
  size: 100,
};
const ModalCreateSalesOrder = ({
  children,
  handleReloadList,
}: ModalCreateSalesOrderProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [contactList, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [searchInputProduct, setSearchInputProduct] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingCreateSalesOrder, setLoadingCreateSalesOrder] = useState(false);
  const [selectedContactData, setSelectedContactData] = useState<any>({});
  const [productType, setProductType] = useState<number>(1);
  const [step, setStep] = useState<number>(1);
  const [openFilters, setOpenFilters] = useState<boolean>(false);
  const [timer, setTimer] = useState<any>(null);
  const [selectProductModel, setSelectProductModel] = useState<number[]>([]);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [filterProduct, setFilterProduct] = useState<any>(
    initialFiltersProduct
  );
  const [productList, setProductList] = useState<any>({});
  const [selectedProduct, setSelectedProduct] = useState<any>({});
  const [productModelList, setProductModelList] = useState<any>({});
  const [productCategory, setProductCategory] = useState<any>({});
  const [productTypeByCategory, setProductTypeByCategory] = useState<any>({});
  const [isShowContactModal, setShowContactModal] = useState(false);
  const [productSetList, setProductSetList] = useState<any>([]);
  const [selectedProductSet, setSelectedProductSet] = useState<any>({});
  const [productSetModelList, setProductSetModelList] = useState<any>({});
  const [projectList, setProjectList] = useState<any>([]);
  const [selectedProject, setSelectedProject] = useState<any>({});

  const getProductList = async () => {
    const res = await apiProduct.getProductListOrder(filterProduct);
    if (!res.isError) {
      setProductList(res.data);
    }
  };
  const getProductSet = async () => {
    const res = await apiProductSet.getProductSetListOrder(filterProduct);
    if (!res.isError) {
      setProductSetList(res.data);
    }
  };

  useEffect(() => {
    getContactList();
  }, [filters.search]);

  useEffect(() => {
    getProductList();
    getProductSet();
  }, [filterProduct]);

  const getContactList = async () => {
    const res = await apiContact.getContactOptions(filters);
    if (res && !res.isError) {
      setContactList(res.data);
    }
  };
  const handleSearch = (event: any, type: 'contact' | 'product') => {
    const isContactType = type === 'contact';
    const setSearchInputFn = isContactType
      ? setSearchInput
      : setSearchInputProduct;
    const setFiltersFn = isContactType ? setFilters : setFilterProduct;
    const currentFilters = isContactType ? filters : filterProduct;

    setSearchInputFn(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);

    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFiltersFn({
        ...currentFilters,
        search: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };

  const handleClearProductFilter = () => {
    const clearFilterProduct = {
      page: filterProduct.page,
      searchName: filterProduct.searchName,
      size: filterProduct.size,
    };
    setFilterProduct(clearFilterProduct);
  };
  const handleSelectProduct = async (item: any) => {
    const res = await apiProductModel.getList(item.id as string);
    if (!res.isError) {
      setSelectedProduct(item);
      setProductModelList(res.data);
      setStep(3);
    }
  };
  const handleSelectProductSet = async (item: any) => {
    const res = await apiProductSet.getProductBySetId(item.id as string);
    if (!res.isError) {
      setSelectedProductSet(item);
      setProductSetModelList(res.data.productModel);
      setStep(3);
    }
  };
  const handleClose = () => {
    setShowContactModal(false);
  };
  const handleOpen = async () => {
    setContactList([]);
    setFilters(initialFilters);
    setFilterProduct(initialFiltersProduct);
    setSearchInput('');
    setSearchInputProduct('');
    setStep(1);
    setProductType(1);
    setSelectedContactData({});
    setOpenFilters(false);
    setSelectProductModel([]);
    await getContactList();
    setShowContactModal(true);
  };

  const getProductCategory = async () => {
    const res = await apiProduct.getCategory({
      size: 100,
      page: 0,
      searchTerm: '',
    });
    if (!res.isError) {
      setProductCategory(res.data.content);
    }
  };
  useEffect(() => {
    getProductCategory();
  }, []);
  const handleChangeCategory = async (value: any) => {
    const res = await apiProduct.getTypeByCategoryId(value);
    if (!res.isError) {
      setProductTypeByCategory(res.data);
      setFilterProduct({
        ...filterProduct,
        productCategoryId: value,
        productTypeId: null,
      });
    }
  };
  const handleCreateSalesOrder = async () => {
    setLoadingCreateSalesOrder(true);
    const sendData = {
      contactId: selectedContactData.id,
      productType: productType === 1 ? 0 : 1,
      productModelId:
        productType === 1 ? selectProductModel : [selectedProductSet.id],
      projectGroupId: selectedProject.id,
    };
    const res = await apiEstimate.createSalesOrder(sendData);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleReloadList();
    }
    setLoadingCreateSalesOrder(false);
    handleClose();
  };
  const handleClickProductModel = (modelId: number) => {
    if (selectProductModel.includes(modelId)) {
      const removeModelId = selectProductModel.filter(
        (item: number) => item !== modelId
      );
      setSelectProductModel(removeModelId);
    } else {
      // const addProductModelId = [...selectProductModel, modelId];
      // setSelectProductModel(addProductModelId);
      setSelectProductModel([modelId]);
    }
  };

  const getProjectList = async () => {
    const res = await apiProject.getProjectList();
    if (!res.isError) {
      setProjectList(res.data);
    }
  };

  useEffect(() => {
    getProjectList();
  }, []);

  const handleChangeProject = (event: ChangeEvent<{}>, option: any) => {
    setSelectedProject(option);
  };

  useEffect(() => {
    setSelectedProject({});
  }, [isShowContactModal]);

  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                {step !== 1 && (
                  <div
                    className="back"
                    onClick={() => {
                      setStep(step - 1);
                      setSelectProductModel([]);
                    }}
                  >
                    <IconButton
                      sx={{
                        color: '#263238',
                      }}
                    >
                      <KeyboardBackspaceRoundedIcon />
                    </IconButton>
                  </div>
                )}
                <div className="title">สร้างคำสั่งซื้อ</div>
                <div
                  className="x-close"
                  onClick={() => setShowContactModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  {step === 1 && (
                    <TextField
                      className="fade-in"
                      fullWidth
                      value={searchInput}
                      onChange={(event: any) => {
                        handleSearch(event, 'contact');
                      }}
                      placeholder="ค้นหาลูกค้า"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            {loadingSearch ? (
                              <div className="h-[24px] w-[24px] flex items-center justify-center">
                                <CircularProgress size={20} />
                              </div>
                            ) : (
                              <Search />
                            )}
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        marginTop: '24px',
                      }}
                    />
                  )}
                  <ModalCreateProductContentStyled>
                    {isEmpty(contactList) && (
                      <div className="empty-contact">
                        <Image
                          src="/icons/icon-account-circle.svg"
                          width={80}
                          height={80}
                          alt=""
                        />
                        <div className="flex flex-col gap-[4px]">
                          <h4>ระบุลูกค้าสำหรับออเดอร์</h4>
                          <div>
                            ค้นหาลูกค้าจากหมายเลขโทรศัพท์, ชื่อ-นามสกุล
                            ชื่อบริษัท หรือ เพิ่มลูกค้าใหม่
                          </div>
                        </div>
                        <div
                          onClick={() => {
                            router.push('/company/contact');
                          }}
                        >
                          <ActionButton
                            variant="outlined"
                            color="blueGrey"
                            icon={<AddCircle />}
                            text="เพิ่มลูกค้าใหม่"
                            borderRadius={'20px'}
                          />
                        </div>
                      </div>
                    )}
                    {!isEmpty(contactList) && step === 1 && (
                      <div className="contact-item-wrap">
                        {contactList.map((item: any, index: number) => (
                          <div
                            key={index}
                            className="contact-item"
                            onClick={() => {
                              setSelectedContactData(item);
                              getProductList();
                              getProductSet();
                              setStep(2);
                            }}
                          >
                            <Avatar
                              src={item.imageUrl}
                              sx={{
                                height: '40px',
                                width: '40px',
                              }}
                            />
                            <div className="">
                              <h4>{item.name}</h4>
                              <p>
                                <span>บุคคลธรรมดา</span>
                                {!isEmpty(item.phoneNumber) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.phoneNumber}</span>
                                  </>
                                )}
                                {!isEmpty(item.note) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.note}</span>
                                  </>
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    {!isEmpty(selectedContactData) && step === 2 && (
                      <div className="content fade-in">
                        <div className="contact-detail-wrap">
                          <div className="contact-group">
                            <div className="profile-image">
                              <Avatar
                                src={selectedContactData.imageUrl}
                                sx={{
                                  height: '44px',
                                  width: '44px',
                                }}
                              />
                            </div>
                            <div className="text-group">
                              <h4 className="name">
                                {selectedContactData.name}
                              </h4>
                              <div className="detail">
                                {selectedContactData.contactType.name} •
                                {` ${selectedContactData.phoneNumber}`} •
                                {` ${selectedContactData.email}`}
                              </div>
                            </div>
                          </div>
                          <div
                            className="change-contact"
                            onClick={() => {
                              setStep(1);
                            }}
                          >
                            เปลี่ยนลูกค้า
                          </div>
                        </div>
                        <Autocomplete
                          disablePortal
                          options={projectList || []}
                          getOptionLabel={(option) => option.name ?? ''}
                          value={selectedProject || null}
                          noOptionsText={'ไม่มีข้อมูล'}
                          isOptionEqualToValue={(option, value) =>
                            option.id === value.id
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              type="text"
                              placeholder="เลือกโปรเจกต์"
                              name="project"
                              error={false}
                              helperText=""
                            />
                          )}
                          sx={{
                            margin: '0 1px',
                          }}
                          onChange={(event, newValue) => {
                            handleChangeProject(event, newValue);
                          }}
                        />
                        <ContactTypeSelect>
                          <div
                            className={`btn-type ${
                              productType === 1 && 'active'
                            }`}
                            onClick={() => {
                              setProductType(1);
                            }}
                          >
                            สินค้า
                          </div>
                          <div
                            className={`btn-type ${
                              productType === 2 && 'active'
                            }`}
                            onClick={() => {
                              setProductType(2);
                            }}
                          >
                            เซ็ตสินค้า
                          </div>
                        </ContactTypeSelect>
                        <TextField
                          fullWidth
                          value={searchInputProduct}
                          onChange={(event: any) => {
                            handleSearch(event, 'product');
                          }}
                          placeholder="ค้นหา"
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                {loadingSearch ? (
                                  <div className="h-[24px] w-[24px] flex items-center justify-center">
                                    <CircularProgress size={20} />
                                  </div>
                                ) : (
                                  <Search />
                                )}
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment
                                className="cursor-pointer"
                                position="end"
                                onClick={() => {
                                  setOpenFilters(!openFilters);
                                }}
                              >
                                <TuneRoundedIcon />
                              </InputAdornment>
                            ),
                          }}
                          sx={{
                            marginTop: '8px',
                            padding: '0 1px',
                            '.MuiInputBase-root': {
                              borderRadius: '20px !important',
                            },
                          }}
                        />
                        <AnimatePresence initial={false} mode="sync">
                          {openFilters && (
                            <motion.div
                              className="filters-wrap"
                              initial={{
                                opacity: 0,
                                height: 0,
                                marginTop: '-24px',
                              }}
                              animate={{
                                opacity: 1,
                                height: 'auto',
                                marginTop: 0,
                                transition: {
                                  opacity: { duration: 0.3, delay: 0.3 },
                                  height: { duration: 0.3, delay: 0 },
                                  marginTop: { duration: 0.3, delay: 0 },
                                  ease: 'easeInOut',
                                },
                              }}
                              exit={{
                                opacity: 0,
                                height: 0,
                                marginTop: '-24px',
                                transition: {
                                  opacity: { duration: 0.3, delay: 0 },
                                  height: { duration: 0.3, delay: 0.3 },
                                  marginTop: { duration: 0.3, delay: 0.3 },
                                  ease: 'easeOut',
                                },
                              }}
                            >
                              <div className="w-full">
                                <div className="label">
                                  หมวดหมู่{' '}
                                  <div
                                    className="clear"
                                    onClick={handleClearProductFilter}
                                  >
                                    ล้าง
                                  </div>
                                </div>
                                <FormControl fullWidth size="small">
                                  <Select
                                    name="categoty"
                                    value={
                                      filterProduct.productCategoryId || ''
                                    }
                                    onChange={async (e: any) => {
                                      await handleChangeCategory(
                                        e.target.value
                                      );
                                    }}
                                    displayEmpty
                                    sx={{
                                      fontSize: '14px',
                                      height: '40px',
                                    }}
                                  >
                                    <MenuItem
                                      disabled
                                      value=""
                                      sx={{
                                        fontSize: '14px',
                                      }}
                                    >
                                      <div className="text-[#78909C]">
                                        เลือก
                                      </div>
                                    </MenuItem>
                                    {productCategory.map(
                                      (item: any, index: React.Key) => (
                                        <MenuItem
                                          key={index}
                                          value={item.id}
                                          sx={{
                                            fontSize: '14px',
                                          }}
                                        >
                                          {item.name}
                                        </MenuItem>
                                      )
                                    )}
                                  </Select>
                                </FormControl>
                              </div>
                              <div className="w-full relative">
                                <div className="label">ประเภท</div>
                                {!filterProduct.productCategoryId && (
                                  <div
                                    style={{
                                      position: 'absolute',
                                      width: '100%',
                                      height: '100%',
                                      zIndex: '9',
                                    }}
                                  />
                                )}
                                <FormControl fullWidth size="small">
                                  <Select
                                    name="type"
                                    value={filterProduct.productTypeId || ''}
                                    onChange={(e: any) => {
                                      setFilterProduct({
                                        ...filterProduct,
                                        productTypeId: e.target.value,
                                      });
                                    }}
                                    displayEmpty
                                    sx={{
                                      fontSize: '14px',
                                      height: '40px',
                                    }}
                                  >
                                    <MenuItem
                                      disabled
                                      value=""
                                      sx={{
                                        fontSize: '14px',
                                      }}
                                    >
                                      <div className="text-[#78909C]">
                                        เลือก
                                      </div>
                                    </MenuItem>
                                    {!isEmpty(productTypeByCategory) &&
                                      productTypeByCategory.map(
                                        (item: any, index: React.Key) => (
                                          <MenuItem
                                            key={index}
                                            value={item.id}
                                            sx={{
                                              fontSize: '14px',
                                            }}
                                          >
                                            {item.name}
                                          </MenuItem>
                                        )
                                      )}
                                  </Select>
                                </FormControl>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                        <div className="product-grid">
                          <AnimatePresence initial={false} mode="sync">
                            {isEmpty(selectedProject) && (
                              <motion.div
                                className="disabled"
                                {...motionFadeConfig}
                              />
                            )}
                          </AnimatePresence>
                          {!isEmpty(productList) &&
                            productType === 1 &&
                            productList.map((item: any, index: number) => (
                              <div className="product-wrap" key={index}>
                                <div
                                  className="product-card"
                                  onClick={async () => {
                                    if (!isEmpty(selectedProject)) {
                                      await handleSelectProduct(item);
                                    }
                                  }}
                                >
                                  <Image
                                    src={
                                      item.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    width={124}
                                    height={124}
                                    alt=""
                                  />
                                </div>
                                <div className="name">{item.name}</div>
                              </div>
                            ))}
                          {!isEmpty(productSetList) &&
                            productType === 2 &&
                            productSetList.map((item: any, index: number) => (
                              <div className="product-wrap" key={index}>
                                <div
                                  className="product-card"
                                  onClick={async () => {
                                    if (!isEmpty(selectedProject)) {
                                      await handleSelectProductSet(item);
                                    }
                                  }}
                                >
                                  <Image
                                    src={
                                      item.thumbnail ||
                                      item.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    width={124}
                                    height={124}
                                    alt=""
                                  />
                                </div>
                                <div className="name">{item.name}</div>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}
                    {step === 3 && (
                      <>
                        <div className="content fade-in">
                          {productType === 1 && (
                            <div className="mt-[24px] product-type-name">
                              {selectedProduct.name}
                            </div>
                          )}
                          {productType === 2 && (
                            <div className="mt-[24px] product-set-info">
                              <div className="image">
                                <Image
                                  src={
                                    `${selectedProductSet.thumbnail}` ||
                                    `${selectedProductSet.imageUrl}` ||
                                    '/images/Mailer_Box.png'
                                  }
                                  width={120}
                                  height={120}
                                  alt=""
                                />
                              </div>
                              <div className="text-group">
                                <div className="name">
                                  {selectedProductSet.name}
                                </div>
                                <div className="description">
                                  มีทั้งหมด {productSetModelList.length}{' '}
                                  โมเดลให้เลือกจัดเซ็ทสินค้า
                                </div>
                              </div>
                            </div>
                          )}
                          {!isEmpty(productModelList) && productType === 1 && (
                            <div className="product-grid model">
                              <AnimatePresence initial={false} mode="sync">
                                {isEmpty(selectedProject) && (
                                  <motion.div
                                    className="disabled"
                                    {...motionFadeConfig}
                                  />
                                )}
                              </AnimatePresence>
                              {productModelList.map((item: any) => (
                                <div className="product-wrap" key={item.id}>
                                  <div
                                    className={`product-type-card ${
                                      selectProductModel.includes(item.id)
                                        ? 'active'
                                        : ''
                                    }`}
                                    onClick={() => {
                                      if (!isEmpty(selectedProject)) {
                                        handleClickProductModel(item.id);
                                      }
                                    }}
                                  >
                                    <div className="check-box-wrap">
                                      <Checkbox
                                        color="primary"
                                        checked={selectProductModel.includes(
                                          item.id
                                        )}
                                        onChange={() => {
                                          handleClickProductModel(item.id);
                                        }}
                                        icon={<IconUnCheckbox />}
                                        checkedIcon={<IconCheckboxBlack />}
                                      />
                                    </div>
                                    <Image
                                      src={
                                        item.imageUrl ||
                                        '/images/product/empty-product.svg'
                                      }
                                      width={122}
                                      height={122}
                                      alt=""
                                    />
                                  </div>
                                  <div className="name">{item.name}</div>
                                </div>
                              ))}
                            </div>
                          )}
                          {!isEmpty(productSetModelList) &&
                            productType === 2 && (
                              <div className="product-grid model set">
                                <AnimatePresence initial={false} mode="sync">
                                  {isEmpty(selectedProject) && (
                                    <motion.div
                                      className="disabled"
                                      {...motionFadeConfig}
                                    />
                                  )}
                                </AnimatePresence>
                                {productSetModelList.map((item: any) => {
                                  return (
                                    <div className="product-wrap" key={item.id}>
                                      <div
                                        className={`product-type-card !cursor-default`}
                                      >
                                        <Image
                                          src={
                                            item.imageUrl ||
                                            '/images/product/empty-product.svg'
                                          }
                                          width={122}
                                          height={122}
                                          alt=""
                                        />
                                      </div>
                                      <div className="name">{item.name}</div>
                                    </div>
                                  );
                                })}
                              </div>
                            )}

                          <Button
                            type="button"
                            variant="contained"
                            color="dark"
                            fullWidth
                            disabled={
                              productType === 1
                                ? selectProductModel.length === 0 ||
                                  loadingCreateSalesOrder
                                : loadingCreateSalesOrder
                            }
                            sx={{
                              fontSize: '16px',
                              maxHeight: '40px',
                            }}
                            onClick={async () => {
                              await handleCreateSalesOrder();
                            }}
                          >
                            {loadingCreateSalesOrder ? (
                              <CircularProgress size={20} />
                            ) : (
                              <div>
                                {productType === 1
                                  ? 'สร้าง'
                                  : 'สร้างออเดอร์เซ็ต'}
                              </div>
                            )}
                          </Button>
                        </div>
                      </>
                    )}
                  </ModalCreateProductContentStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalCreateSalesOrder;
