import {
  <PERSON><PERSON>,
  CircularP<PERSON>ress,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiContact from '@/services/core/contact';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';

const ContactListStyle = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  height: 312px;
  margin-top: 8px;
  padding: 0 4px 0 8px;
  scrollbar-gutter: stable;

  .contact-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    border-radius: 16px;
    padding: 10px;
    transition: 0.3s;
    &:hover {
      background: #f5f7f8;
    }
    h4 {
      margin: 0;
    }
    p {
      font-size: 12px;
      margin: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 4px;
      font-weight: 400;
    }
  }
`;

type ContactDialogProps = {
  handleChooseContact: (contactId: number) => void;
  children: React.ReactNode;
};
const ContactDialog = ({
  handleChooseContact,
  children,
}: ContactDialogProps) => {
  const [contactList, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState({
    page: 0,
    size: 20,
    search: '',
  });
  const [isShowContactModal, setShowContactModal] = useState(false);
  useEffect(() => {
    getContactList();
  }, [filters]);

  const getContactList = async () => {
    const res = await apiContact.getContactOptions(filters);
    if (res && !res.isError) {
      setContactList(res.data.content);
    }
  };
  const handleSearch = (event: any) => {
    setSearchInput(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        search: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  return (
    <>
      <div onClick={() => setShowContactModal(true)}>{children}</div>
      <Dialog
        open={isShowContactModal}
        onClose={() => setShowContactModal(false)}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">รายการผู้ติดต่อ</div>
                <div
                  className="x-close"
                  onClick={() => setShowContactModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    fullWidth
                    value={searchInput}
                    onChange={handleSearch}
                    placeholder="ค้นหา ชื่อลูกค้า ชื่อบริษัท หรือ เบอร์โทร ..."
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <CircularProgress size={20} />
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                  />
                  <ContactListStyle>
                    {contactList &&
                      contactList.map((item: any, index: number) => (
                        <div
                          key={index}
                          className="contact-item"
                          onClick={() => {
                            handleChooseContact(item.id);
                            setShowContactModal(false);
                          }}
                        >
                          <Avatar
                            src={item.imageUrl}
                            sx={{
                              height: '44px',
                              width: '44px',
                            }}
                          />
                          <div className="">
                            <h4>{item.name}</h4>
                            <p>
                              <span>บุคคลธรรมดา</span>
                              {!isEmpty(item.phoneNumber) && (
                                <>
                                  <span>•</span>
                                  <span>{item.phoneNumber}</span>
                                </>
                              )}
                              {!isEmpty(item.note) && (
                                <>
                                  <span>•</span>
                                  <span>{item.note}</span>
                                </>
                              )}
                            </p>
                          </div>
                        </div>
                      ))}
                  </ContactListStyle>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ContactDialog;
