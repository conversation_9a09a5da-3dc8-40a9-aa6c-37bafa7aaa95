import styled from 'styled-components';
import { InputAdornment, TextField } from '@mui/material';
import React from 'react';

const ProductSizeStyle = styled.div`
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  > div {
    p {
      font-size: 12px;
      color: #cfd8dc;
      margin: 8px 0 0;
    }
  }
  .error-text {
    margin-top: 4px;
    color: #e81621;
  }
  .input-adornment-label {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
    width: 40px;
    min-width: 40px;
    border-right: 1px solid #dbe2e5;
    line-height: 1;
    &.r {
      color: #fe4902;
    }
    &.b {
      color: #0344dc;
    }
    &.g {
      color: #008910;
    }
  }
`;

type ProductSizeProps = {
  values: {
    width: number;
    length: number;
    height: number;
  };
  limitSize: {
    minWidth: number;
    minLength: number;
    minHeight: number;
    maxWidth: number;
    maxLength: number;
    maxHeight: number;
  };
  unit: string;
  handleChangeSize: (fields: string, value: number) => void;
  formik: any;
};
const ProductSize = ({
  values,
  unit,
  handleChangeSize,
  limitSize,
  formik,
}: ProductSizeProps) => {
  const convertToDisplayUnit = (value: any) => {
    const convertValue = parseFloat(value);
    if (formik.values.displayUnit === 'cm') {
      return parseFloat((convertValue / 10).toFixed(2));
    }
    return parseFloat(convertValue.toFixed(2));
  };
  return (
    <ProductSizeStyle>
      <div>
        <TextField
          type="number"
          name="length"
          fullWidth
          placeholder="Length"
          value={values.length ? convertToDisplayUnit(values.length) : ''}
          error={formik.touched.length && Boolean(formik.errors.length)}
          onChange={(e) => {
            const value = parseFloat(e.target.value);
            if (value < 1) {
              e.target.value = '';
            } else {
              handleChangeSize('length', value);
            }
          }}
          onKeyDown={(e: any) => {
            if (e.key === '-') {
              e.preventDefault();
            }
          }}
          sx={{
            '.MuiInputBase-root': {
              paddingLeft: '0',
            },
            '.MuiInputBase-input': {
              paddingLeft: '8px',
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <div className="input-adornment-label b">L</div>
              </InputAdornment>
            ),
          }}
        />
        <p>
          Min {convertToDisplayUnit(limitSize.minLength)} {unit} / Max{' '}
          {convertToDisplayUnit(limitSize.maxLength)} {unit}
        </p>
        {formik.touched.length && Boolean(formik.errors.length) && (
          <p className="error-text !mt-1 !ml-0">
            {formik.errors.length === 'กรุณากรอก Length' &&
              formik.errors.length}
            {formik.errors.length === 'Length ต้องมีค่าไม่ต่ำกว่า' &&
              `${formik.errors.length} ${convertToDisplayUnit(
                limitSize.minLength
              )} ${unit}`}
            {formik.errors.length === 'Length ต้องมีค่าไม่เกิน' &&
              `${formik.errors.length} ${convertToDisplayUnit(
                limitSize.maxLength
              )} ${unit}`}
            {formik.errors.length === 'Width ต้องไม่มากกว่า Length' &&
              formik.errors.length}
          </p>
        )}
      </div>

      <div>
        <TextField
          type="number"
          name="width"
          fullWidth
          placeholder="Width"
          value={values.width ? convertToDisplayUnit(values.width) : ''}
          error={formik.touched.width && Boolean(formik.errors.width)}
          onChange={(e) => {
            const value = parseFloat(e.target.value);
            if (value < 1) {
              e.target.value = '';
            } else {
              handleChangeSize('width', value);
            }
          }}
          onKeyDown={(e: any) => {
            if (e.key === '-') {
              e.preventDefault();
            }
          }}
          sx={{
            '.MuiInputBase-root': {
              paddingLeft: '0',
            },
            '.MuiInputBase-input': {
              paddingLeft: '8px',
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <div className="input-adornment-label r">W</div>
              </InputAdornment>
            ),
          }}
        />
        <p>
          Min {convertToDisplayUnit(limitSize.minWidth)} {unit}. / Max{' '}
          {convertToDisplayUnit(limitSize.maxWidth)} {unit}.
        </p>
        {formik.touched.width && Boolean(formik.errors.width) && (
          <p className="error-text !mt-1 !ml-0">
            {formik.errors.width === 'กรุณากรอก Width' && formik.errors.width}
            {formik.errors.width === 'Width ต้องมีค่าไม่ต่ำกว่า' &&
              `${formik.errors.width} ${convertToDisplayUnit(
                limitSize.minWidth
              )} ${unit}`}
            {formik.errors.width === 'Width ต้องมีค่าไม่เกิน' &&
              `${formik.errors.width} ${convertToDisplayUnit(
                limitSize.maxWidth
              )} ${unit}`}
            {formik.errors.width === 'Width ต้องไม่มากกว่า Length' &&
              formik.errors.width}
          </p>
        )}
      </div>

      <div>
        <TextField
          type="number"
          name="height"
          fullWidth
          placeholder="Height"
          value={values.height ? convertToDisplayUnit(values.height) : ''}
          error={formik.touched.height && Boolean(formik.errors.height)}
          onChange={(e) => {
            const value = parseFloat(e.target.value);
            if (value < 1) {
              e.target.value = '';
            } else {
              handleChangeSize('height', value);
            }
          }}
          onKeyDown={(e: any) => {
            if (e.key === '-') {
              e.preventDefault();
            }
          }}
          sx={{
            '.MuiInputBase-root': {
              paddingLeft: '0',
            },
            '.MuiInputBase-input': {
              paddingLeft: '8px',
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <div className="input-adornment-label g">H</div>
              </InputAdornment>
            ),
          }}
        />
        <p>
          Min {convertToDisplayUnit(limitSize.minHeight)} {unit} / Max{' '}
          {convertToDisplayUnit(limitSize.maxHeight)} {unit}
        </p>
        {formik.touched.height && Boolean(formik.errors.height) && (
          <p className="error-text !mt-1 !ml-0">
            {formik.errors.height === 'กรุณากรอก Height' &&
              formik.errors.height}
            {formik.errors.height === 'Height ต้องมีค่าไม่ต่ำกว่า' &&
              `${formik.errors.height} ${convertToDisplayUnit(
                limitSize.minHeight
              )} ${unit}`}
            {formik.errors.height === 'Height ต้องมีค่าไม่เกิน' &&
              `${formik.errors.height} ${convertToDisplayUnit(
                limitSize.maxHeight
              )} ${unit}`}
          </p>
        )}
      </div>
    </ProductSizeStyle>
  );
};

export default ProductSize;
