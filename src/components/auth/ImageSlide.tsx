import Image from 'next/image';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

const ImageSlideStyle = styled.div`
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  > img {
    z-index: 50;
  }
  .img-list {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      transition: 1.5s;
      &.show {
        animation: fadeIn;
        opacity: 1;
      }
    }
  }
  @media screen and (max-width: 820px) {
    > img {
      width: 120px;
      object-fit: contain;
    }
  }
`;

const bgList = ['/images/auth/auth-bg1.png', '/images/auth/auth-bg2.png'];
export default function ImageSlide() {
  const [imgIndex, setImgIndex] = useState(0);
  useEffect(() => {
    const timer = setInterval(() => {
      setImgIndex((prevValue) => prevValue + 1);
    }, 5000);
    return () => clearInterval(timer);
  }, []);
  return (
    <ImageSlideStyle>
      <div className="img-list">
        {bgList.map((url: string, index: number) => (
          <img
            className={imgIndex % bgList.length === index ? 'show' : ''}
            key={index}
            src={url}
            alt=""
          />
        ))}
      </div>
      <Image src={'/images/logo-white.png'} width={300} height={100} alt="" />
    </ImageSlideStyle>
  );
}
