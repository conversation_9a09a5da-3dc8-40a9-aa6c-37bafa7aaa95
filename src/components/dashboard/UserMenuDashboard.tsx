import React, { useEffect, useRef } from 'react';
import styled, { css } from 'styled-components';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import { isNull } from 'lodash';
import { useRouter } from 'next/router';
import apiUser from '@/services/core/user';
import { deleteCookie } from 'cookies-next';

const UserMenuDashboardStyled = styled.div<{
  $open: boolean;
}>`
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  width: 300px;
  padding: 16px;
  border-radius: 16px;
  position: absolute;
  background-color: white;
  box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.16);
  right: 24px;
  top: 72px;
  transition: 0.2s ease-in-out;
  transform-origin: right top;
  transform: scale(0.8);
  ${({ $open }) =>
    $open
      ? css`
          opacity: 1;
          visibility: visible;
          transform: scale(1);
        `
      : css`
          opacity: 0;
          visibility: hidden;
        `}
  .profile {
    width: 100%;
    display: flex;
    align-items: center;
    column-gap: 12px;
    .image {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .text-group {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      .name {
        font-weight: 600;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 1;
      }
      .role {
        font-size: 10px;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 1;
      }
    }
  }
  .menu-wrap {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .menu-item {
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      column-gap: 8px;
      transition: 0.15s ease-out;
      padding: 0 8px;
      cursor: pointer;
      &:hover {
        background-color: #dbe2e5;
      }
      &.log-out {
        color: #d32f2f;
        &:hover {
          background-color: #fde8ef;
        }
      }
      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
      }
      .name {
        white-space: nowrap;
      }
    }
  }
`;
type UserMenuDashboardProps = {
  open: boolean;
  handleOpen: (val: boolean) => void;
};
const UserMenuDashboard = ({ open, handleOpen }: UserMenuDashboardProps) => {
  const router = useRouter();
  const userMenuRef = useRef(null);
  const { user } = useAppSelector(userSelector);
  const handleLogout = async () => {
    await apiUser.logout();
    deleteCookie('access_token');
    deleteCookie('JSESSIONID');
    // handleOpen(false);
    router.push('/', undefined, {
      shallow: true,
    });
  };
  useOutsideAlerter(userMenuRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = ['profile'];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (!checkValue && target.tagName.toLowerCase() !== 'img') {
            handleOpen(false);
          }
        }
      }

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  return (
    <UserMenuDashboardStyled $open={open} ref={userMenuRef}>
      <div className="profile">
        <div className="image">
          <Image
            src={
              !isNull(user.imageUrl)
                ? user.imageUrl
                : '/images/empty-contact.svg'
            }
            width={40}
            height={40}
            alt=""
            draggable={false}
          />
        </div>
        <div className="text-group">
          <div className="name">{user.name}</div>
          <div className="role">{user.userType.name}</div>
        </div>
      </div>
      <div className="menu-wrap">
        <div
          className="menu-item log-out"
          onClick={() => {
            handleLogout();
          }}
        >
          <div className="icon">
            <Image
              src={'/icons/icon-move-item.svg'}
              width={24}
              height={24}
              alt=""
              draggable={false}
            />
          </div>
          <div className="name">ออกจากระบบ</div>
        </div>
      </div>
    </UserMenuDashboardStyled>
  );
};

export default UserMenuDashboard;
