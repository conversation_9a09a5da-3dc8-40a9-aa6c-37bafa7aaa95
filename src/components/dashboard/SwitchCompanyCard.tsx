import React from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import { companyListProps } from '@/pages/company';
import apiCompany from '@/services/core/company';
import AsideCompanyCard from '@/components/aside/AsideCompanyCard';

import { setCookie } from 'cookies-next';
import { isAllowed } from '@/utils/permission';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';

const SwitchCompanyStyle = styled(motion.div)`
  width: 420px;
  max-width: 80vw;
  background-color: #fff;
  box-shadow: 0px 0px 16px 0px #26323829;
  position: absolute;
  z-index: 100;
  top: 52px;
  left: 50%;
  padding: 16px 24px 20px 24px;
  display: flex;
  flex-direction: column;
  transform-origin: center top;
  overflow: hidden;
  will-change: transform, opacity, border-radius, scale;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  row-gap: 16px;
  pointer-events: auto;
  .my-company,
  .invite-company {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    .topic {
      margin: 2px 0 0 8px;
      font-size: 12px;
      &.invite {
        margin-top: 8px;
      }
    }
    .company-list {
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      transition: 0.3s;
      height: 56px;
      border-radius: 16px;
      &:hover {
        background-color: #f5f7f8;
        .group .arrow {
          color: #30d5c7;
        }
      }
      .group {
        display: flex;
        align-items: center;
        .arrow {
          display: flex;
          align-items: center;
          transition: 0.3s;
          color: #cfd8dc;
          margin-right: 4px;
          &.checked {
            color: #263238 !important;
          }
        }
        .logo {
          display: flex;
          align-items: center;
          margin-right: 16px;
          border-radius: 50%;
          overflow: hidden;
          img {
          }
        }
        .text-wrap {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: auto;
          .name {
            font-size: 16px;
            font-weight: 600;
            max-width: 260px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            @media screen and (max-width: 480px) {
              font-size: 16px;
              max-width: 40vw;
            }
          }
          .description {
            font-size: 12px;
            font-weight: 400;
            color: #263238;
            @media screen and (max-width: 480px) {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .create-company-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    height: fit-content;
    align-items: center;
    border-top: 1px solid #f5f7f8;
    .group {
      justify-content: start;
    }
  }
`;
type currentCompanyProp = {
  id: number;
  name: string;
  roleId: number;
  roleName: string;
};
type openSwitchProp = {
  open: boolean;
  data: companyListProps[] | null;
  currentCompany: currentCompanyProp;
  handleFetchUserCompany: (data: string) => void;
  handleClose: () => void;
};
const switchCompanyCard = (props: openSwitchProp) => {
  const { open, data, currentCompany, handleFetchUserCompany, handleClose } =
    props;
  const { permissions } = useAppSelector(permissionSelector);
  const router = useRouter();
  const switchCompany = async (id: number) => {
    const res = await apiCompany.switchCompany(id);
    if (!res.isError) {
      // setCookie('access_token', res.data);
      setCookie('selected-company-id', id);
      handleFetchUserCompany('switch');
      await router.replace('/dashboard', undefined, { shallow: false });
    }
  };
  return (
    <div
      style={{
        perspective: '1200px',
        perspectiveOrigin: 'center top',
        transformStyle: 'preserve-3d',
        position: 'relative',
        pointerEvents: 'none',
      }}
    >
      <AnimatePresence mode="wait">
        {open && (
          <SwitchCompanyStyle
            initial={{
              opacity: 0,
              scale: 0.1,
              scaleX: 0.3,
              scaleY: 0.05,
              rotateX: 90,
              y: 40,
              x: '-50%',
              borderRadius: '80px 80px 80px 80px',
              transformOrigin: 'center top',
            }}
            animate={{
              opacity: 1,
              scale: 1,
              scaleX: 1,
              scaleY: 1,
              rotateX: 0,
              y: 82,
              x: '-50%',
              borderRadius: '16px 16px 16px 16px',
              transformOrigin: 'center top',
            }}
            exit={{
              opacity: 0,
              scale: 0.1,
              scaleX: 0.3,
              scaleY: 0.05,
              rotateX: 90,
              y: 40,
              x: '-50%',
              borderRadius: '80px 80px 80px 80px',
              transformOrigin: 'center top',
              transition: {
                duration: 0.6,
                ease: [0.68, -0.55, 0.265, 1.55],
                opacity: {
                  duration: 0.3,
                  ease: [0.4, 0, 1, 1],
                  delay: 0.1,
                },
                scale: {
                  duration: 0.5,
                  ease: [0.68, -0.55, 0.265, 1.55],
                },
                scaleX: {
                  duration: 0.45,
                  ease: [0.68, -0.55, 0.265, 1.55],
                  delay: 0.02,
                },
                scaleY: {
                  duration: 0.4,
                  ease: [0.68, -0.55, 0.265, 1.55],
                  delay: 0.05,
                },
                rotateX: {
                  duration: 0.5,
                  ease: [0.68, -0.55, 0.265, 1.55],
                },

                borderRadius: {
                  duration: 0.6,
                  ease: [0.4, 0, 0.2, 1],
                  delay: 0,
                },
              },
            }}
            transition={{
              duration: 0.8,
              ease: [0.175, 0.885, 0.32, 1.275],
              opacity: {
                duration: 0.4,
                ease: [0.4, 0, 0.2, 1],
                delay: 0.1,
              },
              scale: {
                duration: 0.7,
                ease: [0.175, 0.885, 0.32, 1.275],
              },
              scaleX: {
                duration: 0.65,
                ease: [0.175, 0.885, 0.32, 1.275],
                delay: 0.05,
              },
              scaleY: {
                duration: 0.6,
                ease: [0.175, 0.885, 0.32, 1.275],
                delay: 0.1,
              },
              rotateX: {
                duration: 0.7,
                ease: [0.175, 0.885, 0.32, 1.275],
              },

              borderRadius: {
                duration: 0.8,
                ease: [0.4, 0, 0.2, 1],
                delay: 0,
              },
            }}
          >
            <motion.div
              className="my-company"
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 30, scale: 0.9 }}
              transition={{
                delay: 0.3,
                duration: 0.5,
                ease: [0.175, 0.885, 0.32, 1.275],
              }}
            >
              <div className="topic">Company</div>
              {data &&
                data
                  .filter((item: any) => item.inviteVerified)
                  .map((item: any, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20, scale: 0.8, rotateX: 15 }}
                      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
                      exit={{ opacity: 0, y: 20, scale: 0.8, rotateX: 15 }}
                      transition={{
                        delay: 0.4 + index * 0.08,
                        duration: 0.6,
                        ease: [0.175, 0.885, 0.32, 1.275],
                      }}
                    >
                      <AsideCompanyCard
                        data={item}
                        currentCompany={currentCompany}
                        onClose={() => {
                          handleClose();
                        }}
                        onSwitch={(id: number) => {
                          switchCompany(id);
                        }}
                        type={'myCompany'}
                      />
                    </motion.div>
                  ))}
            </motion.div>
            {data && data.some((item: any) => !item.inviteVerified) && (
              <motion.div
                className="invite-company"
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 30, scale: 0.9 }}
                transition={{
                  delay: 0.35,
                  duration: 0.5,
                  ease: [0.175, 0.885, 0.32, 1.275],
                }}
              >
                <div className="topic invite">คำเชิญเข้าร่วมบริษัท</div>
                {data
                  .filter((item: any) => !item.inviteVerified)
                  .map((item: any, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20, scale: 0.8, rotateX: 15 }}
                      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
                      exit={{ opacity: 0, y: 20, scale: 0.8, rotateX: 15 }}
                      transition={{
                        delay: 0.45 + index * 0.08,
                        duration: 0.6,
                        ease: [0.175, 0.885, 0.32, 1.275],
                      }}
                    >
                      <AsideCompanyCard
                        data={item}
                        currentCompany={currentCompany}
                        onClose={() => {
                          handleClose();
                        }}
                        onSwitch={(id: number) => {
                          switchCompany(id);
                        }}
                        type={'invite'}
                        onFetchUserCompany={(type: string) => {
                          handleFetchUserCompany(type);
                        }}
                      />
                    </motion.div>
                  ))}
              </motion.div>
            )}
            <motion.div
              className="create-company-wrap"
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 30, scale: 0.9 }}
              transition={{
                delay: 0.5,
                duration: 0.5,
                ease: [0.175, 0.885, 0.32, 1.275],
              }}
            >
              <div
                className="w-full"
                onClick={(_e: any) => {
                  if (isAllowed(permissions, 'company.info.create')) {
                    router.push('/company/create');
                  }
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<AddCircle />}
                  text="สร้างบริษัท"
                  disabled={!isAllowed(permissions, 'company.info.create')}
                  fullWidth={true}
                  borderRadius={'20px'}
                />
              </div>
            </motion.div>
          </SwitchCompanyStyle>
        )}
      </AnimatePresence>
    </div>
  );
};

export default switchCompanyCard;
