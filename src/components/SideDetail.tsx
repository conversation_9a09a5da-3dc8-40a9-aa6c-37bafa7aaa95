import { ReactNode, useEffect, useRef } from 'react';
import styled, { css } from 'styled-components';

const SideDetailStyle = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 64px;
  right: 0;
  transition: 0.4s ease-out;
  max-width: 100%;
  ${({ $isOpen }) =>
    $isOpen
      ? css`
          transform: translateX(0%);
          opacity: 1;
          visibility: visible;
        `
      : css`
          transform: translateX(50%);
          opacity: 0;
          visibility: hidden;
        `};
  width: 620px;
  background-color: #fff;
  //box-shadow: 0px 0px 8px 0px #26323829;
  z-index: 99;
  @media screen and (max-width: 1150px) {
    box-shadow: none;
    width: calc(100vw - 324px);
    height: calc(100vh - 64px);
  }
  @media screen and (max-width: 820px) {
    top: 72px;
    width: calc(100vw - 324px);
    height: calc(100vh - 72px);
  }
  @media screen and (max-width: 767px) {
    width: 100vw;
  }
  .header {
    padding: 0 16px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    border-bottom: 1px solid #dbe2e5;
    .topic {
      font-size: 22px;
      font-weight: 600;
    }
    .x-close {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: 0.3s;
      &:hover {
        rotate: 90deg;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100vh - 128px);
    padding: 0 !important;
    .body {
      padding: 0 24px 24px 24px;
      overflow: auto;
    }
  }
`;

type SideDetailProps = {
  isOpen: boolean;
  children?: ReactNode;
  handleClickOutSide?: () => void;
};
const SideDetail = ({
  isOpen,
  children,
  handleClickOutSide,
}: SideDetailProps) => {
  const wrapperRef = useRef(null);
  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = [
            'info-text',
            'medium-zoom-overlay',
            'medium-zoom-image--opened',
          ];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (handleClickOutSide && isOpen && !checkValue) {
            handleClickOutSide();
          }
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref, isOpen]);
  }

  return (
    <SideDetailStyle ref={wrapperRef} $isOpen={isOpen}>
      {children}
    </SideDetailStyle>
  );
};

export default SideDetail;
