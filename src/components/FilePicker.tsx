import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { FormHelperText } from '@mui/material';
import { isEmpty } from 'lodash';

const FilePickerStyled = styled.label`
  width: 100%;
  height: 40px;
  border-radius: 8px;
  border: 1px dashed #90a4ae;
  display: flex;
  align-items: center;
  padding: 0 8px;
  cursor: pointer;
  transition: 0.15s ease-out;
  &:hover {
    background: #f5f7f8;
  }
  .text {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    max-width: 224px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;
type FilePickerProps = {
  makeFile: (formData: any) => void;
  errorFileMsg: string;
  makeErrorFileMsg: (msg: string) => void;
};

const FilePicker = ({
  makeFile,
  errorFileMsg,
  makeErrorFileMsg,
}: FilePickerProps) => {
  const [fileName, setFileName] = useState<string>('');
  const handleUpload = (event: any) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
      makeFile(file);
    }
  };
  const validateFile = (value: any) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedSize = 5 * 1024 * 1024; // 5MB in bytes
    if (value.length > 0) {
      if (!allowedTypes.includes(value[0].type)) {
        return 'Please select a valid image file (JPEG, PNG, SVG, or GIF)';
      }
      if (value[0].size > allowedSize) {
        return 'File should be less than 5MB';
      }
    }
    return true;
  };
  return (
    <>
      <FilePickerStyled>
        <input
          type="file"
          style={{ display: 'none' }}
          onChange={(e: any) => {
            const validationResult = validateFile(e.target.files);
            if (validationResult === true) {
              handleUpload(e);
              makeErrorFileMsg('');
            } else {
              makeErrorFileMsg(validationResult);
            }
          }}
        />
        <Image src={'/icons/icon-upload.svg'} width={24} height={24} alt="" />
        <div className="text">{fileName || 'เลือกไฟล์'}</div>
      </FilePickerStyled>
      {!isEmpty(errorFileMsg) && (
        <FormHelperText
          sx={{
            margin: '4px 14px 0',
          }}
          error
        >
          {errorFileMsg}
        </FormHelperText>
      )}
    </>
  );
};

export default FilePicker;
