import React from 'react';
import { Dialog } from '@mui/material';
import { Calendar } from 'react-date-range';
import { th } from 'date-fns/locale';
import styled from 'styled-components';
import moment from 'moment';

const CalendarStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrSelected {
    background: #263238 !important;
  }
`;

type HonDatePickerProps = {
  open: boolean;
  dateValue: string;
  handleChangeDate: (val: string) => void;
  handleOpen: (val: boolean) => void;
};

const HonDatePicker = ({
  open,
  dateValue,
  handleChangeDate,
  handleOpen,
}: HonDatePickerProps) => {
  const updateDateValue = (date: Date) => {
    handleChangeDate(moment(date).format('DD/MM/YYYY'));
    handleOpen(false);
  };

  return (
    <Dialog open={open} onClose={() => handleOpen(false)}>
      <CalendarStyle>
        <Calendar
          date={new Date(dateValue)}
          onChange={(date) => updateDateValue(date)}
          locale={th}
        />
      </CalendarStyle>
    </Dialog>
  );
};

export default HonDatePicker;
