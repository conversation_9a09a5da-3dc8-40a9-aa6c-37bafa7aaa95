import styled from 'styled-components';
import dayjs from 'dayjs';
import Image from 'next/image';
import { Avatar } from '@mui/material';
import { Circle } from '@mui/icons-material';

const OrderItemCardStyle = styled.div`
  display: flex;
  flex-direction: column;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    border-color: #000;
  }
  .card-header {
    padding: 20px;
    border-bottom: 1px solid #dbe2e5;
    h3 {
      font-size: 1.3em;
      margin: 0;
    }
    p {
      margin: 0;
      font-size: 0.9em;
      color: #b0bec5;
    }
  }
  .order-item-list {
    padding: 10px;
    .order-item-card {
      display: flex;
      flex-direction: row;
      margin-bottom: 15px;
      p {
        margin: 0;
        font-size: 0.9em;
        font-weight: 500;
        &.status-text {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 5px;
          color: #f9a825;
        }
      }
      h4 {
        margin: 0;
      }
    }
  }
  .contact-details {
    padding: 0 15px 15px 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    h4 {
      margin: 0;
    }
    p {
      margin: 0;
    }
  }
`;

type OrderItemCardProps = {
  data: any;
};

const OrderItemCard = ({ data }: OrderItemCardProps) => {
  return (
    <OrderItemCardStyle>
      <div className="card-header">
        <h3>{data.orderNumber}</h3>
        <p>{dayjs(data.createdDate).format('DD/MM/YYYY HH:mm น.')}</p>
      </div>
      <div className="order-item-list">
        {data.orderItem.map((item: any, index: number) => (
          <div className="order-item-card" key={index}>
            <div className="w-[64px] h-[64px] mr-[10px]">
              {item.imageUrl ? (
                <Image src={item.imageUrl} alt="" />
              ) : (
                <Avatar style={{ width: '60px', height: '60px' }}>
                  {item.name[0]}
                </Avatar>
              )}
            </div>
            <div>
              <p>{item.orderItemId}</p>
              <h4>{item.name}</h4>
              <p className="status-text">
                <Circle style={{ width: '10px' }} />
                <span>{item.orderItemStatus}</span>
              </p>
            </div>
          </div>
        ))}
      </div>
      <div className="contact-details">
        <Avatar />
        <div>
          <h4>{data.contact.name}</h4>
          <p>{data.contact.tel}</p>
        </div>
      </div>
    </OrderItemCardStyle>
  );
};

export default OrderItemCard;
