import styled from 'styled-components';
import { useRouter } from 'next/router';
import { KeyboardBackspace } from '@mui/icons-material';

const OrderNavStyle = styled.div`
  @keyframes fadeNavIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  height: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20px;
  animation: fadeNavIn 0.3s ease;
  border-bottom: 1px solid #eee;
  .back-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    transition: 0.3s;
    svg {
      font-size: 2.5em;
      color: #cfd8dc;
      transition: 0.3s;
    }
    &:active {
      transform: scale(0.9);
    }
    &:hover {
      svg {
        color: #555;
      }
    }
  }
  .title {
    flex: 1;
    text-align: center;
    h2 {
      font-size: 1.2em;
      margin: 0;
      font-weight: 500;
      @media screen and (max-width: 820px) {
        font-size: 1.1em;
      }
    }
    p {
      margin: 0;
      opacity: 0.5;
      font-size: 0.9em;
    }
  }
`;

type OrderNavProps = {
  title: string;
  backUrl?: any;
  children?: React.ReactNode;
};
const OrderNav = ({ title, backUrl, children }: OrderNavProps) => {
  const router = useRouter();
  return (
    <OrderNavStyle className="nav-menu">
      {backUrl && (
        <div
          className="back-btn"
          onClick={() => router.push(backUrl, '', { scroll: true })}
        >
          <KeyboardBackspace style={{ width: '100%' }} />
        </div>
      )}
      <div className="title">
        <h2>{title}</h2>
      </div>
      <div className="">{children}</div>
    </OrderNavStyle>
  );
};

export default OrderNav;
