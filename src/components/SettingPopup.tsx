import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@mui/material';
import styled, { css } from 'styled-components';
import {
  buildStyles,
  CircularProgressbarWithChildren,
} from 'react-circular-progressbar';
import Image from 'next/image';

const SettingPopupStyle = styled.div<{ $open: boolean }>`
  position: fixed;
  right: 16px;
  bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: end;
  row-gap: 8px;
  z-index: 9;
  display: none;
  .card {
    width: 419px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0px 0px 16px 0px #26323829;
    transition: 0.3s ease-in-out;
    transform-origin: 300px bottom;
    z-index: -1;
    position: absolute;
    @media screen and (max-width: 450px) {
      transform-origin: 70% bottom;
      max-width: 85vw;
    }
    ${({ $open }) =>
      $open
        ? css`
            opacity: 1;
            visibility: visible;
            position: relative;
          `
        : css`
            transform: scale(0.1);
            bottom: 56px;
            opacity: 0;
            visibility: hidden;
          `}
    .content {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      padding: 24px 24px 12px;
      .text {
        max-width: 320px;
      }
      .list-wrap {
        margin-top: 12px;
        width: 100%;
        .list {
          width: 100%;
          display: flex;
          align-items: center;
          column-gap: 19px;
          border-bottom: 1px solid #dbe2e5;
          height: 56px;
          cursor: pointer;
          transition: 0.2s ease-out;
          &:hover {
            background-color: #f5f7f8;
            border-radius: 8px;
            padding: 0 8px 0;
          }
          &:last-child {
            border: 0;
          }
          .icon-wrap {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #dbe2e5;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .text {
          }
        }
      }
    }
  }
  button {
    color: #fff;
    width: 150px;
    height: 48px;
    background: linear-gradient(82.16deg, #605dec -36.72%, #30d5c7 107.11%);
    border-radius: 24px;
    transition: 0.3s ease-in;
    justify-content: space-between;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid #ffffff !important;
    box-shadow: 0px 0px 8px 0px #0000003d;
    > div {
      width: 31px;
      height: 31px;
      position: relative;
      .CircularProgressbar {
        display: flex;
        align-items: center;
      }
      .remaining {
        font-size: 12px;
        font-weight: 600;
      }
    }
    &:hover {
      background-color: #28b3a7;
    }
  }
`;

const SettingPopup = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [valueButton, setValueButton] = useState<number>(0);
  const wrapperRef = useRef(null);
  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        if (ref.current && !ref.current.contains(event.target)) {
          setOpen(false);
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  const calcValueButton = () => {
    const numerator = 1;
    const denominator = 5;
    const calculatedPercentage = (numerator / denominator) * 100;
    setValueButton(calculatedPercentage);
  };
  useEffect(() => {
    calcValueButton();
  }, []);
  return (
    <SettingPopupStyle $open={open} ref={wrapperRef}>
      <div className="card">
        <div className="content">
          <Image
            src={'/icons/important-setting/icon-hon.svg'}
            width={77.84}
            height={25.95}
            alt=""
          />
          <div className="text">
            กรุณาตั้งค่าระบบให้ครบถ้วนเพื่อการใช้งานระบบ ได้อย่างเต็มประสิทธิภาพ
          </div>
          <div className="list-wrap">
            <div className="list">
              <div className="icon-wrap">
                <Image
                  src={'/icons/important-setting/commission.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              <div className="text">ตั้งค่าเอกสาร</div>
            </div>
            <div className="list">
              <div className="icon-wrap">
                <Image
                  src={'/icons/important-setting/apartment.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              <div className="text">ข้อมูลบริษัท</div>
            </div>
            <div className="list">
              <div className="icon-wrap">
                <Image
                  src={'/icons/important-setting/inventory.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              <div className="text">สินค้า</div>
            </div>
            <div className="list">
              <div className="icon-wrap">
                <Image
                  src={'/icons/important-setting/currency_bitcoin.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              <div className="text">ระบบการขาย</div>
            </div>
          </div>
        </div>
      </div>
      <Button
        type="button"
        variant="outlined"
        onClick={() => {
          setOpen(!open);
        }}
      >
        <CircularProgressbarWithChildren
          value={valueButton}
          strokeWidth={8}
          background={false}
          styles={buildStyles({
            // textColor: dataCircleProgress.textColor,
            pathColor: '#FFFFFF',
            trailColor: '#FFFFFF40',
            textSize: '12',
            backgroundColor: '',
          })}
        >
          <>
            <div className="remaining">1/5</div>
          </>
        </CircularProgressbarWithChildren>
        ตั้งค่าบริษัท
      </Button>
    </SettingPopupStyle>
  );
};

export default SettingPopup;
