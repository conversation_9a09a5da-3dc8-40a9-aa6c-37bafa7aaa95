import React from 'react';
import { PurchaseRequestData } from '@/types/purchase-request';
import HeaderPage from './Header';
import ListPage from './List';
import ShowApproval from './Approval';

type Props = {
  prData: PurchaseRequestData | undefined;
};

const PurchaseRequestDetail: React.FC<Props> = ({ prData }) => {
  return (
    <>
      <HeaderPage prData={prData} />
      <ListPage prData={prData} />
      <ShowApproval prData={prData} />
    </>
  );
};

export default PurchaseRequestDetail;
