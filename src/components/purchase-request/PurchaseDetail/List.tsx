import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { LineTop } from '@/styles/LineTop.styled';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import KebabTable from '@/components/KebabTable';
import { KebabMenu } from '@/types/kebab-menu';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';

type Props = { prData: any; actionMenuList?: KebabMenu };
const DetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;
const ListStyled = styled.div`
  margin-top: 24px;
  .line {
    width: 100%;
    height: 8px;
    border-top: 1px solid #dbe2e5;
    background: #f5f7f8;
  }
  .detail {
    padding: 24px;
    background: #fff;
    h3 {
      margin: 0;
    }
  }
  .table-wrap {
    margin: 0 24px;
  }
  .actions-cell {
    display: flex;
    justify-content: flex-end;
  }
  .empty-message {
    padding: 16px;
    text-align: center;
    color: #666;
  }
`;
const ListPage = ({ prData, actionMenuList }: Props) => {
  return (
    <ListStyled>
      <LineTop />
      <div className={'detail'}>
        <header>
          <h3>รายการขอซื้อ</h3>
        </header>
      </div>
      <div className="table-wrap">
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow>
                <TableCell align="left">#</TableCell>
                <TableCell align="left">รหัส</TableCell>
                <TableCell align="left">รายการขอซื้อ</TableCell>
                <TableCell align="left">แบรนด์</TableCell>
                <TableCell align="left">วัสดุ</TableCell>
                <TableCell align="left">จำนวน</TableCell>
                <TableCell align="right">จัดการ</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {prData?.prOrderLists && prData.prOrderLists.length > 0 ? (
                prData.prOrderLists.map((item: any, index: number) => (
                  <TableRow key={item.id}>
                    <TableCell align="left">{index + 1}</TableCell>
                    <TableCell align="left">
                      {item.rawMaterial.rawMaterialNo}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-4">
                        <div className="relative w-8 h-8">
                          <Image
                            src={
                              item.rawMaterial.imageUrl ||
                              '/images/product/empty-product.svg'
                            }
                            alt=""
                            width={40}
                            height={40}
                            className="object-cover rounded !min-w-[40px]"
                          />
                        </div>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {item.rawMaterial.name || 'ไม่ระบุชื่อ'}
                            {item.rawMaterial.subMaterialDetail?.side
                              ? ` • ${item.rawMaterial.subMaterialDetail.side} ด้าน`
                              : ''}
                          </span>
                          <span className="text-sm text-gray-600">
                            {item.rawMaterial.itemSize?.name
                              ? `${item.rawMaterial.itemSize.name}`
                              : ''}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell align="left">{item.brand.name}</TableCell>
                    <TableCell align="left">
                      {item.rawMaterial.material.name}
                    </TableCell>
                    <TableCell align="left">
                      {item.amount} {item.rawMaterial.countDimension.name}
                    </TableCell>
                    <TableCell align="right">
                      <div className="actions-cell">
                        <DetailKebabWrap>
                          <KebabTable
                            item={''}
                            isHistory={{
                              status: true,
                              url: '/',
                            }}
                            isHowTo={true}
                            {...(actionMenuList && { ...actionMenuList })}
                            iconHorizonDot={<MoreHorizRoundedIcon />}
                          />
                        </DetailKebabWrap>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7}>
                    <div className="empty-message">ไม่พบข้อมูลรายการขอซื้อ</div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </ListStyled>
  );
};

export default ListPage;
