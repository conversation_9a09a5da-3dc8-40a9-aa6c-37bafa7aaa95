import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import dayjs from 'dayjs';

type Props = {
  isRemainingProduction?: boolean;
  prData: any;
};
const HeaderStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 24px;
  .detail-header {
    width: 100%;
    display: flex;
    gap: 24px;
    margin-top: 24px;
    .selector {
      flex: 1 1 0%;
      overflow: hidden;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      position: relative;
      .top-card {
        width: 100%;
        padding: 20px;
        display: flex;
        align-items: center;
        column-gap: 12px;
        .left-side {
          display: flex;
          column-gap: 12px;
          overflow: hidden;
          .image {
            width: 40px;
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .text-group {
            display: flex;
            flex-direction: column;
            row-gap: 6px;
            justify-content: center;
            overflow: hidden;
            * {
              line-height: 1;
            }
            .text-top {
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .text-bottom {
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              .text-remaining {
                margin-left: 0.5rem;
                color: #90a4ae;
              }
            }
          }
        }
      }
      .detail-wrap {
        width: 100%;
        display: flex;
        border-top: 1px solid #dbe2e5;
        .detail {
          display: flex;
          flex-direction: column;
          flex: 1 1 0%;
          position: relative;
          padding: 24px;
          justify-content: center;
          overflow: hidden;
          &:nth-child(2) {
            &:before {
              content: '';
              position: absolute;
              left: 0;
              height: 100%;
              width: 1px;
              background: #dbe2e5;
              transform: translateX(-50%);
            }
            &:after {
              content: '';
              position: absolute;
              right: 0;
              height: 100%;
              width: 1px;
              background: #dbe2e5;
              transform: translateX(-50%);
            }
          }
          .key {
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.2rem;
          }
          .value {
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &.not-found {
              color: #cfd8dc;
            }
          }
        }
      }
    }
  }
`;
const HeaderPage = ({ prData }: Props) => {
  return (
    <HeaderStyled>
      <div className="detail-header">
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="image">
                <Image
                  src={'/images/product/empty-product.svg'}
                  width={120}
                  height={120}
                  alt=""
                />
              </div>
              <div className="text-group">
                <div className="text-top">{prData?.user.name}</div>
                <div className="text-bottom">ผู้สร้าง</div>
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <Image
                  src={'/icons/ic_calendar_today.svg'}
                  alt={'ic'}
                  width={18}
                  height={18}
                />
                รายการผลิต
              </div>
              <div className="value">{prData?.jobNo || '-'}</div>
            </div>
            <div className="detail">
              <div className="key">
                <Image
                  src={'/icons/ic_calendar_today.svg'}
                  alt={'ic'}
                  width={18}
                  height={18}
                />
                รายการขอซื้อ
              </div>
              <div className="value">{prData?.prOrderLists?.length} รายการ</div>
            </div>
          </div>
        </div>
        <div
          className="selector"
          onClick={() => {
            //
          }}
        >
          <div className="top-card">
            <div className="left-side">
              <div className="text-group p-1">
                <div className="text-top ">หมายเหตุ</div>
                <div className="text-bottom">{prData?.note || '-'}</div>
              </div>
            </div>
          </div>
          <div className="detail-wrap">
            <div className="detail">
              <div className="key">
                <Image
                  src={'/icons/ic_calendar_today.svg'}
                  alt={'ic'}
                  width={18}
                  height={18}
                />
                วันที่สร้างใบขอซื้อ
              </div>
              <div className="value">
                {dayjs(prData?.createdDate).format('DD/MM/YYYY, HH:mm')}
              </div>
            </div>
            <div className="detail">
              <div className="key">
                <Image
                  src={'/icons/ic_calendar_today.svg'}
                  alt={'ic'}
                  width={18}
                  height={18}
                />
                วันที่ต้องการสินค้า
              </div>
              <div className="value not-found">{'-'}</div>
            </div>
          </div>
        </div>
      </div>
    </HeaderStyled>
  );
};

export default HeaderPage;
