import Image from 'next/image';
import React from 'react';
import styled from 'styled-components';

type Props = {
  prData: any;
};

const ShowApprovalStyle = styled.div`
  margin: 24px;
  .border-wrap {
    flex: 1 1 0%;
    overflow: hidden;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    position: relative;
  }
  .text {
    padding: 16px;
    border-bottom: 1px solid #dbe2e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .text:last-child {
    border-bottom: none;
  }
  .approver-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .approver-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .approver-name {
    font-weight: 500;
  }
  .approver-role {
    color: #666;
    font-size: 0.9em;
  }
  .approver-email {
    color: #666;
    font-size: 0.9em;
  }

  .status-approved {
    color: #10b981;
  }
  .status-pending {
    color: #f59e0b;
  }
`;

const ShowApproval: React.FC<Props> = ({ prData }) => {
  if (
    !prData ||
    !prData.prOrderApprovals ||
    prData.prOrderApprovals.length === 0
  ) {
    return (
      <ShowApprovalStyle>
        <h4>ผู้อนุมัติ</h4>
        <div className="border-wrap">
          <div className="text">ไม่พบข้อมูลผู้อนุมัติ</div>
        </div>
      </ShowApprovalStyle>
    );
  }
  const getStatusClass = (status: string) => {
    if (status === 'อนุมัติแล้ว' || status === 'Approved') {
      return 'status-approved';
    }
    if (status === 'รออนุมัติ' || status === 'Pending') {
      return 'status-pending';
    }
    return '';
  };

  return (
    <ShowApprovalStyle>
      <h4>ผู้อนุมัติ</h4>
      <div className="border-wrap">
        {prData.prOrderApprovals.map((approval: any) => (
          <div className="text" key={approval.id}>
            <div className="approver-container">
              <Image
                className="rounded-3xl"
                src={
                  approval.userApprove.imageUrl ||
                  '/images/product/empty-product.svg'
                }
                width={40}
                height={40}
                alt=""
              />
              <div className="approver-info">
                <div className="approver-name">
                  {approval.userApprove.name} • {approval.role.name}
                </div>
                <div className="approver-email">
                  {approval.userApprove.email}
                </div>
              </div>
            </div>
            <div className={`status ${getStatusClass(approval.status)}`}>
              {approval.status}
            </div>
          </div>
        ))}
      </div>
    </ShowApprovalStyle>
  );
};

export default ShowApproval;
