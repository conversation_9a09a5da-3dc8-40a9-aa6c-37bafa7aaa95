import styled, { css } from 'styled-components';
import { KeyboardBackspace, QuestionMark } from '@mui/icons-material';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import { ActionGroupStyle, LoadingFadein } from '@/styles/share.styled';
import Link from 'next/link';
import _, { isEmpty } from 'lodash';
import { IconButton } from '@mui/material';
import ActionButton from '@/components/ActionButton';
import KebabTable from '@/components/KebabTable';
import { useRouter } from 'next/router';
import { KebabMenu } from '@/types/kebab-menu';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import apiAnnotation from '@/services/stock/annotation';
import useSWR from 'swr';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import { getPrStatusData } from '@/utils/helper';
import { useAppDispatch, useAppSelector } from '@/store';
import { alertSelector, setSnackBar } from '@/store/features/alert';
import ModalPrConfirm from '@/components/purchase-order/ModalPrConfirm';
import { userSelector } from '@/store/features/user';

const DetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;

const DetailNavStyle = styled.div<{
  $borderBottom?: boolean;
  $backUrl?: string;
}>`
  position: sticky;
  top: 0;
  left: 0;
  min-height: 64px;
  width: 100%;
  z-index: 105;
  animation: ${LoadingFadein} 0.3s ease-in;
  padding: 0 16px;

  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
  }
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
  ${({ $backUrl }) =>
    !isEmpty($backUrl)
      ? css`
          justify-content: space-between;
        `
      : css`
          justify-content: end;
        `}
  display: flex;
  flex-direction: row;
  align-items: center;
  background: white;
  ${({ $borderBottom }) =>
    !isEmpty($borderBottom) &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  column-gap: 16px;
  h1 {
    font-size: 22px;
    margin: 0;
    font-weight: 600;
  }
  @media screen and (max-width: 820px) {
    top: 64px;
  }
  @media screen and (max-width: 650px) {
    h1 {
      font-size: 1.2em;
    }
  }
  a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #000 !important;
    height: 40px;
    width: 40px;
    svg {
      width: 24px;
      height: 24px;
    }
  }
  ${({ $borderBottom }) =>
    $borderBottom &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
  }
  .back-button {
    a {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    svg {
      width: 24px;
      height: 24px;
      color: #263238;
    }
  }
  .print-btn {
    height: 40px;
    width: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s ease-out;
    background: white;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    position: relative;
    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateY(-50%) translateX(-50%);
    }
    &:hover {
      filter: brightness(0.9);
    }
  }
  .nav-title {
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 70%;
    animation: ${LoadingFadein} 0.3s ease-in;
    ${({ $backUrl }) =>
      $backUrl
        ? css``
        : css`
            position: absolute;
            left: 0;
            transform: translateX(16px);
          `};
  }
  .children {
    display: flex;
    column-gap: 12px;
  }
`;

type DetailNavProps = {
  title?: string;
  backUrl?: string;
  children?: React.ReactNode;
  showBorderBottom?: boolean;
  centerTitle?: boolean;
  animate?: boolean;
  backUrlEvent?: () => void;
  actionMenuList?: KebabMenu;
  prData: any;
  onStatusChange?: (newStatus: number) => void;
  onReload: () => any;
};

const fetcherApproveList = async () => {
  const res = await apiAnnotation.annotationList();
  return res;
};

const getStatusBadgeStyle = (statusId: number) => {
  switch (statusId) {
    case 2:
      return {
        backgroundColor: '#e6f7ed',
        color: '#10b981',
      };
    case 3:
      return {
        backgroundColor: '#fee2e2',
        color: '#ef4444',
      };
    case 4:
      return {
        backgroundColor: '#f3f4f6',
        color: '#6b7280',
      };
    case 1:
    default:
      return {
        backgroundColor: '#FFF8E6',
        color: '#f59e0b',
      };
  }
};

const DetailNav = ({
  title,
  backUrl,
  children,
  showBorderBottom,
  backUrlEvent,
  actionMenuList,
  prData,
  onStatusChange,
  onReload,
}: DetailNavProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { snackBar } = useAppSelector(alertSelector);
  const { user } = useAppSelector(userSelector);
  const [confirmModal, setConfirmModal] = useState<any>({
    visible: false,
    loading: false,
  });
  const [, setOriginalApprovers] = useState(() =>
    prData?.prOrderApprovals ? [...prData.prOrderApprovals] : []
  );

  const [approveDisable, setApproveDisable] = useState<boolean>(false);
  useEffect(() => {
    if (!prData) return;
    if (user && prData.prOrderApprovals) {
      const myUser = _.find(prData.prOrderApprovals, (o: any) => {
        return o.userApprove.id === user.id;
      });
      if (myUser && myUser.status === 'อนุมัติ') {
        setApproveDisable(true);
      } else {
        setApproveDisable(false);
      }
    }
    if (!isApproving && !isModalOpen && prData.prOrderApprovals) {
      setOriginalApprovers([...prData.prOrderApprovals]);
    }
  }, [prData, user]);

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectReject, setSelectReject] = useState<any>(null);
  const [isApproving, setIsApproving] = useState<boolean>(false);

  const { data: response } = useSWR('/annotationList', fetcherApproveList);

  const approveList = response?.data || [];
  const matchedApprove = approveList.filter(
    (item: any) => item.annotationType.id === 2
  );

  const handleOpenModal = () => {
    setSelectReject(prData ? { ...prData } : null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectReject(null);
  };

  const handleReject = async (reason: any) => {
    if (selectReject && selectReject.id) {
      const res = await apiPurchaseRequest.rejectPrOrder({
        prOrderId: selectReject.id,
        reason: {
          annotationId: reason.annotationId,
          note: reason.note,
        },
      });

      if (res.errors) {
        dispatch(
          setSnackBar({
            ...snackBar,
            severity: 'error',
            text: res.errors,
            status: true,
          })
        );
      } else {
        onReload();
        setConfirmModal({ visible: false, loading: false });
        dispatch(
          setSnackBar({
            ...snackBar,
            severity: 'success',
            text: 'ไม่อนุมัติ PR Order สำเร็จ',
            status: true,
          })
        );
      }
      setIsModalOpen(false);
      setSelectReject(null);
    } else {
      setIsModalOpen(false);
      setSelectReject(null);
    }
  };

  const handleApprove = async (id: any) => {
    if (!id || isApproving) return;
    setIsApproving(true);
    setConfirmModal({ ...confirmModal, loading: true });
    if (onStatusChange) {
      onStatusChange(2);
    }
    try {
      const res = await apiPurchaseRequest.approve(id);
      if (!res.errors) {
        onReload();
        setConfirmModal({ visible: false, loading: false });
        dispatch(
          setSnackBar({
            ...snackBar,
            severity: 'success',
            text: 'อนุมัตืใบขอซื้อสำเร็จ',
            status: true,
          })
        );
      }
    } catch (error) {
      dispatch(
        setSnackBar({
          ...snackBar,
          severity: 'error',
          text: error,
          status: true,
        })
      );
      setConfirmModal({ visible: false, loading: false });
    } finally {
      setIsApproving(false);
      setConfirmModal({ visible: false, loading: false });
    }
  };

  const statusData = getPrStatusData(prData?.status);
  const statusStyle = getStatusBadgeStyle(prData?.status || 0);

  return (
    <>
      <ModalPrConfirm
        open={confirmModal.visible}
        handleCloseModal={() =>
          setConfirmModal({ visible: false, loading: false })
        }
        data={{
          open: true,
          title: 'ยืนยันการอนุมัติใบขอซื้อ',
          description: 'คุณต้องการยืนยันที่จะอนุมัติใบขอซื้อ และส่งจัดซื้อนี้',
          iconElement: () => <QuestionMark />,
          confirmLabel: 'ยืนยัน',
          confirmAction: () => handleApprove(prData?.id),
        }}
        loading={confirmModal.loading}
      />
      <DetailNavStyle $borderBottom={showBorderBottom} $backUrl={backUrl}>
        <div>
          {backUrl && !backUrlEvent && (
            <div className="back-button">
              <IconButton size={'small'}>
                <Link href={backUrl}>
                  <KeyboardBackspace />
                </Link>
              </IconButton>
            </div>
          )}
          {backUrlEvent && (
            <div className="back-button">
              <IconButton size={'small'}>
                <Link
                  href={'#'}
                  onClick={(event: any) => {
                    event.preventDefault();
                    backUrlEvent();
                  }}
                >
                  <KeyboardBackspace />
                </Link>
              </IconButton>
            </div>
          )}
          {!isEmpty(title) && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <h1 className="nav-title">{title}</h1>
              {statusData && (
                <div
                  className="status-badge"
                  style={{
                    padding: '4px 10px',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    backgroundColor: statusStyle.backgroundColor,
                    color: statusStyle.color,
                  }}
                >
                  {statusData.name}
                </div>
              )}
            </div>
          )}

          <div className="children">{children}</div>
        </div>
        <div>
          <ActionGroupStyle>
            <ActionButton
              variant="contained"
              color="Hon"
              text="อนุมัติใบขอซื้อ และส่งจัดซื้อ"
              borderRadius={'8px'}
              onClick={() => setConfirmModal({ visible: true, loading: false })}
              disabled={isApproving || prData?.status === 2 || approveDisable}
            />
            <div>
              <ActionButton
                variant="outlined"
                color="blueGrey"
                text="ดาวน์โหลดไฟล์"
                borderRadius={'8px'}
              />
            </div>
            <div
              onClick={() => {
                router.push(`/`);
              }}
            ></div>
            <DetailKebabWrap>
              <KebabTable
                item={prData}
                isHistory={{
                  status: true,
                  url: '/',
                }}
                isHowTo={true}
                isReject={true}
                handleReject={() => handleOpenModal()}
                {...(actionMenuList && { ...actionMenuList })}
                iconHorizonDot={<MoreHorizRoundedIcon />}
              />
            </DetailKebabWrap>
          </ActionGroupStyle>
        </div>
      </DetailNavStyle>

      <AppModalConfirm
        open={isModalOpen}
        matchedApprove={matchedApprove}
        onClickClose={handleCloseModal}
        onConfirm={handleReject}
        confirmTitle="ยืนยันไม่อนุมัติใบขอซื้อ"
        confirmDescription={`คุณตรวจสอบข้อมูลเรียบร้อยแล้ว แต่ต้องการไม่อนุมัติใบขอซื้อ "${
          selectReject?.prOrderNo || prData?.prOrderNo || '-'
        }"  โดยมีสาเหตุที่ไม่อนุมัติใบขอซื้อดังนี้`}
        icon={
          <Image
            src={'/icons/icon-cancel-pr.svg'}
            width={40}
            height={40}
            alt=""
          />
        }
        reasonTypeReject={matchedApprove || []}
        isNote={true}
        loadingConfirm={false}
        maxWidth="420px"
      />
    </>
  );
};

export default DetailNav;
