import { Drawer } from '@mui/material';
import styled from 'styled-components';
import { HistoryOutlined } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import { useRouter } from 'next/router';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import moment from 'moment';

type DrawerProps = {
  open: boolean;
  onClose: () => void;
};

const DrawerHistory = ({ open, onClose }: DrawerProps) => {
  const router = useRouter();
  const { prOrderId } = router.query;
  const [logs, setLogs] = useState<any>([]);

  useEffect(() => {
    if (prOrderId) {
      fetchLogs();
    }
  }, [prOrderId]);

  const fetchLogs = async () => {
    const response: any = await apiPurchaseRequest.getLogs({
      prOrderId: Number(prOrderId),
    });
    if (response.status) {
      setLogs(response.data);
    } else {
      setLogs([]);
    }
  };
  return (
    <Drawer
      anchor={'right'}
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          marginTop: '0 !important',
          borderRadius: '0 !important',
        },
      }}
    >
      <DrawerContent>
        <div className={'header'}>
          <div className={'title'}>
            <HistoryOutlined />
            <span>ประวัติรายการ</span>
          </div>
          <div
            className="close-icon"
            onClick={() => {
              onClose();
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className={'content'}>
          {logs &&
            logs.map((log: any, index: number) => {
              return (
                <div
                  key={index}
                  className={`history-item ${index === 0 && 'mt-[24px]'}`}
                >
                  <div className={'profile-image'}>
                    <Image
                      src={
                        log?.user?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={40}
                      height={40}
                      alt=""
                      style={{
                        borderRadius: '50%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                  <div className={'logs-detail'}>
                    <div className={'name'}>{log?.user?.name}</div>
                    <div className={'action'}>{log?.action}</div>
                    <div className={'remark'}>
                      {`หมายเหตุ: ${log?.remark || '-'}`}
                    </div>
                    <div className={'date'}>
                      {moment(log?.createdDate).format('DD MMM YYYY, HH.mm')}
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerHistory;

const DrawerContent = styled.div`
  width: 480px;
  background: #ffffff;
  .header {
    border-bottom: 1px solid #dbe2e5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    .title {
      font-weight: 700;
      display: flex;
      align-items: center;
      column-gap: 10px;
      font-size: 16px;
    }
    .close-icon {
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
  .content {
    padding: 0 24px 0 24px;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    overflow-y: auto;
    .history-item {
      display: flex;
      column-gap: 16px;
      .profile-image {
      }
      .logs-detail {
        font-size: 14px;
        .name {
          font-size: 16px;
          font-weight: 700;
        }
        .action {
        }
        .remark {
        }
        .date {
          font-size: 12px;
          color: #90a4ae;
        }
      }
    }
  }
`;
