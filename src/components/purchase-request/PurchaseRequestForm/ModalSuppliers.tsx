import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  Avatar,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import apiContact from '@/services/core/contact';
import useSWR from 'swr';
import { ModalCreatePurchaseContentStyled } from '@/components/purchase-order/ModalCreatePurchaseOrder';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { AddCircle, Search } from '@mui/icons-material';
import { isEmpty } from 'lodash';
import ActionButton from '@/components/ActionButton';

const ModalSuppliersStyle = styled(ModalCreatePurchaseContentStyled)`
  .contact-item {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      background-color: #e0e0e0;
    }
  }
`;

type Props = {
  open: boolean;
  handleClose: () => void;
  onSubmit?: (suppliers: any) => void;
  isChange?: boolean;
  initialSelectedSupplier?: any;
};

const initialFilters = {
  search: '',
  contactRoleId: 2,
};

// แก้ไขฟังก์ชันนี้เพื่อตรวจสอบข้อมูลที่ได้รับจาก API
const fetcherContact = async (param: any) => {
  try {
    const res = await apiContact.getContactList(param);
    // console.log('API Response Data:', res.data);

    // ตรวจสอบข้อมูลของแต่ละ supplier
    // if (res.data && res.data.content) {
    //   console.log('First supplier example:', res.data.content[0]);
    // }

    return res.data;
  } catch (error) {
    console.error('Error fetching contacts:', error);
    throw error;
  }
};

const ModalSuppliers = ({
  open,
  handleClose,
  onSubmit,
  isChange,
  initialSelectedSupplier,
}: Props) => {
  const [filters, setFilters] = useState(initialFilters);
  const [selectedSuppliers, setSelectedSuppliers] = useState<any>(
    initialSelectedSupplier || {}
  );
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  const [timer, setTimer] = useState<any>(null);
  const [rawData, setRawData] = useState<any[]>([]);

  // ตั้งค่าเริ่มต้นเมื่อ modal เปิด
  useEffect(() => {
    if (open && initialSelectedSupplier) {
      setSelectedSuppliers(initialSelectedSupplier);
    }
  }, [open, initialSelectedSupplier]);

  const { data: suppliersList } = useSWR(
    open ? [filters] : null,
    ([params]) => fetcherContact(params),
    { revalidateOnFocus: false }
  );

  // เก็บข้อมูลดิบจาก API
  useEffect(() => {
    if (suppliersList && suppliersList.content) {
      setRawData(suppliersList.content);
      // console.log('Raw supplier data:', suppliersList.content);
    }
  }, [suppliersList]);

  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        search: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  const handleSelectedSuppliers = (item: any) => {
    // console.log('Selected supplier item:', item);
    //
    // // ตรวจสอบฟิลด์สำคัญ
    // console.log('Checking fields:', {
    //   id: item.id,
    //   name: item.name,
    //   taxNumber: item.taxNumber,
    //   taxId: item.taxId,
    //   taxAddress: item.taxAddress,
    //   address: item.address,
    //   taxInfo: item.taxInfo,
    // });

    if (selectedSuppliers.id === item.id) {
      setSelectedSuppliers({});
    } else {
      setSelectedSuppliers(item);
    }
  };

  // แก้ไขฟังก์ชันนี้เพื่อแน่ใจว่าข้อมูลถูกส่งไปอย่างถูกต้อง
  const handleSubmit = () => {
    setLoadingSubmit(true);
    // ค้นหาข้อมูลเต็มจาก rawData
    const fullSupplierData =
      rawData.find((item) => item.id === selectedSuppliers.id) ||
      selectedSuppliers;

    // รวมข้อมูลจากทุกแหล่ง
    const supplierData = {
      ...fullSupplierData,
      id: fullSupplierData.id,
      name: fullSupplierData.name,
      // ตรวจสอบและเลือกข้อมูลจากทุกฟิลด์ที่เป็นไปได้
      taxNumber: fullSupplierData.taxNumber || fullSupplierData.taxId || '',
      taxAddress:
        fullSupplierData.taxAddress ||
        fullSupplierData.address ||
        fullSupplierData.addressTax ||
        '',
      email: fullSupplierData.email || '',
      phoneNumber: fullSupplierData.phoneNumber || '',
      contactType: fullSupplierData.contactType || { name: '' },
      imageUrl: fullSupplierData.imageUrl || '',
    };

    if (onSubmit) {
      onSubmit(supplierData);
    }

    setLoadingSubmit(false);
    handleClose();
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div
                className="title"
                style={{
                  transform: 'translateX(50%)',
                  position: 'absolute',
                  margin: '0',
                  right: '50%',
                }}
              >
                {isChange ? 'เปลี่ยนตัวแทนจำหน่าย' : 'สร้างรายการขอซื้อ'}
              </div>
              <div className="x-close" onClick={handleClose}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form style={{ rowGap: '0' }}>
                <div className="font-semibold mt-6">ตัวแทนจำหน่าย</div>
                <TextField
                  className="fade-in mt-2"
                  fullWidth
                  value={searchInput}
                  onChange={handleSearch}
                  placeholder="ค้นหาตัวแทนจำหน่าย"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {loadingSearch ? (
                          <div className="h-[24px] w-[24px] flex items-center justify-center">
                            <CircularProgress size={20} />
                          </div>
                        ) : (
                          <Search />
                        )}
                      </InputAdornment>
                    ),
                  }}
                  sx={{ marginTop: '24px' }}
                />
                <ModalSuppliersStyle>
                  {isEmpty(suppliersList?.content) ? (
                    <div className="empty-contact">
                      <div className="flex flex-col gap-[4px]">
                        <h4>ไม่พบตัวแทนจำหน่าย</h4>
                        <div>
                          ค้นหาตัวแทนจำหน่ายจากหมายเลขโทรศัพท์, ชื่อ-นามสกุล
                          ชื่อบริษัท หรือ เพิ่มตัวแทนจำหน่ายใหม่
                        </div>
                      </div>
                      <ActionButton
                        variant="outlined"
                        color="blueGrey"
                        icon={<AddCircle />}
                        text="เพิ่มตัวแทนจำหน่าย"
                        borderRadius={'20px'}
                      />
                    </div>
                  ) : (
                    <div className="contact-item-wrap">
                      {suppliersList.content.map((item: any, index: number) => (
                        <div
                          key={index}
                          className={`contact-item ${
                            selectedSuppliers.id === item.id ? 'active' : ''
                          }`}
                          onClick={() => handleSelectedSuppliers(item)}
                        >
                          <Avatar
                            src={item.imageUrl}
                            sx={{
                              height: '40px',
                              width: '40px',
                            }}
                          />
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              rowGap: '2px',
                            }}
                          >
                            <h4>{item.name}</h4>
                            <p>
                              {!isEmpty(item.phoneNumber) && (
                                <span>{item.phoneNumber}</span>
                              )}
                              {!isEmpty(item.email) && (
                                <>
                                  <span>•</span>
                                  <span>{item.email}</span>
                                </>
                              )}
                              {!isEmpty(item.taxNumber) && (
                                <>
                                  <span>•</span>
                                  <span>เลขภาษี: {item.taxNumber}</span>
                                </>
                              )}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <Button
                    type="button"
                    variant="contained"
                    color="dark"
                    fullWidth
                    disabled={isEmpty(selectedSuppliers) || loadingSubmit}
                    onClick={handleSubmit}
                    sx={{
                      fontSize: '16px',
                      marginTop: '16px',
                    }}
                  >
                    {loadingSubmit ? (
                      <CircularProgress
                        size={20}
                        sx={{
                          color: 'white',
                        }}
                      />
                    ) : (
                      <div>{isChange ? 'เปลี่ยน' : 'สร้าง'}</div>
                    )}
                  </Button>
                </ModalSuppliersStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalSuppliers;
