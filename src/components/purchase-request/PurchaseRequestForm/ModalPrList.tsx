import {
  <PERSON><PERSON>,
  CircularP<PERSON>ress,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import React, { useState } from 'react';
import 'dayjs/locale/th';
import styled from 'styled-components';
import apiUser from '@/services/core/user';
import useSWR from 'swr';
import { PrOrderApprovalType } from '@/components/purchase-request/PurchaseRequestForm/index';
import { clone, isEmpty } from 'lodash';
import { Search } from '@mui/icons-material';
import { ModalCreatePurchaseContentStyled } from '@/components/purchase-order/ModalCreatePurchaseOrder';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const key = '/core/user';

const ModalPrListStyle = styled(ModalCreatePurchaseContentStyled)`
  .contact-item {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      background-color: #e0e0e0;
    }

    &.previously-selected {
      background-color: #f0f0f0;
      opacity: 0.7;
    }

    &.disabled {
      background-color: #f0f0f0;
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
`;

type Props = {
  open: boolean;
  approvalSelect: PrOrderApprovalType;
  prOrderApproval: PrOrderApprovalType[];
  setPrOrderApproval: (approval: PrOrderApprovalType) => any;
  onClose: () => void;
};

const fetcher = async (url: string, params: any) => {
  const response = await apiUser.getUser(params);
  return response;
};

const ModalPrList = ({
  open,
  approvalSelect,
  prOrderApproval,
  onClose,
  setPrOrderApproval,
}: Props) => {
  const [filters, setFilters] = useState<any>({
    userType: 4,
    page: 0,
    size: 10,
    ascending: true,
    searchTerm: '',
  });
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [searchInput, setSearchInput] = useState<string>('');
  const [timer, setTimer] = useState<any>(null);
  const dispatch = useAppDispatch();

  const { data: userList } = useSWR(
    open ? [key, filters] : null,
    ([url, params]) => fetcher(url, params),
    { revalidateOnFocus: false }
  );

  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        searchTerm: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  const isUserSelectedInOtherRole = (userId: number) => {
    return prOrderApproval.some(
      (approval) =>
        approval.roleApprovalStatusId !== approvalSelect.roleApprovalStatusId &&
        approval.approveUserId === userId
    );
  };

  const handleSubmit = (user: any) => {
    if (!approvalSelect || typeof approvalSelect !== 'object') {
      return;
    }

    const newPrApprovalSelect = clone(approvalSelect);
    newPrApprovalSelect.approveUserId = user.id;
    newPrApprovalSelect.approveUserName = user.name;
    newPrApprovalSelect.approveUserEmail = user.email;
    setPrOrderApproval(newPrApprovalSelect);
    // onClose();
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div
                className="title"
                style={{
                  transform: 'translateX(50%)',
                  position: 'absolute',
                  margin: '0',
                  right: '50%',
                }}
              >
                เลือกผู้อนุมัติ ({approvalSelect.name})
              </div>
              <div className="x-close" onClick={onClose}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form style={{ rowGap: '0' }}>
                <TextField
                  className="fade-in mt-6"
                  fullWidth
                  value={searchInput}
                  onChange={handleSearch}
                  placeholder="ค้นหารายชื่อผู้อนุมัติ"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {loadingSearch ? (
                          <div className="h-[24px] w-[24px] flex items-center justify-center">
                            <CircularProgress size={20} />
                          </div>
                        ) : (
                          <Search />
                        )}
                      </InputAdornment>
                    ),
                  }}
                  sx={{ marginTop: '24px' }}
                />
                <ModalPrListStyle>
                  {isEmpty(userList?.data.content) ? (
                    <div className="empty-contact">
                      <div className="flex flex-col gap-[4px]">
                        <h4>ไม่พบผู้อนุมัติ</h4>
                        <div>ค้นหาผู้อนุมัติจากชื่อ-นามสกุล</div>
                      </div>
                    </div>
                  ) : (
                    <div className="contact-item-wrap">
                      {userList.data.content.map((item: any, index: number) => {
                        const isSelectedInOtherRole = isUserSelectedInOtherRole(
                          item.id
                        );
                        const isSelectedCurrentRole =
                          item.id === approvalSelect.approveUserId;

                        return (
                          <div
                            key={index}
                            className={`contact-item ${
                              isSelectedCurrentRole ? 'active' : ''
                            } ${isSelectedInOtherRole ? 'disabled' : ''}`}
                            onClick={() => {
                              if (!isSelectedInOtherRole) {
                                handleSubmit(item);
                              } else {
                                dispatch(
                                  setSnackBar({
                                    status: true,
                                    text: `ผู้อนุมัติ ${item.name} ได้ถูกเลือกในบทบาทอื่นแล้ว ไม่สามารถเลือกซ้ำได้`,
                                    severity: 'warning',
                                  })
                                );
                              }
                            }}
                          >
                            <Avatar
                              src={item.imageUrl}
                              sx={{
                                height: '40px',
                                width: '40px',
                              }}
                            />
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'column',
                                rowGap: '2px',
                              }}
                            >
                              <h4>{item.name}</h4>
                              <p>
                                {!isEmpty(item.userRole.roleName) && (
                                  <span>{item.userRole.roleName}</span>
                                )}
                                {!isEmpty(item.email) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.email}</span>
                                  </>
                                )}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </ModalPrListStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalPrList;
