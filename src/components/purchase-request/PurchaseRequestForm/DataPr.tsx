import React, { useState } from 'react';
import styled from 'styled-components';
import { FormControl, InputAdornment, Select, TextField } from '@mui/material';
import ModalSelectJob from '@/components/purchase-request/PurchaseRequestForm/ModalSelectJob';

const DataPrStyle = styled.div`
  width: 100%;
  .border-group {
    margin-top: 24px;
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    width: 100%;
    .text-field {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #b0bec5;
      padding: 10px 24px;
      &:last-child {
        border-bottom: none;
      }
      .values {
        width: 280px;
        height: 40px;
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        display: flex;
        justify-content: right;
        align-items: center;
        padding: 0 8px;
        .icon {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
`;
const PurchaseRequestDataPr = () => {
  const [open, setOpen] = useState<boolean>(false);
  // const [openCalendar, setOpenCalendar] = useState<boolean>(false);
  const handleOpenModal = () => {
    console.log('test');
    setOpen(true);
  };
  return (
    <>
      <DataPrStyle>
        <div className="">
          <span className="text-xl font-medium">ข้อมูลใบขอซื้อ</span>
          <div className="border-group">
            <div className="text-field">
              <div>วันที่สร้างใบขอซื้อ</div>
              <div className="values">
                {/* <div className="icon" onClick={() => setOpenCalendar(true)}> */}
                {/*  <Image */}
                {/*    src="/icons/icon-calendar.svg" */}
                {/*    width={24} */}
                {/*    height={24} */}
                {/*    alt="" */}
                {/*  /> */}
                {/* </div> */}
              </div>
            </div>
            <div className="text-field">
              <div>ต้องการภายใน</div>
              <TextField
                sx={{
                  width: '280px',
                  borderRadius: '6px',
                }}
                placeholder="0"
                InputProps={{
                  endAdornment: (
                    <InputAdornment className="cursor-pointer" position="end">
                      <span>วัน</span>
                    </InputAdornment>
                  ),
                }}
              ></TextField>
            </div>
            <div className="text-field">
              <div>วันที่รับสินค้า</div>
              <div className="values">
                {/* <div className="icon" onClick={() => setOpenCalendar(true)}> */}
                {/*  <Image */}
                {/*    src="/icons/icon-calendar.svg" */}
                {/*    width={24} */}
                {/*    height={24} */}
                {/*    alt="" */}
                {/*  /> */}
                {/* </div> */}
              </div>
            </div>
            <div className="text-field">
              <div>เลขที่รายการผลิต (อ้างอิง)</div>
              <div className="">
                <FormControl sx={{ width: '280px' }}>
                  <Select onClick={handleOpenModal}></Select>
                </FormControl>
              </div>
            </div>
          </div>
        </div>
      </DataPrStyle>
      <ModalSelectJob open={open} handleClose={() => setOpen(false)} />
    </>
  );
};

export default PurchaseRequestDataPr;
