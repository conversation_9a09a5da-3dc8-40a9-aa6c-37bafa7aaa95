import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { ModalCreatePurchaseContentStyled } from '@/components/purchase-order/ModalCreatePurchaseOrder';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  FormControl,
  IconButton,
  MenuItem,
  Select,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiMaterial from '@/services/stock/material';
import { useAppDispatch } from '@/store';
import useSWR from 'swr';
import SubMaterialDetailList from '@/components/purchase-request/PurchaseRequestForm/SubMaterialDetailList';
import { MaterialItemNewStyle } from '@/components/product/form/attributes/BaseMaterialForm';
import { find, isEmpty } from 'lodash';
import Image from 'next/image';
import { setMaterialListForm } from '@/store/features/purchase-request';
import SvgArrowBackIcon from '@/components/svg-icon/SvgArrowBackIcon';

type Props = {
  open: boolean;
  onClose: () => void;
};

const fetchMaterial = async () => {
  const res = await apiMaterial.getMaterialOrder();
  return res.data;
};

const ModalSelectMaterialStyle = styled(ModalCreatePurchaseContentStyled)`
  align-items: start !important;
`;

const ModalSelectMaterial = ({ open, onClose }: Props) => {
  const dispatch = useAppDispatch();
  const { data: materials } = useSWR(
    open ? `/material/order` : null,
    fetchMaterial
  );

  const [materialId, setMaterialId] = useState(1);
  const [subMaterialId, setSubMaterialId] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);

  useEffect(() => {
    if (open) setSelectedItems([]);
  }, [open]);

  const selectedMaterial = useMemo(
    () =>
      materials ? find(materials, ['id', materialId]) || materials[0] : null,
    [materials, materialId]
  );

  const subMaterials = useMemo(
    () => selectedMaterial?.subMaterial || [],
    [selectedMaterial]
  );

  const handleMaterialChange = useCallback((value: any) => {
    setMaterialId(Number(value));
    setSubMaterialId(0);
  }, []);

  const handleClose = useCallback(() => {
    setMaterialId(0);
    setSubMaterialId(0);
    onClose();
  }, [onClose]);

  const handleSubMaterialClick = useCallback((subMaterial: any) => {
    setSubMaterialId(subMaterial.id);
  }, []);

  const handleBackSubMaterial = () => setSubMaterialId(0);

  const materialOptions = useMemo(
    () =>
      materials?.map((item: any) => (
        <MenuItem key={item.id} value={item.id}>
          {item.name}
        </MenuItem>
      )),
    [materials]
  );

  const subMaterialList = useMemo(
    () =>
      isEmpty(subMaterials)
        ? null
        : subMaterials.map((subMaterial: any) => (
            <MaterialItemNewStyle
              key={subMaterial.id}
              onClick={() => handleSubMaterialClick(subMaterial)}
              className={subMaterialId === subMaterial.id ? 'selected' : ''}
              style={{ cursor: 'pointer' }}
            >
              <div className="img-container">
                <Image
                  src={
                    subMaterial.imageUrl || '/images/product/empty-product.svg'
                  }
                  alt={subMaterial.name}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <label className="ml-4">{subMaterial.name}</label>
            </MaterialItemNewStyle>
          )),
    [subMaterials, subMaterialId, handleSubMaterialClick]
  );

  const handleSubmit = () => {
    if (!isEmpty(selectedItems)) {
      dispatch(setMaterialListForm(selectedItems));
      setSelectedItems([]);
      setSubMaterialId(0);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        <FormModalStyle $width={492} style={{ height: '52vh' }}>
          <div className="content-wrap">
            <div className="header">
              {subMaterialId !== 0 ? (
                <Button
                  onClick={handleBackSubMaterial}
                  style={{ borderRadius: '10px' }}
                  type="submit"
                >
                  <SvgArrowBackIcon />
                </Button>
              ) : (
                <div></div>
              )}
              <div
                className="title"
                style={{
                  transform: 'translateX(50%)',
                  position: 'absolute',
                  right: '50%',
                }}
              >
                เพิ่มรายการวัสดุ
              </div>
              <div className="x-close" onClick={handleClose}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            {subMaterialId !== 0 ? (
              <SubMaterialDetailList
                materialId={materialId}
                subMaterialId={subMaterialId}
                onSelect={setSelectedItems}
                selectedItems={selectedItems}
              />
            ) : (
              <div className="mt-6 flex flex-col gap-4">
                <FormControl fullWidth>
                  <Select
                    value={materialId.toString()}
                    onChange={(e) => handleMaterialChange(e.target.value)}
                  >
                    {materialOptions}
                  </Select>
                </FormControl>
                <ModalSelectMaterialStyle>
                  {subMaterialList}
                </ModalSelectMaterialStyle>
              </div>
            )}
          </div>
        </FormModalStyle>
      </DialogContent>
      <DialogActions>
        <div className="w-full flex justify-between gap-5 pr-4 pl-4 pb-4">
          <Button
            variant="outlined"
            color="blueGrey"
            fullWidth
            onClick={onClose}
          >
            ยกเลิก
          </Button>
          <Button
            variant="contained"
            color="dark"
            fullWidth
            disabled={isEmpty(selectedItems)}
            onClick={handleSubmit}
          >
            บันทึก
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
};

export default ModalSelectMaterial;
