import React, { ChangeEvent, useState } from 'react';
import apiRawMaterial from '@/services/stock/raw-material';
import useSWR from 'swr';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import {
  Alert,
  AlertTitle,
  Checkbox,
  InputAdornment,
  ListItemButton,
  ListItemText,
  Skeleton,
  TextField,
} from '@mui/material';
import { useDebounceCallback } from 'usehooks-ts';
import { Search } from '@mui/icons-material';

type Props = {
  materialId: number;
  subMaterialId: number;
  onSelect: any;
  selectedItems: any;
};

const SubMaterialDetailList: React.FC<Props> = (props) => {
  const { materialId, subMaterialId, onSelect, selectedItems } = props;
  const [searchName, setSearchName] = useState<string>('');
  const debounced = useDebounceCallback(setSearchName, 400);
  const {
    data: subMaterialDetailList,
    error,
    isLoading,
  } = useSWR(
    subMaterialId !== 0
      ? ['/api/submaterial', materialId, subMaterialId, searchName]
      : null,
    () =>
      apiRawMaterial
        .getSubMaterialById({
          materialId,
          subMaterialId,
          limit: 100,
          searchName: searchName,
        })
        .then((res) => res.data)
  );

  if (error)
    return (
      <div className="my-4">
        <Alert color="error">
          <AlertTitle>ไม่พบข้อมูล</AlertTitle>
        </Alert>
      </div>
    );

  const handleToggle = (subMaterialDetail: any) => () => {
    const currentIndex = selectedItems.findIndex(
      (item: any) => item.id === subMaterialDetail.id
    );
    const newChecked = [...selectedItems];

    if (currentIndex === -1) {
      newChecked.push(subMaterialDetail);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    onSelect(newChecked);
  };

  return (
    <div className="relative flex flex-col h-[500px]">
      <div className="p-4">
        <TextField
          fullWidth
          name="search"
          size="small"
          type="text"
          placeholder="ค้นหา"
          defaultValue={searchName}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            debounced(e.target.value);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <div className="flex items-center">
                  <Search
                    style={{
                      color: '#263238',
                    }}
                  />
                </div>
              </InputAdornment>
            ),
          }}
        />
      </div>
      <div className="flex-1 overflow-y-auto min-h-0">
        {!isLoading ? (
          <List dense={true}>
            {subMaterialDetailList &&
              subMaterialDetailList.map(
                (subMaterialDetail: any, index: number) => {
                  return (
                    <ListItemButton key={index}>
                      <ListItem
                        secondaryAction={
                          <Checkbox
                            edge="end"
                            onChange={handleToggle(subMaterialDetail)}
                            checked={selectedItems.some(
                              (item: any) => item.id === subMaterialDetail.id
                            )}
                          />
                        }
                      >
                        <ListItemText primary={subMaterialDetail.name} />
                      </ListItem>
                    </ListItemButton>
                  );
                }
              )}
          </List>
        ) : (
          <SubMaterialDetailSkeleton count={10} />
        )}
      </div>
    </div>
  );
};

export default SubMaterialDetailList;

const SubMaterialDetailSkeleton = ({ count }: { count: number }) => {
  return (
    <div className="flex flex-col gap-2 py-4">
      {Array.from({ length: count }).map((_, index) => {
        return <Skeleton key={index} height={40} sx={{ transform: 'unset' }} />;
      })}
    </div>
  );
};
