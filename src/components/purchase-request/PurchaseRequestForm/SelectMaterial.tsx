import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  FormControl,
  IconButton,
  MenuItem,
  Select,
} from '@mui/material';
import useSWR from 'swr';
import apiMaterial from '@/services/stock/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import {
  MaterialItemStyle,
  MaterialListStyle,
} from '@/components/product/form/attributes/BaseMaterialForm';
import { find, isEmpty } from 'lodash';
import SubMaterialDetailList from '@/components/purchase-request/PurchaseRequestForm/SubMaterialDetailList';
import { useAppDispatch } from '@/store';
import { setMaterialListForm } from '@/store/features/purchase-request';
import SvgArrowBackIcon from '@/components/svg-icon/SvgArrowBackIcon';

type Props = {
  open: boolean;
  onClose: (materialId?: number, subMaterialId?: number) => void;
};

const fetcher = async () => {
  const res = await apiMaterial.getMaterialOrder();
  return res.data;
};

interface Material {
  id: number;
  name: string;
  subMaterial: SubMaterial[];
}

interface SubMaterial {
  id: number;
  name: string;
  imageUrl: string;
}
interface SubMaterialDetail {
  id: number;
  name: string;
}
const SelectMaterialModal: React.FC<Props> = ({ open, onClose }) => {
  const dispatch = useAppDispatch();
  const { data: materials } = useSWR<Material[]>(
    open ? `/material/order` : null,
    fetcher
  );

  const [materialId, setMaterialId] = useState<number>(1);
  const [subMaterialId, setSubMaterialId] = useState<number>(0);
  const [selectedItems, setSelectedItems] = useState<SubMaterialDetail[]>([]);

  const selectedMaterial = useMemo(() => {
    if (!materials) return null;
    return materialId !== 0
      ? find(materials, ['id', materialId])
      : materials[0];
  }, [materials, materialId]);

  const subMaterials = useMemo(() => {
    return selectedMaterial?.subMaterial || [];
  }, [selectedMaterial]);

  const handleMaterialChange = useCallback((value: string) => {
    setMaterialId(Number(value));
    setSubMaterialId(0);
  }, []);

  const handleClose = useCallback(() => {
    setMaterialId(0);
    setSubMaterialId(0);
    onClose();
  }, [onClose]);

  const handleSubMaterialClick = useCallback(
    (subMaterial: SubMaterial) => {
      setSubMaterialId(subMaterial.id);
    },
    [materialId, onClose]
  );

  const handleBackSubMaterial = () => {
    setSubMaterialId(0);
  };

  const materialOptions = useMemo(() => {
    return materials?.map((item) => (
      <MenuItem key={item.id} value={item.id}>
        {item.name}
      </MenuItem>
    ));
  }, [materials]);

  const subMaterialList = useMemo(() => {
    if (isEmpty(subMaterials)) return null;

    return subMaterials.map((subMaterial) => (
      <MaterialItemStyle
        key={subMaterial.id}
        onClick={() => handleSubMaterialClick(subMaterial)}
        style={{ cursor: 'pointer' }}
        className={subMaterialId === subMaterial.id ? 'selected' : ''}
      >
        <div className="img-container">
          <Image
            src={subMaterial.imageUrl || '/images/product/empty-product.svg'}
            alt={subMaterial.name}
            fill
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex-1 ml-[16px] flex flex-row gap-2 items-center">
          <span className="font-[600]">{subMaterial.name}</span>
          <span></span>
        </div>
        <div className="flex flex-row items-center" />
      </MaterialItemStyle>
    ));
  }, [subMaterials, subMaterialId, handleSubMaterialClick]);

  function onSelect(items: any) {
    setSelectedItems(items);
  }

  function handleSubmit() {
    dispatch(setMaterialListForm(selectedItems));
    setSelectedItems([]);
    onClose();
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModalStyle $width={592}>
          <div className="content-wrap">
            <div className="header">
              <Button onClick={() => handleBackSubMaterial()}>
                <SvgArrowBackIcon />
              </Button>
              <div className="title">เพิ่มรายการวัสดุ</div>
              <div className="x-close">
                <IconButton onClick={handleClose}>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            {subMaterialId !== 0 ? (
              <SubMaterialDetailList
                materialId={materialId}
                subMaterialId={subMaterialId}
                onSelect={onSelect}
                selectedItems={selectedItems}
              />
            ) : (
              <div className="mt-2 p-4 flex flex-col gap-4">
                <FormControl fullWidth>
                  <Select
                    value={materialId.toString()}
                    onChange={(event) =>
                      handleMaterialChange(event.target.value)
                    }
                    defaultValue={materialId.toString()}
                  >
                    {materialOptions}
                  </Select>
                </FormControl>
                <div>
                  <MaterialListStyle>{subMaterialList}</MaterialListStyle>
                </div>
              </div>
            )}
          </div>
        </FormModalStyle>
      </DialogContent>
      <DialogActions>
        <Button
          type="button"
          variant="outlined"
          color="blueGrey"
          sx={{
            boxShadow: 'none',
            fontWeight: '400',
          }}
          fullWidth
          onClick={() => onClose()}
        >
          <span>ยกเลิก</span>
        </Button>
        <Button
          disabled={isEmpty(selectedItems)}
          onClick={() => handleSubmit()}
          variant="contained"
          color="dark"
          sx={{
            boxShadow: 'none',
            fontWeight: '400',
          }}
          fullWidth
        >
          บันทึก
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SelectMaterialModal;
