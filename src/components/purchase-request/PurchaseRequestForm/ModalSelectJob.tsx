import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { ModalCreatePurchaseContentStyled } from '@/components/purchase-order/ModalCreatePurchaseOrder';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { Search } from '@mui/icons-material';
import { isEmpty } from 'lodash';
import apiPurchaseRequestRaw from '@/services/stock/purchaseRequest-raw';

const ModalSelectJobStyle = styled(ModalCreatePurchaseContentStyled)`
  .contact-item {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    &:hover {
      background-color: #f5f5f5;
    }
    &.active {
      background-color: #e0e0e0;
    }
  }
`;

type Props = {
  open: boolean;
  handleClose: () => void;
  params?: any;
  onSelectJob?: (job: any) => void;
};

const ModalSelectJob = ({
  open,
  handleClose,
  params,
  onSelectJob = () => {},
}: Props) => {
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [jobData, setJobData] = useState<any[]>([]);

  useEffect(() => {
    if (!params?.rawMaterialId) return;

    const fetchData = async () => {
      setLoadingSearch(true);
      try {
        const response = await apiPurchaseRequestRaw.getListPrOrderRaw({
          ...params,
          search: searchInput,
        });
        setLoadingSearch(false);
        if (response?.status && Array.isArray(response.data)) {
          setJobData(response.data);
        } else {
          setJobData([]);
        }
      } catch (error) {
        setLoadingSearch(false);
        setJobData([]);
      }
    };
    fetchData();
  }, [params?.rawMaterialId, searchInput]);

  useEffect(() => {
    if (!open) {
      setSelectedJob(null);
      setSearchInput('');
    }
  }, [open]);

  const handleSearch = (event: any) => {
    setSearchInput(event.target.value);
    setLoadingSearch(true);
    if (timer) clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
    }, 1000);
    setTimer(newTimer);
  };

  const handleSelectedJob = (item: any) => {
    setSelectedJob(selectedJob?.id === item.id ? null : item);
  };

  const handleSubmit = () => {
    if (!selectedJob) return;
    setLoadingSubmit(true);
    if (typeof onSelectJob === 'function') {
      onSelectJob(selectedJob);
      setTimeout(() => {
        setLoadingSubmit(false);
        handleClose();
      }, 500);
      setLoadingSubmit(false);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div
                className="title"
                style={{
                  transform: 'translateX(50%)',
                  position: 'absolute',
                  margin: '0',
                  right: '50%',
                }}
              >
                {'เลือกรายการผลิต'}
              </div>
              <div className="x-close" onClick={handleClose}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form
                style={{ rowGap: '0' }}
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <div
                  className="material-name"
                  style={{
                    marginTop: '20px',
                    fontSize: '16px',
                    marginBottom: '4px',
                    fontWeight: '500',
                  }}
                >
                  {jobData[0]?.materialName || '-'}
                </div>
                <TextField
                  className="fade-in mt-2"
                  fullWidth
                  value={searchInput}
                  onChange={(event: any) => handleSearch(event)}
                  placeholder="ค้นหารายการผลิต"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {loadingSearch ? (
                          <CircularProgress size={20} />
                        ) : (
                          <Search />
                        )}
                      </InputAdornment>
                    ),
                  }}
                  sx={{ marginTop: '24px' }}
                />
                <div style={{ marginTop: '16px', fontSize: '14px' }}>
                  {jobData.length} รายการ
                </div>
                <div
                  style={{
                    borderBottom: '1px solid #dbe2e5',
                    marginTop: '10px',
                  }}
                ></div>
                <ModalSelectJobStyle>
                  {isEmpty(jobData) ? (
                    <div className="empty-contact">
                      <div className="flex flex-col gap-[4px]">
                        <h4>ไม่พบรายการผลิต</h4>
                        <div>ค้นหารายการผลิตจากเลขรายการผลิต</div>
                      </div>
                    </div>
                  ) : (
                    <div className="contact-item-wrap">
                      {jobData.map((item: any, index: number) => (
                        <label
                          key={item.id || index}
                          className={`contact-item ${
                            selectedJob?.id === item.id ? 'active' : ''
                          }`}
                          onClick={() => handleSelectedJob(item)}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            width: '100%',
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '12px',
                              paddingLeft: '8px',
                            }}
                          >
                            <div>{item.jobNo}</div>
                          </div>
                          <div className="pr-2">{item.quantity} ชิ้น</div>
                        </label>
                      ))}
                    </div>
                  )}
                  <div className="w-full flex justify-between mt-[24px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      fullWidth
                      onClick={handleClose}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      disabled={!selectedJob}
                    >
                      {loadingSubmit ? (
                        <CircularProgress size={20} />
                      ) : (
                        'ยืนยัน'
                      )}
                    </Button>
                  </div>
                </ModalSelectJobStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalSelectJob;
