import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  FormControl,
  TextField,
  MenuItem,
} from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import DeleteButton from '@/components/global/DeleteButton';
import { Controller } from 'react-hook-form';
import { useAppSelector, useAppDispatch } from '@/store';
import { purchaseRequestSelector } from '@/store/features/purchase-request';
import { setMaterialListForm } from '@/store/features/purchase-request/actions';
import ModalSelectJob from '@/components/purchase-request/PurchaseRequestForm/ModalSelectJob';

import ActionButton from '@/components/ActionButton';

import apiPurchaseRequestRaw from '@/services/stock/purchaseRequest-raw';
import ModalSelectMaterial from './ModalSelectMaterial';

type Props = {
  fieldArray: any;
  form: any;
  sentPrData: any;
  errors: any;
};

const PurchaseRequestList = ({
  fieldArray,
  form,
  sentPrData,
  errors,
}: Props) => {
  const dispatch = useAppDispatch();
  const { materialFormList } = useAppSelector(purchaseRequestSelector);
  const [openModalMaterial, setOpenModalMaterial] = useState<boolean>(false);
  const [openModalJob, setOpenModalJob] = useState<boolean>(false);
  const { control, register, setValue } = form;
  const { fields, append, remove, update } = fieldArray;
  const [currentFieldIndex, setCurrentFieldIndex] = useState<number>(-1);
  const [jobAvailability, setJobAvailability] = useState<
    Record<number, boolean>
  >({});
  const [loadingJobs, setLoadingJobs] = useState<Record<number, boolean>>({});
  const [paramsModalSelectJob, setParamsModalSelectJob] = useState({
    rawMaterialId: 0,
    brandId: 1,
    prOrderRawStatusId: 1,
    search: '',
  });

  useEffect(() => {
    if (fields.length > 0 || !sentPrData?.prOrderLists?.length) return;

    const newFields = sentPrData.prOrderLists.map((item: any) => {
      const rawMaterial = item.rawMaterial || {};
      const brandId = item.brand?.id || 1;
      const amount = item.amount || 1;

      return {
        rawMaterialId: rawMaterial?.id,
        brandId: brandId,
        amount: amount,
        prOrderRawId: item.prOrderRawId || null,
        isJobDefault: item.isJobDefault || false,

        _jobNo: item.jobNo || null,
        _name: rawMaterial?.name || '',
        _rawMaterialNo: rawMaterial?.rawMaterialNo || '',
        _imageUrl: rawMaterial?.imageUrl || '/icons/icon-default.png',
        _materialName: rawMaterial?.material?.name || '-',
        _subMaterialDetail: rawMaterial?.subMaterialDetail || null,
        _itemSize: rawMaterial?.itemSize || null,
        _brands: item.brand ? [item.brand] : [],
        _originalJobQuantity: item.prOrderRawId !== null ? amount : undefined,
      };
    });
    append(newFields);
  }, [sentPrData, append, fields.length]);

  useEffect(() => {
    if (!materialFormList?.length) return;

    const newFields = materialFormList.map((item: any) => {
      let brandId = 1;

      if (item.brand) {
        if (Array.isArray(item.brand) && item.brand.length > 0) {
          brandId = item.brand[0].id || 1;
        } else if (typeof item.brand === 'object' && item.brand.id) {
          brandId = item.brand.id;
        }
      } else if (item.brandId && item.brandId !== null) {
        brandId = item.brandId;
      }

      return {
        rawMaterialId: item.id,
        brandId: brandId,
        amount: item.amount || 1,
        prOrderRawId: null,
        isJobDefault: false,

        _jobNo: null,
        _name: item.name || '',
        _rawMaterialNo: item.rawMaterialNo || '',
        _imageUrl: item.imageUrl || '/icons/icon-default.png',
        _materialName: item.material?.name || '-',
        _subMaterialDetail: item.subMaterialDetail || null,
        _itemSize: item.itemSize || null,
        _brands: item.brand
          ? Array.isArray(item.brand)
            ? item.brand
            : [item.brand]
          : [],
      };
    });

    if (newFields.length) {
      append(newFields);
      dispatch(setMaterialListForm([]));
    }
  }, [materialFormList, append, dispatch]);

  useEffect(() => {
    if (!fields.length) return;

    const materialIds = fields
      .filter(
        (field: any) =>
          field.rawMaterialId && !jobAvailability[field.rawMaterialId]
      )
      .map((field: any) => field.rawMaterialId);

    materialIds.forEach(async (materialId: number) => {
      if (!materialId || loadingJobs[materialId]) return;

      setLoadingJobs((prev) => ({ ...prev, [materialId]: true }));

      const response = await apiPurchaseRequestRaw.getListPrOrderRaw({
        rawMaterialId: materialId,
        brandId: 1,
        prOrderRawStatusId: 1,
        search: '',
      });

      const hasJobs =
        response?.status &&
        Array.isArray(response.data) &&
        response.data.length > 0;
      setJobAvailability((prev) => ({ ...prev, [materialId]: hasJobs }));
    });
  }, [fields]);

  const handleOpenModalMaterial = () => {
    setOpenModalMaterial(true);
  };

  const handleCloseModalMaterial = () => {
    setOpenModalMaterial(false);
  };

  const handleOpenModalJob = (index: number, rawMaterialId: number) => {
    setCurrentFieldIndex(index);
    setParamsModalSelectJob((prev) => ({
      ...prev,
      rawMaterialId,
    }));
    setOpenModalJob(true);
  };

  const handleJobSelected = (selectedJob: any) => {
    if (currentFieldIndex === -1 || !selectedJob) return;

    const currentField = fields[currentFieldIndex];

    const jobBrandId = selectedJob.brand?.id || selectedJob.brandId;
    const newBrandId =
      jobBrandId !== null && jobBrandId !== undefined
        ? jobBrandId
        : currentField.brandId || 1;

    const jobQuantity = selectedJob.quantity || 1;

    const updatedField = {
      ...currentField,
      prOrderRawId: selectedJob.id,
      amount: jobQuantity,
      _jobNo: selectedJob.jobNo,
      brandId: newBrandId,
      _originalJobQuantity: jobQuantity,
    };

    update(currentFieldIndex, updatedField);

    setValue(`prOrderList.${currentFieldIndex}.amount`, jobQuantity, {
      shouldValidate: true,
      shouldDirty: true,
    });

    setValue(`prOrderList.${currentFieldIndex}.brandId`, newBrandId, {
      shouldValidate: true,
      shouldDirty: true,
    });

    setValue(
      `prOrderList.${currentFieldIndex}._originalJobQuantity`,
      jobQuantity
    );

    form.trigger(`prOrderList.${currentFieldIndex}.amount`);
    form.trigger(`prOrderList.${currentFieldIndex}.brandId`);

    setCurrentFieldIndex(-1);
    setOpenModalJob(false);
  };

  const renderJobSelection = (field: any, index: number) => {
    const { rawMaterialId } = field;
    const hasJobs = jobAvailability[rawMaterialId];
    const currentJobNo = field._jobNo || '';

    return (
      <FormControl fullWidth>
        <Select
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (hasJobs || field._jobNo) {
              handleOpenModalJob(index, field.rawMaterialId);
            }
          }}
          value={currentJobNo}
          displayEmpty
          renderValue={() => {
            if (currentJobNo) return currentJobNo;
            return hasJobs ? 'เลือกรายการผลิต' : 'ไม่มีรายการผลิต';
          }}
          open={false}
          MenuProps={{
            style: { display: 'none' },
          }}
          sx={{
            cursor: hasJobs || field._jobNo ? 'pointer' : 'not-allowed',
            '.MuiOutlinedInput-notchedOutline': {
              borderColor: !hasJobs && !field._jobNo ? '#ddd' : undefined,
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: !hasJobs && !field._jobNo ? '#ddd' : undefined,
            },
            '.MuiSelect-select': {
              color: !hasJobs && !field._jobNo ? '#999' : undefined,
            },
          }}
        />
      </FormControl>
    );
  };

  const registerRequiredFields = (field: any, index: number) => {
    const brandIdValue =
      field.brandId !== null && field.brandId !== undefined ? field.brandId : 1;

    register(`prOrderList.${index}.rawMaterialId`, {
      value: field.rawMaterialId,
    });
    register(`prOrderList.${index}.brandId`, {
      value: brandIdValue,
    });
    register(`prOrderList.${index}.prOrderRawId`, {
      value: field.prOrderRawId,
    });
    register(`prOrderList.${index}.isJobDefault`, {
      value: field.isJobDefault,
    });
  };

  return (
    <>
      <ModalSelectJob
        open={openModalJob}
        handleClose={() => setOpenModalJob(false)}
        params={paramsModalSelectJob}
        onSelectJob={handleJobSelected}
      />
      <div className="p-8 flex flex-col gap-2">
        <ModalSelectMaterial
          open={openModalMaterial}
          onClose={handleCloseModalMaterial}
        />
        <div className="flex justify-between items-center mb-4">
          <span className="text-xl font-medium">รายการขอซื้อ</span>
          <div className="flex gap-2">
            <ActionButton
              variant="contained"
              color={'dark'}
              icon={<AddCircle />}
              text="เพิ่มรายการ"
              onClick={handleOpenModalMaterial}
            />
          </div>
        </div>

        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow>
                <TableCell align="left">#</TableCell>
                <TableCell align="left">รายการขอซื้อ</TableCell>
                <TableCell align="left">วัสดุ</TableCell>
                <TableCell align="left">รายการผลิต (อ้างอิง)</TableCell>
                <TableCell align="left">แบรนด์</TableCell>
                <TableCell align="left">จำนวน</TableCell>
                <TableCell align="right">จัดการ</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fields.map((field: any, index: number) => {
                registerRequiredFields(field, index);

                return (
                  <TableRow
                    key={field.id || index}
                    sx={
                      field.prOrderRawId !== null
                        ? { backgroundColor: 'rgba(232, 244, 253, 0.3)' }
                        : {}
                    }
                  >
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <div className="flex gap-4">
                        <div className="relative w-8 h-8">
                          <Image
                            src={field._imageUrl}
                            alt=""
                            width={40}
                            height={40}
                            className="object-cover rounded !min-w-[40px]"
                          />
                        </div>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {field._name || 'ไม่ระบุชื่อ'}
                            {field._subMaterialDetail?.side
                              ? ` • ${field._subMaterialDetail.side} ด้าน`
                              : ''}
                          </span>
                          <span className="text-sm text-gray-600">
                            {field._rawMaterialNo || '-'}
                            {field._itemSize?.name
                              ? ` • ${field._itemSize.name}`
                              : ''}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{field._materialName}</TableCell>
                    <TableCell>
                      {field.prOrderRawId !== null ? (
                        <FormControl fullWidth>
                          <Select
                            value={field._jobNo || ''}
                            displayEmpty
                            disabled={true}
                            renderValue={() => {
                              if (field._jobNo) return field._jobNo;
                              return 'ไม่มีรายการผลิต';
                            }}
                            sx={{
                              '.MuiOutlinedInput-notchedOutline': {
                                borderColor: '#ddd',
                              },
                              '.MuiSelect-select': {
                                color: '#555',
                              },
                            }}
                          >
                            <MenuItem value={field._jobNo || ''}>
                              {field._jobNo || 'ไม่มีรายการผลิต'}
                            </MenuItem>
                          </Select>
                        </FormControl>
                      ) : (
                        renderJobSelection(field, index)
                      )}
                    </TableCell>
                    <TableCell>
                      <FormControl sx={{ minWidth: 160 }}>
                        <Controller
                          name={`prOrderList.${index}.brandId`}
                          control={control}
                          defaultValue={field.brandId || 1}
                          render={({ field: inputField }) => (
                            <Select
                              {...inputField}
                              size="small"
                              displayEmpty
                              disabled={field.prOrderRawId !== null}
                              value={inputField.value || field.brandId || 1}
                              renderValue={(selected) => {
                                if (
                                  !Array.isArray(field._brands) ||
                                  field._brands.length === 0
                                ) {
                                  return 'ไม่มีแบรนด์';
                                }

                                const selectedBrand = field._brands.find(
                                  (b: any) => b && b.id === selected
                                );

                                return (
                                  selectedBrand?.name ||
                                  field._brands[0]?.name ||
                                  'ไม่ระบุแบรนด์'
                                );
                              }}
                              onChange={(e) => {
                                if (field.prOrderRawId !== null) {
                                  return;
                                }

                                const selectedBrandId = e.target.value;
                                const validBrandId =
                                  selectedBrandId !== null &&
                                  selectedBrandId !== undefined
                                    ? selectedBrandId
                                    : 1;

                                inputField.onChange(validBrandId);

                                const updatedField = {
                                  ...field,
                                  brandId: validBrandId,
                                };
                                update(index, updatedField);
                              }}
                            >
                              {Array.isArray(field._brands) &&
                              field._brands.length > 0 ? (
                                field._brands.map((brand: any) => (
                                  <MenuItem key={brand.id} value={brand.id}>
                                    {brand.name}
                                  </MenuItem>
                                ))
                              ) : (
                                <MenuItem value={1}>ไม่มีแบรนด์</MenuItem>
                              )}
                            </Select>
                          )}
                        />
                        <div className={'error-text'}>
                          {errors?.prOrderList?.[index]?.brandId?.message || ''}
                        </div>
                      </FormControl>
                    </TableCell>
                    <TableCell>
                      <TextField
                        {...register(`prOrderList.${index}.amount`, {
                          valueAsNumber: true,
                          value: field.amount || 0,
                          validate: (value: any) => {
                            if (
                              field.prOrderRawId !== null &&
                              field._originalJobQuantity !== undefined
                            ) {
                              return (
                                value >= field._originalJobQuantity ||
                                `ต้องมากกว่าหรือเท่ากับ ${field._originalJobQuantity}`
                              );
                            }
                            return true;
                          },
                        })}
                        defaultValue={field.amount || 0}
                        type="number"
                        fullWidth
                        error={!!errors?.prOrderList?.[index]?.amount}
                        helperText={
                          errors?.prOrderList?.[index]?.amount?.message
                        }
                        InputProps={{
                          inputProps: {
                            min:
                              field.prOrderRawId !== null &&
                              field._originalJobQuantity !== undefined
                                ? field._originalJobQuantity
                                : 0,
                          },
                        }}
                        onBlur={(e) => {
                          const valueAsString = e.target.value;
                          let newValue =
                            valueAsString === '' ? 0 : Number(valueAsString);

                          if (
                            field.prOrderRawId !== null &&
                            field._originalJobQuantity !== undefined
                          ) {
                            if (newValue < field._originalJobQuantity) {
                              newValue = field._originalJobQuantity;
                              e.target.value =
                                field._originalJobQuantity.toString();
                            }
                          }

                          setValue(`prOrderList.${index}.amount`, newValue, {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                          form.trigger(`prOrderList.${index}.amount`);

                          const updatedField = {
                            ...field,
                            amount: newValue,
                          };
                          update(index, updatedField);
                        }}
                        onChange={(e) => {
                          const valueAsString = e.target.value;
                          const newValue =
                            valueAsString === '' ? 0 : Number(valueAsString);

                          if (
                            field.prOrderRawId !== null &&
                            field._originalJobQuantity !== undefined
                          ) {
                            if (newValue < field._originalJobQuantity) {
                              form.setError(`prOrderList.${index}.amount`, {
                                type: 'validate',
                                message: `ต้องมากกว่าหรือเท่ากับ ${field._originalJobQuantity}`,
                              });
                            } else {
                              form.clearErrors(`prOrderList.${index}.amount`);
                            }
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell align="right">
                      <span>
                        <DeleteButton
                          onClick={() => remove(index)}
                          disabled={
                            field.isJobDefault || field.prOrderRawId !== null
                          }
                        >
                          <Image
                            src="/icons/delete-white.svg"
                            width={24}
                            height={24}
                            alt=""
                          />
                        </DeleteButton>
                      </span>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </>
  );
};

export default PurchaseRequestList;
