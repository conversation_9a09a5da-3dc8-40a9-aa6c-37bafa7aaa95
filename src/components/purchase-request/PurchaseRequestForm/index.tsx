import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { PurchaseRequestData } from '@/types/purchase-request';
import PurchaseRequestList from '@/components/purchase-request/PurchaseRequestForm/List';
import { CircularProgress, TextField } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useFieldArray, useForm } from 'react-hook-form';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import PurchaseRequestShowSupplier from '@/components/purchase-request/PurchaseRequestForm/ShowSuppliers';
import PurchaseRequestApproval from '@/components/purchase-request/PurchaseRequestForm/Approval';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

type Props = {
  prData: PurchaseRequestData | undefined;
};

type PrOrderApprovalForm = {
  approveUserId: number | null;
  roleApprovalStatusId: number;
  sort: number;
};

type FormValues = {
  jobRef: number;
  requestedDeliveryDate: number;
  isNotify: boolean;
  note: string;
  prOrderList: {
    rawMaterialId: number;
    brandId: number;
    amount: number;
  }[];
  prOrderApproval: PrOrderApprovalForm[];
};

export type PrOrderApprovalType = {
  roleApprovalStatusId: number;
  approveUserId: number | null;
  approveUserName: string | null;
  approveUserEmail: string | null;
  approveUserImage: string | null;
  sort: number;
  name: string;
  // department: string;
};

type SupplierType = {
  id: number;
  name?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  taxNumber?: string;
  taxAddress?: string;
  imageUrl?: string;
  contactType?: any;
  [key: string]: any;
};

const validationSchemaDraft = yup.object({
  prOrderList: yup
    .array()
    .of(
      yup.object().shape({
        brandId: yup.number().required('กรุณาเลือกแบรนด์'),
        amount: yup
          .number()
          .moreThan(0, 'กรุณาเพิ่มจำนวน')
          .required('กรุณาระบุจำนวน'),
      })
    )
    .min(1, 'กรุณาเพิ่มรายการ')
    .required('กรุณาเพิ่มรายการ'),
  prOrderApproval: yup
    .array()
    .of(
      yup.object({
        approveUserId: yup
          .number()
          .nullable()
          .transform((value) => {
            return value || 0;
          })
          .required('กรุณาเพิ่มผู้อนุมัติ'),
        roleApprovalStatusId: yup.number().required(),
        sort: yup.number().required(),
      })
    )
    .min(2, 'กรุณาเพิ่มผู้อนุมัติอย่างน้อย 2 คน')
    .required('กรุณาเพิ่มผู้อนุมัติ'),
});

const validationSchema = yup.object({
  prOrderList: yup
    .array()
    .of(
      yup.object().shape({
        brandId: yup.number().required('กรุณาเลือกแบรนด์'),
        amount: yup
          .number()
          .moreThan(0, 'กรุณาเพิ่มจำนวน')
          .typeError('กรุณากรอกจำนวน'),
      })
    )
    .min(1, 'กรุณาเพิ่มรายการ')
    .required('กรุณาเพิ่มรายการ'),
  prOrderApproval: yup
    .array()
    .of(
      yup.object({
        approveUserId: yup
          .number()
          .nullable()
          .transform((value) => {
            return value || 0;
          })
          .required('กรุณาเพิ่มผู้อนุมัติ'),
        roleApprovalStatusId: yup.number().required(),
        sort: yup.number().required(),
      })
    )
    .min(2, 'กรุณาเพิ่มผู้อนุมัติอย่างน้อย 2 คน')
    .required('กรุณาเพิ่มผู้อนุมัติ'),
});

const PurchaseRequestForm: React.FC<Props> = ({ prData }) => {
  const router = useRouter();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [submitDraft, setSubmitDraft] = useState<boolean>(false);
  const [prOrderApproval, setPrOrderApproval] = useState<PrOrderApprovalType[]>(
    []
  );
  const [formIsValid, setFormIsValid] = useState<boolean>(false);
  const [draftIsValid, setDraftIsValid] = useState<boolean>(false);

  const [currentSupplier, setCurrentSupplier] = useState<SupplierType | null>(
    null
  );

  const [supplierKey, setSupplierKey] = useState<number>(0);

  const form = useForm<FormValues>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      prOrderList: [],
      prOrderApproval: [],
    },
    mode: 'onChange',
  });

  const draftForm = useForm<FormValues>({
    resolver: yupResolver(validationSchemaDraft),
    mode: 'onChange',
  });

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = form;

  const fieldArray = useFieldArray({
    control,
    name: 'prOrderList',
  });

  useEffect(() => {
    if (prData?.contact) {
      setCurrentSupplier(prData.contact);
    }
  }, [prData]);

  useEffect(() => {
    setSupplierKey((prev) => prev + 1);
  }, [currentSupplier]);

  const fetchRoleApprovalStatus = async () => {
    const response = await apiPurchaseRequest.getListRoleApprove();
    if (!response.errors) {
      const approvalStatusList = response.data;
      const arr: PrOrderApprovalType[] = [];
      approvalStatusList.forEach((status: any) => {
        arr.push({
          roleApprovalStatusId: status.id,
          approveUserId: null,
          approveUserName: null,
          approveUserEmail: null,
          approveUserImage: null,
          sort: status.sort,
          name: status.name,
        });
      });
      setPrOrderApproval(arr);
    }
  };

  useEffect(() => {
    fetchRoleApprovalStatus();
    return () => {
      setPrOrderApproval([]);
    };
  }, []);

  useEffect(() => {
    const subscription = watch((formValues) => {
      const hasOrderItems =
        formValues.prOrderList && formValues.prOrderList.length > 0;

      const hasAllApprovers = prOrderApproval.every(
        (approval) => approval.approveUserId !== null
      );

      const allItemsValid =
        formValues.prOrderList && formValues.prOrderList.length > 0
          ? formValues.prOrderList.every(
              (item: any) => item.brandId && item.amount && item.amount > 0
            )
          : false;

      const hasMinimumApprovers = prOrderApproval.length >= 2;

      setFormIsValid(
        Boolean(
          hasOrderItems &&
            hasAllApprovers &&
            allItemsValid &&
            hasMinimumApprovers
        )
      );

      setDraftIsValid(Boolean(hasOrderItems && allItemsValid));
    });

    return () => subscription.unsubscribe();
  }, [watch, prOrderApproval]);

  const prepareFormData = (values: FormValues) => {
    const prOrderApprovalBody: PrOrderApprovalForm[] = [];
    prOrderApproval.forEach((approval) => {
      prOrderApprovalBody.push({
        approveUserId: approval.approveUserId,
        roleApprovalStatusId: approval.roleApprovalStatusId,
        sort: approval.sort,
      });
    });

    const prOrderListBody = values.prOrderList.map((item) => {
      const prOrderItem = (prData as any)?.prOrderList?.find(
        (prItem: any) =>
          prItem.rawMaterialId === item.rawMaterialId &&
          prItem.brandId === item.brandId
      );

      return {
        ...item,
        isJobDefault: prOrderItem?.isJobDefault || false,
      };
    });

    let finalContactId: number | null = null;

    if (currentSupplier && currentSupplier.id) {
      finalContactId = currentSupplier.id;
    } else if (prData && prData.contact && prData.contact.id) {
      finalContactId = prData.contact.id;
    } else {
      const lastSupplierId = localStorage.getItem('lastSupplierId');
      if (lastSupplierId) {
        finalContactId = Number(lastSupplierId);
      }
    }

    return {
      jobRef: values.jobRef,
      isNotify: values.isNotify || false,
      note: values.note,
      contactId: finalContactId,
      prOrderList: prOrderListBody,
      prOrderApproval: prOrderApprovalBody,
    };
  };

  useEffect(() => {
    setValue('prOrderApproval', prOrderApproval, {
      shouldValidate: true,
      shouldDirty: true,
    });

    draftForm.setValue('prOrderApproval', prOrderApproval);
  }, [prOrderApproval, setValue, draftForm]);

  useEffect(() => {
    const subscription = watch((value) => {
      draftForm.reset(value);
    });

    return () => subscription.unsubscribe();
  }, [watch, draftForm]);

  const handleUpdateSupplier = (updatedSupplier: SupplierType) => {
    const newSupplier: SupplierType = {
      id: updatedSupplier.id,
      name: updatedSupplier.name || '',
      email: updatedSupplier.email || '',
      phoneNumber: updatedSupplier.phoneNumber || '',
      taxNumber: updatedSupplier.taxNumber || '',
      taxAddress: updatedSupplier.taxAddress || '',
      imageUrl: updatedSupplier.imageUrl || '',
      contactType: updatedSupplier.contactType || null,
    };
    setCurrentSupplier(newSupplier);
  };

  const handleSubmitDraft = async () => {
    setSubmitDraft(true);

    const isValid = await draftForm.trigger();

    if (!isValid) {
      setSubmitDraft(false);
      return;
    }

    const values = getValues();
    const formData = prepareFormData(values);

    if (!formData.contactId) {
      setSubmitDraft(false);
      return;
    }
    const res = await apiPurchaseRequest.saveDraft(prData?.id, formData);
    if (!res.errors) {
      router.push(`/stock/purchase-request/update/${prData?.id}`);
    }
    setSubmitDraft(false);
  };

  async function onSubmit(values: FormValues) {
    const isApproved = prOrderApproval.every(
      (approval) => approval.approveUserId !== null
    );

    if (!isApproved) {
      return;
    }
    if (!values.prOrderList || values.prOrderList?.length === 0) {
      return;
    }
    setSubmitting(true);
    const formData = prepareFormData(values);
    if (!formData.contactId) {
      setSubmitting(false);
      return;
    }
    const res = await apiPurchaseRequest.updatePrOrder(prData?.id, formData);
    if (!res.errors) {
      router.push(`/stock/purchase-request/detail/${prData?.id}`);
    }
    setSubmitting(false);
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-2 my-4">
        <div className="flex justify-between mt-6 mr-6 ml-7 gap-x-9">
          <PurchaseRequestShowSupplier
            key={supplierKey}
            suppliers={
              currentSupplier
                ? [currentSupplier]
                : prData?.contact
                ? [prData.contact]
                : []
            }
            onUpdateSupplier={handleUpdateSupplier}
          />
          <div className="flex flex-col w-full ">
            {prData ? (
              <PurchaseRequestApproval
                approvalList={prOrderApproval}
                setPrOrderApproval={setPrOrderApproval}
                isNotify={form.watch('isNotify') || false}
                onNotifyChange={(e: boolean) => {
                  form.setValue('isNotify', e);
                }}
                prData={prData}
              />
            ) : null}
            {errors?.prOrderApproval &&
              errors?.prOrderApproval.length &&
              errors?.prOrderApproval[0]?.approveUserId?.message && (
                <div className="error-text">
                  {errors.prOrderApproval[0].approveUserId?.message}
                </div>
              )}
          </div>
        </div>
        <PurchaseRequestList
          fieldArray={fieldArray}
          form={form}
          sentPrData={prData}
          errors={errors}
        />
        <div className="grid grid-cols-2 px-8 py-4 gap-6">
          <div className="col-span-1" />
          <div className="flex flex-col w-full">
            <div className="flex flex-col gap-2 flex-grow">
              <span className="font-medium">หมายเหตุ</span>
              <TextField
                {...register('note')}
                multiline
                rows={6}
                placeholder="หมายเหตุ"
                sx={{ height: '100%' }}
              />
            </div>
            <div className="grid grid-cols-2 gap-3 mt-4 w-full">
              <LoadingButton
                type="button"
                disabled={submitDraft || !draftIsValid}
                variant="outlined"
                color={draftIsValid ? 'blueGrey' : 'inherit'}
                onClick={handleSubmitDraft}
                sx={{
                  fontWeight: '600',
                  fontSize: '16px',
                  opacity: draftIsValid ? 1 : 0.6,
                }}
              >
                {submitDraft ? (
                  <CircularProgress size={25} style={{ color: 'white' }} />
                ) : (
                  'บันทึกฉบับร่าง'
                )}
              </LoadingButton>
              <LoadingButton
                type="submit"
                disabled={submitting || !formIsValid}
                variant="contained"
                color={formIsValid ? 'dark' : 'inherit'}
                sx={{
                  fontWeight: '600',
                  fontSize: '16px',
                  opacity: formIsValid ? 1 : 0.6,
                }}
              >
                {submitting ? (
                  <CircularProgress size={25} style={{ color: 'white' }} />
                ) : (
                  'บันทึก และดำเนินการต่อ'
                )}
              </LoadingButton>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
};

export default PurchaseRequestForm;
