import React, { useEffect, useState } from 'react';
import { IOSSwitchCustom } from '@/styles/share.styled';
import { PurchaseCardStyled } from '@/styles/pr.styled';
import { Avatar, Divider, IconButton } from '@mui/material';
import { Add } from '@mui/icons-material';
import ModalPrList from '@/components/purchase-request/PurchaseRequestForm/ModalPrList';
import { PrOrderApprovalType } from '@/components/purchase-request/PurchaseRequestForm/index';
import _ from 'lodash';

type Props = {
  approvalList: PrOrderApprovalType[];
  setPrOrderApproval: any;
  prData: any;
  isNotify: boolean;
  onNotifyChange: (value: boolean) => void;
};

const initialApprovalSelect = {
  approveUserEmail: null,
  approveUserId: null,
  approveUserName: null,
  approveUserImage: null,
  name: '',
  roleApprovalStatusId: 0,
  sort: 0,
} as PrOrderApprovalType;

const PurchaseRequestApproval: React.FC<Props> = ({
  approvalList,
  setPrOrderApproval,
  prData,
  isNotify,
  onNotifyChange,
}) => {
  const [approvalSelect, setApprovalSelect] = useState<PrOrderApprovalType>(
    initialApprovalSelect
  );

  const [displayApprovals, setDisplayApprovals] = useState<
    PrOrderApprovalType[]
  >([]);

  const [openModal, setOpenModal] = useState<boolean>(false);

  useEffect(() => {
    const approvals = approvalList.map((item: any) => {
      return {
        roleApprovalStatusId: item.roleApprovalStatusId,
        approveUserId: item.approveUserId,
        approveUserName: item.approveUserName,
        approveUserEmail: item.approveUserEmail,
        approveUserImage: item.approveUserImage,
        sort: item.sort,
        name: item.name,
      };
    });
    setDisplayApprovals(approvals);
  }, [approvalList]);

  const findApproval = (roleApprovalStatusId: number) => {
    if (prData?.prOrderApprovals?.length > 0) {
      const approval = prData.prOrderApprovals.find(
        (item: any) => item.role?.id === roleApprovalStatusId
      );
      return approval;
    }
  };

  useEffect(() => {
    const approvals = approvalList.map((item: any) => {
      const approval = findApproval(item.roleApprovalStatusId);
      return {
        roleApprovalStatusId: item.roleApprovalStatusId,
        approveUserId: item.approveUserId || approval?.userApprove?.id || null,
        approveUserName:
          item.approveUserName || approval?.userApprove?.name || null,
        approveUserEmail:
          item.approveUserEmail || approval?.userApprove?.email || null,
        approveUserImage:
          item.approveUserImage || approval?.userApprove?.image || null,
        sort: item.sort,
        name: item.name,
      };
    });
    setDisplayApprovals(approvals);
  }, [prData, approvalList]);

  const onClick = (approval: PrOrderApprovalType) => {
    setApprovalSelect({ ...approval });
    setOpenModal(true);
  };

  const onClose = () => {
    setOpenModal(false);
  };

  const handleApprovalUpdate = (newApproval: PrOrderApprovalType) => {
    const updateDisplayApprovals = _.map(displayApprovals, (item: any) =>
      item.roleApprovalStatusId === newApproval.roleApprovalStatusId
        ? {
            ...item,
            approveUserEmail: newApproval.approveUserEmail,
            approveUserId: newApproval.approveUserId,
            approveUserName: newApproval.approveUserName,
          }
        : item
    );
    setDisplayApprovals(updateDisplayApprovals);
    setPrOrderApproval(updateDisplayApprovals);
    setOpenModal(false);
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between items-center">
        <span className="font-medium text-xl">ข้อมูลการอนุมัติ</span>
        <div className="flex gap-1 items-center">
          การแจ้งเตือนอีเมล
          <IOSSwitchCustom
            checked={isNotify}
            onChange={(_, checked) => onNotifyChange(checked)}
          />
        </div>
      </div>
      <PurchaseCardStyled>
        {displayApprovals && displayApprovals.length > 0 ? (
          displayApprovals.map((approval: any, index: number) => (
            <div key={`approval-${approval.roleApprovalStatusId}-${index}`}>
              <div className="flex justify-between items-center mr-4">
                <div className="flex gap-2 items-center p-4">
                  <div>
                    <Avatar sx={{ width: '40px', height: '40px' }} />
                  </div>
                  <div className="flex flex-col">
                    <span className="font-medium">
                      {approval.approveUserName ? (
                        <>
                          {approval.approveUserName}
                          <span> • </span>
                          {approval.name}
                        </>
                      ) : (
                        <>
                          <span className="">กรุณาเลือกผู้อนุมัติ</span>
                          <span> • </span>
                          {approval.name}
                        </>
                      )}
                    </span>
                    <span className="text-sm">
                      {approval.approveUserEmail || 'ยังไม่มีอีเมล'}
                    </span>
                  </div>
                </div>
                <div>
                  <IconButton
                    className="rounded-lg"
                    size="small"
                    style={{
                      background: approval.approveUserId ? '#666' : '#3b82f6',
                      color: 'white',
                    }}
                    onClick={() => onClick(approval)}
                  >
                    <Add />
                  </IconButton>
                </div>
              </div>
              {index + 1 !== displayApprovals.length && <Divider />}
            </div>
          ))
        ) : (
          <div className="p-4 text-center text-gray-500">
            ไม่พบข้อมูลการอนุมัติ
          </div>
        )}
      </PurchaseCardStyled>
      {openModal && (
        <ModalPrList
          open={openModal}
          approvalSelect={approvalSelect}
          prOrderApproval={displayApprovals}
          setPrOrderApproval={(newApproval: PrOrderApprovalType) => {
            handleApprovalUpdate(newApproval);
          }}
          onClose={onClose}
        />
      )}
    </div>
  );
};

export default PurchaseRequestApproval;
