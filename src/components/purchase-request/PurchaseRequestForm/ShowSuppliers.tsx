import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Avatar } from '@mui/material';
import ModalSuppliers from '@/components/purchase-request/PurchaseRequestForm/ModalSuppliers';

const PurchaseRequestShowSuppliersStyle = styled.div`
  width: 100%;
  .border-group {
    max-width: 100%;
    margin-top: 24px;
    border: 1px solid #f5f7f8;
    background-color: #f5f7f8;
    height: 150px;
    border-radius: 16px;
    padding: 16px 18px;
  }
  .text-group {
    .border-detail {
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      height: 48px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      .inside-border {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .change {
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .detail-text {
      margin-top: 12px;
    }
  }
`;

type ContactOrSupplier = {
  id: number;
  contactType?: any;
  name?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  taxNumber?: string;
  taxAddress?: string;
  imageUrl?: string;
  [key: string]: any;
};

type Props = {
  suppliers: ContactOrSupplier[];
  onUpdateSupplier?: (supplier: any) => void;
};

const PurchaseRequestShowSupplier: React.FC<Props> = ({
  suppliers,
  onUpdateSupplier,
}: Props) => {
  const [openModalChange, setOpenModalChange] = useState<boolean>(false);
  const [currentSupplier, setCurrentSupplier] =
    useState<ContactOrSupplier | null>(null);

  useEffect(() => {
    if (suppliers && suppliers.length > 0) {
      setCurrentSupplier(suppliers[0]);
    }
  }, [suppliers]);

  const handleChange = () => {
    setOpenModalChange(true);
  };

  const handleSubmitChange = (newSupplier: any) => {
    const updatedSupplier = {
      ...newSupplier,
      id: newSupplier.id,
      name:
        newSupplier.name ||
        `${newSupplier.firstName || ''} ${newSupplier.lastName || ''}`.trim(),
      email: newSupplier.email,
      phoneNumber: newSupplier.phoneNumber,
      taxNumber: newSupplier.taxNumber,
      taxAddress: newSupplier.taxAddress,
      imageUrl: newSupplier.imageUrl,
      contactType: newSupplier.contactType,
    };
    setCurrentSupplier(updatedSupplier);

    if (onUpdateSupplier) {
      onUpdateSupplier(updatedSupplier);
    }
    setOpenModalChange(false);
  };

  const displaySupplier =
    currentSupplier ||
    (suppliers && suppliers.length > 0 ? suppliers[0] : null);

  const getDisplayName = (supplier: ContactOrSupplier | null) => {
    if (!supplier) return '';
    if (supplier.name) return supplier.name;
    return `${supplier.firstName || ''} ${supplier.lastName || ''}`.trim();
  };

  return (
    <>
      <ModalSuppliers
        open={openModalChange}
        handleClose={() => setOpenModalChange(false)}
        onSubmit={handleSubmitChange}
        isChange={true}
        initialSelectedSupplier={displaySupplier}
      />
      <PurchaseRequestShowSuppliersStyle>
        <div className="">
          <span className="text-xl font-medium">ตัวแทนจำหน่าย</span>
        </div>
        <div className="border-group">
          {displaySupplier ? (
            <div className="text-group">
              <div className="border-detail">
                <div className="inside-border">
                  <Avatar
                    src={displaySupplier.imageUrl}
                    sx={{
                      height: '24px',
                      width: '24px',
                    }}
                  />
                  {getDisplayName(displaySupplier)}
                </div>
                <div className="change" onClick={handleChange}>
                  เปลี่ยน
                </div>
              </div>
              <div className="detail-text">
                <div className="flex gap-2">
                  <div>{displaySupplier.contactType?.name || ''}</div>
                  <span>•</span>
                  <div>
                    เลขประจำตัวผู้เสียภาษี {displaySupplier.taxNumber || '-'}
                  </div>
                </div>
                <div className="flex gap-2">
                  <div>โทร. {displaySupplier.phoneNumber || '-'}</div>
                  <span>•</span>
                  <div>{displaySupplier.email || '-'}</div>
                </div>
                <div>{displaySupplier.taxAddress || '-'}</div>
              </div>
            </div>
          ) : (
            <div className="text-group">ไม่มีตัวแทนจำหน่าย</div>
          )}
        </div>
      </PurchaseRequestShowSuppliersStyle>
    </>
  );
};

export default PurchaseRequestShowSupplier;
