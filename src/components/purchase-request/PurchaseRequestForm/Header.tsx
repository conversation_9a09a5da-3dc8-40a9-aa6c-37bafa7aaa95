import React from 'react';
import { PurchaseRequestData } from '@/types/purchase-request';
import { getPrStatusData } from '@/utils/helper';

type Props = {
  prData: PurchaseRequestData | undefined;
};

const PurchaseRequestHeader: React.FC<Props> = ({ prData }) => {
  return (
    <div className="px-8 py-4">
      <div className="flex gap-2 items-center">
        <span className="font-bold text-[36px]">{prData?.prOrderNo}</span>
        <div className="p-2 border-2 border-solid border-gray-400 bg-gray-200 rounded-md">
          {getPrStatusData(prData?.status).name}
        </div>
      </div>
    </div>
  );
};

export default PurchaseRequestHeader;
