import React, { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import {
  Button,
  FormControl,
  IconButton,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import {
  Add as AddIcon,
  ArrowDropDown,
  QuestionMark,
} from '@mui/icons-material';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import { useRouter } from 'next/router';
import {
  confirmModalInit,
  ConfirmModalQuotationType,
} from '@/pages/accounting-finance/quotation/[quotationId]';
import ConfirmModal from '@/components/global/ConfirmModal';
import { purchaseRequestSelector } from '@/store/features/purchase-request';
import apiPurchaseRequest from '@/services/stock/purchase-request';
import { PrOrderApprovalType } from '@/components/purchase-request/PurchaseRequestForm';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PurchaseReqBody,
} from '@/store/features/purchase-request/types';
import Image from 'next/image';
import { NumericFormat } from 'react-number-format';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';
import _ from 'lodash';
import SvgPlusIcon from '@/components/svg-icon/SvgPlusIcon';
import ModalSuppliers from '@/components/purchase-request/PurchaseRequestForm/ModalSuppliers';
import ModalPrList from '@/components/purchase-request/PurchaseRequestForm/ModalPrList';
import ModalAddMaterial from '@/components/purchase-request/modal/ModalAddMaterial';
import ModalSelectPrOrderRaw from '@/components/purchase-request/modal/ModalSelectPrOrderRaw';
import SwitchPure from '@/components/global/SwitchPure';
import { setSnackBar } from '@/store/features/alert';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

interface PrOrderFormData {
  jobRef: number;
  isNotify: boolean;
  note: string;
  contactId: number;
  prOrderList: {
    rawMaterialId: number;
    brandId: number;
    amount: number;
    prOrderRawId: number | null;
    isJobDefault: boolean;
    _jobNo?: any;
    _name?: any;
    _rawMaterialNo?: any;
    _imageUrl?: any;
    _materialName?: any;
    _subMaterialDetail?: any;
    _itemSize?: any;
    _brands?: any;
  }[];
  prOrderApproval: {
    approveUserId: number;
    roleApprovalStatusId: number;
    sort: number;
  }[];
}

const initialApprovalSelect = {
  approveUserEmail: null,
  approveUserId: null,
  approveUserName: null,
  approveUserImage: null,
  name: '',
  roleApprovalStatusId: 0,
  sort: 0,
} as PrOrderApprovalType;

const PrUpdateForm = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { prOrderId } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  const { purchaseReq } = useAppSelector(purchaseRequestSelector);
  const [prOrderApproval, setPrOrderApproval] = useState<PrOrderApprovalType[]>(
    []
  );
  const { register, control, handleSubmit, watch, setValue, reset } =
    useForm<PrOrderFormData>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'prOrderList',
  });
  watch();
  const [jobSelector, setJobSelector] = useState<{
    index: number | null;
    visible: boolean;
    prOrderRaw: any;
  }>({
    index: null,
    visible: false,
    prOrderRaw: null,
  });
  const [confirmModal, setConfirmModal] =
    useState<ConfirmModalQuotationType>(confirmModalInit);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [contacts, setContacts] = useState<any>({});
  const [isApproval, setIsApproval] = useState<boolean>(false);

  useEffect(() => {
    checkApproval();
  }, [prOrderApproval]);

  const checkApproval = () => {
    const noneApprove = _.some(
      prOrderApproval,
      (item: any) => item.approveUserId === null
    );
    setIsApproval(!noneApprove);
  };

  const onCloseConfirmModal = () => {
    setConfirmModal({ ...confirmModal, open: false });
    setTimeout(() => {
      setConfirmModal({ ...confirmModalInit });
      setConfirmLoading(false);
    }, 500);
  };

  useEffect(() => {
    if (purchaseReq && purchaseReq.detail) {
      reset({
        jobRef: 0,
        isNotify: purchaseReq.detail.isNotify,
        note: purchaseReq.detail.note,
        contactId: purchaseReq.detail.contact.id,
        prOrderList: purchaseReq.detail.prOrderLists.map(
          (item: PrOrderList) => {
            return {
              rawMaterialId: item.rawMaterial.id,
              brandId: item.brand.id,
              amount: item.amount,
              prOrderRawId: item.prOrderRawId || null,
              isJobDefault: item.isJobDefault || false,

              _jobNo: item.jobNo || null,
              _name: item.rawMaterial.name || '',
              _rawMaterialNo: item.rawMaterial.rawMaterialNo || '',
              _imageUrl: item.rawMaterial.imageUrl || '/icons/icon-default.png',
              _materialName: item.rawMaterial.material?.name || '-',
              _subMaterialDetail: item.rawMaterial.subMaterialDetail || null,
              _itemSize: item.rawMaterial.itemSize || null,
              _brands: item.brand ? [item.brand] : [],
            };
          }
        ),
        prOrderApproval: purchaseReq.detail.prOrderApprovals.map(
          (item: any) => {
            return {
              approveUserId: item.userApprove.id,
              roleApprovalStatusId: item.status,
              sort: item.role.sort,
            };
          }
        ),
      });
      setContacts(purchaseReq.detail.contact);
      if (purchaseReq.detail.prOrderApprovals.length > 0) {
        const arr: PrOrderApprovalType[] = [];
        purchaseReq.detail.prOrderApprovals.forEach((approve: any) => {
          arr.push({
            roleApprovalStatusId: approve.role.id,
            approveUserId: approve.userApprove.id,
            approveUserName: approve.userApprove.name,
            approveUserEmail: approve.userApprove.email,
            approveUserImage: approve.userApprove.imageUrl,
            sort: approve.role.sort,
            name: approve.role.name,
          });
        });
        setPrOrderApproval(arr);
      } else {
        fetchRoleApprovalStatus();
      }
    } else {
      fetchRoleApprovalStatus();
    }
  }, [purchaseReq, reset]);

  const fetchRoleApprovalStatus = async () => {
    const response = await apiPurchaseRequest.getListRoleApprove();
    if (!response.errors) {
      const approvalStatusList = response.data;
      const arr: PrOrderApprovalType[] = [];
      approvalStatusList.forEach((status: any) => {
        arr.push({
          roleApprovalStatusId: status.id,
          approveUserId: null,
          approveUserName: null,
          approveUserEmail: null,
          approveUserImage: null,
          sort: status.sort,
          name: status.name,
        });
      });
      setPrOrderApproval(arr);
    }
  };

  const onSubmit = async (
    data: PrOrderFormData,
    type: 'continue' | 'draft'
  ) => {
    const payload: PurchaseReqBody = data;
    // payload.contactId = purchaseReq?.detail?.contact?.id;
    payload.contactId = contacts?.id;
    payload.prOrderApproval = prOrderApproval.map((item: any) => {
      return {
        approveUserId: item.approveUserId,
        roleApprovalStatusId: item.roleApprovalStatusId,
        sort: item.sort,
      };
    });
    payload.prOrderList = data.prOrderList.map((order: any) => {
      return {
        rawMaterialId: order.rawMaterialId,
        brandId: order.brandId,
        amount: convert2numeric(order.amount),
        prOrderRawId: order.prOrderRawId,
        isJobDefault: order.isJobDefault,
      };
    });
    setConfirmModal({
      ...confirmModal,
      open: true,
      title: `ยืนยัน${
        type === 'continue' ? 'บันทึก และดำเนินการต่อ' : 'บันทึกแบบร่าง'
      }`,
      description:
        type === 'continue'
          ? `คุณได้ตรวจสอบข้อมูลใบขอซื้อ "${purchaseReq?.detail?.prOrderNo}" เรียบร้อยแล้ว\n` +
            `จะทำการบันทึกและดำเนินการเปลี่ยนสถานะเป็น "รออนุมัติใบขอซื้อ"\n` +
            `ในขั้นตอนต่อไป`
          : `คุณได้ตรวจสอบข้อมูลใบขอซื้อ "${purchaseReq?.detail?.prOrderNo}" เรียบร้อยแล้ว\n` +
            `จะทำการบันทึกแบบร่าง และไม่มีการเปลี่ยนสถานะ\n`,
      iconElement: () => <QuestionMark />,
      confirmLabel: type === 'continue' ? 'ส่งคำขอ' : 'ยืนยัน',
      confirmAction: async () => {
        setConfirmLoading(true);
        if (type === 'draft') {
          const res = await apiPurchaseRequest.saveDraft(
            Number(prOrderId),
            payload
          );
          if (res.status) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'บันทึกแบบร่างสำเร็จ',
                severity: 'success',
              })
            );
            setTimeout(() => {
              router.push(`/stock/purchase-request`);
              setConfirmLoading(false);
              setJobSelector({ index: null, visible: false, prOrderRaw: null });
            }, 1000);
          }
        }
        if (type === 'continue') {
          const res = await apiPurchaseRequest.updatePrOrder(
            Number(prOrderId),
            payload
          );
          if (res.status) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'บันทึกและขออนุมัติใบเสนอราคาสำเร็จ',
                severity: 'success',
              })
            );
            setTimeout(() => {
              router.push(`/stock/purchase-request/${res.data.id}`);
              setConfirmLoading(false);
              setJobSelector({ index: null, visible: false, prOrderRaw: null });
            }, 1000);
          }
        }
      },
    });
  };

  const convert2numeric = (value: any) => {
    return Number(String(value).replace(/,/g, ''));
  };

  const [formModal, setFormModal] = useState<{
    supplier: boolean;
    approval: boolean;
    material: boolean;
  }>({
    supplier: false,
    approval: false,
    material: false,
  });

  // supplier
  const handleSupplierUpdate = (newSupplier: any) => {
    const updatedSupplier = {
      ...newSupplier,
      id: newSupplier.id,
      name:
        newSupplier.name ||
        `${newSupplier.firstName || ''} ${newSupplier.lastName || ''}`.trim(),
      email: newSupplier.email,
      phoneNumber: newSupplier.phoneNumber,
      taxNumber: newSupplier.taxNumber,
      taxAddress: newSupplier.taxAddress,
      imageUrl: newSupplier.imageUrl,
      contactType: newSupplier.contactType,
    };
    setContacts({ ...contacts, ...updatedSupplier });
    // dispatch(
    //   setPurchaseRequest({
    //     ...purchaseReq,
    //     detail: {
    //       ...purchaseReq.detail,
    //       contact: { ...purchaseReq.detail.contact, ...updatedSupplier },
    //     },
    //   })
    // );
  };

  // approval
  const [approvalSelect, setApprovalSelect] = useState<PrOrderApprovalType>(
    initialApprovalSelect
  );

  const handleApprovalUpdate = (newApproval: PrOrderApprovalType) => {
    const updateDisplayApprovals = _.map(prOrderApproval, (item: any) =>
      item.roleApprovalStatusId === newApproval.roleApprovalStatusId
        ? {
            ...item,
            approveUserEmail: newApproval.approveUserEmail,
            approveUserId: newApproval.approveUserId,
            approveUserName: newApproval.approveUserName,
          }
        : item
    );
    setPrOrderApproval(updateDisplayApprovals);
    setFormModal({ ...formModal, approval: false });
  };

  const onAppendMaterial = (list: any[]) => {
    list.map((item: any) => {
      append({
        rawMaterialId: item.id,
        brandId: item.brand[0].id || 1,
        amount: 1,
        prOrderRawId: null,
        isJobDefault: false,
        _jobNo: '',
        _name: item.name || '',
        _rawMaterialNo: item.rawMaterialNo || '',
        _imageUrl: item.imageUrl || '/icons/icon-default.png',
        _materialName: item.material?.name || '-',
        _subMaterialDetail: item.subMaterialDetail || null,
        _itemSize: item.itemSize || null,
        _brands: item.brand
          ? Array.isArray(item.brand)
            ? item.brand
            : [item.brand]
          : [],
      });
    });
  };

  return (
    <PurchaseRequestFormStyled>
      <ConfirmModal
        open={confirmModal.open}
        handleCloseModal={onCloseConfirmModal}
        loading={confirmLoading}
        title={confirmModal.title}
        description={confirmModal.description}
        iconElement={confirmModal.iconElement}
        confirmLabel={confirmModal.confirmLabel}
        confirmAction={confirmModal.confirmAction}
      />
      <ModalSuppliers
        open={formModal.supplier}
        handleClose={() => setFormModal({ ...formModal, supplier: false })}
        onSubmit={handleSupplierUpdate}
        isChange={true}
        initialSelectedSupplier={contacts}
      />
      <ModalPrList
        open={formModal.approval}
        approvalSelect={approvalSelect}
        prOrderApproval={prOrderApproval}
        setPrOrderApproval={(newApproval: PrOrderApprovalType) => {
          handleApprovalUpdate(newApproval);
        }}
        onClose={() => setFormModal({ ...formModal, approval: false })}
      />
      <ModalAddMaterial
        open={formModal.material}
        handleCloseModal={() => setFormModal({ ...formModal, material: false })}
        onSubmit={(selected: any[]) => {
          onAppendMaterial(selected);
        }}
      />
      <ModalSelectPrOrderRaw
        open={jobSelector.visible}
        handleCloseModal={() => {
          setJobSelector({ index: null, visible: false, prOrderRaw: null });
        }}
        data={jobSelector.prOrderRaw}
        onSubmit={(data) => {
          if (jobSelector.index !== null) {
            setValue(`prOrderList.${jobSelector.index}.prOrderRawId`, data.id);
            setValue(`prOrderList.${jobSelector.index}._jobNo`, data.jobNo);
          }
        }}
      />
      <form
        onSubmit={handleSubmit((data: PrOrderFormData) =>
          onSubmit(data, 'continue')
        )}
      >
        <div className="form-section customer-info">
          <div className="section-title">ตัวแทนจำหน่าย</div>
          <div className={'customer-info-content'}>
            <div className={'customer-title'}>
              <div className={'avatar'}>
                <Image
                  src={
                    contacts?.imageUrl || '/images/product/empty-product.svg'
                  }
                  width={24}
                  height={24}
                  alt=""
                  style={{
                    borderRadius: '50%',
                    objectFit: 'cover',
                  }}
                />
              </div>
              <div className={'name'}>{contacts?.name}</div>
              <div
                className={'change-label'}
                onClick={() => setFormModal({ ...formModal, supplier: true })}
              >
                เปลี่ยน
              </div>
            </div>
            <div className={'customer-detail'}>
              <div className={'text'}>
                {`${contacts?.contactType?.name} • เลขประจำตัวผู้เสียภาษี ${contacts?.taxNumber}`}
              </div>
              <div className={'text'}>
                {`โทร ${contacts?.phoneNumber} • ${contacts?.email}`}
              </div>
              <div className={'text'}>
                {`${contacts?.taxAddress} ${contacts?.subDistrict?.name} ${contacts?.district?.name} ${contacts?.province?.name} ${contacts?.contact?.zipcode}`}
              </div>
            </div>
          </div>
        </div>
        <div className="form-section pr-info">
          <div className="section-title flex items-center justify-between">
            <span>ข้อมูลการอนุมัติ</span>
            <div className={'flex items-center gap-2 text-[14px] font-[400]'}>
              <span>การแจ้งเตือนอีเมล</span>
              <SwitchPure
                checked={watch('isNotify')}
                onChange={(checked) => setValue('isNotify', checked)}
              />
            </div>
          </div>
          <div className={'pr-info-content'}>
            {prOrderApproval &&
              prOrderApproval.map((item: any, index) => {
                return (
                  <div
                    key={index}
                    className={`content-item ${index === 0 && 'bd'}`}
                  >
                    <div className={'approval'}>
                      <div className={'avatar'}>
                        <Image
                          src={
                            item.approveUserImage ||
                            '/images/product/empty-product.svg'
                          }
                          width={40}
                          height={40}
                          alt=""
                          style={{
                            borderRadius: '50%',
                            objectFit: 'cover',
                          }}
                        />
                      </div>
                      <div className={'title-info'}>
                        <div className={'name'}>
                          {`${item.approveUserName || ''} ${
                            item.approveUserName ? ` • ` : ''
                          } ${
                            item.sort === 1
                              ? 'ผู้อนุมัติขั้นแรก'
                              : 'ผู้อนุมัติขั้นสูง'
                          }`}
                        </div>
                        <div className={'detail'}>
                          {item.approveUserEmail ? (
                            <>{`${item.name} • ${item.approveUserEmail}`}</>
                          ) : (
                            <div className={'text-gray-400'}>
                              {`ยังไม่ได้เลือกผู้อนุมัติใบขอซื้อขั้น${
                                item.sort === 1 ? 'แรก' : 'สูง'
                              }`}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {isAllowed(
                      permissions,
                      'stock.purchase-request.update'
                    ) && (
                      <>
                        {item.approveUserName ? (
                          <div
                            className={'approve-btn edit'}
                            onClick={() => {
                              setApprovalSelect(item);
                              setFormModal({ ...formModal, approval: true });
                            }}
                          >
                            <SvgEditIcon />
                          </div>
                        ) : (
                          <div
                            className={'approve-btn add'}
                            onClick={() => {
                              setApprovalSelect(item);
                              setFormModal({ ...formModal, approval: true });
                            }}
                          >
                            <SvgPlusIcon />
                          </div>
                        )}
                      </>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
        <div className="form-section ld-list">
          <div className={'flex items-center justify-between'}>
            <div className="section-title">รายการขอซื้อ</div>
            <Button
              type="button"
              variant="contained"
              color="dark"
              startIcon={<AddIcon />}
              disabled={
                !isAllowed(permissions, 'stock.purchase-request.update')
              }
              onClick={() => {
                setFormModal({ ...formModal, material: true });
              }}
            >
              เพิ่มรายการ
            </Button>
          </div>
          <TableWrapper>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                '& .MuiTable-root': {
                  borderCollapse: 'separate',
                  borderSpacing: '0px',
                },
                '& .MuiTableHead-root .MuiTableRow-root': {
                  height: '32px',
                },
                '& .MuiTableHead-root .MuiTableCell-root': {
                  padding: '4px 16px',
                  fontSize: '12px',
                },
                '& .MuiTableBody-root .MuiTableRow-root': {
                  height: '72px',
                },
              }}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell align="center" sx={{ width: '50px' }}>
                      #
                    </TableCell>
                    <TableCell>รายการขอซื้อ</TableCell>
                    <TableCell align="center">วัสดุ</TableCell>
                    <TableCell align="center" sx={{ width: '220px' }}>
                      รายการสั่งผลิต (อ้างอิง)
                    </TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      แบรนด์
                    </TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      จำนวน
                    </TableCell>
                    <TableCell align="center" sx={{ width: '100px' }}>
                      จัดการ
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fields.length > 0 &&
                    fields.map((field, index) => (
                      <TableRow key={field.id}>
                        <TableCell align="center">{index + 1}</TableCell>
                        <TableCell>
                          <div>
                            <div className="flex gap-4">
                              {/* <div className="relative w-8 h-8"> */}
                              {/*  <Image */}
                              {/*    src={field._imageUrl} */}
                              {/*    alt="" */}
                              {/*    width={40} */}
                              {/*    height={40} */}
                              {/*    className="object-cover rounded !min-w-[40px]" */}
                              {/*  /> */}
                              {/* </div> */}
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {field._name || 'ไม่ระบุชื่อ'}
                                  {field._subMaterialDetail?.side
                                    ? ` • ${field._subMaterialDetail.side} ด้าน`
                                    : ''}
                                </span>
                                <span className="text-sm text-gray-600">
                                  {field._rawMaterialNo || '-'}
                                  {field._itemSize?.name
                                    ? ` • ${field._itemSize.name}`
                                    : ''}
                                </span>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell align="center">
                          {field._materialName}
                        </TableCell>
                        <TableCell align="center">
                          {field.prOrderRawId !== null ? (
                            <FormControl fullWidth>
                              <Select
                                value={field._jobNo || ''}
                                displayEmpty
                                disabled={
                                  !isAllowed(
                                    permissions,
                                    'stock.purchase-request.update'
                                  )
                                }
                                renderValue={() => {
                                  if (field._jobNo) return field._jobNo;
                                  return 'ไม่มีรายการผลิต';
                                }}
                                sx={{
                                  '.MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#ddd',
                                  },
                                  '.MuiSelect-select': {
                                    color: '#555',
                                  },
                                }}
                              >
                                <MenuItem value={field._jobNo || ''}>
                                  {field._jobNo || 'ไม่มีรายการผลิต'}
                                </MenuItem>
                              </Select>
                            </FormControl>
                          ) : (
                            <JobNoFormBtn
                              onClick={() =>
                                setJobSelector({
                                  index: index,
                                  visible: true,
                                  prOrderRaw: field,
                                })
                              }
                            >
                              <span
                                className={
                                  watch(`prOrderList.${index}._jobNo`)
                                    ? 'text-black'
                                    : 'text-[#CFD8DC]'
                                }
                              >
                                {watch(`prOrderList.${index}._jobNo`) ||
                                  'เลือกรายการผลิต'}
                              </span>
                              <ArrowDropDown />
                            </JobNoFormBtn>
                            //       <FormControl fullWidth>
                            //       <Select
                            //       displayEmpty
                            //       renderValue={() => {
                            //       if (field._jobNo) return field._jobNo;
                            //       return 'ไม่มีรายการผลิต';
                            //     }}
                            //     sx={{
                            //       '.MuiOutlinedInput-notchedOutline': {
                            //         borderColor: '#ddd',
                            //       },
                            //       '.MuiSelect-select': {
                            //         color: '#555',
                            //       },
                            //     }}
                            //   ></Select>
                            // </FormControl>
                          )}
                        </TableCell>
                        <TableCell align="right">
                          <FormControl sx={{ minWidth: 160 }}>
                            <Controller
                              name={`prOrderList.${index}.brandId`}
                              control={control}
                              defaultValue={field.brandId || 1}
                              disabled={
                                field.prOrderRawId !== null ||
                                !isAllowed(
                                  permissions,
                                  'stock.purchase-request.update'
                                )
                              }
                              render={({ field: inputField }) => (
                                <Select
                                  {...inputField}
                                  size="small"
                                  displayEmpty
                                  value={inputField.value || field.brandId || 1}
                                  renderValue={(selected) => {
                                    if (
                                      !Array.isArray(field._brands) ||
                                      field._brands.length === 0
                                    ) {
                                      return 'ไม่มีแบรนด์';
                                    }

                                    const selectedBrand = field._brands.find(
                                      (b: any) => b && b.id === selected
                                    );

                                    return (
                                      selectedBrand?.name ||
                                      field._brands[0]?.name ||
                                      'ไม่ระบุแบรนด์'
                                    );
                                  }}
                                  onChange={(e) => {
                                    if (field.prOrderRawId !== null) {
                                      return;
                                    }

                                    const selectedBrandId = e.target.value;
                                    const validBrandId =
                                      selectedBrandId !== null &&
                                      selectedBrandId !== undefined
                                        ? selectedBrandId
                                        : 1;

                                    inputField.onChange(validBrandId);
                                  }}
                                >
                                  {Array.isArray(field._brands) &&
                                  field._brands.length > 0 ? (
                                    field._brands.map((brand: any) => (
                                      <MenuItem key={brand.id} value={brand.id}>
                                        {brand.name}
                                      </MenuItem>
                                    ))
                                  ) : (
                                    <MenuItem value={1}>ไม่มีแบรนด์</MenuItem>
                                  )}
                                </Select>
                              )}
                            />
                            <div className={'error-text'}>
                              {/* {errors?.prOrderList?.[index]?.brandId?.message || */}
                              {/*  ''} */}
                            </div>
                          </FormControl>
                        </TableCell>
                        <TableCell align="center">
                          <Controller
                            name={`prOrderList.${index}.amount`}
                            control={control}
                            disabled={
                              !isAllowed(
                                permissions,
                                'stock.purchase-request.update'
                              )
                            }
                            rules={{
                              required: 'โปรดระบุจำนวน',
                              validate: (value) => {
                                const numericValue = convert2numeric(value);
                                if (numericValue <= 0) {
                                  return 'ต้องมากกว่า 0';
                                }
                                return true;
                              },
                            }}
                            render={({ field, fieldState: { error } }) => (
                              <NumericFormat
                                customInput={TextField}
                                thousandSeparator=","
                                decimalScale={0}
                                fixedDecimalScale
                                allowNegative={false}
                                disabled={false}
                                {...field}
                                onValueChange={(values) => {
                                  const val = values.floatValue ?? 0;
                                  field.onChange(val);
                                }}
                                helperText={error ? error.message : ''}
                                error={Boolean(error)}
                                FormHelperTextProps={{
                                  style: {
                                    fontSize: '10px',
                                  },
                                }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <CustomDeleteIcon
                            disabled={
                              !isAllowed(
                                permissions,
                                'stock.purchase-request.update'
                              )
                            }
                            sx={{
                              borderRadius: '6px',
                              width: '40px',
                              height: '40px',
                              border: '1px solid #DBE2E5',
                            }}
                            onClick={() => {
                              remove(index);
                            }}
                          >
                            <SvgDeleteIcon />
                          </CustomDeleteIcon>
                        </TableCell>
                      </TableRow>
                    ))}
                  {fields.length < 1 && (
                    <TableRow>
                      <TableCell
                        align="center"
                        colSpan={9}
                        sx={{ color: '#dbe2e5' }}
                      >
                        ไม่มีรายการขอซื้อ
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TableWrapper>
        </div>
        <div className="form-section"></div>
        <div className="form-section pr-note">
          <div className="section-title">หมายเหตุ</div>
          <TextField
            fullWidth
            multiline
            rows={6}
            placeholder="รายละเอียด"
            disabled={!isAllowed(permissions, 'stock.purchase-request.update')}
            {...register('note')}
          />
          <div className="actions">
            <Button
              fullWidth
              variant="outlined"
              disabled={
                confirmLoading ||
                !isApproval ||
                !isAllowed(permissions, 'stock.purchase-request.update')
              }
              onClick={() => onSubmit(watch(), 'draft')}
            >
              บันทึกฉบับร่าง
            </Button>
            <Button
              fullWidth
              variant="contained"
              type="submit"
              disabled={
                watch('prOrderList')?.length < 1 ||
                confirmLoading ||
                !isApproval ||
                !isAllowed(permissions, 'stock.purchase-request.update')
              }
            >
              บันทึก และดำเนินการต่อ
            </Button>
          </div>
        </div>
      </form>
    </PurchaseRequestFormStyled>
  );
};

export default PrUpdateForm;

const JobNoFormBtn = styled.div`
  border-radius: 8px;
  border: 1px solid #dbe2e5;
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 0 12px;
  cursor: pointer;
  &:hover {
    border: 1px solid #000000;
  }
`;

const CustomDeleteIcon = styled(IconButton)`
  svg {
    path {
      fill: #000000;
    }
  }
`;

const PurchaseRequestFormStyled = styled.div`
  padding: 24px;
  form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    //grid-template-rows: repeat(3, 1fr);
    gap: 0 38px;
  }
  .form-section {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }
  .pr-info {
    .pr-info-content {
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      .content-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        height: 82.5px;
        .approval {
          width: 100%;
          height: 48px;
          background: #fff;
          display: flex;
          align-items: center;
          column-gap: 16px;
          position: relative;
          .avatar {
            display: flex;
            align-items: center;
          }
          .title-info {
            .name {
              font-size: 14px;
              font-weight: 700;
            }
            .detail {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
        .approve-btn {
          cursor: pointer;
          border-radius: 6px;
          height: 40px;
          width: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .approve-btn.edit {
          border: 1px solid #dbe2e5;
          background: #fff;
          svg {
            path {
              fill: black;
            }
          }
        }
        .approve-btn.add {
          border: 1px solid #000;
          background: #000;
          svg {
            path {
              fill: #fff;
            }
          }
        }
      }
      .content-item.bd {
        border-bottom: 1px solid #dbe2e5;
      }
    }
  }
  .customer-info {
    .customer-info-content {
      display: flex;
      width: 100%;
      height: 165px;
      padding: 24px;
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      border-radius: 16px;
      background: #f5f7f8;
      .customer-title {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        background: #fff;
        display: flex;
        align-items: center;
        column-gap: 8px;
        padding: 16px;
        position: relative;
        .avatar {
          display: flex;
          align-items: center;
        }
        .name {
          font-size: 14px;
          font-weight: 400;
        }
        .change-label {
          position: absolute;
          right: 16px;
          cursor: pointer;
          text-decoration: underline;
        }
      }
      .customer-detail {
        .text {
          font-size: 12px;
          font-weight: 400;
          letter-spacing: 0.12px;
        }
      }
    }
  }
  .ld-list {
    grid-column: span 2 / span 2;
    .items-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 24px;
      th,
      td {
        padding: 12px;
        border: 1px solid #e0e0e0;
      }

      th {
        background-color: #f5f5f5;
        text-align: left;
      }
    }
  }
  .pr-note {
    //grid-row-start: 3;
    .actions {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      button {
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
  .section-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

const TableWrapper = styled.div`
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #dbe2e5;
    border-radius: 16px !important;
  }
  .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root {
    border-bottom: none;
  }
`;
