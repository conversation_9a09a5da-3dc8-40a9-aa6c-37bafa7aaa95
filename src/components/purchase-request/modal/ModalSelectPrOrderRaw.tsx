import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Button,
  InputAdornment,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import styled from 'styled-components';
import apiPurchaseRequestRaw from '@/services/stock/purchaseRequest-raw';
import { Search } from '@mui/icons-material';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  onSubmit: (selected: any) => any;
  data: any;
};

const ModalSelectPrOrderRaw = ({
  open,
  handleCloseModal,
  onSubmit,
  data,
}: Props) => {
  const [search, setSearch] = useState<string | null>('');
  const [prOrderRaws, setPrOrderRaws] = useState<any[]>([]);
  const [jobSelected, setJobSelected] = useState<any>(null);
  useEffect(() => {
    if (data) {
      fetchData();
    }
  }, [data, search]);

  useEffect(() => {
    if (open) {
      setJobSelected(null);
    }
  }, [open]);

  const fetchData = async () => {
    const response = await apiPurchaseRequestRaw.getListPrOrderRaw({
      rawMaterialId: data.rawMaterialId,
      brandId: data.brandId,
      prOrderRawStatusId: 1,
      search: search,
    });
    if (response.status) {
      setPrOrderRaws(response.data);
    } else {
      setPrOrderRaws([]);
    }
  };

  const handleSearch = (e: any) => {
    setSearch(e.target.value);
  };

  const handleClose = () => {
    handleCloseModal();
    setTimeout(() => {
      setJobSelected(null);
    }, 500);
  };

  const handleSubmit = () => {
    if (jobSelected) {
      onSubmit(jobSelected);
      handleClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleClose();
      }}
    >
      <DialogContent sx={{ padding: '0 !important' }}>
        <FormModalStyle $width={540}>
          <div className="content-wrap">
            <div className="header pl-4" style={{ transform: 'none' }}>
              <div className="title">เลือกรายการผลิต</div>
              <div
                className="x-close"
                onClick={() => {
                  handleClose();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
                rowGap: '0',
              }}
            >
              <div style={{ padding: '28px 28px 8px 28px' }}>
                <TextField
                  className="fade-in mt-2"
                  fullWidth
                  value={search}
                  onChange={handleSearch}
                  placeholder="ค้นหาตัวแทนจำหน่าย"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div
                style={{
                  fontSize: '14px',
                  fontWeight: '400',
                  margin: '8px 28px 0 28px',
                  paddingBottom: '10px',
                  borderBottom: '1px solid #dbe2e5',
                }}
              >
                {`${0} รายการ`}
              </div>
              <div
                style={{
                  margin: '0 28px 0 28px',
                }}
              >
                <JobListContainer>
                  {prOrderRaws && prOrderRaws.length > 0 ? (
                    prOrderRaws.map((item: any, index: number) => {
                      return (
                        <div
                          key={index}
                          className={`detail-item ${
                            jobSelected?.id === item.id && 'active'
                          }`}
                          onClick={() => setJobSelected(item)}
                        >
                          <div className={'name'}>{item.jobNo}</div>
                          <div className={'quantity'}>{item.quantity} ชิ้น</div>
                        </div>
                      );
                    })
                  ) : (
                    <div>ไม่มีข้อมูล</div>
                  )}
                </JobListContainer>
              </div>
              <div
                className={`w-full flex justify-between`}
                style={{
                  padding: '28px',
                  gap: '24px',
                }}
              >
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleClose}
                >
                  <span>ยกเลิก</span>
                </Button>
                <LoadingButton
                  type="submit"
                  // loading={isSubmitting}
                  onClick={handleSubmit}
                  disabled={!jobSelected}
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  บันทึก
                </LoadingButton>
              </div>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalSelectPrOrderRaw;

const JobListContainer = styled.div`
  max-height: 450px;
  overflow-y: auto;
  padding-top: 8px;
  .detail-item {
    height: 40px;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    border-radius: 8px;
    &:hover {
      background-color: #f5f7f8;
    }
  }
  .active {
    background-color: #f5f7f8;
  }
`;
