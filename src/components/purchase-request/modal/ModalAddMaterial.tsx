import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Button,
  MenuItem,
  Select,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { ArrowBack } from '@mui/icons-material';
import apiMaterial from '@/services/stock/material';
import _ from 'lodash';
import styled from 'styled-components';
import Image from 'next/image';
import apiRawMaterial from '@/services/stock/raw-material';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  onSubmit: (selected: any) => any;
};

const ModalAddMaterial = ({ open, handleCloseModal, onSubmit }: Props) => {
  const [step, setStep] = useState<'filter' | 'selects'>('filter');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [materials, setMaterials] = useState<any[]>([]);
  const [materialSelected, setMaterialSelected] = useState<any>(null);
  const [subMaterialSelected, setSubMaterialSelected] = useState<any>(null);
  const [subMaterialDetails, setSubMaterialDetails] = useState<any[]>([]);

  const [detailSelected, setDetailSelected] = useState<any[]>([]);

  useEffect(() => {
    fetchMaterial();
  }, []);

  const fetchMaterial = async () => {
    const res = await apiMaterial.getMaterialOrder();
    if (res.status) {
      setMaterials(res.data);
    }
  };

  useEffect(() => {
    if (materials.length > 0) {
      setMaterialSelected(materials[0]);
    }
  }, [materials]);

  useEffect(() => {
    if (materialSelected && subMaterialSelected) {
      fetchSubMaterialDetail();
    }
  }, [subMaterialSelected]);

  const fetchSubMaterialDetail = async () => {
    const response: any = await apiRawMaterial.getSubMaterialById({
      materialId: materialSelected.id,
      subMaterialId: subMaterialSelected.id,
      limit: 100,
      searchName: '',
    });
    setSubMaterialDetails(response.data);
  };

  useEffect(() => {
    if (subMaterialSelected) {
      setStep('selects');
    } else {
      setStep('filter');
    }
  }, [subMaterialSelected]);

  const handleClose = () => {
    handleCloseModal();
    setTimeout(() => {
      setStep('filter');
      setSubMaterialSelected(null);
      setSubMaterialDetails([]);
    }, 500);
  };

  useEffect(() => {
    if (open) {
      setDetailSelected([]);
    }
  }, [open]);

  const onSelectDetails = (detail: any) => {
    const isDuplicate = checkOnDetailSelected(detail);
    if (isDuplicate) {
      const newSelected = _.clone(detailSelected);
      _.remove(newSelected, (item: any) => item.id === detail.id);
      setDetailSelected(newSelected);
    } else {
      setDetailSelected((prev: any) => [...prev, detail]);
    }
  };

  const checkOnDetailSelected = (detail: any) => {
    return _.some(detailSelected, (item: any) => item.id === detail.id);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    onSubmit(detailSelected || []);
    setTimeout(() => {
      setIsSubmitting(false);
      handleClose();
    }, 500);
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleClose();
      }}
    >
      <DialogContent sx={{ padding: '0 !important' }}>
        <FormModalStyle $width={540}>
          <div className="content-wrap">
            <div className="header pl-4" style={{ transform: 'none' }}>
              {step === 'selects' && (
                <IconButton
                  onClick={() => {
                    setStep('filter');
                    setSubMaterialSelected(null);
                    setSubMaterialDetails([]);
                  }}
                >
                  <ArrowBack className={'text-black text-md'} />
                </IconButton>
              )}
              <div className="title">เพิ่มรายการพัสดุ</div>
              <div
                className="x-close"
                onClick={() => {
                  handleClose();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
                rowGap: '0',
              }}
            >
              <div
                style={{
                  maxHeight: '570px',
                  overflowY: 'auto',
                  padding: '28px 28px 16px 28px',
                  rowGap: '16px',
                  display: step === 'filter' ? '' : 'none',
                }}
              >
                <Select
                  placeholder={'เลือกรายการสินค้า (อ้างอิง)'}
                  displayEmpty
                  defaultValue={'0'}
                  value={String(materialSelected?.id) || '0'}
                  onChange={(event) => {
                    const data = _.find(
                      materials,
                      (o: any) => o.id === Number(event.target.value)
                    );
                    setMaterialSelected(data);
                  }}
                  sx={{
                    width: '100%',
                    fontSize: '16px',
                    fieldset: {
                      borderWidth: '1px !important',
                    },
                    border: '1px solid #DBE2E5 !important',
                    borderRadius: '8px !important',
                  }}
                >
                  <MenuItem disabled value="0">
                    <span style={{ color: '#999' }}>เลือกวัสดุ</span>
                  </MenuItem>
                  {materials &&
                    materials.map((item: any) => {
                      return (
                        <MenuItem key={item.id} value={String(item.id)}>
                          {item.name}
                        </MenuItem>
                      );
                    })}
                </Select>
              </div>
              <div
                style={{
                  height: '450px',
                  overflowY: 'auto',
                  padding: '0 28px 0 28px',
                  rowGap: '28px',
                  display: step === 'filter' ? 'flex' : 'none',
                  flexDirection: 'column',
                }}
              >
                <SubMaterialListContainer>
                  {materialSelected &&
                    materialSelected.subMaterial.map(
                      (item: any, index: number) => {
                        return (
                          <div
                            key={index}
                            className={`sub-item ${
                              subMaterialSelected?.id === item.id && 'active'
                            }`}
                            onClick={() => setSubMaterialSelected(item)}
                          >
                            <Image
                              src={
                                item.imageUrl ||
                                '/images/product/empty-product.svg'
                              }
                              width={48}
                              height={48}
                              alt=""
                              style={{
                                borderRadius: '8px',
                                objectFit: 'cover',
                              }}
                            />
                            <div className={'name'}>{item.name}</div>
                          </div>
                        );
                      }
                    )}
                </SubMaterialListContainer>
              </div>
              <div
                style={{
                  fontSize: '14px',
                  fontWeight: '400',
                  margin: '28px 28px 0 28px',
                  paddingBottom: '10px',
                  borderBottom: '1px solid #dbe2e5',
                  display: step === 'selects' ? '' : 'none',
                }}
              >
                {`${subMaterialDetails?.length || 0} รายการ`}
              </div>
              <div
                style={{
                  margin: '0 28px 0 28px',
                  display: step === 'selects' ? '' : 'none',
                }}
              >
                <SubMaterialDetailsContainer>
                  {subMaterialDetails &&
                    subMaterialDetails.map((detail: any, index: number) => {
                      return (
                        <div
                          key={index}
                          className={`detail-item ${
                            checkOnDetailSelected(detail) && 'active'
                          }`}
                          onClick={() => onSelectDetails(detail)}
                        >
                          <div className={'name'}>{detail.name}</div>
                        </div>
                      );
                    })}
                </SubMaterialDetailsContainer>
              </div>
              <div
                className={`w-full flex justify-between ${
                  step === 'filter' ? 'hidden' : ''
                }`}
                style={{
                  padding: '28px',
                  gap: '24px',
                }}
              >
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleClose}
                >
                  <span>ยกเลิก</span>
                </Button>
                <LoadingButton
                  type="submit"
                  loading={isSubmitting}
                  onClick={handleSubmit}
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  บันทึก
                </LoadingButton>
              </div>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalAddMaterial;

const SubMaterialListContainer = styled.div`
  .sub-item {
    display: flex;
    align-items: center;
    column-gap: 16px;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    &:hover {
      background-color: #f5f7f8;
    }
  }
  .active {
    background-color: #f5f7f8;
  }
`;

const SubMaterialDetailsContainer = styled.div`
  max-height: 450px;
  overflow-y: auto;
  .detail-item {
    height: 40px;
    padding: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    border-radius: 8px;
    &:hover {
      background-color: #f5f7f8;
    }
  }
  .active {
    background-color: #f5f7f8;
  }
`;
