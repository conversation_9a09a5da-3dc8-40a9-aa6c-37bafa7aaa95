import React, { ReactNode, useEffect } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import { useFormik } from 'formik';

type ModalReasonConfirmProps = {
  open: boolean;
  onClickClose: () => void;
  icon?: ReactNode;
  confirmTitle: string | ReactNode;
  confirmDescription: string;
  loadingConfirm: boolean;
  onConfirm: (value: any) => void;
  maxWidth?: string;
  reasonTypeCancelList?: boolean | ReactNode;
  matchedAnnotations?: any;
  reasonTypeReject?: any;
  matchedApprove?: any;
};

const ModalReasonConfirm = (props: ModalReasonConfirmProps) => {
  const {
    open,
    onClickClose,
    icon,
    confirmTitle,
    confirmDescription,
    loadingConfirm,
    onConfirm,
    maxWidth,
    reasonTypeCancelList,
    matchedAnnotations,
    reasonTypeReject,
    matchedApprove,
  } = props;
  const formik = useFormik({
    initialValues: {
      annotationId: '',
      note: '',
    },
    onSubmit: async (values: any) => {
      onConfirm(values);
    },
  });
  useEffect(() => {
    if (open) {
      formik.resetForm();
    }
  }, [open]);

  const handleReasonTypeChange = (event: any) => {
    const selectReasonType = event.target.value;
    formik.setFieldValue('annotationId', selectReasonType);
    const selectItem = matchedAnnotations.find(
      (item: any) => item.id === selectReasonType
    );
    if (selectItem.description || '') {
      formik.setFieldValue('note', selectItem.description);
    }
  };

  const handleReject = (event: any) => {
    const selectReject = event.target.value;
    formik.setFieldValue('annotationId', selectReject);
    const selectItem = matchedApprove.find(
      (item: any) => item.id === selectReject
    );
    if (selectItem.description || '') {
      formik.setFieldValue('note', selectItem.description);
    }
  };

  return (
    <>
      <Dialog open={open}>
        <DialogContent>
          <FormModalStyle $width={488}>
            <div className="content-wrap">
              <div
                className="header"
                style={{
                  boxShadow: 'none',
                }}
              >
                <div
                  className="x-close"
                  onClick={() => {
                    onClickClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                  onSubmit={formik.handleSubmit}
                >
                  <div
                    className="flex justify-center items-center w-[88px] h-[88px] z-10 overflow-hidden"
                    style={{
                      borderRadius: '50%',
                      backgroundColor: '#F5F7F8',
                      margin: '0 auto 16px',
                    }}
                  >
                    {!isEmpty(icon) ? (
                      icon
                    ) : (
                      <Image
                        src={'/icons/icon-modal-delete.svg'}
                        alt=""
                        width={40}
                        height={40}
                      />
                    )}
                  </div>
                  <div
                    style={{
                      fontSize: '20px',
                      fontWeight: '600',
                      margin: '0 auto 8px',
                    }}
                  >
                    {confirmTitle}
                  </div>
                  <div className="w-full flex justify-center">
                    <div
                      className="text-[16px]  text-center"
                      style={{
                        maxWidth: maxWidth || '300px',
                      }}
                    >
                      {confirmDescription}
                    </div>
                  </div>
                  {reasonTypeCancelList && (
                    <>
                      <p
                        className="mb-[8px]"
                        style={{
                          fontWeight: 600,
                        }}
                      >
                        สาเหตุยกเลิก
                      </p>
                      <FormControl>
                        <Select
                          name="annotationId"
                          value={formik.values.annotationId}
                          onChange={handleReasonTypeChange}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected || selected === '') {
                              return <div>กรุณาเลือกสาเหตุ</div>;
                            }
                            const selectedItem = matchedAnnotations?.find(
                              (item: any) => item.id === selected
                            );
                            return selectedItem?.name || '';
                          }}
                        >
                          <MenuItem value="" disabled>
                            เลือกสาเหตุ
                          </MenuItem>
                          {matchedAnnotations?.map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </>
                  )}
                  {reasonTypeReject && (
                    <>
                      <p
                        className="mb-[8px]"
                        style={{
                          fontWeight: 600,
                        }}
                      >
                        สาเหตุไม่อนุมัติ
                      </p>
                      <FormControl fullWidth>
                        <Select
                          name="annotationId"
                          value={formik.values.annotationId}
                          onChange={handleReject}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected || selected === '') {
                              return <div>กรุณาเลือกสาเหตุ</div>;
                            }
                            const selectedItem = matchedApprove?.find(
                              (item: any) => item.id === selected
                            );
                            return selectedItem?.name || '';
                          }}
                        >
                          <MenuItem value="" disabled>
                            เลือกสาเหตุ
                          </MenuItem>
                          {matchedApprove?.map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </>
                  )}
                  <>
                    <p
                      className="mb-[8px]"
                      style={{
                        fontWeight: 600,
                      }}
                    >
                      หมายเหตุ
                    </p>
                    <TextField
                      type="text"
                      name="note"
                      multiline
                      rows={5}
                      value={formik.values.note}
                      placeholder={'อธิบาย'}
                      onChange={formik.handleChange}
                      error={formik.touched.note && Boolean(formik.errors.note)}
                      helperText={
                        formik.touched.note && (formik.errors.note as string)
                      }
                    />
                  </>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      fullWidth
                      onClick={() => {
                        onClickClose();
                      }}
                    >
                      <span>{'ยกเลิก'}</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color={'dark'}
                      fullWidth
                      disabled={
                        !formik.values.note || !formik.values.annotationId
                      }
                    >
                      {loadingConfirm ? (
                        <CircularProgress
                          size={20}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'ยืนยัน'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalReasonConfirm;
