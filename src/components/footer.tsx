import React from 'react';
import styled from 'styled-components';
import moment from 'moment/moment';

const FooterStyle = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  bottom: 0;
  position: relative;
  .text-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #b0bec5;
    font-size: 12px;
    letter-spacing: 1px;
    gap: 12px;
    flex-wrap: wrap;
    max-width: 85%;
    .item {
      transition: 0.15s;
      cursor: pointer;
      &:hover {
        color: #263238;
      }
    }
  }
`;

const Footer = () => {
  return (
    <FooterStyle>
      <div className="text-wrap">
        <span>{moment().format('YYYY')} © HON version 1.0.0</span>
        <span>•</span>
        <span className="item">English</span>
        <span>•</span>
        <span className="item">เงื่อนไขการใช้งาน</span>
        <span>•</span>
        <span className="item">วิธีการใช้งาน</span>
        <span>•</span>
        <span className="item">ออกจากระบบ</span>
      </div>
    </FooterStyle>
  );
};

export default Footer;
