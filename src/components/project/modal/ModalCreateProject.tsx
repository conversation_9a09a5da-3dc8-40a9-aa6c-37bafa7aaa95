import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import FormModal from '@/components/global/form/FormModal';
import { useForm } from 'react-hook-form';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import apiProject from '@/services/order/project';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  formMode: 'create' | 'edit';
  getProject: () => Promise<void>;
  initialValues: any;
};

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อโปรเจกต์'),
});
const ModalCreateProject = ({
  open,
  setOpen,
  formMode,
  getProject,
  initialValues,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    // watch,
    setValue,
    // setError,
    // clearErrors,
    formState: {
      errors: hookFormErrors,
      // isSubmitted
    },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValues,
  });
  useEffect(() => {
    if (open && formMode === 'create') {
      reset();
    } else if (open && formMode === 'edit') {
      setValue('id', initialValues.id);
      setValue('name', initialValues.name);
    }
  }, [open]);
  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    if (formMode === 'create') {
      const res = await apiProject.createProject(values);
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message}`,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getProject();
        setOpen(false);
      }
    } else if (formMode === 'edit') {
      const res = await apiProject.updateProject(values);
      dispatch(
        setSnackBar({
          status: true,
          text: `${res.message}`,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await getProject();
        setOpen(false);
      }
    }
    setIsSubmitting(false);
  };
  return (
    <Dialog
      open={open}
      onClose={() => {
        setOpen(false);
      }}
    >
      <DialogContent>
        <FormModal
          title={`${formMode === 'create' ? 'เพิ่มโปรเจกต์' : 'แก้ไขโปรเจกต์'}`}
          handleClose={() => {
            setOpen(false);
          }}
          width={492}
        >
          <div className="content-wrap">
            <div className="header">
              <div className="title">
                {formMode === 'create' ? 'เพิ่ม' : 'แก้ไข'}โปรเจกต์
              </div>
              <div
                className="x-close"
                onClick={() => {
                  setOpen(false);
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form
                onSubmit={handleSubmit(onSubmit)}
                style={{
                  rowGap: '0',
                }}
              >
                <div>
                  <p>ชื่อโปรเจกต์</p>
                  <TextField
                    placeholder="ชื่อโปรเจกต์"
                    {...register('name')}
                    error={Boolean(hookFormErrors.name)}
                    helperText={
                      hookFormErrors.name
                        ? (hookFormErrors.name.message as ReactNode)
                        : ''
                    }
                  />
                </div>
                <div className="flex gap-[24px] mt-[34px]">
                  <Button
                    type="button"
                    disabled={false}
                    variant="outlined"
                    color="blueGrey"
                    fullWidth
                    onClick={() => setOpen(false)}
                  >
                    ยกเลิก
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    fullWidth
                  >
                    {isSubmitting ? (
                      <CircularProgress
                        size={20}
                        style={{
                          color: 'white',
                        }}
                      />
                    ) : (
                      'บันทึก'
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalCreateProject;
