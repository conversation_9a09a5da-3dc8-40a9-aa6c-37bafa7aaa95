import React, { ChangeEvent, useEffect, useState } from 'react';
import { CircularProgress, InputAdornment, TextField } from '@mui/material';
import { Search } from '@mui/icons-material';
import styled, { css } from 'styled-components';
import { isEmpty } from 'lodash';
import { useDebounce } from 'usehooks-ts';
import { LoadingFadein } from '@/styles/share.styled';

const SearchInputStyled = styled.div<{
  $openSearch: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 84px;
  cursor: pointer;
  border: 1px solid transparent;
  ${({ $openSearch }) =>
    !$openSearch &&
    css`
      border: 1px solid #00000050;
      border-radius: 8px;
    `};
  .MuiInputBase-root {
    border-radius: 8px !important;
    padding: 0 0 0 8px !important;
    width: 300px;
    column-gap: 8px;
    transition: 0.3s ease-out;
    height: 40px;
    min-width: 84px !important;
    overflow: hidden;
    pointer-events: ${({ $openSearch }) => (!$openSearch ? 'none' : 'auto')};
    ${({ $openSearch }) =>
      !$openSearch &&
      css`
        background-color: transparent !important;
        width: 64px !important;
        border-color: transparent !important;
        box-shadow: none !important;
      `};
    @media screen and (max-width: 1120px) {
      width: 200px;
    }
    @media screen and (max-width: 980px) {
      width: 150px;
    }
    input {
      transition: ${({ $openSearch }) =>
        !$openSearch ? 'none' : '0.3s ease-out'};
      opacity: ${({ $openSearch }) => (!$openSearch ? '0' : '1')};
      transition-delay: ${({ $openSearch }) => (!$openSearch ? '0' : '0.3s')};
      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px white inset !important;
        border-radius: 0px !important;
      }
      padding: 0 !important;
    }
    .MuiInputAdornment-positionStart {
      height: 100% !important;
      margin: 0 !important;
      .icon-search {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .MuiInputAdornment-positionEnd {
      height: 100% !important;
      margin: 0 12px 0 0 !important;
      position: relative;
      .cancel {
        position: absolute;
        right: 0;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        animation: ${LoadingFadein} 0.3s ease-in;
        color: ${({ $openSearch }) => !$openSearch && '#263238'};
        &:hover {
          color: #263238;
        }
      }
    }
  }
`;
type SearchInputProps = {
  makeSearchValue: (val: string) => void;
  handleOpenSearch?: () => void;
};
const SearchInput = (props: SearchInputProps) => {
  const { makeSearchValue, handleOpenSearch } = props;
  const [openSearch, setOpenSearch] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedSearchTerm = useDebounce<string>(keyword, 600);
  const [initComponent, setInitComponent] = useState<boolean>(false);
  useEffect(() => {
    if (initComponent) {
      setSearchLoading(false);
      if (openSearch) {
        if (!isEmpty(debouncedSearchTerm)) {
          makeSearchValue(debouncedSearchTerm);
        } else {
          makeSearchValue('');
        }
      } else {
        makeSearchValue('');
      }
    }
  }, [debouncedSearchTerm]);

  const handleChange = (text: string) => {
    if (!isEmpty(text)) {
      setSearchLoading(true);
    }
    setSearchValue(text);
    setKeyword(text);
  };
  useEffect(() => {
    setInitComponent(true);
  }, []);
  return (
    <SearchInputStyled
      $openSearch={openSearch}
      onClick={() => {
        if (!openSearch) {
          setOpenSearch(true);
          if (handleOpenSearch) {
            handleOpenSearch();
          }
        }
      }}
    >
      <TextField
        fullWidth
        name="search"
        size="small"
        type="text"
        placeholder="ค้นหา"
        value={searchValue}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
          handleChange(e.target.value);
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <div className="icon-search">
                <Search
                  style={{
                    color: '#263238',
                  }}
                />
              </div>
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {!searchLoading ? (
                <div
                  className="cancel"
                  onClick={() => {
                    if (openSearch) {
                      setOpenSearch(false);
                      handleChange('');
                    }
                  }}
                >
                  {openSearch ? 'ยกเลิก' : 'ค้นหา'}
                </div>
              ) : (
                <div className="cancel">
                  <CircularProgress size={20} />
                </div>
              )}
            </InputAdornment>
          ),
        }}
      />
    </SearchInputStyled>
  );
};

export default SearchInput;
