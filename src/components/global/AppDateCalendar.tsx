import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Dialog } from '@mui/material';
import { CalendarMonthOutlined } from '@mui/icons-material';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { Calendar } from 'react-date-range';

// theme css file

const DateRangeSelectorStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: 0.2s;
  padding: 0 12px 0 8px;
  border-radius: 8px;
  width: 180px;
  height: 40px;
  border: 1px solid #dbe2e5;
`;

const DateRangeStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrInRange,
  .rdrStartEdge,
  .rdrEndEdge {
    background: #30d5c7 !important;
  }
  .rdrDateDisplay {
    span {
      border-radius: 8px !important;
      overflow: hidden !important;
      box-shadow: none !important;
    }
  }
`;

type AppDateCalendarProps = {
  maxDate?: any;
  minDate?: any;
  data: any;
  handleChange: (date: any) => void;
  // refContainer?: any;
};

const AppDateCalendar = ({
  maxDate,
  minDate,
  data,
  handleChange,
}: // refContainer,
AppDateCalendarProps) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [date, setDate] = useState<any>();
  useEffect(() => {
    setDate(data);
  }, [data]);

  const applyDate = () => {
    handleChange(date);
    setOpenDialog(false);
  };

  return (
    <>
      <DateRangeSelectorStyle>
        <div
          className="flex flex-row items-center gap-1 h-full"
          onClick={() => setOpenDialog(true)}
        >
          <CalendarMonthOutlined
            sx={{
              fontSize: '22px',
            }}
          />
          <div
            className="whitespace-nowrap"
            style={{
              marginTop: '1px',
            }}
          >
            {data ? `${dayjs(data).format('DD/MM/YYYY')}` : 'เลือกวันที่'}
          </div>
        </div>
        {/* {data && ( */}
        {/*  <Cancel */}
        {/*    className="text-[#78909c]" */}
        {/*    onClick={() => { */}
        {/*      removeDate(); */}
        {/*    }} */}
        {/*    sx={{ */}
        {/*      margin: '0 -4px 0 4px', */}
        {/*    }} */}
        {/*  /> */}
        {/* )} */}
      </DateRangeSelectorStyle>
      <Dialog
        open={openDialog}
        onClose={() => {
          setDate(data);
          setOpenDialog(false);
        }}
      >
        <DateRangeStyle>
          <Calendar
            maxDate={maxDate}
            minDate={minDate}
            date={date}
            onChange={(date) => {
              setDate(date);
            }}
          />
        </DateRangeStyle>
        <div className="px-4 pb-4 flex flex-row gap-2">
          <Button
            className="flex-1"
            variant="outlined"
            color="blueGrey"
            size="small"
            onClick={() => setOpenDialog(false)}
          >
            ยกเลิก
          </Button>
          <Button
            className="flex-1"
            variant="contained"
            size="small"
            onClick={() => applyDate()}
          >
            Apply
          </Button>
        </div>
      </Dialog>
    </>
  );
};

export default AppDateCalendar;
