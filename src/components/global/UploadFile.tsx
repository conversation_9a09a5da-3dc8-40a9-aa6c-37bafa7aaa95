import styled from 'styled-components';
import { useEffect, useRef, useState } from 'react';
import SvgSignatureIcon from '@/components/svg-icon/SvgSignatureIcon';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import Swal from 'sweetalert2';

type TypeProps = {
  imgUrl: string | null;
  onFileChange: (e: any) => void;
  onRemove: () => void;
  sizeKBLimit?: number;
};
const UploadFile = ({
  imgUrl,
  onFileChange,
  onRemove,
  sizeKBLimit,
}: TypeProps) => {
  const hiddenFileInput: any = useRef(null);
  const [file, setFile] = useState<any>(null);
  const [preview, setPreview] = useState<any>(null);
  const onUploadFile = (files: any) => {
    if (files && sizeKBLimit && files[0].size > sizeKBLimit * 1000) {
      Swal.fire({
        title: `ไฟล์มีขนาดใหญ่เกินไป`,
        text: `ไฟล์ต้องมีขนาดไม่เกิน ${sizeKBLimit}KB`,
        icon: 'warning',
      });
    } else {
      const objectUrl = URL.createObjectURL(files[0]);
      const fileData = files[0];
      setPreview(objectUrl);
      setFile(fileData);
    }
  };

  useEffect(() => {
    if (imgUrl) {
      setPreview(imgUrl);
    } else {
      setPreview(null);
    }
  }, [imgUrl]);

  useEffect(() => {
    onFileChange(file);
  }, [file]);

  const onDelete = async () => {
    if (imgUrl) {
      onRemove();
    } else {
      setPreview(null);
      setFile(null);
    }
  };

  return (
    <Container isFile={preview}>
      <Upload visible={!preview}>
        <div className="description">JPG, SVG หรือ PNG ขนาดไม่เกิน 800K</div>
        <label>
          <ButtonStyle>
            <div className={'icon'}>
              <SvgSignatureIcon />
            </div>
            <div className={'label'}>อัปโหลดตรายาง</div>
          </ButtonStyle>
          <input
            type="file"
            multiple={false}
            accept="image/jpeg, image/png, image/jpg ,image/svg"
            style={{ display: 'none' }}
            ref={hiddenFileInput}
            onChange={(e) => {
              onUploadFile(e.target.files);
            }}
          />
        </label>
      </Upload>
      <Preview visible={preview}>
        <div className="delete-icon" onClick={() => onDelete()}>
          <SvgDeleteIcon />
        </div>
        <img alt="" src={preview} />
      </Preview>
    </Container>
  );
};

export default UploadFile;

const Container = styled.div<{ isFile?: boolean }>`
  border-radius: 16px;
  width: 100%;
  height: fit-content;
  padding: 16px;
  background: ${(props) => (props.isFile ? 'none' : '#f5f7f8')};
  border: 1px solid #f5f7f8;
  .description {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 38px 0;
    color: #78909c;
  }
`;

const ButtonStyle = styled.div`
  cursor: pointer;
  border-radius: 6px;
  background: #fff;
  height: 40px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  svg {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 8px;
    top: 8px;
  }
  .label {
    color: #78909c;
  }
`;

const Upload = styled.div<{ visible?: boolean }>`
  display: ${(props: any) => (props.visible ? 'block' : 'none')};
`;
const Preview = styled.div<{ visible?: boolean }>`
  display: ${(props: any) => (props.visible ? 'flex' : 'none')};
  width: 100%;
  justify-content: center;
  align-items: center;
  position: relative;
  .delete-icon {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    background: #fde8ef;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px;
    border-radius: 4px;
    svg {
      color: #d32f2f;
      height: 18px;
      width: 18px;
    }
  }
  img {
    height: 140px;
  }
`;
