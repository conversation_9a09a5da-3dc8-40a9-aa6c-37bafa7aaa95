import React, { Fragment } from 'react';
import { IconButton } from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { FormModalStyle } from '@/styles/share.styled';

type Props = {
  width?: number;
  children: React.ReactElement;
  title: string;
  handleClose: () => void;
  headerButton?: React.ReactElement;
  notTranslateXHeader?: boolean;
};

const FormModal: React.FC<Props> = (props) => {
  const {
    width = 492,
    children,
    title,
    handleClose,
    headerButton,
    notTranslateXHeader,
  } = props;
  return (
    <FormModalStyle $width={width} $notTranslateXHeader={notTranslateXHeader}>
      <div className="content-wrap">
        <div className="header">
          <div className="title">{title}</div>
          <div
            className={`x-close ${headerButton ? 'header-button' : ''}`}
            onClick={() => {
              if (headerButton === undefined) {
                handleClose();
              }
            }}
          >
            {headerButton ?? (
              <IconButton>
                <CloseIcon />
              </IconButton>
            )}
          </div>
        </div>
        <Fragment>{children}</Fragment>
      </div>
    </FormModalStyle>
  );
};

export default FormModal;
