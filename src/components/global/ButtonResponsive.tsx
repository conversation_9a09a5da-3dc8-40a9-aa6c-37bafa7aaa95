import styled from 'styled-components';

const ButtonResponsiveStyle = styled.button`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 42px;
  min-width: 42px;
  border: 1px solid #dbe2e5;
  border-radius: 32px;
  background: none;
  gap: 10px;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    border-color: black;
  }
  .text {
    font-family: Prompt, sans-serif;
    padding-right: 10px;
    @media screen and (max-width: 450px) {
      display: none;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

type ButtonResponsiveProps = {
  icon: any;
  text?: string;
  handleClick: () => void;
  disabled?: boolean;
};

const ButtonResponsive = ({
  icon,
  text,
  handleClick,
  disabled = false,
}: ButtonResponsiveProps) => {
  return (
    <ButtonResponsiveStyle disabled={disabled} onClick={() => handleClick()}>
      <div className="icon">{icon}</div>
      {text && <div className="text">{text}</div>}
    </ButtonResponsiveStyle>
  );
};

export default ButtonResponsive;
