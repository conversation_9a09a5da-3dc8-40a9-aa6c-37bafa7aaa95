import React, { useEffect, useState } from 'react';
import { th } from 'date-fns/locale';
import { Button, Dialog } from '@mui/material';
import { CalendarMonthOutlined, Cancel } from '@mui/icons-material';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { DateRange } from 'react-date-range';

// theme css file

const DateRangeSelectorStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  transition: 0.2s;
  padding: 0 12px 0 8px;
  border-radius: 8px;
  height: 40px;
  border: 1px solid #dbe2e5;
`;

const DateRangeStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrInRange,
  .rdrStartEdge,
  .rdrEndEdge {
    background: #30d5c7 !important;
  }
  .rdrDateDisplay {
    span {
      border-radius: 8px !important;
      overflow: hidden !important;
      box-shadow: none !important;
    }
  }
`;

type AppDateRangeProps = {
  data: any;
  handleChange: (dateRange: any) => void;
  // refContainer?: any;
};

const emptyRange = {
  startDate: null,
  endDate: null,
};
const AppDateRange = ({
  data,
  handleChange,
}: // refContainer,
AppDateRangeProps) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [dateRange, setDateRange] = useState<any>(emptyRange);
  useEffect(() => {
    setDateRange(data);
  }, [data]);

  const applyDate = () => {
    handleChange({
      startDate: dateRange.startDate
        ? dayjs(dateRange.startDate).format('YYYY-MM-DD')
        : null,
      endDate: dateRange.startDate
        ? dayjs(dateRange.endDate).format('YYYY-MM-DD')
        : null,
    });
    setOpenDialog(false);
  };

  const clearDate = () => {
    setDateRange(emptyRange);
  };

  const removeDate = () => {
    handleChange({
      startDate: null,
      endDate: null,
    });
    setDateRange(emptyRange);
  };

  // @ts-ignore
  return (
    <>
      <DateRangeSelectorStyle>
        <div
          className="flex flex-row items-center gap-1 h-full"
          onClick={() => setOpenDialog(true)}
        >
          <CalendarMonthOutlined
            sx={{
              fontSize: '22px',
            }}
          />
          <div
            className="whitespace-nowrap"
            style={{
              marginTop: '1px',
            }}
          >
            {data.startDate
              ? `${dayjs(data.startDate).format('D/M/YY')} -
              ${dayjs(data.endDate).format('D/M/YY')}`
              : 'เลือกวันที่'}
          </div>
        </div>
        {data.startDate && (
          <Cancel
            className="text-[#78909c]"
            onClick={() => {
              removeDate();
            }}
            sx={{
              margin: '0 -4px 0 4px',
            }}
          />
        )}
      </DateRangeSelectorStyle>
      <Dialog
        open={openDialog}
        onClose={() => {
          setDateRange(data);
          setOpenDialog(false);
        }}
      >
        <DateRangeStyle>
          <DateRange
            locale={th}
            editableDateInputs={true}
            ranges={[dateRange]}
            onChange={(ranges) => {
              setDateRange(ranges.range1);
            }}
          />
        </DateRangeStyle>
        <div className="px-4 pb-4 flex flex-row gap-2">
          <Button
            className="flex-1"
            variant="outlined"
            color="blueGrey"
            size="small"
            onClick={() => clearDate()}
          >
            Clear
          </Button>
          <Button
            className="flex-1"
            variant="contained"
            size="small"
            onClick={() => applyDate()}
          >
            Apply
          </Button>
        </div>
      </Dialog>
    </>
  );
};

export default AppDateRange;
