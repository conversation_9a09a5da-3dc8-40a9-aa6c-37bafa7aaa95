import React from 'react';
import styled from 'styled-components';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

type Props = {
  checked: boolean;
  onChange: (checked: boolean) => void;
};

const SwitchPure = ({ checked, onChange }: Props) => {
  const { permissions } = useAppSelector(permissionSelector);
  return (
    <StyledWrapper>
      <label className="switch">
        <input
          disabled={!isAllowed(permissions, 'stock.purchase-request.update')}
          aria-label="Toggle"
          className="switch-input"
          type="checkbox"
          checked={checked}
          onChange={(event) => onChange(event.target.checked)}
        />
        <span className="slider" />
      </label>
    </StyledWrapper>
  );
};

const StyledWrapper = styled.div`
  .switch {
    cursor: pointer;
    display: flex;
    align-items: center;
    --button-width: 42px;
    --button-height: 24px;
    --toggle-diameter: 18px;
    --button-toggle-offset: calc(
      (var(--button-height) - var(--toggle-diameter)) / 2
    );
    --toggle-shadow-offset: 10px;
    --toggle-wider: 3em;
    --color-grey: #cccccc;
    --color-green: #16d5c5;
  }

  .slider {
    display: inline-block;
    width: var(--button-width);
    height: var(--button-height);
    background-color: var(--color-grey);
    border-radius: calc(var(--button-height) / 2);
    position: relative;
    transition: 0.3s all ease-in-out;
  }

  .slider::after {
    content: '';
    display: inline-block;
    width: var(--toggle-diameter);
    height: var(--toggle-diameter);
    background-color: #fff;
    border-radius: calc(var(--toggle-diameter) / 2);
    position: absolute;
    top: var(--button-toggle-offset);
    transform: translateX(var(--button-toggle-offset));
    box-shadow: var(--toggle-shadow-offset) 0
      calc(var(--toggle-shadow-offset) * 4) rgba(0, 0, 0, 0.1);
    transition: 0.3s all ease-in-out;
  }

  .switch input[type='checkbox']:checked + .slider {
    background-color: var(--color-green);
  }

  .switch input[type='checkbox']:checked + .slider::after {
    transform: translateX(
      calc(
        var(--button-width) - var(--toggle-diameter) -
          var(--button-toggle-offset)
      )
    );
    box-shadow: calc(var(--toggle-shadow-offset) * -1) 0
      calc(var(--toggle-shadow-offset) * 4) rgba(0, 0, 0, 0.1);
  }

  .switch input[type='checkbox'] {
    display: none;
  }

  .switch input[type='checkbox']:active + .slider::after {
    width: var(--toggle-wider);
  }

  .switch input[type='checkbox']:checked:active + .slider::after {
    transform: translateX(
      calc(
        var(--button-width) - var(--toggle-wider) - var(--button-toggle-offset)
      )
    );
  }
`;

export default SwitchPure;
