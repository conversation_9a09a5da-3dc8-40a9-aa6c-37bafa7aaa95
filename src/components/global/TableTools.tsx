import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import SearchInput from '@/components/SearchInput';

const TableToolsStyled = styled.div`
  width: 100%;
  min-height: 56px;
  border-bottom: 1px solid #dbe2e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  .count {
    //
  }
  .tools {
    display: flex;
    align-items: center;
    column-gap: 12px;
  }
`;
type Props = {
  tools: string[];
  title: string;
  makeNewFilter: (newFilter: any) => void;
};
const TableTools = ({ tools, title, makeNewFilter }: Props) => {
  const [filters, setFilters] = useState<any>({});
  // useEffect(() => {
  //   const initFilters = tools.map((item: string) => {
  //     return {
  //       [item]: '',
  //     };
  //   });
  //   setFilters(initFilters);
  // }, [tools]);

  useEffect(() => {
    makeNewFilter(filters);
  }, [filters]);

  return (
    <TableToolsStyled>
      <div className="count">{title}</div>
      <div className="tools">
        {tools.includes('search') && (
          <SearchInput
            makeSearchValue={(newValue) =>
              setFilters({
                // ...filters,
                search: newValue,
              })
            }
          />
        )}
      </div>
    </TableToolsStyled>
  );
};

export default TableTools;
