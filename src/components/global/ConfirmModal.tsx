import React from 'react';
import { Dialog, DialogContent, IconButton, Button } from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import styled from 'styled-components';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  loading: boolean;
  title: string;
  description: string;
  iconElement: () => any;
  confirmLabel: string;
  confirmAction: () => void;
  cancelLabel?: string;
};

const ConfirmModal = ({
  open,
  handleCloseModal,
  loading,
  title,
  description,
  iconElement,
  confirmLabel,
  confirmAction,
  cancelLabel,
}: Props) => {
  const submit = () => {
    confirmAction();
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent sx={{ padding: '0 !important' }}>
        <FormModalStyle $width={640}>
          <div className="content-wrap">
            <div
              className="header"
              style={{ transform: 'none', boxShadow: 'none' }}
            >
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <ConfirmModalContainer>
              <div className={'icon'}>{iconElement()}</div>
              <div className={'text'}>
                <div className={'title'}>{title}</div>
                <div className={'description'}>{description}</div>
              </div>
              <div className={`action`}>
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleCloseModal}
                >
                  {(cancelLabel && cancelLabel) || 'ยกเลิก'}
                </Button>
                <LoadingButton
                  type="submit"
                  loading={loading}
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={submit}
                >
                  {(confirmLabel && confirmLabel) || 'บันทึก'}
                </LoadingButton>
              </div>
            </ConfirmModalContainer>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmModal;

const ConfirmModalContainer = styled.div`
  padding: 24px 40px 40px 40px;
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  .icon {
    width: 100%;
    display: flex;
    justify-content: center;
    svg {
      width: 100px;
      height: 100px;
      background: #f5f7f8;
      border-radius: 50%;
      padding: 26px;
    }
  }
  .text {
    color: #263238;
    display: flex;
    flex-direction: column;
    .title {
      text-align: center;
      font-size: 28px;
      font-weight: 600;
    }
    .description {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
    }
  }
  .action {
    width: 100%;
    display: flex;
    justify-content: space-between;
    column-gap: 24px;
  }
`;
