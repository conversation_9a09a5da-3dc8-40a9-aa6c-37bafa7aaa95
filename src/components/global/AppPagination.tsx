import { FormControl, MenuItem, Pagination, Select } from '@mui/material';
import { PaginationStyle } from '@/styles/share.styled';
import React from 'react';
import { FiltersType } from '@/types/app';

type AppPaginationProps = {
  filters: any;
  totalElements: number;
  handleChangeFilters: (newValues: FiltersType) => void;
};
const AppPagination = ({
  filters,
  totalElements,
  handleChangeFilters,
}: AppPaginationProps) => {
  return (
    <>
      <PaginationStyle>
        <div className="flex items-center show-number">
          <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
            <Select
              value={filters.size}
              onChange={(event: any) => {
                handleChangeFilters({
                  ...filters,
                  page: 0,
                  size: event.target.value,
                });
              }}
            >
              <MenuItem value={10}>
                <div className="select-text">แสดง 10</div>
              </MenuItem>
              <MenuItem value={20}>
                <div className="select-text">แสดง 20</div>
              </MenuItem>
              <MenuItem value={50}>
                <div className="select-text">แสดง 50</div>
              </MenuItem>
              <MenuItem value={100}>
                <div className="select-text">แสดง 100</div>
              </MenuItem>
            </Select>
          </FormControl>
          {totalElements !== 0 && (
            <div className={`show-text mt-[8px]`}>
              <span>
                {filters.page * filters.size + 1}
                {' - '}
                {filters.page * filters.size + filters.size > totalElements
                  ? totalElements
                  : filters.page * filters.size + filters.size}{' '}
                จาก {totalElements}
              </span>
            </div>
          )}
        </div>
        <Pagination
          count={Math.ceil(totalElements / filters.size)}
          variant="outlined"
          shape="rounded"
          page={filters.page + 1}
          onChange={(_event: any, page: number) =>
            handleChangeFilters({
              ...filters,
              page: page - 1,
            })
          }
        />
      </PaginationStyle>
    </>
  );
};

export default AppPagination;
