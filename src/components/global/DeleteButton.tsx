import React from 'react';
import styled, { css } from 'styled-components';

const DeleteButtonStyled = styled.div<{ $disabled: boolean | undefined }>`
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 6px;
  transition: 0.3s ease-out;
  ${({ $disabled }) =>
    $disabled
      ? css`
          background: darkgray;
          cursor: default;
        `
      : css`
          background: #d32f2f;
          cursor: pointer;
        `};
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    filter: brightness(0.9);
  }
`;

type Props = {
  children: React.ReactElement;
  onClick: () => void;
  disabled?: boolean | undefined;
};

const DeleteButton = (props: Props) => {
  const { children, onClick, disabled } = props;
  return (
    <DeleteButtonStyled onClick={onClick} $disabled={disabled}>
      {children}
    </DeleteButtonStyled>
  );
};

export default DeleteButton;
