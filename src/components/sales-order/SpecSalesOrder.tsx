import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined';
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import apiLay from '@/services/order/lay';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import SideDetail from '@/components/SideDetail';

import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import LayDataSideDetailContent from '@/components/order/spec/LayDataSideDetailContent';
import ModalAddLayData from '@/components/layout-data/ModalAddLayData';
import dayjs from 'dayjs';
import { numberWithCommas } from '@/utils/number';
import DateRangeRoundedIcon from '@mui/icons-material/DateRangeRounded';
import SalesOrderDetailHeader from '@/components/sales-order/SalesOrderDetailHeader';
import ProductSalesOrderStep from '@/components/sales-order/ProductSalesOrderStep';
import { salesOrderSelector } from '@/store/features/estimate';
import apiEstimate from '@/services/order/estimate';
import LdProgressDetail from '@/components/order/spec/LdProgressDetail';
import LdProgressHeader from '@/components/order/spec/LdProgressHeader';
import LdAccordionWrapper from '@/components/order/spec/LdAccordionWrapper';
import LdAccordion from '@/components/order/spec/LdAccordion';
import ModalChangeProject from '@/components/sales-order/modal/ModalChangeProject';
import apiEstimateProduct from '@/services/order/estimate-product';
import apiEstimatePrintPlate from '@/services/order/estimate-print-plate';
import apiEstimateQuantity from '@/services/order/estimate-quantity';
import {
  Dialog,
  DialogContent,
  Checkbox,
  Button,
  CircularProgress,
  IconButton,
} from '@mui/material';
import {
  FormModalStyle,
  AppTableStyle,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { DataGrid } from '@mui/x-data-grid';
import EstimateExportHeaderTable from '@/components/order/price/EstimateExportHeaderTable';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import apiExportPdf from '@/services/order/export-pdf';

const SpecSalesOrderStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  //max-height: calc(100vh - 64px);
  //overflow: auto;
  @media screen and (max-width: 820px) {
    max-height: calc(100vh - 64px + 72px);
    margin-top: 72px;
  }
  .content {
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    padding: 24px;
    max-height: calc(100vh - 189px);
    overflow: auto;
    z-index: 1;
    @media screen and (max-width: 820px) {
      padding: 16px;
      max-height: calc(100vh - 256px);
    }
    @media screen and (max-width: 650px) {
      max-height: calc(100vh - 234px);
    }
    .card-wrap {
      display: flex;
      gap: 24px;
      width: 100%;
      flex-wrap: wrap;
      max-width: 100%;
      @media screen and (max-width: 820px) {
        gap: 16px;
      }
      .card {
        flex: 1 1 calc(50% - 12px);
        display: flex;
        flex-direction: column;
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        max-width: 100%;
        .card-header {
          padding: 24px;
          border-bottom: 1px solid #dbe2e5;
          height: 100%;
          display: flex;
          align-items: center;
          .profile {
            display: flex;
            align-items: center;
            column-gap: 12px;
            .image {
              border-radius: 50%;
              overflow: hidden;
              height: 40px;
              width: 40px;
              min-width: 40px;
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            .text-group {
              display: flex;
              flex-direction: column;
              row-gap: 8px;
              overflow: hidden;
              .name {
                font-weight: 600;
                font-size: 16px;
                line-height: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 100%;
                overflow: hidden;
              }
              .role {
                font-size: 10px;
                color: #90a4ae;
                line-height: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 100%;
                overflow: hidden;
              }
            }
          }
          .payment {
            display: flex;
            align-items: center;
            column-gap: 24px;
            .amount {
              display: flex;
              flex-direction: column;
              .label {
                font-size: 12px;
              }
              .value {
                font-size: 20px;
                font-weight: 600;
              }
            }
          }
        }
        .detail-wrap {
          display: flex;
          width: 100%;
          .detail {
            flex: 1 1 0%;
            display: flex;
            flex-direction: column;
            padding: 18px 24px;
            row-gap: 2px;
            overflow: hidden;
            justify-content: center;
            @media screen and (max-width: 450px) {
              * {
                text-align: center;
              }
            }
            .title {
              display: flex;
              align-items: center;
              column-gap: 8px;
              font-size: 12px;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              @media screen and (max-width: 450px) {
                flex-direction: column;
                row-gap: 4px;
              }
              span {
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .value {
              font-size: 14px;
              font-weight: 600;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &:nth-child(2) {
              border-left: 1px solid #dbe2e5;
              border-right: 1px solid #dbe2e5;
            }
            .user {
              display: flex;
              column-gap: 12px;
              align-items: center;
              max-width: 100%;
              overflow: hidden;
              .user-image {
                width: 32px;
                min-width: 32px;
                height: 32px;
                border-radius: 50%;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .text-group {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                .name {
                  font-weight: 600;
                  font-size: 14px;
                  white-space: nowrap;
                  max-width: 100%;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
                .role {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }
    }
    .ld-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 8px;
      row-gap: 24px;
    }
    .remark-wrap {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      margin-top: 24px;
      .remark {
        font-size: 12px;
        span {
          font-weight: 600;
          text-decoration: underline;
        }
      }
    }
  }
`;

type SpecOrderProps = {
  reloadOrder: () => Promise<void>;
  handleClickEditPrintPlate: (data: any) => void;
};
type ConfirmSalePriceSelectedState = {
  layDataOrderId: number;
  layData: {
    estimateProductId: number;
    estimateQuantityId: number;
  }[];
  data: any;
};

const SpecOrder = ({
  reloadOrder,
  handleClickEditPrintPlate,
}: SpecOrderProps) => {
  const router = useRouter();
  const { salesOrderId, step } = router.query;
  const dispatch = useAppDispatch();
  const [openAddLd, setOpenAddLd] = useState<boolean>(false);
  const [openModalChangeSpec, setOpenModalChangeSpec] = useState<any>({});
  const [layoutDataDetail, setLayoutDataDetail] = useState<any>({});
  const [openLayDataSideDetail, setOpenLayDataSideDetail] =
    useState<boolean>(false);
  const [forceOpenSettingModal, setForceOpenSettingModal] =
    useState<boolean>(false);
  const [confirmSalePriceSelected, setConfirmSalePriceSelected] =
    useState<ConfirmSalePriceSelectedState>({
      layDataOrderId: Number(salesOrderId),
      layData: [],
      data: {},
    });
  const [openConfirmSalePrice, setOpenConfirmSalePrice] =
    useState<boolean>(false);
  const [openDownloadModal, setOpenDownloadModal] = useState<boolean>(false);
  const [modalSelectedDownload, setModalSelectedDownload] = useState<any>([]);
  const [selectedQuantities, setSelectedQuantities] = useState<number[]>([]);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const { salesOrderById } = useAppSelector(salesOrderSelector);
  const [salesOrderStatus, setSalesOrderStatus] = useState<any>([]);
  const [modalConfirmSpec, setModalConfirmSpec] = useState<any>({
    status: false,
    estimateProductId: 0,
    ldCode: '',
    modelName: '',
    modelImageUrl: '',
  });
  const [modalConfirmLayout, setModalConfirmLayout] = useState<any>({
    status: false,
    printPlateId: 0,
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const [openChangeProject, setOpenChangeProject] = useState<boolean>(false);

  const handleOpenConfirmSpec = (ld: any) => {
    setModalConfirmSpec({
      status: true,
      estimateProductId: ld.id,
      ldCode: ld.ldCode,
      modelName: ld.productModel.productModelName,
      modelImageUrl: ld.productModel.imageUrl,
    });
  };
  const handleOpenConfirmLayout = (ld: any) => {
    setModalConfirmLayout({
      status: true,
      printPlateId: ld.printPlate.id,
    });
  };

  const handleConfirmLayout = async () => {
    setLoadingConfirm(true);
    const res = await apiEstimatePrintPlate.approveEstimatePrintPlate(
      modalConfirmLayout.printPlateId
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setModalConfirmLayout({
        ...modalConfirmLayout,
        status: false,
      });
      await reloadOrder();
    }
    setLoadingConfirm(false);
  };

  const handleConfirmSpec = async () => {
    setLoadingConfirm(true);
    const res = await apiEstimateProduct.confirmSpec(
      modalConfirmSpec.estimateProductId
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setModalConfirmSpec({
        ...modalConfirmSpec,
        status: false,
      });
      await reloadOrder();
    }
    setLoadingConfirm(false);
  };

  const getEstimateStatus = async () => {
    const res = await apiEstimate.getSalesOrderStatus();
    if (!res.isError) {
      const status = res.data.filter(
        (statusItem: any) => statusItem.name !== 'ยกเลิก'
      );
      setSalesOrderStatus(status);
    }
  };

  useEffect(() => {
    getEstimateStatus().then();
  }, []);

  const handleConfirmChangeSpec = async () => {
    const res = await apiLay.changeLd(openModalChangeSpec.data.id);
    if (!res.isError) {
      await reloadOrder();
      dispatch(
        setSnackBar({
          status: true,
          text: 'เปลี่ยน LD สำเร็จ',
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setOpenModalChangeSpec(false);
  };

  const handleOpenLayDataSideDetail = async (estimateProductId: number) => {
    const estimateProductDetail = salesOrderById.estimateProduct.find(
      (es: any) => es.id === estimateProductId
    );
    if (estimateProductDetail) {
      setLayoutDataDetail(estimateProductDetail);
      setOpenLayDataSideDetail(true);
    }
  };

  const handleConfirmSalePrice = async () => {
    setLoadingConfirm(true);
    const res = await apiEstimateQuantity.confirmEstimateQuantity({
      estimateProductId: confirmSalePriceSelected.layData[0].estimateProductId,
      estimateQuantityId:
        confirmSalePriceSelected.layData[0].estimateQuantityId,
    });
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await reloadOrder();
      setOpenConfirmSalePrice(false);
      await router.push(
        `/sales-order/${salesOrderId}/spec?step=ยืนยันคำสั่งซื้อ`
      );
    }
    setLoadingConfirm(false);
  };
  const handleSelectQuantity = (
    estimateProductId: number,
    layDataQuantity: any
  ) => {
    // Find all estimateProducts that have matching quantity in their estimateQuantity arrays
    const matchingSelections: {
      estimateProductId: number;
      estimateQuantityId: number;
    }[] = [];

    salesOrderById.estimateProduct.forEach((product: any) => {
      // Find estimateQuantity item with matching quantity
      const matchingQuantityItem = product.estimateQuantity.find(
        (qty: any) => qty.quantity === layDataQuantity.quantity
      );

      if (matchingQuantityItem) {
        matchingSelections.push({
          estimateProductId: product.id,
          estimateQuantityId: matchingQuantityItem.id,
        });
      }
    });

    // Update confirmSalePriceSelected with all matching selections
    setConfirmSalePriceSelected((prev) => ({
      ...prev,
      layData: matchingSelections,
      data: layDataQuantity,
    }));
  };

  // Function to handle quantity-based selection for download modal
  const handleQuantitySelection = (quantity: number, isChecked: boolean) => {
    if (isChecked) {
      // Add this quantity to selected quantities
      setSelectedQuantities((prev) => [...prev, quantity]);

      // Find all estimateQuantity IDs across all layouts that match this quantity
      const matchingIds: number[] = [];
      salesOrderById?.estimateProduct?.forEach((layData: any) => {
        layData.estimateQuantity?.forEach((item: any) => {
          if (item.quantity === quantity) {
            matchingIds.push(item.id);
          }
        });
      });

      // Add all matching IDs to modalSelectedDownload
      setModalSelectedDownload((prev: number[]) => {
        const combined = [...prev, ...matchingIds];
        return combined.filter((id, index) => combined.indexOf(id) === index);
      });
    } else {
      // Remove this quantity from selected quantities
      setSelectedQuantities((prev) => prev.filter((q) => q !== quantity));

      // Find all estimateQuantity IDs across all layouts that match this quantity
      const matchingIds: number[] = [];
      salesOrderById?.estimateProduct?.forEach((layData: any) => {
        layData.estimateQuantity?.forEach((item: any) => {
          if (item.quantity === quantity) {
            matchingIds.push(item.id);
          }
        });
      });

      // Remove all matching IDs from modalSelectedDownload
      setModalSelectedDownload((prev: number[]) =>
        prev.filter((id) => !matchingIds.includes(id))
      );
    }
  };

  // Function to check if a quantity is selected
  const isQuantitySelected = (quantity: number) => {
    return selectedQuantities.includes(quantity);
  };

  // Function to handle download
  const handleClickDownload = async () => {
    setIsProcessing(true);
    const res = await apiExportPdf.exportEstimatePrice(
      Number(salesOrderId),
      modalSelectedDownload.join(',')
    );
    if (res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Download failed',
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    setIsProcessing(false);
    setOpenDownloadModal(false);
    setModalSelectedDownload([]);
    setSelectedQuantities([]);
  };

  const getPrintPlateById = async (ld: any) => {
    const res = await apiEstimatePrintPlate.getEstimatePrintPlateById(
      ld.printPlate.id
    );
    if (!res.isError) {
      handleClickEditPrintPlate(res.data);
    }
  };

  // Reset modal state when download modal opens
  useEffect(() => {
    if (openDownloadModal) {
      setModalSelectedDownload([]);
      setSelectedQuantities([]);
    }
  }, [openDownloadModal]);

  // console.log('salesOrderById.estimateProduct', salesOrderById.estimateProduct);
  return (
    <>
      <SideDetail
        isOpen={openLayDataSideDetail}
        handleClickOutSide={() => {
          setOpenLayDataSideDetail(false);
        }}
      >
        {!isEmpty(layoutDataDetail) && (
          <>
            <div className="header">
              <div className="topic">รายละเอียดสินค้า</div>
              <div
                className="x-close"
                onClick={() => {
                  setOpenLayDataSideDetail(false);
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="content">
              <div className="body">
                <LayDataSideDetailContent layoutDataDetail={layoutDataDetail} />
              </div>
            </div>
          </>
        )}
      </SideDetail>
      <AppModalConfirm
        open={modalConfirmLayout.status}
        isReason={false}
        onClickClose={() => {
          setModalConfirmLayout({
            ...modalConfirmLayout,
            status: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยืนยันเลย์เอาท์ถูกต้อง`}
        confirmDescription={
          'คุณได้ตรวจสอบข้อมูลเลย์เอาท์อย่างละเอียดแล้ว และต้องการยืนยันความถูกต้องของเลย์เอาท์นี้'
        }
        loadingConfirm={false}
        onConfirm={async () => {
          await handleConfirmLayout();
        }}
        maxWidth={'420px'}
      />
      {!isEmpty(openModalChangeSpec) && (
        <AppModalConfirm
          open={openModalChangeSpec}
          isReason={false}
          onClickClose={() => {
            setOpenModalChangeSpec(false);
          }}
          icon={
            <Image
              src={'/icons/edit-black.svg'}
              alt=""
              width={40}
              height={40}
            />
          }
          confirmTitle={`ปรับแต่งสเปคสินค้า`}
          confirmDescription={`การแก้ไขจากรายการ ${openModalChangeSpec.data.ldCode} จะถูกเปลี่ยนเป็นรายการใหม่
และ ออเดอร์จะถูกเปลี่ยนสถานะเป็น “Draft”`}
          loadingConfirm={false}
          onConfirm={async () => {
            await handleConfirmChangeSpec();
          }}
        />
      )}
      <AppModalConfirm
        open={
          step === 'สเปคสินค้า' || step === undefined
            ? modalConfirmSpec.status
            : false
        }
        isReason={false}
        onClickClose={() => {
          if (step === 'สเปคสินค้า' || step === undefined) {
            setModalConfirmSpec({
              ...modalConfirmSpec,
              status: false,
            });
          }
        }}
        icon={
          step === 'สเปคสินค้า' || step === undefined ? (
            <Image
              src={modalConfirmSpec.modelImageUrl}
              alt=""
              width={90}
              height={90}
            />
          ) : (
            <Image
              src={'/icons/icon-export-notes.svg'}
              alt=""
              width={40}
              height={40}
            />
          )
        }
        confirmTitle={
          step === 'สเปคสินค้า' || step === undefined
            ? `ยืนยันข้อมูลสเปคสินค้า`
            : step === 'layout'
            ? 'ยืนยันเลย์เอาท์'
            : step === 'price'
            ? 'ยืนยันเสนอขาย'
            : step === 'payment'
            ? 'ยืนยันการชำระเงิน'
            : ''
        }
        confirmDescription={
          step === 'สเปคสินค้า' || step === undefined
            ? `คุณได้ตรวจสอบความถูกต้องและต้องการยืนยันข้อมูลสเปคสินค้า “${modalConfirmSpec.ldCode} • ${modalConfirmSpec.modelName}” `
            : step === 'layout'
            ? 'ยืนยันเลย์เอาท์'
            : step === 'price'
            ? 'ยืนยันเสนอขาย'
            : step === 'payment'
            ? 'ยืนยันการชำระเงิน'
            : ''
        }
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          if (step === 'สเปคสินค้า' || step === undefined) {
            await handleConfirmSpec();
          } else if (step === 'price') {
            await handleConfirmSalePrice();
          }
        }}
        confirmText={
          step === 'สเปคสินค้า' || step === undefined
            ? 'ยืนยันสเปคถูกต้อง'
            : '-'
        }
        maxWidth={
          step === 'สเปคสินค้า' || step === undefined ? '360px' : '300px'
        }
      />
      <AppModalConfirm
        open={openConfirmSalePrice}
        onClickClose={() => {
          setOpenConfirmSalePrice(false);
        }}
        confirmTitle={`ยืนยันราคาเสนอขาย`}
        confirmDescription={`คุณได้รับการยืนยันจากลูกค้า ตกลงราคาซื้อขายที่จำนวน ${numberWithCommas(
          confirmSalePriceSelected.data?.quantity
        )} ชิ้น`}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleConfirmSalePrice();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
      />
      <ModalChangeProject
        open={openChangeProject}
        setOpenChangeProject={setOpenChangeProject}
        initialValues={{}}
      />
      <SpecSalesOrderStyled id="side-detail-zoom-container">
        <SalesOrderDetailHeader
          reloadOrder={async (action: any) => {
            await reloadOrder();
            if (action !== undefined) {
              await handleConfirmSpec();
            }
          }}
          forceOpenSettingModal={forceOpenSettingModal}
          closeSettingModal={() => {
            setForceOpenSettingModal(false);
          }}
          setOpenChangeProject={setOpenChangeProject}
          confirmSalePriceSelected={confirmSalePriceSelected}
          setOpenConfirmSalePrice={setOpenConfirmSalePrice}
          setOpenDownloadModal={setOpenDownloadModal}
        />
        <div className="content">
          <ProductSalesOrderStep />
          <div className="card-wrap">
            <div className="card">
              <div className="card-header">
                <div className="profile">
                  <div className="image">
                    <Image
                      src={
                        salesOrderById.contact?.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={80}
                      height={80}
                      alt=""
                    />
                  </div>
                  <div className="text-group">
                    <div className="name">{salesOrderById.contact?.name}</div>
                    <div className="role">
                      ลูกค้า • {salesOrderById.contact?.contactType?.name}
                    </div>
                  </div>
                </div>
              </div>
              <div className="detail-wrap">
                <div className="detail">
                  <div className="title">
                    <BadgeOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>เลขผู้เสียภาษี</span>
                  </div>
                  <div className="value">
                    {salesOrderById.contact?.taxNumber}
                  </div>
                </div>
                <div className="detail">
                  <div className="title">
                    <LocalPhoneOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>โทรศัพท์</span>
                  </div>
                  <div className="value">
                    {salesOrderById.contact?.phoneNumber}
                  </div>
                </div>
                <div className="detail">
                  <div className="title">
                    <EmailOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>อีเมล์</span>
                  </div>
                  <div className="value">{salesOrderById.contact?.email}</div>
                </div>
              </div>
            </div>
            <div className="card">
              <div className="card-header">
                <div className="payment">
                  <div className="amount">
                    <div className="label">ชื่อโปรเจค</div>
                    <div className="value">{salesOrderById.estimateNo}</div>
                  </div>
                </div>
              </div>
              <div className="detail-wrap">
                <div className="detail">
                  <div className="title">
                    <DateRangeRoundedIcon
                      sx={{
                        fontSize: '14px',
                      }}
                    />
                    <span>วันที่สร้าง </span>
                  </div>
                  <div className="value">
                    {dayjs(salesOrderById.createdDate).format('D/M/YYYY HH:mm')}
                  </div>
                </div>
                <div
                  className="detail"
                  style={{
                    borderRight: 0,
                  }}
                >
                  <div className="user">
                    <div className="user-image">
                      <Image
                        src={
                          salesOrderById.createUser?.imageUrl ||
                          '/images/product/empty-product.svg'
                        }
                        alt=""
                        width={64}
                        height={64}
                      />
                    </div>
                    <div className="text-group">
                      <div className="name">
                        {salesOrderById.createUser?.name}
                      </div>
                      <div className="role">ผู้ดูแล</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <>
            {!isEmpty(salesOrderById) && (
              <>
                <ModalAddLayData
                  open={openAddLd}
                  makeCloseModal={() => {
                    setOpenAddLd(false);
                  }}
                  handleReloadOrder={async () => {
                    await reloadOrder();
                  }}
                />
                {step !== 'ชำระเงิน' && step !== 'อาร์ตเวิร์ก' && (
                  <div className="ld-list">
                    {salesOrderById.estimateProduct.map(
                      (item: any, index: number) => (
                        <LdProgressDetail key={index}>
                          <LdProgressHeader
                            data={item}
                            handleOpenLayDataSideDetail={
                              handleOpenLayDataSideDetail
                            }
                          />
                          {!isEmpty(salesOrderStatus) && (
                            <LdAccordionWrapper>
                              {salesOrderStatus.map(
                                (itemStatus: any, idx: number) => {
                                  return (
                                    <LdAccordion
                                      key={idx}
                                      data={itemStatus}
                                      ldData={item}
                                      isSaleOrder={true}
                                      makeConfirmSpec={() => {
                                        handleOpenConfirmSpec(item);
                                      }}
                                      makePrintPlateData={async () => {
                                        await getPrintPlateById(item);
                                      }}
                                      makeConfirmLayout={() => {
                                        handleOpenConfirmLayout(item);
                                      }}
                                      reloadOrder={reloadOrder}
                                      confirmSalePriceSelected={
                                        confirmSalePriceSelected
                                      }
                                      handleSelectQuantity={
                                        handleSelectQuantity
                                      }
                                    />
                                  );
                                }
                              )}
                            </LdAccordionWrapper>
                          )}
                        </LdProgressDetail>
                      )
                    )}
                  </div>
                )}
              </>
            )}
          </>
        </div>
      </SpecSalesOrderStyled>

      {/* Download Modal */}
      <Dialog open={openDownloadModal}>
        <DialogContent>
          <FormModalStyle $width={900}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เลือกใบเสนอราคา</div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDownloadModal(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div
                className="form-wrap"
                style={{
                  paddingTop: '24px',
                  paddingBottom: '56px',
                  rowGap: '8px',
                }}
              >
                <div style={{ marginBottom: '-20px', textAlign: 'right' }}>
                  <span
                    style={{
                      textDecoration: 'underline',
                      cursor: 'pointer',
                      fontSize: '14px',
                      color: '#1976d2',
                      fontWeight: '500',
                    }}
                    onClick={() => {
                      // Get all unique quantities across all layouts
                      const allQuantities: number[] = [];
                      salesOrderById?.estimateProduct?.forEach(
                        (layData: any) => {
                          layData.estimateQuantity?.forEach((item: any) => {
                            if (!allQuantities.includes(item.quantity)) {
                              allQuantities.push(item.quantity);
                            }
                          });
                        }
                      );

                      if (selectedQuantities.length === allQuantities.length) {
                        // Deselect all
                        setSelectedQuantities([]);
                        setModalSelectedDownload([]);
                      } else {
                        // Select all
                        setSelectedQuantities(allQuantities);

                        // Get all IDs for all quantities
                        const allIds: number[] = [];
                        salesOrderById?.estimateProduct?.forEach(
                          (layData: any) => {
                            layData.estimateQuantity?.forEach((item: any) => {
                              allIds.push(item.id);
                            });
                          }
                        );
                        setModalSelectedDownload(allIds);
                      }
                    }}
                  >
                    {(() => {
                      const allQuantities: number[] = [];
                      salesOrderById?.estimateProduct?.forEach(
                        (layData: any) => {
                          layData.estimateQuantity?.forEach((item: any) => {
                            if (!allQuantities.includes(item.quantity)) {
                              allQuantities.push(item.quantity);
                            }
                          });
                        }
                      );
                      return selectedQuantities.length ===
                        allQuantities.length && allQuantities.length > 0
                        ? 'ยกเลิกทั้งหมด'
                        : 'เลือกทั้งหมด';
                    })()}
                  </span>
                </div>
                {salesOrderById?.estimateProduct?.map(
                  (layData: any, index: number) => {
                    return (
                      <div key={layData.id} style={{ marginBottom: '24px' }}>
                        <EstimateExportHeaderTable
                          layData={layData}
                          index={index}
                        />
                        <div
                          style={{
                            width: '100%',
                            border: '1px solid #dbe2e5',
                            position: 'relative',
                            overflow: 'hidden',
                            borderRadius: '16px',
                          }}
                        >
                          <AppTableStyle $rows={layData?.estimateQuantity}>
                            <div className="content-wrap">
                              <ScrollBarStyled>
                                <DataGrid
                                  hideFooter={true}
                                  rows={layData?.estimateQuantity || []}
                                  columns={[
                                    {
                                      field: 'option',
                                      headerName: '#',
                                      editable: false,
                                      headerAlign: 'center',
                                      align: 'center',
                                      maxWidth: 56,
                                      disableColumnMenu: true,
                                      sortable: false,
                                      renderCell: (params: any) => {
                                        return (
                                          <Checkbox
                                            color="primary"
                                            checked={isQuantitySelected(
                                              params.row.quantity
                                            )}
                                            onChange={(event: any) => {
                                              handleQuantitySelection(
                                                params.row.quantity,
                                                event.target.checked
                                              );
                                            }}
                                            icon={<IconUnCheckbox />}
                                            checkedIcon={<IconCheckboxBlack />}
                                            style={{ marginLeft: '-9px' }}
                                          />
                                        );
                                      },
                                    },
                                    {
                                      field: 'quantity',
                                      headerName: 'จำนวนผลิต',
                                      editable: false,
                                      headerAlign: 'left',
                                      align: 'left',
                                      flex: 1,
                                      minWidth: 124,
                                      disableColumnMenu: true,
                                      sortable: false,
                                      renderCell: (params: any) => {
                                        return (
                                          <div style={{ marginTop: '4px' }}>
                                            {numberWithCommas(
                                              params.row.quantity
                                            )}{' '}
                                            ชิ้น
                                          </div>
                                        );
                                      },
                                    },
                                    {
                                      field: 'costPrice',
                                      headerName: 'ราคาต้นทุน (บาท)',
                                      editable: false,
                                      headerAlign: 'left',
                                      align: 'left',
                                      flex: 1,
                                      minWidth: 160,
                                      disableColumnMenu: true,
                                      sortable: false,
                                      renderCell: (params: any) => {
                                        const costPricePerPiece =
                                          params.row.quantity > 0 &&
                                          params.row.costPrice !== 0
                                            ? params.row.costPrice /
                                              params.row.quantity
                                            : 0;

                                        return (
                                          <div
                                            style={{
                                              marginTop: '4px',
                                              display: 'flex',
                                              alignItems: 'center',
                                              columnGap: '4px',
                                              maxWidth: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap',
                                            }}
                                          >
                                            {params.row.costPrice !== 0
                                              ? numberWithCommas(
                                                  params.row.costPrice,
                                                  2
                                                )
                                              : '-'}
                                            {params.row.costPrice !== 0 && (
                                              <span
                                                style={{
                                                  fontSize: '14px',
                                                  color: '#90A4AE',
                                                  maxWidth: '100%',
                                                  overflow: 'hidden',
                                                  textOverflow: 'ellipsis',
                                                  whiteSpace: 'nowrap',
                                                }}
                                              >
                                                (
                                                {numberWithCommas(
                                                  costPricePerPiece,
                                                  2
                                                )}{' '}
                                                บาท/ชิ้น)
                                              </span>
                                            )}
                                          </div>
                                        );
                                      },
                                    },
                                    {
                                      field: 'profit',
                                      headerName: 'กำไร',
                                      editable: false,
                                      headerAlign: 'left',
                                      align: 'left',
                                      flex: 1,
                                      minWidth: 140,
                                      disableColumnMenu: true,
                                      sortable: false,
                                      renderCell: (params: any) => {
                                        const profitPerPiece =
                                          params.row.quantity > 0
                                            ? params.row.profit /
                                              params.row.quantity
                                            : 0;

                                        return (
                                          <div
                                            style={{
                                              marginTop: '4px',
                                              display: 'flex',
                                              alignItems: 'center',
                                              columnGap: '4px',
                                              maxWidth: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap',
                                            }}
                                          >
                                            {numberWithCommas(
                                              params.row.profit,
                                              2
                                            )}
                                            <span
                                              style={{
                                                fontSize: '14px',
                                                color: '#90A4AE',
                                                maxWidth: '100%',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                              }}
                                            >
                                              (
                                              {numberWithCommas(
                                                profitPerPiece,
                                                2
                                              )}{' '}
                                              บาท/ชิ้น)
                                            </span>
                                          </div>
                                        );
                                      },
                                    },
                                    {
                                      field: 'sumSalePrice',
                                      headerName: 'สรุปราคาเสนอขาย (บาท)',
                                      editable: false,
                                      headerAlign: 'left',
                                      align: 'left',
                                      flex: 1,
                                      minWidth: 180,
                                      disableColumnMenu: true,
                                      sortable: false,
                                      renderCell: (params: any) => {
                                        const pricePerPiece =
                                          params.row.quantity > 0
                                            ? params.row.totalSalePrice /
                                              params.row.quantity
                                            : 0;

                                        return (
                                          <div
                                            style={{
                                              marginTop: '4px',
                                              display: 'flex',
                                              alignItems: 'center',
                                              columnGap: '4px',
                                              maxWidth: '100%',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap',
                                            }}
                                          >
                                            {numberWithCommas(
                                              params.row.totalSalePrice,
                                              2
                                            )}
                                            <span
                                              style={{
                                                fontSize: '14px',
                                                color: '#90A4AE',
                                                maxWidth: '100%',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                              }}
                                            >
                                              (
                                              {numberWithCommas(
                                                pricePerPiece,
                                                2
                                              )}{' '}
                                              บาท/ชิ้น)
                                            </span>
                                          </div>
                                        );
                                      },
                                    },
                                  ]}
                                  paginationMode="server"
                                  rowCount={
                                    layData?.estimateQuantity.length || 0
                                  }
                                  disableSelectionOnClick={false}
                                  autoHeight={true}
                                  sortModel={[]}
                                  getRowHeight={() => 56}
                                  headerHeight={48}
                                />
                              </ScrollBarStyled>
                            </div>
                          </AppTableStyle>
                        </div>
                      </div>
                    );
                  }
                )}
              </div>
              <div className="w-full flex gap-[24px]">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    setOpenDownloadModal(false);
                    setModalSelectedDownload([]);
                    setSelectedQuantities([]);
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  color="dark"
                  fullWidth
                  onClick={handleClickDownload}
                  disabled={isEmpty(modalSelectedDownload) || isProcessing}
                >
                  {isProcessing ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'ดาวน์โหลด'
                  )}
                </Button>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SpecOrder;
