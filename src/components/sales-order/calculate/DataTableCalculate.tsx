import React from 'react';
import styled from 'styled-components';
import HeadTable from '@/components/sales-order/calculate/HeadTable';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import { LoadingFadein } from '@/styles/share.styled';
import RowPlateTable from '@/components/sales-order/calculate/RowPlateTable';
import RowPrintTable from '@/components/sales-order/calculate/RowPrintTable';
import RowCoatingTable from '@/components/sales-order/calculate/RowCoatingTable';
import RowDieCutTable from '@/components/sales-order/calculate/RowDieCutTable';
import RowServiceTable from '@/components/sales-order/calculate/RowServiceTable';
import RowProfitTable from '@/components/sales-order/calculate/RowProfitTable';
import RowDiscountTable from '@/components/sales-order/calculate/RowDiscountTable';
import RowCostTable from '@/components/sales-order/calculate/RowCostTable';
import { RowMaterialTable } from '@/components/sales-order/calculate/RowMaterialTable';
import RowSummaryOfferingPrice from '@/components/sales-order/calculate/RowSummaryOfferingPrice';
import { useAppSelector } from '@/store';
import { salesOrderSelector } from '@/store/features/estimate';
import RowExtraTable from '@/components/sales-order/calculate/RowExtraTable';

const DataTableCalculateStyled = styled.div`
  width: 100%;
  position: relative;
`;
const ContentTableStyled = styled.div`
  pointer-events: revert;
  //height: calc(100dvh - 264px);
  //overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
type Props = {
  getStationByEstimateId: () => Promise<void>;
  currentPage?: number;
  product?: any;
  currentEstimateQuantityIds?: number[];
  isReadonly: boolean;
};
const DataTableCalculate = ({
  getStationByEstimateId,
  currentPage,
  product,
  currentEstimateQuantityIds,
  isReadonly,
}: Props) => {
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);

  // สร้าง function สำหรับ build ตาราง station ของแต่ละ product
  const buildEstimateStationBody = () => {
    if (product) {
      // ถ้ามี product ส่งมา ใช้ product นั้น
      // Filter estimateQuantity based on currentEstimateQuantityIds if provided
      const currentEstimateQuantities = currentEstimateQuantityIds
        ? product.estimateQuantity?.filter((eq: any) =>
            currentEstimateQuantityIds.includes(eq.estimateQuantityId)
          ) || []
        : product.estimateQuantity || [];

      // หา extraStations สำหรับ product นี้
      const extraStations =
        product.station?.filter(
          (station: any) => station.optionCostType?.name === 'เทคนิคพิเศษ'
        ) || [];

      // console.log('estimateQuantityStation', estimateQuantityStation);
      return (
        <div key={0}>
          <RowMaterialTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            getStationByEstimateId={getStationByEstimateId}
            isReadonly={isReadonly}
            product={product}
          />
          <RowServiceTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            getStationByEstimateId={getStationByEstimateId}
            isReadonly={isReadonly}
            product={product}
          />
          <RowPlateTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
          />
          <RowPrintTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
          />
          <RowCoatingTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
          />
          <RowDieCutTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
          />
          {extraStations.map((station: any, stationIndex: number) => (
            <RowExtraTable
              key={stationIndex}
              currentEstimateQuantities={currentEstimateQuantities}
              extraStation={station}
              isReadonly={isReadonly}
            />
          ))}
          <RowCostTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
          />
          <RowProfitTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
            product={product}
          />
          <RowDiscountTable
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            isReadonly={isReadonly}
            product={product}
          />
          <RowSummaryOfferingPrice
            currentEstimateQuantities={currentEstimateQuantities}
            productStations={product.station}
            product={product}
          />
        </div>
      );
    }
    // Fallback: ใช้ logic เดิมถ้าไม่มี product
    if (!estimateQuantityStation?.estimateProducts) return null;
  };

  // console.log('estimateQuantityStation', estimateQuantityStation);
  return (
    <>
      <DataTableCalculateStyled>
        <HeadTable
          getStationByEstimateId={getStationByEstimateId}
          currentPage={currentPage || 1}
          currentEstimateQuantities={
            product ? product.estimateQuantity || [] : []
          }
          product={product}
          isReadonly={isReadonly}
          currentEstimateQuantityIds={currentEstimateQuantityIds}
        />
        <HrSpaceStyle />
        <ContentTableStyled>{buildEstimateStationBody()}</ContentTableStyled>
      </DataTableCalculateStyled>
    </>
  );
};

export default DataTableCalculate;
