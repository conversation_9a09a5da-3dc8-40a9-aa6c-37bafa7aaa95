import React, { useEffect, useState, useRef } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import { motionRowsTableCalculate } from '@/utils/motion/motion-config';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import {
  calcAdjustCostBySubCostDiff,
  updateSummaryOfferingPrice,
} from '@/utils/station-cost';
import CountUp from '@/components/CountUp';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
};

const RowCostTable = ({
  currentEstimateQuantities,
  productStations,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationCost, setStationCost] = useState<any>({});
  const [timer, setTimer] = useState<any>(null);
  const isProcessingRef = useRef(false);

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const costSt = productStations.find(
        (s: any) => s.optionCostType.name === 'ต้นทุน'
      );
      if (costSt) setStationCost(costSt);
    }
  }, [productStations]);

  useEffect(() => {
    if (isEmpty(estimateQuantityStation) || isProcessingRef.current) return;

    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      if (isProcessingRef.current) return;
      isProcessingRef.current = true;

      try {
        // คำนวณ diff ต้นทุน/ค่าแรง/กำไร
        const costUpdated = calcAdjustCostBySubCostDiff(
          estimateQuantityStation
        );
        // คำนวณสรุปราคาเสนอขาย + VAT หลังจากต้นทุนอัปเดตแล้ว
        const fullyUpdated = updateSummaryOfferingPrice(costUpdated);

        // Only update if the functions returned a different object reference
        // This means there were actual changes
        if (fullyUpdated !== estimateQuantityStation) {
          dispatch(setEstimateQuantityStation(fullyUpdated));
        }
      } finally {
        // Reset processing flag after a short delay
        setTimeout(() => {
          isProcessingRef.current = false;
        }, 100);
      }
    }, 500);

    setTimer(newTimer);

    // Cleanup function
    return () => {
      clearTimeout(newTimer);
      isProcessingRef.current = false;
    };
  }, [estimateQuantityStation, dispatch]);

  return (
    <>
      {!isEmpty(stationCost) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationCost.optionCostType.imageIconUrl}
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationCost.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationCost.subStation.map(
                      (subStationItem: any, subStationIndex: number) => (
                        <div
                          key={subStationIndex}
                          className="first-detail-item"
                          ref={register(subStationIndex)}
                          style={{
                            fontWeight:
                              subStationItem.subTitle === 'สรุปราคาต้นทุน'
                                ? '600'
                                : '400',
                          }}
                        >
                          <span>
                            {subStationItem.subTitle}{' '}
                            {subStationItem.subTitle === 'ราคาปรับขึ้น' && (
                              <ArrowDownwardRoundedIcon
                                sx={{
                                  fontSize: 16,
                                  rotate: '180deg',
                                  color: '#8BC34A',
                                }}
                              />
                            )}
                            {subStationItem.subTitle === 'ราคาปรับลง' && (
                              <ArrowDownwardRoundedIcon
                                sx={{ fontSize: 16, color: '#D32F2F' }}
                              />
                            )}
                          </span>
                        </div>
                      )
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          <div className="col-wrap">
            {currentEstimateQuantities.map((qItem: any, _index: number) => {
              const quantityId = qItem.estimateQuantityId;
              const matchedCost = stationCost.cost.find(
                (c: any) => c.estimateQuantityId === quantityId
              );
              if (!matchedCost) return null;

              return (
                <div className="col" key={quantityId}>
                  <div className="col-content">
                    <div className="value" style={{ color: '#605DEC' }}>
                      <span>
                        {(() => {
                          const summaryRow = stationCost.subStation.find(
                            (s: any) => s.subTitle === 'สรุปราคาต้นทุน'
                          );
                          const finalPrice =
                            summaryRow?.subCost.find(
                              (c: any) => c.estimateQuantityId === quantityId
                            )?.priceCostResult ?? 0;

                          return (
                            <CountUp
                              key={`${quantityId}-${finalPrice}`}
                              from={0}
                              to={finalPrice}
                              separator=","
                              duration={0.3}
                              decimals={2}
                            />
                          );
                        })()}
                      </span>
                    </div>
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key="col-detail"
                          {...motionRowsTableCalculate}
                        >
                          {stationCost.subStation.map(
                            (subStationItem: any, subStationIndex: number) => {
                              const cost = subStationItem.subCost.find(
                                (c: any) => c.estimateQuantityId === quantityId
                              );
                              const priceCostResult =
                                cost?.priceCostResult || 0;
                              return (
                                <div
                                  key={subStationIndex}
                                  ref={register(subStationIndex)}
                                  className="detail"
                                  style={{
                                    padding: '16px 0',
                                    fontWeight:
                                      subStationItem.subTitle ===
                                      'สรุปราคาต้นทุน'
                                        ? '600'
                                        : '400',
                                  }}
                                >
                                  <div className="value">
                                    <div
                                      style={{
                                        color:
                                          subStationItem.subTitle ===
                                          'ผลของการปรับราคา'
                                            ? priceCostResult < 0
                                              ? '#D32F2F'
                                              : priceCostResult > 0
                                              ? '#8BC34A'
                                              : 'initial'
                                            : '',
                                      }}
                                    >
                                      <CountUp
                                        from={priceCostResult}
                                        to={priceCostResult || 0}
                                        separator=","
                                        duration={0.3}
                                        decimals={2}
                                      />
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              );
            })}
            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i: number) => (
                <div className="col" key={`dummy-cost-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`col-detail-${i}`}
                          {...motionRowsTableCalculate}
                        >
                          {[...Array(stationCost.subStation.length)].map(
                            (_, j) => (
                              <div
                                key={`dummy-detail-${i}-${j}`}
                                ref={register(j)}
                                className="detail"
                                style={{ padding: '16px 0' }}
                              />
                            )
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowCostTable;
