import React, { useEffect, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import { InputAdornment, TextField } from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import CountUp from '@/components/CountUp';
import { findStationInEstimateData } from '@/utils/estimate';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
};

const RowPrintTable = ({
  currentEstimateQuantities,
  productStations,
  isReadonly,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationPrint, setStationPrint] = useState<any>({});

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const station = productStations.find(
        (stationItem: any) => stationItem.optionCostType.name === 'การพิมพ์'
      );
      if (station) setStationPrint(station);
    }
  }, [productStations]);

  return !isEmpty(stationPrint) ? (
    <RowsTableStyled>
      <div className="col-wrap first">
        <div className="col cursor-pointer">
          <div className="group">
            <div className="topic">
              <div className="icon">
                <Image
                  src={stationPrint.optionCostType.imageIconUrl}
                  alt=""
                  width={24}
                  height={24}
                />
              </div>
              <span>{stationPrint.title}</span>
            </div>
            <ExpandMoreRoundedIcon
              sx={{
                transition: '0.2s linear',
                rotate: isOpen ? '180deg' : '0deg',
              }}
              onClick={() => setIsOpen(!isOpen)}
            />
          </div>
          <AnimatePresence mode="sync">
            {isOpen && (
              <motion.div
                className="first-detail"
                key="first-detail"
                {...motionRowsTableCalculate}
              >
                {stationPrint.subStation.map((sub: any, subIndex: number) => (
                  <div
                    key={subIndex}
                    className="first-detail-item"
                    ref={register(subIndex)}
                  >
                    <span>{sub.subTitle}</span>
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      <div className="col-wrap">
        {currentEstimateQuantities.map((qty: any, colKey: number) => {
          const quantityId = qty.estimateQuantityId;
          const finalPrice = stationPrint.subStation.reduce(
            (sum: number, sub: any) => {
              const cost = sub.subCost.find(
                (c: any) => c.estimateQuantityId === quantityId
              );
              return sum + (cost?.priceCostResult || 0);
            },
            0
          );

          const clientDiffCount = stationPrint.subStation.filter((sub: any) => {
            const cost = sub.subCost.find(
              (c: any) => c.estimateQuantityId === quantityId
            );
            return cost?.priceCostResult !== cost?.priceCalculate;
          }).length;

          return (
            <div className="col" key={colKey}>
              <div className="col-content">
                <div className="value">
                  <span>
                    <CountUp
                      from={finalPrice}
                      to={finalPrice || 0}
                      separator=","
                      duration={0.3}
                      decimals={2}
                    />
                  </span>
                  <AnimatePresence mode="sync">
                    {clientDiffCount > 0 && (
                      <motion.div className="badge" {...motionBadgeConfig}>
                        {clientDiffCount}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <AnimatePresence mode="sync">
                  {isOpen && (
                    <motion.div
                      className="col-content-detail"
                      key="col-detail"
                      {...motionRowsTableCalculate}
                    >
                      {stationPrint.subStation.map(
                        (sub: any, subIndex: number) => {
                          const cost = sub.subCost.find(
                            (c: any) => c.estimateQuantityId === quantityId
                          );
                          const clientDiff =
                            (cost?.priceCostResult || 0) -
                            (cost?.priceCalculate || 0);

                          return (
                            <div
                              key={subIndex}
                              ref={register(subIndex)}
                              className="detail"
                              style={{ padding: '16px 0' }}
                            >
                              <div className="input-group">
                                <NumericFormat
                                  decimalScale={2}
                                  placeholder="0"
                                  customInput={TextField}
                                  thousandSeparator
                                  value={cost?.priceCostResult ?? ''}
                                  onKeyDown={(e: any) =>
                                    ['-', 'e'].includes(e.key) &&
                                    e.preventDefault()
                                  }
                                  disabled={isReadonly}
                                  onValueChange={({ floatValue }) => {
                                    const newData = structuredClone(
                                      estimateQuantityStation
                                    );
                                    const targetStation =
                                      findStationInEstimateData(
                                        newData,
                                        stationPrint.estimateQuantityStationId
                                      );
                                    if (!targetStation) return;
                                    const targetCost = targetStation.subStation[
                                      subIndex
                                    ].subCost.find(
                                      (c: any) =>
                                        c.estimateQuantityId === quantityId
                                    );
                                    if (targetCost) {
                                      targetCost.priceCostResult =
                                        floatValue || 0;
                                      dispatch(
                                        setEstimateQuantityStation(newData)
                                      );
                                    }
                                  }}
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <SyncRoundedIcon
                                          className="sync"
                                          onClick={() => {
                                            const newData = structuredClone(
                                              estimateQuantityStation
                                            );
                                            const targetStation =
                                              findStationInEstimateData(
                                                newData,
                                                stationPrint.estimateQuantityStationId
                                              );
                                            const targetCost =
                                              targetStation?.subStation[
                                                subIndex
                                              ].subCost.find(
                                                (c: any) =>
                                                  c.estimateQuantityId ===
                                                  quantityId
                                              );
                                            if (targetCost) {
                                              targetCost.priceCostResult =
                                                targetCost.priceCalculate;
                                              dispatch(
                                                setEstimateQuantityStation(
                                                  newData
                                                )
                                              );
                                            }
                                          }}
                                        />
                                      </InputAdornment>
                                    ),
                                  }}
                                  sx={{
                                    padding: '0 8px',
                                    '& .MuiInputBase-root': {
                                      boxShadow: `0 0 0 1px ${
                                        clientDiff === 0
                                          ? '#B0BEC5'
                                          : clientDiff < 0
                                          ? '#D32F2F'
                                          : '#8BC34A'
                                      }`,
                                    },
                                  }}
                                />

                                <div className="input-desc">
                                  <div
                                    className="desc-group"
                                    style={{
                                      transition: '0.3s linear',
                                      color: !clientDiff
                                        ? '#B0BEC5'
                                        : clientDiff < 0
                                        ? '#D32F2F'
                                        : '#8BC34A',
                                    }}
                                  >
                                    <AnimatePresence
                                      mode="wait"
                                      initial={false}
                                    >
                                      {clientDiff ? (
                                        <motion.div
                                          key="arrow"
                                          {...motionFadeConfig}
                                          style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                          }}
                                        >
                                          <ArrowDownwardRoundedIcon
                                            sx={{
                                              fontSize: 16,
                                              transition: '0.3s linear',
                                              rotate:
                                                clientDiff < 0
                                                  ? '0deg'
                                                  : '180deg',
                                            }}
                                          />
                                        </motion.div>
                                      ) : (
                                        <motion.div
                                          key="dot"
                                          {...motionFadeConfig}
                                          style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                          }}
                                        >
                                          <FiberManualRecordRoundedIcon
                                            sx={{ fontSize: 16 }}
                                          />
                                        </motion.div>
                                      )}
                                      <motion.span
                                        key="diff-value"
                                        {...motionFadeConfig}
                                      >
                                        {clientDiff ? (
                                          <CountUp
                                            from={clientDiff}
                                            to={clientDiff || 0}
                                            separator=","
                                            duration={0.3}
                                            decimals={2}
                                          />
                                        ) : (
                                          'ไม่เปลี่ยน'
                                        )}
                                      </motion.span>
                                    </AnimatePresence>
                                  </div>
                                  <div className="desc-group">
                                    <span>{cost?.description}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        }
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          );
        })}

        {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
          (_, i) => (
            <div className="col" key={`print-pad-${i}`}>
              <div className="col-content">
                <div className="value" />
                <AnimatePresence mode="sync">
                  {isOpen && (
                    <motion.div
                      className="col-content-detail"
                      key={`print-col-detail-${i}`}
                      {...motionRowsTableCalculate}
                    >
                      {[...Array(stationPrint.subStation.length)].map(
                        (_, j) => (
                          <div
                            key={`print-sub-${i}-${j}`}
                            ref={register(j)}
                            className="detail"
                            style={{ padding: '16px 0' }}
                          />
                        )
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          )
        )}

        <div className="col add border-0" />
      </div>
    </RowsTableStyled>
  ) : null;
};

// @ts-ignore
export default RowPrintTable;
