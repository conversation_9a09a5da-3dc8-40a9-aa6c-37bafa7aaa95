import React, { useEffect, useMemo, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import {
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField,
} from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import CountUp from '@/components/CountUp';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
};

const RowPlateTable = ({
  currentEstimateQuantities,
  productStations,
  isReadonly,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationPlate, setStationPlate] = useState<any>({});

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const station = productStations.find(
        (item: any) => item.optionCostType.name === 'เพลท'
      );
      if (station) setStationPlate(station);
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  return !isEmpty(stationPlate) ? (
    <RowsTableStyled>
      <div className="col-wrap first">
        <div className="col cursor-pointer">
          <div className="group">
            <div className="topic">
              <div className="icon">
                <Image
                  src={stationPlate.optionCostType.imageIconUrl}
                  alt=""
                  width={24}
                  height={24}
                />
              </div>
              <span>{stationPlate.title}</span>
            </div>
            <ExpandMoreRoundedIcon
              sx={{
                transition: '0.2s linear',
                rotate: isOpen ? '180deg' : '0deg',
              }}
              onClick={() => setIsOpen(!isOpen)}
            />
          </div>
          <AnimatePresence mode="sync">
            {isOpen && (
              <motion.div
                className="first-detail"
                key="first-detail"
                {...motionRowsTableCalculate}
              >
                {stationPlate.subStation.map(
                  (subStationItem: any, idx: number) => (
                    <div
                      key={idx}
                      className="first-detail-item"
                      ref={register(idx)}
                    >
                      <span>{subStationItem.subTitle}</span>
                      <FormControlLabel
                        onClick={(e) => e.stopPropagation()}
                        control={
                          <Checkbox
                            color="primary"
                            checked={false}
                            onChange={(e) => e.stopPropagation()}
                            icon={<IconUnCheckbox />}
                            checkedIcon={<IconCheckbox />}
                          />
                        }
                        label="ใช้เพลท LD-2023001XXX"
                      />
                    </div>
                  )
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      <div className="col-wrap">
        {currentEstimateQuantities.map((quantity: any, colIndex: number) => {
          const quantityId = quantity.estimateQuantityId;

          const finalPrice = stationPlate.subStation.reduce(
            (sum: number, subStationItem: any) => {
              const cost = subStationItem.subCost.find(
                (c: any) => c.estimateQuantityId === quantityId
              );
              return sum + (cost?.priceCostResult || 0);
            },
            0
          );

          const clientDiffCount = stationPlate.subStation.filter((sub: any) => {
            const cost = sub.subCost.find(
              (c: any) => c.estimateQuantityId === quantityId
            );
            return cost?.priceCostResult !== cost?.priceCalculate;
          }).length;

          return (
            <div className="col" key={colIndex}>
              <div className="col-content">
                <div className="value">
                  <span>
                    <CountUp
                      key={`${colIndex}-${finalPrice}`}
                      from={0}
                      to={finalPrice}
                      separator=","
                      duration={0.3}
                      decimals={2}
                    />
                  </span>
                  <AnimatePresence mode="sync">
                    {clientDiffCount > 0 && (
                      <motion.div className="badge" {...motionBadgeConfig}>
                        {clientDiffCount}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                <AnimatePresence mode="sync">
                  {isOpen && (
                    <motion.div
                      className="col-content-detail"
                      key="col-detail"
                      {...motionRowsTableCalculate}
                    >
                      {stationPlate.subStation.map(
                        (subStationItem: any, subStationIndex: number) => {
                          const cost = subStationItem.subCost.find(
                            (c: any) => c.estimateQuantityId === quantityId
                          );
                          const diff =
                            (cost?.priceCostResult || 0) -
                            (cost?.priceCalculate || 0);

                          return (
                            <div
                              key={subStationIndex}
                              ref={register(subStationIndex)}
                              className="detail"
                              style={{ padding: '16px 0' }}
                            >
                              <div className="input-group">
                                <NumericFormat
                                  decimalScale={2}
                                  placeholder="0"
                                  customInput={TextField}
                                  thousandSeparator
                                  value={cost?.priceCostResult ?? ''}
                                  onKeyDown={(e) => {
                                    if (['-', 'e'].includes(e.key))
                                      e.preventDefault();
                                  }}
                                  disabled={isReadonly}
                                  onValueChange={({ floatValue }) => {
                                    const newData: any = structuredClone(
                                      estimateQuantityStation
                                    );

                                    // Find the current product that contains this station
                                    const currentProduct =
                                      newData.estimateProducts?.find(
                                        (product: any) =>
                                          product.station?.some(
                                            (s: any) =>
                                              s.estimateQuantityStationId ===
                                              stationPlate.estimateQuantityStationId
                                          )
                                      );

                                    if (!currentProduct) return;

                                    const station = currentProduct.station.find(
                                      (s: any) =>
                                        s.estimateQuantityStationId ===
                                        stationPlate.estimateQuantityStationId
                                    );
                                    if (!station) return;

                                    const targetCost = station.subStation[
                                      subStationIndex
                                    ].subCost.find(
                                      (c: any) =>
                                        c.estimateQuantityId === quantityId
                                    );
                                    if (targetCost) {
                                      targetCost.priceCostResult =
                                        floatValue || 0;
                                      debouncedSave(newData);
                                    }
                                  }}
                                  error={false}
                                  helperText={false}
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <SyncRoundedIcon
                                          className="sync"
                                          onClick={() => {
                                            const newData: any =
                                              structuredClone(
                                                estimateQuantityStation
                                              );

                                            // Find the current product that contains this station
                                            const currentProduct =
                                              newData.estimateProducts?.find(
                                                (product: any) =>
                                                  product.station?.some(
                                                    (s: any) =>
                                                      s.estimateQuantityStationId ===
                                                      stationPlate.estimateQuantityStationId
                                                  )
                                              );

                                            if (!currentProduct) return;

                                            const station =
                                              currentProduct.station.find(
                                                (s: any) =>
                                                  s.estimateQuantityStationId ===
                                                  stationPlate.estimateQuantityStationId
                                              );
                                            if (!station) return;

                                            const targetCost =
                                              station.subStation[
                                                subStationIndex
                                              ].subCost.find(
                                                (c: any) =>
                                                  c.estimateQuantityId ===
                                                  quantityId
                                              );
                                            if (targetCost) {
                                              targetCost.priceCostResult =
                                                targetCost.priceCalculate;
                                              debouncedSave(newData);
                                            }
                                          }}
                                        />
                                      </InputAdornment>
                                    ),
                                  }}
                                  sx={{
                                    padding: '0 8px',
                                    '& .MuiInputBase-root': {
                                      boxShadow: `0 0 0 1px ${
                                        diff === 0
                                          ? '#B0BEC5'
                                          : diff < 0
                                          ? '#D32F2F'
                                          : '#8BC34A'
                                      }`,
                                    },
                                  }}
                                />
                                <div className="input-desc">
                                  <div
                                    className="desc-group"
                                    style={{
                                      transition: '0.3s linear',
                                      color: !diff
                                        ? '#B0BEC5'
                                        : diff < 0
                                        ? '#D32F2F'
                                        : '#8BC34A',
                                    }}
                                  >
                                    <AnimatePresence
                                      mode="wait"
                                      initial={false}
                                    >
                                      {diff ? (
                                        <motion.div
                                          key="arrow"
                                          {...motionFadeConfig}
                                          style={{ display: 'flex' }}
                                        >
                                          <ArrowDownwardRoundedIcon
                                            sx={{
                                              fontSize: 16,
                                              rotate:
                                                diff < 0 ? '0deg' : '180deg',
                                            }}
                                          />
                                        </motion.div>
                                      ) : (
                                        <motion.div
                                          key="dot"
                                          {...motionFadeConfig}
                                          style={{ display: 'flex' }}
                                        >
                                          <FiberManualRecordRoundedIcon
                                            sx={{ fontSize: 16 }}
                                          />
                                        </motion.div>
                                      )}
                                      {diff ? (
                                        <motion.span
                                          key="diff-value"
                                          {...motionFadeConfig}
                                        >
                                          <CountUp
                                            from={diff}
                                            to={diff}
                                            separator=","
                                            duration={0.3}
                                            decimals={2}
                                          />
                                        </motion.span>
                                      ) : (
                                        <motion.span
                                          key="no-diff-value"
                                          {...motionFadeConfig}
                                        >
                                          ไม่เปลี่ยน
                                        </motion.span>
                                      )}
                                    </AnimatePresence>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        }
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          );
        })}

        {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
          (_, i: number) => (
            <div className="col" key={`pad-${i}`}>
              <div className="col-content">
                <div className="value" />
                <AnimatePresence mode="sync">
                  {isOpen && (
                    <motion.div
                      className="col-content-detail"
                      key={`col-detail-${i}`}
                      {...motionRowsTableCalculate}
                    >
                      {[...Array(stationPlate.subStation.length)].map(
                        (_, j) => (
                          <div
                            key={`dummy-sub-${i}-${j}`}
                            ref={register(j)}
                            className="detail"
                            style={{ padding: '16px 0' }}
                          />
                        )
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          )
        )}

        <div className="col add border-0" />
      </div>
    </RowsTableStyled>
  ) : null;
};

export default RowPlateTable;
