import React, { useEffect, useMemo, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import {
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField,
} from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import CountUp from '@/components/CountUp';
import { findStationInEstimateData } from '@/utils/estimate';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
};

const RowDieCutTable = ({
  currentEstimateQuantities,
  productStations,
  isReadonly,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationDieCut, setStationDieCut] = useState<any>({});

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const stationDieCut = productStations.find(
        (stationItem: any) => stationItem.optionCostType.name === 'ไดคัท'
      );
      if (stationDieCut) {
        setStationDieCut(stationDieCut);
      }
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  return (
    <>
      {!isEmpty(stationDieCut) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationDieCut.optionCostType.imageIconUrl}
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationDieCut.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationDieCut.subStation.map(
                      (subStationItem: any, subStationIndex: number) => (
                        <div
                          key={subStationIndex}
                          className="first-detail-item"
                          ref={register(subStationIndex)}
                        >
                          <span>{subStationItem.subTitle}</span>
                          {subStationIndex === 0 && (
                            <FormControlLabel
                              onClick={(event: any) => event.stopPropagation()}
                              control={
                                <Checkbox
                                  color="primary"
                                  checked={false}
                                  onChange={(event: any) => {
                                    event.stopPropagation();
                                    console.log(event.target.checked);
                                  }}
                                  icon={<IconUnCheckbox />}
                                  checkedIcon={<IconCheckbox />}
                                />
                              }
                              label="ใช้บล็อก LD-2023001XXX"
                            />
                          )}
                        </div>
                      )
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          <div className="col-wrap">
            {currentEstimateQuantities.map(
              (quantity: any, colIndex: number) => {
                const quantityId = quantity.estimateQuantityId;
                const finalPrice = stationDieCut.subStation.reduce(
                  (sum: number, sub: any) => {
                    const cost = sub.subCost.find(
                      (c: any) => c.estimateQuantityId === quantityId
                    );
                    return sum + (cost?.priceCostResult || 0);
                  },
                  0
                );
                const clientDiffCount = stationDieCut.subStation.filter(
                  (sub: any) => {
                    const cost = sub.subCost.find(
                      (c: any) => c.estimateQuantityId === quantityId
                    );
                    return cost?.priceCostResult !== cost?.priceCalculate;
                  }
                ).length;

                return (
                  <div className="col" key={colIndex}>
                    <div className="col-content">
                      <div className="value">
                        <span>
                          <CountUp
                            from={finalPrice}
                            to={finalPrice || 0}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </span>
                        <AnimatePresence mode="sync">
                          {clientDiffCount > 0 && (
                            <motion.div
                              className="badge"
                              {...motionBadgeConfig}
                            >
                              {clientDiffCount}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                      <AnimatePresence mode="sync">
                        {isOpen && (
                          <motion.div
                            className="col-content-detail"
                            key="col-detail"
                            {...motionRowsTableCalculate}
                          >
                            {stationDieCut.subStation.map(
                              (sub: any, subStationIndex: number) => {
                                const cost = sub.subCost.find(
                                  (c: any) =>
                                    c.estimateQuantityId === quantityId
                                );
                                const clientDiff =
                                  (cost?.priceCostResult || 0) -
                                  (cost?.priceCalculate || 0);

                                return (
                                  <div
                                    key={subStationIndex}
                                    ref={register(subStationIndex)}
                                    className="detail"
                                    style={{ padding: '16px 0' }}
                                  >
                                    <div className="input-group">
                                      <NumericFormat
                                        placeholder="0"
                                        decimalScale={2}
                                        customInput={TextField}
                                        thousandSeparator
                                        value={cost?.priceCostResult ?? ''}
                                        onKeyDown={(e: any) => {
                                          const invalidChars = ['-', 'e'];
                                          if (invalidChars.includes(e.key))
                                            e.preventDefault();
                                        }}
                                        disabled={isReadonly}
                                        onValueChange={({ floatValue }) => {
                                          const newData: any = structuredClone(
                                            estimateQuantityStation
                                          );

                                          const targetStation =
                                            findStationInEstimateData(
                                              newData,
                                              stationDieCut.estimateQuantityStationId
                                            );
                                          if (!targetStation) return;

                                          const subCost =
                                            targetStation.subStation[
                                              subStationIndex
                                            ].subCost.find(
                                              (c: any) =>
                                                c.estimateQuantityId ===
                                                quantityId
                                            );
                                          if (subCost) {
                                            subCost.priceCostResult =
                                              floatValue || 0;
                                            debouncedSave(newData);
                                          }
                                        }}
                                        error={false}
                                        helperText={false}
                                        InputProps={{
                                          endAdornment: (
                                            <InputAdornment position="end">
                                              <SyncRoundedIcon
                                                className="sync"
                                                onClick={() => {
                                                  const newData: any =
                                                    structuredClone(
                                                      estimateQuantityStation
                                                    );

                                                  const targetStation =
                                                    findStationInEstimateData(
                                                      newData,
                                                      stationDieCut.estimateQuantityStationId
                                                    );
                                                  if (!targetStation) return;

                                                  const subCost =
                                                    targetStation.subStation[
                                                      subStationIndex
                                                    ].subCost.find(
                                                      (c: any) =>
                                                        c.estimateQuantityId ===
                                                        quantityId
                                                    );
                                                  if (subCost) {
                                                    subCost.priceCostResult =
                                                      subCost.priceCalculate;
                                                    debouncedSave(newData);
                                                  }
                                                }}
                                              />
                                            </InputAdornment>
                                          ),
                                        }}
                                        sx={{
                                          padding: '0 8px',
                                          '& .MuiInputBase-root': {
                                            boxShadow: `0 0 0 1px ${
                                              clientDiff === 0
                                                ? '#B0BEC5'
                                                : clientDiff < 0
                                                ? '#D32F2F'
                                                : '#8BC34A'
                                            }`,
                                          },
                                        }}
                                      />
                                      <div className="input-desc">
                                        <div
                                          className="desc-group"
                                          style={{
                                            transition: '0.3s linear',
                                            color: !clientDiff
                                              ? '#B0BEC5'
                                              : clientDiff < 0
                                              ? '#D32F2F'
                                              : '#8BC34A',
                                          }}
                                        >
                                          <AnimatePresence
                                            mode="wait"
                                            initial={false}
                                          >
                                            {clientDiff ? (
                                              <motion.div
                                                key="arrow"
                                                {...motionFadeConfig}
                                                style={{
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                }}
                                              >
                                                <ArrowDownwardRoundedIcon
                                                  sx={{
                                                    fontSize: 16,
                                                    transition: '0.3s linear',
                                                    rotate:
                                                      clientDiff < 0
                                                        ? '0deg'
                                                        : '180deg',
                                                  }}
                                                />
                                              </motion.div>
                                            ) : (
                                              <motion.div
                                                key="dot"
                                                {...motionFadeConfig}
                                                style={{
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                }}
                                              >
                                                <FiberManualRecordRoundedIcon
                                                  sx={{ fontSize: 16 }}
                                                />
                                              </motion.div>
                                            )}
                                            {clientDiff ? (
                                              <motion.span
                                                key="diff-value"
                                                {...motionFadeConfig}
                                              >
                                                <CountUp
                                                  from={clientDiff}
                                                  to={clientDiff || 0}
                                                  separator=","
                                                  duration={0.3}
                                                  decimals={2}
                                                />
                                              </motion.span>
                                            ) : (
                                              <motion.span
                                                key="no-diff-value"
                                                {...motionFadeConfig}
                                              >
                                                ไม่เปลี่ยน
                                              </motion.span>
                                            )}
                                          </AnimatePresence>
                                        </div>
                                        <div className="desc-group">
                                          <span>{cost?.description || ''}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                );
              }
            )}

            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i: number) => (
                <div className="col" key={`diecut-dummy-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`diecut-detail-${i}`}
                          {...motionRowsTableCalculate}
                        >
                          {stationDieCut.subStation.map((_: any, j: number) => (
                            <div
                              key={`diecut-sub-${i}-${j}`}
                              ref={register(j)}
                              className="detail"
                              style={{ padding: '16px 0' }}
                            />
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowDieCutTable;
