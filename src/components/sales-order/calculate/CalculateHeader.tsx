import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import IconButton from '@mui/material/IconButton';
import NavigateNextRoundedIcon from '@mui/icons-material/NavigateNextRounded';
import { FormControlLabel, Checkbox } from '@mui/material';
import { LoadingFadein } from '@/styles/share.styled';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import { numberWithCommas } from '@/utils/number';
import dayjs from 'dayjs';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';

const CalculateHeaderStyled = styled.div`
  width: 100%;
  padding: 24px;
  display: flex;
  position: relative;
  animation: ${LoadingFadein} 0.3s ease-in;
  .left-side {
    display: flex;
    align-items: center;
    gap: 12px;
    .image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      background-color: #f0f0f0;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .info-group {
      display: flex;
      flex-direction: column;
      .ld-code-wrap {
        display: flex;
        align-items: center;
        column-gap: 12px;
        .code {
          font-size: 24px;
          font-weight: 600;
        }
        .status {
          height: 28px;
          padding: 0 12px;
          display: flex;
          align-items: center;
          background: #fff5d3;
          color: #f9a925;
          border-radius: 24px;
        }
      }
      .model {
        //
      }
      .detail {
        //
      }
    }
  }
  .flat-pagination {
    display: flex;
    align-items: center;
    column-gap: 12px;
    position: absolute;
    bottom: 16px;
    right: 16px;
    .MuiFormControlLabel-root {
      border-radius: 8px;
      padding: 0 10px 0 0;
    }
    .checkbox-control {
      .MuiFormControlLabel-label {
        font-size: 14px;
        font-weight: 500;
      }
    }
    .button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: white;
      cursor: pointer;
      border: 1px solid #dbe2e5;
    }
    .page {
      //
    }
  }
`;
const CalculateHeaderStyledWrapperStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  top: 0;
  background: white;
  position: sticky;
  z-index: 1;
  &:before {
    content: '';
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 0) 0%,
      rgb(255, 255, 255) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    bottom: -24px;
  }
`;
type Props = {
  pagination: any;
  onChangePage?: (page: number) => void;
  currentPage: number;
  product?: any;
  isReadonly?: boolean;
};
const CalculateHeader = ({
  pagination,
  onChangePage,
  currentPage,
  product,
  isReadonly = false,
}: Props) => {
  const dispatch = useAppDispatch();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);

  // ใช้ product ที่ส่งมาจาก props หรือหาจาก currentPage
  const currentProduct =
    product ||
    estimateQuantityStation?.estimateProducts?.find((prod: any) =>
      prod.estimateQuantity?.some((q: any) => q.page === currentPage)
    );
  const productModel = currentProduct?.productModel;

  // ตรวจสอบว่า product นี้มี isSetZero หรือไม่
  const isSetZero = currentProduct?.isSetZero || false;

  // ตรวจสอบว่ามี estimateProducts มากกว่า 1 หรือไม่
  const hasMultipleProducts =
    (estimateQuantityStation?.estimateProducts?.length || 0) > 1;

  // นับจำนวน products ที่มี isSetZero = true
  const checkedProductsCount =
    estimateQuantityStation?.estimateProducts?.filter(
      (prod: any) => prod.isSetZero
    ).length || 0;

  // ตรวจสอบว่าสามารถ check ได้หรือไม่ (ห้าม check ครบทุกตัว)
  const canCheck =
    checkedProductsCount <
    (estimateQuantityStation?.estimateProducts?.length || 0) - 1;

  // ฟังก์ชันสำหรับจัดการ checkbox isSetZero
  const handleSetZeroChange = (checked: boolean) => {
    if (isReadonly) return;

    // ถ้าพยายาม check แต่ไม่สามารถ check ได้ (เพราะจะครบทุกตัว)
    if (checked && !canCheck) return;

    const newData = structuredClone(estimateQuantityStation);

    // หา product ที่ต้องการแก้ไข
    const targetProductIndex = newData.estimateProducts?.findIndex(
      (prod: any) =>
        prod.estimateProductId === currentProduct?.estimateProductId
    );

    if (targetProductIndex !== -1 && newData.estimateProducts) {
      const targetProduct = newData.estimateProducts[targetProductIndex];

      // ตั้งค่า isSetZero
      targetProduct.isSetZero = checked;

      // หา station ต้นทุนสำหรับคำนวณกำไร 7%
      const costStation = targetProduct.station?.find(
        (s: any) => s.optionCostType.name === 'ต้นทุน'
      );

      // วนลูปผ่าน station ทั้งหมดของ product นี้
      targetProduct.station?.forEach((station: any) => {
        const isProfit = station.optionCostType.name === 'กำไร';

        station.subStation?.forEach((subStation: any) => {
          subStation.subCost?.forEach((cost: any) => {
            if (checked) {
              // ถ้า check = true ให้ตั้ง priceCostResult เป็น 0
              cost.priceCostResult = 0;
            } else if (isProfit) {
              // สำหรับ station กำไร ให้คำนวณเป็น 7% ของต้นทุน
              const totalCost =
                costStation?.cost.find(
                  (c: any) => c.estimateQuantityId === cost.estimateQuantityId
                )?.priceStation || 0;

              // คำนวณกำไร 7%
              const profitAmount = totalCost * 0.07;
              cost.priceCostResult = profitAmount;
              cost.rawPrice = 0.07; // ตั้งค่า rawPrice เป็น 7%
            } else {
              // สำหรับ station อื่นๆ ให้กลับไปใช้ priceCalculate
              cost.priceCostResult = cost.priceCalculate || 0;
            }
          });
        });

        // อัปเดต priceStation ของแต่ละ station
        if (station.cost) {
          station.cost.forEach((costItem: any) => {
            if (checked) {
              costItem.priceStation = 0;
            } else {
              // คำนวณ priceStation ใหม่จาก subCost
              const totalPrice =
                station.subStation?.reduce((sum: number, sub: any) => {
                  const subCost = sub.subCost?.find(
                    (sc: any) =>
                      sc.estimateQuantityId === costItem.estimateQuantityId
                  );
                  return sum + (subCost?.priceCostResult || 0);
                }, 0) || 0;
              costItem.priceStation = totalPrice;
            }
          });
        }
      });

      dispatch(setEstimateQuantityStation(newData));
    }
  };

  return (
    <CalculateHeaderStyledWrapperStyled>
      <CalculateHeaderStyled>
        <div className="left-side">
          <div className="image">
            <Image
              src={
                productModel?.imageUrl || '/images/product/empty-product.svg'
              }
              alt=""
              width={160}
              height={160}
            />
          </div>
          <div className="info-group">
            <div className="ld-code-wrap">
              <span className="code">{currentProduct?.ldCode}</span>
              {/* <span className="status">API STATUS</span> */}
            </div>
            <span className="model">
              {productModel?.productModelName} |{' '}
              {`${numberWithCommas(
                currentProduct?.width || 0
              )} x ${numberWithCommas(
                currentProduct?.height || 0
              )} x ${numberWithCommas(currentProduct?.length || 0)} mm.`}
            </span>
            <span className="detail">
              ระดับความละเอียด:{' '}
              {currentProduct?.detailLevelEnum === 'ADVANCED'
                ? 'งานละเอียด'
                : 'งานทั่วไป'}{' '}
              | กำหนดส่งสินค้า:{' '}
              {dayjs(currentProduct?.scheduleDate).format('DD/MM/YYYY')}
            </span>
          </div>
        </div>
        <div className="flat-pagination">
          {hasMultipleProducts && (
            <FormControlLabel
              className="checkbox-control"
              control={
                <Checkbox
                  color="primary"
                  checked={isSetZero}
                  onChange={(event: any) => {
                    handleSetZeroChange(event.target.checked);
                  }}
                  disabled={isReadonly || (isSetZero ? false : !canCheck)}
                  icon={<IconUnCheckbox />}
                  checkedIcon={<IconCheckbox />}
                />
              }
              label="เลย์รวม"
            />
          )}
          <IconButton
            className="button"
            disabled={pagination.currentPage <= 1}
            onClick={() => onChangePage?.(pagination.currentPage - 1)}
          >
            <NavigateNextRoundedIcon sx={{ transform: 'rotate(-180deg)' }} />
          </IconButton>
          <span className="page">
            {pagination.currentPage} / {pagination.totalPage}
          </span>
          <IconButton
            className="button"
            disabled={pagination.currentPage >= pagination.totalPage}
            onClick={() => onChangePage?.(pagination.currentPage + 1)}
          >
            <NavigateNextRoundedIcon />
          </IconButton>
        </div>
      </CalculateHeaderStyled>
      <HrSpaceStyle />
    </CalculateHeaderStyledWrapperStyled>
  );
};

export default CalculateHeader;
