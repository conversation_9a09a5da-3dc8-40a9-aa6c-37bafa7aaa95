import React, { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import { InputAdornment, TextField } from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import { numberWithCommas } from '@/utils/number';
import Image from 'next/image';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import CountUp from '@/components/CountUp';
import { findStationInEstimateData } from '@/utils/estimate';
import ModalPrintSheetAllowance from '@/components/sales-order/modal/ModalPrintSheetAllowance';

export const RowsTableStyled = styled.div`
  width: 100%;
  display: flex;
  .col-wrap {
    flex: 2 1 0%;
    height: inherit;
    display: flex;
    align-items: center;
    overflow: hidden;
    &.first {
      flex: 1 1 0%;
      border-right: 1px solid #dbe2e5;
      border-bottom: 1px solid #dbe2e5;
      font-weight: 600;
      .icon {
        width: 24px;
        height: 24px;
      }
    }

    .col {
      flex: 1 1 0%;
      border-right: 1px solid #dbe2e5;
      display: flex;
      flex-direction: column;
      padding: 16px 0;
      column-gap: 8px;
      border-bottom: 1px solid #dbe2e5;
      height: 100%;
      max-width: 100%;
      overflow: hidden;
      .first-detail {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 12px;
        position: relative;
        //background: #1b5c458f;
        background: #ecf8fe;

        &:before {
          content: '';
          position: sticky;
          top: 0;
          width: calc(100% + 16px);
          transform: translateX(-16px);
          height: 1px;
          background: #dbe2e5;
        }

        .first-detail-item {
          width: 100%;
          //border-bottom: 1px solid #dbe2e5;
          padding: 16px;
          position: relative;
          display: flex;
          flex-direction: column;
          .kebab-wrap {
            position: absolute;
            top: 11px;
            right: 8px;
          }
          .action-button {
            button {
              width: fit-content;
              padding: 0;
            }
          }
          span {
            display: flex;
            align-items: center;
            column-gap: 4px;
            flex-wrap: wrap;
          }
          &:before {
            content: '';
            position: absolute;
            display: block;
            bottom: 0;
            width: calc(100% + 16px);
            transform: translateX(-16px);
            height: 1px;
            background: #dbe2e5;
          }
          .size-btn {
            button {
              padding: 0;
            }
          }
        }
      }

      .col-content {
        display: flex;
        flex-direction: column;
        height: inherit;
        overflow: hidden;
        .value {
          height: 24px;
          display: flex;
          align-items: center;
          padding: 0 16px;
          width: 100%;
          justify-content: space-between;
          column-gap: 14px;
          span {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .badge {
            width: 24px;
            min-width: 24px;
            aspect-ratio: 1/1;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #16d5c5;
            color: white;
            font-weight: 600;
          }
        }
        .col-content-detail {
          width: 100%;
          display: flex;
          flex-direction: column;
          margin-top: 12px;
          position: relative;
          //background: #1b5c458f;
          background: #ecf8fe;
          &:before {
            content: '';
            position: sticky;
            display: block;
            top: 0;
            width: calc(100% + 16px);
            transform: translateX(-16px);
            height: 1px;
            background: #dbe2e5;
          }
          .detail {
            width: 100%;
            padding: 16px 0;
            position: relative;
            &:before {
              content: '';
              position: absolute;
              display: block;
              bottom: 0;
              width: calc(100% + 16px);
              transform: translateX(-16px);
              height: 1px;
              background: #dbe2e5;
            }
            .input-group {
              display: flex;
              flex-direction: column;
              row-gap: 8px;
              .sync {
                font-size: 20px;
                color: #263238;
                cursor: pointer;
                transition: 0.3s ease;
                &:hover {
                  rotate: -90deg;
                }
              }
              .input-desc {
                width: 100%;
                display: flex;
                justify-content: space-between;
                column-gap: 8px;
                row-gap: 4px;
                padding: 0 8px;
                font-size: 12px;
                //margin-top: 4px;
                .desc-group {
                  display: flex;
                  align-items: center;
                  column-gap: 4px;
                  overflow: hidden;
                  div {
                    display: flex;
                    align-items: center;
                  }
                  span {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                }
              }
            }
          }
        }
      }

      .group {
        display: flex;
        column-gap: 8px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        overflow: hidden;
        .topic {
          display: flex;
          column-gap: 8px;
          overflow: hidden;
          .icon {
            //
          }
          span {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      &.add {
        justify-content: center;
        cursor: pointer;
        min-width: 56px;
        max-width: 56px;
        width: 56px;
      }

      &:last-child {
        border: 0;
      }
    }
  }
`;

type Props = {
  getStationByEstimateId: () => Promise<void>;
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
  product?: any;
};
export const RowMaterialTable = ({
  getStationByEstimateId,
  currentEstimateQuantities,
  productStations,
  isReadonly,
  product,
}: Props) => {
  const estimateProductId = product?.estimateProductId;
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationMaterial, setStationMaterial] = useState<any>({});

  // ตรวจสอบว่า product นี้มี isSetZero หรือไม่
  const isSetZero = product?.isSetZero || false;
  const [editPrintSheetAllowance, setEditPrintSheetAllowance] = useState<any>({
    status: false,
    values: {
      estimateProductId: '',
      estimateQuantityId: '',
      printSheetAllowance: '',
    },
  });
  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const mat = productStations.find(
        (s: any) => s.optionCostType.name === 'วัสดุหลัก'
      );
      if (mat) setStationMaterial(mat);
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  return (
    <>
      <ModalPrintSheetAllowance
        getStationByEstimateId={getStationByEstimateId}
        data={editPrintSheetAllowance}
        setEditPrintSheetAllowance={setEditPrintSheetAllowance}
      />
      {!isEmpty(stationMaterial) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationMaterial.optionCostType.imageIconUrl}
                      alt={stationMaterial.optionCostType.name}
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationMaterial.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>

              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationMaterial.subStation.map((sub: any) => (
                      <div
                        key={sub.estimateQuantityStationItemId}
                        className="first-detail-item"
                        ref={register(sub.estimateQuantityStationItemId)}
                      >
                        {sub.subTitle}
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          <div className="col-wrap">
            {stationMaterial.cost
              .filter((costItem: any) =>
                currentEstimateQuantities.some(
                  (q) => q.estimateQuantityId === costItem.estimateQuantityId
                )
              )
              .map((costItem: any) => {
                const qtyId = costItem.estimateQuantityId;

                const finalPrice = stationMaterial.subStation.reduce(
                  (sum: number, sub: any) => {
                    const c = sub.subCost.find(
                      (x: any) => x.estimateQuantityId === qtyId
                    );
                    return sum + (c?.priceCostResult || 0);
                  },
                  0
                );

                const diffCount = stationMaterial.subStation.filter(
                  (sub: any) => {
                    const c = sub.subCost.find(
                      (x: any) => x.estimateQuantityId === qtyId
                    );
                    return c && c.priceCostResult !== c.priceCalculate;
                  }
                ).length;
                return (
                  <div className="col" key={qtyId}>
                    <div className="col-content">
                      <div className="value">
                        <span>
                          <CountUp
                            key={`${qtyId}-${finalPrice}`}
                            from={0}
                            to={finalPrice}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </span>
                        <AnimatePresence>
                          {diffCount > 0 && (
                            <motion.div
                              className="badge"
                              {...motionBadgeConfig}
                            >
                              {diffCount}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>

                      <AnimatePresence mode="sync">
                        {isOpen && (
                          <motion.div
                            className="col-content-detail"
                            key={`detail-${qtyId}`}
                            {...motionRowsTableCalculate}
                          >
                            {stationMaterial.subStation.map(
                              (sub: any, subIndex: number) => {
                                const c = sub.subCost.find(
                                  (x: any) => x.estimateQuantityId === qtyId
                                );

                                if (sub.isDes) {
                                  return (
                                    <div
                                      key={sub.estimateQuantityStationItemId}
                                      className="detail"
                                      ref={register(
                                        sub.estimateQuantityStationItemId
                                      )}
                                      style={{ padding: '16px 0' }}
                                    >
                                      <div className="value">
                                        {c?.description}
                                      </div>
                                    </div>
                                  );
                                }

                                if (subIndex === 0) {
                                  const diff =
                                    (c?.priceCostResult || 0) -
                                    (c?.priceCalculate || 0);

                                  return (
                                    <div
                                      key={sub.estimateQuantityStationItemId}
                                      className="detail"
                                      ref={register(
                                        sub.estimateQuantityStationItemId
                                      )}
                                      style={{ padding: '16px 0' }}
                                    >
                                      <div className="input-group">
                                        <NumericFormat
                                          placeholder="0"
                                          decimalScale={2}
                                          customInput={TextField}
                                          thousandSeparator
                                          value={c?.rawPrice}
                                          onKeyDown={(e) => e.preventDefault()}
                                          disabled={isReadonly || isSetZero}
                                          InputProps={{
                                            endAdornment: (
                                              <InputAdornment position="end">
                                                บาท/กก.
                                              </InputAdornment>
                                            ),
                                          }}
                                          style={{ padding: '0 8px' }}
                                        />
                                        <NumericFormat
                                          placeholder="0"
                                          decimalScale={2}
                                          customInput={TextField}
                                          thousandSeparator
                                          value={c?.priceCostResult}
                                          onKeyDown={(e) => {
                                            if (['-', 'e'].includes(e.key))
                                              e.preventDefault();
                                          }}
                                          disabled={isReadonly || isSetZero}
                                          onValueChange={({ floatValue }) => {
                                            const newData: any =
                                              structuredClone(
                                                estimateQuantityStation
                                              );
                                            const tgt =
                                              findStationInEstimateData(
                                                newData,
                                                stationMaterial.estimateQuantityStationId
                                              );
                                            if (tgt) {
                                              const tgtSub =
                                                tgt.subStation[subIndex];
                                              const costObj =
                                                tgtSub.subCost.find(
                                                  (x: any) =>
                                                    x.estimateQuantityId ===
                                                    qtyId
                                                );
                                              if (costObj)
                                                costObj.priceCostResult =
                                                  floatValue || 0;
                                              debouncedSave(newData);
                                            }
                                          }}
                                          InputProps={{
                                            endAdornment: (
                                              <InputAdornment position="end">
                                                <SyncRoundedIcon
                                                  className="sync"
                                                  onClick={() => {
                                                    const newData: any =
                                                      structuredClone(
                                                        estimateQuantityStation
                                                      );
                                                    const tgt =
                                                      findStationInEstimateData(
                                                        newData,
                                                        stationMaterial.estimateQuantityStationId
                                                      );
                                                    if (tgt) {
                                                      const tgtSub =
                                                        tgt.subStation[
                                                          subIndex
                                                        ];
                                                      const costObj =
                                                        tgtSub.subCost.find(
                                                          (x: any) =>
                                                            x.estimateQuantityId ===
                                                            qtyId
                                                        );
                                                      if (costObj)
                                                        costObj.priceCostResult =
                                                          costObj.priceCalculate;

                                                      debouncedSave(newData);
                                                    }
                                                  }}
                                                />
                                              </InputAdornment>
                                            ),
                                          }}
                                          sx={{
                                            padding: '0 8px',
                                            '& .MuiInputBase-root': {
                                              boxShadow: `0 0 0 1px ${
                                                diff === 0
                                                  ? '#B0BEC5'
                                                  : diff < 0
                                                  ? '#D32F2F'
                                                  : '#8BC34A'
                                              }`,
                                            },
                                          }}
                                        />

                                        <div className="input-desc">
                                          <div
                                            className="desc-group"
                                            style={{
                                              transition: '0.3s linear',
                                              color:
                                                diff === 0
                                                  ? '#B0BEC5'
                                                  : diff < 0
                                                  ? '#D32F2F'
                                                  : '#8BC34A',
                                            }}
                                          >
                                            <AnimatePresence mode="wait">
                                              {diff !== 0 ? (
                                                <motion.div
                                                  {...motionFadeConfig}
                                                >
                                                  <ArrowDownwardRoundedIcon
                                                    sx={{
                                                      fontSize: 16,
                                                      rotate:
                                                        diff < 0
                                                          ? '0deg'
                                                          : '180deg',
                                                    }}
                                                  />
                                                </motion.div>
                                              ) : (
                                                <motion.div
                                                  {...motionFadeConfig}
                                                >
                                                  <FiberManualRecordRoundedIcon
                                                    sx={{ fontSize: 16 }}
                                                  />
                                                </motion.div>
                                              )}
                                              <motion.span
                                                {...motionFadeConfig}
                                              >
                                                {diff !== 0 ? (
                                                  <CountUp
                                                    from={diff}
                                                    to={diff}
                                                    separator=","
                                                    duration={0.3}
                                                    decimals={2}
                                                  />
                                                ) : (
                                                  'ไม่เปลี่ยน'
                                                )}
                                              </motion.span>
                                            </AnimatePresence>
                                          </div>
                                          <div className="desc-group">
                                            <span>
                                              {numberWithCommas(
                                                currentEstimateQuantities.find(
                                                  (q: any) =>
                                                    q.estimateQuantityId ===
                                                    qtyId
                                                )?.printSheetQuantity || 0
                                              )}{' '}
                                              ใบพิมพ์
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                }

                                if (subIndex === 1) {
                                  const diff =
                                    (c?.priceCostResult || 0) -
                                    (c?.priceCalculate || 0);

                                  return (
                                    <div
                                      key={sub.estimateQuantityStationItemId}
                                      className="detail"
                                      ref={register(
                                        sub.estimateQuantityStationItemId
                                      )}
                                      style={{ padding: '16px 0' }}
                                    >
                                      <div className="input-group">
                                        <NumericFormat
                                          decimalScale={2}
                                          placeholder="0"
                                          customInput={TextField}
                                          thousandSeparator
                                          value={
                                            currentEstimateQuantities.find(
                                              (q: any) =>
                                                q.estimateQuantityId === qtyId
                                            )?.printSheetAllowance || 0
                                          }
                                          onKeyDown={(e) => e.preventDefault()}
                                          disabled={isReadonly || isSetZero}
                                          onClick={() => {
                                            if (!isSetZero) {
                                              setEditPrintSheetAllowance({
                                                values: {
                                                  estimateProductId,
                                                  estimateQuantityId: qtyId,
                                                  printSheetAllowance:
                                                    currentEstimateQuantities.find(
                                                      (q: any) =>
                                                        q.estimateQuantityId ===
                                                        qtyId
                                                    )?.printSheetAllowance || 0,
                                                },
                                                status: true,
                                              });
                                            }
                                          }}
                                          InputProps={{
                                            endAdornment: (
                                              <InputAdornment position="end">
                                                ใบพิมพ์
                                              </InputAdornment>
                                            ),
                                          }}
                                          sx={{ padding: '0 8px' }}
                                        />

                                        <NumericFormat
                                          decimalScale={2}
                                          placeholder="0"
                                          customInput={TextField}
                                          thousandSeparator
                                          value={c?.priceCostResult}
                                          onKeyDown={(e) => {
                                            if (['-', 'e'].includes(e.key))
                                              e.preventDefault();
                                          }}
                                          disabled={isReadonly || isSetZero}
                                          onValueChange={({ floatValue }) => {
                                            const newData: any =
                                              structuredClone(
                                                estimateQuantityStation
                                              );
                                            const tgt =
                                              findStationInEstimateData(
                                                newData,
                                                stationMaterial.estimateQuantityStationId
                                              );
                                            if (tgt) {
                                              const tgtSub =
                                                tgt.subStation[subIndex];
                                              const costObj =
                                                tgtSub.subCost.find(
                                                  (x: any) =>
                                                    x.estimateQuantityId ===
                                                    qtyId
                                                );
                                              if (costObj)
                                                costObj.priceCostResult =
                                                  floatValue || 0;

                                              debouncedSave(newData);
                                            }
                                          }}
                                          InputProps={{
                                            endAdornment: (
                                              <InputAdornment position="end">
                                                <SyncRoundedIcon
                                                  className="sync"
                                                  onClick={() => {
                                                    const newData: any =
                                                      structuredClone(
                                                        estimateQuantityStation
                                                      );
                                                    const tgt =
                                                      findStationInEstimateData(
                                                        newData,
                                                        stationMaterial.estimateQuantityStationId
                                                      );
                                                    if (tgt) {
                                                      const tgtSub =
                                                        tgt.subStation[
                                                          subIndex
                                                        ];
                                                      const costObj =
                                                        tgtSub.subCost.find(
                                                          (x: any) =>
                                                            x.estimateQuantityId ===
                                                            qtyId
                                                        );
                                                      if (costObj)
                                                        costObj.priceCostResult =
                                                          costObj.priceCalculate;

                                                      debouncedSave(newData);
                                                    }
                                                  }}
                                                />
                                              </InputAdornment>
                                            ),
                                          }}
                                          sx={{
                                            padding: '0 8px',
                                            '& .MuiInputBase-root': {
                                              boxShadow: `0 0 0 1px ${
                                                diff === 0
                                                  ? '#B0BEC5'
                                                  : diff < 0
                                                  ? '#D32F2F'
                                                  : '#8BC34A'
                                              }`,
                                            },
                                          }}
                                        />

                                        <div className="input-desc">
                                          <div
                                            className="desc-group"
                                            style={{
                                              transition: '0.3s linear',
                                              color:
                                                diff === 0
                                                  ? '#B0BEC5'
                                                  : diff < 0
                                                  ? '#D32F2F'
                                                  : '#8BC34A',
                                            }}
                                          >
                                            <AnimatePresence mode="wait">
                                              {diff !== 0 ? (
                                                <motion.div
                                                  {...motionFadeConfig}
                                                >
                                                  <ArrowDownwardRoundedIcon
                                                    sx={{
                                                      fontSize: 16,
                                                      rotate:
                                                        diff < 0
                                                          ? '0deg'
                                                          : '180deg',
                                                    }}
                                                  />
                                                </motion.div>
                                              ) : (
                                                <motion.div
                                                  {...motionFadeConfig}
                                                >
                                                  <FiberManualRecordRoundedIcon
                                                    sx={{ fontSize: 16 }}
                                                  />
                                                </motion.div>
                                              )}
                                              <motion.span
                                                {...motionFadeConfig}
                                              >
                                                {diff !== 0 ? (
                                                  <CountUp
                                                    from={diff}
                                                    to={diff}
                                                    separator=","
                                                    duration={0.3}
                                                    decimals={2}
                                                  />
                                                ) : (
                                                  'ไม่เปลี่ยน'
                                                )}
                                              </motion.span>
                                            </AnimatePresence>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                }

                                return null;
                              }
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                );
              })}
            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, colIndex) => (
                <div className="col" key={`dummy-${colIndex}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`dummy-detail-${colIndex}`}
                          {...motionRowsTableCalculate}
                        >
                          {stationMaterial.subStation.map((sub: any) => (
                            <div
                              key={sub.estimateQuantityStationItemId}
                              ref={register(sub.estimateQuantityStationItemId)}
                              className="detail"
                              style={{ padding: '16px 0' }}
                            />
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};
