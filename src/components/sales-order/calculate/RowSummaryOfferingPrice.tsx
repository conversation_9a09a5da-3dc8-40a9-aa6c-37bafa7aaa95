import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import styled from 'styled-components';
import CountUp from '@/components/CountUp';

const OfferingPriceStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  padding: 0 16px;
  .price-group {
    display: flex;
    flex-direction: column;
    .topic {
      font-size: 12px;
    }
    .sum-value {
      font-size: 20px;
      font-weight: 600;
      height: 30px;
    }
    .vat {
      font-size: 10px;
      color: #b0bec5;
    }
  }
`;

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  product?: any;
};

const RowSummaryOfferingPrice = ({
  currentEstimateQuantities,
  productStations,
  product,
}: Props) => {
  const [stationSummaryOfferingPrice, setStationSummaryOfferingPrice] =
    useState<any>({});
  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const station = productStations.find(
        (s: any) => s.optionCostType.name === 'สรุปราคาเสนอขาย'
      );
      if (station) setStationSummaryOfferingPrice(station);
    }
  }, [productStations]);

  return (
    <>
      {!isEmpty(stationSummaryOfferingPrice) && (
        <RowsTableStyled>
          <div className="col-wrap first"></div>

          <div className="col-wrap">
            {currentEstimateQuantities.map((q: any) => {
              const qId = q.estimateQuantityId;

              // จำนวน
              const qty = q.quantity;

              const totalPrice =
                stationSummaryOfferingPrice.cost.find(
                  (c: any) => c.estimateQuantityId === qId
                )?.priceStation ?? 0;

              // VAT (7% ของราคาก่อน VAT)
              const vatAmount = +(totalPrice - totalPrice / 1.07).toFixed(2);

              // ราคา/ชิ้น คำนวณฝั่ง client จาก totalPrice / quantity
              // ถ้า isSetZero = true ให้แสดงราคา/ชิ้น เป็น 0
              const unitPrice = product?.isSetZero
                ? 0
                : totalPrice && qty
                ? +(totalPrice / qty).toFixed(2)
                : 0;

              return (
                <div className="col" key={qId}>
                  <div className="col-content">
                    <OfferingPriceStyled>
                      <div className="price-group">
                        <div className="topic">จำนวน</div>
                        <div className="sum-value">
                          <CountUp
                            key={`${qId}-${qty}`}
                            from={0}
                            to={qty}
                            separator=","
                            duration={0.3}
                            decimals={0}
                          />
                        </div>
                      </div>

                      <div className="price-group">
                        <div className="topic">ราคารวม</div>
                        <div className="sum-value">
                          <CountUp
                            key={`${qId}-${totalPrice}`}
                            from={0}
                            to={totalPrice}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </div>
                        <div className="vat">
                          <span>VAT </span>
                          <CountUp
                            key={`${qId}-${vatAmount}`}
                            from={0}
                            to={vatAmount}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </div>
                      </div>

                      <div className="price-group">
                        <div className="topic">ราคา/ชิ้น</div>
                        <div className="sum-value">
                          <CountUp
                            key={`${qId}-${unitPrice}`}
                            from={0}
                            to={unitPrice}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </div>
                      </div>
                    </OfferingPriceStyled>
                  </div>
                </div>
              );
            })}

            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i) => (
                <div className="col" key={`dummy-summary-price-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    {/* <AnimatePresence mode="sync"> */}
                    {/*  <motion.div */}
                    {/*    className="col-content-detail" */}
                    {/*    key="col-detail" */}
                    {/*    {...motionRowsTableCalculate} */}
                    {/*  /> */}
                    {/* </AnimatePresence> */}
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowSummaryOfferingPrice;
