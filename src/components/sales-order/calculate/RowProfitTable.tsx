import React, { useEffect, useMemo, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import { InputAdornment, TextField } from '@mui/material';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import CountUp from '@/components/CountUp';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
  product?: any;
};

const RowProfitTable = ({
  currentEstimateQuantities,
  productStations,
  isReadonly,
  product,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationProfit, setStationProfit] = useState<any>({});
  const [profitPercentMap, setProfitPercentMap] = useState<
    Record<number, number>
  >({});
  const [isInitMapProfit, setIsInitMapProfit] = useState<boolean>(false);

  // ตรวจสอบว่า product นี้มี isSetZero หรือไม่
  const isSetZero = product?.isSetZero || false;
  const costStation = useMemo(() => {
    if (!productStations || isEmpty(productStations)) return undefined;
    return productStations.find((s: any) => s.optionCostType.name === 'ต้นทุน');
  }, [productStations]);

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const station = productStations.find(
        (s: any) => s.optionCostType.name === 'กำไร'
      );
      if (station) setStationProfit(station);
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  const profitColumnData = useMemo(() => {
    if (isEmpty(stationProfit)) return [];

    return currentEstimateQuantities.map((qty) => {
      const quantityId = qty.estimateQuantityId;

      const totalCost =
        costStation?.cost.find((c: any) => c.estimateQuantityId === quantityId)
          ?.priceStation ?? 0;

      const finalPrice = stationProfit.subStation.reduce(
        (sum: number, sub: any) => {
          const c = sub.subCost.find(
            (x: any) => x.estimateQuantityId === quantityId
          );
          return sum + (c?.priceCostResult || 0);
        },
        0
      );

      const profitPercentGlobal = totalCost
        ? Number(((finalPrice / totalCost) * 100).toFixed(2))
        : undefined;

      const clientDiffCount = stationProfit.subStation.filter((sub: any) => {
        const c = sub.subCost.find(
          (c: any) => c.estimateQuantityId === quantityId
        );
        return c && c.priceCostResult !== c.priceCalculate;
      }).length;

      return {
        quantityId,
        totalCost,
        finalPrice,
        profitPercentGlobal,
        clientDiffCount,
      };
    });
  }, [currentEstimateQuantities, costStation, stationProfit]);

  useEffect(() => {
    if (!costStation || isEmpty(stationProfit) || isInitMapProfit) return;

    const initMap: Record<number, number> = {};
    currentEstimateQuantities.forEach(({ estimateQuantityId }) => {
      const qId = estimateQuantityId;
      const totalCost =
        costStation.cost.find((c: any) => c.estimateQuantityId === qId)
          ?.priceStation ?? 0;

      const profitRow = stationProfit.subStation[0]?.subCost.find(
        (c: any) => c.estimateQuantityId === qId
      );
      if (profitRow && totalCost) {
        initMap[qId] = +((profitRow.priceCostResult / totalCost) * 100).toFixed(
          2
        );
      }
    });

    setProfitPercentMap(initMap);
    setIsInitMapProfit(true);
  }, [costStation, stationProfit, currentEstimateQuantities]);

  useEffect(() => {
    const stationProfitId = stationProfit?.estimateQuantityStationId;
    if (
      !stationProfitId ||
      isEmpty(profitPercentMap) ||
      isEmpty(estimateQuantityStation)
    )
      return;

    if (!productStations) return;

    const originalProfitSt = productStations.find(
      (s: any) => s.estimateQuantityStationId === stationProfitId
    );
    if (!originalProfitSt) return;

    // Create a deep copy to avoid read-only issues
    const profitSt = JSON.parse(JSON.stringify(originalProfitSt));

    Object.entries(profitPercentMap).forEach(([qIdStr, percent]) => {
      const qId = +qIdStr;
      const totalCost =
        costStation?.cost.find((c: any) => c.estimateQuantityId === qId)
          ?.priceStation ?? 0;
      if (!totalCost) return;

      profitSt.subStation.forEach((sub: any) => {
        const row = sub.subCost.find((c: any) => c.estimateQuantityId === qId);
        if (row) {
          const raw = totalCost * (percent / 100);
          row.priceCostResult = Number.isInteger(raw) ? raw : +raw.toFixed(2);
        }
      });

      const sum = profitSt.subStation.reduce((acc: number, sub: any) => {
        const r = sub.subCost.find((c: any) => c.estimateQuantityId === qId);
        return acc + (r?.priceCostResult ?? 0);
      }, 0);

      const costRow = profitSt.cost.find(
        (c: any) => c.estimateQuantityId === qId
      );
      if (costRow) costRow.priceStation = sum;
    });

    // Update the estimateQuantityStation with the modified productStations
    const newData: any = structuredClone(estimateQuantityStation);
    if (newData.estimateProducts && newData.estimateProducts.length > 0) {
      // Find the product that contains this station and update it
      const productIndex = newData.estimateProducts.findIndex((product: any) =>
        product.station?.some(
          (s: any) => s.estimateQuantityStationId === stationProfitId
        )
      );
      if (productIndex !== -1) {
        const stationIndex = newData.estimateProducts[
          productIndex
        ].station.findIndex(
          (s: any) => s.estimateQuantityStationId === stationProfitId
        );
        if (stationIndex !== -1) {
          const originalStation =
            newData.estimateProducts[productIndex].station[stationIndex];
          // Only update if there are actual changes
          if (JSON.stringify(originalStation) !== JSON.stringify(profitSt)) {
            newData.estimateProducts[productIndex].station[stationIndex] =
              profitSt;
            debouncedSave(newData);
          }
        }
      }
    }
  }, [profitPercentMap, estimateQuantityStation, costStation, stationProfit]);

  return (
    <>
      {!isEmpty(stationProfit) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationProfit.optionCostType.imageIconUrl}
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationProfit.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationProfit.subStation.map((item: any, i: number) => (
                      <div
                        key={i}
                        className="first-detail-item"
                        ref={register(i)}
                      >
                        <span>{item.subTitle}</span>
                        <div
                          style={{
                            marginTop: '8px',
                            fontWeight: 400,
                            color: '#B0BEC5',
                          }}
                        >
                          โปรเจคล่าสุด (LD-2023001XXX) กำไร XX%
                        </div>
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          <div className="col-wrap">
            {currentEstimateQuantities.map((qty: any, colIndex: number) => {
              const { quantityId, finalPrice, clientDiffCount } =
                profitColumnData[colIndex];

              return (
                <div className="col" key={colIndex}>
                  <div className="col-content">
                    <div className="value" style={{ color: '#8BC34A' }}>
                      <span>
                        <CountUp
                          from={finalPrice}
                          to={finalPrice || 0}
                          separator=","
                          duration={0.3}
                          decimals={2}
                        />
                      </span>
                      <AnimatePresence mode="sync">
                        {clientDiffCount > 0 && (
                          <motion.div className="badge" {...motionBadgeConfig}>
                            {clientDiffCount}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key="col-detail"
                          {...motionRowsTableCalculate}
                        >
                          {stationProfit.subStation.map(
                            (sub: any, i: number) => {
                              const cost = sub.subCost.find(
                                (c: any) => c.estimateQuantityId === quantityId
                              );
                              // const clientDiff =
                              //   (cost?.priceCostResult || 0) -
                              //   (cost?.priceCalculate || 0);

                              return (
                                <div
                                  key={i}
                                  ref={register(i)}
                                  className="detail"
                                  style={{ padding: '16px 0' }}
                                >
                                  <div className="input-group">
                                    {/* <NumericFormat */}
                                    {/*  placeholder="0" */}
                                    {/*  customInput={TextField} */}
                                    {/*  thousandSeparator */}
                                    {/*  value={cost?.priceCostResult ?? ''} */}
                                    {/*  onKeyDown={(e) => { */}
                                    {/*    if (['-', 'e'].includes(e.key)) */}
                                    {/*      e.preventDefault(); */}
                                    {/*  }} */}
                                    {/*  onValueChange={({ floatValue }) => { */}
                                    {/*    const newData: any = structuredClone( */}
                                    {/*      estimateQuantityStation */}
                                    {/*    ); */}

                                    {/*    const station = newData.station.find( */}
                                    {/*      (s: any) => */}
                                    {/*        s.estimateQuantityStationId === */}
                                    {/*        stationProfit.estimateQuantityStationId */}
                                    {/*    ); */}
                                    {/*    if (!station) return; */}

                                    {/*    const subCost = station.subStation[ */}
                                    {/*      i */}
                                    {/*    ]?.subCost.find( */}
                                    {/*      (c: any) => */}
                                    {/*        c.estimateQuantityId === */}
                                    {/*        quantityId */}
                                    {/*    ); */}
                                    {/*    if (subCost) { */}
                                    {/*      subCost.priceCostResult = */}
                                    {/*        floatValue || 0; */}
                                    {/*      debouncedSave(newData); */}
                                    {/*    } */}
                                    {/*  }} */}
                                    {/*  InputProps={{ */}
                                    {/*    endAdornment: ( */}
                                    {/*      <InputAdornment position="end"> */}
                                    {/*        <SyncRoundedIcon */}
                                    {/*          className="sync" */}
                                    {/*          onClick={() => { */}
                                    {/*            const newData: any = */}
                                    {/*              structuredClone( */}
                                    {/*                estimateQuantityStation */}
                                    {/*              ); */}
                                    {/*            const station = */}
                                    {/*              newData.station.find( */}
                                    {/*                (s: any) => */}
                                    {/*                  s.estimateQuantityStationId === */}
                                    {/*                  stationProfit.estimateQuantityStationId */}
                                    {/*              ); */}
                                    {/*            if (!station) return; */}
                                    {/*            const subCost = */}
                                    {/*              station.subStation[ */}
                                    {/*                i */}
                                    {/*              ]?.subCost.find( */}
                                    {/*                (c: any) => */}
                                    {/*                  c.estimateQuantityId === */}
                                    {/*                  quantityId */}
                                    {/*              ); */}
                                    {/*            if (subCost) { */}
                                    {/*              subCost.priceCostResult = */}
                                    {/*                subCost.priceCalculate; */}
                                    {/*              debouncedSave(newData); */}
                                    {/*            } */}
                                    {/*          }} */}
                                    {/*        /> */}
                                    {/*      </InputAdornment> */}
                                    {/*    ), */}
                                    {/*  }} */}
                                    {/*  sx={{ */}
                                    {/*    padding: '0 8px', */}
                                    {/*    '& .MuiInputBase-root': { */}
                                    {/*      boxShadow: `0 0 0 1px ${ */}
                                    {/*        clientDiff === 0 */}
                                    {/*          ? '#B0BEC5' */}
                                    {/*          : clientDiff < 0 */}
                                    {/*          ? '#D32F2F' */}
                                    {/*          : '#8BC34A' */}
                                    {/*      }`, */}
                                    {/*    }, */}
                                    {/*  }} */}
                                    {/* /> */}
                                    <NumericFormat
                                      decimalScale={2}
                                      placeholder="0"
                                      customInput={TextField}
                                      thousandSeparator
                                      value={(() => {
                                        const stored =
                                          profitPercentMap[quantityId];
                                        if (stored !== undefined) {
                                          return Number.isInteger(stored)
                                            ? String(stored)
                                            : stored.toFixed(2);
                                        }

                                        const totalCost =
                                          costStation?.cost.find(
                                            (c: any) =>
                                              c.estimateQuantityId ===
                                              quantityId
                                          )?.priceStation ?? 0;
                                        if (!cost || totalCost === 0) return '';

                                        const percent =
                                          (cost.priceCostResult / totalCost) *
                                          100;
                                        return Number.isInteger(percent)
                                          ? String(percent)
                                          : percent.toFixed(2);
                                      })()}
                                      onKeyDown={(e) => {
                                        if (['-', 'e'].includes(e.key))
                                          e.preventDefault();
                                      }}
                                      disabled={isReadonly || isSetZero}
                                      onValueChange={({ floatValue }) => {
                                        setProfitPercentMap((prev) => ({
                                          ...prev,
                                          [quantityId]: floatValue ?? 0,
                                        }));
                                      }}
                                      InputProps={{
                                        endAdornment: (
                                          <InputAdornment position="end">
                                            %
                                          </InputAdornment>
                                        ),
                                      }}
                                      style={{ padding: '0 8px' }}
                                    />
                                    <div className="input-desc">
                                      <div
                                        className="desc-group"
                                        style={{
                                          transition: '0.3s linear',
                                          color: '#8BC34A',
                                        }}
                                      >
                                        <motion.div
                                          key="arrow"
                                          {...motionFadeConfig}
                                          style={{ display: 'flex' }}
                                        >
                                          <ArrowDownwardRoundedIcon
                                            sx={{
                                              fontSize: 16,
                                              transition: '0.3s linear',
                                              rotate: '180deg',
                                            }}
                                          />
                                        </motion.div>
                                        <span>เพิ่มกำไร</span>
                                        {/* <AnimatePresence */}
                                        {/*  mode="wait" */}
                                        {/*  initial={false} */}
                                        {/* > */}
                                        {/*  {clientDiff ? ( */}
                                        {/*    <motion.div */}
                                        {/*      key="arrow" */}
                                        {/*      {...motionFadeConfig} */}
                                        {/*      style={{ display: 'flex' }} */}
                                        {/*    > */}
                                        {/*      <ArrowDownwardRoundedIcon */}
                                        {/*        sx={{ */}
                                        {/*          fontSize: 16, */}
                                        {/*          transition: '0.3s linear', */}
                                        {/*          rotate: */}
                                        {/*            clientDiff < 0 */}
                                        {/*              ? '0deg' */}
                                        {/*              : '180deg', */}
                                        {/*        }} */}
                                        {/*      /> */}
                                        {/*    </motion.div> */}
                                        {/*  ) : ( */}
                                        {/*    <motion.div */}
                                        {/*      key="dot" */}
                                        {/*      {...motionFadeConfig} */}
                                        {/*      style={{ display: 'flex' }} */}
                                        {/*    > */}
                                        {/*      <FiberManualRecordRoundedIcon */}
                                        {/*        sx={{ fontSize: 16 }} */}
                                        {/*      /> */}
                                        {/*    </motion.div> */}
                                        {/*  )} */}
                                        {/*  {clientDiff ? ( */}
                                        {/*    <motion.span */}
                                        {/*      key="diff-value" */}
                                        {/*      {...motionFadeConfig} */}
                                        {/*    > */}
                                        {/*      <CountUp */}
                                        {/*        from={clientDiff} */}
                                        {/*        to={clientDiff || 0} */}
                                        {/*        separator="," */}
                                        {/*        duration={0.3} */}
                                        {/*        decimals={2} */}
                                        {/*      /> */}
                                        {/*    </motion.span> */}
                                        {/*  ) : ( */}
                                        {/*    <motion.span */}
                                        {/*      key="no-diff-value" */}
                                        {/*      {...motionFadeConfig} */}
                                        {/*    > */}
                                        {/*      ไม่เปลี่ยน */}
                                        {/*    </motion.span> */}
                                        {/*  )} */}
                                        {/* </AnimatePresence> */}
                                      </div>
                                      {(() => {
                                        if (i !== 0) return null;

                                        let percent: number | undefined =
                                          profitPercentMap[quantityId];

                                        if (percent === undefined) {
                                          if (
                                            !cost ||
                                            cost.priceCostResult == null
                                          )
                                            return null;

                                          const totalCost =
                                            costStation?.cost.find(
                                              (c: any) =>
                                                c.estimateQuantityId ===
                                                quantityId
                                            )?.priceStation ?? 0;

                                          if (!totalCost) return null;

                                          percent = +(
                                            (cost.priceCostResult / totalCost) *
                                            100
                                          ).toFixed(2);
                                        }

                                        if (percent < 7) {
                                          return (
                                            <div
                                              className="desc-group"
                                              style={{
                                                transition: '0.3s linear',
                                                color: '#D32F2F',
                                              }}
                                            >
                                              <AnimatePresence
                                                mode="wait"
                                                initial={false}
                                              >
                                                <motion.span
                                                  key="percentage-error"
                                                  {...motionFadeConfig}
                                                >
                                                  ไม่ต่ำกว่า&nbsp;7%
                                                </motion.span>
                                              </AnimatePresence>
                                            </div>
                                          );
                                        }
                                        return null;
                                      })()}
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              );
            })}
            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i) => (
                <div className="col" key={`dummy-profit-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`col-detail-${i}`}
                          {...motionRowsTableCalculate}
                        >
                          {[...Array(stationProfit.subStation.length)].map(
                            (_, j) => (
                              <div
                                key={`dummy-detail-${i}-${j}`}
                                ref={register(j)}
                                className="detail"
                                style={{ padding: '16px 0' }}
                              />
                            )
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowProfitTable;
