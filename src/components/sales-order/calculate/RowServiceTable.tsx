import React, { useEffect, useMemo, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import { InputAdornment, TextField } from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import PopoverAction from '@/components/PopoverActionn';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import CountUp from '@/components/CountUp';
import ActionButton from '@/components/ActionButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import ModalAddService from '@/components/sales-order/modal/ModalAddService';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { setSnackBar } from '@/store/features/alert';
import {
  buildEstimateStationBody,
  findStationInEstimateData,
} from '@/utils/estimate';
import apiEstimate from '@/services/order/estimate';

type Props = {
  getStationByEstimateId: () => Promise<void>;
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
  product?: any;
};

const RowServiceTable = ({
  getStationByEstimateId,
  currentEstimateQuantities,
  productStations,
  isReadonly,
  product,
}: Props) => {
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationService, setStationService] = useState<any>({});
  const [openAddService, setOpenAddService] = useState<any>({
    status: false,
    values: {},
  });
  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const stationService = productStations.find(
        (stationItem: any) => stationItem.optionCostType.name === 'บริการ'
      );
      if (stationService) {
        setStationService(stationService);
      }
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  const handleSaveCalculate = async () => {
    if (!estimateQuantityStation?.estimateProducts) {
      console.error('No products found');
      return;
    }

    // Loop through all products and update station for each
    for (const product of estimateQuantityStation.estimateProducts) {
      const body = buildEstimateStationBody(product);
      await apiEstimateQuantityStation.updateStation(body);
    }

    // Update remarkEstimate once after all products are processed
    await apiEstimate.updateRemarkEstimate({
      estimateId: estimateQuantityStation.estimateId,
      remarkEstimate: estimateQuantityStation.remarkEstimate || '',
    });
  };

  const handleDeleteService = async (data: any) => {
    await handleSaveCalculate();
    const res = await apiEstimateQuantityStation.deleteServiceLay({
      estimateQuantityStationItemId: data.estimateQuantityStationItemId,
    });
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await getStationByEstimateId();
    }
  };
  return (
    <>
      <ModalAddService
        data={openAddService}
        setOpenAddService={setOpenAddService}
        getStationByEstimateId={getStationByEstimateId}
      />
      {!isEmpty(stationService) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationService.optionCostType.imageIconUrl}
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationService.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationService.subStation.map(
                      (subStationItem: any, subStationIndex: number) => (
                        <div
                          key={subStationIndex}
                          className="first-detail-item"
                          ref={register(subStationIndex)}
                        >
                          <span>{subStationItem.subTitle}</span>
                          <PopoverAction
                            triggerElement={
                              <div className="kebab">
                                <div className="dot" />
                              </div>
                            }
                            customItems={[
                              {
                                IconElement: () => <SvgDeleteIcon />,
                                title: 'ลบ',
                                onAction: async () => {
                                  await handleDeleteService(subStationItem);
                                },
                              },
                            ]}
                          />
                        </div>
                      )
                    )}
                    {!isReadonly && (
                      <div
                        className="first-detail-item"
                        ref={register(stationService.subStation.length)}
                      >
                        <div className="action-button">
                          <ActionButton
                            variant="contained"
                            color="dark"
                            icon={<AddRoundedIcon />}
                            text="เพิ่มบริการ"
                            borderRadius="8px"
                            onClick={(e: any) => {
                              e.stopPropagation();
                              setOpenAddService({
                                status: true,
                                values: {
                                  estimateProductId: product?.estimateProductId,
                                },
                              });
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          <div className="col-wrap">
            {currentEstimateQuantities.map(
              (quantityItem: any, quantityIndex: number) => {
                const quantityId = quantityItem.estimateQuantityId;
                const finalPrice = stationService.subStation.reduce(
                  (sum: number, subStationItem: any) => {
                    const matchedCost = subStationItem.subCost.find(
                      (c: any) => c.estimateQuantityId === quantityId
                    );
                    return sum + (matchedCost?.priceCostResult || 0);
                  },
                  0
                );

                const clientDiffCount = stationService.subStation.filter(
                  (sub: any) => {
                    const cost = sub.subCost.find(
                      (c: any) => c.estimateQuantityId === quantityId
                    );
                    return cost?.priceCostResult !== cost?.priceCalculate;
                  }
                ).length;

                return (
                  <div className="col" key={quantityIndex}>
                    <div className="col-content">
                      <div className="value">
                        <span>
                          <CountUp
                            from={finalPrice}
                            to={finalPrice || 0}
                            separator=","
                            duration={0.3}
                            decimals={2}
                          />
                        </span>
                        <AnimatePresence mode="sync">
                          {clientDiffCount > 0 && (
                            <motion.div
                              className="badge"
                              {...motionBadgeConfig}
                            >
                              {clientDiffCount}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                      <AnimatePresence mode="sync">
                        {isOpen && (
                          <motion.div
                            className="col-content-detail"
                            key="col-detail"
                            {...motionRowsTableCalculate}
                          >
                            {stationService.subStation.map(
                              (
                                subStationItem: any,
                                subStationIndex: number
                              ) => {
                                const cost = subStationItem.subCost.find(
                                  (c: any) =>
                                    c.estimateQuantityId === quantityId
                                );
                                const clientDiff =
                                  (cost?.priceCostResult || 0) -
                                  (cost?.priceCalculate || 0);

                                return (
                                  <div
                                    key={subStationIndex}
                                    ref={register(subStationIndex)}
                                    className="detail"
                                    style={{ padding: '16px 0' }}
                                  >
                                    <div className="input-group">
                                      <NumericFormat
                                        decimalScale={2}
                                        placeholder="0"
                                        customInput={TextField}
                                        thousandSeparator
                                        value={cost?.priceCostResult ?? ''}
                                        onKeyDown={(e: any) => {
                                          const invalidChars = ['-', 'e'];
                                          if (invalidChars.includes(e.key))
                                            e.preventDefault();
                                        }}
                                        disabled={isReadonly}
                                        onValueChange={({ floatValue }) => {
                                          const newData: any = structuredClone(
                                            estimateQuantityStation
                                          );

                                          const targetStation =
                                            findStationInEstimateData(
                                              newData,
                                              stationService.estimateQuantityStationId
                                            );
                                          if (!targetStation) return;

                                          const subCost =
                                            targetStation.subStation[
                                              subStationIndex
                                            ].subCost.find(
                                              (c: any) =>
                                                c.estimateQuantityId ===
                                                quantityId
                                            );
                                          if (subCost) {
                                            subCost.priceCostResult =
                                              floatValue || 0;
                                            debouncedSave(newData);
                                          }
                                        }}
                                        InputProps={{
                                          endAdornment: (
                                            <InputAdornment position="end">
                                              <SyncRoundedIcon
                                                className="sync"
                                                onClick={() => {
                                                  const newData: any =
                                                    structuredClone(
                                                      estimateQuantityStation
                                                    );
                                                  const targetStation =
                                                    findStationInEstimateData(
                                                      newData,
                                                      stationService.estimateQuantityStationId
                                                    );
                                                  if (!targetStation) return;
                                                  const subCost =
                                                    targetStation.subStation[
                                                      subStationIndex
                                                    ].subCost.find(
                                                      (c: any) =>
                                                        c.estimateQuantityId ===
                                                        quantityId
                                                    );
                                                  if (subCost) {
                                                    subCost.priceCostResult =
                                                      subCost.priceCalculate;
                                                    debouncedSave(newData);
                                                  }
                                                }}
                                              />
                                            </InputAdornment>
                                          ),
                                        }}
                                        sx={{
                                          padding: '0 8px',
                                          '& .MuiInputBase-root': {
                                            boxShadow: `0 0 0 1px ${
                                              clientDiff === 0
                                                ? '#B0BEC5'
                                                : clientDiff < 0
                                                ? '#D32F2F'
                                                : '#8BC34A'
                                            }`,
                                          },
                                        }}
                                      />
                                      <div className="input-desc">
                                        <div
                                          className="desc-group"
                                          style={{
                                            transition: '0.3s linear',
                                            color: !clientDiff
                                              ? '#B0BEC5'
                                              : clientDiff < 0
                                              ? '#D32F2F'
                                              : '#8BC34A',
                                          }}
                                        >
                                          <AnimatePresence
                                            mode="wait"
                                            initial={false}
                                          >
                                            {clientDiff ? (
                                              <motion.div
                                                key="arrow"
                                                {...motionFadeConfig}
                                                style={{
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                }}
                                              >
                                                <ArrowDownwardRoundedIcon
                                                  sx={{
                                                    fontSize: 16,
                                                    transition: '0.3s linear',
                                                    rotate:
                                                      clientDiff < 0
                                                        ? '0deg'
                                                        : '180deg',
                                                  }}
                                                />
                                              </motion.div>
                                            ) : (
                                              <motion.div
                                                key="dot"
                                                {...motionFadeConfig}
                                                style={{
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                }}
                                              >
                                                <FiberManualRecordRoundedIcon
                                                  sx={{ fontSize: 16 }}
                                                />
                                              </motion.div>
                                            )}
                                            {clientDiff ? (
                                              <motion.span
                                                key="diff-value"
                                                {...motionFadeConfig}
                                              >
                                                <CountUp
                                                  from={clientDiff}
                                                  to={clientDiff || 0}
                                                  separator=","
                                                  duration={0.3}
                                                  decimals={2}
                                                />
                                              </motion.span>
                                            ) : (
                                              <motion.span
                                                key="no-diff-value"
                                                {...motionFadeConfig}
                                              >
                                                ไม่เปลี่ยน
                                              </motion.span>
                                            )}
                                          </AnimatePresence>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                            )}
                            {!isReadonly && (
                              <div
                                key={stationService.subStation.length}
                                ref={register(stationService.subStation.length)}
                                className="detail"
                                style={{ padding: '16px 0' }}
                              />
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                );
              }
            )}
            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i: number) => (
                <div className="col" key={`dummy-service-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`col-detail-${i}`}
                          {...motionRowsTableCalculate}
                        >
                          {[...Array(stationService.subStation.length + 1)].map(
                            (_, j) => (
                              <div
                                key={`dummy-detail-${i}-${j}`}
                                ref={register(j)}
                                className="detail"
                                style={{ padding: '16px 0' }}
                              />
                            )
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowServiceTable;
