import React, { useState } from 'react';
import styled from 'styled-components';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import PopoverAction from '@/components/PopoverActionn';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import { LoadingFadein } from '@/styles/share.styled';
import { useAppDispatch, useAppSelector } from '@/store';
import { salesOrderSelector } from '@/store/features/estimate';
import ModalEditQuantity from '@/components/sales-order/modal/ModalEditQuantity';
import ModalCreateQuantity from '@/components/sales-order/modal/ModalCreateQuantity';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { setSnackBar } from '@/store/features/alert';
import CountUp from '@/components/CountUp';
import { buildEstimateStationBody } from '@/utils/estimate';
import apiEstimate from '@/services/order/estimate';

const HeadTableStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  height: 56px;
  animation: ${LoadingFadein} 0.3s ease-in;
  .col-wrap {
    flex: 2 1 0%;
    height: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    max-width: 100%;
    overflow: hidden;
    &.first {
      flex: 1 1 0%;
      border-right: 1px solid #dbe2e5;
    }
    .col {
      flex: 1 1 0%;
      border-right: 1px solid #dbe2e5;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 16px;
      justify-content: space-between;
      max-width: 100%;
      overflow: hidden;
      &.empty {
        justify-content: center;
        svg {
          color: #cfd8dc;
        }
      }
      &.add {
        justify-content: center;
        cursor: pointer;
        min-width: 56px;
        max-width: 56px;
        width: 56px;
        * {
          transition: 0.3s ease-out;
        }
        svg {
          color: initial;
        }
        &:hover {
          svg {
            rotate: 90deg;
          }
        }
      }
      &:last-child {
        border: 0;
      }
      .quantity {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
`;
type Props = {
  getStationByEstimateId: () => Promise<void>;
  currentPage: number;
  currentEstimateQuantities: any[];
  product: any;
  isReadonly: boolean;
  currentEstimateQuantityIds?: number[];
};
const HeadTable = ({
  getStationByEstimateId,
  currentEstimateQuantities: _currentEstimateQuantities,
  product,
  isReadonly,
  currentEstimateQuantityIds,
}: Props) => {
  const { estimateProductId } = product;
  const dispatch = useAppDispatch();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [openCreateQuantity, setOpenCreateQuantity] = useState<boolean>(false);
  const [editQuantity, setEditQuantity] = useState<any>({
    status: false,
    quantityId: '',
  });

  // ใช้ currentEstimateQuantityIds ที่ส่งมาจาก parent เพื่อ filter quantities ที่ถูกต้อง
  const currentQuantities = currentEstimateQuantityIds
    ? product.estimateQuantity?.filter((q: any) =>
        currentEstimateQuantityIds.includes(q.estimateQuantityId)
      ) || []
    : product.estimateQuantity || [];

  const handleEdit = (qtyItem: any) => {
    setEditQuantity({
      status: true,
      estimateQuantityId: qtyItem.estimateQuantityId,
      quantity: qtyItem.quantity,
    });
  };

  const handleDeleteQty = async (qty: any) => {
    if (estimateQuantityStation?.estimateProducts) {
      // Loop through all products and update station for each
      for (const product of estimateQuantityStation.estimateProducts) {
        const saveBody = buildEstimateStationBody(product);
        await apiEstimateQuantityStation.updateStation(saveBody);
      }

      // Update remarkEstimate once after all products are processed
      await apiEstimate.updateRemarkEstimate({
        estimateId: estimateQuantityStation.estimateId,
        remarkEstimate: estimateQuantityStation.remarkEstimate || '',
      });
    }
    const body = {
      estimateProductId,
      estimateQuantityId: qty.estimateQuantityId,
    };
    const res = await apiEstimateQuantityStation.deleteEstimateQuantity(body);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await getStationByEstimateId();
    }
  };
  return (
    <>
      <ModalCreateQuantity
        open={openCreateQuantity}
        setOpen={setOpenCreateQuantity}
        getStationByEstimateId={getStationByEstimateId}
        estimateProductId={estimateProductId}
      />
      <ModalEditQuantity
        data={editQuantity}
        setEditQuantity={setEditQuantity}
        getStationByEstimateId={getStationByEstimateId}
        estimateProductId={estimateProductId}
      />
      <HeadTableStyled>
        <div className="col-wrap first">
          <div className="col">จำนวน ({currentQuantities.length})</div>
        </div>
        <div className="col-wrap">
          {currentQuantities.map((qtyItem: any, index: number) => (
            <div className="col" key={qtyItem.estimateQuantityId}>
              <div className="quantity">
                <CountUp
                  key={`${index}-${qtyItem.quantity}`}
                  from={0}
                  to={qtyItem.quantity}
                  separator=","
                  duration={0.3}
                  decimals={0}
                  suffix={'ชิ้น'}
                />
              </div>
              {!isReadonly && (
                <PopoverAction
                  triggerElement={
                    <div className="kebab">
                      <div className="dot" />
                    </div>
                  }
                  customItems={[
                    {
                      IconElement: () => <SvgEditIcon />,
                      title: 'แก้ไข',
                      onAction: () => handleEdit(qtyItem),
                    },
                    {
                      IconElement: () => <SvgDeleteIcon />,
                      title: 'ลบ',
                      onAction: async () => handleDeleteQty(qtyItem),
                    },
                  ]}
                />
              )}
            </div>
          ))}
          {/* ช่องเปล่าเติมให้ครบ 5 */}
          {[...Array(Math.max(0, 5 - currentQuantities.length))].map(
            (_, idx) => (
              <div className="col empty" key={`empty-${idx}`}>
                <AddRoundedIcon />
              </div>
            )
          )}
          <div
            className="col empty add"
            onClick={() => {
              if (!isReadonly) {
                setOpenCreateQuantity(true);
              }
            }}
          >
            <AddRoundedIcon />
          </div>
        </div>
      </HeadTableStyled>
    </>
  );
};

export default HeadTable;
