import React, { useEffect, useMemo, useState } from 'react';
import ExpandMoreRoundedIcon from '@mui/icons-material/ExpandMoreRounded';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionBadgeConfig,
  motionFadeConfig,
  motionRowsTableCalculate,
} from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import { InputAdornment, TextField } from '@mui/material';
import SyncRoundedIcon from '@mui/icons-material/SyncRounded';
import ArrowDownwardRoundedIcon from '@mui/icons-material/ArrowDownwardRounded';
import { debounce, isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateQuantityStation,
} from '@/store/features/estimate';
import Image from 'next/image';
import { RowsTableStyled } from '@/components/sales-order/calculate/RowMaterialTable';
import useSyncRowHeights from '@/hooks/useSyncRowHeights';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import CountUp from '@/components/CountUp';

type Props = {
  currentEstimateQuantities: any[];
  productStations?: any[];
  isReadonly: boolean;
  product?: any;
};

const RowDiscountTable = ({
  currentEstimateQuantities,
  productStations,
  isReadonly,
  product,
}: Props) => {
  // ตรวจสอบว่า product นี้มี isSetZero หรือไม่
  const isSetZero = product?.isSetZero || false;
  const dispatch = useAppDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const { register } = useSyncRowHeights();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [stationDiscount, setStationDiscount] = useState<any>({});

  const costStation = useMemo(
    () =>
      productStations && !isEmpty(productStations)
        ? productStations.find((s: any) => s.optionCostType.name === 'ต้นทุน')
        : undefined,
    [productStations]
  );

  const profitStation = useMemo(
    () =>
      productStations && !isEmpty(productStations)
        ? productStations.find((s: any) => s.optionCostType.name === 'กำไร')
        : undefined,
    [productStations]
  );

  useEffect(() => {
    if (productStations && !isEmpty(productStations)) {
      const station = productStations.find(
        (s: any) => s.optionCostType.name === 'ส่วนลด'
      );
      if (station) setStationDiscount(station);
    }
  }, [productStations]);

  const debouncedSave = useMemo(
    () =>
      debounce((newData) => {
        dispatch(setEstimateQuantityStation(newData));
      }, 300),
    [dispatch]
  );

  const discountColumnData = useMemo(() => {
    if (isEmpty(stationDiscount)) return [];

    return currentEstimateQuantities.map((q) => {
      const qId = q.estimateQuantityId;

      //  ต้นทุนสุทธิ
      const totalCost =
        costStation?.cost.find((c: any) => c.estimateQuantityId === qId)
          ?.priceStation ?? 0;

      //  กำไรก่อน VAT
      const profit =
        profitStation?.cost.find((c: any) => c.estimateQuantityId === qId)
          ?.priceStation ?? 0;

      //  ยอดส่วนลด (finalPrice = ผลรวมทุก subStation)
      const discount = stationDiscount.subStation.reduce(
        (sum: number, sub: any) => {
          const c = sub.subCost.find((x: any) => x.estimateQuantityId === qId);
          return sum + (c?.priceCostResult || 0);
        },
        0
      );

      //  เปอร์เซ็นต์ส่วนลด %
      const base = totalCost + profit;
      const discountPercentGlobal = base
        ? Number(((discount / base) * 100).toFixed(2))
        : undefined;

      // จำนวน field ที่ client แก้เอง
      const clientDiffCount = stationDiscount.subStation.filter((sub: any) => {
        const c = sub.subCost.find((c: any) => c.estimateQuantityId === qId);
        return c && c.priceCostResult !== c.priceCalculate;
      }).length;

      return { qId, discount, discountPercentGlobal, clientDiffCount };
    });
  }, [currentEstimateQuantities, costStation, profitStation, stationDiscount]);

  return (
    <>
      {!isEmpty(stationDiscount) && (
        <RowsTableStyled>
          <div className="col-wrap first">
            <div className="col cursor-pointer">
              <div className="group">
                <div className="topic">
                  <div className="icon">
                    <Image
                      src={stationDiscount.optionCostType.imageIconUrl}
                      alt=""
                      width={24}
                      height={24}
                    />
                  </div>
                  <span>{stationDiscount.title}</span>
                </div>
                <ExpandMoreRoundedIcon
                  sx={{
                    transition: '0.2s linear',
                    rotate: isOpen ? '180deg' : '0deg',
                  }}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
              <AnimatePresence mode="sync">
                {isOpen && (
                  <motion.div
                    className="first-detail"
                    key="first-detail"
                    {...motionRowsTableCalculate}
                  >
                    {stationDiscount.subStation.map(
                      (subStationItem: any, index: number) => (
                        <div
                          key={index}
                          className="first-detail-item"
                          ref={register(index)}
                        >
                          <span>{subStationItem.subTitle}</span>
                          <div
                            style={{
                              marginTop: '8px',
                              fontWeight: '400',
                              color: '#B0BEC5',
                            }}
                          >
                            โปรเจคล่าสุด (LD-2023001XXX) ส่วนลด XX%
                          </div>
                        </div>
                      )
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          <div className="col-wrap">
            {currentEstimateQuantities.map((quantityItem: any) => {
              const quantityId = quantityItem.estimateQuantityId;
              const colData = discountColumnData.find(
                (d) => d.qId === quantityId
              );
              const discountPercentGlobal = colData?.discountPercentGlobal;

              const finalPrice = stationDiscount.subStation.reduce(
                (sum: number, sub: any) => {
                  const cost = sub.subCost.find(
                    (c: any) => c.estimateQuantityId === quantityId
                  );
                  return sum + (cost?.priceCostResult || 0);
                },
                0
              );

              const clientDiffCount = stationDiscount.subStation.filter(
                (sub: any) => {
                  const cost = sub.subCost.find(
                    (c: any) => c.estimateQuantityId === quantityId
                  );
                  return cost?.priceCostResult !== cost?.priceCalculate;
                }
              ).length;

              return (
                <div className="col" key={quantityId}>
                  <div className="col-content">
                    <div className="value" style={{ color: '#D32F2F' }}>
                      <span>
                        <CountUp
                          key={`${quantityId}-${finalPrice}`}
                          from={0}
                          to={finalPrice}
                          separator=","
                          duration={0.3}
                          decimals={2}
                        />
                      </span>
                      <AnimatePresence mode="sync">
                        {clientDiffCount > 0 && (
                          <motion.div className="badge" {...motionBadgeConfig}>
                            {clientDiffCount}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>

                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key="col-detail"
                          {...motionRowsTableCalculate}
                        >
                          {stationDiscount.subStation.map(
                            (subStationItem: any, subStationIndex: number) => {
                              const cost = subStationItem.subCost.find(
                                (c: any) => c.estimateQuantityId === quantityId
                              );

                              const clientDiff =
                                (cost?.priceCostResult || 0) -
                                (cost?.priceCalculate || 0);

                              return (
                                <div
                                  key={subStationIndex}
                                  ref={register(subStationIndex)}
                                  className="detail"
                                  style={{ padding: '16px 0' }}
                                >
                                  <div className="input-group">
                                    <NumericFormat
                                      placeholder="0"
                                      decimalScale={2}
                                      customInput={TextField}
                                      thousandSeparator
                                      value={cost?.priceCostResult ?? ''}
                                      onKeyDown={(e: any) => {
                                        if (['-', 'e'].includes(e.key))
                                          e.preventDefault();
                                      }}
                                      disabled={isReadonly || isSetZero}
                                      onValueChange={({ floatValue }) => {
                                        const newData: any = structuredClone(
                                          estimateQuantityStation
                                        );

                                        // Find the current product that contains this station
                                        const currentProduct =
                                          newData.estimateProducts?.find(
                                            (product: any) =>
                                              product.station?.some(
                                                (s: any) =>
                                                  s.estimateQuantityStationId ===
                                                  stationDiscount.estimateQuantityStationId
                                              )
                                          );

                                        if (!currentProduct) return;

                                        const station =
                                          currentProduct.station.find(
                                            (s: any) =>
                                              s.estimateQuantityStationId ===
                                              stationDiscount.estimateQuantityStationId
                                          );
                                        if (!station) return;
                                        const targetSub =
                                          station.subStation[subStationIndex];
                                        const targetCost =
                                          targetSub?.subCost.find(
                                            (c: any) =>
                                              c.estimateQuantityId ===
                                              quantityId
                                          );
                                        if (targetCost) {
                                          targetCost.priceCostResult =
                                            floatValue || 0;
                                          debouncedSave(newData);
                                        }
                                      }}
                                      InputProps={{
                                        endAdornment: (
                                          <InputAdornment position="end">
                                            <SyncRoundedIcon
                                              className="sync"
                                              onClick={() => {
                                                const newData: any =
                                                  structuredClone(
                                                    estimateQuantityStation
                                                  );

                                                // Find the current product that contains this station
                                                const currentProduct =
                                                  newData.estimateProducts?.find(
                                                    (product: any) =>
                                                      product.station?.some(
                                                        (s: any) =>
                                                          s.estimateQuantityStationId ===
                                                          stationDiscount.estimateQuantityStationId
                                                      )
                                                  );

                                                if (!currentProduct) return;

                                                const station =
                                                  currentProduct.station.find(
                                                    (s: any) =>
                                                      s.estimateQuantityStationId ===
                                                      stationDiscount.estimateQuantityStationId
                                                  );
                                                if (!station) return;
                                                const targetSub =
                                                  station.subStation[
                                                    subStationIndex
                                                  ];
                                                const targetCost =
                                                  targetSub?.subCost.find(
                                                    (c: any) =>
                                                      c.estimateQuantityId ===
                                                      quantityId
                                                  );
                                                if (targetCost) {
                                                  targetCost.priceCostResult =
                                                    targetCost.priceCalculate;
                                                  debouncedSave(newData);
                                                }
                                              }}
                                            />
                                          </InputAdornment>
                                        ),
                                      }}
                                      sx={{
                                        padding: '0 8px',
                                        '& .MuiInputBase-root': {
                                          boxShadow: `0 0 0 1px ${
                                            clientDiff === 0
                                              ? '#B0BEC5'
                                              : clientDiff < 0
                                              ? '#8BC34A'
                                              : '#D32F2F'
                                          }`,
                                        },
                                      }}
                                    />
                                    <div className="input-desc">
                                      <div
                                        className="desc-group"
                                        style={{
                                          transition: '0.3s linear',
                                          color: !clientDiff
                                            ? '#B0BEC5'
                                            : clientDiff < 0
                                            ? '#8BC34A'
                                            : '#D32F2F',
                                        }}
                                      >
                                        <AnimatePresence
                                          mode="wait"
                                          initial={false}
                                        >
                                          {clientDiff ? (
                                            <motion.div
                                              key="arrow"
                                              {...motionFadeConfig}
                                              style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                              }}
                                            >
                                              <ArrowDownwardRoundedIcon
                                                sx={{
                                                  fontSize: 16,
                                                  transition: '0.3s linear',
                                                  rotate:
                                                    clientDiff < 0
                                                      ? '180deg'
                                                      : '0deg',
                                                }}
                                              />
                                            </motion.div>
                                          ) : (
                                            <motion.div
                                              key="dot"
                                              {...motionFadeConfig}
                                              style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                              }}
                                            >
                                              <FiberManualRecordRoundedIcon
                                                sx={{ fontSize: 16 }}
                                              />
                                            </motion.div>
                                          )}

                                          {clientDiff ? (
                                            <motion.span
                                              key="diff-value"
                                              {...motionFadeConfig}
                                            >
                                              <CountUp
                                                from={clientDiff}
                                                to={clientDiff || 0}
                                                separator=","
                                                duration={0.3}
                                                decimals={2}
                                              />
                                            </motion.span>
                                          ) : (
                                            <motion.span
                                              key="no-diff-value"
                                              {...motionFadeConfig}
                                            >
                                              ไม่เปลี่ยน
                                            </motion.span>
                                          )}
                                        </AnimatePresence>
                                      </div>
                                      <div
                                        className="desc-group"
                                        style={{
                                          transition: '0.3s linear',
                                          color:
                                            clientDiff === 0
                                              ? '#B0BEC5'
                                              : clientDiff < 0
                                              ? '#8BC34A'
                                              : '#D32F2F',
                                        }}
                                      >
                                        <AnimatePresence
                                          mode="wait"
                                          initial={false}
                                        >
                                          {subStationIndex === 0 &&
                                            discountPercentGlobal !==
                                              undefined && (
                                              <motion.span
                                                key="percentage"
                                                {...motionFadeConfig}
                                              >
                                                <CountUp
                                                  from={discountPercentGlobal}
                                                  to={discountPercentGlobal}
                                                  separator=","
                                                  duration={0.3}
                                                  decimals={2}
                                                />
                                                %
                                              </motion.span>
                                            )}
                                        </AnimatePresence>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              );
            })}

            {[...Array(Math.max(0, 5 - currentEstimateQuantities.length))].map(
              (_, i) => (
                <div className="col" key={`dummy-discount-${i}`}>
                  <div className="col-content">
                    <div className="value" />
                    <AnimatePresence mode="sync">
                      {isOpen && (
                        <motion.div
                          className="col-content-detail"
                          key={`col-detail-discount-${i}`}
                          {...motionRowsTableCalculate}
                        >
                          {[...Array(stationDiscount.subStation.length)].map(
                            (_, j) => (
                              <div
                                key={`dummy-detail-${i}-${j}`}
                                ref={register(j)}
                                className="detail"
                                style={{ padding: '16px 0' }}
                              />
                            )
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )
            )}

            <div className="col add border-0" />
          </div>
        </RowsTableStyled>
      )}
    </>
  );
};

export default RowDiscountTable;
