import React, { useState } from 'react';
import styled from 'styled-components';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  TextField,
} from '@mui/material';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import AccountCircleRoundedIcon from '@mui/icons-material/AccountCircleRounded';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import { useRouter } from 'next/router';
// import { ModalCreateProductContentStyled } from '@/components/layout-data/ModalCreateOrder';
import { Search } from '@mui/icons-material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import ModalSetting from '@/components/order/ModalSetting';
import { useAppDispatch, useAppSelector } from '@/store';
import { salesOrderSelector } from '@/store/features/estimate';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';
import apiEstimate from '@/services/order/estimate';
import { setSnackBar } from '@/store/features/alert';
import ActionButton from '@/components/ActionButton';
import SvgTableIcon from '@/components/svg-icon/SvgTableIcon';
import { AnimatePresence, motion } from 'framer-motion';
import { motionFadeConfig } from '@/utils/motion';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import SvgDownloadIcon from '@/components/svg-icon/SvgDownloadIcon';
import { isEmpty } from 'lodash';

dayjs.extend(utc);
const SalesOrderDetailHeaderStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28px 24px 28px;
  column-gap: 40px;
  row-gap: 24px;
  flex-wrap: wrap;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 820px) {
    padding: 28px 16px 28px;
  }
  @media screen and (max-width: 650px) {
    row-gap: 16px;
    padding: 24px 16px 24px;
  }
  .od-number {
    font-size: 40px;
    font-weight: 600;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @media screen and (max-width: 650px) {
      font-size: 28px;
    }
    @media screen and (max-width: 350px) {
      font-size: 22px;
    }
  }
`;

type SalesOrderDetailHeaderProps = {
  reloadOrder: (action?: string) => void;
  forceOpenSettingModal: boolean;
  closeSettingModal: () => void;
  setOpenChangeProject: (open: boolean) => void;
  confirmSalePriceSelected?: any;
  setOpenConfirmSalePrice?: (open: boolean) => void;
  setOpenDownloadModal?: (open: boolean) => void;
};
const SalesOrderDetailHeader = ({
  reloadOrder,
  forceOpenSettingModal,
  closeSettingModal,
  setOpenChangeProject,
  confirmSalePriceSelected,
  setOpenConfirmSalePrice,
  setOpenDownloadModal,
}: SalesOrderDetailHeaderProps) => {
  const router = useRouter();
  const { salesOrderId } = router.query;
  const dispatch = useAppDispatch();
  const [anchorEl, setAnchorEl] = useState(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [modalCancel, setModalCancel] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const [openChangeCustomer, setOpenChangeCustomer] = useState<boolean>(false);
  const [openModalSetting, setOpenModalSetting] = useState<boolean>(false);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [customerFilters, setCustomerFilters] = useState({
    page: 0,
    size: 100,
    search: '',
  });
  const { salesOrderById } = useAppSelector(salesOrderSelector);
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCancelOrder = async () => {
    setSubmitting(true);
    const res = await apiEstimate.cancelSalesOrder(salesOrderById.id);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleCloseModalCancel();
      await router.push('/sales-order');
    }
    setSubmitting(false);
  };
  const handleChangeCustomer = () => {
    handleOpen().then();
  };
  const handleOpen = async () => {
    setCustomerFilters({
      page: 0,
      size: 100,
      search: '',
    });
    setSearchInput('');
    setOpenChangeCustomer(true);
  };

  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setCustomerFilters({
        ...customerFilters,
        search: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };
  const handleClose = () => {
    setAnchorEl(null);
    setOpenChangeCustomer(false);
  };

  const handleCloseModalCancel = () => {
    setModalCancel({
      ...modalCancel,
      open: false,
    });
  };
  const handleOpenModalCancel = () => {
    setModalCancel({
      open: true,
      title: '',
      description: '',
    });
  };

  const handleOpenModalSetting = () => {
    setOpenModalSetting(true);
  };

  console.log('salesOrderById', salesOrderById);
  return (
    <>
      <ModalSetting
        open={openModalSetting || forceOpenSettingModal}
        handleClose={() => {
          closeSettingModal();
          setOpenModalSetting(false);
        }}
        reloadOrder={() => {
          reloadOrder('confirm');
        }}
      />
      <Dialog
        open={openChangeCustomer}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  เปลี่ยนลูกค้า
                </div>
                <div
                  className="x-close"
                  onClick={() => setOpenChangeCustomer(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหาลูกค้า"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                      '.MuiInputBase-input': {
                        paddingLeft: '0',
                      },
                    }}
                  />
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={modalCancel.open}
        onClickClose={() => {
          handleCloseModalCancel();
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยกเลิกรายการ
          </div>
        }
        confirmDescription={`คุณต้องการยกเลิกรายการ “${salesOrderById.estimateNo}” รายการนี้จะถูกเปลี่ยนสถานะเป็น “ยกเลิก”`}
        loadingConfirm={submitting}
        onConfirm={async () => {
          await handleCancelOrder();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <SalesOrderDetailHeaderStyled>
        <div className="od-number">{salesOrderById.estimateNo}</div>
        <div className="flex items-center gap-[16px]">
          <AnimatePresence mode="sync">
            {salesOrderById?.estimateStatus.name === 'เสนอราคา' &&
              confirmSalePriceSelected &&
              setOpenConfirmSalePrice && (
                <motion.div key="confirm-price" {...motionFadeConfig}>
                  <ActionButton
                    variant="contained"
                    color="Hon"
                    icon={<CheckCircleRoundedIcon />}
                    text="ยืนยันราคาเสนอขาย"
                    borderRadius="8px"
                    disabled={
                      confirmSalePriceSelected.layData &&
                      confirmSalePriceSelected.layData.length > 0
                        ? isEmpty(confirmSalePriceSelected.layData)
                        : true
                    }
                    onClick={() => {
                      if (!isEmpty(confirmSalePriceSelected.layData)) {
                        setOpenConfirmSalePrice(true);
                      }
                    }}
                  />
                </motion.div>
              )}
            {salesOrderById?.estimateStatus.name === 'เสนอราคา' &&
              setOpenDownloadModal && (
                <motion.div key="download-quote" {...motionFadeConfig}>
                  <ActionButton
                    variant="outlined"
                    color="blueGrey"
                    icon={<SvgDownloadIcon />}
                    text="ใบเสนอราคา"
                    borderRadius="8px"
                    onClick={() => {
                      setOpenDownloadModal(true);
                    }}
                  />
                </motion.div>
              )}
            {salesOrderById?.estimateStatus.id >= 3 && (
              <motion.div key="calculate-price" {...motionFadeConfig}>
                <ActionButton
                  variant="contained"
                  color="dark"
                  icon={<SvgTableIcon />}
                  text={`${
                    salesOrderById?.estimateStatus.id > 3
                      ? 'ดูคำนวณราคา'
                      : 'คำนวณราคา'
                  }`}
                  borderRadius="8px"
                  onClick={async () => {
                    await router.push(`/sales-order/${salesOrderId}/calculate`);
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>
          <div>
            <Button
              variant="outlined"
              size="small"
              color="blueGrey"
              sx={{
                minWidth: '40px',
              }}
              onClick={handleClick}
            >
              <div className="action-dot">
                <div className="dot" />
              </div>
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{
                marginTop: '12px',
                '.MuiList-root': {
                  padding: '8px',
                  width: '192px',
                  display: 'flex',
                  flexDirection: 'column',
                  rowGap: '4px',
                },
                li: {
                  width: 'auto',
                },
              }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div
                  className="drop-menu"
                  onClick={() => {
                    setOpenChangeProject(true);
                  }}
                >
                  <SvgEditIcon />
                  เปลี่ยนโปรเจกต์
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div
                  className="drop-menu"
                  onClick={() => {
                    handleOpenModalSetting();
                  }}
                >
                  <SettingsOutlinedIcon />
                  ตั้งค่าออเดอร์
                </div>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  handleClose();
                  handleChangeCustomer();
                }}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div className="drop-menu">
                  <AccountCircleRoundedIcon />
                  เปลี่ยนลูกค้า
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FDE8EF',
                  },
                }}
              >
                <div
                  className="drop-menu text-[#D32F2F]"
                  onClick={() => {
                    handleOpenModalCancel();
                  }}
                >
                  <Image
                    src="/icons/icon-scan-delete.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                  ยกเลิกรายการ
                </div>
              </MenuItem>
            </Menu>
          </div>
        </div>
      </SalesOrderDetailHeaderStyled>
      <HrSpaceStyle />
    </>
  );
};

export default SalesOrderDetailHeader;
