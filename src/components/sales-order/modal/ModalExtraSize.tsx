import React, { <PERSON>actNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import styled from 'styled-components';
import { AnimatePresence, motion } from 'framer-motion';
import { motionListItemConfig } from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import apiEstimateProduct from '@/services/order/estimate-product';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const ModalExtraSizeStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .quantity-row {
    width: 100%;
    display: flex;
    column-gap: 12px;
    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0 !important;
    }
    .order {
      width: 24px;
      min-width: 24px;
      display: flex;
      height: 40px;
      align-items: center;
    }
  }
`;

type Props = {
  modalExtraSize: any;
  setModalExtraSize: (data: any) => void;
  getStationByEstimateId: () => Promise<void>;
};

const validationSchema = yup.object().shape({
  extraSize: yup
    .array()
    .of(
      yup.object().shape({
        width: yup
          .number()
          .typeError('กรอกความกว้าง')
          .required('กรอกความกว้าง')
          .moreThan(0, 'ค่าต้องมากกว่าศูนย์'),
        height: yup
          .number()
          .typeError('กรอกความยาว')
          .required('กรอกความยาว')
          .moreThan(0, 'ค่าต้องมากกว่าศูนย์'),
      })
    )
    .required(),
});

const ModalExtraSize = ({
  modalExtraSize,
  setModalExtraSize,
  getStationByEstimateId,
}: Props) => {
  // const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);
  const {
    handleSubmit,
    control,
    formState: { errors: hookFormErrors },
    reset,
    // watch,
  } = useForm<any>({
    mode: 'onChange',
    resolver: yupResolver(validationSchema),
    defaultValues: {
      estimateProductExtraId: '',
      extraSize: [],
    },
  } as any);
  const { fields } = useFieldArray({
    control,
    name: 'extraSize',
  });

  const onSubmit = async (values: any) => {
    setSubmitting(true);
    const res = await apiEstimateProduct.saveEstimateProductExtraSize(values);
    if (!res.isError) {
      setModalExtraSize({
        ...modalExtraSize,
        isOpen: false,
      });
      await getStationByEstimateId();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };

  const getEstimateExtraSizeByEstimateProductExtraId = async () => {
    const res =
      await apiEstimateProduct.getEstimateExtraSizeByEstimateProductExtraId(
        modalExtraSize.data.estimateProductExtraId
      );
    if (!res.isError) {
      const defaultItems = res.data.map((item: any) => {
        return {
          estimateProductExtraSizeId: item.estimateProductExtraSizeId,
          width: item.width,
          height: item.height,
          result: '',
        };
      });
      reset({
        estimateProductExtraId: modalExtraSize.data.estimateProductExtraId,
        extraSize: defaultItems,
      });
    }
    setHasMounted(true);
  };

  useEffect(() => {
    if (modalExtraSize.isOpen) {
      getEstimateExtraSizeByEstimateProductExtraId().then();
    } else {
      setHasMounted(false);
    }
  }, [modalExtraSize]);

  return (
    <Dialog
      open={modalExtraSize.isOpen}
      onClose={() =>
        setModalExtraSize({
          ...modalExtraSize,
          isOpen: false,
        })
      }
    >
      <DialogContent>
        <FormModal
          width={500}
          title="กำหนดขนาด"
          handleClose={() => {
            setModalExtraSize({
              ...modalExtraSize,
              isOpen: false,
            });
          }}
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p />
              <ModalExtraSizeStyled>
                <AnimatePresence mode="sync" initial={false}>
                  {hasMounted &&
                    fields.map((field, index) => (
                      <motion.div
                        key={field.id}
                        className="quantity-row"
                        {...motionListItemConfig}
                        animate={{
                          ...motionListItemConfig.animate,
                          marginBottom: '24px',
                        }}
                      >
                        <div className="order">#{index + 1}</div>
                        {/* width */}
                        <Controller
                          name={`extraSize.${index}.width`}
                          control={control}
                          rules={{ required: 'กรอกความกว้าง' }}
                          render={({ field }) => (
                            <NumericFormat
                              decimalScale={2}
                              getInputRef={field.ref}
                              onBlur={field.onBlur}
                              placeholder="กว้าง"
                              customInput={TextField}
                              thousandSeparator
                              value={field.value || ''}
                              onValueChange={({ floatValue }) => {
                                field.onChange(floatValue ?? '');
                              }}
                              onKeyDown={(e) => {
                                if (['-', 'e'].includes(e.key))
                                  e.preventDefault();
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    นิ้ว.
                                  </InputAdornment>
                                ),
                              }}
                              error={
                                Array.isArray(hookFormErrors.extraSize) &&
                                !!hookFormErrors.extraSize[index]?.width
                              }
                              helperText={
                                Array.isArray(hookFormErrors.extraSize)
                                  ? (hookFormErrors.extraSize[index]?.width
                                      ?.message as ReactNode)
                                  : undefined
                              }
                              style={{ padding: '0 8px' }}
                            />
                          )}
                        />

                        {/* height */}
                        <Controller
                          name={`extraSize.${index}.height`}
                          control={control}
                          rules={{ required: 'กรอกความยาว' }}
                          render={({ field }) => (
                            <NumericFormat
                              decimalScale={2}
                              getInputRef={field.ref}
                              onBlur={field.onBlur}
                              placeholder="ยาว"
                              customInput={TextField}
                              thousandSeparator
                              value={field.value || ''}
                              onValueChange={({ floatValue }) => {
                                field.onChange(floatValue ?? '');
                              }}
                              onKeyDown={(e) => {
                                if (['-', 'e'].includes(e.key))
                                  e.preventDefault();
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    นิ้ว.
                                  </InputAdornment>
                                ),
                              }}
                              error={
                                Array.isArray(hookFormErrors.extraSize) &&
                                !!hookFormErrors.extraSize[index]?.height
                              }
                              helperText={
                                Array.isArray(hookFormErrors.extraSize)
                                  ? (hookFormErrors.extraSize[index]?.height
                                      ?.message as ReactNode)
                                  : undefined
                              }
                              style={{ padding: '0 8px' }}
                            />
                          )}
                        />

                        {/* result */}
                        <Controller
                          name={`extraSize.${index}.result`}
                          control={control}
                          render={({ field }) => (
                            <NumericFormat
                              decimalScale={2}
                              getInputRef={field.ref}
                              onBlur={field.onBlur}
                              placeholder="ราคา"
                              customInput={TextField}
                              thousandSeparator
                              value={field.value || ''}
                              onValueChange={({ floatValue }) => {
                                field.onChange(floatValue ?? '');
                              }}
                              onKeyDown={(e) => {
                                if (['-', 'e'].includes(e.key))
                                  e.preventDefault();
                              }}
                              disabled
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    บาท
                                  </InputAdornment>
                                ),
                              }}
                              style={{ padding: '0 8px' }}
                            />
                          )}
                        />
                      </motion.div>
                    ))}
                </AnimatePresence>
              </ModalExtraSizeStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    setModalExtraSize({
                      ...modalExtraSize,
                      isOpen: false,
                    });
                  }}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  disabled={submitting}
                  fullWidth
                >
                  {submitting ? <CircularProgress size={20} /> : 'คำนวณ'}
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalExtraSize;
