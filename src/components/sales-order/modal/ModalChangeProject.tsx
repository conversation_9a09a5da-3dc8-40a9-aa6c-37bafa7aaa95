import {
  Autocomplete,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import React, { ChangeEvent, ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiProject from '@/services/order/project';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

export const ModalCreateProductContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24px;
  padding-top: 1px;
  width: 100%;
  min-height: 78px;
`;

type ModalChangeProjectProps = {
  open: boolean;
  setOpenChangeProject: (open: boolean) => void;
  initialValues: any;
};
const validationSchema = yup.object({
  projectGroupId: yup
    .number()
    .required('กรุณาเลือกโปรเจกต์')
    .typeError('กรุณาเลือกโปรเจกต์'),
});
const ModalChangeProject = ({
  open,
  setOpenChangeProject,
  initialValues,
}: ModalChangeProjectProps) => {
  const [projectList, setProjectList] = useState<any>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    // setError,
    // clearErrors,
    formState: {
      errors: hookFormErrors,
      // isSubmitted
    },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValues || {},
  });
  const getProjectList = async () => {
    const res = await apiProject.getProjectList();
    if (!res.isError) {
      setProjectList(res.data);
    }
  };

  useEffect(() => {
    getProjectList();
  }, []);

  const handleChangeProject = (event: ChangeEvent<{}>, option: any) => {
    setValue('projectGroupId', option.id);
  };

  useEffect(() => {
    reset();
  }, [open]);

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    console.log(values);
    setIsSubmitting(false);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          setOpenChangeProject(false);
        }}
      >
        <DialogContent>
          <FormModalStyle $width={320}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เปลี่ยนโปรเจกต์</div>
                <div
                  className="x-close"
                  onClick={() => setOpenChangeProject(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                  onSubmit={handleSubmit(onSubmit)}
                >
                  <ModalCreateProductContentStyled>
                    <Autocomplete
                      {...register('projectGroupId')}
                      disablePortal
                      options={projectList || []}
                      getOptionLabel={(option) => option.name ?? ''}
                      value={watch('projectGroupId') || null}
                      noOptionsText={'ไม่มีข้อมูล'}
                      isOptionEqualToValue={(option, value) =>
                        option.id === value.id
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          type="text"
                          placeholder="เลือกโปรเจกต์"
                          name="project"
                          error={Boolean(hookFormErrors.projectGroupId)}
                          helperText={
                            hookFormErrors.projectGroupId?.message as ReactNode
                          }
                        />
                      )}
                      onChange={(event, newValue) => {
                        handleChangeProject(event, newValue);
                      }}
                      fullWidth
                    />
                  </ModalCreateProductContentStyled>
                  <div className="flex gap-[24px] mt-[34px]">
                    <Button
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      fullWidth
                      onClick={() => setOpenChangeProject(false)}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                    >
                      {isSubmitting ? (
                        <CircularProgress
                          size={20}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'บันทึก'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalChangeProject;
