import React, { ReactNode, useEffect, useState } from 'react';
import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { Controller, useForm } from 'react-hook-form';
import styled from 'styled-components';
import { AnimatePresence, motion } from 'framer-motion';
import { motionListItemConfig } from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { buildEstimateStationBody } from '@/utils/estimate';
import { salesOrderSelector } from '@/store/features/estimate';
import apiEstimate from '@/services/order/estimate';

const ModalPrintSheetAllowanceStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .quantity-row {
    width: 100%;
    display: flex;
    align-items: center;
    column-gap: 12px;
    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0 !important;
    }
    .order {
      width: 24px;
      min-width: 24px;
      display: flex;
      align-items: center;
    }
  }
`;

type Props = {
  data: any;
  setEditPrintSheetAllowance: (data: any) => void;
  getStationByEstimateId: () => Promise<void>;
};

const ModalPrintSheetAllowance = ({
  data,
  setEditPrintSheetAllowance,
  getStationByEstimateId,
}: Props) => {
  const dispatch = useAppDispatch();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [submitting, setSubmitting] = useState(false);
  const {
    handleSubmit,
    formState: { errors: hookFormErrors },
    reset,
    control,
    // watch,
  } = useForm<any>({
    mode: 'onChange',
    defaultValues: {},
  });

  const handleSaveCalculate = async () => {
    if (!estimateQuantityStation?.estimateProducts) {
      console.error('No products found');
      return;
    }

    // Loop through all products and update station for each
    for (const product of estimateQuantityStation.estimateProducts) {
      const body = buildEstimateStationBody(product);
      await apiEstimateQuantityStation.updateStation(body);
    }

    // Update remarkEstimate once after all products are processed
    await apiEstimate.updateRemarkEstimate({
      estimateId: estimateQuantityStation.estimateId,
      remarkEstimate: estimateQuantityStation.remarkEstimate || '',
    });
  };
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    await handleSaveCalculate();

    // Include estimateProductId from the data.values
    const requestData = {
      ...values,
      estimateProductId: data.values.estimateProductId,
      estimateQuantityId: data.values.estimateQuantityId,
    };
    const res = await apiEstimateQuantityStation.updatePrintSheetAllowance(
      requestData
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setEditPrintSheetAllowance({
        ...data,
        status: false,
      });
      await getStationByEstimateId();
    }
    setSubmitting(false);
  };

  useEffect(() => {
    if (data.status) {
      reset(data.values);
    }
  }, [data.status]);

  return (
    <Dialog
      open={data.status}
      onClose={() => setEditPrintSheetAllowance({ ...data, status: false })}
    >
      <DialogContent>
        <FormModal
          width={500}
          title="แก้ไขจำนวนเผื่อเสีย"
          handleClose={() =>
            setEditPrintSheetAllowance({ ...data, status: false })
          }
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p />
              <ModalPrintSheetAllowanceStyled>
                <AnimatePresence mode="sync" initial={true}>
                  <motion.div
                    key={'quantity-row'}
                    className="quantity-row"
                    {...motionListItemConfig}
                    animate={{
                      ...motionListItemConfig.animate,
                      marginBottom: '24px',
                    }}
                  >
                    <Controller
                      name="printSheetAllowance"
                      control={control}
                      rules={{
                        required: 'กรุณากรอกจำนวน',
                        min: { value: 1, message: 'จำนวนต้องมากกว่าศูนย์' },
                      }}
                      render={({ field }) => (
                        <NumericFormat
                          decimalScale={2}
                          getInputRef={field.ref}
                          onBlur={field.onBlur}
                          placeholder="0"
                          customInput={TextField}
                          thousandSeparator
                          value={field.value ?? ''}
                          onValueChange={(values) => {
                            const { floatValue } = values;
                            field.onChange(floatValue ?? '');
                          }}
                          onKeyDown={(e: any) => {
                            const invalidChars = ['-', 'e'];
                            if (invalidChars.includes(e.key)) {
                              e.preventDefault();
                            }
                          }}
                          error={!!hookFormErrors.quantity}
                          helperText={
                            hookFormErrors.quantity?.message as ReactNode
                          }
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                ใบพิมพ์
                              </InputAdornment>
                            ),
                          }}
                          style={{
                            padding: '0 8px',
                          }}
                        />
                      )}
                    />
                  </motion.div>
                </AnimatePresence>
              </ModalPrintSheetAllowanceStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() =>
                    setEditPrintSheetAllowance({ ...data, status: false })
                  }
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  disabled={submitting}
                  fullWidth
                >
                  {submitting ? <CircularProgress size={20} /> : 'บันทึก'}
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalPrintSheetAllowance;
