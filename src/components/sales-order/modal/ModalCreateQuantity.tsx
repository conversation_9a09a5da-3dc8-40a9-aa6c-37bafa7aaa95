import React, { useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import styled from 'styled-components';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import Image from 'next/image';
import DeleteButton from '@/components/global/DeleteButton';
import { AnimatePresence, motion } from 'framer-motion';
import { motionListItemConfig } from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { buildEstimateStationBody } from '@/utils/estimate';
import { salesOrderSelector } from '@/store/features/estimate';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import apiEstimate from '@/services/order/estimate';

const ModalQuantityStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .quantity-row {
    width: 100%;
    display: flex;
    column-gap: 12px;
    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0 !important;
    }
    .order {
      width: 24px;
      min-width: 24px;
      display: flex;
      height: 40px;
      align-items: center;
    }
  }
`;

type QuantityRow = {
  quantity: string;
};

type FormValues = {
  estimateProductId: string;
  quantityItems: QuantityRow[];
};

type Props = {
  open: any;
  estimateProductId: number;
  setOpen: (open: boolean) => void;
  getStationByEstimateId: () => Promise<void>;
};

const ModalCreateQuantity = ({
  open,
  estimateProductId,
  setOpen,
  getStationByEstimateId,
}: Props) => {
  const dispatch = useAppDispatch();
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const [submitting, setSubmitting] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);
  const existingQuantities =
    estimateQuantityStation?.estimateQuantity?.map((q: any) =>
      Number(q.quantity)
    ) ?? [];
  const getValidationSchema = (existingQuantities: number[]) =>
    yup.object({
      quantityItems: yup.array().of(
        yup.object().shape({
          quantity: yup
            .number()
            .typeError('กรุณากรอกจำนวน')
            .required('กรุณากรอกจำนวนผลิต')
            .min(1, 'จำนวนต้องมากกว่าศูนย์')
            .test(
              'not-in-existing',
              'มีจำนวนนี้อยู่แล้วในระบบ',
              function (value) {
                if (value == null) return true;
                return !existingQuantities.includes(value);
              }
            )
            .test(
              'no-duplicate-in-form',
              'ห้ามกรอกจำนวนที่ซ้ำกัน',
              function (value, context) {
                const { path } = this;
                const currentIndex = parseInt(
                  path.match(/\d+/)?.[0] || '-1',
                  10
                );
                if (value == null || currentIndex === -1) return true;

                const allValues =
                  context?.from?.[1]?.value?.quantityItems || [];

                const currentVal = Number(String(value).replace(/,/g, ''));
                const count = allValues.filter(
                  (item: any) =>
                    Number(String(item.quantity).replace(/,/g, '')) ===
                    currentVal
                ).length;

                return count <= 1;
              }
            ),
        })
      ),
    });

  const {
    handleSubmit,
    control,
    formState: { errors: hookFormErrors },
    reset,
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      estimateProductId: '',
      quantityItems: [{ quantity: '' }],
    },
    resolver: yupResolver(getValidationSchema(existingQuantities)),
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'quantityItems',
  });
  const handleSaveCalculate = async () => {
    console.log('estimateQuantityStation', estimateQuantityStation);

    if (!estimateQuantityStation?.estimateProducts) {
      console.error('No products found');
      return;
    }

    // Loop through all products and update station for each
    for (const product of estimateQuantityStation.estimateProducts) {
      const body = buildEstimateStationBody(product);
      console.log('body for product', product.estimateProductId, body);

      await apiEstimateQuantityStation.updateStation(body);
    }

    // Update remarkEstimate once after all products are processed
    await apiEstimate.updateRemarkEstimate({
      estimateId: estimateQuantityStation.estimateId,
      remarkEstimate: estimateQuantityStation.remarkEstimate || '',
    });
  };

  const delay = (ms: number) =>
    // eslint-disable-next-line no-promise-executor-return
    new Promise((resolve) => setTimeout(resolve, ms));

  const onSubmit = async (values: FormValues) => {
    setSubmitting(true);
    await handleSaveCalculate();

    const bodyList = values.quantityItems.map((item) => ({
      estimateProductId: Number(estimateProductId),
      quantityItems: [
        {
          quantity: Number(String(item.quantity).replace(/,/g, '')),
        },
      ],
    }));

    const results: Awaited<
      ReturnType<typeof apiEstimateQuantityStation.addQuantity>
    >[] = [];

    try {
      for (let i = 0; i < bodyList.length; i++) {
        const body = bodyList[i];
        const res = await apiEstimateQuantityStation.addQuantity(body);
        results.push(res);

        if (i < bodyList.length - 1) {
          await delay(200); // Delay 200ms between requests
        }
      }

      const hasError = results.some((res) => res.isError);

      dispatch(
        setSnackBar({
          status: true,
          text: results[0].message,
          severity: results[0].isError ? 'error' : 'success',
        })
      );

      if (!hasError) {
        await getStationByEstimateId();
        setOpen(false);
      }
    } catch (err) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาดขณะบันทึกข้อมูล',
          severity: 'error',
        })
      );
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    if (open) {
      setHasMounted(true);
      reset();
    } else {
      setHasMounted(false);
    }
  }, [open]);

  return (
    <Dialog open={open} onClose={() => setOpen(false)}>
      <DialogContent>
        <FormModal
          width={500}
          title="สร้างจำนวนเสนอราคา"
          handleClose={() => setOpen(false)}
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p />
              <ModalQuantityStyled>
                <AnimatePresence mode="sync" initial={false}>
                  {hasMounted &&
                    fields.map((field, index) => (
                      <motion.div
                        key={field.id}
                        className="quantity-row"
                        {...motionListItemConfig}
                        animate={{
                          ...motionListItemConfig.animate,
                          marginBottom: '24px',
                        }}
                      >
                        <div className="order">#{index + 1}</div>
                        <Controller
                          name={`quantityItems.${index}.quantity`}
                          control={control}
                          rules={{
                            required: 'กรอกจำนวนผลิต',
                            min: { value: 1, message: 'จำนวนต้องมากกว่าศูนย์' },
                          }}
                          render={({ field }) => (
                            <NumericFormat
                              decimalScale={2}
                              getInputRef={field.ref}
                              onBlur={field.onBlur}
                              placeholder="จำนวนผลิต"
                              customInput={TextField}
                              thousandSeparator
                              value={field.value || ''}
                              onValueChange={(values) => {
                                const { floatValue } = values;
                                field.onChange(floatValue ?? '');
                              }}
                              onKeyDown={(e: any) => {
                                const invalidChars = ['-', 'e'];
                                if (invalidChars.includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                              error={
                                !!hookFormErrors.quantityItems?.[index]
                                  ?.quantity
                              }
                              helperText={
                                hookFormErrors.quantityItems?.[index]?.quantity
                                  ?.message
                              }
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    ชิ้น
                                  </InputAdornment>
                                ),
                              }}
                              style={{
                                padding: '0 8px',
                              }}
                            />
                          )}
                        />
                        {index === 0 ? (
                          <Button
                            type="button"
                            variant="contained"
                            color="dark"
                            sx={{
                              width: '40px',
                              minWidth: '40px',
                              '*': { transition: 'all 0.3s ease-out' },
                              '&:hover': { svg: { rotate: '90deg' } },
                            }}
                            onClick={() => append({ quantity: '' })}
                            disabled={fields.length >= 4}
                          >
                            <AddRoundedIcon />
                          </Button>
                        ) : (
                          <DeleteButton onClick={() => remove(index)}>
                            <Image
                              src="/icons/delete-white.svg"
                              width={24}
                              height={24}
                              alt=""
                            />
                          </DeleteButton>
                        )}
                      </motion.div>
                    ))}
                </AnimatePresence>
              </ModalQuantityStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => setOpen(false)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  disabled={submitting}
                  fullWidth
                >
                  {submitting ? <CircularProgress size={20} /> : 'สร้าง'}
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

// @ts-ignore
export default ModalCreateQuantity;
