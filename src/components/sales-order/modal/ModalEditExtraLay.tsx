import React, { ReactNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import styled from 'styled-components';
import { AnimatePresence, motion } from 'framer-motion';
import { motionListItemConfig } from '@/utils/motion/motion-config';
import { NumericFormat } from 'react-number-format';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

const ModalEditExtraLayStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  .foil-wrap {
    display: flex;
    column-gap: 1.25rem;
  }
  .input-adornment-label {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
    width: 40px;
    min-width: 40px;
    border-right: 1px solid #dbe2e5;
    line-height: 1;
    &.r {
      color: #fe4902;
    }
    &.b {
      color: #0344dc;
    }
    &.g {
      color: #008910;
    }
  }
`;

type Props = {
  open: boolean;
  extra: any;
  onClose: () => void;
  onSave: (updated: any) => void;
};

const makeSchema = (needWH: boolean, isFoil: boolean) =>
  yup.object({
    extras: yup.array().of(
      yup.object({
        width: needWH
          ? yup
              .number()
              .typeError('กรอกความกว้าง')
              .required('กรอกความกว้าง')
              .moreThan(0, 'ต้องมากกว่าศูนย์')
          : yup.number().nullable(),
        height: needWH
          ? yup
              .number()
              .typeError('กรอกความยาว')
              .required('กรอกความยาว')
              .moreThan(0, 'ต้องมากกว่าศูนย์')
          : yup.number().nullable(),
      })
    ),
    extraGroups: isFoil
      ? yup.object({
          widthMaterial: yup
            .number()
            .typeError('กรอกกว้างฟอยล์')
            .required('กรอกกว้างฟอยล์')
            .moreThan(0, 'ต้องมากกว่าศูนย์'),
          heightMaterial: yup
            .number()
            .typeError('กรอกยาวฟอยล์')
            .required('กรอกยาวฟอยล์')
            .moreThan(0, 'ต้องมากกว่าศูนย์'),
        })
      : yup.mixed().nullable(),
  });

export const ModalEditExtraLay: React.FC<Props> = ({
  open,
  extra,
  onClose,
  onSave,
}) => {
  const [hasMounted, setHasMounted] = useState(false);

  const needWH = extra?.extraAreaDimension === null;
  const isFoil = extra?.extraGroups !== null;

  const transformExtra = (raw: any) => ({
    blogSubMaterialDetailId: raw?.blogSubMaterialDetail?.id ?? null,
    extraAreaDimensionId: raw?.extraAreaDimension?.id ?? null,
    extras:
      raw?.extras?.map((e: any) => ({
        estimateProductExtraId: e.estimateProductExtraId,
        quantity: e.quantity,
        width: e.width ?? '',
        height: e.height ?? '',
      })) ?? [],
    extraGroups: raw?.extraGroups
      ? {
          estimateProductExtraGroupId:
            raw?.extraGroups.estimateProductExtraGroupId,
          widthMaterial: raw?.extraGroups.widthMaterial || '',
          heightMaterial: raw?.extraGroups.heightMaterial || '',
        }
      : null,
  });

  const {
    handleSubmit,
    control,
    reset,
    watch,
    formState: { errors: hookFormErrors },
  } = useForm<any>({
    mode: 'onChange',
    resolver: yupResolver(makeSchema(needWH, isFoil)),
    defaultValues: transformExtra(extra),
  });
  const { fields } = useFieldArray({ control, name: 'extras' });
  const extraGroupsErr = hookFormErrors.extraGroups as any;

  useEffect(() => {
    if (open && extra) {
      reset(transformExtra(extra));
      setHasMounted(true);
    } else {
      setHasMounted(false);
    }
  }, [open, extra, reset]);

  const onSubmit = (values: any) => {
    onSave(values);
    onClose();
  };

  console.log('extra', extra);
  console.log('fields', fields);
  console.log('needWH', needWH);
  console.log('watch', watch());
  if (!open) return null;
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        <FormModal
          width={500}
          title={extra.blogSubMaterialDetail.name || ''}
          handleClose={onClose}
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p />
              <ModalEditExtraLayStyled>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    rowGap: '24px',
                  }}
                >
                  {needWH && <p className="mt-0 mb-[-16px]">ขนาดเทคนิค</p>}
                  {needWH && (
                    <AnimatePresence mode="sync" initial={false}>
                      {hasMounted &&
                        fields.map((field, index) => (
                          <motion.div
                            key={field.id}
                            {...motionListItemConfig}
                            animate={{
                              ...motionListItemConfig.animate,
                              marginBottom: 0,
                            }}
                          >
                            <p
                              className="mt-0"
                              style={{
                                fontSize: '12px',
                                color: '#6B7280',
                                fontWeight: 400,
                              }}
                            >
                              ขนาดคาดการณ์ {extra.extras[index].widthEstimate}x
                              {extra.extras[index].heightEstimate} นิ้ว
                            </p>
                            <div className="flex gap-5">
                              <Controller
                                name={`extras.${index}.width`}
                                control={control}
                                render={({ field }) => (
                                  <NumericFormat
                                    decimalScale={2}
                                    getInputRef={field.ref}
                                    onBlur={field.onBlur}
                                    placeholder="กว้าง"
                                    customInput={TextField}
                                    thousandSeparator
                                    value={field.value ?? ''}
                                    onValueChange={({ floatValue }) =>
                                      field.onChange(floatValue ?? '')
                                    }
                                    onKeyDown={(e) =>
                                      ['-', 'e'].includes(e.key) &&
                                      e.preventDefault()
                                    }
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <div className="input-adornment-label r">
                                            W
                                          </div>
                                        </InputAdornment>
                                      ),
                                      endAdornment: (
                                        <InputAdornment position="end">
                                          นิ้ว
                                        </InputAdornment>
                                      ),
                                    }}
                                    sx={{
                                      '.MuiInputBase-root': {
                                        paddingLeft: '0',
                                      },
                                      '.MuiInputBase-input': {
                                        paddingLeft: '8px',
                                      },
                                    }}
                                    error={
                                      Array.isArray(hookFormErrors.extras) &&
                                      !!hookFormErrors.extras[index]?.width
                                    }
                                    helperText={
                                      Array.isArray(hookFormErrors.extras)
                                        ? (hookFormErrors.extras[index]?.width
                                            ?.message as ReactNode)
                                        : undefined
                                    }
                                  />
                                )}
                              />

                              {/* height */}
                              <Controller
                                name={`extras.${index}.height`}
                                control={control}
                                render={({ field }) => (
                                  <NumericFormat
                                    decimalScale={2}
                                    getInputRef={field.ref}
                                    onBlur={field.onBlur}
                                    placeholder="ยาว"
                                    customInput={TextField}
                                    thousandSeparator
                                    value={field.value ?? ''}
                                    onValueChange={({ floatValue }) =>
                                      field.onChange(floatValue ?? '')
                                    }
                                    onKeyDown={(e) =>
                                      ['-', 'e'].includes(e.key) &&
                                      e.preventDefault()
                                    }
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <div className="input-adornment-label g">
                                            H
                                          </div>
                                        </InputAdornment>
                                      ),
                                      endAdornment: (
                                        <InputAdornment position="end">
                                          นิ้ว
                                        </InputAdornment>
                                      ),
                                    }}
                                    sx={{
                                      '.MuiInputBase-root': {
                                        paddingLeft: '0',
                                      },
                                      '.MuiInputBase-input': {
                                        paddingLeft: '8px',
                                      },
                                    }}
                                    error={
                                      Array.isArray(hookFormErrors.extras) &&
                                      !!hookFormErrors.extras[index]?.height
                                    }
                                    helperText={
                                      Array.isArray(hookFormErrors.extras)
                                        ? (hookFormErrors.extras[index]?.height
                                            ?.message as ReactNode)
                                        : undefined
                                    }
                                  />
                                )}
                              />
                            </div>
                            <Controller
                              name={`extras.${index}.quantity`}
                              control={control}
                              render={({ field }) => (
                                <NumericFormat
                                  decimalScale={0}
                                  getInputRef={field.ref}
                                  onBlur={field.onBlur}
                                  placeholder="จำนวน"
                                  customInput={TextField}
                                  thousandSeparator
                                  value={field.value ?? ''}
                                  onValueChange={({ floatValue }) =>
                                    field.onChange(floatValue ?? '')
                                  }
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        จุด
                                      </InputAdornment>
                                    ),
                                  }}
                                  className="mt-5"
                                />
                              )}
                            />
                          </motion.div>
                        ))}
                    </AnimatePresence>
                  )}
                </div>
                {isFoil && (
                  <div>
                    {/* ------------------ กรณีฟอยล์ (มีขนาดฟอยล์) ------------------ */}
                    <p className="mt-0">
                      {extra.extraGroups.subMaterialDetailName}
                    </p>
                    <div className="foil-wrap">
                      {/* widthMaterial */}
                      <Controller
                        name="extraGroups.widthMaterial"
                        control={control}
                        render={({ field }) => (
                          <NumericFormat
                            decimalScale={2}
                            getInputRef={field.ref}
                            onBlur={field.onBlur}
                            placeholder="กว้างฟอยล์"
                            customInput={TextField}
                            thousandSeparator
                            value={field.value ?? ''}
                            onValueChange={({ floatValue }) =>
                              field.onChange(floatValue ?? '')
                            }
                            onKeyDown={(e) =>
                              ['-', 'e'].includes(e.key) && e.preventDefault()
                            }
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  นิ้ว
                                </InputAdornment>
                              ),
                            }}
                            error={!!extraGroupsErr?.widthMaterial}
                            helperText={extraGroupsErr?.widthMaterial?.message}
                          />
                        )}
                      />
                      {/* heightMaterial */}
                      <Controller
                        name="extraGroups.heightMaterial"
                        control={control}
                        render={({ field }) => (
                          <NumericFormat
                            decimalScale={2}
                            getInputRef={field.ref}
                            onBlur={field.onBlur}
                            placeholder="ยาวฟอยล์"
                            customInput={TextField}
                            thousandSeparator
                            value={field.value ?? ''}
                            onValueChange={({ floatValue }) =>
                              field.onChange(floatValue ?? '')
                            }
                            onKeyDown={(e) =>
                              ['-', 'e'].includes(e.key) && e.preventDefault()
                            }
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  นิ้ว
                                </InputAdornment>
                              ),
                            }}
                            error={!!extraGroupsErr?.heightMaterial}
                            helperText={extraGroupsErr?.heightMaterial?.message}
                          />
                        )}
                      />
                    </div>
                  </div>
                )}
              </ModalEditExtraLayStyled>

              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={onClose}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  fullWidth
                >
                  บันทึก
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};
