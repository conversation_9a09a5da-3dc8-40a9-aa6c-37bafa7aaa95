import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Select,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { AnimatePresence, motion } from 'framer-motion';
import { motionListItemConfig } from '@/utils/motion/motion-config';
import apiEstimateQuantityStation from '@/services/order/estimate-quantity-station';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import apiServiceLay from '@/services/stock/service-lay';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { isEmpty } from 'lodash';
import { buildEstimateStationBody } from '@/utils/estimate';
import { salesOrderSelector } from '@/store/features/estimate';
import apiEstimate from '@/services/order/estimate';

const ModalAddServiceStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const ServiceFormStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  margin-top: 32px;
  .list {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border: 1px solid #dbe2e5;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: white;
    padding: 8px 0;
    &:before {
      content: '';
      height: 100%;
      width: 1px;
      position: absolute;
      background: #dbe2e5;
      left: calc(50%);
      transform: translateX(-50%);
    }
    .check-box-zone {
      left: 8px;
      position: relative;
    }
    .check-box-zone,
    .selector-zone {
      display: flex;
      align-items: center;
      flex: 1 1 0%;
      .MuiInputBase-root {
        box-shadow: none !important;
      }
    }
  }
`;

type Props = {
  data: any;
  setOpenAddService: (data: any) => void;
  getStationByEstimateId: () => Promise<void>;
};

type IForm = {
  serviceLay: {
    serviceLayId: number | '';
    serviceLayTypeId: number;
  }[];
};

const validationSchema = yup.object({
  serviceLay: yup.array().of(
    yup.object({
      serviceLayId: yup
        .number()
        .typeError('กรุณาเลือกบริการ')
        .required('กรุณาเลือกบริการ'),
      serviceLayTypeId: yup.number().required(),
    })
  ),
});

const ModalAddService = ({
  data,
  setOpenAddService,
  getStationByEstimateId,
}: Props) => {
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState(false);
  const [servicesLay, setServicesLay] = useState<any[]>([]);
  const [isRefetched, setIsRefetched] = useState<boolean>(false);
  const { estimateQuantityStation } = useAppSelector(salesOrderSelector);
  const {
    handleSubmit,
    formState: { errors: hookFormErrors, isSubmitted },
    reset,
    watch,
    setValue,
  } = useForm<IForm>({
    mode: 'onChange',
    resolver: yupResolver(validationSchema),
    defaultValues: {
      serviceLay: [],
    },
  });

  const handleSaveCalculate = async () => {
    if (!estimateQuantityStation?.estimateProducts) {
      console.error('No products found');
      return;
    }

    // Loop through all products and update station for each
    for (const product of estimateQuantityStation.estimateProducts) {
      const body = buildEstimateStationBody(product);
      await apiEstimateQuantityStation.updateStation(body);
    }

    // Update remarkEstimate once after all products are processed
    await apiEstimate.updateRemarkEstimate({
      estimateId: estimateQuantityStation.estimateId,
      remarkEstimate: estimateQuantityStation.remarkEstimate || '',
    });
  };

  const onSubmit = async (values: IForm) => {
    setSubmitting(true);
    await handleSaveCalculate();
    const body = {
      estimateProductId: Number(data.values.estimateProductId),
      ...values,
    };
    const res = await apiEstimateQuantityStation.addServiceLay(body);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: res.isError ? 'error' : 'success',
      })
    );
    if (!res.isError) {
      setOpenAddService({ ...data, status: false });
      await getStationByEstimateId();
    }
    setSubmitting(false);
  };

  const getServices = async () => {
    const res = await apiServiceLay.getEstimateServiceLayList(
      Number(data.values.estimateProductId)
    );
    if (!res.isError) setServicesLay(res.data);
  };

  useEffect(() => {
    if (data.status) {
      setIsRefetched(false);
      setServicesLay([]);
      getServices().then();
      setIsRefetched(true);
      reset({ ...data.values, serviceLay: data.values?.serviceLay ?? [] });
    }
  }, [data.status]);

  const toggleCheck = (service: any) => {
    const current = watch('serviceLay');
    const idx = current.findIndex((s) => s.serviceLayTypeId === service.id);

    if (idx > -1) {
      setValue(
        'serviceLay',
        current.filter((_, i) => i !== idx),
        { shouldValidate: !!isSubmitted }
      );
    } else {
      setValue(
        'serviceLay',
        [...current, { serviceLayId: '', serviceLayTypeId: service.id }],
        {
          shouldValidate: !!isSubmitted,
        }
      );
    }
  };
  const getErrMsg = (typeId: number) => {
    const idx = watch('serviceLay').findIndex(
      (s) => s.serviceLayTypeId === typeId
    );
    return idx > -1
      ? (hookFormErrors.serviceLay?.[idx]?.serviceLayId?.message as
          | string
          | undefined)
      : undefined;
  };
  return (
    <Dialog
      open={data.status && isRefetched}
      onClose={() => setOpenAddService({ ...data, status: false })}
    >
      <DialogContent>
        <FormModal
          width={540}
          title="เพิ่มค่าบริการ"
          handleClose={() => setOpenAddService({ ...data, status: false })}
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <ModalAddServiceStyled>
              <AnimatePresence mode="wait">
                {!isEmpty(servicesLay) ? (
                  <ServiceFormStyled
                    as={motion.div}
                    {...motionListItemConfig}
                    key="service-form"
                    animate={{
                      ...motionListItemConfig.animate,
                      marginBottom: 0,
                    }}
                  >
                    {servicesLay.map((svc) => {
                      const idxInForm = watch('serviceLay').findIndex(
                        (r) => r.serviceLayTypeId === svc.id
                      );
                      const row =
                        idxInForm > -1 ? watch('serviceLay')[idxInForm] : null;
                      const errMsg = getErrMsg(svc.id);
                      return (
                        <React.Fragment key={svc.id}>
                          <div className="list">
                            <div className="check-box-zone">
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={!!row}
                                    onChange={() => toggleCheck(svc)}
                                    icon={<IconUnCheckbox />}
                                    checkedIcon={<IconCheckboxBlack />}
                                  />
                                }
                                label={svc.name}
                              />
                            </div>
                            <div className="selector-zone">
                              <FormControl fullWidth size="small">
                                <Select
                                  disabled={!row}
                                  displayEmpty
                                  value={row?.serviceLayId ?? ''}
                                  onChange={(e) => {
                                    if (!row) return;
                                    const current = watch('serviceLay');
                                    current[idxInForm].serviceLayId = e.target
                                      .value as number;
                                    setValue('serviceLay', current, {
                                      shouldValidate: !!isSubmitted,
                                    });
                                  }}
                                  error={!!errMsg}
                                >
                                  <MenuItem disabled value="">
                                    <span className="text-[#78909C]">
                                      กรุณาเลือก
                                    </span>
                                  </MenuItem>
                                  {svc.serviceLay.map((opt: any) => (
                                    <MenuItem key={opt.id} value={opt.id}>
                                      {opt.name}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </div>
                          </div>
                          {errMsg && (
                            <div
                              style={{
                                marginTop: '-16px',
                              }}
                            >
                              <FormHelperText error>{errMsg}</FormHelperText>
                            </div>
                          )}
                        </React.Fragment>
                      );
                    })}
                  </ServiceFormStyled>
                ) : (
                  <motion.div
                    {...motionListItemConfig}
                    key="service-empty"
                    animate={{
                      ...motionListItemConfig.animate,
                      marginBottom: 0,
                    }}
                    style={{
                      marginTop: '32px',
                    }}
                  >
                    <div className="w-full text-center text-[#78909C]">
                      ไม่มีค่าบริการ
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </ModalAddServiceStyled>

            <div className="w-full flex justify-between mt-8 gap-5">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                fullWidth
                onClick={() => setOpenAddService({ ...data, status: false })}
              >
                ยกเลิก
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="dark"
                disabled={submitting}
                fullWidth
              >
                {submitting ? <CircularProgress size={20} /> : 'บันทึก'}
              </Button>
            </div>
          </form>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalAddService;
