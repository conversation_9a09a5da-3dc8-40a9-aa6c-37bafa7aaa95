import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { useRouter } from 'next/router';
import { salesOrderSelector } from '@/store/features/estimate';
import apiEstimate from '@/services/order/estimate';

const ProductSalesOrderStepStyled = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 24px;
  margin: 8px 0;
  align-items: center;
  .breadcrumb-wrap {
    font-size: 12px;
    font-weight: 400;
    .main {
      color: #cfd8dc;
      cursor: pointer;
      transition: 0.15s ease-out;
      &:hover {
        color: #263238;
      }
    }
    .order {
      color: #263238;
    }
  }
  .step-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-width: 100%;
    gap: 16px;
    .step {
      flex: 1 0 188px;
      height: 40px;
      border: 1px solid #dbe2e5;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background: white;
      user-select: none;
      border-radius: 20px;
      &:active {
        box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
        background: #fafbfb;
      }
      &.false {
        background: #16d5c547 !important;
      }
      .icon {
        position: absolute;
        left: 8px;
        display: flex;
        align-items: center;
      }
      .text {
        font-weight: 600;
      }
      &.active {
        background: #fafbfb;
        box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 4px 0px inset;
      }
      &.disable {
        pointer-events: none;
        color: #cfd8dc;
      }
    }
  }
`;

const ProductSalesOrderStep = () => {
  const router = useRouter();
  const [salesOrderStatus, setsalesOrderStatus] = useState<any[]>([]);
  const { salesOrderById } = useAppSelector(salesOrderSelector);

  const getOrderStatus = async () => {
    const res = await apiEstimate.getSalesOrderStatus();
    if (!res.isError) {
      const status = res.data.filter(
        (statusItem: any) => statusItem.name !== 'ยกเลิก'
      );
      setsalesOrderStatus(status);
    }
  };

  useEffect(() => {
    getOrderStatus().then();
  }, []);

  const currentId = salesOrderById.estimateStatus.id;

  return (
    <ProductSalesOrderStepStyled>
      <div className="step-wrap">
        {salesOrderStatus.map((item: any) => {
          let statusClass = '';
          if (item.name === 'ยืนยันคำสั่งซื้อ' && currentId === item.id) {
            statusClass = '';
          } else if (item.id < currentId) {
            statusClass = ''; // ผ่านแล้ว
          } else if (item.id === currentId) {
            statusClass = 'active'; // ปัจจุบัน
          } else {
            statusClass = 'disable'; // ยังไม่ถึง
          }

          return (
            <div
              key={item.id}
              className={`step ${statusClass}`}
              onClick={async () => {
                if (statusClass !== 'disable') {
                  if (item.name === 'การผลิต') {
                    await router.push(`/job`);
                  } else {
                    await router.push(
                      `/sales-order/${salesOrderById.id}/spec?step=${item.name}`
                    );
                  }
                }
              }}
            >
              <div className="icon">
                {item.name === 'ยืนยันคำสั่งซื้อ' && currentId === item.id ? (
                  <Image
                    src="/icons/task-done.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                ) : item.id < currentId ? (
                  <Image
                    src="/icons/task-done.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                ) : item.id === currentId ? (
                  <Image
                    src="/icons/schedule.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                ) : null}
              </div>
              <div className="text">{item.name}</div>
            </div>
          );
        })}
      </div>
    </ProductSalesOrderStepStyled>
  );
};
export default ProductSalesOrderStep;
