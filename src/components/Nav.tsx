import styled from 'styled-components';
import { useRouter } from 'next/router';
import { ChevronLeft } from '@mui/icons-material';

const NavStyle = styled.div`
  @keyframes fadeNavIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  height: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20px;
  animation: fadeNavIn 0.3s ease;
  border-bottom: 1px solid #eee;
  .back-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    transition: 0.3s;
    svg {
      font-size: 2.5em;
      color: #cfd8dc;
      transition: 0.3s;
    }
    &:active {
      transform: scale(0.9);
    }
    &:hover {
      svg {
        color: #555;
      }
    }
  }
  .title {
    flex: 1;
    h2 {
      font-size: 1.2em;
      margin: 0;
      font-weight: 500;
      @media screen and (max-width: 820px) {
        font-size: 1.1em;
      }
    }
    p {
      margin: 0;
      opacity: 0.5;
      font-size: 0.9em;
    }
  }
`;

type NavbarProps = {
  title: string;
  subTitle?: string;
  backUrl?: any;
  showUserMenu?: boolean;
  children?: React.ReactNode;
  titleColor?: string;
};
export default function Nav({
  title,
  subTitle,
  backUrl,
  children,
  titleColor,
}: NavbarProps) {
  const router = useRouter();
  return (
    <NavStyle className="nav-menu">
      {backUrl && (
        <div
          className="back-btn"
          onClick={() => router.push(backUrl, '', { scroll: true })}
        >
          <ChevronLeft />
        </div>
      )}
      <div className="title">
        <h2 className={titleColor ? `text-[${titleColor}]` : ''}>{title}</h2>
        {subTitle && <p>{subTitle}</p>}
      </div>
      <div className="">{children}</div>
    </NavStyle>
  );
}
