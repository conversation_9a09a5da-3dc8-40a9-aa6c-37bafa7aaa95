import styled from '@emotion/styled';
import { FormHelperText, MenuItem, OutlinedInput, Select } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import apiMaterial from '@/services/stock/material';
import apiSubMaterial from '@/services/stock/subMaterial';
import apiSubMaterialDetail from '@/services/stock/subMaterialDetail';

export default function FilterForm({
  onFilterChange,
}: {
  onFilterChange: any;
}) {
  const [materials, setMaterials] = useState([]);
  const [subMaterials, setSubMaterials] = useState([]);
  const [subMaterialDetails, setSubMaterialDetails] = useState([]);

  useEffect(() => {
    fetchMaterials();
  }, []);

  const formik = useFormik({
    initialValues: {
      materialId: null,
      subMaterialId: null,
      subMaterialDetailId: null,
    },
    onSubmit: (values: any) => {
      console.log(values);
    },
  });

  useEffect(() => {
    onFilterChange({
      materialId: formik.values.materialId,
      subMaterialId: formik.values.subMaterialId,
      subMaterialDetailId: formik.values.subMaterialDetailId,
    });
  }, [formik.values]);

  const fetchMaterials = async () => {
    const materials = await apiMaterial.getList({ page: 0, size: 100000 });
    if (materials.status) {
      await setMaterials(materials.data.content);
    } else {
      await setMaterials([]);
    }
  };

  useEffect(() => {
    fetchSubMaterials();
    formik.setFieldValue('subMaterialId', null);
    formik.setFieldValue('subMaterialDetailId', null);
    formik.setFieldValue('itemSizeId', null);
  }, [formik.values.materialId]);

  const fetchSubMaterials = async () => {
    if (formik.values.materialId) {
      const sub_materials = await apiSubMaterial.getList({
        page: 0,
        size: 100000,
        materialId: formik.values.materialId,
      });
      if (sub_materials.status) {
        await setSubMaterials(sub_materials.data.content);
      } else {
        await setSubMaterials([]);
      }
    }
  };

  useEffect(() => {
    fetchSubMaterialDetails();
    formik.setFieldValue('subMaterialDetailId', null);
  }, [formik.values.subMaterialId]);

  const fetchSubMaterialDetails = async () => {
    if (formik.values.subMaterialId) {
      const sub_material_details = await apiSubMaterialDetail.getList({
        page: 0,
        size: 100000,
        subMaterialId: formik.values.subMaterialId,
      });
      if (sub_material_details.status) {
        await setSubMaterialDetails(sub_material_details.data.content);
      } else {
        await setSubMaterialDetails([]);
      }
    }
  };

  const onResetFilter = () => {
    formik.resetForm();
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <FilterContainer>
        <div className={'filter-item'}>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="materialId"
            value={formik.values.materialId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Material
                  </div>
                );
              }
              const selectedItem: any = materials.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.materialId && Boolean(formik.errors.materialId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {materials.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.materialId && formik.errors.materialId && (
            <FormHelperText error>{formik.errors.materialId}</FormHelperText>
          )}
        </div>
        <div className={'filter-item'}>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="subMaterialId"
            value={formik.values.subMaterialId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Sub Material
                  </div>
                );
              }
              const selectedItem: any = subMaterials.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.subMaterialId &&
              Boolean(formik.errors.subMaterialId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {subMaterials.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.subMaterialId && formik.errors.subMaterialId && (
            <FormHelperText error>{formik.errors.subMaterialId}</FormHelperText>
          )}
        </div>
        <div className={'filter-item'}>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="subMaterialDetailId"
            value={formik.values.subMaterialDetailId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Sub Material Detail
                  </div>
                );
              }
              const selectedItem: any = subMaterialDetails.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.subMaterialDetailId &&
              Boolean(formik.errors.subMaterialDetailId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {subMaterialDetails.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.subMaterialDetailId &&
            formik.errors.subMaterialDetailId && (
              <FormHelperText error>
                {formik.errors.subMaterialDetailId}
              </FormHelperText>
            )}
        </div>
        <div className={'reset-btn'} onClick={() => onResetFilter()}>
          รีเซ็ต
        </div>
      </FilterContainer>
    </form>
  );
}

const FilterContainer = styled.div`
  display: flex;
  padding: 14px;
  column-gap: 8px;

  p {
    margin: 0;
  }

  .filter-item {
    width: 250px;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
  }

  .reset-btn {
    background: #30d5c7;
    color: #fff;
    width: 40px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
`;
