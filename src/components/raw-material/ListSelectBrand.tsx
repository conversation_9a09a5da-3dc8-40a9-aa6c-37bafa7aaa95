import React from 'react';
import styled from 'styled-components';
import DeleteButton from '@/components/global/DeleteButton';
import Image from 'next/image';

const ListSelectBrandStyled = styled.div`
  width: 592px;
  border-bottom: 1px solid #b0bec5;
  //@media (max-width: 1980px) {
  //  width: 100%;
  //}
  &:last-child {
    border-bottom: none;
  }
  .group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 24px;

    .text {
      margin-top: 8px;

      .head {
        font-weight: bold;
        font-size: 16px;
        color: #263238;
      }

      .detail {
        margin-top: 0;
      }
    }

    .icon {
      color: #cfd8dc;
    }
    .delete {
      display: flex;
      align-items: center;
      &:hover {
        filter: brightness(0.4);
      }
    }
  }
`;
type Props = {
  brandSelected: any;
  handleRemoveBrand: (data: any) => void;
};
const ListSelectBrand = ({ brandSelected, handleRemoveBrand }: Props) => {
  return (
    <>
      {brandSelected.map((item: any, index: number) => {
        return (
          <ListSelectBrandStyled key={index}>
            <div className="group">
              <div className="text">
                <div className="head">{item.name}</div>
                <p className="detail">{item.description || '-'}</p>
              </div>
              <DeleteButton
                onClick={() => {
                  handleRemoveBrand(item);
                }}
              >
                <Image
                  src="/icons/delete-white.svg"
                  width={24}
                  height={24}
                  alt=""
                />
              </DeleteButton>
            </div>
          </ListSelectBrandStyled>
        );
      })}
    </>
  );
};

export default ListSelectBrand;
