import React, { useEffect, useState } from 'react';
import ImageField from '@/components/ImageField';
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormHelperText,
  IconButton,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Select,
  TextField,
} from '@mui/material';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { useFormik } from 'formik';
import { Add, Check } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import apiPickingType from '@/services/stock/picking-type';
import apiMaterial from '@/services/stock/material';
import apiSubMaterial from '@/services/stock/subMaterial';
import apiSubMaterialDetail from '@/services/stock/subMaterialDetail';
import apiItemSize from '@/services/stock/itemSize';
import apiRawMaterial from '@/services/stock/raw-material';
import apiDimensions from '@/services/stock/dimensions';
import ModalRawMaterialImportBrand from '@/components/raw-material/ModalRawMaterialImportBrand';

import AppModalConfirm from '@/components/global/AppModalConfirm';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import ListSelectBrand from '@/components/raw-material/ListSelectBrand';

const RawMaterialFormStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 40px;
  animation: ${LoadingFadein} 0.3s ease-in;
  width: 100%;
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 640px;
    padding: 0 24px;
    max-width: 100%;
  }
  .brand-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .button-add {
      margin-top: 24px;
      border-radius: 8px;
    }
  }
  .group-title {
    margin-top: 24px;
    margin-bottom: 0;
    font-size: 18px;
    font-weight: 500;
  }
  .field-title {
    margin-top: 18px;
    margin-bottom: 8px;
    font-size: 14px;
  }
  .brand-selected {
    //height: 100%;
    margin-top: 16px;
    border-radius: 8px;
    border: 1px solid #b0bec5;
  }
  .error-brand {
    color: #d32f2f;
    font-weight: 400;
    font-size: 0.75rem;
    line-height: 1.66;
    text-align: left;
    margin: 4px 14px;
    font-family: Prompt, sans-serif;
    animation: jBcSpD 0.3s ease-in;
  }
`;

type RawMaterialFormProps = {
  handleSubmit: (value: any, file: any) => void;
  submitting: boolean;
  disable: boolean;
  initialValues?: any;
};
const RawMaterialForm = ({
  handleSubmit,
  submitting,
  disable,
  initialValues,
}: RawMaterialFormProps) => {
  const [materials, setMaterials] = useState([]);
  const [subMaterials, setSubMaterials] = useState([]);
  const [subMaterialDetails, setSubMaterialDetails] = useState([]);
  const [itemSizes, setItemSizes] = useState([]);
  const [pickingTypes, setPickingTypes] = useState([]);
  const [dimensionsListType, setDimensionsListType] = useState([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [brandSelected, setBrandSelected] = useState<any[]>([]);
  const [modalRemoveBrand, setModalRemoveBrand] = useState<any>({
    id: 0,
    name: '',
    description: '',
    open: false,
  });

  const [image, setImage] = useState<{ isNew: boolean; file: any } | null>({
    isNew: false,
    file: null,
  });
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      imageUrl: null,
      id: null,
      rawMaterialNo: '',
      materialId: null,
      subMaterialId: null,
      subMaterialDetailId: null,
      itemSizeId: null,
      countDimensionId: '',
      pickingTypeId: null,
      isLotExpirationDate: false,
      rawMaterialTypeId: 1,
      isSell: false,
      isPurchase: false,
      isSerialNumber: false,
      rawPrice: '',
      brand: [],
    },
    validate: async (values: any) => {
      const errors: any = {};
      if (!values.rawMaterialNo) {
        errors.rawMaterialNo = 'กรุณากรอกรหัส';
      } else {
        const check: any = await apiRawMaterial.checkRawMaterialNo(
          values.rawMaterialNo
        );
        if (!check.data) {
          if (initialValues) {
            if (initialValues.rawMaterialNo !== values.rawMaterialNo) {
              errors.rawMaterialNo = 'รหัสถูกใช้งานไปเเล้ว';
            }
          } else {
            errors.rawMaterialNo = 'รหัสถูกใช้งานไปเเล้ว';
          }
        }
      }
      if (!values.name) {
        errors.name = 'กรุณากรอกชื่อ';
      }
      if (!values.rawPrice && initialValues?.rawPrice) {
        errors.rawPrice = 'กรุณากรอกราคา';
      }
      if (!values.materialId) {
        errors.materialId = 'กรุณาเลือก Material';
      }
      if (!values.subMaterialId && subMaterials.length > 0) {
        errors.subMaterialId = 'กรุณาเลือก Sub Material';
      }
      if (!values.subMaterialDetailId && subMaterialDetails.length > 0) {
        errors.subMaterialDetailId = 'กรุณาเลือก Sub Material Detail';
      }
      if (!values.itemSizeId) {
        errors.itemSizeId = 'กรุณาเลือกขนาด';
      }
      if (!values.countDimensionId) {
        errors.countDimensionId = 'กรุณาเลือกหน่วย';
      }
      if (isEmpty(values.brand)) {
        errors.brand = 'กรุณาเลือกแบรนด์';
      }
      return errors;
    },
    onSubmit: (values) => {
      handleSubmit(values, image);
    },
  });
  useEffect(() => {
    if (initialValues) {
      formik.setFieldValue('imageUrl', initialValues.imageUrl);
      formik.setFieldValue('id', initialValues.id);
      formik.setFieldValue('rawMaterialNo', initialValues.rawMaterialNo);
      formik.setFieldValue('name', initialValues.name);
      formik.setFieldValue('description', initialValues.description);
      formik.setFieldValue('materialId', initialValues.material?.id || null);
      formik.setFieldValue(
        'countDimensionId',
        initialValues.countDimension?.id || ''
      );
      formik.setFieldValue(
        'subMaterialId',
        initialValues?.subMaterialDetail?.subMaterialId || null
      );
      formik.setFieldValue(
        'subMaterialDetailId',
        initialValues.subMaterialDetail?.id || null
      );
      formik.setFieldValue('pickingTypeId', initialValues.pickingType.id);
      formik.setFieldValue(
        'isLotExpirationDate',
        initialValues.isLotExpirationDate
      );
      formik.setFieldValue('isSerialNumber', initialValues.isSerialNumber);
      formik.setFieldValue('isSell', initialValues.isSell);
      formik.setFieldValue('isPurchase', initialValues.isPurchase);
      formik.setFieldValue('rawPrice', initialValues.rawPrice);
      formik.setFieldValue('rawMaterialTypeId', 1);
    }
  }, [initialValues]);
  const handleClose = () => {
    setIsOpen(false);
  };
  const makeFile = async (imageFile: any) => {
    if (imageFile && imageFile.length === 1) {
      await setImage({ isNew: true, file: imageFile[0] });
      await formik.setFieldValue('imageUrl', URL.createObjectURL(imageFile[0]));
    } else {
      await setImage({ isNew: false, file: null });
    }
  };
  useEffect(() => {
    fetchMaterials();
    fetchPickingType();
    fetchDimensionsListType();
  }, []);

  const fetchMaterials = async () => {
    const materials = await apiMaterial.getList({ page: 0, size: 100000 });
    if (materials.status) {
      await setMaterials(materials.data.content);
    } else {
      await setMaterials([]);
    }
  };

  const fetchPickingType = async () => {
    const pickingType = await apiPickingType.getList();
    if (pickingType.status) {
      await setPickingTypes(pickingType.data);
      await formik.setFieldValue(
        'pickingTypeId',
        pickingType?.data[0]?.id || null
      );
    } else {
      await setPickingTypes([]);
    }
  };
  const fetchDimensionsListType = async () => {
    const dimensions = await apiDimensions.getListDimensionByDimensionTypeId(6);
    if (dimensions.status) {
      setDimensionsListType(dimensions.data);
    } else {
      setDimensionsListType([]);
    }
  };

  useEffect(() => {
    fetchSubMaterials();
    fetchItemSize();
    if (initialValues && itemSizes) {
      if (formik.values.materialId === initialValues.material.id) {
        formik.setFieldValue(
          'subMaterialId',
          initialValues.subMaterial?.id || null
        );
        formik.setFieldValue('itemSizeId', initialValues.itemSize.id);
      } else {
        formik.setFieldValue('subMaterialId', null);
        formik.setFieldValue('subMaterialDetailId', null);
        formik.setFieldValue('itemSizeId', null);
      }
    } else {
      formik.setFieldValue('subMaterialId', null);
      formik.setFieldValue('subMaterialDetailId', null);
      formik.setFieldValue('itemSizeId', null);
    }
  }, [formik.values.materialId]);

  useEffect(() => {
    if (initialValues && itemSizes) {
      if (formik.values.materialId === initialValues.material.id) {
        formik.setFieldValue('itemSizeId', initialValues.itemSize.id);
      } else {
        formik.setFieldValue('itemSizeId', null);
      }
    }
  }, [itemSizes]);

  const fetchSubMaterials = async () => {
    if (formik.values.materialId) {
      const sub_materials = await apiSubMaterial.getList({
        page: 0,
        size: 100000,
        materialId: formik.values.materialId,
      });
      if (sub_materials.status) {
        setSubMaterials(sub_materials.data.content);
      } else {
        setSubMaterials([]);
      }
    }
  };

  const fetchItemSize = async () => {
    if (formik.values.materialId) {
      const selectedItem: any = materials.find(
        (item: any) => item.id === formik.values.materialId
      );
      if (selectedItem) {
        const item = await apiItemSize.getItemSizeDimensionConfig(
          selectedItem.dimensionConfig.id
        );
        if (item.status) {
          setItemSizes(item.data);
        } else {
          setItemSizes([]);
        }
      } else {
        setItemSizes([]);
      }
    }
  };

  useEffect(() => {
    fetchSubMaterialDetails();
    if (initialValues && initialValues.subMaterial) {
      if (formik.values.subMaterialId === initialValues.subMaterial.id) {
        formik.setFieldValue(
          'subMaterialDetailId',
          initialValues.subMaterialDetail?.id || null
        );
      } else {
        formik.setFieldValue('subMaterialDetailId', null);
      }
    } else {
      formik.setFieldValue('subMaterialDetailId', null);
    }
  }, [formik.values.subMaterialId]);

  const fetchSubMaterialDetails = async () => {
    if (formik.values.subMaterialId) {
      const sub_material_details = await apiSubMaterialDetail.getList({
        page: 0,
        size: 100000,
        subMaterialId: formik.values.subMaterialId,
      });
      if (sub_material_details.status) {
        setSubMaterialDetails(sub_material_details.data.content);
      } else {
        setSubMaterialDetails([]);
      }
    }
  };

  useEffect(() => {
    if (!isEmpty(initialValues)) {
      setBrandSelected(initialValues.brand);
    }
  }, [initialValues]);

  const removeImage = async () => {
    await formik.setFieldValue('imageUrl', null);
    if (initialValues?.imageUrl) {
      await formik.setFieldValue('imageUrl', initialValues?.imageUrl);
    }
    setImage({ isNew: false, file: null });
  };

  const handleRemoveBrand = () => {
    if (!modalRemoveBrand.id) return;
    setBrandSelected((item: any[]) =>
      item.filter((b) => b.id !== modalRemoveBrand.id)
    );
    setModalRemoveBrand((prev: any) => ({
      ...prev,
      open: false,
    }));
  };

  useEffect(() => {
    if (!isEmpty(brandSelected)) {
      const resultBrandId = brandSelected.map((item: any) => {
        return {
          brandId: item.id,
        };
      });
      formik.setFieldValue('brand', resultBrandId);
      formik.setErrors({ brand: '' });
    } else {
      formik.setFieldValue('brand', []);
    }
  }, [brandSelected]);
  return (
    <RawMaterialFormStyle>
      <AppModalConfirm
        open={modalRemoveBrand.open}
        onClickClose={() => {
          setModalRemoveBrand({
            ...modalRemoveBrand,
            open: false,
          });
        }}
        confirmTitle={modalRemoveBrand.title}
        confirmDescription={modalRemoveBrand.description}
        loadingConfirm={false}
        onConfirm={() => {
          handleRemoveBrand();
        }}
        icon={
          <Image
            src={'/icons/delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="420px"
      />
      <ModalRawMaterialImportBrand
        open={isOpen}
        handleClose={handleClose}
        brandSelected={brandSelected}
        setBrandSelected={setBrandSelected}
      />
      <form
        onSubmit={(e) => {
          // setSubmitAttempted(true);
          formik.handleSubmit(e);
        }}
      >
        <CoverContainer>
          <div className={'container'}>
            {image?.isNew && (
              <div className={'delete-btn'} onClick={removeImage}>
                <Trash2 />
              </div>
            )}
            <ImageField
              handleChange={async (files: any) => {
                await makeFile(files);
              }}
              objectFit="cover"
              defaultBackground={
                formik.values?.imageUrl || '/images/add-image.svg'
              }
              borderRadius="14px"
            />
          </div>
        </CoverContainer>
        <p className="group-title">ข้อมูลทั่วไป</p>
        <p className="field-title">รหัส</p>
        <TextField
          type="text"
          name="rawMaterialNo"
          placeholder="รหัสวัสดุ"
          value={formik.values.rawMaterialNo}
          onChange={formik.handleChange}
          error={
            formik.touched.rawMaterialNo && Boolean(formik.errors.rawMaterialNo)
          }
          helperText={
            formik.touched.rawMaterialNo &&
            (formik.errors.rawMaterialNo as string)
          }
        />
        <p className="field-title">ชื่อวัสดุ</p>
        <TextField
          type="text"
          name="name"
          placeholder="ชื่อวัสดุ"
          value={formik.values.name}
          onChange={formik.handleChange}
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && (formik.errors.name as string)}
        />
        <p className="field-title">Material</p>
        <FormControl fullWidth>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="materialId"
            value={formik.values.materialId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Material
                  </div>
                );
              }
              const selectedItem: any = materials.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.materialId && Boolean(formik.errors.materialId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {materials.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.materialId && formik.errors.materialId && (
            <FormHelperText error>{formik.errors.materialId}</FormHelperText>
          )}
        </FormControl>
        <p className="field-title">Sub Material</p>
        <FormControl fullWidth>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="subMaterialId"
            value={formik.values.subMaterialId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Sub Material
                  </div>
                );
              }
              const selectedItem: any = subMaterials.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.subMaterialId &&
              Boolean(formik.errors.subMaterialId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {subMaterials.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.subMaterialId && formik.errors.subMaterialId && (
            <FormHelperText error>{formik.errors.subMaterialId}</FormHelperText>
          )}
        </FormControl>
        <p className="field-title">Sub Material Detail</p>
        <FormControl fullWidth>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="subMaterialDetailId"
            value={formik.values.subMaterialDetailId}
            onChange={formik.handleChange}
            input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">
                    เลือก Sub Material Detail
                  </div>
                );
              }
              const selectedItem: any = subMaterialDetails.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.name : '';
            }}
            error={
              formik.touched.subMaterialDetailId &&
              Boolean(formik.errors.subMaterialDetailId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {subMaterialDetails.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.subMaterialDetailId &&
            formik.errors.subMaterialDetailId && (
              <FormHelperText error>
                {formik.errors.subMaterialDetailId}
              </FormHelperText>
            )}
        </FormControl>
        {initialValues?.rawPrice && (
          <>
            <p className="field-title">ราคาต่อกิโลกรัม</p>
            <TextField
              type="number"
              name="rawPrice"
              placeholder="ราคาต่อกิโลกรัม"
              value={formik.values.rawPrice}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                if (value < 1) {
                  e.target.value = '';
                } else {
                  formik.handleChange(e);
                }
              }}
              onKeyDown={(e: any) => {
                if (e.key === '-') {
                  e.preventDefault();
                }
              }}
              onFocus={(e) =>
                e.target.addEventListener(
                  'wheel',
                  function (e) {
                    e.preventDefault();
                  },
                  { passive: false }
                )
              }
              error={formik.touched.rawPrice && Boolean(formik.errors.rawPrice)}
              helperText={
                formik.touched.rawPrice && (formik.errors.rawPrice as string)
              }
            />
            <p className={'m-0 mt-2 text-xs'}>
              * เมื่อเปลี่ยนราคาต่อกิโลกรัม
              ราคาต่อกิโลกรัมของวัสดุนี้จะถูกเปลี่ยนทั้งหมด
            </p>
          </>
        )}

        <p className="field-title">รายละเอียด</p>
        <TextField
          multiline
          rows={4}
          type="text"
          name="description"
          placeholder="ระบุข้อมูล"
          value={formik.values.description}
          onChange={formik.handleChange}
          error={
            formik.touched.description && Boolean(formik.errors.description)
          }
          helperText={
            formik.touched.description && (formik.errors.description as string)
          }
        />
        <p className="group-title">สเปควัสดุ</p>
        <p className="field-title">ขนาด</p>
        <FormControl fullWidth>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="itemSizeId"
            value={formik.values.itemSizeId}
            onChange={formik.handleChange}
            // input={<OutlinedInput />}
            renderValue={(selected) => {
              if (!selected) {
                return (
                  <div className="text-[14px] text-[#B0BEC5]">เลือกขนาด</div>
                );
              }
              const selectedItem: any = itemSizes.find(
                (item: any) => item.id === selected
              );
              return selectedItem ? selectedItem.itemSizeName : '';
            }}
            error={
              formik.touched.itemSizeId && Boolean(formik.errors.itemSizeId)
            }
          >
            <MenuItem disabled value="">
              <em>-- Please select --</em>
            </MenuItem>
            {itemSizes.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.itemSizeName}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.itemSizeId && formik.errors.itemSizeId && (
            <FormHelperText error>{formik.errors.itemSizeId}</FormHelperText>
          )}
        </FormControl>

        <p className="field-title">หน่วยนับ</p>
        <FormControl fullWidth>
          <Select
            sx={{
              width: '100%',
              height: '40px',
              border: '0',
            }}
            displayEmpty
            name="countDimensionId"
            value={formik.values.countDimensionId}
            onChange={formik.handleChange}
            error={
              formik.touched.countDimensionId &&
              Boolean(formik.errors.countDimensionId)
            }
          >
            <MenuItem disabled value="">
              <div className="text-[14px] text-[#B0BEC5]">เลือกหน่วยนับ</div>
            </MenuItem>
            {dimensionsListType?.map((data: any, index: number) => (
              <MenuItem key={index} value={data.id}>
                {data.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.countDimensionId &&
            formik.errors.countDimensionId && (
              <FormHelperText error>
                {formik.errors.countDimensionId}
              </FormHelperText>
            )}
        </FormControl>
        <div className="brand-group">
          <p className="group-title">แบรนด์</p>
          <IconButton
            className="button-add"
            size="small"
            style={{ background: '#222', color: 'white' }}
            onClick={() => setIsOpen(true)}
          >
            <Add />
          </IconButton>
        </div>
        <div className="brand-selected">
          <ListSelectBrand
            handleRemoveBrand={(data: any) => {
              setModalRemoveBrand({
                title: 'ยืนยันการลบ',
                description: `คุณต้องการลบ ${data.name} ออกจากรายการนี้`,
                id: data.id,
                open: true,
              });
            }}
            brandSelected={brandSelected}
          />
        </div>
        {/* {submitAttempted && isEmpty(brandSelected) && ( */}
        {/*  <p className="error-brand">กรุณาเลือกแบรนด์</p> */}
        {/* )} */}
        {formik.touched.brand && formik.errors.brand && (
          <FormHelperText error>{formik.errors.brand}</FormHelperText>
        )}
        <p className="group-title">ตั้งค่าสินค้า</p>
        <p className="field-title">ข้อมูลบังคับ</p>
        <FormControlLabel
          control={
            <Checkbox
              icon={<Check />}
              checkedIcon={<Check className={'p-1'} />}
              sx={CheckboxStyle}
              name={'isLotExpirationDate'}
              checked={formik.values.isLotExpirationDate}
              onChange={formik.handleChange}
            />
          }
          label="วันหมดอายุ"
        />
        <FormControlLabel
          control={
            <Checkbox
              icon={<Check />}
              checkedIcon={<Check className={'p-1'} />}
              sx={CheckboxStyle}
              name={'isSerialNumber'}
              checked={formik.values.isSerialNumber}
              onChange={formik.handleChange}
            />
          }
          label="Serial Number"
        />
        <p className="field-title">การซื้อขาย</p>
        <FormControlLabel
          control={
            <Checkbox
              icon={<Check />}
              checkedIcon={<Check className={'p-1'} />}
              sx={CheckboxStyle}
              name={'isPurchase'}
              checked={formik.values.isPurchase}
              onChange={formik.handleChange}
            />
          }
          label="สามารถซื้อได้"
        />
        <FormControlLabel
          control={
            <Checkbox
              icon={<Check />}
              checkedIcon={<Check className={'p-1'} />}
              sx={CheckboxStyle}
              name={'isSell'}
              checked={formik.values.isSell}
              onChange={formik.handleChange}
            />
          }
          label="สามารถขายได้"
        />
        <p className="field-title">การหยิบสินค้า</p>
        <RadioGroup
          name="pickingTypeId"
          value={formik.values.pickingTypeId}
          onChange={formik.handleChange}
        >
          {pickingTypes &&
            pickingTypes.map((item: any, i) => {
              return (
                <FormControlLabel
                  key={i}
                  value={item.id}
                  control={
                    <Radio
                      icon={<Check />}
                      checkedIcon={<Check className={'p-1'} />}
                      sx={RadioStyle}
                    />
                  }
                  label={item.name}
                />
              );
            })}
        </RadioGroup>
        <Button
          type="submit"
          variant="contained"
          color="dark"
          disabled={disable}
          fullWidth
          sx={{ fontSize: '16px', margin: '40px 0 24px 0', minHeight: '36px' }}
        >
          {submitting ? (
            <CircularProgress
              size={20}
              style={{
                color: 'white',
              }}
            />
          ) : (
            'บันทึก'
          )}
        </Button>
      </form>
    </RawMaterialFormStyle>
  );
};

export default RawMaterialForm;

const CoverContainer = styled.div`
  display: flex;
  justify-content: center;

  .container {
    width: fit-content;
    display: flex;
    justify-content: end;

    .delete-btn {
      background: #e81621;
      color: #fff;
      position: absolute;
      z-index: 1;
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      padding: 5px;
      cursor: pointer;
      margin: 8px;
    }
  }
`;

export const RadioStyle = {
  color: '#fff',
  backgroundColor: '#fff',
  width: '24px',
  height: '24px',
  border: '1px solid #dbe2e5',
  margin: '6px 10px',
  '&.Mui-checked': {
    color: '#fff',
    backgroundColor: '#16D5C5',
  },
};

const CheckboxStyle = {
  color: '#fff',
  backgroundColor: '#fff',
  width: '24px',
  height: '24px',
  border: '1px solid #dbe2e5',
  margin: '6px 10px',
  borderRadius: '6px',
  '&.Mui-checked': {
    color: '#fff',
    backgroundColor: '#000',
  },
};
