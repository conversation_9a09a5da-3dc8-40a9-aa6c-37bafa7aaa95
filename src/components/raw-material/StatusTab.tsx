import styled from 'styled-components';
import { Chip, Divider } from '@mui/material';
import { rawMaterialStatus } from '@/utils/rawMaterialStatus';
import { Trash2 } from 'react-feather';
import { MoveUp, Search, Tune } from '@mui/icons-material';
import React from 'react';

const StatusTabStyle = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 0 24px;
  .status {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #dbe2e5;
    > div {
      padding: 10px 15px;
      border-bottom: 2px solid #fff;
      cursor: pointer;
      white-space: nowrap;
      opacity: 0.6;
      &.active {
        border-color: #222222;
        opacity: 1;
      }
    }
  }
`;

const Action = styled.div`
  display: flex;
  align-items: center;
  column-gap: 12px;
  padding: 8px 0;
  .action-item {
    display: flex;
    align-items: center;
    height: 24px;
    column-gap: 6px;
  }
`;

type StatusTabProps = {
  activeStatus: string;
  handleClick: (orderStatus: string) => void;
};
const StatusTab = ({ activeStatus, handleClick }: StatusTabProps) => {
  return (
    <div className="overflow-y-auto w-full">
      <StatusTabStyle>
        <div className={'status'}>
          {rawMaterialStatus.map((item: any, index: number) => (
            <div
              key={index}
              className={activeStatus === item.orderStatus ? 'active' : ''}
              onClick={() => handleClick(item.orderStatus)}
            >
              {item.name}{' '}
              <Chip
                label="10"
                size="small"
                style={{ background: '#263238', color: 'white' }}
              />
            </div>
          ))}
        </div>
        <Action>
          <div className="action-item">
            <Trash2 />
            <p>ลบ</p>
          </div>
          <Divider orientation="vertical" flexItem />
          <div className="action-item">
            <MoveUp />
            <p>โอนสินค้า</p>
          </div>
          <Divider orientation="vertical" flexItem />
          <div className="action-item">
            <Tune />
            <p>ตัวกรอง</p>
          </div>
          <Divider orientation="vertical" flexItem />
          <div className="action-item">
            <Search />
            <p>ค้นหา</p>
          </div>
        </Action>
      </StatusTabStyle>
    </div>
  );
};

export default StatusTab;
