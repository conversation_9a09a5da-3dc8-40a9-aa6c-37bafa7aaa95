import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as yup from 'yup';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControlLabel,
  FormGroup,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { Search } from '@mui/icons-material';
import Image from 'next/image';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import { isEmpty } from 'lodash';
import apiBrand from '@/services/stock/brand';
import { useController, useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDebounceCallback } from 'usehooks-ts';
import IconDoneCheckbox from '@/components/IconDoneCheckbox';

const RawMaterialImportBrandStyles = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 500px;
  min-height: 174px;
  align-items: center;

  .content-wrap {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #dbe2e5;

    .list-text {
      display: flex;
    }
    .selectAll {
      color: #90a4ae;
      .MuiFormControlLabel-root {
        flex-direction: row-reverse;
        column-gap: 10px;
        margin: 0;
      }
    }
  }
  .list-wrap {
    min-height: 438px;
    max-height: 438px;
    width: 100%;
    overflow: auto;
    .item-wrap {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 0 0 10px;
      border-radius: 10px;
      margin-top: 5px;
      &:hover {
        background: #f5f7f8;
      }

      .list {
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 14px;
        cursor: pointer;
        border-radius: 16px;
        padding: 10px 0;
        transition: 0.3s;
        width: 100%;
        .text-wrap {
          h4 {
            margin: 0;
          }
        }
      }
    }
    .empty-brand {
      text-align: center;
      color: #cfd8dc;
      margin-top: 30px;
      animation: ${LoadingFadein} 0.3s ease-in;
    }
  }
`;

type Props = {
  open: boolean;
  handleClose: () => void;
  setBrandSelected: any;
  brandSelected: any;
};

type FormValues = {
  brands: [];
};

const schema = yup.object().shape({
  brands: yup.array().min(1, 'เลือกอย่างน้อย 1 แบรนด์').required(),
});

const ModalRawMaterialImportBrand = ({
  open,
  handleClose,
  setBrandSelected,
  brandSelected = [],
}: Props) => {
  const { control, handleSubmit, reset } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });
  const {
    field: { ref, value, onChange },
    formState: { errors },
  } = useController({
    name: 'brands',
    control,
    defaultValue: [],
  });
  const [searchBrand, setSearchBrand] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [listBrandModal, setListBrandModal] = useState<any[]>([]);
  const checkboxIds: any = useWatch({ control, name: 'brands' }) || [];
  const debounced = useDebounceCallback(setSearchBrand, 500);

  const fetchListBrand = async (value?: string) => {
    setLoadingSearch(true);
    const brandList = await apiBrand.getListBrand({
      searchName: value || '',
    });
    if (brandList.status) {
      setListBrandModal(brandList.data);
    }
    setLoadingSearch(false);
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      fetchListBrand(searchBrand);
    }, 1000);
    return () => clearTimeout(timeout);
  }, [searchBrand]);

  useEffect(() => {
    if (open) {
      setListBrandModal([]);
      setSearchBrand('');
      fetchListBrand();
    }
    return () => {
      reset({ brands: brandSelected.map((brand: any) => brand.id) });
    };
  }, [open, brandSelected, reset]);

  const isAllSelected = () => {
    const selectedBrandIds = new Set([
      ...brandSelected.map((b: any) => b.id),
      ...checkboxIds,
    ]);

    return (
      listBrandModal.length > 0 &&
      listBrandModal.every((brand) => selectedBrandIds.has(brand.id))
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectableBrands = listBrandModal
      .filter((brand) => !brandSelected.some((b: any) => b.id === brand.id))
      .map((brand) => brand.id);
    if (event.target.checked) {
      const newSelection: any = [...value];
      selectableBrands.forEach((id) => {
        if (!newSelection.includes(id)) {
          newSelection.push(id);
        }
      });
      onChange(newSelection);
    } else {
      const newSelection: any = value.filter((id) =>
        brandSelected.some((b: any) => b.id === id)
      );
      onChange(newSelection);
    }
  };

  const handleSingleSelect = (brandId: number) => {
    if (brandSelected.some((b: any) => b.id === brandId)) return;

    const newArray: any = [...value];

    const index = newArray.findIndex((id: number) => id === brandId);
    if (index === -1) {
      newArray.push(brandId);
    } else {
      newArray.splice(index, 1);
    }
    onChange(newArray);
  };

  const onSubmit = (values: any) => {
    const newSelectedBrands = listBrandModal.filter((brand: any) =>
      values.brands.includes(brand.id)
    );

    const brandMap = new Map();

    brandSelected.forEach((brand: any) => {
      brandMap.set(brand.id, brand);
    });

    newSelectedBrands.forEach((brand: any) => {
      brandMap.set(brand.id, brand);
    });

    const allSelectedBrands = Array.from(brandMap.values());

    setBrandSelected(allSelectedBrands);
    handleClose();
  };
  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div className="title">จัดการแบรนด์</div>
              <div className="x-close" onClick={handleClose}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form onSubmit={handleSubmit(onSubmit)} style={{ rowGap: 0 }}>
                <TextField
                  className="fade-in"
                  fullWidth
                  placeholder="จัดการแบรนด์"
                  onChange={(event) => debounced(event.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <div className="h-[24px] w-[24px] flex items-center justify-center">
                          {loadingSearch ? (
                            <CircularProgress size={20} />
                          ) : (
                            <Search />
                          )}
                        </div>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ marginTop: '24px' }}
                />
                <RawMaterialImportBrandStyles>
                  <div className="content-wrap">
                    <div className="list-text">
                      {listBrandModal.length} รายการ
                    </div>
                    <div className="selectAll">
                      <FormControlLabel
                        label="เลือกทั้งหมด"
                        control={
                          <Checkbox
                            color="primary"
                            checked={isAllSelected()}
                            onChange={handleSelectAll}
                            icon={<IconUnCheckbox />}
                            checkedIcon={<IconCheckboxBlack />}
                            sx={{ marginLeft: '-8px' }}
                          />
                        }
                      />
                    </div>
                  </div>
                  <div className="list-wrap">
                    <FormGroup>
                      {!isEmpty(listBrandModal) ? (
                        listBrandModal.map((brand, index) => (
                          <label key={index} className="item-wrap">
                            <div className="list">
                              <Image
                                src={
                                  brand.imageUrl ||
                                  '/images/product/empty-product.svg'
                                }
                                width={40}
                                height={40}
                                alt=""
                                style={{ borderRadius: '4px' }}
                              />
                              <div className="text-wrap">
                                <h4>{brand.name}</h4>
                                <span>{brand.description || '-'}</span>
                              </div>
                            </div>
                            <FormControlLabel
                              id={`brand-${index}`}
                              label=""
                              control={
                                <Checkbox
                                  color="primary"
                                  checked={
                                    (value as number | any).includes(
                                      brand.id
                                    ) ||
                                    brandSelected.some(
                                      (b: any) => b.id === brand.id
                                    )
                                  }
                                  onChange={() => handleSingleSelect(brand.id)}
                                  value={brand.id}
                                  ref={ref}
                                  icon={<IconUnCheckbox />}
                                  checkedIcon={
                                    brandSelected.some(
                                      (b: any) => b.id === brand.id
                                    ) ? (
                                      <IconDoneCheckbox />
                                    ) : (
                                      <IconCheckboxBlack />
                                    )
                                  }
                                  sx={{ marginLeft: '-8px' }}
                                />
                              }
                            />
                          </label>
                        ))
                      ) : (
                        <div className="empty-brand">
                          <h4>ไม่พบแบรนด์ที่ค้นหา</h4>
                          <div>ค้นหาแบรนด์สินค้าจากชื่อแบรนด์</div>
                        </div>
                      )}
                      <div className={'error-text'}>
                        {errors?.brands?.message || ''}
                      </div>
                    </FormGroup>
                  </div>
                </RawMaterialImportBrandStyles>
                <div className="flex gap-[24px] mt-[34px]">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    fullWidth
                    onClick={handleClose}
                  >
                    ยกเลิก
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    fullWidth
                  >
                    บันทึก
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalRawMaterialImportBrand;
