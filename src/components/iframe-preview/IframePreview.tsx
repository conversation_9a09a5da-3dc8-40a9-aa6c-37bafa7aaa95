import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';

type PreviewProps = {
  modelId: number;
  modelType: '3d' | 'dieline';
  width: number;
  height: number;
  length: number;
  material: {
    inside: MaterialType;
    outside: MaterialType;
  };
  onInfo: (info: any) => void;
};

type MaterialType = 'white' | 'kraft' | 'gray';

const IframePreview = ({
  modelId,
  modelType,
  width,
  height,
  length,
  material,
  onInfo,
}: PreviewProps) => {
  const router = useRouter();
  const iframeRef: any = useRef(null);

  useEffect(() => {
    if (iframeRef.current) {
      // eslint-disable-next-line no-self-assign
      iframeRef.current.src = iframeRef.current.src;
    }
  }, [router.query]);

  useEffect(() => {
    if (iframeRef.current) {
      console.log('iframeRef', iframeRef);
    }
  }, [iframeRef]);

  return (
    <iframe
      ref={iframeRef}
      src={`http://localhost:3200/preview?modelId=${modelId}&type=${modelType}&width=${width}&height=${height}&length=${length}&inside=${material.inside}&outside=${material.outside}`}
      width="100%"
      height="100%"
    />
  );
};

export default IframePreview;
