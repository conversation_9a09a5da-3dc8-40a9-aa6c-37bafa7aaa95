import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';

type PreviewProps = {
  modelId: number;
  modelType: '3d' | 'dieline';
  width: number;
  height: number;
  length: number;
  material: {
    inside: MaterialType;
    outside: MaterialType;
  };
  onInfo: (info: any) => void;
};

type MaterialType = 'white' | 'kraft' | 'gray';

const IframePreview = ({
  modelId,
  modelType,
  width,
  height,
  length,
  material,
  onInfo,
}: PreviewProps) => {
  const router = useRouter();
  const iframeRef: any = useRef(null);

  useEffect(() => {
    if (iframeRef.current) {
      // eslint-disable-next-line no-self-assign
      iframeRef.current.src = iframeRef.current.src;
    }
  }, [router.query]);

  // PostMessage API listener for receiving data from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security - only accept messages from the iframe domain
      if (event.origin !== 'http://localhost:3200') {
        console.warn(
          'Received message from unauthorized origin:',
          event.origin
        );
        return;
      }

      // Handle the received data
      console.log('Received data from iframe:', event.data);

      // Call the onInfo callback with the received data
      if (onInfo && typeof onInfo === 'function') {
        onInfo(event.data);
      }
    };

    // Add event listener for messages
    window.addEventListener('message', handleMessage);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [onInfo]);

  return (
    <iframe
      ref={iframeRef}
      src={`http://localhost:3200/preview?modelId=${modelId}&type=${modelType}&width=${width}&height=${height}&length=${length}&inside=${material.inside}&outside=${material.outside}`}
      width="100%"
      height="100%"
    />
  );
};

export default IframePreview;
