import styled from 'styled-components';
import usePacdoraScript from '@/hooks/usePacdoraScript';
import usePacdoraInitialization from '@/hooks/usePacdoraInitialization';
import React, { useEffect, useState, useRef } from 'react';
import { Package, PackageOpen } from 'lucide-react';
import {
  createScene,
  getBoxInfo,
  setCollapse,
  setMaterial,
  setSize,
} from '@/utils/pacdora';

// http://127.0.0.1:3200/preview?modelId=100010&type=3d&width=100&height=50&length=40&material=kraft

type PreviewProps = {
  modelId: number;
  modelType: '3d' | 'dieline';
  width: number;
  height: number;
  length: number;
  material: {
    inside: MaterialType;
    outside: MaterialType;
  };
  onInfo: (info: any) => void;
};

type MaterialType = 'white' | 'kraft' | 'gray';

interface ModelControllerWrapperProps {
  activeMode: '3d' | 'dieline' | null;
  collapseValue?: number;
}

const Preview = ({
  modelId,
  modelType,
  width,
  height,
  length,
  material,
  onInfo,
}: PreviewProps) => {
  const [type, setType] = useState<'3d' | 'dieline'>(modelType || '3d');
  const [modelCollapse, setModalCollapse] = useState<number>(0);
  const viewContainerRef = useRef<HTMLDivElement>(null);
  const [modelMaterial, setModalMaterial] = useState<{
    name: string;
    image: string;
    insideName: string;
    insideImage: string;
  } | null>(null);
  const scriptLoaded = usePacdoraScript();
  const isInitialized = usePacdoraInitialization(scriptLoaded, modelId);

  useEffect(() => {
    onCreateScene();
  }, [isInitialized]);

  const onCreateScene = async () => {
    if (!isInitialized) {
      return;
    }
    await createScene({
      modelId: modelId,
      width: width,
      height: height,
      length: length,
    });
  };

  useEffect(() => {
    handleMaterial();
  }, [material]);

  const handleMaterial = () => {
    const white =
      'https://cdn.pacdora.com/web-assets/7a8defb0-6713-4b9a-8a43-b8ce277fe7f0.jpeg';
    const gray = '/texture/gray-paper.png';
    const kraft =
      'https://cdn.pacdora.com/science/image/00e45c0b-9cf7-4d39-bdc8-82bb202909d9.png';
    setModalMaterial({
      name: 'material name for outside surface',
      image:
        material.outside === 'white'
          ? white
          : material.outside === 'kraft'
          ? kraft
          : material.outside === 'gray'
          ? gray
          : white,
      insideName: 'material name for inside surface',
      insideImage:
        material.inside === 'white'
          ? white
          : material.inside === 'kraft'
          ? kraft
          : material.inside === 'gray'
          ? gray
          : white,
    });
  };

  useEffect(() => {
    getInfo();
  }, [isInitialized, width, height, length]);

  const getInfo = async () => {
    if (!isInitialized) {
      return;
    }
    await getBoxInfo().then((info) => {
      console.log('INFO-2', info);
      onInfo(info);
    });
  };

  useEffect(() => {
    onCollapse();
  }, [modelCollapse]);

  const onCollapse = async () => {
    if (!isInitialized) {
      return;
    }
    if (modelCollapse) {
      await setCollapse({
        id: 'view3d',
        score: 1 - modelCollapse,
      });
    }
  };

  useEffect(() => {
    onResize({ width: width, height: height, length: length });
  }, [width, height, length, isInitialized]);

  const onResize = async (dataSize: {
    width: number;
    height: number;
    length: number;
  }) => {
    await setSize({
      width: dataSize.width,
      height: dataSize.height,
      length: dataSize.length,
      type: 'id',
      async: true,
    });
  };

  useEffect(() => {
    onHandleMaterial();
  }, [modelMaterial, isInitialized]);

  const onHandleMaterial = async () => {
    if (!isInitialized) {
      return;
    }
    if (modelMaterial && type === '3d') {
      await setMaterial({
        name: modelMaterial.name,
        image: modelMaterial.image,
        insideName: modelMaterial.insideName,
        insideImage: modelMaterial.insideImage,
        async: true,
      });
    }
  };

  return (
    <PreviewContainer>
      {!isInitialized && (
        <LoadingIndicator>
          <div className="loader" />
        </LoadingIndicator>
      )}
      <div className={'relative h-full'} ref={viewContainerRef}>
        <div
          className={`absolute w-full h-full ${
            type === '3d' ? 'z-20' : 'z-10'
          }`}
        >
          <ViewContainer data-pacdora-ui={'3d'} data-pacdora-id="view3d" />
        </div>
        <div
          className={`absolute w-full h-full ${
            type === 'dieline' ? 'z-20' : 'z-10'
          }`}
        >
          <ViewContainer data-pacdora-ui={'dieline'} data-pacdora-id="view3d" />
        </div>
      </div>
      <ModelControllerWrapper activeMode={type} collapseValue={modelCollapse}>
        <div className={'controller-container'}>
          <div className={'mode-switcher'}>
            <div
              className={`mode-item ${type === '3d' ? 'active' : ''}`}
              onClick={() => setType('3d')}
            >
              3D
            </div>
            <div
              className={`mode-item ${type === 'dieline' ? 'active' : ''}`}
              onClick={() => setType('dieline')}
            >
              2D
            </div>
          </div>
          <div className={'collapse-slide'}>
            <label className="slider">
              <Package size={16} className="volume left-icon" />
              <input
                type="range"
                className="level"
                min="0"
                max="1"
                step="0.001"
                value={modelCollapse}
                onChange={(e) => setModalCollapse(parseFloat(e.target.value))}
              />
              <PackageOpen size={16} className="volume right-icon" />
            </label>
          </div>
          {/* <div className={'zoom-control'}> */}
          {/*  <button */}
          {/*    className="zoom-btn" */}
          {/*    onClick={handleZoomDecrease} */}
          {/*    disabled={modelZoom <= 0.1} */}
          {/*  > */}
          {/*    <Minus size={20} /> */}
          {/*  </button> */}
          {/*  <span className="zoom-percentage"> */}
          {/*    {Math.round(modelZoom * 100)}% */}
          {/*  </span> */}
          {/*  <button */}
          {/*    className="zoom-btn" */}
          {/*    onClick={handleZoomIncrease} */}
          {/*    disabled={modelZoom >= 2.0} */}
          {/*  > */}
          {/*    <Plus size={20} /> */}
          {/*  </button> */}
          {/* </div> */}
        </div>
      </ModelControllerWrapper>
    </PreviewContainer>
  );
};

export default Preview;

const PreviewContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #f5f7f8;
  overflow: hidden;
  position: relative;
`;

const LoadingIndicator = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;

  .loader {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: transparent;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    animation: spin89345 1s linear infinite;
  }

  @keyframes spin89345 {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
`;

const ViewContainer = styled.div`
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
`;

const ModelControllerWrapper = styled.div<ModelControllerWrapperProps>`
  width: 100%;
  position: absolute;
  bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 30;

  .controller-container {
    width: fit-content;
    height: 48px;
    box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.16);
    padding: 16px;
    border-radius: 100px;
    display: flex;
    align-items: center;
    gap: 16px;
    background: #fff;
    .mode-switcher {
      position: relative;
      display: flex;
      height: 32px;
      padding: 2px;
      align-items: center;
      gap: 2px;
      border-radius: 16px;
      border: 1px solid #dbe2e5;
      background: #f5f7f8;
      overflow: hidden;

      /* Sliding indicator background */
      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 28px;
        height: 26px;
        background: #000;
        border-radius: 14px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(
          ${(props) => (props.activeMode === '3d' ? '0px' : '30px')}
        );
        z-index: 1;
      }

      .mode-item {
        position: relative;
        border-radius: 14px;
        background: transparent;
        display: flex;
        width: 28px;
        height: 28px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        color: #666;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 2;

        /* Hover effect */
        &:hover {
          color: #333;
          transform: scale(1.05);
        }

        /* Active state */
        &.active {
          color: #fff;
          transform: scale(1);
        }

        /* Smooth color transition when becoming active/inactive */
        &:not(.active) {
          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .collapse-slide {
      display: flex;
      align-items: center;
      position: relative;
      /* Add vertical line to the right */
      //&::after {
      //  content: '';
      //  position: absolute;
      //  right: -8px;
      //  top: 50%;
      //  transform: translateY(-50%);
      //  width: 1px;
      //  height: 24px;
      //  background-color: #ccc;
      //}
      /* slider */
      --slider-width: 80px;
      --slider-height: 6px;
      --slider-bg: #ccc;
      --slider-border-radius: 999px;
      /* level */
      --level-color: #000;
      --level-transition-duration: 0.1s;
      /* icon */
      --icon-margin: 12px;
      --icon-color: #666;
      --icon-size: 16px;

      .slider {
        cursor: pointer;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        gap: var(--icon-margin);
      }

      .slider .volume {
        display: inline-block;
        vertical-align: top;
        color: var(--icon-color);
        width: var(--icon-size);
        height: auto;
        flex-shrink: 0;
      }

      .slider .left-icon {
        order: 1;
      }

      .slider .level {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: var(--slider-width);
        height: var(--slider-height);
        background: var(--slider-bg);
        overflow: hidden;
        border-radius: var(--slider-border-radius);
        transition: height var(--level-transition-duration);
        cursor: inherit;
        order: 2;
      }

      .slider .right-icon {
        order: 3;
      }

      .slider .level::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 0;
        height: 0;
        box-shadow: -200px 0 0 200px var(--level-color);
      }

      .slider .level::-moz-range-thumb {
        width: 0;
        height: 0;
        border: none;
        background: transparent;
        box-shadow: -200px 0 0 200px var(--level-color);
      }
    }

    .zoom-control {
      display: flex;
      align-items: center;
      gap: 8px;

      .zoom-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        border-radius: 4px;
        cursor: pointer;
        color: #666;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          color: #333;
        }

        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }

        svg {
          width: 20px;
          height: 20px;
        }
      }

      .zoom-percentage {
        font-size: 12px;
        font-weight: 600;
        color: #333;
        min-width: 36px;
        text-align: center;
      }
    }
  }
`;
