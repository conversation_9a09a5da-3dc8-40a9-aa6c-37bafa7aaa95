import styled from 'styled-components';
import { CheckBox, SquareOutlined } from '@mui/icons-material';

const ProductItemStyle = styled.div`
  position: relative;
  img {
    width: 100%;
    border: 2px solid #fff;
    border-radius: 16px;
    transition: 0.4s;
    background: #f5f7f8;
  }
  p {
    margin: 0 0 5px 0;
    font-size: 0.8em;
    color: #b0bec5;
  }
  h3 {
    margin-top: 0;
    margin-bottom: 5px;
    font-weight: 400;
    font-size: 1em;
  }
  cursor: pointer;
  &:hover {
    img {
      border-color: #30d5c7;
    }
  }
  .checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
  }
`;
type ProductItemProps = {
  data: any;
  handleClick: (value: number) => void;
  checked: boolean;
};
const ProductItem = ({ data, handleClick, checked }: ProductItemProps) => {
  return (
    <ProductItemStyle onClick={() => handleClick(data.id)}>
      <div className="checkbox">
        {checked ? (
          <CheckBox sx={{ color: '#30D5C7' }} />
        ) : (
          <SquareOutlined sx={{ color: '#DBE2E5' }} />
        )}
      </div>
      <img src="/images/product/mock-product.png" alt="" />
      <div className="py-2">
        <h3 className="text-center">{data.name}</h3>
        <p className="text-center">{data.productTypeName}</p>
      </div>
    </ProductItemStyle>
  );
};

export default ProductItem;
