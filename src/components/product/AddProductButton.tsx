import styled from 'styled-components';
import { AddCircle } from '@mui/icons-material';

const AddProductButtonStyle = styled.button`
  height: 40px;
  border: 1px solid #dbe2e5;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  border-radius: 32px;
  padding: 0 15px;
  background: none;
  font-family: Prompt, sans-serif;
  width: fit-content;
  cursor: pointer;
  transition: 0.2s;
  &:hover {
    border-color: #333;
  }
`;

type AddProductButtonProps = {
  handleClick: () => void;
};
const AddProductButton = ({ handleClick }: AddProductButtonProps) => {
  return (
    <AddProductButtonStyle onClick={() => handleClick()}>
      <AddCircle />
      <span>เพิ่มสินค้า</span>
    </AddProductButtonStyle>
  );
};

export default AddProductButton;
