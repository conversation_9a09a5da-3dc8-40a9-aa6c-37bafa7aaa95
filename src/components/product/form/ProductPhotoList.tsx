import styled from 'styled-components';
import PhotoBox from '@/components/product/form/PhotoBox';
import { useState } from 'react';

const ProductPhotoListStyle = styled.div`
  display: grid;
  grid-gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
`;

const ProductPhotoList = () => {
  const [images] = useState([]);
  return (
    <ProductPhotoListStyle>
      <PhotoBox
        imageUrl={images[0]}
        handleChangeFile={(file: any) => console.log(file)}
        handleDelete={() => {
          console.log('aa');
        }}
      />
      <PhotoBox
        imageUrl={images[1]}
        handleChangeFile={(file: any) => console.log(file)}
        handleDelete={() => {
          console.log('aa');
        }}
      />
      <PhotoBox
        imageUrl={images[2]}
        handleChangeFile={(file: any) => console.log(file)}
        handleDelete={() => {
          console.log('aa');
        }}
      />
      <PhotoBox
        imageUrl={images[3]}
        handleChangeFile={(file: any) => console.log(file)}
        handleDelete={() => {
          console.log('aa');
        }}
      />
    </ProductPhotoListStyle>
  );
};

export default ProductPhotoList;
