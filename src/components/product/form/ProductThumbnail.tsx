import styled from 'styled-components';
import <PERSON>Field from '@/components/ImageField';
import apiProductImages from '@/services/stock/productImages';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import { LoadingFadein } from '@/styles/share.styled';

const ThumbnailStyle = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin: 28px 0 8px;
  animation: ${LoadingFadein} 0.3s ease-in;
`;

type ProductThumbnailProps = {
  imageUrl: string;
  name: string;
  categoryName: string;
};
const ProductThumbnail = ({
  imageUrl,
  name,
  categoryName,
}: ProductThumbnailProps) => {
  const router = useRouter();
  const uploadThumbnail = async (file: any) => {
    if (file) {
      const { id: productId } = router.query;
      const formData = new FormData();
      formData.append('productId', String(productId));
      formData.append('file', file[0]);
      const res = await apiProductImages.uploadThumbnail(formData);
      if (res && !res.isError) {
        Swal.fire('สำเร็จ', 'อัปเดตรูปปกสินค้าเรียบร้อยแล้ว', 'success');
      }
    }
  };
  return (
    <ThumbnailStyle>
      <div className="img-container">
        <ImageField
          handleChange={(file: any) => {
            uploadThumbnail(file);
          }}
          objectFit="cover"
          defaultBackground={imageUrl || '/images/add-image.svg'}
          borderRadius="14px"
        />
      </div>
      <h2 className="m-0">{name}</h2>
      <p className="m-0">{categoryName}</p>
    </ThumbnailStyle>
  );
};

export default ProductThumbnail;
