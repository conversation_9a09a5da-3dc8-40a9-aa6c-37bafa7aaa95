export type ProductSizeType = {
  maxHeight: string | number;
  maxLength: string | number;
  maxWidth: string | number;
  minHeight: string | number;
  minLength: string | number;
  minWidth: string | number;
};

export type ProductFormType = {
  name: string;
  description: string;
  productCategoryId: string;
  productTypeId: string;
  urlSlug: string;
  componentId: string;
  productSize: ProductSizeType;
  displayUnit: string;
  imageUrl: string;
};
