import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import {
  Avatar,
  Button,
  Checkbox,
  Chip,
  Dialog,
  IconButton,
} from '@mui/material';
import { Add, Close, KeyboardBackspace } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Swal from 'sweetalert2';
import styled, { css } from 'styled-components';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiPrinting from '@/services/stock/printing';
import apiProductColor from '@/services/stock/productColor';

export const PrintingListStyle = styled.div`
  display: flex;
  flex-direction: column;
`;

export const PrintingItemStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  overflow: hidden;
  .img-container {
    height: 80px;
    width: 80px;
    min-width: 80px;
    border-right: 1px solid #dbe2e5;
    padding: 12px;
    position: relative;
    .img-box {
      position: relative;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  svg {
    opacity: 0.5;
  }
  .product-printing-detail {
    flex: 1;
    padding-left: 16px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding-right: 10px;
  }
`;

const DialogContentStyle = styled.div<{ $step: number }>`
  width: 500px;
  max-width: 100%;
  background: white;
  height: 500px;
  position: relative;
  .dialog-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 8px;
    height: 58px;
    width: 100%;
    border-bottom: 1px solid #dbe2e5;
    h2 {
      flex: 1;
      text-align: center;
      font-size: 1.2em;
    }
  }
  .dialog-body {
    padding: 20px;
    > div {
      opacity: 0;
      visibility: hidden;
      z-index: -1;
      display: none;
      transition: 0.3s;
      ${({ $step }) =>
        $step &&
        css`
          &:nth-of-type(${$step}) {
            display: block;
            position: static;
            opacity: 1;
            visibility: visible;
            z-index: 10;
          }
        `}
    }
    .printing-list {
      height: 290px;
      margin-top: 20px;
      overflow-y: auto;
      .printing-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        background: white;
        margin-bottom: 5px;
        gap: 10px;
        padding: 10px;
        font-size: 0.9em;
        font-weight: 300;
        border-radius: 8px;
        transition: 0.2s;
        color: #263238;
        &:hover {
          background: #f5f7f8;
          cursor: pointer;
        }
      }
    }
    .detail-list {
      height: 170px;
      overflow-y: auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      .detail-item {
        width: 100%;
        border-top: 1px solid #dbe2e5;
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          background: #f9f9f9;
        }
        &:last-child {
          border-bottom: 1px solid #dbe2e5;
        }
      }
    }
  }
`;

type MaterialFormProps = {
  productData: any;
};
const PrintingForm = ({ productData }: MaterialFormProps) => {
  const router = useRouter();
  const [rows, setRows] = useState<any>([]);
  const [dialogStep, setDialogStep] = useState(1);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [printingList, setPrintingList] = useState([]);
  const [printingSelected, setPrintingSelected] = useState<any>(null);
  const formik = useFormik({
    initialValues: {
      colorId: [],
    } as any,
    onSubmit: (values: any) => {
      submitImport({
        ...values,
        colorId: values.colorId.map((item: string) => parseInt(item)),
      });
    },
  });

  useEffect(() => {
    getProductPrintingList();
  }, []);

  const getProductPrintingList = async () => {
    const res = await apiProductColor.getList(productData.id);
    // console.log('getProductPrintingList', res.data);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const getPrintingList = async () => {
    const res = await apiPrinting.getList();
    if (res && !res.isError) {
      setPrintingList(res.data);
    }
  };

  const openDialog = async () => {
    setDialogStep(1);
    await getPrintingList();
    setOpenDialog(true);
  };

  const deleteModel = async (printing: any, productPrintingId: number) => {
    Swal.fire({
      title: 'ลบ Printing',
      text: `ยืนยันการลบ ${printing.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductColor.delete(`${productPrintingId}`);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: `ลบข้อมูล ${printing.name} เรียบร้อย`,
            icon: 'success',
          }).then(() => {
            getProductPrintingList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const clickPrinting = async (printing: any) => {
    setPrintingSelected(printing);
    setDialogStep(2);
    await formik.setFieldValue('colorId', []);
  };

  const submitImport = async (values: any) => {
    const { id: productId } = router.query;
    const data = {
      productId,
      printId: printingSelected.id,
      ...values,
    };
    const res = await apiProductColor.create(data);
    if (res && !res.isError) {
      getProductPrintingList();
      setOpenDialog(false);
    }
  };

  return (
    <>
      <AttributeHeaderStyle>
        <h2>Printings</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog()}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <PrintingListStyle>
        {rows &&
          rows.map((item: any) => (
            <PrintingItemStyle key={item.id}>
              <div className="img-container">
                <div className="img-box">
                  <Image
                    src={
                      item.print.imageUrl || '/images/product/empty-product.svg'
                    }
                    alt=""
                    fill
                    style={{ objectFit: 'cover', borderRadius: '4px' }}
                  />
                </div>
              </div>
              <div className="flex-1 flex-col overflow-auto">
                <div className="product-printing-detail">
                  <span className="font-[600] flex-1">{item.print.name}</span>
                  <div className="flex flex-row items-center">
                    <IconButton
                      color="error"
                      onClick={() => deleteModel(item.print, item.id)}
                      style={{ borderRadius: '8px' }}
                    >
                      <Trash2 />
                    </IconButton>
                  </div>
                </div>
                <div className="px-4 py-2 flex flex-row gap-2 overflow-auto color-list">
                  {item.color.map((item: any, index: number) => (
                    <Chip label={item.name} key={index} size="small" />
                  ))}
                </div>
              </div>
            </PrintingItemStyle>
          ))}
      </PrintingListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContentStyle $step={dialogStep}>
          <div className="dialog-head">
            <div className="w-[40px]">
              {dialogStep === 2 && (
                <IconButton onClick={() => setDialogStep(1)}>
                  <KeyboardBackspace />
                </IconButton>
              )}
            </div>
            <h2>Import Printing</h2>
            <div className="w-[40px]">
              <IconButton onClick={() => setOpenDialog(false)}>
                <Close />
              </IconButton>
            </div>
          </div>
          <div className="dialog-body">
            <div className="step-1">
              <div className="flex flex-row items-center justify-between pb-2">
                <h3 className="m-0">Printings</h3>
              </div>
              <div className="printing-list">
                {printingList.map((item: any, index: number) => (
                  <div
                    className="printing-item"
                    key={index}
                    onClick={() => clickPrinting(item)}
                  >
                    <Avatar src={item.imageUrl}>{item.name[0]}</Avatar>
                    <div>{item.name}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="step-2">
              {printingSelected && (
                <div className="flex flex-col items-center">
                  <Avatar
                    src={printingSelected.imageUrl}
                    alt=""
                    sx={{ width: '100px', height: '100px' }}
                  ></Avatar>
                  <h2 className="text-[1.2em] mb-8">{printingSelected.name}</h2>
                  <form onSubmit={formik.handleSubmit} className="w-full">
                    <div className="detail-list">
                      {printingSelected.colors.map(
                        (item: any, index: number) => (
                          <label className="detail-item" key={index}>
                            <Checkbox
                              name="colorId"
                              value={item.id}
                              onChange={formik.handleChange}
                              checked={formik.values.colorId.includes(
                                `${item.id}`
                              )}
                            />{' '}
                            <span>{item.name}</span>
                          </label>
                        )
                      )}
                      {printingSelected.colors.length === 0 && (
                        <p className="opacity-50 text-center">* ไม่พบข้อมูล</p>
                      )}
                    </div>
                    <div className="w-full">
                      <Button
                        type="submit"
                        fullWidth
                        color="dark"
                        variant="contained"
                        disabled={formik.values.colorId.length === 0}
                      >
                        Import
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </DialogContentStyle>
      </Dialog>
    </>
  );
};

export default PrintingForm;
