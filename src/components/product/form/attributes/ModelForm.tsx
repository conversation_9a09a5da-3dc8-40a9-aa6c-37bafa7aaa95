import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import {
  But<PERSON>,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { Add, Edit } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import React, { useEffect, useState } from 'react';
import apiProductModel from '@/services/stock/productModel';
import { useRouter } from 'next/router';
import Image from 'next/image';
import ImageField from '@/components/ImageField';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import {
  CustomIosSwitchStyle,
  FormModalStyle,
  LoadingFadein,
} from '@/styles/share.styled';
import styled from 'styled-components';
import * as yup from 'yup';
import { isEmpty } from 'lodash';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const ModelListStyle = styled.div`
  display: flex;
  flex-direction: column;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
const ModelItemStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  overflow: hidden;
  padding-right: 10px;
  .img-container {
    height: 80px;
    width: 80px;
    border-right: 1px solid #dbe2e5;
    padding: 12px;
    position: relative;
    .img-box {
      position: relative;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  svg {
    opacity: 0.5;
  }
`;

const validationSchema = yup.object().shape({
  name: yup.string().required('กรุณากรอก Name'),
  file: yup.mixed().required('กรุณาอัปโหลดไฟล์'),
  isAutoLay: yup.boolean(),
  layModelId: yup.lazy((value, context) => {
    if (context.parent.isAutoLay) {
      return yup.number().required('กรุณาเลือก Model');
    }
    return yup.number().notRequired();
  }),
});

const validationUpdateSchema = yup.object().shape({
  name: yup.string().required('กรุณากรอก Name'),
  isAutoLay: yup.boolean(),
  layModelId: yup.lazy((value, context) => {
    if (context.parent.isAutoLay) {
      return yup.number().required('กรุณาเลือก Model');
    }
    return yup.number().notRequired();
  }),
});

export { validationSchema, validationUpdateSchema };

const ModelForm = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id: productId } = router.query;
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [data, setData] = useState<any>([]);
  const [formMode, setFormMode] = useState<string>('');
  const [modelImage, setModelLayImage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      file: null,
      name: '',
      layModelId: '',
      isAutoLay: false,
    },
    validationSchema:
      formMode === 'create' ? validationSchema : validationUpdateSchema,
    onSubmit: (values: any) => {
      if (formMode === 'create') {
        createModel({
          ...values,
          productId,
        });
      } else {
        updateModel({
          ...values,
          productId,
        });
      }
    },
  });

  // const getLayModel = async () => {
  //   const res = await apiLayModel.getLayModelList();
  //   if (!res.isError) {
  //     setLayModelList(res.content);
  //   }
  // };

  useEffect(() => {
    // getLayModel();
    getModels();
  }, []);

  const getModels = async () => {
    const { id: productId } = router.query;
    const res = await apiProductModel.getList(`${productId}`);
    if (res && !res.isError) {
      setData(res.data);
    }
  };

  const createModel = async (values: any) => {
    setLoading(true);
    const formData: FormData = new FormData();
    const { file } = formik.values;
    if (values.isAutoLay) {
      formData.append(
        'productModelRequest',
        JSON.stringify({
          name: values.name,
          productId: Number(values.productId),
          layModelId: values.layModelId,
          isAutoLay: true,
        })
      );
    } else {
      formData.append(
        'productModelRequest',
        JSON.stringify({
          name: values.name,
          productId: Number(values.productId),
          layModelId: null,
          isAutoLay: false,
        })
      );
    }

    if (file) {
      formData.append('file', file[0]);
    }
    const res = await apiProductModel.create(formData);
    if (res && !res.isError) {
      getModels();
      setOpenDialog(false);
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มโมเดลสำเร็จ',
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoading(false);
  };

  const updateModel = async (values: any) => {
    setLoading(true);
    const formData: FormData = new FormData();
    if (values.isAutoLay) {
      formData.append(
        'productModelRequest',
        JSON.stringify({
          name: values.name,
          productId: Number(values.productId),
          isActive: values.isActive,
          layModelId: values.layModelId,
          isAutoLay: true,
        })
      );
    } else {
      formData.append(
        'productModelRequest',
        JSON.stringify({
          name: values.name,
          productId: Number(values.productId),
          isActive: values.isActive,
          layModelId: null,
          isAutoLay: false,
        })
      );
    }
    if (formik.values.file) {
      formData.append('file', formik.values.file[0] as any);
    }
    const res = await apiProductModel.update(values.id, formData);
    if (res && !res.isError) {
      getModels();
      setOpenDialog(false);
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขโมเดลสำเร็จ',
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setLoading(false);
  };

  const updateStatusModel = async (values: any) => {
    const formData: FormData = new FormData();
    formData.append(
      'productModelRequest',
      JSON.stringify({
        name: values.name,
        productId: Number(values.productId),
        isActive: !values.isActive,
        layModelId: isEmpty(values.layModelId) ? null : values.layModelId,
        isAutoLay: values.isAutoLay,
      })
    );
    const res = await apiProductModel.update(values.id, formData);
    if (res && !res.isError) {
      getModels();
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขสถานะสำเร็จ',
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };

  const openDialog = async (mode: string, modelId: any) => {
    formik.resetForm();
    if (modelId) {
      const model = data.find((item: any) => item.id === modelId);
      if (model) {
        if (model.layModelId === null) {
          model.layModelId = '';
        }
        await formik.setValues(model);
        setModelLayImage(model.imageUrl);
      }
    } else {
      setModelLayImage('');
    }
    setFormMode(mode);
    setOpenDialog(true);
  };

  const deleteModel = async (model: any) => {
    Swal.fire({
      title: 'ลบ Model',
      text: `ยืนยันการลบ ${model.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductModel.delete(model.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'ลบ Model เรียบร้อย',
            icon: 'success',
          }).then(() => {
            getModels();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบ Model ได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const handleChangeStatus = (value: any) => {
    updateStatusModel({
      ...value,
      productId,
    });
  };

  // const handleChangeIsAutoLay = (event: any) => {
  //   const { checked } = event.target;
  //   formik.setFieldValue('isAutoLay', checked);
  // };
  return (
    <>
      <AttributeHeaderStyle>
        <h2>Models</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog('create', null)}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      {data && (
        <ModelListStyle>
          {data.map((item: any) => (
            <ModelItemStyle key={item.id}>
              <div className="img-container">
                <div className="img-box">
                  <Image
                    src={item.imageUrl || '/images/product/empty-product.svg'}
                    alt=""
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              </div>
              <div className="flex-1 ml-[16px] flex flex-row gap-2 items-center">
                <span className="font-[600]">{item.name}</span>
              </div>
              <div className="flex flex-row items-center">
                <CustomIosSwitchStyle
                  style={{
                    marginRight: '8px',
                  }}
                >
                  <IOSSwitch
                    checked={item.isActive}
                    onClick={() => {
                      handleChangeStatus(item);
                    }}
                  />
                </CustomIosSwitchStyle>
                <IconButton
                  style={{ borderRadius: '8px' }}
                  onClick={() => openDialog('edit', item.id)}
                >
                  <Edit />
                </IconButton>
                <IconButton
                  color="error"
                  onClick={() => deleteModel(item)}
                  style={{ borderRadius: '8px' }}
                >
                  <Trash2 />
                </IconButton>
              </div>
            </ModelItemStyle>
          ))}
        </ModelListStyle>
      )}

      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {formMode === 'create' ? 'Create' : 'Edit'} Model
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    setOpenDialog(false);
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div
                className="form-wrap"
                style={{
                  marginTop: '24px',
                }}
              >
                <form onSubmit={formik.handleSubmit}>
                  <ImageField
                    defaultBackground={modelImage || '/images/add-image.svg'}
                    textUploadBtn="Upload"
                    borderRadius={'14px'}
                    conditionText={'Upload JPG, PNG or SVG file maximum 1 MB.'}
                    alertRequire={
                      formik.touched.file && Boolean(formik.errors.file)
                    }
                    handleChange={(file: any) => {
                      formik.setFieldValue('file', file);
                    }}
                  />
                  <div>
                    <p>Name</p>
                    <TextField
                      type="text"
                      name="name"
                      placeholder="Model Name"
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={
                        formik.touched.name && (formik.errors.name as string)
                      }
                    />
                  </div>
                  {/* <FormControlLabel */}
                  {/*  sx={{ */}
                  {/*    margin: '8px 0 0', */}
                  {/*  }} */}
                  {/*  control={ */}
                  {/*    <Checkbox */}
                  {/*      color="primary" */}
                  {/*      checked={formik.values.isAutoLay} */}
                  {/*      onChange={(event: any) => { */}
                  {/*        handleChangeIsAutoLay(event); */}
                  {/*      }} */}
                  {/*      icon={<IconUnCheckbox />} */}
                  {/*      checkedIcon={<IconCheckboxBlack />} */}
                  {/*    /> */}
                  {/*  } */}
                  {/*  label="เปิดใช้งานออโต้เลย์เอาท์" */}
                  {/* /> */}
                  {/* {formik.values.isAutoLay && ( */}
                  {/*  <FadeInStyled> */}
                  {/*    <p className="!mt-[8px]">2D Model</p> */}
                  {/*    <FormControl */}
                  {/*      fullWidth */}
                  {/*      size="small" */}
                  {/*      error={ */}
                  {/*        formik.touched.layModelId && */}
                  {/*        Boolean(formik.errors.layModelId) */}
                  {/*      } */}
                  {/*      sx={{ */}
                  {/*        height: '40px', */}
                  {/*      }} */}
                  {/*    > */}
                  {/*      <Select */}
                  {/*        name="layModelId" */}
                  {/*        value={formik.values.layModelId} */}
                  {/*        onChange={formik.handleChange} */}
                  {/*        displayEmpty */}
                  {/*      > */}
                  {/*        <MenuItem disabled value=""> */}
                  {/*          <div>Select Model</div> */}
                  {/*        </MenuItem> */}
                  {/*        {layModelList.map((item: any) => ( */}
                  {/*          <MenuItem key={item.id} value={item.id}> */}
                  {/*            {item.name} */}
                  {/*          </MenuItem> */}
                  {/*        ))} */}
                  {/*      </Select> */}
                  {/*    </FormControl> */}
                  {/*    {formik.touched.layModelId && */}
                  {/*      formik.errors.layModelId && ( */}
                  {/*        <FormHelperText */}
                  {/*          error */}
                  {/*          sx={{ */}
                  {/*            margin: '4px 14px 0', */}
                  {/*          }} */}
                  {/*        > */}
                  {/*          {formik.errors.layModelId} */}
                  {/*        </FormHelperText> */}
                  {/*      )} */}
                  {/*  </FadeInStyled> */}
                  {/* )} */}

                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={() => {
                        setOpenDialog(false);
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <LoadingButton
                      type="submit"
                      loading={loading}
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                    >
                      บันทึก
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModelForm;
