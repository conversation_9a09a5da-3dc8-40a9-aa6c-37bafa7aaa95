import ConfigTreeView from '@/components/product/form/attributes/ConfigTreeView';
import { ArrowBack, ArrowForward } from '@mui/icons-material';
import styled from 'styled-components';
import React, { useEffect, useState } from 'react';
import apiMaster from '@/services/stock/master';
import { useRouter } from 'next/router';
import apiSubMaterial from '@/services/stock/subMaterial';
import _ from 'lodash';
import { Button } from '@mui/material';
import apiProductConfig from '@/services/stock/product-config';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import Swal from 'sweetalert2';

export default function ConfigTransfer({
  infoProduct,
  editMasterId,
  title,
  onClose,
}: {
  infoProduct: any;
  editMasterId: number | null;
  title: string;
  onClose: any;
}) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;
  const [masterConfigs, setMasterConfigs] = useState<any>([]);
  const [selectedTreeView, setSelectedTreeView] = useState<any>([]);
  const [selectorTreeView, setSelectorTreeView] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const [selectorChecked, setSelectorChecked] = useState<any>([]);
  const [selectorCollapsed, setSelectorCollapsed] = useState<any>([]);
  const [selectedChecked, setSelectedChecked] = useState<any>([]);
  const [selectedCollapsed, setSelectedCollapsed] = useState<any>([]);
  const [changelog, setChangelog] = useState<{ add: any[]; remove: any[] }>({
    add: [],
    remove: [],
  });

  useEffect(() => {
    getMasterChooseConfig();
    setInitialTreeSelected();
  }, [infoProduct]);

  const getMasterChooseConfig = async () => {
    if (infoProduct && id) {
      await apiMaster
        .getMasterChooseConfig(infoProduct.id, Number(id))
        .then((response) => {
          if (response.status) {
            setMasterConfigs(response.data);
          }
        });
    }
  };

  useEffect(() => {
    setSelectorItems();
  }, [masterConfigs]);

  const setSelectorItems = async () => {
    if (masterConfigs) {
      const dataFormat = await masterConfigs.map((material: any) => {
        return {
          id: `m-${material.id}`,
          name: material.name,
          metadata: {
            id: material.id,
            amount: material.subMaterial.length || 0,
          },
          children: material.subMaterial.map((subMaterial: any) => {
            return {
              id: `sup-${subMaterial.id}-m-${material.id}`,
              name: subMaterial.name,
              metadata: {
                id: subMaterial.id,
                amount: subMaterial.subMaterialDetail.length || 0,
              },
              children: subMaterial.subMaterialDetail.map(
                (subMaterialDetail: any) => {
                  return {
                    id: `sd-${subMaterialDetail.id}-sup-${subMaterial.id}-m-${material.id}`,
                    name: subMaterialDetail.name,
                    metadata: {
                      id: subMaterialDetail.id,
                      name: subMaterialDetail.name,
                      masterId: material.id,
                      masterName: material.name,
                      subMaterialId: subMaterial.id,
                      subMaterialName: subMaterial.name,
                      subMaterialDetailId: subMaterialDetail.id,
                    },
                  };
                }
              ),
            };
          }),
        };
      });
      await setSelectorTreeView(dataFormat);
    }
  };

  const setInitialTreeSelected = async () => {
    const subMaterialList = await apiSubMaterial.getAllList();
    if (infoProduct?.productConfig && subMaterialList.data) {
      const format = infoProduct?.productConfig.map((item: any) => {
        const subMaterialIdList = item.configMaterial.map(
          (value: any) => value.subMaterialDetail.subMaterialId
        );
        const subMaterialIds = _.uniq(subMaterialIdList);
        if (subMaterialIds) {
          return {
            id: `m-${item.master.id}`,
            name: item.master.name,
            metadata: {
              id: item.master.id,
              amount: subMaterialIds.length || 0,
            },
            children: subMaterialIds.map((subId: any) => {
              const subMaterialInfo = _.find(
                subMaterialList.data,
                (o: any) => o.id === subId
              );
              const subDetailList = _.filter(
                item.configMaterial,
                (m: any) => m.subMaterialDetail.subMaterialId === subId
              );
              return {
                id: `sup-${subMaterialInfo.id}-m-${item.master.id}`,
                name: subMaterialInfo.name,
                metadata: {
                  id: subMaterialInfo.id,
                  amount: subDetailList.length || 0,
                },
                children: subDetailList.map((subMaterialDetail: any) => {
                  return {
                    id: `sd-${subMaterialDetail.subMaterialDetail.id}-sup-${subMaterialInfo.id}-m-${item.master.id}`,
                    name: subMaterialDetail.subMaterialDetail.name,
                    metadata: {
                      id: subMaterialDetail.subMaterialDetail.id,
                      name: subMaterialDetail.subMaterialDetail.name,
                      masterId: item.master.id,
                      masterName: item.master.name,
                      subMaterialId: subMaterialInfo.id,
                      subMaterialName: subMaterialInfo.name,
                      subMaterialDetailId:
                        subMaterialDetail.subMaterialDetail.id,
                    },
                  };
                }),
              };
            }),
          };
        }
      });
      await setSelectedTreeView(format);
    }
  };

  const onSelectedChange = async (type: 'selector' | 'selected', data: any) => {
    if (type === 'selector') {
      const isSelected = _.some(selectorChecked, (v: any) => v.id === data.id);
      if (!isSelected) {
        setSelectorChecked((prevState: any) => [...prevState, data]);
      } else {
        _.remove(selectorChecked, (v: any) => v.id === data.id);
        setSelectorChecked([]);
        setSelectorChecked(selectorChecked);
      }
    } else {
      const isSelected = _.some(selectedChecked, (v: any) => v.id === data.id);
      if (!isSelected) {
        setSelectedChecked((prevState: any) => [...prevState, data]);
      } else {
        _.remove(selectedChecked, (v: any) => v.id === data.id);
        setSelectedChecked([]);
        setSelectedChecked(selectedChecked);
      }
    }
  };

  const editAutoCollapse = () => {
    if (editMasterId && selectedTreeView.length > 0) {
      const isList = _.some(
        selectedTreeView,
        (o: any) => o.id === `m-${editMasterId}`
      );
      if (isList) {
        setSelectedCollapsed([`m-${editMasterId}`]);
      }
    }
    if (editMasterId && selectorTreeView.length > 0) {
      const isList = _.some(
        selectorTreeView,
        (o: any) => o.id === `m-${editMasterId}`
      );
      if (isList) {
        setSelectorCollapsed([`m-${editMasterId}`]);
      }
    }
  };

  useEffect(() => {
    editAutoCollapse();
  }, [editMasterId, selectedTreeView, selectorTreeView]);

  const onTransferAdd = () => {
    const emptyMaterialData: any[] = [];
    selectorChecked.map((item: any) => {
      const isMaterial = _.find(
        selectedTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      const indexMaterial = _.findIndex(
        selectedTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      if (isMaterial) {
        // add
        const isSubMaterial = _.find(
          isMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        if (isSubMaterial) {
          const indexSubMaterial = _.findIndex(
            isMaterial.children,
            (v: any) =>
              v.id ===
              `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
          );
          const clone = _.clone(selectedTreeView);
          clone[indexMaterial].children[indexSubMaterial].children = [
            ...clone[indexMaterial].children[indexSubMaterial].children,
            { id: item.id, metadata: item.metadata, name: item.name },
          ];
          setSelectedTreeView(clone);
        } else {
          const clone = _.clone(selectedTreeView);
          clone[indexMaterial].children = [
            ...clone[indexMaterial].children,
            {
              id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
              name: item.metadata.subMaterialName,
              children: [
                { id: item.id, metadata: item.metadata, name: item.name },
              ],
            },
          ];
          setSelectedTreeView(clone);
        }
      } else {
        const isMat = _.find(
          emptyMaterialData,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        const indexMat = _.findIndex(
          emptyMaterialData,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        if (!isMat) {
          emptyMaterialData.push({
            id: `m-${item.metadata.masterId}`,
            name: item.metadata.masterName,
            children: [
              {
                id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
                name: item.metadata.subMaterialName,
                children: [
                  { id: item.id, metadata: item.metadata, name: item.name },
                ],
              },
            ],
          });
        } else {
          const isSubMat = _.find(
            isMat.children,
            (v: any) =>
              v.id ===
              `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
          );
          if (!isSubMat) {
            emptyMaterialData[indexMat].children = [
              ...emptyMaterialData[indexMat].children,
              {
                id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
                name: item.metadata.subMaterialName,
                children: [
                  { id: item.id, metadata: item.metadata, name: item.name },
                ],
              },
            ];
          } else {
            const indexSubMat = _.findIndex(
              isMat.children,
              (v: any) =>
                v.id ===
                `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
            );
            emptyMaterialData[indexMat].children[indexSubMat].children = [
              ...emptyMaterialData[indexMat].children[indexSubMat].children,
              { id: item.id, metadata: item.metadata, name: item.name },
            ];
          }
        }
      }

      // remove
      const isRemoveMaterial = _.find(
        selectorTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      if (isRemoveMaterial) {
        const isRemoveSubMaterial = _.find(
          isRemoveMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const indexRemoveMaterial = _.findIndex(
          selectorTreeView,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        const indexRemoveSubMaterial = _.findIndex(
          isRemoveMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const indexRemoveSubMaterialDetail = _.findIndex(
          isRemoveSubMaterial.children,
          (v: any) =>
            v.id ===
            `sd-${item.metadata.id}-sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const cloneRemove = _.clone(selectorTreeView);
        cloneRemove[indexRemoveMaterial].children[
          indexRemoveSubMaterial
        ].children.splice(indexRemoveSubMaterialDetail, 1);
        setSelectorTreeView(cloneRemove);
      }
    });
    const ids: any[] = [];
    const addLogs: any[] = [];
    selectorChecked.map((slc: any) => {
      const masterId = `m-${slc.metadata.masterId}`;
      const subMaterialId = `sup-${slc.metadata.subMaterialId}-m-${slc.metadata.masterId}`;
      ids.push(masterId);
      ids.push(subMaterialId);
      addLogs.push(slc.metadata.id);
    });
    const idsUniq = _.uniq(ids);
    setSelectedCollapsed(idsUniq);
    setSelectedTreeView((prev: any) => [...prev, ...emptyMaterialData]);
    const removeChangelog = _.pullAll(changelog.remove, addLogs);
    setChangelog({
      remove: removeChangelog,
      add: [...changelog.add, ...addLogs],
    });
    setSelectedChecked([]);
    setSelectorChecked([]);
  };

  const onTransferRemove = () => {
    const emptyMaterialData: any[] = [];
    selectedChecked.map((item: any) => {
      const isMaterial = _.find(
        selectorTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      const indexMaterial = _.findIndex(
        selectorTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      if (isMaterial) {
        // add
        const isSubMaterial = _.find(
          isMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        if (isSubMaterial) {
          const indexSubMaterial = _.findIndex(
            isMaterial.children,
            (v: any) =>
              v.id ===
              `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
          );
          const clone = _.clone(selectorTreeView);
          clone[indexMaterial].children[indexSubMaterial].children = [
            ...clone[indexMaterial].children[indexSubMaterial].children,
            { id: item.id, metadata: item.metadata, name: item.name },
          ];
          setSelectorTreeView(clone);
        } else {
          const clone = _.clone(selectorTreeView);
          clone[indexMaterial].children = [
            ...clone[indexMaterial].children,
            {
              id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
              name: item.metadata.subMaterialName,
              children: [
                { id: item.id, metadata: item.metadata, name: item.name },
              ],
            },
          ];
          setSelectorTreeView(clone);
        }
      } else {
        const isMat = _.find(
          emptyMaterialData,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        const indexMat = _.findIndex(
          emptyMaterialData,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        if (!isMat) {
          emptyMaterialData.push({
            id: `m-${item.metadata.masterId}`,
            name: item.metadata.masterName,
            children: [
              {
                id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
                name: item.metadata.subMaterialName,
                children: [
                  { id: item.id, metadata: item.metadata, name: item.name },
                ],
              },
            ],
          });
        } else {
          const isSubMat = _.find(
            isMat.children,
            (v: any) =>
              v.id ===
              `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
          );
          if (!isSubMat) {
            emptyMaterialData[indexMat].children = [
              ...emptyMaterialData[indexMat].children,
              {
                id: `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`,
                name: item.metadata.subMaterialName,
                children: [
                  { id: item.id, metadata: item.metadata, name: item.name },
                ],
              },
            ];
          } else {
            const indexSubMat = _.findIndex(
              isMat.children,
              (v: any) =>
                v.id ===
                `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
            );
            emptyMaterialData[indexMat].children[indexSubMat].children = [
              ...emptyMaterialData[indexMat].children[indexSubMat].children,
              { id: item.id, metadata: item.metadata, name: item.name },
            ];
          }
        }
      }

      // remove
      const isRemoveMaterial = _.find(
        selectedTreeView,
        (v: any) => v.id === `m-${item.metadata.masterId}`
      );
      if (isRemoveMaterial) {
        const isRemoveSubMaterial = _.find(
          isRemoveMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const indexRemoveMaterial = _.findIndex(
          selectedTreeView,
          (v: any) => v.id === `m-${item.metadata.masterId}`
        );
        const indexRemoveSubMaterial = _.findIndex(
          isRemoveMaterial.children,
          (v: any) =>
            v.id ===
            `sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const indexRemoveSubMaterialDetail = _.findIndex(
          isRemoveSubMaterial.children,
          (v: any) =>
            v.id ===
            `sd-${item.metadata.id}-sup-${item.metadata.subMaterialId}-m-${item.metadata.masterId}`
        );
        const cloneRemove = _.clone(selectedTreeView);
        cloneRemove[indexRemoveMaterial].children[
          indexRemoveSubMaterial
        ].children.splice(indexRemoveSubMaterialDetail, 1);
        setSelectedTreeView(cloneRemove);
      }
    });
    const ids: any[] = [];
    const removeLogs: any[] = [];
    selectedChecked.map((slc: any) => {
      const masterId = `m-${slc.metadata.masterId}`;
      const subMaterialId = `sup-${slc.metadata.subMaterialId}-m-${slc.metadata.masterId}`;
      ids.push(masterId);
      ids.push(subMaterialId);
      removeLogs.push(slc.metadata.id);
    });
    const addChangelog = _.pullAll(changelog.add, removeLogs);
    const idsUniq = _.uniq(ids);
    setSelectorCollapsed(idsUniq);
    setSelectorTreeView((prev: any) => [...prev, ...emptyMaterialData]);
    setChangelog({
      add: addChangelog,
      remove: [...changelog.remove, ...removeLogs],
    });
    setSelectedChecked([]);
    setSelectorChecked([]);
  };

  const onValidateImport = async () => {
    if (changelog.add.length === 0 && changelog.remove.length === 0) {
      Swal.fire({
        title: `${title} ไม่มีการเปลี่ยนแปลง`,
        text: `กรุณาเลือก ${title} ที่ต้องการเพิ่มหรือลบในสินค้านี้`,
        icon: 'warning',
        showCancelButton: true,
        showConfirmButton: false,
        cancelButtonText: 'ปิด',
      });
    } else if (changelog.add.length > 0 || changelog.remove.length > 0) {
      Swal.fire({
        title: `${title} มีการเปลี่ยนแปลง`,
        text: `คุณต้องการบันทึก ${title} ในสินค้านี้ ?`,
        icon: 'question',
        showCancelButton: true,
        cancelButtonText: 'ยกเลิก',
        confirmButtonText: 'บันทึก',
      }).then(async (result) => {
        if (result.isConfirmed) {
          importMaterial();
        }
      });
    }
  };

  const importMaterial = async () => {
    setLoading(true);
    selectedTreeView.map(async (res: any) => {
      const split = String(res.id).split('-');
      const masterId = Number(split[1]);
      const subMaterialDetailList: any[] = [];
      await res.children.map((sub: any) => {
        sub.children.map((detail: any) => {
          subMaterialDetailList.push({
            subMaterialDetailId: detail.metadata.id,
          });
        });
      });
      await apiProductConfig
        .create({
          productId: Number(id),
          masterId: masterId,
          configMaterial: subMaterialDetailList,
        })
        .then((res) => {
          if (res && (res.isError || res.error)) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'An error occurred!',
                severity: 'error',
              })
            );
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Success!',
                severity: 'success',
              })
            );
            onClose();
          }
        });
    });
    setLoading(false);
  };

  return (
    <>
      <div className={'transfer-container'}>
        <TransferBox>
          <div className={'box-title selector'}>รายการที่ยังไม่ได้ใช้งาน</div>
          <div className={'tree-view-container'}>
            <ConfigTreeView
              treeList={selectorTreeView || []}
              onSelected={(data: any) => onSelectedChange('selector', data)}
              expands={selectorCollapsed}
            />
          </div>
        </TransferBox>
        <TransferAction>
          <div className={'action'} onClick={() => onTransferAdd()}>
            <ArrowForward />
          </div>
          <div className={'action'} onClick={() => onTransferRemove()}>
            <ArrowBack />
          </div>
        </TransferAction>
        <TransferBox>
          <div className={'box-title selected'}>รายการที่ใช้งานแล้ว</div>
          <div className={'tree-view-container'}>
            <ConfigTreeView
              treeList={selectedTreeView || []}
              onSelected={(data: any) => onSelectedChange('selected', data)}
              expands={selectedCollapsed}
            />
          </div>
        </TransferBox>
      </div>
      <div className="pt-4">
        <Button
          type="submit"
          fullWidth
          variant="contained"
          color="dark"
          onClick={() => onValidateImport()}
          disabled={loading}
        >
          {loading ? 'กำลังบันทึกข้อมูล' : 'บันทึก'}
        </Button>
      </div>
    </>
  );
}

const TransferBox = styled.div`
  border-radius: 8px;
  border: 1px solid #dedede;
  width: 100%;

  .box-title {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
  }

  .selector {
    background: #dedede;
    color: #6c6c6c;
    border-radius: 8px 8px 0 0;
  }

  .selected {
    background: #263238;
    color: #fff;
    border-radius: 8px 8px 0 0;
  }

  .tree-view-container {
    max-height: calc(100vh - 280px);
    min-height: 500px;
    overflow: auto;
    width: 100%;
    padding: 6px 8px;
  }

  .cover {
    padding: 2px 0;
  }

  .transfer-item {
    padding: 4px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    column-gap: 8px;

    .collapse-action {
      display: flex;
      align-items: center;

      .icon {
        width: 16px;
        height: 16px;
        cursor: pointer;
        background: #263238;
        color: #fff;
        border-radius: 999px;
      }
    }

    .checkbox-action {
      display: flex;
      align-items: center;

      .icon {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      .blank {
        width: 16px;
        height: 16px;
        cursor: pointer;
        border-radius: 4px;
        border: 1px solid rgba(40, 44, 52, 0.2);
      }
    }
  }

  .parent {
    background: rgba(0, 0, 0, 0.2);
    color: #000;
  }

  .child {
    margin-left: 24px;
    background: rgba(0, 0, 0, 0.15);
    color: #000;
  }

  .sub {
    margin-left: 48px;
    background: rgba(0, 0, 0, 0.1);
    color: #000;
  }
`;

const TransferAction = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  row-gap: 8px;
  .action {
    cursor: pointer;
    border: 1px solid #dedede;
    border-radius: 8px;
    width: 100%;
    aspect-ratio: 1/1;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      background: #dedede;
      color: #6c6c6c;
    }
  }
`;
