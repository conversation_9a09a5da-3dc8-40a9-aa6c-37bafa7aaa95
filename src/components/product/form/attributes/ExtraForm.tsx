import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import { Avatar, Button, Checkbox, Dialog, IconButton } from '@mui/material';
import { Add, Close } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Swal from 'sweetalert2';
import styled from 'styled-components';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiProductExtra from '@/services/stock/productExtra';
import apiExtra from '@/services/stock/extra';
import { isEmpty } from 'lodash';

export const ExtraListStyle = styled.div`
  display: flex;
  flex-direction: column;
`;

export const ExtraItemStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  overflow: hidden;
  .img-container {
    height: 80px;
    width: 80px;
    border-right: 1px solid #dbe2e5;
    padding: 12px;
    position: relative;
    .img-box {
      position: relative;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  svg {
    opacity: 0.5;
  }
  .product-coating-detail {
    flex: 1;
    padding-left: 16px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding-right: 10px;
  }
`;

const DialogContentStyle = styled.div`
  width: 500px;
  max-width: 100%;
  background: white;
  height: 500px;
  position: relative;
  .dialog-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 8px;
    height: 58px;
    width: 100%;
    border-bottom: 1px solid #dbe2e5;
    h2 {
      flex: 1;
      text-align: center;
      font-size: 1.2em;
    }
  }
  .dialog-body {
    padding: 20px;
    > div {
      transition: 0.3s;
    }
    .extra-list {
      height: 290px;
      margin-top: 20px;
      overflow-y: auto;
      .extra-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        background: white;
        margin-bottom: 5px;
        gap: 10px;
        padding: 10px 5px;
        font-size: 0.9em;
        font-weight: 300;
        border-radius: 8px;
        transition: 0.2s;
        color: #263238;
        &:hover {
          background: #f5f7f8;
          cursor: pointer;
        }
      }
    }
    .detail-list {
      height: 170px;
      overflow-y: auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      .detail-item {
        width: 100%;
        border-top: 1px solid #dbe2e5;
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          background: #f9f9f9;
        }
        &:last-child {
          border-bottom: 1px solid #dbe2e5;
        }
      }
    }
  }
`;

type MaterialFormProps = {
  productData: any;
};
const ExtraForm = ({ productData }: MaterialFormProps) => {
  const router = useRouter();
  const [rows, setRows] = useState<any>([]);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [extraList, setExtraList] = useState([]);
  const formik = useFormik({
    initialValues: {
      extraId: [],
    } as any,
    onSubmit: (values: any) => {
      submitImport({
        ...values,
        extraId: values.extraId.map((item: string) => parseInt(item)),
      });
    },
  });

  useEffect(() => {
    getProductExtraList();
  }, []);

  const getProductExtraList = async () => {
    const res = await apiProductExtra.getList(productData.id, '');
    if (res && !res.isError) {
      setRows(res.data);
    }
  };
  useEffect(() => {
    const extraIds = rows.map((item: any) => item.extraId);
    formik.setFieldValue('extraId', extraIds);
  }, [rows]);

  useEffect(() => {
    if (!isEmpty(rows) && isOpenDialog) {
      const extraIds = rows.map((item: any) => item.extraId);
      formik.setFieldValue('extraId', extraIds);
    }
  }, [isOpenDialog]);

  const getExtraList = async () => {
    const res = await apiExtra.getList({
      page: 0,
      size: 300,
    });
    if (res && !res.isError) {
      setExtraList(res.data.content);
    }
  };

  const openDialog = async () => {
    await getExtraList();
    setOpenDialog(true);
  };

  const deleteModel = async (productExtra: any) => {
    Swal.fire({
      title: 'ลบ Coating',
      text: `ยืนยันการลบ ${productExtra.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductExtra.delete(productExtra.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: `ลบข้อมูล ${productExtra.name} เรียบร้อย`,
            icon: 'success',
          }).then(() => {
            getProductExtraList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const submitImport = async (values: any) => {
    const { id: productId } = router.query;
    const existingExtraIds = rows.map((row: any) => row.extraId);
    const filteredExtraId = values.extraId
      .map((item: number) => item)
      .filter((extraId: number) => !existingExtraIds.includes(extraId));
    const data = {
      productId,
      extraId: filteredExtraId,
    };
    const res = await apiProductExtra.create(data);
    if (res && !res.isError) {
      getProductExtraList();
      setOpenDialog(false);
    }
  };

  const handleSelectExtra = (extraId: number) => {
    if (formik.values.extraId.includes(extraId)) {
      const removeExtraId = formik.values.extraId.filter(
        (item: number) => item !== extraId
      );
      formik.setFieldValue('extraId', removeExtraId);
    } else {
      const addExtraId = [...formik.values.extraId, extraId];
      formik.setFieldValue('extraId', addExtraId);
    }
  };
  return (
    <>
      <AttributeHeaderStyle>
        <h2>Extras</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog()}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <ExtraListStyle>
        {rows.map((item: any) => (
          <ExtraItemStyle key={item.id}>
            <div className="img-container">
              <div className="img-box">
                <Image
                  src={item.imageUrl || '/images/product/empty-product.svg'}
                  alt=""
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
            <div className="flex-1 flex-col">
              <div className="product-coating-detail">
                <div className="flex-1 font-bold">{item.name}</div>
                <div className="flex flex-row items-center">
                  <IconButton
                    color="error"
                    onClick={() => deleteModel(item)}
                    style={{ borderRadius: '8px' }}
                  >
                    <Trash2 />
                  </IconButton>
                </div>
              </div>
              <div className="px-4 py-2 flex flex-row gap-2 text-[0.8em] opacity-70">
                {item.description || '-'}
              </div>
            </div>
          </ExtraItemStyle>
        ))}
      </ExtraListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContentStyle>
          <div className="dialog-head">
            <h2>Import Extras</h2>
            <div className="w-[40px]">
              <IconButton onClick={() => setOpenDialog(false)}>
                <Close />
              </IconButton>
            </div>
          </div>
          <div className="dialog-body">
            <div className="step-1">
              <div className="flex flex-row items-center justify-between pb-2">
                <h3 className="m-0">Extras</h3>
                <Link className="text-[0.8em]" href="/company/setting/extra/">
                  ตั้งค่า
                </Link>
              </div>
              <form onSubmit={formik.handleSubmit}>
                <div className="extra-list">
                  {extraList.map((item: any, index: number) => (
                    <label className="extra-item" key={index}>
                      <div className="w-[30px]">
                        <Checkbox
                          name="extraId"
                          value={item.id}
                          checked={formik.values.extraId.includes(item.id)}
                          onChange={() => {
                            handleSelectExtra(item.id);
                          }}
                        />
                      </div>
                      <Avatar src={item.imageUrl}>{item.name[0]}</Avatar>
                      <div>
                        <div className="font-bold">{item.name}</div>
                        <div className="text-[0.8em] opacity-40">
                          {item.description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
                <div className="w-full">
                  <Button
                    type="submit"
                    fullWidth
                    color="dark"
                    variant="contained"
                    disabled={formik.values.extraId.length === 0}
                  >
                    Import
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </DialogContentStyle>
      </Dialog>
    </>
  );
};

export default ExtraForm;
