import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import {
  Avatar,
  Button,
  Checkbox,
  Dialog,
  IconButton,
  MenuItem,
  Select,
} from '@mui/material';
import { Add, Close, KeyboardBackspace } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Swal from 'sweetalert2';
import styled, { css } from 'styled-components';
import apiProductMaterial from '@/services/stock/productMaterial';
import apiProductSubMaterial from '@/services/stock/subMaterial';
import Link from 'next/link';
import apiProductSubMaterialDetail from '@/services/stock/subMaterialDetail';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiMaterial from '@/services/stock/material';

export const MaterialListStyle = styled.div`
  display: flex;
  flex-direction: column;
`;

export const MaterialItemStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  overflow: hidden;
  padding-right: 10px;
  .img-container {
    height: 80px;
    width: 80px;
    border-right: 1px solid #dbe2e5;
    padding: 12px;
    position: relative;
    .img-box {
      position: relative;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  svg {
    opacity: 0.5;
  }
`;
export const MaterialItemNewStyle = styled.div`
  display: flex;
  //flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  padding-right: 10px;
  .img-container {
    height: 50px;
    width: 50px;
    position: relative;
  }
  svg {
    opacity: 0.5;
  }
`;

export const DialogContentStyle = styled.div<{ $step: number }>`
  width: 500px;
  max-width: 100%;
  background: white;
  height: 500px;
  position: relative;
  .dialog-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 8px;
    height: 58px;
    width: 100%;
    border-bottom: 1px solid #dbe2e5;
    h2 {
      flex: 1;
      text-align: center;
      font-size: 1.2em;
    }
  }
  .dialog-body {
    padding: 20px;
    > div {
      opacity: 0;
      visibility: hidden;
      z-index: -1;
      display: none;
      transition: 0.3s;
      ${({ $step }) =>
        $step &&
        css`
          &:nth-of-type(${$step}) {
            display: block;
            position: static;
            opacity: 1;
            visibility: visible;
            z-index: 10;
          }
        `}
    }
    .submat-list {
      height: 290px;
      margin-top: 20px;
      overflow-y: auto;
      .submat-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        background: white;
        margin-bottom: 5px;
        gap: 10px;
        padding: 10px;
        font-size: 0.9em;
        font-weight: 300;
        border-radius: 8px;
        transition: 0.2s;
        color: #263238;
        &:hover {
          background: #f5f7f8;
          cursor: pointer;
        }
      }
    }
    .detail-list {
      height: 170px;
      overflow-y: auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      .detail-item {
        width: 100%;
        border-top: 1px solid #dbe2e5;
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          background: #f9f9f9;
        }
        &:last-child {
          border-bottom: 1px solid #dbe2e5;
        }
      }
    }
  }
`;

type BaseMaterialFormProps = {
  productData: any;
};
const BaseMaterialForm = ({ productData }: BaseMaterialFormProps) => {
  const router = useRouter();
  const [rows, setRows] = useState<any>([]);
  const [dialogStep, setDialogStep] = useState(1);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [materials, setMaterials] = useState([]);
  const [materialId, setMaterialId] = useState('');
  const [subMaterials, setSubMaterials] = useState([]);
  const [subMaterialSelected, setSubMaterialSelected] = useState<any>(null);
  const [subMaterialDetails, setSubmaterialDetails] = useState([]);
  const formik = useFormik({
    initialValues: {
      subMaterialDetailId: [],
    } as any,
    onSubmit: (values: any) => {
      submitImport({
        ...values,
        subMaterialDetailId: values.subMaterialDetailId.map((item: string) =>
          parseInt(item)
        ),
      });
    },
  });

  useEffect(() => {
    getProductMaterialList();
  }, []);

  const getProductMaterialList = async () => {
    const res = await apiProductMaterial.getList(productData.id);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const getMaterialList = async () => {
    const res = await apiMaterial.getList({
      page: 0,
      size: 100,
      materialType: 'BASE',
    });
    if (res && !res.isError) {
      setMaterials(res.data.content);
    }
  };

  const openDialog = async () => {
    setDialogStep(1);
    await getMaterialList();
    setOpenDialog(true);
  };

  const deleteModel = async (material: any) => {
    Swal.fire({
      title: 'ลบ Base Material',
      text: `ยืนยันการลบ ${material.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductMaterial.delete(material.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'ลบ Base Material เรียบร้อย',
            icon: 'success',
          }).then(() => {
            getProductMaterialList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const getSubMaterials = async (materialId: string) => {
    setMaterialId(materialId);
    setSubMaterialSelected([]);
    if (materialId) {
      const res = await apiProductSubMaterial.getList({
        page: 0,
        size: 100,
        materialId,
      });
      if (res && !res.isError) {
        setSubMaterials(res.data.content);
      }
    } else {
      setSubMaterials([]);
    }
  };

  const clickSubMaterial = async (subMaterial: any) => {
    const res = await apiProductSubMaterialDetail.getListBindRawMaterial({
      page: 0,
      size: 100,
      subMaterialId: subMaterial.id,
    });
    if (res && !res.isError) {
      setDialogStep(2);
      setSubMaterialSelected(subMaterial);
      setSubmaterialDetails(res.data);
      await formik.setFieldValue('subMaterialDetailId', []);
    }
  };

  const submitImport = async (values: any) => {
    const { id: productId } = router.query;
    const data = {
      productId,
      materialId,
      subMaterialId: subMaterialSelected.id,
      ...values,
    };
    const res = await apiProductMaterial.import(data);
    if (res && !res.isError) {
      setOpenDialog(false);
      getProductMaterialList();
    }
  };
  // console.log('rows', rows);
  // console.log('subMaterialDetails', subMaterialDetails);
  // console.log(formik.values);
  return (
    <>
      <AttributeHeaderStyle>
        <h2>Base Materials</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog()}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <MaterialListStyle>
        {rows.map((item: any) => (
          <MaterialItemStyle key={item.id}>
            <div className="img-container">
              <div className="img-box">
                <Image
                  src={item.imageUrl || '/images/product/empty-product.svg'}
                  alt=""
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
            <div className="flex-1 ml-[16px] flex flex-row gap-2 items-center">
              <span className="font-[600]">{item.name}</span>
            </div>
            <div className="flex flex-row items-center">
              <IconButton
                color="error"
                onClick={() => deleteModel(item)}
                style={{ borderRadius: '8px' }}
              >
                <Trash2 />
              </IconButton>
            </div>
          </MaterialItemStyle>
        ))}
      </MaterialListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContentStyle $step={dialogStep}>
          <div className="dialog-head">
            <div className="w-[40px]">
              {dialogStep === 2 && (
                <IconButton onClick={() => setDialogStep(1)}>
                  <KeyboardBackspace />
                </IconButton>
              )}
            </div>
            <h2>Import Material</h2>
            <div className="w-[40px]">
              <IconButton onClick={() => setOpenDialog(false)}>
                <Close />
              </IconButton>
            </div>
          </div>
          <div className="dialog-body">
            <div className="step-1">
              <div className="flex flex-row items-center justify-between pb-2">
                <h3 className="m-0">Base Materials</h3>
                <Link className="text-[0.8em]" href="/stock/setting/material/">
                  ตั้งค่า
                </Link>
              </div>
              <Select
                size="small"
                fullWidth
                displayEmpty
                value={materialId}
                onChange={(event: any) => getSubMaterials(event.target.value)}
              >
                <MenuItem value="">กรุณาเลือก Base Material</MenuItem>
                {materials.map((item: any, index: number) => (
                  <MenuItem key={index} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              <div className="submat-list">
                {subMaterials.map((item: any, index: number) => (
                  <div
                    className="submat-item"
                    key={index}
                    onClick={() => clickSubMaterial(item)}
                  >
                    <Avatar src={item.imageUrl}>{item.name[0]}</Avatar>
                    <div>{item.name}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="step-2">
              {subMaterialSelected && (
                <div className="flex flex-col items-center">
                  <Avatar
                    src={subMaterialSelected.imageUrl}
                    alt=""
                    sx={{ width: '100px', height: '100px' }}
                  ></Avatar>
                  <h2 className="text-[1.2em] mb-8">
                    {subMaterialSelected.name}
                  </h2>
                  <form onSubmit={formik.handleSubmit} className="w-full">
                    <div className="detail-list">
                      {subMaterialDetails.map((item: any, index: number) => (
                        <label className="detail-item" key={index}>
                          <Checkbox
                            name="subMaterialDetailId"
                            value={item.id}
                            onChange={formik.handleChange}
                            checked={
                              rows.some(
                                (rowItem: any) =>
                                  rowItem.subMaterialDetailId === item.id
                              ) ||
                              formik.values.subMaterialDetailId.includes(
                                `${item.id}`
                              )
                            }
                          />
                          <span>{item.name}</span>
                        </label>
                      ))}
                      {subMaterialDetails.length === 0 && (
                        <p className="opacity-50 text-center">* ไม่พบข้อมูล</p>
                      )}
                    </div>
                    <div className="w-full">
                      <Button
                        type="submit"
                        fullWidth
                        color="dark"
                        variant="contained"
                        disabled={
                          formik.values.subMaterialDetailId.length === 0
                        }
                      >
                        Import
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </DialogContentStyle>
      </Dialog>
    </>
  );
};

export default BaseMaterialForm;
