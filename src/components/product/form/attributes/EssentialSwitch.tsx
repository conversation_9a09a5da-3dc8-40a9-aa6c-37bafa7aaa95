import Image from 'next/image';

type EssentialSwitchProps = {
  isChecked: boolean;
  handleChange?: (isChecked: boolean) => void;
};
const EssentialSwitch = ({ isChecked, handleChange }: EssentialSwitchProps) => {
  const handleCheck = () => {
    if (handleChange) {
      handleChange(!isChecked);
    }
  };
  return (
    <>
      <div
        className="flex justify-center cursor-pointer"
        onClick={() => handleCheck()}
      >
        <Image
          src={
            isChecked ? '/icons/essential-on.png' : '/icons/essential-off.png'
          }
          alt=""
          quality={90}
          width={24}
          height={24}
        />
      </div>
    </>
  );
};

export default EssentialSwitch;
