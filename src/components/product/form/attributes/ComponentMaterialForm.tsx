import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import {
  Avatar,
  Button,
  Checkbox,
  Dialog,
  IconButton,
  MenuItem,
  Select,
} from '@mui/material';
import { Add, Close, KeyboardBackspace } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Swal from 'sweetalert2';
import apiProductSubMaterial from '@/services/stock/subMaterial';
import Link from 'next/link';
import apiProductSubMaterialDetail from '@/services/stock/subMaterialDetail';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiMaterial from '@/services/stock/material';
import {
  DialogContentStyle,
  MaterialItemStyle,
  MaterialListStyle,
} from '@/components/product/form/attributes/BaseMaterialForm';
import apiProductComponent from '@/services/stock/productComponent';

type ComponentMaterialFormProps = {
  productData: any;
};
const ComponentMaterialForm = ({ productData }: ComponentMaterialFormProps) => {
  const router = useRouter();
  const [rows, setRows] = useState<any>([]);
  const [dialogStep, setDialogStep] = useState(1);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [materials, setMaterials] = useState([]);
  const [materialId, setMaterialId] = useState('');
  const [subMaterials, setSubMaterials] = useState([]);
  const [subMaterialSelected, setSubMaterialSelected] = useState<any>(null);
  const [subMaterialDetails, setSubmaterialDetails] = useState([]);
  const formik = useFormik({
    initialValues: {
      subMaterialDetailId: [],
    } as any,
    onSubmit: (values: any) => {
      submitImport({
        ...values,
        subMaterialDetailId: values.subMaterialDetailId.map((item: string) =>
          parseInt(item)
        ),
      });
    },
  });

  useEffect(() => {
    getProductComponentList();
  }, []);

  const getProductComponentList = async () => {
    const res = await apiProductComponent.getList(productData.id, {
      page: 0,
      size: 300,
    });
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const getMaterialList = async () => {
    const res = await apiMaterial.getList({
      page: 0,
      size: 300,
      materialType: 'COMPONENT',
    });
    if (res && !res.isError) {
      setMaterials(res.data.content);
    }
  };

  const openDialog = async () => {
    setDialogStep(1);
    await getMaterialList();
    setOpenDialog(true);
  };

  const deleteModel = async (material: any) => {
    Swal.fire({
      title: 'ลบส่วนประกอบ',
      text: `ยืนยันการลบ ${material.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductComponent.delete(material.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'ลบส่วนประกอบเรียบร้อย',
            icon: 'success',
          }).then(() => {
            getProductComponentList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const getSubMaterials = async (materialId: string) => {
    setMaterialId(materialId);
    setSubMaterialSelected([]);
    if (materialId) {
      const res = await apiProductSubMaterial.getList({
        materialId,
      });
      if (res && !res.isError) {
        setSubMaterials(res.data.content);
      }
    } else {
      setSubMaterials([]);
    }
  };

  const clickSubMaterial = async (subMaterial: any) => {
    const res = await apiProductSubMaterialDetail.getListBindRawMaterial({
      page: 0,
      size: 100,
      subMaterialId: subMaterial.id,
    });
    if (res && !res.isError) {
      setDialogStep(2);
      setSubMaterialSelected(subMaterial);
      setSubmaterialDetails(res.data);
      await formik.setFieldValue('subMaterialDetailId', []);
    }
  };

  const submitImport = async (values: any) => {
    const { id: productId } = router.query;
    const data = {
      productId,
      materialId,
      subMaterialId: subMaterialSelected.id,
      ...values,
    };
    const res = await apiProductComponent.import(data);
    if (res && !res.isError) {
      setOpenDialog(false);
      getProductComponentList();
    }
  };

  return (
    <>
      <AttributeHeaderStyle>
        <h2>ส่วนประกอบ</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog()}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <MaterialListStyle>
        {rows.map((item: any) => (
          <MaterialItemStyle key={item.id}>
            <div className="img-container">
              <div className="img-box">
                <Image
                  src={item.imageUrl || '/images/product/empty-product.svg'}
                  alt=""
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
            <div className="flex-1 ml-[16px] flex flex-row gap-2 items-center">
              <span className="font-[600]">{item.name}</span>
            </div>
            <div className="flex flex-row items-center">
              <IconButton
                color="error"
                onClick={() => deleteModel(item)}
                style={{ borderRadius: '8px' }}
              >
                <Trash2 />
              </IconButton>
            </div>
          </MaterialItemStyle>
        ))}
      </MaterialListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContentStyle $step={dialogStep}>
          <div className="dialog-head">
            <div className="w-[40px]">
              {dialogStep === 2 && (
                <IconButton onClick={() => setDialogStep(1)}>
                  <KeyboardBackspace />
                </IconButton>
              )}
            </div>
            <h2>Import Material</h2>
            <div className="w-[40px]">
              <IconButton onClick={() => setOpenDialog(false)}>
                <Close />
              </IconButton>
            </div>
          </div>
          <div className="dialog-body">
            <div className="step-1">
              <div className="flex flex-row items-center justify-between pb-2">
                <h3 className="m-0">Component Materials</h3>
                <Link className="text-[0.8em]" href="/stock/setting/material/">
                  ตั้งค่า
                </Link>
              </div>
              <Select
                size="small"
                fullWidth
                displayEmpty
                value={materialId}
                onChange={(event: any) => getSubMaterials(event.target.value)}
              >
                <MenuItem value="">กรุณาเลือก Component Material</MenuItem>
                {materials.map((item: any, index: number) => (
                  <MenuItem key={index} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              <div className="submat-list">
                {subMaterials.map((item: any, index: number) => (
                  <div
                    className="submat-item"
                    key={index}
                    onClick={() => clickSubMaterial(item)}
                  >
                    <Avatar src={item.imageUrl}>{item.name[0]}</Avatar>
                    <div>{item.name}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="step-2">
              {subMaterialSelected && (
                <div className="flex flex-col items-center">
                  <Avatar
                    src={subMaterialSelected.imageUrl}
                    alt=""
                    sx={{ width: '100px', height: '100px' }}
                  ></Avatar>
                  <h2 className="text-[1.2em] mb-8">
                    {subMaterialSelected.name}
                  </h2>
                  <form onSubmit={formik.handleSubmit} className="w-full">
                    <div className="detail-list">
                      {subMaterialDetails.map((item: any, index: number) => (
                        <label className="detail-item" key={index}>
                          <Checkbox
                            name="subMaterialDetailId"
                            value={item.id}
                            onChange={formik.handleChange}
                            checked={
                              rows.some(
                                (rowItem: any) =>
                                  rowItem.subMaterialDetailId === item.id
                              ) ||
                              formik.values.subMaterialDetailId.includes(
                                `${item.id}`
                              )
                            }
                          />
                          <span>{item.name}</span>
                        </label>
                      ))}
                      {subMaterialDetails.length === 0 && (
                        <p className="opacity-50 text-center">* ไม่พบข้อมูล</p>
                      )}
                    </div>
                    <div className="w-full">
                      <Button
                        type="submit"
                        fullWidth
                        color="dark"
                        variant="contained"
                        disabled={
                          formik.values.subMaterialDetailId.length === 0
                        }
                      >
                        Import
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </DialogContentStyle>
      </Dialog>
    </>
  );
};

export default ComponentMaterialForm;
