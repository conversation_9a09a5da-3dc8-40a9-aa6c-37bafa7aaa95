import {
  AttributeDialogStyle,
  AttributeHeaderStyle,
  AttributeItemStyle,
  AttributeListStyle,
} from '@/styles/product-form.styled';
import { Button, Dialog, IconButton, TextField } from '@mui/material';
import { Add, Close, Edit } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import * as yup from 'yup';
import apiProductQuantity from '@/services/stock/productQuantity';
import { CustomIosSwitchStyle } from '@/styles/share.styled';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const SizeForm = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [rows, setRows] = useState<any>([]);
  const [formMode, setFormMode] = useState<string>('');
  const validationSchema = yup.object({
    quantity: yup.number().required('กรุณากรอกจำนวน'),
  });
  const formik = useFormik({
    initialValues: {
      id: 0,
      quantity: '',
      isActive: true,
    },
    validationSchema,
    onSubmit: (values: any) => {
      const { id: productId } = router.query;
      if (formMode === 'create') {
        create({
          ...values,
          productId,
        });
      } else {
        update(values.id, {
          ...values,
          productId,
        });
      }
    },
  });

  useEffect(() => {
    getList();
  }, []);

  const getList = async () => {
    const { id: productId } = router.query;
    const res = await apiProductQuantity.getList(`${productId}`);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const create = async (values: any) => {
    const res = await apiProductQuantity.create(values);
    if (res && !res.isError) {
      getList();
      dispatch(
        setSnackBar({
          status: true,
          text: 'สร้าง Quantity สำเร็จ',
          severity: 'success',
        })
      );
      setOpenDialog(false);
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถเพิ่มขนาดได้ กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };

  const update = async (itemId: number, values: any) => {
    const res = await apiProductQuantity.update({
      ...values,
      id: itemId,
    });
    if (res && !res.isError) {
      getList();
      dispatch(
        setSnackBar({
          status: true,
          text: 'อัปเดตข้อมูล Quantity สำเร็จ',
          severity: 'success',
        })
      );
      setOpenDialog(false);
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถอัปเดตขนาดได้ กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };

  const openDialog = async (mode: string, itemId: any) => {
    if (itemId) {
      const row = rows.find((item: any) => item.id === itemId);
      formik.setValues(row);
    } else {
      formik.resetForm();
    }
    setFormMode(mode);
    setOpenDialog(true);
  };

  const remove = async (quantity: any) => {
    Swal.fire({
      title: 'ลบจำนวน',
      text: `ยืนยันการลบ ${quantity.quantity} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductQuantity.delete(quantity.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'ลบจำนวนเรียบร้อยแล้ว',
            icon: 'success',
          }).then(() => {
            getList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถลบจำนวนได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };
  const updateStatus = async (value: any, check: boolean) => {
    const res = await apiProductQuantity.update({
      ...value,
      isActive: check,
    });
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'อัปเดตสถานะสำเร็จ',
          severity: 'success',
        })
      );
      getList();
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };
  return (
    <>
      <AttributeHeaderStyle>
        <h2>Quantity</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog('create', null)}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <AttributeListStyle>
        {rows.map((item: any) => (
          <AttributeItemStyle key={item.id}>
            <div className="flex-1 ml-[10px]">
              <b>{item.quantity}</b>
            </div>
            <div className="flex flex-row items-center">
              <div className="mr-2">
                <CustomIosSwitchStyle>
                  <IOSSwitch
                    checked={item.isActive}
                    onClick={(e: any) => {
                      updateStatus(item, e.target.checked);
                    }}
                  />
                </CustomIosSwitchStyle>
              </div>
              <IconButton
                style={{ borderRadius: '8px' }}
                onClick={() => openDialog('edit', item.id)}
              >
                <Edit />
              </IconButton>
              <IconButton
                style={{ borderRadius: '8px' }}
                color="error"
                onClick={() => remove(item)}
              >
                <Trash2 />
              </IconButton>
            </div>
          </AttributeItemStyle>
        ))}
      </AttributeListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <AttributeDialogStyle>
          <form onSubmit={formik.handleSubmit}>
            <div className="heading-popup">
              <h2>{formMode === 'create' ? 'Create' : 'Edit'} Quantity</h2>
              <div className="close-btn">
                <IconButton onClick={() => setOpenDialog(false)}>
                  <Close />
                </IconButton>
              </div>
            </div>
            <div className="form-container">
              <p className="mb-2">จำนวน</p>
              <TextField
                name="quantity"
                placeholder="quantity"
                value={formik.values.quantity}
                onChange={(e: any) => {
                  if (Number(e.target.value) < 0) {
                    e.preventDefault();
                  } else {
                    formik.handleChange(e);
                  }
                }}
                onKeyDown={(e: any) => {
                  if (e.key === '-') {
                    e.preventDefault();
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                }}
                error={
                  formik.touched.quantity && Boolean(formik.errors.quantity)
                }
                helperText={formik.touched.quantity && formik.errors.quantity}
              />
              <div className="mt-8">
                <Button
                  type="submit"
                  color="dark"
                  variant="contained"
                  fullWidth
                >
                  {formMode === 'create' ? 'Create' : 'Edit'}
                </Button>
              </div>
            </div>
          </form>
        </AttributeDialogStyle>
      </Dialog>
    </>
  );
};

export default SizeForm;
