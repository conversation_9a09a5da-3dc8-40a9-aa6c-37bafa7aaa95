import {
  AttributeDialogStyle,
  AttributeHeaderStyle,
  AttributeItemStyle,
  AttributeListStyle,
} from '@/styles/product-form.styled';
import {
  Button,
  Dialog,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Add, Close, Edit } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import Swal from 'sweetalert2';
import apiProductSize from '@/services/stock/productSize';
import * as yup from 'yup';
import { CustomIosSwitchStyle } from '@/styles/share.styled';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

type SizeFormProps = {
  productData: any;
};
const SizeForm = ({ productData }: SizeFormProps) => {
  const dispatch = useAppDispatch();
  const { productSize, unitSizeName } = productData;
  const router = useRouter();
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [rows, setRows] = useState<any>([]);
  const [formMode, setFormMode] = useState<string>('');
  const validationSchema = yup.object({
    name: yup.string().required(),
    length: yup
      .number()
      .required()
      .min(productSize.minLength)
      .max(productSize.maxLength),
    height: yup
      .number()
      .required()
      .min(productSize.minHeight)
      .max(productSize.maxHeight),
    width: yup
      .number()
      .required()
      .min(productSize.minWidth)
      .max(productSize.maxWidth)
      .test(
        'width-length-validation',
        'Width ต้องไม่มากกว่า Length',
        function (value) {
          const { length } = this.parent;
          return !value || !length || value <= length;
        }
      ),
  });
  const formik = useFormik({
    initialValues: {
      id: 0,
      name: '',
      length: '',
      height: '',
      width: '',
      isActive: true,
    },
    validationSchema,
    onSubmit: (values: any) => {
      const { id: productId } = router.query;
      if (formMode === 'create') {
        create({
          ...values,
          productId,
        });
      } else {
        update(values.id, {
          ...values,
          productId,
        });
      }
    },
  });

  useEffect(() => {
    getList();
  }, []);

  const getList = async () => {
    const { id: productId } = router.query;
    const res = await apiProductSize.getList(`${productId}`);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const create = async (values: any) => {
    const res = await apiProductSize.create(values);
    if (res && !res.isError) {
      getList();
      dispatch(
        setSnackBar({
          status: true,
          text: 'สร้าง Essential Sizes สำเร็จ',
          severity: 'success',
        })
      );
      setOpenDialog(false);
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถเพิ่มขนาดได้ กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };

  const update = async (itemId: number, values: any) => {
    const res = await apiProductSize.update({
      ...values,
      id: itemId,
    });
    if (res && !res.isError) {
      getList();
      dispatch(
        setSnackBar({
          status: true,
          text: 'อัปเดตข้อมูล Essential Sizes สำเร็จ',
          severity: 'success',
        })
      );
      setOpenDialog(false);
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่สามารถอัปเดตขนาดได้ กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };

  const openDialog = async (mode: string, itemId: any) => {
    if (itemId) {
      const row = rows.find((item: any) => item.id === itemId);
      formik.setValues(row);
    } else {
      formik.resetForm();
    }
    setFormMode(mode);
    setOpenDialog(true);
  };

  const remove = async (size: any) => {
    Swal.fire({
      title: 'ลบขนาด',
      text: `ยืนยันการลบ ${size.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductSize.delete(size.id);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: 'ลบขนาดเรียบร้อยแล้ว',
            icon: 'success',
          }).then(() => {
            getList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถลบขนาดได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };
  const updateStatus = async (value: any, check: boolean) => {
    const res = await apiProductSize.update({
      ...value,
      isActive: check,
    });
    if (res && !res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'อัปเดตสถานะสำเร็จ',
          severity: 'success',
        })
      );
      getList();
    } else {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'กรุณาลองใหม่ภายหลัง',
        icon: 'error',
      });
    }
  };
  return (
    <>
      <AttributeHeaderStyle>
        <h2>Essential Sizes</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog('create', null)}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <AttributeListStyle>
        {rows.map((item: any) => (
          <AttributeItemStyle key={item.id}>
            <div className="flex-1 ml-[10px]">
              <b className="mr-[8px]">{item.name}</b>{' '}
              <span className="text-[0.9em]">
                {item.length}x{item.width}x{item.height} {unitSizeName}
              </span>
            </div>
            <div className="flex flex-row items-center">
              <div className="mr-2">
                <CustomIosSwitchStyle>
                  <IOSSwitch
                    checked={item.isActive}
                    onClick={(e: any) => {
                      updateStatus(item, e.target.checked);
                    }}
                  />
                </CustomIosSwitchStyle>
              </div>
              <IconButton
                style={{ borderRadius: '8px' }}
                onClick={() => openDialog('edit', item.id)}
              >
                <Edit />
              </IconButton>
              <IconButton
                style={{ borderRadius: '8px' }}
                color="error"
                onClick={() => remove(item)}
              >
                <Trash2 />
              </IconButton>
            </div>
          </AttributeItemStyle>
        ))}
      </AttributeListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <AttributeDialogStyle>
          <form onSubmit={formik.handleSubmit}>
            <div className="heading-popup">
              <h2>{formMode === 'create' ? 'Create' : 'Edit'} Size</h2>
              <div className="close-btn">
                <IconButton onClick={() => setOpenDialog(false)}>
                  <Close />
                </IconButton>
              </div>
            </div>
            <div className="form-container">
              <p className="mb-2">Title</p>
              <TextField
                name="name"
                placeholder="Name Size"
                value={formik.values.name}
                onChange={formik.handleChange}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />
              <p className="mb-2">Length</p>
              <TextField
                name="length"
                value={formik.values.length}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <span>{unitSizeName}</span>
                    </InputAdornment>
                  ),
                }}
                error={formik.touched.length && Boolean(formik.errors.length)}
                helperText={formik.touched.length && formik.errors.length}
              />

              <p className="mb-2">Width</p>
              <TextField
                name="width"
                value={formik.values.width}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <span>{unitSizeName}</span>
                    </InputAdornment>
                  ),
                }}
                error={formik.touched.width && Boolean(formik.errors.width)}
                helperText={formik.touched.width && formik.errors.width}
              />

              <p className="mb-2">Height</p>
              <TextField
                name="height"
                value={formik.values.height}
                onChange={formik.handleChange}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <span>{unitSizeName}</span>
                    </InputAdornment>
                  ),
                }}
                error={formik.touched.height && Boolean(formik.errors.height)}
                helperText={formik.touched.height && formik.errors.height}
              />
              <div className="mt-8">
                <Button
                  type="submit"
                  color="dark"
                  variant="contained"
                  fullWidth
                >
                  {formMode === 'create' ? 'Create' : 'Edit'}
                </Button>
              </div>
            </div>
          </form>
        </AttributeDialogStyle>
      </Dialog>
    </>
  );
};

export default SizeForm;
