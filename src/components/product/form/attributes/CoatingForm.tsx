import { AttributeHeaderStyle } from '@/styles/product-form.styled';
import {
  Avatar,
  Button,
  Checkbox,
  Chip,
  Dialog,
  IconButton,
} from '@mui/material';
import { Add, Close, KeyboardBackspace } from '@mui/icons-material';
import { Trash2 } from 'react-feather';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Swal from 'sweetalert2';
import styled, { css } from 'styled-components';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import apiCoating from '@/services/stock/coating';
import apiProductCoating from '@/services/stock/productCoating';

export const CoatingListStyle = styled.div`
  display: flex;
  flex-direction: column;
`;

export const CoatingItemStyle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
  overflow: hidden;
  .img-container {
    height: 80px;
    width: 80px;
    min-width: 80px;
    border-right: 1px solid #dbe2e5;
    padding: 12px;
    position: relative;
    .img-box {
      position: relative;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  svg {
    opacity: 0.5;
  }
  .product-coating-detail {
    flex: 1;
    padding-left: 16px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding-right: 10px;
  }
`;

const DialogContentStyle = styled.div<{ $step: number }>`
  width: 500px;
  max-width: 100%;
  background: white;
  height: 500px;
  position: relative;
  .dialog-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 8px;
    height: 58px;
    width: 100%;
    border-bottom: 1px solid #dbe2e5;
    h2 {
      flex: 1;
      text-align: center;
      font-size: 1.2em;
    }
  }
  .dialog-body {
    padding: 20px;
    > div {
      opacity: 0;
      visibility: hidden;
      z-index: -1;
      display: none;
      transition: 0.3s;
      ${({ $step }) =>
        $step &&
        css`
          &:nth-of-type(${$step}) {
            display: block;
            position: static;
            opacity: 1;
            visibility: visible;
            z-index: 10;
          }
        `}
    }
    .coating-list {
      height: 290px;
      margin-top: 20px;
      overflow-y: auto;
      .coating-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        background: white;
        margin-bottom: 5px;
        gap: 10px;
        padding: 10px;
        font-size: 0.9em;
        font-weight: 300;
        border-radius: 8px;
        transition: 0.2s;
        color: #263238;
        &:hover {
          background: #f5f7f8;
          cursor: pointer;
        }
      }
    }
    .detail-list {
      height: 170px;
      overflow-y: auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      .detail-item {
        width: 100%;
        border-top: 1px solid #dbe2e5;
        cursor: pointer;
        transition: 0.3s;
        &:hover {
          background: #f9f9f9;
        }
        &:last-child {
          border-bottom: 1px solid #dbe2e5;
        }
      }
    }
  }
`;

type MaterialFormProps = {
  productData: any;
};
const CoatingForm = ({ productData }: MaterialFormProps) => {
  const router = useRouter();
  const [rows, setRows] = useState<any>([]);
  const [dialogStep, setDialogStep] = useState(1);
  const [isOpenDialog, setOpenDialog] = useState(false);
  const [coatingList, setCoatingList] = useState([]);
  const [coatingSelected, setCoatingSelected] = useState<any>(null);
  const formik = useFormik({
    initialValues: {
      finishId: [],
    } as any,
    onSubmit: (values: any) => {
      submitImport({
        ...values,
        finishId: values.finishId.map((item: string) => parseInt(item)),
      });
    },
  });

  useEffect(() => {
    getProductCoatingList();
  }, []);

  const getProductCoatingList = async () => {
    const res = await apiProductCoating.getList(productData.id);
    if (res && !res.isError) {
      setRows(res.data);
    }
  };

  const getCoatingList = async () => {
    const res = await apiCoating.getListForProduct({
      page: 0,
      size: 100,
    });
    if (res && !res.isError) {
      setCoatingList(res.data.content);
    }
  };

  const openDialog = async () => {
    setDialogStep(1);
    await getCoatingList();
    setOpenDialog(true);
  };

  const deleteModel = async (coating: any, productCoatingId: number) => {
    Swal.fire({
      title: 'ลบ Coating',
      text: `ยืนยันการลบ ${coating.name} ?`,
      icon: 'warning',
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiProductCoating.delete(productCoatingId);
        if (res && !res.isError) {
          Swal.fire({
            title: 'ลบเรียบร้อย',
            text: `ลบข้อมูล ${coating.name} เรียบร้อย`,
            icon: 'success',
          }).then(() => {
            getProductCoatingList();
          });
        } else {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: 'ไม่สามารถ ลบได้ กรุณาลองใหม่ภายหลัง',
            icon: 'error',
          });
        }
      }
    });
  };

  const clickCoating = async (coating: any) => {
    setCoatingSelected(coating);
    setDialogStep(2);
    await formik.setFieldValue('finishId', []);
  };

  const submitImport = async (values: any) => {
    const { id: productId } = router.query;
    const data = {
      productId,
      coatingId: coatingSelected.id,
      ...values,
    };
    const res = await apiProductCoating.create(data);
    if (res && !res.isError) {
      getProductCoatingList();
      setOpenDialog(false);
    }
  };

  return (
    <>
      <AttributeHeaderStyle>
        <h2>Coatings</h2>
        <div>
          <IconButton
            style={{ background: '#222', color: 'white' }}
            onClick={() => openDialog()}
          >
            <Add />
          </IconButton>
        </div>
      </AttributeHeaderStyle>
      <CoatingListStyle>
        {rows.map((item: any) => (
          <CoatingItemStyle key={item.id}>
            <div className="img-container">
              <div className="img-box">
                <Image
                  src={
                    item.coating.imageUrl || '/images/product/empty-product.svg'
                  }
                  alt=""
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
            <div className="flex-1 flex-col overflow-auto">
              <div className="product-coating-detail">
                <span className="font-[600] flex-1">{item.coating.name}</span>
                <div className="flex flex-row items-center">
                  <IconButton
                    color="error"
                    onClick={() => deleteModel(item.coating, item.id)}
                    style={{ borderRadius: '8px' }}
                  >
                    <Trash2 />
                  </IconButton>
                </div>
              </div>
              <div className="px-4 py-2 flex flex-row gap-2 overflow-auto">
                {item.finishes.map((item: any, index: number) => (
                  <Chip label={item.name} key={index} size="small" />
                ))}
              </div>
            </div>
          </CoatingItemStyle>
        ))}
      </CoatingListStyle>
      <Dialog open={isOpenDialog} onClose={() => setOpenDialog(false)}>
        <DialogContentStyle $step={dialogStep}>
          <div className="dialog-head">
            <div className="w-[40px]">
              {dialogStep === 2 && (
                <IconButton onClick={() => setDialogStep(1)}>
                  <KeyboardBackspace />
                </IconButton>
              )}
            </div>
            <h2>Import Coating</h2>
            <div className="w-[40px]">
              <IconButton onClick={() => setOpenDialog(false)}>
                <Close />
              </IconButton>
            </div>
          </div>
          <div className="dialog-body">
            <div className="step-1">
              <div className="flex flex-row items-center justify-between pb-2">
                <h3 className="m-0">Coatings</h3>
                <Link className="text-[0.8em]" href="/company/setting/coating/">
                  ตั้งค่า
                </Link>
              </div>
              <div className="coating-list">
                {coatingList.map((item: any, index: number) => (
                  <div
                    className="coating-item"
                    key={index}
                    onClick={() => clickCoating(item)}
                  >
                    <Avatar src={item.imageUrl}>{item.name[0]}</Avatar>
                    <div>{item.name}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="step-2">
              {coatingSelected && (
                <div className="flex flex-col items-center">
                  <Avatar
                    src={coatingSelected.imageUrl}
                    alt=""
                    sx={{ width: '100px', height: '100px' }}
                  ></Avatar>
                  <h2 className="text-[1.2em] mb-8">{coatingSelected.name}</h2>
                  <form onSubmit={formik.handleSubmit} className="w-full">
                    <div className="detail-list">
                      {coatingSelected.finish.map(
                        (item: any, index: number) => (
                          <label className="detail-item" key={index}>
                            <Checkbox
                              name="finishId"
                              value={item.id}
                              onChange={formik.handleChange}
                              checked={formik.values.finishId.includes(
                                `${item.id}`
                              )}
                            />{' '}
                            <span>{item.name}</span>
                          </label>
                        )
                      )}
                      {coatingSelected.finish.length === 0 && (
                        <p className="opacity-50 text-center">* ไม่พบข้อมูล</p>
                      )}
                    </div>
                    <div className="w-full">
                      <Button
                        type="submit"
                        fullWidth
                        color="dark"
                        variant="contained"
                        disabled={formik.values.finishId.length === 0}
                      >
                        Import
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </DialogContentStyle>
      </Dialog>
    </>
  );
};

export default CoatingForm;
