import TreeView, { flattenTree } from 'react-accessible-treeview';
import styled from '@emotion/styled';
import {
  AddBox,
  CheckBox,
  CheckBoxOutlineBlank,
  IndeterminateCheckBox,
} from '@mui/icons-material';
import { useEffect, useState } from 'react';
import _ from 'lodash';

export default function ConfigTreeView({ treeList, onSelected, expands }: any) {
  const [options, setOptions] = useState<any>();
  const [selectedIds, setSelectedIds] = useState<any[]>([]);
  useEffect(() => {
    setFormatForOptions();
  }, [treeList]);

  const setFormatForOptions = async () => {
    if (treeList) {
      setSelectedIds([]);
      setOptions({ name: '', children: treeList });
    }
  };

  const [data, setData] = useState<any>(
    flattenTree({
      name: '',
      children: [],
    })
  );

  useEffect(() => {
    setData(
      flattenTree(
        options || {
          id: '',
          name: '',
          children: [],
        }
      )
    );
    getCollapse();
  }, [options]);

  const onSetSelected = (data: any) => {
    if (data) {
      onSelected(data);
    }
  };

  const onSetSelectedIds = (data: any) => {
    const isCheck = _.some(selectedIds, (o: any) => o === data.id);
    if (!isCheck) {
      setSelectedIds((prev: any) => [...prev, data.id]);
    } else {
      const filter = _.filter(selectedIds, (o: any) => o !== data.id);
      setSelectedIds(filter);
    }
  };

  const [expandeds, setExpandeds] = useState<string[]>([]);
  const [manualExpandeds, setManualExpandeds] = useState<any[]>([]);

  const getCollapse = () => {
    const merge = [...expandeds, ...expands];
    const mergeManual = [...merge, ...manualExpandeds];
    const mergeUniq = _.uniq(mergeManual);
    setExpandeds(mergeUniq);
  };

  return (
    <TreeViewStyle>
      <div className="checkbox">
        <TreeView
          data={data}
          aria-label="Checkbox tree"
          multiSelect
          propagateSelect
          propagateSelectUpwards
          togglableSelect
          defaultSelectedIds={[]}
          onSelect={(props) => {
            const split = props.element.id.toString().split('-');
            const code = split[0];
            if (code === 'sd') {
              onSetSelected(props.element);
              onSetSelectedIds(props.element);
            }
          }}
          onExpand={(props) => {
            const expands = Array.from(props.treeState.expandedIds);
            // console.log('expands', type, expands);
            setManualExpandeds(expands);
            // const expandId = props.element.id;
            // const isExpand = _.some(expandeds, (o: any) => o === expandId);
            // if (!isExpand) {
            //   setExpandeds((prevState: any) => [...prevState, expandId]);
            // } else {
            //   const without = _.filter(expandeds, (o: any) => o !== expandId);
            //   setExpandeds(without);
            // }
          }}
          expandedIds={expandeds}
          nodeRenderer={({
            element,
            // isBranch,
            isExpanded,
            // isSelected,
            isHalfSelected,
            getNodeProps,
            level,
            handleSelect,
            handleExpand,
          }) => {
            // const amount: any = element?.metadata?.amount || 0;
            const isParent: any = level === 1 || level === 2;
            const isCheck = _.some(selectedIds, (o: any) => o === element.id);
            return (
              <div
                {...getNodeProps({ onClick: handleExpand })}
                style={{
                  marginLeft: 25 * (level - 1),
                  marginBottom: level === 3 ? '5px' : '1px',
                  display: 'flex',
                  columnGap: '8px',
                  paddingLeft: '4px',
                  cursor: isParent && 'pointer',
                  backgroundColor: isCheck
                    ? '#F2FEFE'
                    : level === 1
                    ? '#f1f1f1'
                    : level === 2
                    ? '#f1f1f1'
                    : '#fff',
                  borderRadius: '4px',
                  // opacity: isParent && amount === 0 ? 0.4 : 1,
                }}
              >
                {isParent && <ArrowIcon isOpen={isExpanded} />}
                {level === 3 && (
                  <CheckBoxIcon
                    className="checkbox-icon"
                    onClick={(e: any) => {
                      handleSelect(e);
                      e.stopPropagation();
                    }}
                    variant={isHalfSelected ? 'some' : isCheck ? 'all' : 'none'}
                  />
                )}
                <span
                  className={`name ${
                    level === 3 && isCheck && 'text-[#00C1AF]'
                  } text-[15px] ${isParent && 'font-[500]'}`}
                >{`${element.name}`}</span>
              </div>
            );
          }}
        />
      </div>
    </TreeViewStyle>
  );
}

const ArrowIcon = ({ isOpen, className }: any) => {
  // const baseClass = 'arrow';
  // const classes = cx(
  //   baseClass,
  //   { [`${baseClass}--closed`]: !isOpen },
  //   { [`${baseClass}--open`]: isOpen },
  //   className
  // );
  // <ArrowDropDownCircle className={`${classes} w-[18px] mr-[8px]`} />
  return isOpen ? (
    <IndeterminateCheckBox className={`${className}`} />
  ) : (
    <AddBox className={`${className}`} />
  );
};

const CheckBoxIcon = ({ variant, ...rest }: any) => {
  switch (variant) {
    case 'all':
      return (
        <CheckBox {...rest} className={'text-[#00c1af] ml-[5px] mr-[5px]'} />
      );
    case 'none':
      return <CheckBoxOutlineBlank {...rest} />;
    case 'some':
      return <IndeterminateCheckBox {...rest} />;
    default:
      return null;
  }
};

const TreeViewStyle = styled.div`
  .checkbox {
    font-size: 16px;
    user-select: none;
  }

  .checkbox .tree,
  .checkbox .tree-node,
  .checkbox .tree-node-group {
    list-style: none;
    margin: 0;
    padding: 3px 0;
  }

  .checkbox .tree-branch-wrapper,
  .checkbox .tree-node__leaf {
    outline: none;
  }

  .checkbox .tree-node {
    //cursor: pointer;
  }

  .checkbox .tree-node .name:hover {
    //color: rgba(0, 0, 0, 0.75);
  }

  .checkbox .tree-node--focused .name {
    //background: rgba(0, 0, 0, 0.2);
  }

  .checkbox .tree-node {
    display: inline-block;
  }

  .checkbox .checkbox-icon {
    cursor: pointer;
    margin: 0 5px;
    vertical-align: middle;
  }

  .checkbox button {
    border: none;
    background: transparent;
    cursor: pointer;
  }

  .checkbox .arrow {
    margin-left: 5px;
    vertical-align: middle;
  }

  .checkbox .arrow--open {
    transform: rotate(90deg);
  }
`;
