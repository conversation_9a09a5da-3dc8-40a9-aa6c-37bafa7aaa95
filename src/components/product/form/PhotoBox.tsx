import styled from 'styled-components';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Add } from '@mui/icons-material';
import { IconButton } from '@mui/material';
import { Trash2 } from 'react-feather';

const PhotoBoxStyle = styled.div`
  height: 132px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7f8;
  overflow: hidden;
  border-radius: 12px;
  .image-preview {
    position: relative;
    height: 100%;
    width: 100%;
    img {
      object-fit: cover !important;
    }
    .del-btn {
      position: absolute;
      top: 4px;
      right: 4px;
    }
  }
  .upload-image {
    height: 100%;
    width: 100%;
    border-radius: 12px;
    border: 2px dashed #dbe2e5;
    cursor: pointer;
    .add-product {
      height: 100%;
      width: 100%;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      input {
        display: none;
      }
      > div {
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        display: flex;
        justify-content: center;
        align-items: center;
        > svg {
          width: 40px;
          height: 40px;
        }
      }
    }
  }
`;

type PhotoBoxType = {
  imageUrl?: string;
  handleChangeFile: (file: any) => void;
  handleDelete: (imageUrl: string) => void;
};
const PhotoBox = ({
  imageUrl,
  handleChangeFile,
  handleDelete,
}: PhotoBoxType) => {
  const [imagePreview, setImagePreview] = useState<any>(null);
  const [imageFile, setImageFile] = useState(null);

  useEffect(() => {
    if (imageUrl) {
      setImagePreview(imageUrl);
    } else {
      setImagePreview(null);
    }
  }, [imageUrl]);

  useEffect(() => {
    handleChangeFile(imageFile);
  }, [imageFile]);

  const changeImage = (event: any) => {
    const url = URL.createObjectURL(event.target.files[0]);
    setImageFile(event.target.files[0]);
    setImagePreview(url);
  };
  return (
    <PhotoBoxStyle>
      {imagePreview && (
        <div className="image-preview">
          <Image
            src={imagePreview}
            alt="Thumb"
            fill
            style={{ objectFit: 'contain' }}
          />
          <div className="del-btn">
            <IconButton
              onClick={() => handleDelete(imagePreview)}
              style={{
                background: '#D32F2F',
                color: 'white',
                borderRadius: '8px',
              }}
            >
              <Trash2 />
            </IconButton>
          </div>
        </div>
      )}
      {!imagePreview && (
        <div className="upload-image">
          <label className="add-product">
            <div>
              <Add />
            </div>
            <input type="file" accept="image/*" onChange={changeImage} />
          </label>
        </div>
      )}
    </PhotoBoxStyle>
  );
};
export default PhotoBox;
