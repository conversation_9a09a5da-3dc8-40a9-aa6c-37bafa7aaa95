import {
  <PERSON>ton,
  CircularProgress,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { getIn, useFormik } from 'formik';
import * as yup from 'yup';
import styled from 'styled-components';
import React, { useEffect, useState } from 'react';
import apiProductCategory from '@/services/stock/productCategory';
import apiProductType from '@/services/stock/productType';
import { ProductFormType } from '@/components/product/form/types/information';
import { isNull } from 'lodash';
import ImageField from '@/components/ImageField';
import { useRouter } from 'next/router';

const SizeBGStyle = styled.div`
  padding: 20px;
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  gap: 15px;
  background: #f5f7f8;
  .MuiFormControl-root {
    background: white;
  }
  .MuiFormHelperText-root {
    background: #f5f7f8 !important;
    margin: 0 !important;
    padding: 4px 0 0 14px !important;
  }
`;

type ProductFormProps = {
  initialValues?: ProductFormType;
  handleSubmit: (values: any, imageFile: File) => void;
  submitting: boolean;
};

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อสินค้า'),
  // description: yup.string().required('กรุณากรอกคำอธิบายสินค้า'),
  productCategoryId: yup.string().required('กรุณาเลือก Category'),
  productTypeId: yup.string().required('กรุณาเลือก Type'),
  productSize: yup.object().shape({
    maxHeight: yup
      .number()
      .required('กรุณากรอกค่า Max Height')
      .typeError('กรุณากรอกค่า Max Height')
      .test('minHeightCheck', 'Max Height > Min Height', function (value) {
        return value >= this.parent.minHeight;
      }),
    maxLength: yup
      .number()
      .required('กรุณากรอกค่า Max Length')
      .typeError('กรุณากรอกค่า Max Length')
      .test('minLengthCheck', 'Max Length > Min Length', function (value) {
        return value >= this.parent.minLength;
      }),
    maxWidth: yup
      .number()
      .required('กรุณากรอกค่า Max Width')
      .typeError('กรุณากรอกค่า Max Width')
      .test('minWidthCheck', 'Max Width > Min Width', function (value) {
        return value >= this.parent.minWidth;
      })
      .test(
        'widthLengthCheck',
        'Width ต้องไม่มากกว่า Length',
        function (value) {
          return (
            !value || !this.parent.maxLength || value <= this.parent.maxLength
          );
        }
      ),
    minHeight: yup
      .number()
      .required('กรุณากรอกค่า Min Height')
      .typeError('กรุณากรอกค่า Min Height'),
    minLength: yup
      .number()
      .required('กรุณากรอกค่า Min Length')
      .typeError('กรุณากรอกค่า Min Length'),
    minWidth: yup
      .number()
      .required('กรุณากรอกค่า Min Width')
      .typeError('กรุณากรอกค่า Min Width')
      .test(
        'widthLengthCheck',
        'Width ต้องไม่มากกว่า Length',
        function (value) {
          return (
            !value || !this.parent.minLength || value <= this.parent.minLength
          );
        }
      ),
  }),
});

const InformationForm = ({
  initialValues,
  handleSubmit,
  submitting,
}: ProductFormProps) => {
  const router = useRouter();
  const { id } = router.query;
  const [categoryList, setCategoryList] = useState<any[]>([]);
  const [typeList, setTypeList] = useState<any[]>([]);
  const [imageFile, setImageFile] = useState<any>({});
  const displayUnitList = ['mm', 'cm', 'in'];
  const formik = useFormik({
    initialValues: initialValues || {
      name: '',
      description: '',
      productCategoryId: '',
      productTypeId: '',
      productSize: {
        maxHeight: '',
        maxLength: '',
        maxWidth: '',
        minHeight: '',
        minLength: '',
        minWidth: '',
      },
      displayUnit: 'mm',
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values, imageFile);
    },
  });
  const getCategoryList = async () => {
    const res = await apiProductCategory.getList();
    if (res && !res.isError) {
      setCategoryList(res.data.content);
    }
  };

  const getProductType = async () => {
    if (formik.values.productCategoryId) {
      const res = await apiProductType.getListByCategoryId(
        `${formik.values.productCategoryId}`
      );
      if (res && !res.isError) {
        setTypeList(res.data);
      }
    }
  };

  useEffect(() => {
    getCategoryList().then();
  }, []);
  useEffect(() => {
    getProductType().then();
  }, [formik.values.productCategoryId]);

  const convertToDisplayUnit = (value: any) => {
    const convertValue = parseFloat(value);
    switch (formik.values.displayUnit) {
      case 'cm':
        return parseFloat((convertValue / 10).toFixed(2));
      case 'in':
        return parseFloat((convertValue / 25.4).toFixed(2));
      default:
        return parseFloat(convertValue.toFixed(2));
    }
  };

  const convertToMM = (value: any) => {
    const convertValue = parseFloat(value);
    switch (formik.values.displayUnit) {
      case 'cm':
        return parseFloat((convertValue * 10).toFixed(2));
      case 'in':
        return parseFloat((convertValue * 25.4).toFixed(2));
      default:
        return parseFloat(convertValue.toFixed(2));
    }
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <ImageField
        handleChange={async (files: any) => {
          if (!isNull(files)) {
            setImageFile(files[0]);
          }
        }}
        maxSizeInMB={2}
        ignoreQuality={false}
        isCompress={true}
        objectFit="cover"
        defaultBackground={initialValues?.imageUrl ?? '/images/add-image.svg'}
        borderRadius="14px"
        textUploadBtn={'อัปโหลด'}
      />
      <p className="field-title">ชื่อสินค้า</p>
      <TextField
        type="text"
        name="name"
        value={formik.values.name}
        onChange={formik.handleChange}
        placeholder="Product Name"
        error={formik.touched.name && Boolean(formik.errors.name)}
        helperText={formik.touched.name && formik.errors.name}
      />
      <p className="field-title">หมวดหมู่สินค้า</p>
      <FormControl
        fullWidth
        size="small"
        error={Boolean(
          getIn(formik.touched, 'productCategoryId') &&
            getIn(formik.errors, 'productCategoryId')
        )}
      >
        <Select
          name="productCategoryId"
          value={formik.values.productCategoryId}
          onChange={(e: any) => {
            formik.handleChange(e);
            formik.setFieldValue('productTypeId', '');
          }}
          displayEmpty
        >
          <MenuItem disabled value="">
            <div className="text-[#78909C]">กรุณาเลือก</div>
          </MenuItem>
          {categoryList.map((item: any, index: any) => (
            <MenuItem key={index} value={item.id}>
              {item.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {formik.touched.productCategoryId && formik.errors.productCategoryId && (
        <FormHelperText error>{formik.errors.productCategoryId}</FormHelperText>
      )}
      <p className="field-title">ประเภทสินค้า</p>
      <FormControl
        fullWidth
        size="small"
        error={Boolean(
          getIn(formik.touched, 'productTypeId') &&
            getIn(formik.errors, 'productTypeId')
        )}
      >
        <Select
          name="productTypeId"
          value={formik.values.productTypeId}
          onChange={formik.handleChange}
          displayEmpty
        >
          <MenuItem disabled value="">
            <div className="text-[#78909C]">กรุณาเลือก</div>
          </MenuItem>
          {typeList.map((item: any) => (
            <MenuItem key={item.key} value={item.id}>
              {item.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {formik.touched.productTypeId && formik.errors.productTypeId && (
        <FormHelperText error>{formik.errors.productTypeId}</FormHelperText>
      )}
      <p className="field-title">รายละเอียด</p>
      <TextField
        type="text"
        name="description"
        multiline
        rows={4}
        value={formik.values.description}
        onChange={formik.handleChange}
        placeholder="About Product Details"
        error={Boolean(
          getIn(formik.touched, 'description') &&
            getIn(formik.errors, 'description')
        )}
        helperText={
          getIn(formik.touched, 'description') &&
          getIn(formik.errors, 'description')
        }
      />
      <div className="field-title">
        <span className="">Minimum Size</span>
        <FormControl
          sx={{
            height: '24px',
          }}
        >
          <Select
            name="displayUnit"
            value={formik.values.displayUnit}
            onChange={(e) => {
              formik.handleChange(e);
            }}
            displayEmpty
          >
            {displayUnitList.map((item, index) => (
              <MenuItem key={index} value={item} className="!lowercase">
                {item}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <SizeBGStyle>
        <div>
          <p className="mt-0">Length</p>
          <TextField
            type="number"
            name="productSize.minLength"
            placeholder="0"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.minLength)
                : ''
            }
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.minLength',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{ style: { paddingRight: '10px' } }}
            error={Boolean(
              getIn(formik.touched, 'productSize.minLength') &&
                getIn(formik.errors, 'productSize.minLength')
            )}
            helperText={
              getIn(formik.touched, 'productSize.minLength') &&
              getIn(formik.errors, 'productSize.minLength')
            }
          />
        </div>

        <div>
          <p className="mt-0">Width</p>
          <TextField
            type="number"
            name="productSize.minWidth"
            placeholder="0"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.minWidth)
                : ''
            }
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.minWidth',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{
              style: { paddingRight: '10px' },
            }}
            error={Boolean(
              getIn(formik.touched, 'productSize.minWidth') &&
                getIn(formik.errors, 'productSize.minWidth')
            )}
            helperText={
              getIn(formik.touched, 'productSize.minWidth') &&
              getIn(formik.errors, 'productSize.minWidth')
            }
          />
        </div>

        <div>
          <p className="mt-0">Height</p>
          <TextField
            type="number"
            name="productSize.minHeight"
            placeholder="0"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.minHeight)
                : ''
            }
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.minHeight',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{
              style: { paddingRight: '10px' },
            }}
            error={Boolean(
              getIn(formik.touched, 'productSize.minHeight') &&
                getIn(formik.errors, 'productSize.minHeight')
            )}
            helperText={
              getIn(formik.touched, 'productSize.minHeight') &&
              getIn(formik.errors, 'productSize.minHeight')
            }
          />
        </div>
      </SizeBGStyle>
      <p>Maximum Size</p>
      <SizeBGStyle>
        <div>
          <p className="mt-0">Length</p>
          <TextField
            type="number"
            name="productSize.maxLength"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.maxLength)
                : ''
            }
            placeholder="0"
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.maxLength',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{
              style: { paddingRight: '10px' },
            }}
            error={Boolean(
              getIn(formik.touched, 'productSize.maxLength') &&
                getIn(formik.errors, 'productSize.maxLength')
            )}
            helperText={
              getIn(formik.touched, 'productSize.maxLength') &&
              getIn(formik.errors, 'productSize.maxLength')
            }
          />
        </div>

        <div>
          <p className="mt-0">Width</p>
          <TextField
            type="number"
            name="productSize.maxWidth"
            placeholder="0"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.maxWidth)
                : ''
            }
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.maxWidth',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{
              style: { paddingRight: '10px' },
            }}
            error={Boolean(
              getIn(formik.touched, 'productSize.maxWidth') &&
                getIn(formik.errors, 'productSize.maxWidth')
            )}
            helperText={
              getIn(formik.touched, 'productSize.maxWidth') &&
              getIn(formik.errors, 'productSize.maxWidth')
            }
          />
        </div>

        <div>
          <p className="mt-0">Height</p>
          <TextField
            type="number"
            name="productSize.maxHeight"
            placeholder="0"
            value={
              formik.values.productSize
                ? convertToDisplayUnit(formik.values.productSize.maxHeight)
                : ''
            }
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (value <= 0) {
                e.target.value = '0';
                formik.handleChange(e);
              } else {
                formik.setFieldValue(
                  'productSize.maxHeight',
                  convertToMM(value)
                );
              }
            }}
            onKeyDown={(e: any) => {
              if (e.key === '-') {
                e.preventDefault();
              }
            }}
            InputProps={{
              endAdornment: <span>{formik.values.displayUnit}.</span>,
            }}
            inputProps={{
              style: { paddingRight: '10px' },
            }}
            error={Boolean(
              getIn(formik.touched, 'productSize.maxHeight') &&
                getIn(formik.errors, 'productSize.maxHeight')
            )}
            helperText={
              getIn(formik.touched, 'productSize.maxHeight') &&
              getIn(formik.errors, 'productSize.maxHeight')
            }
          />
        </div>
      </SizeBGStyle>
      {id === undefined && (
        <div
          style={{
            fontSize: '12px',
            color: '#90A4AE',
            marginTop: '16px',
          }}
        >
          หมายเหตุ หลังจากที่คุณสร้างสินค้าใหม่เรียบร้อยแล้ว
          คุณจะต้องเข้าไปปรับแต่งสินค้าเพิ่มข้อมูลคุณลักษณะ ของสินค้า และ
          ตั้งค่าข้อมูลการผลิตให้ครบถ้วน
        </div>
      )}

      <div className="pt-8 pb-[5em]">
        <Button
          type="submit"
          fullWidth
          variant="contained"
          color="dark"
          disabled={submitting}
          onClick={(e: any) => {
            if (submitting) {
              e.preventDefault();
            }
          }}
        >
          {submitting ? (
            <CircularProgress
              size={20}
              sx={{
                color: 'white',
              }}
            />
          ) : id ? (
            'แก้ไข'
          ) : (
            'สร้าง'
          )}
        </Button>
      </div>
    </form>
  );
};

export default InformationForm;
