import styled from 'styled-components';
import Link from 'next/link';
import { useRouter } from 'next/router';

const FormTabStyle = styled.div<{ $activeIndex: number }>`
  display: flex;
  flex-direction: row;
  position: relative;
  cursor: pointer;
  background: #f5f7f8;
  height: 56px;
  border-radius: 16px;
  width: 100%;
  &:after {
    content: '';
    width: calc(33.33% - 16px);
    background: white;
    position: absolute;
    top: 8px;
    height: calc(100% - 16px);
    box-shadow: 0 0 3px 0 #26323830;
    transition: 0.3s;
    border-radius: 12px;
    ${({ $activeIndex }) => `left: calc(${$activeIndex * 33.33}% + 8px)`};
    z-index: 9;
  }
  > a {
    padding: 15px 20px;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1em;
    @media screen and (max-width: 650px) {
      font-size: 0.9em;
    }
    font-weight: 400;
    z-index: 10;
    color: #607d8b;
    transition: 0.2s;
    &:hover {
      color: #000;
    }
  }
`;

type ProductFromTabProps = {
  activeIndex: number;
};
const ProductFormTab = ({ activeIndex }: ProductFromTabProps) => {
  const router = useRouter();
  const { id } = router.query;
  return (
    <div
      className="w-[640px] px-[24px]"
      style={{
        maxWidth: '100%',
      }}
    >
      <FormTabStyle $activeIndex={activeIndex}>
        <Link href={`/product/${id}/information`}>ข้อมูลสินค้า</Link>
        <Link href={`/product/${id}/attributes`}>คุณลักษณะ</Link>
        <Link href={`/product/${id}/gallery`}>รูปภาพ</Link>
      </FormTabStyle>
    </div>
  );
};

export default ProductFormTab;
