import React, { useMemo, useState } from 'react';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import TableProductAttribute from '@/components/product/TableProductAttribute';
import { toSnakeCase } from '@/utils/text';
import { FadeInStyled } from '@/styles/share.styled';
import { numberWithCommas } from '@/utils/number';

const ProductAttributesTableStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  column-gap: 24px;
  padding: 24px;
  row-gap: 24px;
  .top-zone {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .attr-name {
      font-size: 18px;
      font-weight: 600;
      display: flex;
      column-gap: 4px;
    }
  }
`;
type Props = {
  data?: any;
  reFetchModels?: () => Promise<void>;
  modelList?: any;
  printList?: any;
  handleRemove: (item: any) => void;
  handleReFetchPrint?: () => void;
};
const ProductAttributesTable = ({
  data,
  reFetchModels,
  modelList,
  printList,
  handleRemove,
  handleReFetchPrint,
}: Props) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  const handleCloseModal = () => {
    setOpenModal(false);
  };
  const handleOpenModal = () => {
    setOpenModal(true);
  };
  const attributeName = useMemo(() => {
    if (data?.name) return data.name;
    if (modelList !== undefined) return 'โมเดล';
    if (printList !== undefined) return 'การพิมพ์';
    return null;
  }, [data?.name, modelList, printList]);

  const badgeCount = useMemo(() => {
    if (modelList !== undefined) return modelList?.length || 0;
    if (printList !== undefined) return printList?.length || 0;
    if (data?.productConfig?.length) return data.productConfig.length;
    return 0;
  }, [modelList, printList, data]);
  return (
    <>
      <ProductAttributesTableStyle
        id={
          data?.name
            ? toSnakeCase(data.name)
            : modelList !== undefined
            ? 'โมเดล'
            : printList !== undefined
            ? 'การพิมพ์'
            : ''
        }
      >
        <div className="top-zone">
          <div className="attr-name">
            {attributeName}
            {badgeCount > 0 && (
              <FadeInStyled>{`(${numberWithCommas(badgeCount)})`}</FadeInStyled>
            )}
          </div>
          <div
            onClick={() => {
              setOpenModal(true);
              setFormMode('create');
            }}
          >
            <ActionButton
              variant="contained"
              color="dark"
              icon={<AddRoundedIcon />}
              text="เพิ่ม"
              borderRadius={'8px'}
            />
          </div>
        </div>
        <TableProductAttribute
          openModal={openModal}
          handleCloseModal={handleCloseModal}
          handleOpenModal={handleOpenModal}
          formMode={formMode}
          makeFormMode={(mode: 'create' | 'edit') => {
            setFormMode(mode);
          }}
          reFetchModels={async () => {
            if (reFetchModels) {
              await reFetchModels();
            }
          }}
          modelList={modelList}
          data={data}
          printList={printList}
          handleRemove={(item: any) => {
            handleRemove(item);
          }}
          handleReFetchPrint={handleReFetchPrint}
        />
      </ProductAttributesTableStyle>
    </>
  );
};

export default ProductAttributesTable;
