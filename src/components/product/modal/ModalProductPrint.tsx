import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
} from '@mui/material';
import {
  FadeInStyled,
  FormModalStyle,
  LoadingFadein,
} from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppDispatch } from '@/store';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import { useRouter } from 'next/router';
import apiProductConfig from '@/services/stock/product-config';
import { setSnackBar } from '@/store/features/alert';
import apiPrintSystem from '@/services/stock/printSystem';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  formMode: string;
  initialValues: any;
  handleReFetchPrint: () => void;
  printList: any;
};
const ModalProductPrint = ({
  open,
  handleCloseModal,
  formMode,
  initialValues,
  handleReFetchPrint,
  printList,
}: Props) => {
  const router = useRouter();
  const { id: productId } = router.query;
  const dispatch = useAppDispatch();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [colorList, setColorList] = useState<any>([]);
  const [printSystemList, setPrintSystemList] = useState<any>([]);

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors: hookFormErrors },
  } = useForm<any>({
    // resolver: yupResolver(
    //   formMode === 'create'
    //     ? createMaterialValidationSchema
    //     : editMaterialValidationSchema
    // ),
    defaultValues: {
      printSystemId: '',
      productId: Number(productId),
      printColorId: '',
    },
  });

  useEffect(() => {
    setColorList([]);
    if (open && formMode === 'create' && isEmpty(initialValues)) {
      reset();
    } else if (open && formMode === 'edit' && !isEmpty(initialValues)) {
      setValue('printSystemId', initialValues.printSystemId);
      setValue('printColorId', initialValues.printColorId);
    }
  }, [open, initialValues]);

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const res = await apiProductConfig.createProductPrintConfig(values);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleCloseModal();
      handleReFetchPrint();
    }
    setIsSubmitting(false);
  };

  useEffect(() => {
    if (watch('printSystemId') && !isEmpty(printSystemList)) {
      const initColor = printSystemList.find(
        (printItem: any) => printItem.id === watch('printSystemId')
      ).printConfig;
      setColorList(initColor);
      setValue('printColorId', '');
    }
  }, [watch('printSystemId'), printSystemList]);

  const handleClickColor = (colorItem: any) => {
    setValue('printColorId', colorItem.printColorId);
  };

  const getPrintListSystem = async () => {
    const res = await apiPrintSystem.getPrintListSystem();
    if (!res.isError) {
      setPrintSystemList(res.data);
    }
  };

  useEffect(() => {
    if (open) {
      getPrintListSystem().then();
    }
  }, [open]);

  const isColorExist = (colorItem: any, printList: any[]) => {
    if (printList) {
      return printList.some(
        (printItem) =>
          printItem.printColorId === colorItem.printColorId &&
          printItem.printSystemId === colorItem.printSystemId
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent>
        <FormModalStyle $width={524}>
          <div className="content-wrap">
            <div className="header">
              <div className="title">
                {formMode === 'create' ? `เลือกพิมพ์` : `แก้ไขพิมพ์`}
              </div>
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
              }}
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <ModalProductMaterialStyle>
                  <FadeInStyled className="w-full">
                    <div className="select-wrap">
                      <FormControl
                        fullWidth
                        sx={{
                          padding: '1px 1px 0',
                          marginTop: '4px',
                          zIndex: 1,
                        }}
                      >
                        <Select
                          displayEmpty
                          {...register('printSystemId')}
                          error={Boolean(hookFormErrors.printSystemId)}
                          value={watch('printSystemId')}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {printSystemList?.map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {hookFormErrors.printSystemId && (
                          <FormHelperText error>
                            {hookFormErrors.printSystemId.message as ReactNode}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </div>
                    <div className="material-item-wrap">
                      {colorList?.map((colorItem: any, index: number) => {
                        const selectedColorItem = {
                          ...colorItem,
                          printSystemId: watch('printSystemId'),
                        };

                        const isExist = isColorExist(
                          selectedColorItem,
                          printList
                        );

                        return (
                          <div
                            className="material-item"
                            onClick={() => {
                              if (!isExist) {
                                handleClickColor(colorItem);
                              }
                            }}
                            key={`${colorItem.printColorId}-${colorItem.printSystemId}-${index}`}
                            style={{
                              cursor: isExist ? 'not-allowed' : 'pointer',
                            }}
                          >
                            {isExist && <div className="disabled" />}
                            <Checkbox
                              color="primary"
                              checked={
                                watch('printColorId') === colorItem.printColorId
                              }
                              value={colorItem.id}
                              icon={<IconUnCheckbox />}
                              checkedIcon={<IconCheckboxBlack />}
                            />
                            <Image
                              src={
                                colorItem.imageUrl ||
                                '/images/product/empty-product.svg'
                              }
                              width={120}
                              height={120}
                              alt=""
                              className="image"
                            />
                            <span>{colorItem.name}</span>
                          </div>
                        );
                      })}

                      {isEmpty(colorList) && (
                        <div className="empty">
                          <h4>ไม่มีรายการ</h4>
                        </div>
                      )}
                    </div>
                  </FadeInStyled>
                  <div className="w-full flex justify-between mt-[24px] gap-5 white-shadow">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={() => {
                        handleCloseModal();
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      disabled={!watch('printColorId')}
                    >
                      {isSubmitting ? (
                        <CircularProgress
                          size={20}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        `บันทึก ${
                          !isEmpty(watch('colorConfig'))
                            ? `(${numberWithCommas(
                                watch('colorConfig').length
                              )})`
                            : ''
                        }`
                      )}
                    </Button>
                  </div>
                </ModalProductMaterialStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

const ModalProductMaterialStyle = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  align-items: center;
  position: relative;

  .empty {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 400;
    }
  }

  .material-item-wrap {
    min-height: 356px;
    max-height: 356px;
    width: 100%;
    overflow: auto;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;

    .material-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 12px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      position: relative;
      animation: ${LoadingFadein} 0.3s ease-in;

      &:hover {
        background: #f5f7f8;
      }

      span {
        margin: 0;
      }

      .image {
        width: 48px;
        height: 48px;
        min-width: 48px;
        border-radius: 8px;
        object-fit: cover;
        overflow: hidden;
      }

      .count {
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        font-weight: 600;
      }
    }

    .disabled {
      width: 100%;
      height: 100%;
      z-index: 9;
      background: rgba(255, 255, 255, 0.5);
      position: absolute;
      animation: ${LoadingFadein} 0.3s ease-in;
    }
  }

  .white-shadow {
    position: relative;

    &:before {
      content: '';
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: 64px;
    }
  }

  .select-wrap {
    width: 100%;
    padding: 0 0 24px;
    position: relative;

    &:before {
      content: '';
      background: linear-gradient(
        to top,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: -24px;
    }
  }

  .sub-material-selector-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;

    .count-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
      position: relative;
      //margin-bottom: 8px;
      margin-top: -8px;
      min-height: 42px;

      &:before {
        content: '';
        width: 100%;
        height: 100%;
        border-bottom: 1px solid #dbe2e5;
        position: absolute;
      }

      &:after {
        content: '';
        background: linear-gradient(
          to top,
          rgba(255, 255, 255, 0) 0%,
          rgb(255, 255, 255) 100%
        );
        position: absolute;
        z-index: 1;
        height: 24px;
        left: 0;
        width: 100%;
        bottom: -40px;
      }

      .count {
        //
      }

      .select-all {
        .MuiFormControlLabel-root {
          flex-direction: row-reverse;
          margin: 0 !important;

          .MuiCheckbox-root {
            padding-right: 0 !important;
            background: transparent !important;

            span {
              display: none !important;
            }
          }

          .MuiTypography-root {
            font-size: 12px !important;
            color: #90a4ae !important;
          }
        }
      }
    }

    .selector-zone {
      width: 100%;
      display: flex;
      flex-direction: column;
      max-height: 318px;
      overflow: auto;

      .MuiFormControlLabel-root {
        margin-right: 0 !important;
        padding-left: 1px;

        .MuiCheckbox-root {
          background: transparent !important;

          span {
            display: none !important;
          }
        }
      }

      .empty-wrap {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 4px;
        height: 500px;

        .title {
          font-size: 20px;
          font-weight: 600;
          color: #cfd8dc;
        }

        .description {
          color: #cfd8dc;
        }
      }
    }
  }
`;

export default ModalProductPrint;
