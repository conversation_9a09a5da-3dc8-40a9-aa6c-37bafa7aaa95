import React, { ReactNode, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  IconButton,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import apiProductModel from '@/services/stock/productModel';
import { setSnackBar } from '@/store/features/alert';
import { useRouter } from 'next/router';
import { useAppDispatch } from '@/store';
import ImageField from '@/components/ImageField';

const createModelValidationSchema = yup.object().shape({
  name: yup.string().required('กรุณากรอกชื่อ'),
  file: yup
    .mixed()
    .test(
      'fileRequired',
      'กรุณาอัปโหลดไฟล์',
      (value: any) => value && value.length > 0
    ),
});
const editModelValidationSchema = yup.object().shape({
  name: yup.string().required('กรุณากรอกชื่อ'),
});
type Props = {
  open: boolean;
  handleCloseModal: () => void;
  reFetchModels: () => Promise<void>;
  formMode: string;
  initialValues: any;
};
const ModalProductModel = ({
  open,
  handleCloseModal,
  reFetchModels,
  formMode,
  initialValues,
}: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [modelImage, setModelImage] = useState<string | null>(null);
  const {
    register,
    handleSubmit,
    setValue,
    // reset,
    // watch,
    formState: { errors: hookFormErrors },
  } = useForm<any>({
    resolver: yupResolver(
      formMode === 'create'
        ? createModelValidationSchema
        : editModelValidationSchema
    ),
    defaultValues: {},
  });

  useEffect(() => {
    if (open && formMode === 'create') {
      setModelImage(null);
    }
  }, [open]);

  useEffect(() => {
    setModelImage(initialValues.imageUrl);
    setValue('name', initialValues.name);
  }, [initialValues]);

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const formData: FormData = new FormData();
    formData.append(
      'productModelRequest',
      JSON.stringify({
        name: values.name,
        productId: Number(id),
        isActive: true,
      })
    );
    const { file } = values;
    if (file) {
      formData.append('file', file[0]);
    }
    if (formMode === 'edit') {
      const res = await apiProductModel.update(initialValues.id, formData);
      if (res && !res.isError) {
        await reFetchModels();
        handleCloseModal();
      }
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
    } else {
      const res = await apiProductModel.create(formData);
      if (res && !res.isError) {
        await reFetchModels();
        handleCloseModal();
      }
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    setIsSubmitting(false);
  };
  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div className="title">
                {formMode === 'create' ? 'สร้างโมเดล' : 'แก้ไขโมเดล'}
              </div>
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
              }}
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <ImageField
                  defaultBackground={modelImage ?? '/images/add-image.svg'}
                  textUploadBtn="อัปโหลด"
                  borderRadius={'14px'}
                  conditionText={'Upload JPG, PNG or SVG file maximum 2 MB.'}
                  alertRequire={!!hookFormErrors.file}
                  handleChange={(file: any) => {
                    setValue('file', file, { shouldValidate: true });
                  }}
                  isCompress={true}
                  maxSizeInMB={2}
                  ignoreQuality={false}
                />
                <div>
                  <p>ชื่อโมเดล</p>
                  <TextField
                    type="text"
                    placeholder="ชื่อโมเดล"
                    {...register('name')}
                    error={Boolean(hookFormErrors.name)}
                    helperText={hookFormErrors.name?.message as ReactNode}
                  />
                </div>
                <div className="w-full flex justify-between mt-[34px] gap-5">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                    onClick={() => {
                      handleCloseModal();
                    }}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <LoadingButton
                    type="submit"
                    loading={isSubmitting}
                    variant="contained"
                    color="dark"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                  >
                    บันทึก
                  </LoadingButton>
                </div>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalProductModel;
