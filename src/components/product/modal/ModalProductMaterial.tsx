import React, { ChangeEvent, ReactNode, useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormHelperText,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import {
  FadeInStyled,
  FormModalStyle,
  LoadingFadein,
} from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { useForm } from 'react-hook-form';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  productAttributesSelector,
  setProductAttributes,
} from '@/store/features/product/attributes';
import apiMaster from '@/services/stock/master';
import { isEmpty } from 'lodash';
import apiMasterConfig from '@/services/stock/master-config';
import { numberWithCommas } from '@/utils/number';
import { useRouter } from 'next/router';
import { Search } from '@mui/icons-material';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import IconCheckbox from '@/components/IconCheckbox';
import apiProductConfig from '@/services/stock/product-config';
import apiMasterCategory from '@/services/stock/master-category';
import { setSnackBar } from '@/store/features/alert';

// const createMaterialValidationSchema = yup.object().shape({
//   // name: yup.string().required('กรุณากรอกชื่อ'),
//   // file: yup
//   //   .mixed()
//   //   .test(
//   //     'fileRequired',
//   //     'กรุณาอัปโหลดไฟล์',
//   //     (value: any) => value && value.length > 0
//   //   ),
// });
// const editMaterialValidationSchema = yup.object().shape({
//   // name: yup.string().required('กรุณากรอกชื่อ'),
// });

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  formMode: string;
  initialValues: any;
  data: any;
};
const ModalProductMaterial = ({
  open,
  handleCloseModal,
  formMode,
  initialValues,
  data,
}: Props) => {
  const router = useRouter();
  const { id: productId } = router.query;
  const dispatch = useAppDispatch();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);
  const { productAttributes } = useAppSelector(productAttributesSelector);
  const [listMasterConfig, setListMasterConfig] = useState<any>([]);
  const [subMaterialList, setSubMaterialList] = useState<any>([]);
  const [selectedSubMaterial, setSelectedSubMaterial] = useState<any>({});
  const [subMaterialDetailCheckList, setSubMaterialDetailCheckList] =
    useState<any>([]);
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>({
    search: '',
  });
  const [openEdit, setOpenEdit] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors: hookFormErrors },
  } = useForm<any>({
    // resolver: yupResolver(
    //   formMode === 'create'
    //     ? createMaterialValidationSchema
    //     : editMaterialValidationSchema
    // ),
    defaultValues: {
      masterId: '',
      productId: Number(productId),
      subMaterialId: '',
      configMaterial: [],
    },
  });

  useEffect(() => {
    if (open && formMode === 'create' && isEmpty(initialValues)) {
      reset();
      setStep(1);
      setSelectedSubMaterial({});
    } else if (open && formMode === 'edit' && !isEmpty(initialValues)) {
      setValue('subMaterialId', initialValues.subMaterialId);
      setValue('masterId', initialValues.masterId);
      setSelectedSubMaterial(initialValues);
      setStep(2);
    }
    if (open) {
      setSubMaterialList([]);
      setSubMaterialDetailCheckList([]);
    }
  }, [open, initialValues]);

  const getProductAttributes = async () => {
    const res = await apiMasterCategory.getInfoProductById(Number(productId));
    if (!res.isError) {
      await dispatch(setProductAttributes(res.data));
    }
  };

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const sendValue = {
      ...values,
      subMaterialId:
        selectedSubMaterial.subMaterial?.id ||
        selectedSubMaterial.subMaterialId,
    };
    const res = await apiProductConfig.createProductConfig(sendValue);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleCloseModal();
      setOpenEdit(false);
      await getProductAttributes();
    }
    setIsSubmitting(false);
  };

  const getListMasterConfig = async () => {
    const masterCategoryId = data.id;
    const res = await apiMaster.getListMasterConfig(masterCategoryId);
    if (!res.isError) {
      setListMasterConfig(res.data);
    }
  };

  useEffect(() => {
    if (!isEmpty(productAttributes)) {
      getListMasterConfig().then();
    }
  }, [productAttributes]);

  const getListMasterConfigMaterialByMasterIdConfig = async () => {
    const res =
      await apiMasterConfig.getListMasterConfigMaterialByMasterIdConfig(
        watch('masterId')
      );
    setSubMaterialList([]);
    if (!res.isError) {
      setSubMaterialList(res.data);
    }
  };

  useEffect(() => {
    if (watch('masterId')) {
      getListMasterConfigMaterialByMasterIdConfig().then();
    }
  }, [watch('masterId')]);

  const handleClickSubMaterial = (subMaterialItem: any) => {
    setSelectedSubMaterial(subMaterialItem);
  };

  const getProductSubMaterialCheckList = async (isInit: boolean) => {
    const res = await apiMasterConfig.getProductSubMaterialCheckList({
      productId,
      subMaterialId:
        selectedSubMaterial.subMaterial?.id ||
        selectedSubMaterial.subMaterialId,
      masterId: watch('masterId'),
      search: filters.search,
    });
    if (!res.isError) {
      setSubMaterialDetailCheckList(res.data);
      if (isInit) {
        const initConfigMaterialValue = res.data
          ?.filter((item: any) => item.isProductConfig)
          ?.map((filterItem: any) => {
            return {
              subMaterialDetailId: filterItem.id,
            };
          });
        setValue('configMaterial', initConfigMaterialValue);
      }
      setStep(2);
      setOpenEdit(true);
    }
  };

  useEffect(() => {
    if (!isEmpty(selectedSubMaterial)) {
      getProductSubMaterialCheckList(true).then();
    }
  }, [selectedSubMaterial]);

  useEffect(() => {
    if (watch('masterId')) {
      getProductSubMaterialCheckList(false).then();
    }
  }, [filters]);

  useEffect(() => {
    if (step === 1) {
      setSelectedSubMaterial({});
    }
  }, [step]);

  const handleChangeConfigMaterial = (
    isCheck: boolean,
    subMaterialDetailId: number
  ) => {
    if (isCheck) {
      setValue('configMaterial', [
        ...watch('configMaterial'),
        {
          subMaterialDetailId,
        },
      ]);
    } else {
      const removeSubMaterialDetailId = watch('configMaterial')?.filter(
        (item: any) => item.subMaterialDetailId !== subMaterialDetailId
      );
      setValue('configMaterial', removeSubMaterialDetailId);
    }
  };

  const handleCheckAll = (isCheck: boolean) => {
    if (isCheck) {
      const checkAllValue = subMaterialDetailCheckList.map((item: any) => {
        return {
          subMaterialDetailId: item.id,
        };
      });
      setValue('configMaterial', checkAllValue);
    } else {
      setValue('configMaterial', []);
    }
  };

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) => {
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        search: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  return (
    <Dialog
      open={formMode === 'create' ? open : openEdit}
      onClose={() => {
        handleCloseModal();
        setOpenEdit(false);
      }}
    >
      <DialogContent>
        <FormModalStyle $width={524}>
          <div className="content-wrap">
            <div className="header">
              {step !== 1 && formMode !== 'edit' && (
                <div className="back" onClick={() => setStep(step - 1)}>
                  <IconButton sx={{ color: '#263238' }}>
                    <KeyboardBackspaceRoundedIcon />
                  </IconButton>
                </div>
              )}
              <div className="title">
                {step === 1
                  ? formMode === 'create'
                    ? `เลือก${data?.name}`
                    : `แก้ไข${data?.name}`
                  : step === 2
                  ? selectedSubMaterial?.subMaterial?.name ||
                    selectedSubMaterial.subMaterialName
                  : ''}
              </div>
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                  setOpenEdit(false);
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
              }}
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <ModalProductMaterialStyle>
                  {step === 1 && (
                    <FadeInStyled className="w-full">
                      <div className="select-wrap">
                        <FormControl
                          fullWidth
                          sx={{
                            padding: '1px 1px 0',
                            marginTop: '4px',
                            zIndex: 1,
                          }}
                        >
                          <Select
                            displayEmpty
                            {...register('masterId')}
                            error={Boolean(hookFormErrors.masterId)}
                            value={watch('masterId')}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {listMasterConfig?.map((item: any) => (
                              <MenuItem key={item.id} value={item.id}>
                                {item.name}
                              </MenuItem>
                            ))}
                          </Select>
                          {hookFormErrors.masterId && (
                            <FormHelperText error>
                              {hookFormErrors.masterId.message as ReactNode}
                            </FormHelperText>
                          )}
                        </FormControl>
                      </div>
                      <div className="material-item-wrap">
                        {subMaterialList?.map(
                          (subMaterialItem: any, index: number) => {
                            return (
                              <div
                                className="material-item"
                                onClick={() => {
                                  handleClickSubMaterial(subMaterialItem);
                                }}
                                key={index}
                              >
                                <Image
                                  src={
                                    subMaterialItem.subMaterial.imageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  width={120}
                                  height={120}
                                  alt=""
                                  className="image"
                                />
                                <span>{subMaterialItem.subMaterial.name}</span>
                                <div className="count">
                                  {numberWithCommas(
                                    subMaterialItem.subMaterial
                                      .countSubMaterialDetail
                                  )}
                                </div>
                              </div>
                            );
                          }
                        )}
                        {isEmpty(subMaterialList) && (
                          <div className="empty">
                            <h4>ไม่มีรายการ</h4>
                          </div>
                        )}
                      </div>
                    </FadeInStyled>
                  )}
                  {step === 2 && (
                    <FadeInStyled className="sub-material-selector-wrap w-full">
                      <TextField
                        fullWidth
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {
                          handleSearch(e);
                        }}
                        placeholder="ค้นหา"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              {loadingSearch ? (
                                <div className="flex items-center justify-center h-[24px] w-[24px]">
                                  <CircularProgress size={20} />
                                </div>
                              ) : (
                                <Search />
                              )}
                            </InputAdornment>
                          ),
                        }}
                        sx={{ padding: '0 1px', marginTop: '1px' }}
                      />
                      <div className="count-wrap">
                        <div className="count">
                          {!isEmpty(subMaterialDetailCheckList)
                            ? `${numberWithCommas(
                                subMaterialDetailCheckList.length
                              )} รายการ`
                            : 'ไม่มีรายการ'}
                        </div>
                        {!isEmpty(subMaterialDetailCheckList) && (
                          <div className="select-all">
                            <FormControlLabel
                              control={
                                <Checkbox
                                  color="primary"
                                  checked={
                                    watch('configMaterial').length ===
                                    subMaterialDetailCheckList.length
                                  }
                                  onChange={(event: any) => {
                                    handleCheckAll(event.target.checked);
                                  }}
                                  icon={<IconUnCheckbox />}
                                  checkedIcon={<IconCheckboxBlack />}
                                />
                              }
                              label="เลือกทั้งหมด"
                            />
                          </div>
                        )}
                      </div>
                      <div className="selector-zone">
                        {!isEmpty(subMaterialDetailCheckList) ? (
                          subMaterialDetailCheckList.map(
                            (subMaterialDetailItem: any, index: number) => {
                              return (
                                <FormControlLabel
                                  key={index}
                                  control={
                                    <Checkbox
                                      color="primary"
                                      checked={watch('configMaterial')?.some(
                                        (item: any) =>
                                          item.subMaterialDetailId ===
                                          subMaterialDetailItem.id
                                      )}
                                      value={subMaterialDetailItem.id}
                                      onChange={(event: any) => {
                                        handleChangeConfigMaterial(
                                          event.target.checked,
                                          subMaterialDetailItem.id
                                        );
                                      }}
                                      icon={<IconUnCheckbox />}
                                      checkedIcon={
                                        subMaterialDetailCheckList.some(
                                          (item: any) =>
                                            item.id ===
                                              subMaterialDetailItem.id &&
                                            item.isProductConfig
                                        ) ? (
                                          <IconCheckboxBlack />
                                        ) : (
                                          <IconCheckbox />
                                        )
                                      }
                                    />
                                  }
                                  label={subMaterialDetailItem.name}
                                />
                              );
                            }
                          )
                        ) : (
                          <div className="empty-wrap">
                            <div className="title">ไม่มีรายการ</div>
                            <div className="description">
                              คุณยังไม่ไ้ด้สร้าง Sub material Detail ในรายการนี้
                            </div>
                            <Button
                              type="button"
                              variant="outlined"
                              color="blueGrey"
                              sx={{
                                minWidth: '88px',
                                marginTop: '8px',
                              }}
                            >
                              สร้าง
                            </Button>
                          </div>
                        )}
                      </div>
                    </FadeInStyled>
                  )}
                  <div className="w-full flex justify-between mt-[24px] gap-5 white-shadow">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '400',
                      }}
                      fullWidth
                      onClick={() => {
                        handleCloseModal();
                        setOpenEdit(false);
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      disabled={
                        step === 1 ||
                        isEmpty(watch('configMaterial')) ||
                        isEmpty(subMaterialDetailCheckList)
                      }
                    >
                      {isSubmitting ? (
                        <CircularProgress
                          size={20}
                          style={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        `บันทึก ${
                          !isEmpty(watch('configMaterial'))
                            ? `(${numberWithCommas(
                                watch('configMaterial').length
                              )})`
                            : ''
                        }`
                      )}
                    </Button>
                  </div>
                </ModalProductMaterialStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

const ModalProductMaterialStyle = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  align-items: center;
  position: relative;
  .empty {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 400;
    }
  }
  .material-item-wrap {
    min-height: 356px;
    max-height: 356px;
    width: 100%;
    overflow: auto;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .material-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 12px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      position: relative;
      animation: ${LoadingFadein} 0.3s ease-in;
      &:hover {
        background: #f5f7f8;
      }
      span {
        margin: 0;
      }
      .image {
        width: 48px;
        height: 48px;
        min-width: 48px;
        border-radius: 8px;
        object-fit: cover;
        overflow: hidden;
      }
      .count {
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        font-weight: 600;
      }
    }
  }
  .white-shadow {
    position: relative;
    &:before {
      content: '';
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: 64px;
    }
  }
  .select-wrap {
    width: 100%;
    padding: 0 0 24px;
    position: relative;
    &:before {
      content: '';
      background: linear-gradient(
        to top,
        rgba(255, 255, 255, 0) 0%,
        rgb(255, 255, 255) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: -24px;
    }
  }
  .sub-material-selector-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    .count-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
      position: relative;
      margin-bottom: 8px;
      min-height: 42px;
      &:before {
        content: '';
        width: 100%;
        height: 100%;
        border-bottom: 1px solid #dbe2e5;
        position: absolute;
      }
      &:after {
        content: '';
        background: linear-gradient(
          to top,
          rgba(255, 255, 255, 0) 0%,
          rgb(255, 255, 255) 100%
        );
        position: absolute;
        z-index: 1;
        height: 24px;
        left: 0;
        width: 100%;
        bottom: -40px;
      }
      .count {
        //
      }
      .select-all {
        .MuiFormControlLabel-root {
          flex-direction: row-reverse;
          margin: 0 !important;
          .MuiCheckbox-root {
            padding-right: 0 !important;
            background: transparent !important;
            span {
              display: none !important;
            }
          }
          .MuiTypography-root {
            font-size: 12px !important;
            color: #90a4ae !important;
          }
        }
      }
    }
    .selector-zone {
      width: 100%;
      display: flex;
      flex-direction: column;
      max-height: 318px;
      overflow: auto;
      padding: 4px 0;
      .MuiFormControlLabel-root {
        margin-right: 0 !important;
        padding-left: 1px;
        .MuiCheckbox-root {
          background: transparent !important;
          span {
            display: none !important;
          }
        }
      }
      .empty-wrap {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 4px;
        height: 500px;
        .title {
          font-size: 20px;
          font-weight: 600;
          color: #cfd8dc;
        }
        .description {
          color: #cfd8dc;
        }
      }
    }
  }
`;

export default ModalProductMaterial;
