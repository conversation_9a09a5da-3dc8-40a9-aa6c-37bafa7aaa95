import styled, { css } from 'styled-components';
import { KeyboardBackspace } from '@mui/icons-material';
import Image from 'next/image';
import React from 'react';
import { LoadingFadein } from '@/styles/share.styled';
import Link from 'next/link';
import { isEmpty, isUndefined } from 'lodash';
import { IconButton } from '@mui/material';
import BentoBtn from '@/components/BentoBtn';
import { useRouter } from 'next/router';

const ProductNavStyle = styled.div<{
  $borderBottom?: boolean;
  $backUrl?: string;
  $centerTitle?: boolean;
}>`
  position: sticky;
  top: 0;
  left: 0;
  min-height: 64px;
  width: 100%;
  z-index: 99;
  animation: ${LoadingFadein} 0.3s ease-in;
  padding: 0 24px;
  > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    ${({ $centerTitle }) =>
      !isUndefined($centerTitle) &&
      css`
        width: 100%;
      `}
  }

  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }

  ${({ $backUrl }) =>
    !isEmpty($backUrl)
      ? css`
          justify-content: space-between;
        `
      : css`
          justify-content: end;
        `}

  display: flex;
  flex-direction: row;
  align-items: center;
  background: white;
  ${({ $borderBottom }) =>
    !isEmpty($borderBottom) &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}

  column-gap: 16px;
  h1 {
    font-size: 22px;
    margin: 0;
    font-weight: 600;
  }
  @media screen and (max-width: 820px) {
    top: 64px;
  }
  @media screen and (max-width: 650px) {
    h1 {
      font-size: 1.2em;
    }
  }
  a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #000 !important;
    height: 40px;
    width: 40px;
    svg {
      width: 24px;
      height: 24px;
    }
  }
  ${({ $borderBottom }) =>
    $borderBottom &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
  }
  .back-button {
    a {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    svg {
      width: 24px;
      height: 24px;
      color: #263238;
    }
  }
  .print-btn {
    height: 40px;
    width: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s ease-out;
    background: white;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    position: relative;
    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateY(-50%) translateX(-50%);
    }
    &:hover {
      filter: brightness(0.9);
    }
  }
  .nav-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 50%;
    animation: ${LoadingFadein} 0.3s ease-in;
    ${({ $backUrl }) =>
      $backUrl
        ? css`
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
          `
        : css`
            position: absolute;
            left: 0;
            transform: translateX(16px);
          `};
  }
  .children {
    display: flex;
    align-items: center;
    column-gap: 12px;
  }
`;

type ProductNavProps = {
  title?: string;
  backUrl?: string;
  children?: React.ReactNode;
  showBorderBottom?: boolean;
  showAvatar?: boolean;
  centerTitle?: boolean;
  animate?: boolean;
  dashBoardButton?: boolean;
  backUrlEvent?: () => void;
  isBack?: boolean;
};
const ProductNav = ({
  title,
  backUrl,
  children,
  showBorderBottom,
  showAvatar,
  backUrlEvent,
  dashBoardButton,
  isBack,
}: ProductNavProps) => {
  const router = useRouter();
  return (
    <ProductNavStyle $borderBottom={showBorderBottom} $backUrl={backUrl}>
      {backUrl && !backUrlEvent && (
        <div className="back-button">
          <IconButton size={'small'}>
            <Link
              href={backUrl}
              onClick={(e: any) => {
                if (isBack) {
                  e.preventDefault();
                  router.back();
                }
              }}
            >
              <KeyboardBackspace />
            </Link>
          </IconButton>
        </div>
      )}
      {backUrlEvent && (
        <div className="back-button">
          <IconButton size={'small'}>
            <Link
              href={'#'}
              onClick={(event: any) => {
                event.preventDefault();
                backUrlEvent();
              }}
            >
              <KeyboardBackspace />
            </Link>
          </IconButton>
        </div>
      )}
      {!isEmpty(title) && <h1 className="nav-title">{title}</h1>}
      <div className="children">{children}</div>
      {dashBoardButton && <BentoBtn url={'/dashboard'} />}
      {showAvatar && (
        <div className="avatar">
          <Image src={'/icons/icon-blank-profile.svg'} alt="" fill />
        </div>
      )}
    </ProductNavStyle>
  );
};

export default ProductNav;
