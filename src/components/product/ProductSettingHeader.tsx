import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { ProductByIdType } from '@/types/product';
import { useRouter } from 'next/router';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import apiProduct from '@/services/stock/product';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const ProductSettingHeaderStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  background: white;
  padding: 16px 24px 24px;
  .product-name-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    .name-text {
      font-size: 34px;
      max-width: 50%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 600;
    }
  }
  .product-info {
    width: 100%;
    display: flex;
    column-gap: 24px;
    @media screen and (max-width: 550px) {
      flex-direction: column;
      row-gap: 24px;
    }
    .image-wrap {
      width: 140px;
      height: 140px;
      border-radius: 16px;
      overflow: hidden;
      min-width: 140px;
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
    .info-group {
      display: flex;
      justify-content: space-between;
      column-gap: 40px;
      row-gap: 16px;
      width: 100%;
      flex-wrap: wrap;
      .detail {
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        .row {
          display: flex;
          .key {
            font-size: 14px;
            width: 108px;
            min-width: 108px;
            color: #90a4ae;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .value {
            font-size: 14px;
          }
        }
      }
    }
  }
`;
type Props = {
  productData: ProductByIdType;
};
const ProductSettingHeader = ({ productData }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const [openConfirmRemove, setOpenConfirmRemove] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const handleConfirmDelete = async () => {
    setLoadingConfirm(true);
    const res = await apiProduct.remove(id as string);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await router.push('/product');
    }
    setLoadingConfirm(false);
  };

  return (
    <>
      <AppModalConfirm
        open={openConfirmRemove.open}
        onClickClose={() => {
          setOpenConfirmRemove({
            ...openConfirmRemove,
            open: false,
          });
        }}
        confirmTitle={openConfirmRemove.title}
        confirmDescription={openConfirmRemove.description}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleConfirmDelete();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="280px"
      />
      <ProductSettingHeaderStyle id={'productSettingHeader'}>
        <div className="product-name-wrap">
          <div className="name-text">{productData.name}</div>
          {/* <KebabTable */}
          {/*  item={productData} */}
          {/*  handleRemove={async (item: any) => { */}
          {/*    setOpenConfirmRemove({ */}
          {/*      open: true, */}
          {/*      title: 'ยืนยันลบสินค้า', */}
          {/*      description: `คุณต้องการลบ ${item.name} ใช่หรือไม่`, */}
          {/*    }); */}
          {/*  }} */}
          {/*  isEdit={{ */}
          {/*    status: true, */}
          {/*    url: `/product/${id}`, */}
          {/*  }} */}
          {/*  isRemove={true} */}
          {/* /> */}
        </div>
        <div className="product-info">
          <div className="image-wrap">
            <Image
              src={productData.imageUrl || '/images/product/empty-product.svg'}
              width={280}
              height={280}
              alt=""
            />
          </div>
          <div className="info-group">
            <div className="detail">
              <div className="row">
                <div className="key">รหัส</div>
                <div className="value">{productData.code}</div>
              </div>
              <div className="row">
                <div className="key">ชื่อสินค้า</div>
                <div className="value">{productData.name}</div>
              </div>
              <div className="row">
                <div className="key">หมวดหมู่</div>
                <div className="value">{productData.productCategoryName}</div>
              </div>
              <div className="row">
                <div className="key">ประเภท</div>
                <div className="value">{productData.productTypeName}</div>
              </div>
            </div>
            <div className="detail">
              <div className="row">
                <div className="key">รายละเอียด</div>
                <div className="value">{productData.description || '-'}</div>
              </div>
              <div className="row">
                <div className="key">ขนาดเล็กสุด</div>
                <div className="value">
                  {`${productData.productSize.minLength}x${productData.productSize.minWidth}x${productData.productSize.minHeight}`}{' '}
                  mm
                </div>
              </div>
              <div className="row">
                <div className="key">ขนาดใหญ่สุด</div>
                <div className="value">
                  {`${productData.productSize.maxLength}x${productData.productSize.maxWidth}x${productData.productSize.maxHeight}`}{' '}
                  mm
                </div>
              </div>
            </div>
          </div>
        </div>
      </ProductSettingHeaderStyle>
    </>
  );
};

export default ProductSettingHeader;
