import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import {
  BadgeStyle,
  FilterWrapStyled,
  ScrollBarStyled,
} from '@/styles/share.styled';
import { Tab, Tabs } from '@mui/material';
import ProductAttributesTable from '@/components/product/ProductAttributesTable';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  productAttributesSelector,
  setProductAttributes,
} from '@/store/features/product/attributes';
import { useRouter } from 'next/router';
import apiMasterCategory from '@/services/stock/master-category';
import { isEmpty } from 'lodash';
import apiProductModel from '@/services/stock/productModel';
import { setSnackBar } from '@/store/features/alert';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import Image from 'next/image';
import apiProductConfig from '@/services/stock/product-config';
import { toSnakeCase } from '@/utils/text';

const ProductAttributesStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .tab-bar-position {
    position: sticky;
    top: 0;
    z-index: 1;
    background: white;
  }
  .attr-table-wrap {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
  }
`;
type Props = {
  refSettingProduct: any;
};
const ProductAttributes = ({ refSettingProduct }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const { productAttributes } = useAppSelector(productAttributesSelector);
  const [tabValue, setTabValue] = useState<number>(1);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const [tabsBadge, setTabsBadge] = useState<any>([]);
  const [productAttributesTabs, setProductAttributesTabs] = useState<any>([]);
  const [modelList, setModelList] = useState<any>([]);
  const [modalConfirm, setModalConfirm] = useState<any>({
    open: false,
    title: '',
    description: '',
    attrName: '',
    id: null,
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const [printListByProduct, setPrintListByProduct] = useState<any>([]);
  const scrollTo = (elemId: string) => {
    const element = document.getElementById(elemId);
    const tabsElem = document.getElementById('productSettingTabBar');
    const rectTabsElem = tabsElem?.getBoundingClientRect();
    if (element && rectTabsElem && refSettingProduct.current) {
      const { height: tabsHeight } = rectTabsElem;
      const elementPosition =
        element.offsetTop - refSettingProduct.current.offsetTop;
      setIsScrolling(true);

      refSettingProduct.current.scrollTo({
        top: elementPosition - tabsHeight,
        behavior: 'smooth',
      });
      setTimeout(() => {
        setIsScrolling(false);
      }, 500);
    }
  };

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: number) => {
    const tab = productAttributesTabs.find(
      (item: any) => item.value === newValue
    );
    if (tab) {
      setTabValue(newValue);
      scrollTo(tab.activeId);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      if (isScrolling) return;

      const tabsElem = document.getElementById('productSettingTabBar');
      const rectTabsElem = tabsElem?.getBoundingClientRect();
      const tabsHeight = rectTabsElem?.height || 0;

      if (refSettingProduct.current) {
        const { scrollTop } = refSettingProduct.current;
        productAttributesTabs.forEach((item: any) => {
          const element = document.getElementById(item.activeId);

          if (element) {
            const elementPosition =
              element.offsetTop - refSettingProduct.current.offsetTop;

            if (scrollTop >= elementPosition - tabsHeight) {
              setTabValue(item.value);
            }
          }
        });
      }
    };

    const currentRef = refSettingProduct.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener('scroll', handleScroll);
      }
    };
  }, [productAttributesTabs, refSettingProduct, isScrolling]);

  const getProductAttributes = async () => {
    const res = await apiMasterCategory.getInfoProductById(Number(id));
    if (!res.isError) {
      await dispatch(setProductAttributes(res.data));
    }
  };

  const getModels = async () => {
    const res = await apiProductModel.getList(id as string);
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setModelList(res.data);
      } else {
        setModelList(null);
      }
    }
  };
  const getPrintListByProduct = async () => {
    const res = await apiProductConfig.getProductPrintConfigByProductId(
      id as string
    );
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setPrintListByProduct(res.data);
      } else {
        setPrintListByProduct(null);
      }
    }
  };

  useEffect(() => {
    if (id) {
      getProductAttributes().then();
      getModels().then();
      getPrintListByProduct().then();
    }
  }, [id]);

  useEffect(() => {
    if (!isEmpty(productAttributes)) {
      if (productAttributesTabs.length <= 1) {
        let counter = 2;

        const mappedTabs = productAttributes.reduce(
          (acc: any[], item: any, index: number) => {
            if (index === 2) {
              acc.push({
                name: 'การพิมพ์',
                value: counter++,
                activeId: 'การพิมพ์',
              });
            }

            acc.push({
              name: item.name,
              value: counter++,
              activeId: toSnakeCase(item.name),
            });
            return acc;
          },
          []
        );
        const addModel = [
          {
            name: 'โมเดล',
            value: 1,
            activeId: 'โมเดล',
          },
          ...mappedTabs,
        ];

        setProductAttributesTabs(addModel);
      }

      const mappedBadge = productAttributes.reduce(
        (acc: any[], item: any, index: number) => {
          if (index === 2) {
            acc.push(printListByProduct?.length || 0);
          }
          acc.push(item.productConfig.length);
          return acc;
        },
        []
      );

      setTabsBadge([modelList?.length || 0, ...mappedBadge]);
    }
  }, [productAttributes, modelList, printListByProduct]);

  const handleRemove = (dataItem: any, attrName: string) => {
    switch (attrName) {
      case 'โมเดล':
        setModalConfirm({
          open: true,
          title: `ยืนยันลบโมเดลสินค้า`,
          description: `คุณต้องการลบการใช้งาน “${dataItem.name}” ออกจากรายการนี้ใช่หรือไม่?`,
          attrName,
          id: dataItem.id,
        });
        break;
      case 'การพิมพ์':
        setModalConfirm({
          open: true,
          title: `ยืนยันลบการพิมพ์`,
          description: `คุณต้องการลบการใช้งาน “${dataItem.printSystemName}” ออกจากรายการนี้ใช่หรือไม่?`,
          attrName,
          id: dataItem.id,
        });
        break;
      default:
        setModalConfirm({
          open: true,
          title: `ยืนยันลบ${dataItem.subMaterialName}`,
          description: `คคุณต้องการที่จะลบข้อมูลคุณลักษณะนี้ออกจากรายการขายนี้ใช่หรือไม่?`,
          attrName,
          id: dataItem.id,
        });
    }
  };

  const handleConfirmDelete = async () => {
    setLoadingConfirm(true);
    switch (modalConfirm.attrName) {
      case 'โมเดล': {
        const res = await apiProductModel.delete(modalConfirm.id);
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: !res.isError ? 'success' : 'error',
          })
        );
        if (!res.isError) {
          await getModels();
        }
        break;
      }
      case 'การพิมพ์': {
        const res = await apiProductConfig.deleteProductPrintConfig(
          modalConfirm.id
        );
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: !res.isError ? 'success' : 'error',
          })
        );
        if (!res.isError) {
          await getPrintListByProduct();
        }
        break;
      }
      default: {
        const res = await apiProductConfig.deleteProductConfig(modalConfirm.id);
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: !res.isError ? 'success' : 'error',
          })
        );
        if (!res.isError) {
          await getProductAttributes();
        }
        break;
      }
    }
    setLoadingConfirm(false);
    setModalConfirm({
      ...modalConfirm,
      open: false,
    });
  };

  const renderedTable = useMemo(() => {
    if (isEmpty(productAttributes)) return null;

    return productAttributes.reduce(
      (acc: ReactNode[], item: any, index: number) => {
        if (index === 2) {
          acc.push(
            <ProductAttributesTable
              key={index}
              printList={printListByProduct}
              handleRemove={(dataItem: any) => {
                handleRemove(dataItem, 'การพิมพ์');
              }}
              handleReFetchPrint={async () => {
                await getPrintListByProduct();
              }}
            />
          );
        }

        acc.push(
          <ProductAttributesTable
            key={index}
            data={item}
            handleRemove={(dataItem: any) => {
              handleRemove(dataItem, item.name);
            }}
          />
        );

        return acc;
      },
      []
    );
  }, [productAttributes, printListByProduct, handleRemove]);

  return (
    <>
      <AppModalConfirm
        open={modalConfirm.open}
        onClickClose={() => {
          setModalConfirm({
            ...modalConfirm,
            open: false,
          });
        }}
        confirmTitle={modalConfirm.title}
        confirmDescription={modalConfirm.description}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleConfirmDelete();
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        maxWidth="280px"
      />
      <ProductAttributesStyle>
        <div className="tab-bar-position" id={'productSettingTabBar'}>
          <ScrollBarStyled>
            <FilterWrapStyled>
              <div
                style={{
                  minWidth: '482px',
                }}
              >
                <Tabs
                  value={tabValue}
                  onChange={handleChangeTab}
                  variant="standard"
                >
                  {productAttributesTabs.map((item: any, index: number) => (
                    <Tab
                      key={item.value}
                      label={
                        <div
                          className="flex items-center"
                          style={{
                            columnGap: '8px',
                            minHeight: '32px',
                          }}
                        >
                          {item.name}
                          <BadgeStyle
                            className={`${
                              tabValue !== item.value ? '' : 'active'
                            }`}
                          >
                            {tabsBadge[index]}
                          </BadgeStyle>
                        </div>
                      }
                      value={item.value}
                      sx={{
                        color: tabValue !== item.value ? '#78909C' : '',
                      }}
                    />
                  ))}
                </Tabs>
              </div>
            </FilterWrapStyled>
          </ScrollBarStyled>
        </div>
        <div className="attr-table-wrap">
          <ProductAttributesTable
            reFetchModels={async () => {
              await getModels();
            }}
            modelList={modelList}
            handleRemove={(dataItem: any) => {
              handleRemove(dataItem, 'โมเดล');
            }}
          />
          {renderedTable}
        </div>
      </ProductAttributesStyle>
    </>
  );
};

export default ProductAttributes;
