import React, { useEffect, useRef, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';
import styled, { css } from 'styled-components';
import { LoadingFadein, ScrollBarStyled } from '@/styles/share.styled';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { isEmpty, isNull } from 'lodash';
import ModalProductModel from '@/components/product/modal/ModalProductModel';
import ModalProductMaterial from '@/components/product/modal/ModalProductMaterial';
import { productAttributesSelector } from '@/store/features/product/attributes';
import { numberWithCommas } from '@/utils/number';
import ModalProductPrint from '@/components/product/modal/ModalProductPrint';
import PopoverAction from '@/components/PopoverActionn';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import SvgEditIcon from '@/components/svg-icon/SvgEditIcon';

const TableProductAttributeWrapStyle = styled.div`
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  width: 100%;
  position: relative;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 650px) {
    margin-top: 24px;
  }
`;

const TableProductAttributeStyle = styled.div<{ $height: number }>`
  width: 100%;
  transition: height 0.3s ease-out;
  ${({ $height }) =>
    $height &&
    css`
      height: ${$height}px;
    `}
  .color-chip {
    height: 32px;
    border: 1px solid #dbe2e5;
    border-radius: 28px;
    display: flex;
    align-items: center;
    padding: 0 12px 0 4px;
    overflow: hidden;
    column-gap: 8px;
    width: fit-content;
    max-width: 124px;
    img {
      width: 24px;
      height: 24px;
      min-width: 24px;
      border-radius: 50%;
      overflow: hidden;
    }
    span {
      white-space: nowrap;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      font-weight: 600;
    }
  }
  thead {
    tr {
      th:last-child {
        position: absolute !important;
        right: 0;
        top: 0;
        height: 41px !important;
        &:before {
          content: '';
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 1) 100%
          );
          position: absolute;
          z-index: 1;
          height: 100%;
          left: -32px;
          padding: 0 16px;
          top: 0;
        }
      }
    }
  }
  tbody {
    tr {
      position: relative;
      td:last-child {
        position: absolute !important;
        right: 0;
        transform: translateY(-50%);
        top: 50%;
        border: none;
        &:before {
          content: '';
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 1) 100%
          );
          position: absolute;
          z-index: 1;
          height: 100%;
          left: -32px;
          padding: 0 16px;
          top: 0;
        }
      }
    }
  }
  .MuiTable-root {
    border-collapse: initial;
    border-spacing: initial;
    overflow: hidden;
    .MuiTableHead-root {
      .MuiTableRow-head {
        .MuiTableCell-head {
          font-weight: 400;
          white-space: nowrap;
          font-size: 14px;
        }
      }
    }
    .MuiTableCell-root {
      color: #263238;
      background: white;
      vertical-align: center;
      position: relative;
      font-size: 14px;
      padding: 8px 16px;
      .count-chip {
        height: 30px;
        min-width: 30px;
        max-width: fit-content;
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
      }
    }
    .MuiTableBody-root {
      .table-space {
        height: 8px;
        padding: 0;
        .MuiTableCell-root {
          padding: 0;
          background: #f5f7f8;
        }
      }
      .MuiTableRow-root {
        &.blank {
          height: 58px !important;
        }
        &:last-child {
          .MuiTableCell-root {
            border-bottom: 0;
          }
        }
      }
      .blank {
        height: 72px;
        td {
          left: 0;
        }
        .blank-text {
          position: absolute;
          top: 78%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #cfd8dc;
        }
      }
    }
    .image-with-data {
      display: flex;
      align-items: center;
      column-gap: 12px;
      font-weight: 500;
      .image-wrap {
        height: 42px;
        width: 42px;
        min-width: 42px;
        border-radius: 6px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
`;
type Props = {
  openModal: boolean;
  handleCloseModal: () => void;
  handleOpenModal: () => void;
  reFetchModels: () => Promise<void>;
  makeFormMode: (mode: 'create' | 'edit') => void;
  formMode: 'create' | 'edit';
  modelList?: any;
  data?: any;
  printList?: any;
  handleRemove: (item: any) => void;
  handleReFetchPrint?: () => void;
};

const TableCreatePurchaseOrder = ({
  openModal,
  handleCloseModal,
  handleOpenModal,
  formMode,
  makeFormMode,
  reFetchModels,
  modelList,
  data,
  printList,
  handleRemove,
  handleReFetchPrint,
}: Props) => {
  const tableRef = useRef<any>(null);
  const [tableHeight, setTableHeight] = useState<number>(107);
  const [initialValues, setInitialValues] = useState<any>({});
  const { productAttributes } = useAppSelector(productAttributesSelector);
  const makeInitialData = (data: any) => {
    setInitialValues({ ...data });
    makeFormMode('edit');
    handleOpenModal();
  };

  useEffect(() => {
    if (openModal && formMode === 'create') {
      setInitialValues({});
    }
  }, [openModal]);

  const getTableHeightModel = () => {
    if (tableRef.current) {
      const ref = tableRef.current;
      const rect = ref.getBoundingClientRect();
      const { height: rectHeight } = rect;
      setTableHeight(rectHeight);
    }
  };

  useEffect(() => {
    getTableHeightModel();
  }, [modelList, productAttributes, printList]);

  return (
    <>
      {modelList !== undefined && (
        <ModalProductModel
          open={openModal}
          handleCloseModal={() => {
            handleCloseModal();
          }}
          formMode={formMode}
          reFetchModels={async () => {
            await reFetchModels();
          }}
          initialValues={initialValues}
        />
      )}
      {printList !== undefined && (
        <ModalProductPrint
          open={openModal}
          handleCloseModal={() => {
            handleCloseModal();
          }}
          formMode={formMode}
          initialValues={initialValues}
          handleReFetchPrint={() => {
            if (handleReFetchPrint) {
              handleReFetchPrint();
            }
          }}
          printList={printList}
        />
      )}
      {modelList === undefined && printList === undefined && (
        <ModalProductMaterial
          open={openModal}
          handleCloseModal={() => {
            handleCloseModal();
          }}
          formMode={formMode}
          data={data}
          initialValues={initialValues}
        />
      )}
      <ScrollBarStyled>
        {modelList !== undefined && (
          <TableProductAttributeWrapStyle>
            <TableProductAttributeStyle $height={tableHeight}>
              <Table ref={tableRef}>
                <TableHead>
                  <TableRow>
                    <TableCell>รายการ</TableCell>
                    <TableCell>รหัสโมเดล</TableCell>
                    <TableCell>จัดการ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow className="table-space">
                    {[...Array(4)].map((_, index) => (
                      <TableCell key={index} />
                    ))}
                  </TableRow>
                  {!isEmpty(modelList) ? (
                    modelList.map((item: any, index: number) => {
                      return (
                        <TableRow key={index}>
                          <TableCell>
                            <div className="image-with-data">
                              <div className="image-wrap">
                                <Image
                                  src={
                                    item.imageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  width={80}
                                  height={80}
                                  alt=""
                                />
                              </div>
                              {item.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>{'n/a'}</div>
                          </TableCell>
                          <TableCell>
                            <PopoverAction
                              triggerElement={
                                <div className="kebab">
                                  <div className="dot" />
                                </div>
                              }
                              customItems={[
                                {
                                  IconElement: () => <SvgEditIcon />,
                                  title: 'แก้ไข',
                                  onAction: () => {
                                    makeInitialData(item);
                                  },
                                },
                                {
                                  IconElement: () => <SvgDeleteIcon />,
                                  title: 'ลบ',
                                  onAction: () => {
                                    handleRemove(item);
                                  },
                                },
                              ]}
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : isNull(modelList) ? (
                    <TableRow className="blank">
                      <TableCell>
                        <div className="blank-text">ไม่มีรายการ</div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    <TableRow className="blank">
                      <TableCell>
                        <div className="blank-text">กำลังโหลด</div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableProductAttributeStyle>
          </TableProductAttributeWrapStyle>
        )}
        {modelList === undefined && printList === undefined && (
          <TableProductAttributeWrapStyle>
            <TableProductAttributeStyle $height={tableHeight}>
              <Table ref={tableRef}>
                <TableHead>
                  <TableRow>
                    <TableCell>รายการ</TableCell>
                    <TableCell>ประเภท</TableCell>
                    <TableCell>การใช้งาน</TableCell>
                    <TableCell>จัดการ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow className="table-space">
                    {[...Array(4)].map((_, index) => (
                      <TableCell key={index} />
                    ))}
                  </TableRow>
                  {(() => {
                    if (data && !isEmpty(data.productConfig)) {
                      return data.productConfig.map(
                        (item: any, index: number) => {
                          return (
                            <TableRow key={index}>
                              <TableCell>
                                <div className="image-with-data">
                                  <div className="image-wrap">
                                    <Image
                                      src={
                                        item.imageUrl ||
                                        '/images/product/empty-product.svg'
                                      }
                                      width={80}
                                      height={80}
                                      alt=""
                                    />
                                  </div>
                                  {item.subMaterialName}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div>{item.materialName}</div>
                              </TableCell>
                              <TableCell>
                                <div className="count-chip">
                                  {numberWithCommas(item.count)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <PopoverAction
                                  triggerElement={
                                    <div className="kebab">
                                      <div className="dot" />
                                    </div>
                                  }
                                  customItems={[
                                    {
                                      IconElement: () => <SvgEditIcon />,
                                      title: 'แก้ไข',
                                      onAction: () => {
                                        makeInitialData(item);
                                      },
                                    },
                                    {
                                      IconElement: () => <SvgDeleteIcon />,
                                      title: 'ลบ',
                                      onAction: () => {
                                        handleRemove(item);
                                      },
                                    },
                                  ]}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        }
                      );
                    }
                    return (
                      <TableRow className="blank">
                        <TableCell>
                          <div className="blank-text">ไม่มีรายการ</div>
                        </TableCell>
                      </TableRow>
                    );
                  })()}
                  {isEmpty(productAttributes) && (
                    <TableRow className="blank">
                      <TableCell>
                        <div className="blank-text">กำลังโหลด</div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableProductAttributeStyle>
          </TableProductAttributeWrapStyle>
        )}
        {printList !== undefined && (
          <TableProductAttributeWrapStyle>
            <TableProductAttributeStyle $height={tableHeight}>
              <Table ref={tableRef}>
                <TableHead>
                  <TableRow>
                    <TableCell>ระบบการพิมพ์</TableCell>
                    <TableCell>โหมดสี</TableCell>
                    {/* <TableCell>การใช้งาน</TableCell> */}
                    <TableCell>จัดการ</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow className="table-space">
                    {[...Array(4)].map((_, index) => (
                      <TableCell key={index} />
                    ))}
                  </TableRow>
                  {(() => {
                    if (!isEmpty(printList)) {
                      return printList.map((item: any, index: number) => {
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <div className="image-with-data">
                                <div className="image-wrap">
                                  <Image
                                    src={
                                      item.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    width={80}
                                    height={80}
                                    alt=""
                                  />
                                </div>
                                {item.printSystemName}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="color-chip">
                                <Image
                                  src={
                                    item.printColorImageUrl ||
                                    '/images/product/empty-product.svg'
                                  }
                                  width={48}
                                  height={48}
                                  alt=""
                                />
                                <span>{item.printColorName}</span>
                              </div>
                            </TableCell>
                            {/* <TableCell> */}
                            {/*  <div className="count-chip"> */}
                            {/*    {numberWithCommas(item.countColor) || 1} */}
                            {/*  </div> */}
                            {/* </TableCell> */}
                            <TableCell>
                              <PopoverAction
                                triggerElement={
                                  <div className="kebab">
                                    <div className="dot" />
                                  </div>
                                }
                                customItems={[
                                  // {
                                  //   IconElement: () => <SvgEditIcon />,
                                  //   title: 'แก้ไข',
                                  //   onAction: () => {
                                  //     makeInitialData(item);
                                  //   },
                                  // },
                                  {
                                    IconElement: () => <SvgDeleteIcon />,
                                    title: 'ลบ',
                                    onAction: () => {
                                      handleRemove(item);
                                    },
                                  },
                                ]}
                              />
                              {/* <KebabTable */}
                              {/*  item={item} */}
                              {/*  handleRemove={(item: any) => { */}
                              {/*    handleRemove(item); */}
                              {/*  }} */}
                              {/*  isEdit={{ */}
                              {/*    status: true, */}
                              {/*    url: '#', */}
                              {/*    action: () => { */}
                              {/*      makeInitialData(item); */}
                              {/*    }, */}
                              {/*  }} */}
                              {/*  isRemove={true} */}
                              {/* /> */}
                            </TableCell>
                          </TableRow>
                        );
                      });
                    }
                    if (printList === null) {
                      return (
                        <TableRow className="blank">
                          <TableCell>
                            <div className="blank-text">ไม่มีรายการ</div>
                          </TableCell>
                        </TableRow>
                      );
                    }
                    if (isEmpty(printList)) {
                      return (
                        <TableRow className="blank">
                          <TableCell>
                            <div className="blank-text">กำลังโหลด</div>
                          </TableCell>
                        </TableRow>
                      );
                    }
                  })()}
                </TableBody>
              </Table>
            </TableProductAttributeStyle>
          </TableProductAttributeWrapStyle>
        )}
      </ScrollBarStyled>
    </>
  );
};

export default TableCreatePurchaseOrder;
