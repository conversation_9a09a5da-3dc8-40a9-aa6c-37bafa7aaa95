import { Menu } from '@mui/icons-material';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '@/store';
import { setShowAside, showAsideSelector } from '@/store/features/layout';
import styled from 'styled-components';

const HeaderStyle = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: white;
  border: 1px solid #eee;
  width: 100%;
  height: 60px;
  display: none;
  > div {
    padding: 0 15px;
  }
  @media screen and (max-width: 820px) {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
`;
export default function Header() {
  const { showAside } = useAppSelector(showAsideSelector);
  const dispatch = useDispatch();
  return (
    <HeaderStyle>
      <div className="logo">HON APP</div>
      <div
        className="menu-btn"
        onClick={() => dispatch(setShowAside(!showAside))}
      >
        <Menu />
      </div>
    </HeaderStyle>
  );
}
