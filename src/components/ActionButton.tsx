import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';

export const ActionButtonStyle = styled(Button)<{
  $borderRadius: string | undefined;
  $bgColor: string | undefined;
}>`
  background: ${({ $bgColor }) => $bgColor} !important;
  height: 40px !important;
  font-size: 14px !important;
  border-radius: ${({ $borderRadius }) => $borderRadius || '8px'} !important;
  box-shadow: none !important;
  .group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    column-gap: 6px;
    overflow: visible !important;
    .icon {
      display: flex;
      align-items: center;
      margin-left: -8px;
      * {
        font-size: 20px;
      }
    }
    .text {
      white-space: nowrap;
    }
  }
`;
type ActionButtonProps = {
  variant: 'outlined' | 'contained';
  color: 'blueGrey' | 'Hon' | 'dark';
  icon?: ReactNode;
  text: string;
  borderRadius?: string | undefined;
  fullWidth?: boolean;
  disabled?: boolean;
  onClick?: any;
  bgColor?: string;
};
const ActionButton = (props: ActionButtonProps) => {
  const {
    variant,
    color,
    icon,
    text,
    fullWidth,
    borderRadius,
    disabled,
    onClick,
    bgColor,
  } = props;
  return (
    <ActionButtonStyle
      $borderRadius={borderRadius}
      $bgColor={bgColor}
      variant={variant}
      color={color}
      fullWidth={fullWidth || false}
      disabled={disabled}
      onClick={onClick}
    >
      <div className="group">
        {icon && (
          <div
            className="icon"
            style={{
              filter: disabled ? 'opacity(0.4)' : 'none',
            }}
          >
            {icon}
          </div>
        )}
        <div className="text leading-4">{text}</div>
      </div>
    </ActionButtonStyle>
  );
};

export default ActionButton;
