import { LoadingFadein } from '@/styles/share.styled';
import styled from 'styled-components';

const ContainerStyle = styled.div<{ width: string | null }>`
  padding: 10px;
  position: relative;
  ${({ width }) => width && `width: ${width || '100%'}`};
  max-width: 100% !important;
  animation: ${LoadingFadein} 0.3s ease-in;
`;

type ContainerProps = {
  width?: string;
  children: React.ReactNode;
};
export default function Container({ width, children }: ContainerProps) {
  return <ContainerStyle width={width || null}>{children}</ContainerStyle>;
}
