const SvgSettingIcon = ({ fill }: { fill?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
    >
      <path
        d="M14.01 21.5H10.9812C10.7568 21.5 10.5626 21.4246 10.3985 21.274C10.2344 21.1233 10.1356 20.9358 10.1023 20.7115L9.81194 18.4538C9.54399 18.3641 9.2693 18.2384 8.98789 18.0769C8.70647 17.9153 8.45486 17.7422 8.23306 17.5576L6.14461 18.4384C5.93051 18.5217 5.71801 18.5301 5.50711 18.4634C5.29621 18.3967 5.1305 18.2647 5.00999 18.0673L3.50619 15.4481C3.38567 15.2506 3.34881 15.0397 3.39561 14.8154C3.4424 14.591 3.55617 14.4102 3.73694 14.2731L5.54464 12.9058C5.52155 12.757 5.5052 12.6077 5.49559 12.4577C5.48597 12.3077 5.48116 12.1583 5.48116 12.0096C5.48116 11.8673 5.48597 11.7227 5.49559 11.5759C5.5052 11.4291 5.52155 11.2686 5.54464 11.0942L3.73694 9.72688C3.55617 9.58971 3.4424 9.40894 3.39561 9.18458C3.34881 8.96023 3.38567 8.74934 3.50619 8.5519L5.00999 5.95195C5.12025 5.74425 5.2818 5.60963 5.49461 5.5481C5.70743 5.48657 5.92089 5.49747 6.13499 5.5808L8.22344 6.45195C8.46447 6.26092 8.72185 6.08623 8.99556 5.9279C9.2693 5.76955 9.53821 5.6423 9.80231 5.54615L10.1023 3.28848C10.1356 3.06411 10.2344 2.87661 10.3985 2.72598C10.5626 2.57533 10.7568 2.5 10.9812 2.5H14.01C14.2343 2.5 14.4301 2.57533 14.5974 2.72598C14.7648 2.87661 14.8651 3.06411 14.8984 3.28848L15.1888 5.55578C15.4888 5.66474 15.7603 5.79198 16.0032 5.9375C16.2462 6.08302 16.4914 6.2545 16.7388 6.45195L18.8657 5.5808C19.0798 5.49747 19.2907 5.48753 19.4984 5.55098C19.7061 5.61444 19.8702 5.74489 19.9907 5.94233L21.4945 8.5519C21.6151 8.74934 21.6519 8.96023 21.6051 9.18458C21.5583 9.40894 21.4446 9.58971 21.2638 9.72688L19.4176 11.123C19.4535 11.2846 19.4731 11.4355 19.4763 11.5759C19.4795 11.7163 19.4811 11.8577 19.4811 12C19.4811 12.1359 19.4779 12.274 19.4715 12.4144C19.4651 12.5548 19.442 12.7154 19.4022 12.8962L21.2292 14.2731C21.4163 14.4038 21.5317 14.583 21.5753 14.8106C21.6189 15.0381 21.5805 15.2506 21.4599 15.4481L19.9561 18.0519C19.8356 18.2493 19.6673 18.3823 19.4513 18.4509C19.2353 18.5195 19.0202 18.5121 18.8061 18.4288L16.7388 17.548C16.4914 17.7455 16.2388 17.9201 15.9811 18.0721C15.7234 18.224 15.4593 18.348 15.1888 18.4442L14.8984 20.7115C14.8651 20.9358 14.7648 21.1233 14.5974 21.274C14.4301 21.4246 14.2343 21.5 14.01 21.5ZM12.5119 15C13.3439 15 14.0519 14.708 14.6359 14.124C15.2199 13.54 15.5119 12.832 15.5119 12C15.5119 11.1679 15.2199 10.4599 14.6359 9.87595C14.0519 9.29198 13.3439 9 12.5119 9C11.6696 9 10.959 9.29198 10.3802 9.87595C9.80135 10.4599 9.51194 11.1679 9.51194 12C9.51194 12.832 9.80135 13.54 10.3802 14.124C10.959 14.708 11.6696 15 12.5119 15ZM12.5119 13.5C12.0952 13.5 11.7411 13.3541 11.4494 13.0625C11.1577 12.7708 11.0119 12.4166 11.0119 12C11.0119 11.5833 11.1577 11.2291 11.4494 10.9375C11.7411 10.6458 12.0952 10.5 12.5119 10.5C12.9286 10.5 13.2827 10.6458 13.5744 10.9375C13.8661 11.2291 14.0119 11.5833 14.0119 12C14.0119 12.4166 13.8661 12.7708 13.5744 13.0625C13.2827 13.3541 12.9286 13.5 12.5119 13.5ZM11.5004 20H13.4657L13.8254 17.3211C14.3356 17.1878 14.802 16.9984 15.2244 16.7529C15.6468 16.5074 16.0542 16.1917 16.4465 15.8058L18.9311 16.85L19.9157 15.15L17.7465 13.5154C17.8298 13.2564 17.8866 13.0025 17.9167 12.7538C17.9468 12.5051 17.9619 12.2538 17.9619 12C17.9619 11.7397 17.9468 11.4884 17.9167 11.2461C17.8866 11.0038 17.8298 10.7564 17.7465 10.5038L19.935 8.84998L18.9504 7.14998L16.4369 8.2096C16.1023 7.8519 15.7013 7.53588 15.234 7.26153C14.7667 6.98716 14.2939 6.79293 13.8157 6.67883L13.5004 3.99998H11.5157L11.185 6.6692C10.6747 6.78972 10.2036 6.97433 9.77151 7.22305C9.33946 7.47177 8.92729 7.79228 8.53499 8.1846L6.05036 7.14998L5.06574 8.84998L7.22536 10.4596C7.14203 10.6968 7.0837 10.9436 7.05036 11.2C7.01703 11.4564 7.00036 11.7263 7.00036 12.0096C7.00036 12.2699 7.01703 12.525 7.05036 12.775C7.0837 13.025 7.13882 13.2718 7.21574 13.5154L5.06574 15.15L6.05036 16.85L8.52536 15.8C8.90485 16.1897 9.31061 16.509 9.74266 16.7577C10.1747 17.0064 10.6523 17.1974 11.1754 17.3308L11.5004 20Z"
        fill={fill || '#263238'}
      />
    </svg>
  );
};

export default SvgSettingIcon;
