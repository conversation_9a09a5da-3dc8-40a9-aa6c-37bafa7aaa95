export default function SvgPaperCancelModalIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
    >
      <mask
        id="mask0_23468_51773"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="48"
        height="48"
      >
        <rect width="48" height="48" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_23468_51773)">
        <path
          d="M38.0961 38.2345L34.9 41.4C34.623 41.6769 34.2801 41.8236 33.8711 41.8403C33.4621 41.857 33.1025 41.7102 32.7923 41.4C32.5026 41.1102 32.3577 40.7589 32.3577 40.3461C32.3577 39.9333 32.5026 39.582 32.7923 39.2923L35.9577 36.0961L32.7923 32.9C32.5154 32.623 32.3686 32.2801 32.3519 31.8711C32.3352 31.4621 32.482 31.1025 32.7923 30.7923C33.082 30.5026 33.4333 30.3577 33.8461 30.3577C34.2589 30.3577 34.6102 30.5026 34.9 30.7923L38.0961 33.9577L41.2923 30.7923C41.5692 30.5154 41.9121 30.3686 42.3211 30.3519C42.7301 30.3352 43.0897 30.482 43.4 30.7923C43.6897 31.082 43.8346 31.4333 43.8346 31.8461C43.8346 32.2589 43.6897 32.6102 43.4 32.9L40.2345 36.0961L43.4 39.2923C43.6769 39.5692 43.8236 39.9121 43.8403 40.3211C43.857 40.7301 43.7102 41.0897 43.4 41.4C43.1102 41.6897 42.7589 41.8346 42.3461 41.8346C41.9333 41.8346 41.582 41.6897 41.2923 41.4L38.0961 38.2345ZM11.9999 42.9999C10.6026 42.9999 9.41987 42.5159 8.4519 41.548C7.48397 40.58 7 39.3974 7 38V35.8461C7 34.8487 7.35322 33.9968 8.05965 33.2904C8.76605 32.584 9.61797 32.2308 10.6154 32.2308H13V8.6154C13 7.61797 13.3532 6.76605 14.0596 6.05965C14.766 5.35322 15.618 5 16.6154 5H37.3845C38.3819 5 39.2339 5.35322 39.9403 6.05965C40.6467 6.76605 40.9999 7.61797 40.9999 8.6154V24.6154C40.9999 25.041 40.8563 25.3974 40.5691 25.6846C40.2819 25.9718 39.9255 26.1154 39.4999 26.1154C39.0743 26.1154 38.7179 25.9718 38.4307 25.6846C38.1435 25.3974 38 25.041 38 24.6154V8.6154C38 8.4359 37.9422 8.28845 37.8268 8.17305C37.7114 8.05765 37.564 7.99995 37.3845 7.99995H16.6154C16.4359 7.99995 16.2885 8.05765 16.173 8.17305C16.0576 8.28845 15.9999 8.4359 15.9999 8.6154V32.2308H26.9424C27.368 32.2308 27.7243 32.3743 28.0115 32.6615C28.2987 32.9487 28.4423 33.3051 28.4423 33.7307C28.4423 34.1563 28.2987 34.5127 28.0115 34.7999C27.7243 35.0871 27.368 35.2307 26.9424 35.2307H10.6154C10.4359 35.2307 10.2885 35.2884 10.173 35.4038C10.0576 35.5192 9.99995 35.6666 9.99995 35.8461V38C9.99995 38.5666 10.1916 39.0416 10.5749 39.425C10.9583 39.8083 11.4333 40 11.9999 40H26.9424C27.368 40 27.7243 40.1435 28.0115 40.4307C28.2987 40.7179 28.4423 41.0743 28.4423 41.4999C28.4423 41.9255 28.2987 42.2819 28.0115 42.5691C27.7243 42.8563 27.368 42.9999 26.9424 42.9999H11.9999ZM28.3077 40H9.99995H27.7231H27.6808H28.3077ZM19.8845 17.3845C19.4589 17.3845 19.1025 17.2409 18.8154 16.9538C18.5282 16.6666 18.3846 16.3102 18.3846 15.8846C18.3846 15.4589 18.5282 15.1025 18.8154 14.8154C19.1025 14.5282 19.4589 14.3846 19.8845 14.3846H34.1154C34.541 14.3846 34.8974 14.5282 35.1846 14.8154C35.4717 15.1025 35.6153 15.4589 35.6153 15.8846C35.6153 16.3102 35.4717 16.6666 35.1846 16.9538C34.8974 17.2409 34.541 17.3845 34.1154 17.3845H19.8845ZM19.8845 23.1538C19.4589 23.1538 19.1025 23.0102 18.8154 22.723C18.5282 22.4358 18.3846 22.0794 18.3846 21.6538C18.3846 21.2281 18.5282 20.8717 18.8154 20.5846C19.1025 20.2974 19.4589 20.1538 19.8845 20.1538H34.1154C34.541 20.1538 34.8974 20.2974 35.1846 20.5846C35.4717 20.8717 35.6153 21.2281 35.6153 21.6538C35.6153 22.0794 35.4717 22.4358 35.1846 22.723C34.8974 23.0102 34.541 23.1538 34.1154 23.1538H19.8845Z"
          fill="#D32F2F"
        />
      </g>
    </svg>
  );
}
