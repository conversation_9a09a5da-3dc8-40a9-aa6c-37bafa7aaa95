const SvgDatePickerIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <mask
        id="mask0_24057_25420"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width="24" height="24" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_24057_25420)">
        <path
          d="M5.30775 21.5C4.80258 21.5 4.375 21.325 4.025 20.975C3.675 20.625 3.5 20.1974 3.5 19.6923V6.30777C3.5 5.8026 3.675 5.37502 4.025 5.02502C4.375 4.67502 4.80258 4.50002 5.30775 4.50002H6.69225V3.15377C6.69225 2.9346 6.76567 2.7516 6.9125 2.60477C7.05933 2.4581 7.24233 2.38477 7.4615 2.38477C7.68083 2.38477 7.86383 2.4581 8.0105 2.60477C8.15733 2.7516 8.23075 2.9346 8.23075 3.15377V4.50002H15.8077V3.13477C15.8077 2.92193 15.8795 2.74368 16.023 2.60002C16.1667 2.45652 16.3449 2.38477 16.5577 2.38477C16.7706 2.38477 16.9487 2.45652 17.0922 2.60002C17.2359 2.74368 17.3077 2.92193 17.3077 3.13477V4.50002H18.6923C19.1974 4.50002 19.625 4.67502 19.975 5.02502C20.325 5.37502 20.5 5.8026 20.5 6.30777V19.6923C20.5 20.1974 20.325 20.625 19.975 20.975C19.625 21.325 19.1974 21.5 18.6923 21.5H5.30775ZM5.30775 20H18.6923C18.7692 20 18.8398 19.9679 18.9038 19.9038C18.9679 19.8398 19 19.7693 19 19.6923V10.3078H5V19.6923C5 19.7693 5.03208 19.8398 5.09625 19.9038C5.16025 19.9679 5.23075 20 5.30775 20ZM12 14.077C11.7552 14.077 11.5465 13.9908 11.374 13.8183C11.2017 13.6459 11.1155 13.4373 11.1155 13.1923C11.1155 12.9474 11.2017 12.7388 11.374 12.5663C11.5465 12.3939 11.7552 12.3078 12 12.3078C12.2448 12.3078 12.4535 12.3939 12.626 12.5663C12.7983 12.7388 12.8845 12.9474 12.8845 13.1923C12.8845 13.4373 12.7983 13.6459 12.626 13.8183C12.4535 13.9908 12.2448 14.077 12 14.077ZM8 14.077C7.75517 14.077 7.5465 13.9908 7.374 13.8183C7.20167 13.6459 7.1155 13.4373 7.1155 13.1923C7.1155 12.9474 7.20167 12.7388 7.374 12.5663C7.5465 12.3939 7.75517 12.3078 8 12.3078C8.24483 12.3078 8.4535 12.3939 8.626 12.5663C8.79833 12.7388 8.8845 12.9474 8.8845 13.1923C8.8845 13.4373 8.79833 13.6459 8.626 13.8183C8.4535 13.9908 8.24483 14.077 8 14.077ZM16 14.077C15.7552 14.077 15.5465 13.9908 15.374 13.8183C15.2017 13.6459 15.1155 13.4373 15.1155 13.1923C15.1155 12.9474 15.2017 12.7388 15.374 12.5663C15.5465 12.3939 15.7552 12.3078 16 12.3078C16.2448 12.3078 16.4535 12.3939 16.626 12.5663C16.7983 12.7388 16.8845 12.9474 16.8845 13.1923C16.8845 13.4373 16.7983 13.6459 16.626 13.8183C16.4535 13.9908 16.2448 14.077 16 14.077ZM12 18C11.7552 18 11.5465 17.9138 11.374 17.7413C11.2017 17.5689 11.1155 17.3603 11.1155 17.1155C11.1155 16.8705 11.2017 16.6618 11.374 16.4895C11.5465 16.317 11.7552 16.2308 12 16.2308C12.2448 16.2308 12.4535 16.317 12.626 16.4895C12.7983 16.6618 12.8845 16.8705 12.8845 17.1155C12.8845 17.3603 12.7983 17.5689 12.626 17.7413C12.4535 17.9138 12.2448 18 12 18ZM8 18C7.75517 18 7.5465 17.9138 7.374 17.7413C7.20167 17.5689 7.1155 17.3603 7.1155 17.1155C7.1155 16.8705 7.20167 16.6618 7.374 16.4895C7.5465 16.317 7.75517 16.2308 8 16.2308C8.24483 16.2308 8.4535 16.317 8.626 16.4895C8.79833 16.6618 8.8845 16.8705 8.8845 17.1155C8.8845 17.3603 8.79833 17.5689 8.626 17.7413C8.4535 17.9138 8.24483 18 8 18ZM16 18C15.7552 18 15.5465 17.9138 15.374 17.7413C15.2017 17.5689 15.1155 17.3603 15.1155 17.1155C15.1155 16.8705 15.2017 16.6618 15.374 16.4895C15.5465 16.317 15.7552 16.2308 16 16.2308C16.2448 16.2308 16.4535 16.317 16.626 16.4895C16.7983 16.6618 16.8845 16.8705 16.8845 17.1155C16.8845 17.3603 16.7983 17.5689 16.626 17.7413C16.4535 17.9138 16.2448 18 16 18Z"
          fill="#263238"
        />
      </g>
    </svg>
  );
};

export default SvgDatePickerIcon;
