import { SvgIcon } from '@mui/material';

type propsType = {
  color: string;
};
const SvgCursorIcon = ({ color }: propsType) => {
  return (
    <SvgIcon>
      <svg
        width="9"
        height="13"
        viewBox="0 0 9 13"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.1 12.2247C5.84444 12.3469 5.58889 12.3608 5.33333 12.2664C5.07778 12.1719 4.88889 11.9969 4.76667 11.7414L2.76667 7.44136L1.21667 9.60803C1.02778 9.87469 0.777778 9.95803 0.466667 9.85803C0.155556 9.75803 0 9.54691 0 9.22469V0.674691C0 0.396914 0.125 0.196914 0.375 0.0746914C0.625 -0.0475309 0.861111 -0.0197531 1.08333 0.158025L7.81667 5.45802C8.07222 5.64691 8.14722 5.89136 8.04167 6.19136C7.93611 6.49136 7.72222 6.64136 7.4 6.64136H4.6L6.58333 10.8914C6.70556 11.1469 6.71944 11.4025 6.625 11.658C6.53056 11.9136 6.35556 12.1025 6.1 12.2247Z"
          fill={color}
        />
      </svg>
    </SvgIcon>
  );
};

export default SvgCursorIcon;
