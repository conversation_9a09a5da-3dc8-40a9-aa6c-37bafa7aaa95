const SvgScheduleFillIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12.75 11.6961V7.74995C12.75 7.53747 12.6781 7.35935 12.5343 7.2156C12.3904 7.07185 12.2122 6.99998 11.9997 6.99998C11.7871 6.99998 11.609 7.07185 11.4654 7.2156C11.3218 7.35935 11.25 7.53747 11.25 7.74995V11.9269C11.25 12.0446 11.2718 12.1587 11.3154 12.269C11.359 12.3794 11.4276 12.4814 11.5212 12.575L14.9462 16C15.0846 16.1384 15.2587 16.2093 15.4683 16.2125C15.6779 16.2157 15.8551 16.1448 16 16C16.1448 15.8551 16.2173 15.6795 16.2173 15.4731C16.2173 15.2667 16.1448 15.091 16 14.9462L12.75 11.6961ZM12.0017 21.5C10.6877 21.5 9.45268 21.2506 8.29655 20.752C7.1404 20.2533 6.13472 19.5765 5.2795 18.7217C4.42427 17.8669 3.74721 16.8616 3.24833 15.706C2.74944 14.5504 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45268 3.248 8.29655C3.74667 7.1404 4.42342 6.13472 5.27825 5.2795C6.1331 4.42427 7.13834 3.74721 8.29398 3.24833C9.44959 2.74944 10.6844 2.5 11.9983 2.5C13.3122 2.5 14.5473 2.74933 15.7034 3.248C16.8596 3.74667 17.8652 4.42342 18.7205 5.27825C19.5757 6.1331 20.2527 7.13834 20.7516 8.29398C21.2505 9.44959 21.5 10.6844 21.5 11.9983C21.5 13.3122 21.2506 14.5473 20.752 15.7034C20.2533 16.8596 19.5765 17.8652 18.7217 18.7205C17.8669 19.5757 16.8616 20.2527 15.706 20.7516C14.5504 21.2505 13.3156 21.5 12.0017 21.5Z"
        fill="#263238"
      />
      <path
        d="M12.75 11.6961V7.74995C12.75 7.53747 12.6781 7.35935 12.5343 7.2156C12.3904 7.07185 12.2122 6.99998 11.9997 6.99998C11.7871 6.99998 11.609 7.07185 11.4654 7.2156C11.3218 7.35935 11.25 7.53747 11.25 7.74995V11.9269C11.25 12.0446 11.2718 12.1587 11.3154 12.269C11.359 12.3794 11.4276 12.4814 11.5212 12.575L14.9462 16C15.0846 16.1384 15.2587 16.2093 15.4683 16.2125C15.6779 16.2157 15.8551 16.1448 16 16C16.1448 15.8551 16.2173 15.6795 16.2173 15.4731C16.2173 15.2667 16.1448 15.091 16 14.9462L12.75 11.6961ZM12.0017 21.5C10.6877 21.5 9.45268 21.2506 8.29655 20.752C7.1404 20.2533 6.13472 19.5765 5.2795 18.7217C4.42427 17.8669 3.74721 16.8616 3.24833 15.706C2.74944 14.5504 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45268 3.248 8.29655C3.74667 7.1404 4.42342 6.13472 5.27825 5.2795C6.1331 4.42427 7.13834 3.74721 8.29398 3.24833C9.44959 2.74944 10.6844 2.5 11.9983 2.5C13.3122 2.5 14.5473 2.74933 15.7034 3.248C16.8596 3.74667 17.8652 4.42342 18.7205 5.27825C19.5757 6.1331 20.2527 7.13834 20.7516 8.29398C21.2505 9.44959 21.5 10.6844 21.5 11.9983C21.5 13.3122 21.2506 14.5473 20.752 15.7034C20.2533 16.8596 19.5765 17.8652 18.7217 18.7205C17.8669 19.5757 16.8616 20.2527 15.706 20.7516C14.5504 21.2505 13.3156 21.5 12.0017 21.5Z"
        fill="black"
        fill-opacity="0.2"
      />
      <path
        d="M12.75 11.6961V7.74995C12.75 7.53747 12.6781 7.35935 12.5343 7.2156C12.3904 7.07185 12.2122 6.99998 11.9997 6.99998C11.7871 6.99998 11.609 7.07185 11.4654 7.2156C11.3218 7.35935 11.25 7.53747 11.25 7.74995V11.9269C11.25 12.0446 11.2718 12.1587 11.3154 12.269C11.359 12.3794 11.4276 12.4814 11.5212 12.575L14.9462 16C15.0846 16.1384 15.2587 16.2093 15.4683 16.2125C15.6779 16.2157 15.8551 16.1448 16 16C16.1448 15.8551 16.2173 15.6795 16.2173 15.4731C16.2173 15.2667 16.1448 15.091 16 14.9462L12.75 11.6961ZM12.0017 21.5C10.6877 21.5 9.45268 21.2506 8.29655 20.752C7.1404 20.2533 6.13472 19.5765 5.2795 18.7217C4.42427 17.8669 3.74721 16.8616 3.24833 15.706C2.74944 14.5504 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45268 3.248 8.29655C3.74667 7.1404 4.42342 6.13472 5.27825 5.2795C6.1331 4.42427 7.13834 3.74721 8.29398 3.24833C9.44959 2.74944 10.6844 2.5 11.9983 2.5C13.3122 2.5 14.5473 2.74933 15.7034 3.248C16.8596 3.74667 17.8652 4.42342 18.7205 5.27825C19.5757 6.1331 20.2527 7.13834 20.7516 8.29398C21.2505 9.44959 21.5 10.6844 21.5 11.9983C21.5 13.3122 21.2506 14.5473 20.752 15.7034C20.2533 16.8596 19.5765 17.8652 18.7217 18.7205C17.8669 19.5757 16.8616 20.2527 15.706 20.7516C14.5504 21.2505 13.3156 21.5 12.0017 21.5Z"
        fill="black"
        fill-opacity="0.2"
      />
      <path
        d="M12.75 11.6961V7.74995C12.75 7.53747 12.6781 7.35935 12.5343 7.2156C12.3904 7.07185 12.2122 6.99998 11.9997 6.99998C11.7871 6.99998 11.609 7.07185 11.4654 7.2156C11.3218 7.35935 11.25 7.53747 11.25 7.74995V11.9269C11.25 12.0446 11.2718 12.1587 11.3154 12.269C11.359 12.3794 11.4276 12.4814 11.5212 12.575L14.9462 16C15.0846 16.1384 15.2587 16.2093 15.4683 16.2125C15.6779 16.2157 15.8551 16.1448 16 16C16.1448 15.8551 16.2173 15.6795 16.2173 15.4731C16.2173 15.2667 16.1448 15.091 16 14.9462L12.75 11.6961ZM12.0017 21.5C10.6877 21.5 9.45268 21.2506 8.29655 20.752C7.1404 20.2533 6.13472 19.5765 5.2795 18.7217C4.42427 17.8669 3.74721 16.8616 3.24833 15.706C2.74944 14.5504 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45268 3.248 8.29655C3.74667 7.1404 4.42342 6.13472 5.27825 5.2795C6.1331 4.42427 7.13834 3.74721 8.29398 3.24833C9.44959 2.74944 10.6844 2.5 11.9983 2.5C13.3122 2.5 14.5473 2.74933 15.7034 3.248C16.8596 3.74667 17.8652 4.42342 18.7205 5.27825C19.5757 6.1331 20.2527 7.13834 20.7516 8.29398C21.2505 9.44959 21.5 10.6844 21.5 11.9983C21.5 13.3122 21.2506 14.5473 20.752 15.7034C20.2533 16.8596 19.5765 17.8652 18.7217 18.7205C17.8669 19.5757 16.8616 20.2527 15.706 20.7516C14.5504 21.2505 13.3156 21.5 12.0017 21.5Z"
        fill="black"
        fill-opacity="0.2"
      />
    </svg>
  );
};

export default SvgScheduleFillIcon;
