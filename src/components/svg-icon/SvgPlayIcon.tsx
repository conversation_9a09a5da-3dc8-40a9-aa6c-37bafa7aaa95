import { SvgIcon } from '@mui/material';

type propsType = {
  color: string;
};
const SvgPlayIcon = ({ color }: propsType) => {
  return (
    <SvgIcon>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.33203 11.4495V4.54948C5.33203 4.36059 5.3987 4.20226 5.53203 4.07448C5.66536 3.9467 5.82092 3.88281 5.9987 3.88281C6.05425 3.88281 6.11259 3.89115 6.1737 3.90781C6.23481 3.92448 6.29314 3.94948 6.3487 3.98281L11.782 7.43281C11.882 7.49948 11.957 7.58281 12.007 7.68281C12.057 7.78281 12.082 7.88837 12.082 7.99948C12.082 8.11059 12.057 8.21615 12.007 8.31615C11.957 8.41615 11.882 8.49948 11.782 8.56615L6.3487 12.0161C6.29314 12.0495 6.23481 12.0745 6.1737 12.0911C6.11259 12.1078 6.05425 12.1161 5.9987 12.1161C5.82092 12.1161 5.66536 12.0523 5.53203 11.9245C5.3987 11.7967 5.33203 11.6384 5.33203 11.4495Z"
          fill={color}
        />
      </svg>
    </SvgIcon>
  );
};

export default SvgPlayIcon;
