import React, { ChangeEvent, useEffect, useState } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import {
  Autocomplete,
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  InputAdornment,
  TextField,
} from '@mui/material';
import { isEmpty, isNull } from 'lodash';
import { useFormik } from 'formik';
import apiAddress from '@/services/stock/address';
import { useDebounce } from 'usehooks-ts';
import * as yup from 'yup';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';

const CreateWarehouseFormStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  animation: ${LoadingFadein} 0.3s ease-in;
  width: 100%;
  margin-top: 16px;
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 640px;
    padding: 0 24px;
    max-width: 100%;
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
  }
  .fade {
    animation: ${LoadingFadein} 0.3s ease-in;
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
  address: yup.string().required('กรุณากรอกที่อยู่'),
  zipcode: yup
    .string()
    .required('กรุณากรอกรหัสไปรษณีย์')
    .matches(/^\d{5}$/, 'รหัสไปรษณีย์ต้องมี 5 หลักเท่านั้น'),
  province: yup.object().required('กรุณาระบุ จังหวัด'),
  district: yup.object().required('กรุณาระบุ อำเภอ'),
  subDistrict: yup.object().required('กรุณาระบุ ตำบล/แขวง'),
});

type WarehouseFormProps = {
  submitting: boolean;
  disable: boolean;
  handleSubmitForm: (value: any) => void;
  initialValues?: any;
};
const WarehouseForm = ({
  submitting,
  disable,
  handleSubmitForm,
  initialValues,
}: WarehouseFormProps) => {
  const [loadingZipcode, setLoadingZipcode] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedZipcode = useDebounce<string>(keyword, 600);
  const [districtList, setDistrictList] = useState<any>([]);
  const [subDistrictList, setSubDistrictList] = useState<any>([]);
  const [init, setInit] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: !isEmpty(initialValues)
      ? {
          id: initialValues.id,
          name: initialValues.name,
          description: initialValues.description,
          address: initialValues.address,
          zipcode: initialValues.zipcode,
          province: initialValues.province,
          district: initialValues.district,
          subDistrict: initialValues.subDistrict,
          isDefault: initialValues.isDefault,
        }
      : {
          id: null,
          name: '',
          description: '',
          address: '',
          zipcode: '',
          province: '',
          district: null,
          subDistrict: null,
          isDefault: false,
        },
    validationSchema,
    onSubmit: (values: any) => {
      const sendData = {
        ...values,
        provinceId: values.province.id,
        districtId: values.district.id,
        subDistrictId: values.subDistrict.id,
      };
      delete sendData.subDistrict;
      delete sendData.district;
      delete sendData.province;
      handleSubmitForm(sendData);
    },
  });

  const handleChangeZipcode = (value: string) => {
    if (value.length === 5) {
      setLoadingZipcode(true);
    }
    formik.setFieldValue('zipcode', value);
    setKeyword(value);
  };
  useEffect(() => {
    if (debouncedZipcode.length === 5) {
      getAddressByZipcode();
    }
  }, [debouncedZipcode]);
  const getAddressByZipcode = async () => {
    const res = await apiAddress.getAddressByZipcode(formik.values.zipcode);
    if (!res.isError) {
      await formik.setFieldValue('province', res.data.province);
      setDistrictList(res.data.district);
    } else {
      formik.setFieldError('zipcode', res.message.message);
    }
    setLoadingZipcode(false);
  };
  useEffect(() => {
    setDistrictList([]);
    setSubDistrictList([]);
    if (init) {
      formik.setFieldValue('province', '');
      formik.setFieldValue('district', null);
      formik.setFieldValue('subDistrict', null);
    }
    if (
      !init &&
      !isNull(formik.values.zipcode) &&
      formik.values.zipcode.length === 5
    ) {
      getAddressByZipcode();
      setInit(true);
    }
  }, [formik.values.zipcode]);
  useEffect(() => {
    if (!isEmpty(formik.values.district)) {
      getSubDistrictByDistrictId();
    }
  }, [formik.values.district]);
  const getSubDistrictByDistrictId = async () => {
    const districtId = formik.values.district.id;
    const res = await apiAddress.getSubDistrictByDistrictId(districtId);
    if (!res.isError) {
      setSubDistrictList(res.data);
    }
  };
  const handleChangeDistrict = (event: ChangeEvent<{}>, option: any) => {
    formik.setFieldValue('district', option);
  };
  const handleChangeSubDistrict = (event: ChangeEvent<{}>, option: any) => {
    formik.setFieldValue('subDistrict', option);
  };
  const handleChangeMainWarehouse = (event: any) => {
    if (!initialValues?.isDefault) {
      const { checked } = event.target;
      formik.setFieldValue('isDefault', checked);
    }
  };
  return (
    <CreateWarehouseFormStyle>
      <form onSubmit={formik.handleSubmit}>
        <p className="field-title">ชื่อคลัง</p>
        <TextField
          type="text"
          name="name"
          placeholder="ระบุชื่อคลัง"
          value={formik.values.name}
          onChange={formik.handleChange}
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && (formik.errors.name as string)}
        />
        <p className="field-title">รายละเอียด</p>
        <TextField
          multiline
          rows={4}
          type="text"
          name="description"
          placeholder="วัตถุประสงค์"
          value={formik.values.description}
          onChange={formik.handleChange}
          error={
            formik.touched.description && Boolean(formik.errors.description)
          }
          helperText={
            formik.touched.description && (formik.errors.description as string)
          }
        />
        <p className="field-title">ที่อยู่</p>
        <TextField
          type="text"
          name="address"
          placeholder="เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ"
          value={formik.values.address}
          onChange={formik.handleChange}
          error={formik.touched.address && Boolean(formik.errors.address)}
          helperText={
            formik.touched.address && (formik.errors.address as string)
          }
        />
        <p className="field-title">รหัสไปรษณีย์</p>
        <TextField
          type="text"
          name="zipcode"
          placeholder="รหัสไปรษณีย์"
          value={formik.values.zipcode}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            handleChangeZipcode(e.target.value);
          }}
          inputProps={{ maxLength: 5 }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                {loadingZipcode && <CircularProgress size={20} />}
              </InputAdornment>
            ),
          }}
          onKeyPress={(event) => {
            if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
              event.preventDefault();
            }
          }}
          error={Boolean(formik.errors.zipcode)}
          helperText={formik.errors.zipcode as string}
        />
        {!isEmpty(formik.values.province) && (
          <div className="fade">
            <p className="field-title">จังหวัด</p>
            <TextField
              type="text"
              value={formik.values.province.name}
              disabled
            />
          </div>
        )}
        {!isEmpty(districtList) && (
          <div className="fade">
            <p className="field-title">อำเภอ</p>
            <Autocomplete
              disablePortal
              options={districtList}
              getOptionLabel={(option) => option.name}
              value={formik.values.district}
              noOptionsText={'ไม่มีข้อมูล'}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              renderInput={(params) => (
                <TextField
                  type="text"
                  placeholder="เลือก หรือกรอก"
                  name="district"
                  value={
                    formik.values.district ? formik.values.district.name : ''
                  }
                  error={
                    formik.touched.district && Boolean(formik.errors.district)
                  }
                  helperText={
                    formik.touched.district &&
                    (formik.errors.district as string)
                  }
                  {...params}
                />
              )}
              onChange={handleChangeDistrict}
            />
          </div>
        )}
        {!isEmpty(subDistrictList) && (
          <div className="fade">
            <p>ตำบล/แขวง</p>
            <Autocomplete
              disablePortal
              options={subDistrictList}
              getOptionLabel={(option) => option.name}
              value={formik.values.subDistrict}
              noOptionsText={'ไม่มีข้อมูล'}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              renderInput={(params) => (
                <TextField
                  type="text"
                  placeholder="เลือก หรือกรอก"
                  name="subDistrict"
                  value={
                    formik.values.subDistrict
                      ? formik.values.subDistrict.name
                      : ''
                  }
                  error={
                    formik.touched.subDistrict &&
                    Boolean(formik.errors.subDistrict)
                  }
                  helperText={
                    formik.touched.subDistrict &&
                    (formik.errors.subDistrict as string)
                  }
                  {...params}
                />
              )}
              onChange={handleChangeSubDistrict}
            />
          </div>
        )}
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
              checked={formik.values.isDefault}
              onChange={(event: any) => {
                handleChangeMainWarehouse(event);
              }}
              icon={<IconUnCheckbox />}
              checkedIcon={<IconCheckboxBlack />}
            />
          }
          sx={{
            marginTop: '14px',
          }}
          label="ตั้งเป็นคลังหลัก"
        />
        <Button
          type="submit"
          variant="contained"
          color="dark"
          disabled={disable}
          fullWidth
          sx={{ fontSize: '16px', margin: '24px 0 24px 0' }}
        >
          {submitting ? (
            <CircularProgress
              size={20}
              style={{
                color: 'white',
              }}
            />
          ) : (
            'บันทึก'
          )}
        </Button>
      </form>
    </CreateWarehouseFormStyle>
  );
};

export default WarehouseForm;
