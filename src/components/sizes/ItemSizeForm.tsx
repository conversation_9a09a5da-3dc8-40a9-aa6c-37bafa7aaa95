import { useFormik } from 'formik';
import React, { useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import apiDimensions from '@/services/stock/dimensions';
import _, { isEmpty } from 'lodash';
import styled from 'styled-components';

type SizeFormProps = {
  handleSubmit: (value: any) => void;
  submitting: boolean;
  disable: boolean;
  initialValues?: any;
};

type FormValues = {
  id: number | null;
  name: string | null;
  dimensionConfigId: number | string;
  dimensionId: number | string;
  materialSizeRequest: {
    materialSizeDimensionId: number | null;
    value: string | number;
  }[];
};

const ItemSizeForm = ({
  handleSubmit,
  submitting,
  disable,
  initialValues,
}: SizeFormProps) => {
  const [dimensionConfigs, setDimensionConfigs] = useState<any[]>([]);
  const [unitSizes, setUnitSizes] = useState<any[]>([]);

  const initialForms: FormValues = {
    id: null,
    name: '',
    dimensionConfigId: '',
    dimensionId: '',
    materialSizeRequest: [],
  };

  const formik = useFormik({
    initialValues: initialForms,
    validate: (values: any) => {
      const errors: any = {};
      if (!values.dimensionConfigId) {
        errors.dimensionConfigId = 'กรุณาเลือกขนาด';
      }
      if (!values.dimensionId) {
        errors.dimensionId = 'กรุณาเลือกหน่วย';
      }
      const isError: boolean = _.some(values.materialSizeRequest, (o: any) => {
        return o.value === null || o.value === '' || o.value === undefined;
      });
      if (values.materialSizeRequest?.length > 0 && isError) {
        errors.materialSizeRequest = [];
        values.materialSizeRequest.map((item: any, index: number) => {
          if (!item.value) {
            errors.materialSizeRequest.push({
              index: index,
              message: 'กรุณากรอก',
            });
          }
        });
      }
      return errors;
    },
    onSubmit: async (values: any) => {
      const unitSizesString = await dimensionConfigs
        .find((configItem: any) => configItem.id === values.dimensionConfigId)
        .dimension.find((unit: any) => unit.id === values.dimensionId).name;
      const dimensionValue = await values.materialSizeRequest.map(
        (item: any) => item.value
      );
      let dimensionCombined = '';
      if (dimensionValue.length > 1) {
        dimensionCombined = `${dimensionValue.join(' * ')} ${unitSizesString}`;
      } else {
        dimensionCombined = `${dimensionValue} ${unitSizesString}`;
      }
      values.name = dimensionCombined;
      handleSubmit(values);
    },
  });

  useEffect(() => {
    formik.resetForm();
    fetchDimensionConfig().then();
  }, []);

  useEffect(() => {
    if (!isEmpty(initialValues)) {
      formik.setFieldValue('id', initialValues.id);
      formik.setFieldValue('name', initialValues.itemSizeName);
      formik.setFieldValue(
        'dimensionConfigId',
        initialValues.dimensionConfig.id ?? ''
      );
      formik.setFieldValue('dimensionId', initialValues.dimension.id ?? '');
      const materialSizeRequest = initialValues.subItemSizeRequest?.map(
        (item: any) => {
          return {
            materialSizeDimensionId: item.materialSizeDimensionId,
            value: item.value,
          };
        }
      );
      formik.setFieldValue('materialSizeRequest', materialSizeRequest);
    }
  }, [initialValues]);

  useEffect(() => {
    const config = _.find(
      dimensionConfigs,
      (dimension) => dimension.id === formik.values.dimensionConfigId
    );
    if (config) {
      setUnitSizes(config.materialSizeDimension);
    }
  }, [formik.values.dimensionConfigId, dimensionConfigs]);

  const handleClearMaterialSizeRequest = (dimensionConfigId: number) => {
    const config = _.find(
      dimensionConfigs,
      (dimension) => dimension.id === dimensionConfigId
    );
    if (config) {
      formik.setFieldValue(
        'materialSizeRequest',
        config.materialSizeDimension.map((item: any) => {
          return {
            value: '',
            materialSizeDimensionId: item.id,
          };
        })
      );
    }
  };

  const fetchDimensionConfig = async () => {
    const response = await apiDimensions.getConfigList();
    if (response.status) {
      setDimensionConfigs(response.data);
    } else {
      setDimensionConfigs([]);
    }
  };

  const renderDynamicMessage = (index: number, label: string) => {
    if (formik.errors?.materialSizeRequest) {
      const err: any = _.find(formik.errors.materialSizeRequest, (o: any) => {
        return o.index === index;
      });
      if (err) {
        return `${err.message} ${label}`;
      }
    }
  };

  const handleInputChange =
    (_index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = Number(e.target.value);
      if (value >= 0) {
        formik.handleChange(e);
      }
    };

  const preventNegativeInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === '-') {
      e.preventDefault();
    }
  };
  // console.log(formik.values);
  // console.log(dimensionConfigs);
  // console.log('formik.values', formik.values);
  // console.log('materialSizeRequest', formik.values.materialSizeRequest);
  return (
    <form onSubmit={formik.handleSubmit}>
      <div
        className={`flex flex-col`}
        style={{
          rowGap: '24px',
          minHeight: '279px',
        }}
      >
        <div>
          <p>ขนาด</p>
          <FormControl fullWidth>
            <Select
              displayEmpty
              name="dimensionConfigId"
              value={
                !isEmpty(dimensionConfigs)
                  ? formik.values.dimensionConfigId
                  : ''
              }
              onChange={(e: any) => {
                handleClearMaterialSizeRequest(e.target.value);
                formik.setFieldValue('dimensionConfigId', e.target.value);
                formik.setFieldValue('dimensionId', '');
              }}
              error={
                formik.touched.dimensionConfigId &&
                Boolean(formik.errors.dimensionConfigId)
              }
            >
              <MenuItem disabled value="">
                <div className="text-[#78909C]">กรุณาเลือก</div>
              </MenuItem>
              {dimensionConfigs.map((data: any, index: number) => (
                <MenuItem key={index} value={data.id}>
                  {data.configName}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.dimensionConfigId &&
              formik.errors.dimensionConfigId && (
                <FormHelperText error>
                  {formik.errors.dimensionConfigId}
                </FormHelperText>
              )}
          </FormControl>
        </div>
        {formik.values.dimensionConfigId && (
          <div>
            <p className="mt-0">หน่วย</p>
            <FormControl fullWidth>
              <Select
                displayEmpty
                name="dimensionId"
                value={
                  !isEmpty(dimensionConfigs) ? formik.values.dimensionId : ''
                }
                onChange={formik.handleChange}
                error={
                  formik.touched.dimensionId &&
                  Boolean(formik.errors.dimensionId)
                }
              >
                <MenuItem disabled value="">
                  <div className="text-[#78909C]">กรุณาเลือก</div>
                </MenuItem>
                {dimensionConfigs
                  ?.find(
                    (dimensionItem: any) =>
                      dimensionItem.id === formik.values.dimensionConfigId
                  )
                  ?.dimension?.map((item: any, index: number) => {
                    return (
                      <MenuItem key={index} value={item.id}>
                        {item.name}
                      </MenuItem>
                    );
                  })}
              </Select>
              {formik.touched.dimensionId && formik.errors.dimensionId && (
                <FormHelperText error>
                  {formik.errors.dimensionId}
                </FormHelperText>
              )}
            </FormControl>
          </div>
        )}

        <DimensionGridStyle>
          {
            (() => {
              return unitSizes?.map((item: any, index) => {
                return (
                  <div key={index}>
                    <p
                      className="mt-0"
                      style={{
                        minHeight: '21px',
                      }}
                    >
                      {item.name}
                    </p>
                    <TextField
                      type="number"
                      name={`materialSizeRequest[${index}].value`}
                      value={
                        formik.values?.materialSizeRequest[index]?.value || ''
                      }
                      placeholder={`กรอก${item.name}`}
                      onChange={handleInputChange(index)}
                      onKeyDown={preventNegativeInput}
                      InputProps={{
                        endAdornment: (
                          <div className="p-[2px]">
                            {/* {getUnitSizeInfo()?.name || ''} */}
                          </div>
                        ),
                      }}
                    />
                    {formik.touched.materialSizeRequest &&
                      formik.errors?.materialSizeRequest && (
                        <FormHelperText
                          error
                          sx={{
                            margin: '4px 14px 0',
                          }}
                        >
                          {renderDynamicMessage(index, item.name)}
                        </FormHelperText>
                      )}
                  </div>
                );
              });
            })() as any
          }
        </DimensionGridStyle>
      </div>
      <Button
        type="submit"
        variant="contained"
        color="dark"
        disabled={disable}
        fullWidth
        sx={{ fontSize: '16px', marginTop: '34px' }}
      >
        {submitting ? (
          <CircularProgress
            size={20}
            style={{
              color: 'white',
            }}
          />
        ) : (
          'บันทึก'
        )}
      </Button>
    </form>
  );
};

const DimensionGridStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 16px;
  row-gap: 16px;
  @media screen and (max-width: 350px) {
    justify-content: center;
    grid-template-columns: repeat(1, 1fr);
  }
`;

export default ItemSizeForm;
