import { useFormik } from 'formik';
import React, { useEffect } from 'react';
import {
  Button,
  CircularProgress,
  FormHelperText,
  TextField,
} from '@mui/material';
import _, { isEmpty } from 'lodash';
import styled from '@emotion/styled';

type SizeFormProps = {
  handleSubmit: (value: any) => void;
  submitting: boolean;
  disable: boolean;
  initialValues?: any;
  itemSize?: any;
};

type FormValues = {
  id: number | null;
  itemSizeName: string | null;
  itemSizeId: number | null;
  cut: number | string;
  materialSizeRequest: {
    value: string | number;
    materialSizeDimensionId: number | null;
  }[];
  isEdit: boolean;
};
const SubItemSizeForm = ({
  handleSubmit,
  submitting,
  disable,
  initialValues,
  itemSize,
}: SizeFormProps) => {
  const formik = useFormik({
    initialValues: {
      id: initialValues.id || null,
      itemSizeName: '',
      cut: initialValues.cut || '',
      itemSizeId: itemSize.id || null,
      materialSizeRequest: itemSize.dimensionConfig.materialSizeDimension.map(
        (item: any, index: number) => {
          return {
            value: !isEmpty(initialValues)
              ? initialValues.materialSizeRequest[index].value
              : '',
            materialSizeDimensionId: !isEmpty(initialValues)
              ? initialValues.materialSizeRequest[index].materialSizeDimensionId
              : item.id,
          };
        }
      ),
    } as FormValues,
    validate: (values: any) => {
      const errors: any = {};
      if (!values.cut) {
        errors.cut = 'กรุณากรอกตัด';
      } else if (values.cut < 0) {
        errors.cut = '> = 0';
      }
      const isError: boolean = _.some(values.materialSizeRequest, (o: any) => {
        return o.value === null || o.value === '' || o.value === undefined;
      });
      if (values.materialSizeRequest.length > 0 && isError) {
        errors.materialSizeRequest = [];
        values.materialSizeRequest.map((item: any, index: number) => {
          if (!item.value) {
            errors.materialSizeRequest.push({
              index: index,
              message: 'กรุณากรอก',
            });
          }
        });
      }
      return errors;
    },
    onSubmit: async (values: any) => {
      const name = `ตัด ${Number(values.cut) + 1}`;

      const itemSizeName = values.materialSizeRequest
        .map((item: any) => item.value)
        .join(' * ');
      values.itemSizeName = `${name} ${itemSizeName} ${itemSize.dimension.name}`;
      handleSubmit(values);
    },
  });

  useEffect(() => {
    formik.resetForm();
  }, []);

  const renderDynamicMessage = (index: number, label: string) => {
    if (formik.errors?.materialSizeRequest) {
      const err: any = _.find(formik.errors.materialSizeRequest, (o: any) => {
        return o.index === index;
      });
      if (err) {
        return `${err.message} ${label}`;
      }
    }
  };

  const handleInputChange =
    (_index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = Number(e.target.value);
      if (value >= 0) {
        formik.handleChange(e);
      }
    };

  const preventNegativeInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === '-') {
      e.preventDefault();
    }
  };

  console.log('itemSize', itemSize);
  return (
    <form onSubmit={formik.handleSubmit}>
      <div>
        <p>ตัด</p>
        <TextField
          type="number"
          name="cut"
          placeholder="ตัด"
          value={formik.values.cut}
          onChange={(e: any) => {
            if (Number(e.target.value) < 0) {
              e.preventDefault();
            } else {
              formik.handleChange(e);
            }
          }}
          onKeyDown={(e: any) => {
            if (e.key === '-') {
              e.preventDefault();
            }
          }}
          error={formik.touched.cut && Boolean(formik.errors.cut)}
          helperText={formik.touched.cut && (formik.errors.cut as string)}
        />
      </div>
      <div
        className={`flex flex-col min-h-[69px]`}
        style={{
          rowGap: '24px',
        }}
      >
        <DimensionGridStyle>
          {
            (() => {
              return itemSize.dimensionConfig.materialSizeDimension?.map(
                (item: any, index: number) => {
                  return (
                    <div key={index}>
                      <p
                        style={{
                          minHeight: '21px',
                        }}
                      >
                        {item.name}
                      </p>
                      <TextField
                        type="number"
                        name={`materialSizeRequest[${index}].value`}
                        value={
                          formik.values?.materialSizeRequest[index]?.value || ''
                        }
                        placeholder={`กรอก${item.name}`}
                        onChange={handleInputChange(index)}
                        onKeyDown={preventNegativeInput}
                        InputProps={{
                          endAdornment: (
                            <div className="p-[2px]">
                              {/* {getUnitSizeInfo()?.name || ''} */}
                            </div>
                          ),
                        }}
                      />
                      {formik.touched.materialSizeRequest &&
                        formik.errors?.materialSizeRequest && (
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            {renderDynamicMessage(index, item.name)}
                          </FormHelperText>
                        )}
                    </div>
                  );
                }
              );
            })() as any
          }
        </DimensionGridStyle>
      </div>
      <Button
        type="submit"
        variant="contained"
        color="dark"
        disabled={disable}
        fullWidth
        sx={{ fontSize: '16px', marginTop: '34px' }}
      >
        {submitting ? (
          <CircularProgress
            size={20}
            style={{
              color: 'white',
            }}
          />
        ) : (
          'บันทึก'
        )}
      </Button>
    </form>
  );
};

export default SubItemSizeForm;

const DimensionGridStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 16px;
  row-gap: 16px;
  @media screen and (max-width: 350px) {
    justify-content: center;
    grid-template-columns: repeat(1, 1fr);
  }
`;
