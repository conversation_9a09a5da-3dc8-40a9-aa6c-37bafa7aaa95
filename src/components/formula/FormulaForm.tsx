import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { isEmpty } from 'lodash';
import BackspaceIcon from '@mui/icons-material/Backspace';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import apiFormula from '@/services/stock/formula';
import { LoadingFadein } from '@/styles/share.styled';
import { useRouter } from 'next/router';
import { displayFormula } from '@/utils/formula/displayFormula';

const FormulaFormStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  align-items: center;
  justify-content: center;
  height: calc(100dvh - 64px);
  animation: ${LoadingFadein} 0.6s ease-in;
  * {
    user-select: none;
  }
  @media screen and (max-width: 820px) {
    padding: 16px;
    height: calc(100dvh - 136px);
  }
  form {
    width: 600px;
    max-width: 100%;
    .from-wrap {
      width: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      padding: 24px;
      background: #949494;
      box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.08);
      .Mui-disabled {
        -webkit-text-fill-color: initial !important;
      }
      button {
        box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.2) !important;
      }
      .text-field {
        .MuiFormControl-root {
          * {
            box-shadow: none !important;
          }
        }
      }
      #mui-component-select-optionsId {
        color: white !important;
      }
      #mui-component-select-optionsCategoryId {
        color: white !important;
      }
      .variable {
        width: 100%;
        height: 40px;
        border-radius: 8px;
        border: 1px dashed #dbe2e5;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
      .config-type-wrap {
        width: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;

        .config-type {
          flex: 1 1 0%;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 16px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          background: #bababa;
          color: white;
          font-weight: 600;
          cursor: pointer;
          transition: 0.15s ease-out;
          box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.2);
          &:hover {
            box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.6);
          }
          span {
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
          }
        }
      }
      .pad-wrap {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        .item {
          background: #263238;
          height: 40px;
          border-radius: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          font-weight: 600;
          padding: 0 16px;
          color: white;
          cursor: pointer;
          transition: 0.15s ease-out;
          box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.2);
          &.operation {
            color: #bababa;
          }
          &:hover {
            background: #516873;
          }
        }
      }
    }
  }
  .name-long {
    box-shadow: 0 0 0 1px #dbe2e5;
    border-radius: 8px;
    background: white;
  }
`;

const validationSchema = yup.object({
  formula: yup.string().required('กรุณากรอก'),
  nameLong: yup.string().required('กรุณากรอกชื่อ'),
  optionsCategoryId: yup
    .number()
    .required('กรุณาเลือก')
    .typeError('กรุณาเลือก'),
  optionsId: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
});

type Props = {
  initialValue: any;
};

const FormulaForm = ({ initialValue }: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const [optionsByCategoryList, setOptionsByCategoryList] = useState<any>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [displayFormulaText, setDisplayFormulaText] = useState<string>('');
  const [optionTypeList, setOptionTypeList] = useState<any>([]);
  const [categoryList, setCategoryList] = useState<any>([]);
  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    control,
    reset,
    trigger,
    formState: { errors: hookFormErrors, isSubmitted },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });
  // console.log('initialValue', initialValue);
  const watchFormula = useWatch({
    control,
    name: 'formula',
    defaultValue: initialValue.formula || '',
  });

  const watchCategoryOptionId = useWatch({
    control,
    name: 'optionsCategoryId',
    defaultValue: initialValue.optionsCategoryId || '',
  });

  const watchOptionId = useWatch({
    control,
    name: 'optionsId',
    defaultValue: initialValue.optionsId || '',
  });

  const getOptionsByCategoryList = async () => {
    const res = await apiFormula.getOptionsList({
      optionsCategoryId: watchCategoryOptionId,
    });
    if (!res.isError) {
      setOptionsByCategoryList(res.data);
    }
  };
  const getCategoryList = async () => {
    const res = await apiFormula.getOptionsCategoryList();
    if (!res.isError) {
      setCategoryList(res.data);
    }
  };
  useEffect(() => {
    getCategoryList();
  }, []);

  useEffect(() => {
    if (watchCategoryOptionId) {
      getOptionsByCategoryList();
    }
  }, [watchCategoryOptionId]);

  useEffect(() => {
    reset(initialValue);
  }, [initialValue]);

  const onSubmit = async (values: any) => {
    const sendValue = {
      ...values,
      name: displayFormulaText,
    };
    const { formula } = sendValue;

    // ฟังก์ชันตรวจสอบวงเล็บว่าปิดครบหรือไม่
    const checkParenthesesBalance = (formula: string) => {
      let openCount = 0;

      for (let i = 0; i < formula.length; i++) {
        const char = formula[i];

        if (char === '(') {
          openCount++;
        } else if (char === ')') {
          openCount--;
        }

        // ถ้า count ของวงเล็บปิดมากกว่าวงเล็บเปิด
        if (openCount < 0) {
          return false; // พบวงเล็บปิดก่อนวงเล็บเปิด
        }
      }

      return openCount === 0; // ต้องมีจำนวนวงเล็บเปิดและปิดเท่ากัน
    };

    // ฟังก์ชันตรวจสอบตัวดำเนินการ
    const checkOperationBalance = (formula: string) => {
      const operationSymbols = ['+', '-', '*', '/', '%'];
      const lastChar = formula.slice(-1);
      if (operationSymbols.includes(lastChar)) {
        return true;
      }
      return false;
    };

    if (checkOperationBalance(formula)) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'กรุณาตรวจสอบตัวดำเนินการ',
          severity: 'error',
        })
      );
      return;
    }

    if (!checkParenthesesBalance(formula)) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'วงเล็บเปิดและปิดไม่สมดุล',
          severity: 'error',
        })
      );
      return;
    }
    setSubmitting(true);
    if (id) {
      const res = await apiFormula.updateFormula(sendValue, Number(id));
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'แก้ไขสูตรสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await router.push('/company/formula/list');
      }
    } else {
      const res = await apiFormula.createFormula(sendValue);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? 'สร้างสูตรสำเร็จ' : 'เกิดข้อผิดพลาด',
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        await router.push('/company/formula/list');
      }
    }
    setSubmitting(false);
  };
  const handleItemClick = (value: string) => {
    const currentValue = getValues('formula') || '';
    const operationSymbols = ['+', '-', '*', '/', '%'];
    const lastChar = currentValue[currentValue.length - 1];
    const lastAtIndex = currentValue.lastIndexOf('@');

    // ตรวจสอบว่าค่า configType คือ @uuid อยู่หรือไม่
    if (
      lastAtIndex !== -1 &&
      currentValue.length > lastAtIndex + 1 &&
      value !== 'backspace'
    ) {
      const partialUuid = currentValue.substring(lastAtIndex + 1);
      const isConfigType = optionTypeList.some((item: any) =>
        item.uuid.startsWith(partialUuid)
      );

      if (isConfigType && value !== 'C') {
        // หากเป็น configType ห้ามใส่ตัวเลขหรือจุดทศนิยม
        if (
          !operationSymbols.includes(value) &&
          value !== '(' &&
          value !== ')'
        ) {
          console.log('ไม่สามารถใส่ตัวเลขหรือจุดทศนิยมหลัง configType ได้');
          return;
        }
      }
    }
    // ส่วนของการจัดการค่าปกติ
    if (value === 'backspace') {
      if (lastAtIndex !== -1) {
        const partialUuid = currentValue.substring(lastAtIndex + 1);
        const isPartialUuid = optionTypeList.some((item: any) =>
          item.uuid.startsWith(partialUuid)
        );

        if (isPartialUuid) {
          setValue('formula', currentValue.substring(0, lastAtIndex));
        } else {
          setValue('formula', currentValue.slice(0, -1));
        }
      } else {
        setValue('formula', currentValue.slice(0, -1));
      }
    } else if (value === 'C') {
      setValue('formula', '');
    } else if (operationSymbols.includes(value)) {
      if (
        !operationSymbols.includes(lastChar) &&
        lastChar !== '(' &&
        lastChar !== '.'
      ) {
        setValue('formula', currentValue + value);
      } else {
        console.log('ไม่สามารถใส่ตัวดำเนินการต่อเนื่องกันได้');
      }
    } else if (value === '(') {
      if (
        operationSymbols.includes(lastChar) ||
        currentValue === '' ||
        lastChar === '('
      ) {
        setValue('formula', currentValue + value);
      } else {
        console.log('ต้องมีตัวดำเนินการก่อนใส่วงเล็บเปิด');
      }
    } else if (value === ')') {
      const openParenthesesCount = (currentValue.match(/\(/g) || []).length;
      const closeParenthesesCount = (currentValue.match(/\)/g) || []).length;

      if (
        openParenthesesCount > closeParenthesesCount &&
        lastChar !== '(' &&
        !operationSymbols.includes(lastChar)
      ) {
        setValue('formula', currentValue + value);
      } else {
        console.log('ไม่สามารถปิดวงเล็บโดยไม่มีวงเล็บเปิดหรือมีวงเล็บเปิดก่อน');
      }
    } else if (value === '.') {
      const lastOperationIndex = Math.max(
        currentValue.lastIndexOf('+'),
        currentValue.lastIndexOf('-'),
        currentValue.lastIndexOf('*'),
        currentValue.lastIndexOf('/'),
        currentValue.lastIndexOf('%'),
        currentValue.lastIndexOf('('),
        currentValue.lastIndexOf(')')
      );
      const lastNumberSegment = currentValue.substring(lastOperationIndex + 1);

      const isLastCharNumberOrConfigType =
        !Number.isNaN(lastChar) || lastChar === '@';
      // ตรวจสอบว่าห้ามใส่ "." เป็นตัวแรกหลัง "(" หรือ ")" หรือเครื่องหมายดำเนินการ
      if (
        lastChar === '(' ||
        lastChar === ')' ||
        operationSymbols.includes(lastChar)
      ) {
        console.log(
          'ไม่สามารถใส่จุดทศนิยมหลังวงเล็บหรือเครื่องหมายดำเนินการได้'
        );
      } else if (
        (!lastNumberSegment.includes('.') && isLastCharNumberOrConfigType) ||
        (lastChar === ')' && !currentValue.endsWith('.'))
      ) {
        setValue('formula', currentValue + value);
      } else {
        console.log('ไม่สามารถใส่จุดทศนิยมในตำแหน่งนี้ได้');
      }
    } else if (!Number.isNaN(Number(value))) {
      const lastOperationIndex = Math.max(
        currentValue.lastIndexOf('+'),
        currentValue.lastIndexOf('-'),
        currentValue.lastIndexOf('*'),
        currentValue.lastIndexOf('/'),
        currentValue.lastIndexOf('%'),
        currentValue.lastIndexOf('('),
        currentValue.lastIndexOf(')')
      );
      const lastNumberSegment = currentValue.substring(lastOperationIndex + 1);

      if (
        lastNumberSegment.includes('.') &&
        lastNumberSegment.split('.').length > 2
      ) {
        console.log('ไม่สามารถใส่ตัวเลขต่อหลังจุดทศนิยมมากกว่าหนึ่งจุดได้');
      } else if (
        lastChar === '(' ||
        lastChar === '.' ||
        operationSymbols.includes(lastChar) ||
        lastChar === ')' ||
        !Number.isNaN(lastChar)
      ) {
        setValue('formula', currentValue + value);
      } else {
        console.log('ไม่สามารถใส่ตัวเลขในตำแหน่งนี้ได้');
      }
    }
  };

  const handleClickConfigType = (data: any) => {
    const currentValue = getValues('formula') || '';
    const lastChar = currentValue[currentValue.length - 1];
    const operationSymbols = ['+', '-', '*', '/'];
    // ตรวจสอบให้แน่ใจว่ามี operator หรือเปิดวงเล็บก่อนการใส่ @uuid หรือถ้า formula ว่างเปล่า
    if (
      currentValue === '' ||
      operationSymbols.includes(lastChar) ||
      lastChar === '('
    ) {
      setValue('formula', `${currentValue}@${data.uuid}`);
    } else if (lastChar === ')' || !operationSymbols.includes(lastChar)) {
      console.log('ต้องใส่ตัวดำเนินการก่อนที่จะเพิ่ม @uuid');
    } else {
      console.log('ไม่สามารถใส่ค่าในตำแหน่งนี้ได้');
    }
  };

  useEffect(() => {
    if (watchFormula) {
      trigger('formula');
    }
    if (!isEmpty(optionTypeList)) {
      const text = displayFormula(watchFormula, optionTypeList);
      setDisplayFormulaText(text);
    }
  }, [watchFormula, optionTypeList]);

  useEffect(() => {
    if (!isEmpty(optionsByCategoryList)) {
      const findOptionType = optionsByCategoryList.find(
        (optionItem: any) => optionItem.id === watchOptionId
      );
      if (findOptionType) {
        const formatedOptionType =
          findOptionType.optionsCategory.optionType.map((item: any) => {
            return item.optionsType;
          });
        setOptionTypeList(formatedOptionType);
      }
    }
  }, [watchOptionId, optionsByCategoryList]);

  return (
    <FormulaFormStyle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="from-wrap">
          <FormControl
            sx={{
              width: '100%',
            }}
          >
            <Select
              sx={{
                height: '40px',
                border: '0',
              }}
              displayEmpty
              {...register('optionsCategoryId')}
              error={Boolean(hookFormErrors.optionsCategoryId) && isSubmitted}
              input={<OutlinedInput />}
              renderValue={(selected) => {
                if (!selected) {
                  return (
                    <div className="text-[14px] text-[#B0BEC5]">กรุณาเลือก</div>
                  );
                }
                const selectedItem = categoryList.find(
                  (item: any) => item.id === selected
                );
                return selectedItem ? selectedItem.name : '';
              }}
              value={watchCategoryOptionId || ''}
              onChange={(e) => {
                setValue('optionsCategoryId', e.target.value, {
                  shouldValidate: true,
                });
                setValue('optionsId', '', {
                  shouldValidate: true,
                });
                setValue('formula', '');
                setDisplayFormulaText('');
                setOptionTypeList([]);
              }}
            >
              {categoryList.map((item: any) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
            {hookFormErrors.optionsCategoryId && isSubmitted && (
              <FormHelperText error>
                {hookFormErrors.optionsCategoryId.message as ReactNode}
              </FormHelperText>
            )}
          </FormControl>
          {watchCategoryOptionId && (
            <FormControl
              sx={{
                width: '100%',
              }}
            >
              <Select
                sx={{
                  height: '40px',
                  border: '0',
                }}
                displayEmpty
                {...register('optionsId')}
                error={Boolean(hookFormErrors.optionsId) && isSubmitted}
                input={<OutlinedInput />}
                renderValue={(selected) => {
                  if (!selected) {
                    return (
                      <div className="text-[14px] text-[#B0BEC5]">
                        กรุณาเลือก
                      </div>
                    );
                  }
                  const selectedItem = optionsByCategoryList.find(
                    (item: any) => item.id === selected
                  );
                  return selectedItem ? selectedItem.name : '';
                }}
                value={watchOptionId || ''}
                onChange={(e) => {
                  setValue('optionsId', e.target.value, {
                    shouldValidate: true,
                  });
                  setValue('formula', '');
                }}
              >
                {optionsByCategoryList.map((item: any) => (
                  <MenuItem key={item.id} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              {hookFormErrors.optionsId && isSubmitted && (
                <FormHelperText error>
                  {hookFormErrors.optionsId.message as ReactNode}
                </FormHelperText>
              )}
            </FormControl>
          )}
          {isEmpty(optionTypeList) && <div className="variable">ตัวแปร</div>}
          {!isEmpty(optionTypeList) && (
            <div className="config-type-wrap">
              {optionTypeList.map((item: any, index: number) => {
                return (
                  <div
                    className="config-type"
                    key={index}
                    onClick={() => {
                      handleClickConfigType(item);
                    }}
                  >
                    <span>{item.name}</span>
                  </div>
                );
              })}
            </div>
          )}
          <div className="text-field name-long">
            <TextField
              placeholder="ชื่อสูตร"
              {...register('nameLong')}
              error={Boolean(hookFormErrors.nameLong)}
              helperText={hookFormErrors.nameLong?.message as ReactNode}
            />
          </div>
          <div className="text-field">
            <TextField
              placeholder="สูตร"
              value={displayFormulaText}
              disabled
              error={Boolean(hookFormErrors.formula)}
              helperText={hookFormErrors.formula?.message as ReactNode}
            />
          </div>
          <div className="pad-wrap">
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('(');
              }}
            >
              (
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick(')');
              }}
            >
              )
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('%');
              }}
            >
              %
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('C');
              }}
            >
              C
            </div>
            {/* // */}
            <div
              className="item"
              onClick={() => {
                handleItemClick('7');
              }}
            >
              7
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('8');
              }}
            >
              8
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('9');
              }}
            >
              9
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('/');
              }}
            >
              /
            </div>
            {/* // */}
            <div
              className="item"
              onClick={() => {
                handleItemClick('4');
              }}
            >
              4
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('5');
              }}
            >
              5
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('6');
              }}
            >
              6
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('*');
              }}
            >
              *
            </div>
            {/* // */}
            <div
              className="item"
              onClick={() => {
                handleItemClick('1');
              }}
            >
              1
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('2');
              }}
            >
              2
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('3');
              }}
            >
              3
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('-');
              }}
            >
              -
            </div>
            {/* // */}
            <div
              className="item"
              onClick={() => {
                handleItemClick('0');
              }}
            >
              0
            </div>
            <div
              className="item"
              onClick={() => {
                handleItemClick('.');
              }}
            >
              .
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('backspace');
              }}
            >
              <BackspaceIcon />
            </div>
            <div
              className="item operation"
              onClick={() => {
                handleItemClick('+');
              }}
            >
              +
            </div>
          </div>
          <Button type="submit" variant="contained" color="primary" fullWidth>
            {submitting ? (
              <CircularProgress
                size={20}
                sx={{
                  color: 'white',
                }}
              />
            ) : (
              'บันทึก'
            )}
          </Button>
        </div>
      </form>
    </FormulaFormStyle>
  );
};

export default FormulaForm;
