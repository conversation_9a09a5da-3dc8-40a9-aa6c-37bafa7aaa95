import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/th';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { isEmpty } from 'lodash';
import apiCredit from '@/services/core/credit';
import { useRouter } from 'next/router';
import apiOrder from '@/services/order/order';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import FormModal from '@/components/global/form/FormModal';
import { orderSelector } from '@/store/features/order';

dayjs.extend(utc);
type ModalSettingProps = {
  open: boolean;
  handleClose: () => void;
  reloadOrder: () => void;
};

const initialValue = {
  layDataOrderId: null,
  deliveryDate: '',
  expireDay: '',
  creditDay: '',
};

const validationSchema = yup.object({
  deliveryDate: yup.string().required('กรุณาเลือก'),
  expireDay: yup.string().required('กรุณากรอกจำนวนวันหมดอายุ'),
  creditDay: yup.string().required('กรุณาเลือกเครดิตการชำระเงิน'),
});

const ModalSetting = ({
  open,
  handleClose,
  reloadOrder,
}: ModalSettingProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [dateValue, setDateValue] = React.useState<Dayjs | null>(dayjs());
  const [creditTypeList, setCreditTypeList] = useState<any>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const { orderById } = useAppSelector(orderSelector);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValue,
  });
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    const sendData = {
      ...values,
      layDataOrderId: router.query.orderId,
    };
    const res = await apiOrder.settingOrder(sendData);
    if (!res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: 'บันทึกการตั้งค่าออร์เดอร์สำเร็จ',
          severity: 'success',
        })
      );
      reloadOrder();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };
  const handleChangeExpDate = (value: Dayjs) => {
    setDateValue(value);
  };

  const handleDefaultDeliveryDate = () => {
    if (!isEmpty(dateValue)) {
      const utcValue = dateValue.utc().format('YYYY-MM-DD');
      setValue('deliveryDate', utcValue);
    }
  };

  useEffect(() => {
    handleDefaultDeliveryDate();
  }, [dateValue]);

  const getCreditTypeList = async () => {
    const res = await apiCredit.getCreditOption();
    if (!res.isError) {
      setCreditTypeList(res);
    }
  };

  useEffect(() => {
    getCreditTypeList();
  }, []);
  useEffect(() => {
    if (open) {
      reset();
    }
    if (orderById.credit === null) {
      handleDefaultDeliveryDate();
    }
    if (open && orderById.credit !== null) {
      setValue(
        'deliveryDate',
        dayjs(orderById.deliveryDate).format('YYYY-MM-DD')
      ); // แปลง timestamp เป็นวันที่ในรูปแบบสตริง
      setValue('creditDay', orderById.creditDay);
      setValue('expireDay', orderById.expireDay);
      setDateValue(dayjs(orderById.deliveryDate)); // แปลง timestamp เป็นวันที่
    }
  }, [open]);
  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="th">
        <Dialog
          open={open}
          onClose={() => {
            handleClose();
          }}
        >
          <DialogContent>
            <FormModal
              title={`ตั้งค่าออเดอร์`}
              handleClose={() => {
                handleClose();
              }}
              width={492}
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                <div>
                  <p>วันที่ส่งมอบ</p>
                  <MobileDatePicker
                    value={dateValue}
                    onChange={(value: any) => {
                      handleChangeExpDate(value);
                    }}
                  />
                </div>
                <div>
                  <p>กำหนดวันหมดอายุ</p>
                  <TextField
                    type="number"
                    placeholder="กรอกจำนวนวัน"
                    onKeyDown={(e: any) => {
                      if (e.key === '-') {
                        e.preventDefault();
                      }
                    }}
                    onPaste={(e) => {
                      e.preventDefault();
                    }}
                    {...register('expireDay', {
                      onChange: (e: any) => {
                        if (Number(e.target.value) < 0) {
                          e.preventDefault();
                          e.target.value = '';
                        }
                      },
                    })}
                    error={Boolean(hookFormErrors.expireDay)}
                    helperText={
                      hookFormErrors.expireDay
                        ? (hookFormErrors.expireDay.message as ReactNode)
                        : ''
                    }
                    InputProps={{
                      endAdornment: (
                        <InputAdornment
                          className="cursor-pointer"
                          position="end"
                        >
                          วัน
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                <div>
                  {!isEmpty(creditTypeList) && (
                    <>
                      <p>เครดิตการชำระเงิน</p>
                      <FormControl
                        fullWidth
                        error={Boolean(hookFormErrors.creditDay)}
                        sx={{
                          height: '40px',
                        }}
                      >
                        <Select
                          {...register('creditDay')}
                          value={watch('creditDay')}
                          // onChange={(e: any) => {
                          //   formik.handleChange(e);
                          // }}
                          displayEmpty
                        >
                          <MenuItem
                            disabled
                            value=""
                            sx={{
                              fontSize: '14px',
                            }}
                          >
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {creditTypeList.map((item: any, index: React.Key) => (
                            <MenuItem
                              key={index}
                              value={item.id}
                              sx={{
                                fontSize: '14px',
                              }}
                            >
                              {item.day}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </>
                  )}
                  {Boolean(hookFormErrors.creditDay) && (
                    <FormHelperText
                      error
                      sx={{
                        margin: '4px 14px 0',
                      }}
                    >
                      {hookFormErrors.creditDay?.message as ReactNode}
                    </FormHelperText>
                  )}
                </div>
                <div className="w-full flex justify-between mt-[34px] gap-5">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                    onClick={() => {
                      handleClose();
                    }}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                  >
                    {submitting ? (
                      <CircularProgress
                        size={20}
                        sx={{
                          color: 'white',
                        }}
                      />
                    ) : (
                      'บันทึก'
                    )}
                  </Button>
                </div>
              </form>
            </FormModal>
          </DialogContent>
        </Dialog>
      </LocalizationProvider>
    </>
  );
};

export default ModalSetting;
