import React from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import Image from 'next/image';
import dayjs from 'dayjs';

type Props = {
  title: string;
  dataPayment: any;
};
const InvoiceDataTableStyle = styled.div`
  header {
    display: flex;
    align-items: end;
    justify-content: space-between;
    h3 {
      margin: 0;
      font-size: 22px;
    }
  }
  .box-data-table {
    margin-top: 1rem;
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
  }
`;

const InvoiceDataTable = ({ title, dataPayment }: Props) => {
  const columns: GridColDef[] = [
    {
      field: 'invoiceNo',
      headerName: 'เลขที่',
      minWidth: 172,
      flex: 1,
    },
    {
      field: 'totalPrice',
      headerName: 'ราคารวม (บาท)',
      minWidth: 150,
      flex: 1,
    },
    {
      field: 'createUser',
      headerName: 'ผู้สร้าง',
      minWidth: 64,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <Image
            src={
              params.row.createUser.imageUrl ||
              '/images/product/empty-product.svg'
            }
            width={32}
            height={32}
            alt=""
            style={{
              borderRadius: '50%',
              objectFit: 'cover',
              minWidth: '32px',
            }}
          />
        );
      },
    },
    {
      field: 'paymentInvoiceStatus',
      headerName: 'สถานะ',
      minWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <span
            style={{
              color:
                params.row.paymentInvoiceStatus.name === 'แบบร่าง'
                  ? 'gray'
                  : params.row.paymentInvoiceStatus.name === 'รออนุมัติ'
                  ? 'orange'
                  : params.row.paymentInvoiceStatus.name === 'รอรับชำระ'
                  ? 'orange'
                  : params.row.paymentInvoiceStatus.name === 'รับชำระแล้ว'
                  ? '#8BC34A'
                  : params.row.paymentInvoiceStatus.name === 'เกินเวลารับชำระ'
                  ? 'red'
                  : params.row.paymentInvoiceStatus.name === 'ปฏิเสธ'
                  ? 'red'
                  : 'initial',
            }}
          >
            {params.row.paymentInvoiceStatus.name}
          </span>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'วันที่สร้าง',
      minWidth: 172,
      flex: 1,
      align: 'left',
      headerAlign: 'left',
      renderCell: (params: any) => (
        <div>{dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm')}</div>
      ),
    },
    {
      field: 'dueDate',
      headerName: 'วันครบกำหนด',
      minWidth: 172,
      flex: 1,
      align: 'left',
      headerAlign: 'left',
      renderCell: (params: any) => (
        <div>{dayjs(params.row.dueDate).format('DD/MM/YYYY, HH:mm')}</div>
      ),
    },
    {
      field: 'receiptNo',
      headerName: 'ใบเสร็จชำระเงิน',
      minWidth: 172,
      flex: 1,
      align: 'right',
      headerAlign: 'right',
      renderCell: (params: any) => <div>{params.row.receiptNo || '-'}</div>,
    },
  ];
  return (
    <InvoiceDataTableStyle>
      <header>
        <h3>{title}</h3>
      </header>
      <div className={'box-data-table'}>
        <AppTableStyle $rows={dataPayment.orderPaymentInvoice || []}>
          <div className="content-wrap">
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={dataPayment.orderPaymentInvoice || []}
                columns={columns || []}
                paginationMode="server"
                // checkboxSelection={true}
                // rowCount={totalElements || 0}
                // pageSize={filters.size}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
        </AppTableStyle>
      </div>
    </InvoiceDataTableStyle>
  );
};

export default InvoiceDataTable;
