import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Button, Menu, MenuItem } from '@mui/material';
import { LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import apiOrder from '@/services/order/order';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { orderSelector } from '@/store/features/order';
import ActionButton from '@/components/ActionButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import apiLayData from '@/services/order/layData';
import apiEstimate from '@/services/order/estimate';

dayjs.extend(utc);
const PaymentHeaderStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28px 24px 28px;
  column-gap: 40px;
  row-gap: 24px;
  flex-wrap: wrap;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 820px) {
    padding: 28px 16px 28px;
  }
  @media screen and (max-width: 650px) {
    row-gap: 16px;
    padding: 24px 16px 24px;
  }
  .od-number {
    font-size: 40px;
    font-weight: 600;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @media screen and (max-width: 650px) {
      font-size: 28px;
    }
    @media screen and (max-width: 350px) {
      font-size: 22px;
    }
  }
`;

type Props = {
  data: any;
};
const PaymentHeader = ({ data }: Props) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { orderId } = router.query;
  const [anchorEl, setAnchorEl] = useState(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [modalCancel, setModalCancel] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const [paymentType, setPaymentType] = useState<any>([]);
  const [isSomePriceConfirmed] = useState<boolean>(false);
  const [openCreateSalePrice, setOpenCreateSalePrice] = useState<any>({
    status: false,
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const { orderById } = useAppSelector(orderSelector);
  const [openSendToJob, setOpenSendToJob] = useState<any>({
    isPayment: false,
    status: false,
  });
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCancelOrder = async (value: any) => {
    setSubmitting(true);
    const sendValue = { reason: value, orderId: orderById.id };
    const res = await apiOrder.cancelOrder(sendValue);
    if (!res.isError) {
      handleCloseModalCancel();
      await router.push('/orders');
    }
    setSubmitting(false);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCloseModalCancel = () => {
    setModalCancel({
      ...modalCancel,
      open: false,
    });
  };
  const handleOpenModalCancel = () => {
    setModalCancel({
      open: true,
      title: 'AA',
      description: 'BB',
    });
  };

  const getPaymentType = async () => {
    const res = await apiEstimate.getListPaymentTypeEnum();
    if (!res.isError) {
      setPaymentType(res.data);
    }
  };

  useEffect(() => {
    getPaymentType().then();
  }, [orderById]);

  const handleClickCreateSalePrice = () => {
    setOpenCreateSalePrice({
      ...openCreateSalePrice,
      status: true,
    });
  };

  const handleSendToArtwork = async () => {
    setLoadingConfirm(true);
    const res = await apiLayData.approveLayData(data.layDataId);
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setOpenSendToJob({
        ...openSendToJob,
        status: false,
      });
      await router.push(`/orders/${orderId}/spec?step=อาร์ตเวิร์ก`);
    }
    setLoadingConfirm(false);
  };

  return (
    <>
      {isSomePriceConfirmed && (
        <AppModalConfirm
          open={openCreateSalePrice.status}
          isReason={false}
          onClickClose={() => {
            setOpenCreateSalePrice({
              ...openCreateSalePrice,
              status: false,
            });
          }}
          icon={
            <Image
              src={'/icons/icon-export-notes.svg'}
              alt=""
              width={40}
              height={40}
            />
          }
          confirmTitle={`สร้างใบราคาเสนอขาย`}
          confirmDescription={`สินค้าในรายการสั่งผลิตนี้ได้เสนอราคาขายเรียบร้อยแล้ว การสร้างราคาเสนอขายใหม่ คุณจะต้องทำการยืนยันราคาขายอีกครั้ง`}
          loadingConfirm={loadingConfirm}
          onConfirm={async (_value: any) => {
            //
          }}
          maxWidth={'380px'}
          paymentType={paymentType}
        />
      )}
      <AppModalConfirm
        open={openSendToJob.status}
        isReason={false}
        onClickClose={() => {
          setOpenSendToJob({
            ...openSendToJob,
            status: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          openSendToJob.isPayment
            ? `ส่งดำเนินการผลิต`
            : 'ยืนยันที่จะส่งดำเนินการผลิต'
        }
        confirmDescription={
          openSendToJob.isPayment
            ? `คุณต้องการที่จะส่งดำเนินการผลิตใช่หรือไม่`
            : `คุณต้องการที่จะส่งดำเนินการผลิตโดยที่ยังไม่ได้ชำระเงินใช่หรือไม่`
        }
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleSendToArtwork();
        }}
        maxWidth={'380px'}
      />
      <AppModalConfirm
        open={modalCancel.open}
        isReason
        onClickClose={() => {
          handleCloseModalCancel();
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยกเลิกรายการ
          </div>
        }
        confirmDescription={`คุณต้องการยกเลิกรายการ “${orderById.layDataOrderNo}” รายการนี้จะถูกเปลี่ยนสถานะเป็น “ยกเลิก”`}
        loadingConfirm={submitting}
        onConfirm={async (reason: {
          annotationId: any;
          note: any;
          reason: string;
        }) => {
          await handleCancelOrder(reason);
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <PaymentHeaderStyled>
        <div className="od-number">{data.ldCode}</div>
        <div className="flex items-center gap-[16px]">
          {isSomePriceConfirmed && (
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddRoundedIcon />}
              text={`สร้างราคาเสนอขาย`}
              borderRadius={'8px'}
              onClick={handleClickCreateSalePrice}
            />
          )}
          <div>
            <Button
              variant="outlined"
              size="small"
              color="blueGrey"
              sx={{
                minWidth: '40px',
              }}
              onClick={handleClick}
            >
              <div className="action-dot">
                <div className="dot" />
              </div>
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{
                marginTop: '12px',
                '.MuiList-root': {
                  padding: '8px',
                  width: '192px',
                  display: 'flex',
                  flexDirection: 'column',
                  rowGap: '4px',
                },
                li: {
                  width: 'auto',
                },
              }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
                disabled
              >
                <div className="drop-menu">
                  <InfoOutlinedIcon />
                  วิธีใช้งาน
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FDE8EF',
                  },
                }}
                disabled
              >
                <div
                  className="drop-menu text-[#D32F2F]"
                  onClick={() => {
                    handleOpenModalCancel();
                  }}
                >
                  <Image
                    src="/icons/icon-scan-delete.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                  ยกเลิกรายการ
                </div>
              </MenuItem>
            </Menu>
          </div>
        </div>
      </PaymentHeaderStyled>
      <HrSpaceStyle />
    </>
  );
};

export default PaymentHeader;
