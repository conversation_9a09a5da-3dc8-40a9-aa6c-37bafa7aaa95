import React from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { numberWithCommas } from '@/utils/number';
import dayjs from 'dayjs';
import Image from 'next/image';

const PaymentInformationStyles = styled.div`
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  header {
    padding: 16px 24px;
    border-bottom: 1px solid #dbe2e5;
    h3 {
      font-size: 22px;
      margin: 0;
    }
  }
  .box-data-payment-panel {
    display: flex;
    border-bottom: 1px solid #dbe2e5;
    .payment-panel {
      padding: 24px;
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      max-width: 100%;
      border-right: 1px solid #dbe2e5;
      &:last-child {
        border-right: none;
      }
      p {
        margin: 0;
        font-size: 12px;
      }
      .value {
        font-size: 22px;
        font-weight: 600;
      }
    }
  }
`;
type Props = {
  dataPayment: any;
};
const PaymentInformation = ({ dataPayment }: Props) => {
  const columns: GridColDef[] = [
    {
      field: 'receiptNo',
      headerName: 'ใบเสร็จรับเงิน',
      minWidth: 172,
    },
    {
      field: 'transaction',
      headerName: 'ช่องทางชำระเงิน',
      minWidth: 172,
      renderCell: (params: any) => {
        return (
          <div className="flex items-center gap-2">
            {params.row.transaction.bankAccountLogo !== null && (
              <Image
                src={params.row.transaction.bankAccountLogo}
                alt=""
                width={40}
                height={40}
                style={{
                  borderRadius: '6px',
                }}
              />
            )}
            {params.row.transaction.enumType}
          </div>
        );
      },
    },
    {
      field: 'totalPrice',
      headerName: 'มูลค่าที่รับชำระรวม',
      minWidth: 172,
      renderCell: (params: any) => (
        <div>{numberWithCommas(params.row.totalPrice, 2)}</div>
      ),
    },
    {
      field: 'transaction.transactionSlipUrl',
      headerName: 'หลักฐาน',
      minWidth: 64,
      renderCell: (params: any) => (
        <div
          className="flex items-center cursor-pointer"
          onClick={() => {
            window.open(params.row.transaction.transactionSlipUrl, '_blank');
          }}
        >
          <Image
            src={params.row.transaction.transactionSlipUrl}
            alt=""
            width={40}
            height={40}
            style={{
              borderRadius: '6px',
            }}
          />
        </div>
      ),
    },
    {
      field: 'createdDate',
      headerName: 'รับชำระเมื่อวันที่',
      minWidth: 172,
      flex: 1,
      headerAlign: 'right',
      align: 'right',
      renderCell: (params: any) => (
        <div>
          {dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm น.')}
        </div>
      ),
    },
  ];
  console.log('dataPayment', dataPayment);
  return (
    <PaymentInformationStyles>
      <header>
        <h3>ข้อมูลชำระเงิน</h3>
      </header>
      <div className={'box-data-payment-panel'}>
        <div className={'payment-panel'}>
          <p>มูลค่าสุทธิที่ต้องรับชำระทั้งสิ้น</p>
          <div className={'value'}>
            {numberWithCommas(dataPayment.totalPriceOrder, 2)}
          </div>
        </div>
        <div className={'payment-panel'}>
          <p>มูลค่าที่รับชำระแล้ว</p>
          <div className={'value'}>
            {numberWithCommas(dataPayment.paidPayment, 2)}
          </div>
        </div>
        <div className={'payment-panel'}>
          <p>ค้างชำระเงิน (บาท)</p>
          <div className={'value'}>
            {numberWithCommas(dataPayment.balancePayment, 2)}
          </div>
        </div>
      </div>
      <div className={'box-data-table'}>
        <AppTableStyle $rows={dataPayment.orderPaymentReceipt || []}>
          <div className="content-wrap">
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={dataPayment.orderPaymentReceipt || []}
                columns={columns as any}
                paginationMode="server"
                // checkboxSelection={true}
                // rowCount={totalElements || 0}
                // pageSize={filters.size}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
        </AppTableStyle>
      </div>
    </PaymentInformationStyles>
  );
};

export default PaymentInformation;
