import React from 'react';
import styled from 'styled-components';
import PaymentInformation from '@/components/order/payment/PaymentInformation';
import QuotationDataTable from '@/components/order/payment/QuotationDataTable';
import InvoiceDataTable from '@/components/order/payment/InvoiceDataTable';

const PaymentStatusStyles = styled.div`
  width: 100%;
  .content-detail {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
`;
type Props = {
  dataPayment: any;
};
const PaymentStatus = ({ dataPayment }: Props) => {
  return (
    <PaymentStatusStyles>
      <div className={'content-detail'}>
        <PaymentInformation dataPayment={dataPayment} />
        <QuotationDataTable dataPayment={dataPayment} title={'ใบเสนอราคา'} />
        <InvoiceDataTable dataPayment={dataPayment} title={'ใบแจ้งหนี้'} />
      </div>
    </PaymentStatusStyles>
  );
};

export default PaymentStatus;
