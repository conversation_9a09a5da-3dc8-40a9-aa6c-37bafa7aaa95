import React from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useRouter } from 'next/router';
import Image from 'next/image';
import dayjs from 'dayjs';

type Props = {
  title: string;
  dataPayment: any;
};
const QuotationDataTableStyle = styled.div`
  header {
    display: flex;
    align-items: end;
    justify-content: space-between;
    h3 {
      margin: 0;
      font-size: 22px;
    }
  }
  .box-data-table {
    margin-top: 1rem;
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
  }
`;

const QuotationDataTable = ({ title, dataPayment }: Props) => {
  const router = useRouter();
  const columns: GridColDef[] = [
    {
      field: 'quotationNo',
      headerName: 'เลขที่',
      minWidth: 172,
      flex: 1,
    },
    {
      field: 'createdUser',
      headerName: 'ผู้สร้าง',
      minWidth: 64,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <Image
            src={
              params.row.createdUser.imageUrl ||
              '/images/product/empty-product.svg'
            }
            width={32}
            height={32}
            alt=""
            style={{
              borderRadius: '50%',
              objectFit: 'cover',
              minWidth: '32px',
            }}
          />
        );
      },
    },
    {
      field: 'paymentQuotationStatus',
      headerName: 'สถานะ',
      minWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <span
            style={{
              color:
                params.row.paymentQuotationStatus.name === 'ตอบรับแล้ว'
                  ? '#8BC34A'
                  : params.row.paymentQuotationStatus.name === 'แบบร่าง'
                  ? 'gray'
                  : params.row.paymentQuotationStatus.name === 'รออนุมัติ'
                  ? 'orange'
                  : params.row.paymentQuotationStatus.name === 'รอตอบรับ'
                  ? 'orange'
                  : params.row.paymentQuotationStatus.name === 'ปฏิเสธ'
                  ? 'red'
                  : 'initial',
            }}
          >
            {params.row.paymentQuotationStatus.name}
          </span>
        );
      },
    },
    {
      field: 'createdDate',
      headerName: 'วันที่สร้าง',
      minWidth: 172,
      flex: 1,
      align: 'left',
      headerAlign: 'left',
      renderCell: (params: any) => {
        return (
          <div>{dayjs(params.row.createdDate).format('DD/MM/YYYY, HH:mm')}</div>
        );
      },
    },
    {
      field: 'dueDate',
      headerName: 'วันครบกำหนด',
      minWidth: 172,
      flex: 1,
      align: 'right',
      headerAlign: 'right',
      renderCell: (params: any) => {
        return (
          <div>{dayjs(params.row.dueDate).format('DD/MM/YYYY, HH:mm')}</div>
        );
      },
    },
  ];
  return (
    <QuotationDataTableStyle>
      <header>
        <h3>{title}</h3>
        <Button
          variant={'contained'}
          startIcon={<AddIcon />}
          onClick={async () => {
            await router.push('/accounting-finance/quotation');
          }}
        >
          เพิ่มรายการ
        </Button>
      </header>
      <div className={'box-data-table'}>
        <AppTableStyle $rows={dataPayment.orderPaymentQuotation || []}>
          <div className="content-wrap">
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="จัดการ" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={dataPayment.orderPaymentQuotation || []}
                columns={columns || []}
                paginationMode="server"
                // checkboxSelection={true}
                // rowCount={totalElements || 0}
                // pageSize={filters.size}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
        </AppTableStyle>
      </div>
    </QuotationDataTableStyle>
  );
};

export default QuotationDataTable;
