import {
  Avatar,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { AddCircle, Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiContact from '@/services/core/contact';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import { useRouter } from 'next/router';
import { useAppDispatch } from '@/store';
import apiEstimate from '@/services/order/estimate';
import apiOrder from '@/services/order/order';
import { setSnackBar } from '@/store/features/alert';
import Autocomplete from '@mui/material/Autocomplete';
import apiCredit from '@/services/core/credit';

export const ModalCreateOrderContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;
  .empty-contact {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .contact-item-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .contact-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }

      h4 {
        margin: 0;
        line-height: 1.3;
      }

      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
        font-weight: 400;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }

  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;

    .contact-detail-wrap {
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      column-gap: 24px;

      .contact-group {
        display: flex;
        align-items: center;
        column-gap: 14px;
        overflow: hidden;

        .profile-image {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .text-group {
          display: flex;
          flex-direction: column;
          width: 300px;
          overflow: auto;

          .name {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .detail {
            font-size: 12px;
            gap: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .change-contact {
        font-size: 12px;
        text-decoration: underline;
        cursor: pointer;
        white-space: nowrap;
      }
    }

    .product-grid {
      display: grid;
      grid-gap: 16px;
      position: relative;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      min-height: 226px;
      &.model {
        overflow: auto;
        min-height: 296px;
        .product-wrap {
          &:last-child {
            padding-bottom: 0;
          }
        }
        &.set {
          min-height: 220px;
        }
      }
      .product-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 8px;
        animation: ${LoadingFadein} 0.3s ease-in;
        position: relative;
        &:last-child {
          padding-bottom: 16px;
        }
        .product-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #f5f7f9;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          transition: 0.3s ease-out;
          position: relative;
          &:before {
            transition: 0.3s ease-out;
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            background-color: transparent;
          }
          &:hover {
            &:before {
              background-color: rgba(139, 217, 255, 0.2);
            }
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
        .product-type-card {
          aspect-ratio: 1;
          border-radius: 8px;
          width: 100%;
          background-color: #aadaff;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          cursor: pointer;
          position: relative;
          user-select: none;
          &:before {
            content: '';
            width: 100%;
            border-radius: 8px;
            height: 100%;
            position: absolute;
            z-index: 1;
            box-shadow: transparent 0px 0px 0px 2px inset;
          }
          &:hover {
            &:before {
              box-shadow: #ddd 0px 0px 0px 2px inset;
            }
          }
          &.active {
            &:before {
              box-shadow: #263238 0px 0px 0px 2px inset;
            }
          }
          .check-box-wrap {
            position: absolute;
            top: 0;
            right: 0;
            .MuiButtonBase-root {
              &:hover {
                background: none !important;
              }
            }
            .MuiTouchRipple-root {
              display: none !important;
            }
          }
        }
        .name {
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }
      }
      .disabled {
        cursor: not-allowed;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
        background: #ffffff90;
      }
    }
    .filters-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      row-gap: 8px;
      column-gap: 24px;
      padding: 0 1px;
      .label {
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        align-items: baseline;
        .clear {
          font-size: 12px;
          font-weight: 400;
          text-decoration: underline;
          cursor: pointer;
          color: #cfd8dc;
        }
      }
    }
    .product-type-name {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: -8px;
    }
    .product-set-info {
      width: 100%;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      padding: 8px;
      min-height: 96px;
      display: flex;
      align-items: center;
      column-gap: 20px;
      .image {
        width: 80px;
        height: 80px;
        min-width: 80px;
        aspect-ratio: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f6f7f9;
        overflow: hidden;
        border-radius: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .text-group {
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        overflow: hidden;
        .name {
          font-size: 20px;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
        .description {
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          line-height: 1;
        }
      }
    }
  }
`;

type ModalCreateOrderProps = {
  children: React.ReactNode;
  handleReloadList: () => void;
};
const initialFilters = {
  search: '',
  contactRoleId: 1,
};

const ModalCreateOrder = ({
  children,
  handleReloadList,
}: ModalCreateOrderProps) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [contactList, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingCreateOrder, setLoadingCreateOrder] = useState(false);
  const [selectedContactData, setSelectedContactData] = useState<any>({});
  const [step, setStep] = useState<number>(1);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [isShowContactModal, setShowContactModal] = useState(false);
  const [selectedPaymentTypeId, setSelectedPaymentTypeId] = useState<
    number | string
  >('');
  const [selectedEstimate, setSelectedEstimate] = useState<any>(null);
  const [estimateList, setEstimateList] = useState<any>([]);
  const [paymentTypeList, setPaymentTypeList] = useState<any>([]);
  const [creditTypeList, setCreditTypeList] = useState<any>([]);
  const [selectedCredit, setSelectedCredit] = useState<number | null>(null);
  const getPaymentType = async () => {
    const res = await apiEstimate.getListPaymentTypeEnum();
    if (res && !res.isError) {
      setPaymentTypeList(res.data);
    }
  };

  const getContactList = async () => {
    const res = await apiContact.getContactOptions(filters);
    if (res && !res.isError) {
      setContactList(res.data);
    }
  };

  const getEstimateList = async () => {
    const res = await apiEstimate.getList({
      page: 0,
      size: 9999999,
      contactId: selectedContactData.id,
      estimateStatusId: 4,
    });
    if (res && !res.isError) {
      setEstimateList(res.data.content);
    }
  };

  useEffect(() => {
    getContactList().then();
  }, [filters.search]);

  useEffect(() => {
    getPaymentType().then();
  }, []);

  useEffect(() => {
    if (!isEmpty(selectedContactData)) {
      getEstimateList().then();
      setSelectedPaymentTypeId('');
      setSelectedEstimate({});
    }
  }, [selectedContactData]);

  const handleSearch = (event: any) => {
    const setSearchInputFn = setSearchInput;
    const setFiltersFn = setFilters;
    const currentFilters = filters;

    setSearchInputFn(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);

    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFiltersFn({
        ...currentFilters,
        search: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };

  const handleClose = () => {
    setShowContactModal(false);
  };

  const handleOpen = async () => {
    setContactList([]);
    setFilters(initialFilters);
    setSearchInput('');
    setStep(1);
    setSelectedContactData({});
    await getContactList();
    setShowContactModal(true);
  };

  const handleCreateOrder = async () => {
    setLoadingCreateOrder(true);
    const body = {
      contactId: selectedContactData.id,
      paymentType: selectedPaymentTypeId,
      estimateId: selectedEstimate.id,
      creditTypeId: selectedCredit || null,
    };
    const res = await apiOrder.createOrder(body);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleReloadList();
    }
    setLoadingCreateOrder(false);
    handleClose();
  };

  const handleChangeEs = (_event: any, option: any) => {
    setSelectedEstimate(option);
  };

  useEffect(() => {
    setSelectedEstimate({});
  }, [isShowContactModal]);

  const getCreditTypeList = async () => {
    const res = await apiCredit.getCreditOption();
    if (!res.isError) {
      setCreditTypeList(res);
    }
  };

  useEffect(() => {
    getCreditTypeList().then();
  }, []);

  console.log('creditTypeList', creditTypeList);
  return (
    <>
      <div
        onClick={async () => {
          await handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                {step !== 1 && (
                  <div
                    className="back"
                    onClick={() => {
                      setStep(step - 1);
                    }}
                  >
                    <IconButton
                      sx={{
                        color: '#263238',
                      }}
                    >
                      <KeyboardBackspaceRoundedIcon />
                    </IconButton>
                  </div>
                )}
                <div className="title">สร้างคำสั่งซื้อ</div>
                <div
                  className="x-close"
                  onClick={() => setShowContactModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  {step === 1 && (
                    <TextField
                      className="fade-in"
                      fullWidth
                      value={searchInput}
                      onChange={(event: any) => {
                        handleSearch(event);
                      }}
                      placeholder="ค้นหาลูกค้า"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            {loadingSearch ? (
                              <div className="h-[24px] w-[24px] flex items-center justify-center">
                                <CircularProgress size={20} />
                              </div>
                            ) : (
                              <Search />
                            )}
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        marginTop: '24px',
                      }}
                    />
                  )}
                  <ModalCreateOrderContentStyled>
                    {isEmpty(contactList) && (
                      <div className="empty-contact">
                        <Image
                          src="/icons/icon-account-circle.svg"
                          width={80}
                          height={80}
                          alt=""
                        />
                        <div className="flex flex-col gap-[4px]">
                          <h4>ระบุลูกค้าสำหรับออเดอร์</h4>
                          <div>
                            ค้นหาลูกค้าจากหมายเลขโทรศัพท์, ชื่อ-นามสกุล
                            ชื่อบริษัท หรือ เพิ่มลูกค้าใหม่
                          </div>
                        </div>
                        <div
                          onClick={() => {
                            router.push('/company/contact');
                          }}
                        >
                          <ActionButton
                            variant="outlined"
                            color="blueGrey"
                            icon={<AddCircle />}
                            text="เพิ่มลูกค้าใหม่"
                            borderRadius={'20px'}
                          />
                        </div>
                      </div>
                    )}
                    {!isEmpty(contactList) && step === 1 && (
                      <div className="contact-item-wrap">
                        {contactList.map((item: any, index: number) => (
                          <div
                            key={index}
                            className="contact-item"
                            onClick={() => {
                              setSelectedContactData(item);
                              setStep(2);
                            }}
                          >
                            <Avatar
                              src={item.imageUrl}
                              sx={{
                                height: '40px',
                                width: '40px',
                              }}
                            />
                            <div className="">
                              <h4>{item.name}</h4>
                              <p>
                                <span>บุคคลธรรมดา</span>
                                {!isEmpty(item.phoneNumber) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.phoneNumber}</span>
                                  </>
                                )}
                                {!isEmpty(item.note) && (
                                  <>
                                    <span>•</span>
                                    <span>{item.note}</span>
                                  </>
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    {!isEmpty(selectedContactData) && step === 2 && (
                      <div className="content fade-in">
                        <div className="contact-detail-wrap">
                          <div className="contact-group">
                            <div className="profile-image">
                              <Avatar
                                src={selectedContactData.imageUrl}
                                sx={{
                                  height: '44px',
                                  width: '44px',
                                }}
                              />
                            </div>
                            <div className="text-group">
                              <h4 className="name">
                                {selectedContactData.name}
                              </h4>
                              <div className="detail">
                                {selectedContactData.contactType.name} •
                                {` ${selectedContactData.phoneNumber}`} •
                                {` ${selectedContactData.email}`}
                              </div>
                            </div>
                          </div>
                          <div
                            className="change-contact"
                            onClick={() => {
                              setStep(1);
                            }}
                          >
                            เปลี่ยนลูกค้า
                          </div>
                        </div>
                        <FormControl
                          fullWidth
                          style={{
                            padding: '0 1px',
                          }}
                        >
                          <div
                            style={{
                              marginBottom: '8px',
                            }}
                          >
                            ใบคำนวณราคา
                          </div>
                          <Autocomplete
                            options={estimateList}
                            getOptionLabel={(option) => option.estimateNo || ''}
                            value={selectedEstimate}
                            noOptionsText="ไม่มีข้อมูล"
                            isOptionEqualToValue={(option, value) =>
                              option.id === value.id
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                placeholder="เลือก"
                                name="project"
                                error={false}
                                helperText=""
                              />
                            )}
                            sx={{ m: '0 1px' }}
                            onChange={handleChangeEs}
                          />
                        </FormControl>
                        <FormControl
                          fullWidth
                          style={{
                            marginBottom: '4px',
                            padding: '0 1px',
                          }}
                        >
                          <div
                            style={{
                              marginBottom: '8px',
                            }}
                          >
                            ประเภทการชำระเงิน
                          </div>
                          <Select
                            displayEmpty
                            error={false}
                            value={selectedPaymentTypeId || ''}
                            onChange={(e) => {
                              setSelectedPaymentTypeId(
                                e.target.value as number
                              );
                              setSelectedCredit(null);
                            }}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {paymentTypeList.map((item: any) => (
                              <MenuItem key={item.value} value={item.value}>
                                {item.description}
                              </MenuItem>
                            ))}
                          </Select>
                          {/* {hookFormErrors.detailLevel && ( */}
                          {/*    <FormHelperText error> */}
                          {/*      {hookFormErrors.detailLevel.message as React.ReactNode} */}
                          {/*    </FormHelperText> */}
                          {/* )} */}
                        </FormControl>
                        {!isEmpty(creditTypeList) &&
                          selectedPaymentTypeId === 2 && (
                            <>
                              <FormControl
                                fullWidth
                                error={false}
                                style={{
                                  marginBottom: '4px',
                                  padding: '0 1px',
                                }}
                              >
                                <div
                                  style={{
                                    marginBottom: '8px',
                                  }}
                                >
                                  เครดิตการชำระเงิน
                                </div>
                                <Select
                                  value={selectedCredit || ''}
                                  onChange={(e: any) => {
                                    setSelectedCredit(e.target.value);
                                  }}
                                  displayEmpty
                                >
                                  <MenuItem
                                    disabled
                                    value=""
                                    sx={{
                                      fontSize: '14px',
                                    }}
                                  >
                                    <div className="text-[#78909C]">
                                      กรุณาเลือก
                                    </div>
                                  </MenuItem>
                                  {creditTypeList.map(
                                    (item: any, index: React.Key) => (
                                      <MenuItem
                                        key={index}
                                        value={item.id}
                                        sx={{
                                          fontSize: '14px',
                                        }}
                                      >
                                        {item.day} วัน
                                      </MenuItem>
                                    )
                                  )}
                                </Select>
                              </FormControl>
                            </>
                          )}
                        {/* {Boolean(hookFormErrors.creditDay) && ( */}
                        {/*    <FormHelperText */}
                        {/*        error */}
                        {/*        sx={{ */}
                        {/*          margin: '4px 14px 0', */}
                        {/*        }} */}
                        {/*    > */}
                        {/*      {hookFormErrors.creditDay?.message as ReactNode} */}
                        {/*    </FormHelperText> */}
                        {/* )} */}
                      </div>
                    )}
                  </ModalCreateOrderContentStyled>
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <Button
                      type="button"
                      variant="outlined"
                      color="blueGrey"
                      fullWidth
                      onClick={() => {
                        handleClose();
                      }}
                    >
                      <span>ยกเลิก</span>
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      onClick={async (event: any) => {
                        event.preventDefault();
                        await handleCreateOrder();
                      }}
                      disabled={
                        !selectedEstimate?.id ||
                        !selectedPaymentTypeId ||
                        (Number(selectedPaymentTypeId) === 2 && !selectedCredit)
                      }
                    >
                      {loadingCreateOrder ? (
                        <CircularProgress
                          size={20}
                          sx={{
                            color: 'white',
                          }}
                        />
                      ) : (
                        'สร้าง'
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalCreateOrder;
