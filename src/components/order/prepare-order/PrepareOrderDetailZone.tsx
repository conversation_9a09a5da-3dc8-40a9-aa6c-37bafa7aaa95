import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { numberWithCommas } from '@/utils/number';
import { isEmpty } from 'lodash';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingFadein } from '@/styles/share.styled';
import { salesOrderSelector } from '@/store/features/estimate';
import dayjs from 'dayjs';
import FullScreenPopup from '@/components/FullScreenPopup';
import Preview from '@/components/Preview';
import ToggleButtonGroup from '@/components/common/ToggleButtonGroup';
import IframePreview from '@/components/iframe-preview/IframePreview';

const PrepareOrderDetailZoneStyled = styled.div`
  width: 50%;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  .product-info {
    display: flex;
    column-gap: 24px;
    .product-image {
      width: 168px;
      min-width: 168px;
      height: 168px;
      border-radius: 16px;
    }
    .text-info {
      display: flex;
      flex-direction: column;
      max-width: 100%;
      overflow: hidden;
      justify-content: space-between;

      .ld-code {
        font-size: 20px;
        font-weight: 600;
        max-width: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .common-text {
        line-height: 1.6;
        div {
          max-width: 100%;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .na {
          color: #cfd8dc;
        }
      }
    }
    .model-image-wrap {
      border-radius: 16px;
      position: relative;
      overflow: hidden;
      width: 162px;
      min-width: 162px;
      height: 162px;
      img {
        width: 100%;
        height: 100%;
      }
      .model-preview {
        position: absolute;
        top: 8px;
        right: 8px;
        filter: drop-shadow(0 0 8px black);
        width: 34px;
        height: 34px;
        cursor: pointer;
      }
    }
  }
  .selector-preview-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    .preview-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 22px;
      overflow: hidden;
      .label {
        font-weight: 600;
        font-size: 16px;
        padding-bottom: 8px;
      }
      .list {
        width: 100%;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        column-gap: 24px;
        row-gap: 8px;
        border-bottom: 1px solid #263238;
        overflow: hidden;
        &.first-child {
          border-top: 1px solid #263238;
        }
        .key {
          font-size: 12px;
          white-space: nowrap;
        }
        .value {
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 100%;
          overflow: hidden;
          &.na {
            color: #dbe2e5;
          }
        }
      }
    }
  }
`;

type Props = {
  finishList: any[];
  displayUnit: string;
};

const PrepareOrderDetailZone = ({ finishList, displayUnit }: Props) => {
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('สรุปรายละเอียดสินค้า');
  const [material, setMaterial] = useState<{
    inside: 'kraft' | 'white' | 'gray';
    outside: 'kraft' | 'white' | 'gray';
  }>({ inside: 'white', outside: 'white' });

  const getCoatingName = (subMaterialId: number) => {
    const findCoatingName = finishList?.find(
      (fItem: any) => fItem.subMaterialId === subMaterialId
    )?.masterName;
    if (findCoatingName) {
      return findCoatingName;
    }
  };

  const convertToDisplayUnit = (value: any) => {
    const convertValue = parseFloat(value);
    if (displayUnit === 'cm') {
      return parseFloat((convertValue / 10).toFixed(2));
    }
    if (displayUnit === 'in') {
      return parseFloat((convertValue / 25.4).toFixed(2));
    }
    return parseFloat(convertValue.toFixed(2));
  };

  const safeDisplayValue = (value: any) => {
    const num = Number(value);
    return Number.isFinite(num)
      ? numberWithCommas(convertToDisplayUnit(num))
      : '0';
  };

  const checkWordFromString = (text: string, words: string[]): boolean => {
    if (!text || !words || words.length === 0) return false;
    const normalizedText = text.toLowerCase().trim();
    return words.some((word) =>
      normalizedText.includes(word.toLowerCase().trim())
    );
  };

  const onPreview3d = () => {
    const { subMaterialDetail } = estimateDataSalesOrder;
    if (!subMaterialDetail) {
      setMaterial({ inside: 'white', outside: 'white' });
    } else {
      const materialName = subMaterialDetail.name;
      if (checkWordFromString(materialName, ['อาร์ตการ์ด'])) {
        setMaterial({ inside: 'white', outside: 'white' });
      } else if (checkWordFromString(materialName, [' คราฟ'])) {
        setMaterial({ inside: 'kraft', outside: 'kraft' });
      } else if (checkWordFromString(materialName, ['หลังเทา'])) {
        setMaterial({ inside: 'gray', outside: 'white' });
      } else {
        setMaterial({ inside: 'white', outside: 'white' });
      }
    }
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const handleTabToggle = (tab: string) => {
    setActiveTab(tab);
  };

  // Animation variants for smooth transitions
  const tabContentVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  };

  const modelContainerVariants = {
    hidden: {
      opacity: 0,
      scale: 0.9,
      borderRadius: '24px',
    },
    visible: {
      opacity: 1,
      scale: 1,
      borderRadius: '16px',
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94],
        borderRadius: {
          duration: 0.3,
        },
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      borderRadius: '24px',
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  };

  return (
    <PrepareOrderDetailZoneStyled as={motion.div}>
      <div style={{ marginBottom: '24px' }}>
        <ToggleButtonGroup
          buttonItem={['โมเดลสินค้า', 'สรุปรายละเอียดสินค้า']}
          value={activeTab}
          handleToggle={handleTabToggle}
        />
      </div>

      <AnimatePresence mode="wait">
        {activeTab === 'โมเดลสินค้า' ? (
          <motion.div
            key="model-tab"
            variants={modelContainerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              height: '100%',
              overflow: 'hidden',
            }}
          >
            {/* <Preview */}
            {/*  modelId={estimateDataSalesOrder.productModel.modelCodePacdora} */}
            {/*  modelType={'3d'} */}
            {/*  width={estimateDataSalesOrder.width} */}
            {/*  height={estimateDataSalesOrder.height} */}
            {/*  length={estimateDataSalesOrder.length} */}
            {/*  material={material} */}
            {/*  onInfo={() => null} */}
            {/* /> */}
            <IframePreview
              modelId={estimateDataSalesOrder.productModel.modelCodePacdora}
              modelType={'3d'}
              width={estimateDataSalesOrder.width}
              height={estimateDataSalesOrder.height}
              length={estimateDataSalesOrder.length}
              material={material}
              onInfo={() => null}
            />
          </motion.div>
        ) : (
          <motion.div
            key="details-tab"
            variants={tabContentVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{ height: '100%' }}
          >
            <motion.div
              className="product-info"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="model-image-wrap">
                <Image
                  src={estimateDataSalesOrder.productModel.imageUrl}
                  alt=""
                  width={300}
                  height={300}
                />
                <div className="model-preview" onClick={onPreview3d}>
                  <Image
                    src={'/icons/icon-3d-rotation.svg'}
                    alt=""
                    width={24}
                    height={24}
                  />
                </div>
              </div>
              <div className="text-info">
                <div className="ld-code">{estimateDataSalesOrder.ldCode}</div>
                <div className="common-text">
                  <div>{estimateDataSalesOrder.productModel.productName}</div>
                  <div>
                    ขนาด {safeDisplayValue(estimateDataSalesOrder.length)} x{' '}
                    {safeDisplayValue(estimateDataSalesOrder.width)} x{' '}
                    {safeDisplayValue(estimateDataSalesOrder.height)}{' '}
                    {displayUnit}
                  </div>
                  <div>
                    วัสดุ:{' '}
                    {estimateDataSalesOrder.subMaterialDetail?.id ? (
                      <span>
                        {estimateDataSalesOrder.subMaterialDetail.name}
                      </span>
                    ) : (
                      <span className="na">ยังไม่ได้กำหนด</span>
                    )}
                  </div>
                  <div>
                    จำนวนผลิต{' '}
                    {estimateDataSalesOrder.estimateQuantity[0]?.quantity
                      ? `${numberWithCommas(
                          estimateDataSalesOrder.estimateQuantity[0]?.quantity
                        )} ชิ้น`
                      : '-'}
                  </div>
                  <div>
                    ระดับความละเอียด:{' '}
                    {estimateDataSalesOrder.detailLevelEnum === 'BASIC'
                      ? 'งานทั่วไป'
                      : 'งานละเอียด'}
                  </div>
                  <div>
                    กำหนดส่งสินค้า:{' '}
                    {dayjs(estimateDataSalesOrder.scheduleDate).format(
                      'DD/MM/YYYY'
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="selector-preview-wrap"
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              {/* Printing Front Side */}
              {(estimateDataSalesOrder.printingRequest?.printingSideDimension
                ?.id === 21 ||
                estimateDataSalesOrder.printingRequest?.printingSideDimension
                  ?.id === 23) && (
                <div className="preview-group">
                  <div className="label">ด้านหน้า</div>
                  <div className="list first-child">
                    <div className="key">การพิมพ์</div>
                    <div
                      className={`value ${
                        estimateDataSalesOrder.printingRequest.printSystem?.name
                          ? ''
                          : 'na'
                      }`}
                    >
                      {estimateDataSalesOrder.printingRequest.printSystem
                        ?.name || 'ยังไม่ได้กำหนด'}
                    </div>
                  </div>
                  {estimateDataSalesOrder.printingRequest?.colorFront?.map(
                    (cItem: any, index: number) => {
                      if (cItem.printAreaDimension.id && cItem.printColor.id) {
                        return (
                          <div className="list" key={index}>
                            <div className="key">สีด้านหน้า</div>
                            <div className="value">
                              {`${
                                `${cItem.printColor.name} ${cItem.colorCode}` ||
                                cItem.printColor.name
                              } • ${cItem.printAreaDimension.name}`}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                  {estimateDataSalesOrder.coatingRequest.finishFront?.map(
                    (ff: any, index: number) => {
                      if (ff.coatingMasterId && ff.finishSubMaterialDetail.id) {
                        return (
                          <div className="list" key={index}>
                            <div className="key">{`เคลือบ${
                              ff.coatingOrderEnum === 'BEFORE'
                                ? 'ก่อนพิมพ์'
                                : 'หลังพิมพ์'
                            }`}</div>
                            <div className="value">
                              {`${getCoatingName(
                                ff.finishSubMaterialDetail.subMaterialId
                              )} • ${ff.finishSubMaterialDetail.name}`}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                  {estimateDataSalesOrder.extra.map(
                    (extraItem: any, index: number) => {
                      if (extraItem.extraSideDimension.name === 'ด้านหน้า') {
                        return (
                          <div className="list" key={index}>
                            <div className="key">เทคนิคพิเศษ</div>
                            <div className="value">
                              {extraItem.blogSubMaterialDetail.name} •{' '}
                              {numberWithCommas(extraItem.quantity)} จุด •{' '}
                              {extraItem.extraAreaDimension?.name
                                ? extraItem.extraAreaDimension.name
                                : `${numberWithCommas(
                                    extraItem.widthEstimate
                                  )} x ${numberWithCommas(
                                    extraItem.heightEstimate
                                  )} นิ้ว`}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                </div>
              )}

              {/* Printing Back Side */}
              {(estimateDataSalesOrder.printingRequest?.printingSideDimension
                ?.id === 22 ||
                estimateDataSalesOrder.printingRequest?.printingSideDimension
                  ?.id === 23) && (
                <div className="preview-group">
                  <div className="label">ด้านหลัง</div>
                  <div className="list first-child">
                    <div className="key">การพิมพ์</div>
                    <div
                      className={`value ${
                        estimateDataSalesOrder.printingRequest.printSystem?.name
                          ? ''
                          : 'na'
                      }`}
                    >
                      {estimateDataSalesOrder.printingRequest.printSystem
                        ?.name || 'ยังไม่ได้กำหนด'}
                    </div>
                  </div>
                  {estimateDataSalesOrder.printingRequest?.colorBack?.map(
                    (cItem: any, index: number) => {
                      if (cItem.printAreaDimension.id && cItem.printColor.id) {
                        return (
                          <div className="list" key={index}>
                            <div className="key">สีด้านหลัง</div>
                            <div className="value">
                              {`${
                                `${cItem.printColor.name} ${cItem.colorCode}` ||
                                cItem.printColor.name
                              } • ${cItem.printAreaDimension.name}`}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                  {estimateDataSalesOrder.coatingRequest.finishBack?.map(
                    (ff: any, index: number) => {
                      if (ff.coatingMasterId && ff.finishSubMaterialDetail.id) {
                        return (
                          <div className="list" key={index}>
                            <div className="key">{`เคลือบ${
                              ff.coatingOrderEnum === 'BEFORE'
                                ? 'ก่อนพิมพ์'
                                : 'หลังพิมพ์'
                            }`}</div>
                            <div className="value">
                              {`${getCoatingName(
                                ff.finishSubMaterialDetail.subMaterialId
                              )} • ${ff.finishSubMaterialDetail.name}`}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                  {estimateDataSalesOrder.extra.map(
                    (extraItem: any, index: number) => {
                      if (extraItem.extraSideDimension.name === 'ด้านหลัง') {
                        return (
                          <div className="list" key={index}>
                            <div className="key">เทคนิคพิเศษ</div>
                            <div className="value">
                              {extraItem.blogSubMaterialDetail?.name} •{' '}
                              {numberWithCommas(extraItem.quantity)} จุด •{' '}
                              {extraItem.extraAreaDimension?.name}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )}
                </div>
              )}

              {/* Product Parts */}
              <div className="preview-group">
                <div className="label">ส่วนประกอบ</div>
                {!isEmpty(estimateDataSalesOrder.productParts) ? (
                  estimateDataSalesOrder.productParts.map(
                    (partItem: any, index: number) => (
                      <motion.div
                        key={`list-parts-${index}`}
                        className={`list ${index === 0 ? 'first-child' : ''}`}
                      >
                        <div className="key">
                          {partItem.subMaterialDetail.name}
                        </div>
                        <div className="value">
                          {numberWithCommas(partItem.value)} ชิ้น
                        </div>
                      </motion.div>
                    )
                  )
                ) : (
                  <motion.div className="list first-child">
                    <div className="value na">ยังไม่ได้กำหนด</div>
                  </motion.div>
                )}
              </div>

              {/* Product Design */}
              <div className="preview-group">
                <div className="label">ออกแบบอาร์ตเวิร์ก</div>
                {!isEmpty(estimateDataSalesOrder.productDesign) ? (
                  <div className="list first-child">
                    <div className="key">
                      {estimateDataSalesOrder.productDesign.name}
                    </div>
                    <div className="value">
                      {estimateDataSalesOrder.productDesign.description}
                    </div>
                  </div>
                ) : (
                  <div className="list first-child">
                    <div className="value na">ยังไม่ได้กำหนด</div>
                  </div>
                )}
              </div>

              {/* Master Example */}
              <div className="preview-group">
                <div className="label">ตัวอย่างสินค้า</div>
                {!isEmpty(estimateDataSalesOrder.masterExample) ? (
                  estimateDataSalesOrder.masterExample.map(
                    (extraItem: any, index: number) => (
                      <div
                        className={`list ${index === 0 ? 'first-child' : ''}`}
                        key={index}
                      >
                        <div className="key">{extraItem.name}</div>
                        <div className="value">{extraItem.description}</div>
                      </div>
                    )
                  )
                ) : (
                  <div className="list first-child">
                    <div className="value na">ยังไม่ได้กำหนด</div>
                  </div>
                )}
              </div>

              {/* Service Lay */}
              <div className="preview-group">
                <div className="label">บริการ</div>
                {!isEmpty(estimateDataSalesOrder.serviceLay) &&
                estimateDataSalesOrder.serviceLay[0].serviceLay.id &&
                estimateDataSalesOrder.serviceLay[0].serviceLayType.id ? (
                  estimateDataSalesOrder.serviceLay.map(
                    (sl: any, index: number) => {
                      const { serviceLay, serviceLayType } = sl;
                      if (serviceLay?.id && serviceLayType?.id) {
                        return (
                          <div
                            className={`list ${
                              index === 0 ? 'first-child' : ''
                            }`}
                            key={serviceLay.id}
                          >
                            <div className="key">{serviceLayType.name}</div>
                            <div className="value">{serviceLay.name}</div>
                          </div>
                        );
                      }
                      return null;
                    }
                  )
                ) : (
                  <div className="list first-child">
                    <div className="value na">ยังไม่ได้กำหนด</div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <FullScreenPopup
        isOpen={isPopupOpen}
        onClose={handleClosePopup}
        showCloseButton={true}
      >
        <Preview
          modelId={estimateDataSalesOrder.productModel.modelCodePacdora}
          modelType={'3d'}
          width={estimateDataSalesOrder.width}
          height={estimateDataSalesOrder.height}
          length={estimateDataSalesOrder.length}
          material={material}
          onInfo={() => null}
        />
      </FullScreenPopup>
    </PrepareOrderDetailZoneStyled>
  );
};

export default PrepareOrderDetailZone;
