import React, { ChangeEvent, useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  Button,
  CircularProgress,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import { isEmpty, isNull } from 'lodash';
import { FadeInStyled, LoadingFadein } from '@/styles/share.styled';
import apiMasterConfig from '@/services/stock/master-config';
import { useAppDispatch, useAppSelector } from '@/store';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import {
  MaterialConfigItem,
  ProductConfigCategory,
} from '@/components/order/prepare-order/PrepareOrderFormZone';
import { numberWithCommas } from '@/utils/number';
import Image from 'next/image';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const MaterialSideSelectorStyled = styled.div`
  width: 100%;
  margin-top: 24px;
  height: calc(100dvh - (128px + 48px));
  .material-item-wrap {
    margin-top: 16px;
    min-height: 356px;
    max-height: 356px;
    width: 100%;
    overflow: auto;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .material-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 12px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      position: relative;
      animation: ${LoadingFadein} 0.3s ease-in;
      &:hover {
        background: #f5f7f8;
      }
      span {
        margin: 0;
      }
      .image {
        width: 48px;
        height: 48px;
        min-width: 48px;
        border-radius: 8px;
        object-fit: cover;
        overflow: hidden;
      }
      .count {
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        font-weight: 600;
      }
    }
  }
  .sub-material-detail-config-selector {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    margin-top: 16px;
    min-height: calc(100dvh - 296px);
    margin-bottom: 16px;
    .sub-material-detail-list {
      width: 100%;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
      padding: 0 8px 0 12px;
      cursor: pointer;
      transition: 0.15s ease-out;
      &.checked {
        box-shadow: #000 0px 0px 0px 1px inset;
      }
      &.disabled {
        cursor: not-allowed;
        opacity: 0.4;
      }
    }
  }
  .empty {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 400;
    }
  }
  .button-warp {
    width: 100%;
    display: flex;
    align-items: end;
    height: 48px;
    position: sticky;
    bottom: 0;
    background: white;
    &:before {
      content: '';
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      top: -24px;
    }
    &:after {
      content: '';
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: -24px;
      background: white;
    }
  }
`;
type Props = {
  productConfig: ProductConfigCategory;
  hookForm: any;
  isOpen: boolean;
  step: number;
  setStep: (step: number) => void;
  setSubMaterialName: (name: string) => void;
  setOpenSideSelector: (open: boolean) => void;
  type: string;
};
type productSubMaterialDetailConfigType = {
  id: number;
  name: string;
  isProductConfig: boolean;
};
const ProductConfigSideSelector = ({
  productConfig,
  hookForm,
  isOpen,
  step,
  setStep,
  setSubMaterialName,
  setOpenSideSelector,
  type,
}: Props) => {
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const { setValue, watch } = hookForm;
  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [timer, setTimer] = useState<any>(null);
  const [selectedMasterId, setSelectedMasterId] = useState<number | null>(null);
  const [selectedSubMaterialId, setSelectedSubMaterialId] = useState<
    number | null
  >(null);
  const [selectedSubMaterialDetail, setSelectedSubMaterialDetail] =
    useState<any>([]);
  const [productSubMaterialDetailConfig, setProductSubMaterialDetailConfig] =
    useState<productSubMaterialDetailConfigType[]>([]);
  const [filters, setFilters] = useState<any>({
    search: '',
  });

  const handleSearch = (value: string) => {
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...filters,
        search: value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  useEffect(() => {
    if (isOpen) {
      setStep(1);
      setSelectedMasterId(null);
    }
  }, [isOpen]);

  const getProductSubMaterialDetailCheckList = async () => {
    const res = await apiMasterConfig.getProductSubMaterialCheckList({
      productId: estimateDataSalesOrder.productModel.productId,
      subMaterialId: selectedSubMaterialId,
      masterId: selectedMasterId,
      search: filters.search,
    });
    if (!res.isError) {
      const isProductConfig = res.data.filter(
        (item: productSubMaterialDetailConfigType) => item.isProductConfig
      );
      if (isProductConfig) {
        setProductSubMaterialDetailConfig(isProductConfig);
        setStep(2);
      } else setProductSubMaterialDetailConfig([]);
    }
  };

  useEffect(() => {
    if (!isNull(selectedMasterId)) {
      getProductSubMaterialDetailCheckList().then();
    }
  }, [selectedMasterId, filters]);

  const handleClickSubMaterial = async (
    subMaterialItem: MaterialConfigItem
  ) => {
    setSubMaterialName(subMaterialItem.subMaterialName);
    setSelectedMasterId(subMaterialItem.masterId);
    setSelectedSubMaterialId(subMaterialItem.subMaterialId);
  };

  const handleClickSubMaterialDetail = (subMaterialDetailItem: any) => {
    const subMaterialDetailId = subMaterialDetailItem.id;
    if (type === 'parts') {
      const isSelected = selectedSubMaterialDetail.some(
        (selectedSubMaterialDetailItem: any) =>
          selectedSubMaterialDetailItem.id === subMaterialDetailId
      );
      if (isSelected) {
        const filteredSubMaterialDetail = selectedSubMaterialDetail.filter(
          (selectedSubMaterialDetailItem: any) =>
            selectedSubMaterialDetailItem.id !== subMaterialDetailId
        );
        setSelectedSubMaterialDetail(filteredSubMaterialDetail);
      } else {
        setSelectedSubMaterialDetail([
          ...selectedSubMaterialDetail,
          subMaterialDetailItem,
        ]);
      }
    } else if (type === 'material') {
      if (subMaterialDetailId !== selectedSubMaterialDetail[0]?.id) {
        setSelectedSubMaterialDetail([subMaterialDetailItem]);
      } else {
        setSelectedSubMaterialDetail([]);
      }
    }
  };
  useEffect(() => {
    if (step === 1) {
      setSelectedMasterId(null);
      setSelectedSubMaterialId(null);
      setSelectedSubMaterialDetail([]);
      setProductSubMaterialDetailConfig([]);
    }
  }, [step]);

  const handleImportMaterial = () => {
    if (type === 'material') {
      if (selectedSubMaterialDetail[0].side === 1) {
        setValue('printingRequest', {
          ...watch('printingRequest'),
          printingSideDimensionId: 20,
          printSystemId: '',
          colorFront: [],
          colorBack: [],
        });
        setValue('coatingRequest', {
          ...watch('coatingRequest'),
          coatingSideDimensionId: 31,
          finishFront: [],
          finishBack: [],
        });
        dispatch(
          setEstimateDataSalesOrder({
            ...estimateDataSalesOrder,
            subMaterialDetail: selectedSubMaterialDetail[0],
            printingRequest: {
              ...estimateDataSalesOrder.printingRequest,
              colorFront: [],
              printingSideDimension: {
                id: 20,
                imageUrl:
                  'https://cdn.honconnect.co/master/dimension/blank.png',
                name: 'ไม่พิมพ์',
              },
            },
            coatingRequest: {
              ...estimateDataSalesOrder.coatingRequest,
              finishFront: [],
              coatingSideDimension: {
                id: 31,
                imageUrl:
                  'https://cdn.honconnect.co/master/dimension/blank.png',
                name: 'ไม่เคลือบ',
              },
            },
          })
        );
      } else {
        dispatch(
          setEstimateDataSalesOrder({
            ...estimateDataSalesOrder,
            subMaterialDetail: selectedSubMaterialDetail[0],
          })
        );
      }
      setValue('materialMasterId', selectedMasterId, {
        shouldValidate: true,
      });
      setValue('subMaterialDetailId', selectedSubMaterialDetail[0].id);
    } else if (type === 'parts') {
      const newSelectedSubMaterialDetailPart = selectedSubMaterialDetail.map(
        (selectedSubMaterialDetailItem: any) => {
          const updateLayDataOrderProductParts = {
            partMasterId: selectedMasterId,
            value: 1,
            subMaterialDetail: selectedSubMaterialDetailItem,
          };
          delete updateLayDataOrderProductParts.subMaterialDetail
            .isProductConfig;
          return updateLayDataOrderProductParts;
        }
      );
      const newSelectedSubMaterialDetailPartValue =
        newSelectedSubMaterialDetailPart.map(
          (newSelectedSubMaterialDetailPartItem: any) => {
            const partsValue = {
              ...newSelectedSubMaterialDetailPartItem,
              subMaterialDetailId:
                newSelectedSubMaterialDetailPartItem.subMaterialDetail.id,
            };
            delete partsValue.subMaterialDetail;
            delete partsValue.isProductConfig;
            return partsValue;
          }
        );
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          productParts: [
            ...estimateDataSalesOrder.productParts,
            ...newSelectedSubMaterialDetailPart,
          ],
        })
      );
      setValue('productParts', [
        ...watch('productParts'),
        ...newSelectedSubMaterialDetailPartValue,
      ]);
    }
    setOpenSideSelector(false);
  };
  // console.log('selectedSubMaterialDetail', selectedSubMaterialDetail);
  // console.log('productSubMaterialDetailConfig', productSubMaterialDetailConfig);
  // console.log('estimateDataSalesOrder', estimateDataSalesOrder);
  // console.log('watch', watch());
  return (
    <MaterialSideSelectorStyled>
      <TextField
        fullWidth
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
          handleSearch(e.target.value);
        }}
        placeholder="ค้นหา"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              {loadingSearch ? (
                <div className="flex items-center justify-center h-[24px] w-[24px]">
                  <CircularProgress size={20} />
                </div>
              ) : (
                <Search />
              )}
            </InputAdornment>
          ),
        }}
        sx={{ padding: '0 1px', marginTop: '1px' }}
      />
      {(() => {
        if (step === 1) {
          return (
            <div className="material-item-wrap">
              {productConfig?.productConfig?.map(
                (subMaterialItem: MaterialConfigItem) => (
                  <div
                    className="material-item"
                    onClick={() => handleClickSubMaterial(subMaterialItem)}
                    key={subMaterialItem.id}
                  >
                    <Image
                      src={
                        subMaterialItem.imageUrl ||
                        '/images/product/empty-product.svg'
                      }
                      width={120}
                      height={120}
                      alt=""
                      className="image"
                    />
                    <span>{subMaterialItem.subMaterialName}</span>
                    <div className="count">
                      {numberWithCommas(subMaterialItem.count)}
                    </div>
                  </div>
                )
              )}
              {isEmpty(productConfig.productConfig) && (
                <div className="empty">
                  <h4>ไม่มีรายการ</h4>
                </div>
              )}
            </div>
          );
        }
        if (step === 2) {
          return (
            <FadeInStyled className="sub-material-detail-config-selector">
              {!isEmpty(productSubMaterialDetailConfig) ? (
                productSubMaterialDetailConfig.map(
                  (
                    subMaterialDetailItem: productSubMaterialDetailConfigType
                  ) => {
                    const isSelectedInParts =
                      estimateDataSalesOrder.productParts.some(
                        (part: any) =>
                          part.subMaterialDetail.id === subMaterialDetailItem.id
                      );
                    const isChecked = selectedSubMaterialDetail.some(
                      (selectedSubMaterialDetailItem: any) =>
                        selectedSubMaterialDetailItem.id ===
                        subMaterialDetailItem.id
                    );
                    return (
                      <div
                        className={`sub-material-detail-list ${
                          isChecked ? 'checked' : ''
                        } ${!isSelectedInParts ? '' : 'disabled'}`}
                        key={subMaterialDetailItem.id}
                        onClick={() => {
                          if (!isSelectedInParts) {
                            handleClickSubMaterialDetail(subMaterialDetailItem);
                          }
                        }}
                      >
                        {subMaterialDetailItem.name}
                        {isChecked && (
                          <FadeInStyled
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            <CheckCircleRoundedIcon
                              sx={{
                                fontSize: '24px',
                              }}
                            />
                          </FadeInStyled>
                        )}
                      </div>
                    );
                  }
                )
              ) : (
                <div className="empty">
                  <h4>ไม่มีรายการ</h4>
                </div>
              )}
            </FadeInStyled>
          );
        }
        return null;
      })()}

      {step === 2 && !isEmpty(productSubMaterialDetailConfig) && (
        <div className="button-warp">
          <Button
            type="button"
            variant="contained"
            color="dark"
            fullWidth
            disabled={
              !productSubMaterialDetailConfig.some(
                (
                  productSubMaterialDetailConfigItem: productSubMaterialDetailConfigType
                ) =>
                  selectedSubMaterialDetail.some(
                    (selectedSubMaterialDetailItem: any) =>
                      selectedSubMaterialDetailItem.id ===
                      productSubMaterialDetailConfigItem.id
                  )
              )
            }
            onClick={handleImportMaterial}
          >
            ตกลง
          </Button>
        </div>
      )}
    </MaterialSideSelectorStyled>
  );
};

export default ProductConfigSideSelector;
