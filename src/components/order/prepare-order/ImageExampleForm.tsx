import React, { useState } from 'react';
import styled from 'styled-components';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { validateImageFiles } from '@/utils/size';
import { useRouter } from 'next/router';
import apiEstimateProduct from '@/services/order/estimate-product';
import { setSnackBar } from '@/store/features/alert';
import Image from 'next/image';
import { LoadingFadein } from '@/styles/share.styled';
import DeleteButton from '@/components/global/DeleteButton';
import useMediumZoom from '@/hooks/useMediumZoom';

const ImageExampleFormStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  row-gap: 16px;
  .image-list {
    width: 100%;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid #dbe2e5;
    display: flex;
    align-items: center;
    column-gap: 16px;
    position: relative;
    animation: ${LoadingFadein} 0.3s ease-in;

    .image-wrap {
      width: 80px;
      height: 80px;
      min-width: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      background: #f5f7f8;
      cursor: pointer;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      * {
        transition: 0.2s ease-in-out;
      }
      &:hover {
        svg {
          rotate: 90deg;
        }
      }
    }
    .text-wrap {
      display: flex;
      flex-direction: column;
      column-gap: 4px;
      .name {
        font-weight: 600;
        font-size: 16px;
      }
      .desc {
        color: #b0bec5;
      }
    }
    .delete {
      position: absolute;
      z-index: 1;
      right: 0;
      top: 0;
      * {
        background: transparent;
      }
    }
  }
`;

const ImageExampleForm = () => {
  const router = useRouter();
  const { estimateProductId } = router.query;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [, setErrorImageUpload] = useState<any>({
    status: false,
    message: '',
  });
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isProgress, setIsProgress] = useState<boolean>(false);
  useMediumZoom('.zoom-image', {
    margin: 40,
    container: '#zoom-container',
  });

  const getEstimateProductById = async () => {
    const res = await apiEstimateProduct.getEstimateProductById(
      estimateProductId as string
    );
    if (!res.isError) {
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          imageResolution: res.data.imageResolution,
        })
      );
    }
  };
  const handleFileUpload = async (files: File[]) => {
    if (files && estimateProductId) {
      const formData = new FormData();
      formData.append('file', files[0]);
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent: ProgressEvent) => {
          const percentComplete = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
          setUploadProgress(percentComplete);
        },
      };
      const res = await apiEstimateProduct.uploadImageExample(
        formData,
        config,
        estimateProductId.toString()
      );
      setUploadProgress(0);
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        setErrorImageUpload({ status: false, message: '' });
        await getEstimateProductById();
      } else {
        setErrorImageUpload({
          status: true,
          message: res.error.response.data.message,
        });
      }
    }
  };
  const handleRemoveImage = async (imageItem: any) => {
    const res = await apiEstimateProduct.deleteImageExample(imageItem.id);
    if (!res.isError) {
      await getEstimateProductById();
    }
  };
  return (
    <>
      <p className="topic">รูปสินค้า/ตัวอย่าง</p>
      <ImageExampleFormStyled>
        <div className="image-list">
          <label>
            <input
              type="file"
              style={{ display: 'none' }}
              onChange={async (event: any) => {
                const { files } = event.target;
                setIsProgress(true);
                const validationResult = await validateImageFiles(
                  files,
                  10,
                  true,
                  false
                );
                setIsProgress(false);
                if (validationResult.status) {
                  const newFiles = Array.from(validationResult.files);
                  await handleFileUpload(newFiles);
                  setErrorImageUpload({
                    status: false,
                    message: '',
                  });
                } else {
                  setErrorImageUpload({
                    status: true,
                    message: validationResult.message,
                  });
                }
              }}
            />
            <div className="image-wrap">
              <AddRoundedIcon />
            </div>
          </label>
          <div className="text-wrap">
            <span className="name">
              {uploadProgress !== 0
                ? `กำลังอัปโหลด...${uploadProgress}%`
                : isProgress
                ? 'กำลังบีบอัด...'
                : 'เลือกรูป'}
            </span>
            <span className="desc">JPG, GIF, PNG – Max size 10MB</span>
          </div>
        </div>

        {estimateDataSalesOrder.imageResolution?.map(
          (imageItem: any, index: number) => {
            return (
              <div className="image-list" key={imageItem.id}>
                <div className="delete">
                  <DeleteButton onClick={() => handleRemoveImage(imageItem)}>
                    <Image
                      src="/icons/delete-white.svg"
                      width={24}
                      height={24}
                      alt=""
                    />
                  </DeleteButton>
                </div>
                <div className="image-wrap">
                  <img
                    src={imageItem.imageResolutionUrl}
                    className="zoom-image"
                  />
                </div>
                <div className="text-wrap">
                  <span className="name">{`รูปที่ ${index + 1}`}</span>
                  <span className="desc">JPG, GIF, PNG – Max size 10MB</span>
                </div>
              </div>
            );
          }
        )}
      </ImageExampleFormStyled>
    </>
  );
};

export default ImageExampleForm;
