import React, { ReactNode, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FormHelperText, InputAdornment, TextField } from '@mui/material';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { numberWithCommas } from '@/utils/number';
import { NumericFormat } from 'react-number-format';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import { Controller } from 'react-hook-form';
import SvgDimansionIcon from '@/components/svg-icon/SvgDimansionIcon';

const ProductSizeStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  padding: 16px;
  .top-zone {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 24px;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      bottom: -16px;
      height: 1px;
      width: calc(100% + 32px);
      background: #dbe2e5;
      left: -16px;
    }
    .profile-group {
      display: flex;
      align-items: center;
      column-gap: 12px;
      .profile {
        height: 40px;
        width: 40px;
        min-width: 40px;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .name-n-role {
        display: flex;
        flex-direction: column;
        .name {
          font-weight: 600;
        }
        .role {
          font-size: 12px;
        }
      }
    }
    .quantity {
      width: 124px;
    }
  }
  .bottom-zone {
    display: flex;
    column-gap: 12px;
    margin-top: 8px;
    > div {
      flex: 1 1 0%;
    }
    .dimension-icon {
      flex: unset;
      margin-right: 6px;
      svg {
        width: 44px;
        height: 44px;
        min-width: 44px;
      }
    }
    .field {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      overflow: hidden;
      .MuiFormControl-root {
        padding: 1px;
      }
      .minmax {
        font-size: 10px;
        color: #dbe2e5;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .input-adornment-label {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 700;
      width: 40px;
      min-width: 40px;
      border-right: 1px solid #dbe2e5;
      line-height: 1;
      &.r {
        color: #fe4902;
      }
      &.g {
        color: #008910;
      }
      &.b {
        color: #0344dc;
      }
    }
  }
`;

type Props = {
  hookForm: any;
  productInfo: any;
  displayUnit: string;
};

const ProductSizeFrom = ({ hookForm, productInfo, displayUnit }: Props) => {
  const { register, hookFormErrors, setValue, watch, isSubmitted } = hookForm;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const prevUnitRef = useRef(displayUnit);

  // const watchQuantity = useWatch({
  //   control,
  //   name: 'layDataQuantity',
  //   defaultValue: '',
  // });
  //
  // useEffect(() => {
  //   dispatch(
  //     setEstimateDataSalesOrder({
  //       ...estimateDataSalesOrder,
  //       layDataQuantity: [
  //         {
  //           ...estimateDataSalesOrder.layDataQuantity[0],
  //           quantity: watchQuantity[0]?.quantity,
  //         },
  //       ],
  //     })
  //   );
  // }, [watchQuantity]);

  const watchWidth = watch('width');
  const watchHeight = watch('height');
  const watchLength = watch('length');

  useEffect(() => {
    const prevUnit = prevUnitRef.current;

    // ถ้า displayUnit เปลี่ยนจริง และมีค่าเดิมใน form
    if (displayUnit !== prevUnit) {
      const width = watch('width');
      const height = watch('height');
      const length = watch('length');

      const convert = (
        val: number | string | null | undefined,
        fromUnit: string,
        toUnit: string
      ) => {
        if (!val || Number.isNaN(Number(val))) return '';
        let mm = parseFloat(val.toString());

        // convert from previous unit → mm
        if (fromUnit === 'cm') mm *= 10;
        else if (fromUnit === 'in') mm *= 25.4;

        // convert mm → new unit
        if (toUnit === 'cm') return parseFloat((mm / 10).toFixed(2));
        if (toUnit === 'in') return parseFloat((mm / 25.4).toFixed(2));
        return parseFloat(mm.toFixed(2)); // mm
      };

      if (width) setValue('width', convert(width, prevUnit, displayUnit));
      if (height) setValue('height', convert(height, prevUnit, displayUnit));
      if (length) setValue('length', convert(length, prevUnit, displayUnit));
    }

    // อัปเดตหน่วยก่อนหน้า
    prevUnitRef.current = displayUnit;
  }, [displayUnit]);

  useEffect(() => {
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        width: watchWidth,
        height: watchHeight,
        length: watchLength,
      })
    );
  }, [watchWidth, watchHeight, watchLength]);

  const convertValue = (val: any, fromUnit: string, toUnit: string) => {
    if (!val || Number.isNaN(Number(val))) return '';
    let mm = parseFloat(val.toString());

    if (fromUnit === 'cm') mm *= 10;
    else if (fromUnit === 'in') mm *= 25.4;

    if (toUnit === 'cm') return parseFloat((mm / 10).toFixed(2));
    if (toUnit === 'in') return parseFloat((mm / 25.4).toFixed(2));
    return parseFloat(mm.toFixed(2));
  };

  return (
    <ProductSizeStyled>
      <div className="top-zone">
        <div className="profile-group">
          <div className="profile">
            <Image
              src="/images/product/empty-product.svg"
              alt=""
              width={80}
              height={80}
            />
          </div>
          <div className="name-n-role">
            <div className="name">ทนงศักดิ์ ต้นนพรัตน์</div>
            <div className="role">ลูกค้า • บุคคลธรรมดา • เครดิต 30 วัน</div>
          </div>
        </div>
        <div className="quantity">
          <NumericFormat
            decimalScale={2}
            placeholder="จำนวน"
            customInput={TextField}
            {...register('estimateQuantity[0].quantity')}
            value={watch('estimateQuantity[0].quantity')}
            thousandSeparator
            onChange={(e) => {
              const rawValue = e.target.value.replace(/,/g, '');
              const value = parseFloat(rawValue) || '';

              setValue('estimateQuantity[0].quantity', value, {
                shouldValidate: !!isSubmitted,
              });

              dispatch(
                setEstimateDataSalesOrder({
                  ...estimateDataSalesOrder,
                  estimateQuantity: [{ quantity: value }],
                })
              );
            }}
            error={Boolean(hookFormErrors.estimateQuantity?.[0]?.quantity)}
            helperText={
              hookFormErrors.estimateQuantity?.[0]?.quantity
                ?.message as ReactNode
            }
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">ชิ้น</InputAdornment>
              ),
            }}
          />
        </div>
      </div>
      <div className="bottom-zone">
        {/* <Image src="/icons/icon-layer.svg" alt="" width={64} height={64} /> */}
        <div className="dimension-icon">
          <SvgDimansionIcon />
        </div>
        <div className="field">
          <Controller
            name="length"
            control={hookForm.control}
            render={({ field }) => (
              <TextField
                type="number"
                fullWidth
                placeholder="Length"
                value={convertValue(field.value, 'mm', displayUnit)}
                onChange={(e) => {
                  const inputValue = parseFloat(e.target.value);
                  field.onChange(convertValue(inputValue, displayUnit, 'mm'));
                }}
                error={Boolean(hookFormErrors.length)}
                helperText={
                  hookFormErrors.length &&
                  (hookFormErrors.length.message as ReactNode)
                }
                sx={{
                  '.MuiInputBase-root': { paddingLeft: '0' },
                  '.MuiInputBase-input': { paddingLeft: '8px' },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <div className="input-adornment-label b">L</div>
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <div style={{ color: '#dbe2e5' }}>{displayUnit}</div>
                  ),
                }}
              />
            )}
          />
          <div className="minmax">
            Length min{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.minLength, 'mm', displayUnit)
            )}{' '}
            {displayUnit}/max{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.maxLength, 'mm', displayUnit)
            )}{' '}
            {displayUnit}
          </div>
        </div>

        <div className="field">
          <Controller
            name="width"
            control={hookForm.control}
            render={({ field }) => (
              <TextField
                type="number"
                fullWidth
                placeholder="Width"
                value={convertValue(field.value, 'mm', displayUnit)}
                onChange={(e) => {
                  const inputValue = parseFloat(e.target.value);
                  field.onChange(convertValue(inputValue, displayUnit, 'mm'));
                }}
                error={Boolean(hookFormErrors.width)}
                helperText={
                  hookFormErrors.width &&
                  (hookFormErrors.width.message as ReactNode)
                }
                sx={{
                  '.MuiInputBase-root': { paddingLeft: '0' },
                  '.MuiInputBase-input': { paddingLeft: '8px' },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <div className="input-adornment-label r">W</div>
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <div style={{ color: '#dbe2e5' }}>{displayUnit}</div>
                  ),
                }}
              />
            )}
          />
          {hookFormErrors.optionsCostTypeId && (
            <FormHelperText error>
              {hookFormErrors.optionsCostTypeId.message as ReactNode}
            </FormHelperText>
          )}
          <div className="minmax">
            Width min{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.minWidth, 'mm', displayUnit)
            )}{' '}
            {displayUnit}/max{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.maxWidth, 'mm', displayUnit)
            )}{' '}
            {displayUnit}
          </div>
        </div>

        <div className="field">
          <Controller
            name="height"
            control={hookForm.control}
            render={({ field }) => (
              <TextField
                type="number"
                fullWidth
                placeholder="Height"
                value={convertValue(field.value, 'mm', displayUnit)}
                onChange={(e) => {
                  const inputValue = parseFloat(e.target.value);
                  field.onChange(convertValue(inputValue, displayUnit, 'mm'));
                }}
                error={Boolean(hookFormErrors.height)}
                helperText={
                  hookFormErrors.height &&
                  (hookFormErrors.height.message as ReactNode)
                }
                sx={{
                  '.MuiInputBase-root': { paddingLeft: '0' },
                  '.MuiInputBase-input': { paddingLeft: '8px' },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <div className="input-adornment-label g">H</div>
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <div style={{ color: '#dbe2e5' }}>{displayUnit}</div>
                  ),
                }}
              />
            )}
          />
          <div className="minmax">
            Height min{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.minHeight, 'mm', displayUnit)
            )}{' '}
            {displayUnit}/max{' '}
            {numberWithCommas(
              convertValue(productInfo.productSize.maxHeight, 'mm', displayUnit)
            )}{' '}
            {displayUnit}
          </div>
        </div>
      </div>
    </ProductSizeStyled>
  );
};

export default ProductSizeFrom;
