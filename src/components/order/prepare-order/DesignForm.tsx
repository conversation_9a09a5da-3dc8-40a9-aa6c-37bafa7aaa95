import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import apiProductDesign from '@/services/stock/product-design';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import ActionButton from '@/components/ActionButton';
import SvgUploadIcon from '@/components/svg-icon/SvgUploadIcon';
import { setSnackBar } from '@/store/features/alert';
import { AnimatePresence, motion } from 'framer-motion';
import {
  motionFadeConfig,
  motionListItemConfig,
  motionProgress,
} from '@/utils/motion/motion-config';
import apiEstimateProduct from '@/services/order/estimate-product';
import SvgDownloadIcon from '@/components/svg-icon/SvgDownloadIcon';
import { Controller } from 'react-hook-form';
import { TextField } from '@mui/material';

type Props = {
  hookForm: any;
};

const DesignForm = ({ hookForm }: Props) => {
  const { setValue, watch, hookFormErrors } = hookForm;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [designList, setDesignList] = useState<any[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const getDesign = async () => {
    const res = await apiProductDesign.getList();
    if (res && !res.isError) {
      setDesignList(res.data);
    }
  };

  useEffect(() => {
    getDesign().then();
  }, []);

  const handleSelect = (design: any) => {
    setValue('productDesignId', design.id);
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        productDesign: design,
      })
    );
  };

  const validateFile = (file: File): boolean => {
    const maxSize = 5 * 1024 * 1024;

    if (file.size > maxSize) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ไฟล์มีขนาดเกิน 5MB กรุณาเลือกไฟล์ที่เล็กกว่า',
          severity: 'error',
        })
      );
      return false;
    }

    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/svg+xml',
      'application/pdf',
      'application/zip',
      'application/x-photoshop',
      'application/postscript',
      'font/ttf',
    ];

    const allowedExtensions =
      /\.(jpg|jpeg|png|svg|psd|ai|pdf|ttf|zip|abr|pat)$/i;

    const isAllowedType = allowedTypes.includes(file.type);
    const isAllowedExt = allowedExtensions.test(file.name.toLowerCase());

    if (!isAllowedType && !isAllowedExt) {
      dispatch(
        setSnackBar({
          status: true,
          text: `ไม่รองรับประเภทไฟล์: ${file.name}`,
          severity: 'error',
        })
      );
      return false;
    }

    return true;
  };

  const reFetchEsDataAfterUploadFile = async () => {
    const res = await apiEstimateProduct.getEstimateProductById(
      estimateDataSalesOrder.id as string
    );
    if (!res.isError) {
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          fileExtraUrl: res.data.fileExtraUrl,
        })
      );
    }
  };

  const uploadFileToServer = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const config = {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent: ProgressEvent) => {
        const percentComplete = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
        setUploadProgress(percentComplete);
      },
    };
    const res = await apiEstimateProduct.uploadFileExtraUrl(
      formData,
      config,
      estimateDataSalesOrder.id
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await reFetchEsDataAfterUploadFile();
    }
    setUploadProgress(0);
  };
  const getFileName = (url: string): string => {
    try {
      return url.split('/').pop() || '';
    } catch {
      return '';
    }
  };
  return (
    <>
      <p className="topic">ออกแบบอาร์ตเวิร์ก</p>
      <DesignFormStyled>
        {designList.map((item: any) => (
          <div
            key={item.id}
            className={`card-selector ${
              watch('productDesignId') === item.id ? 'active' : ''
            }`}
            onClick={() => handleSelect(item)}
          >
            {watch('productDesignId') === item.id && (
              <div className="checked">
                <CheckRoundedIcon sx={{ fontSize: '16px', color: 'white' }} />
              </div>
            )}
            <div className="image-wrap">
              <Image
                src={item.imageUrl || '/images/product/empty-product.svg'}
                alt={item.name}
                width={200}
                height={200}
              />
            </div>
            <div className="text-group">
              <span className="name">{item.name}</span>
              <span className="description">{item.description}</span>
            </div>
          </div>
        ))}
      </DesignFormStyled>
      <AnimatePresence mode="sync">
        {watch('productDesignId') === 2 && (
          <UploadFileStyled
            as={motion.div}
            {...motionListItemConfig}
            animate={{
              ...motionListItemConfig.animate,
              marginBottom: 0,
            }}
            exit={{
              ...motionListItemConfig.exit,
              marginTop: 0,
              transition: {
                ...motionListItemConfig.exit?.transition,
                marginTop: {
                  duration: 0.3,
                  delay: 0.3,
                },
              },
            }}
          >
            <div className="file-zone">
              <AnimatePresence mode="sync" initial={false}>
                {uploadProgress && (
                  <motion.div
                    key="progress-bar"
                    className="progress-bar"
                    custom={uploadProgress}
                    variants={motionProgress}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                  />
                )}
              </AnimatePresence>
              <input
                type="file"
                accept=".jpg,.jpeg,.png,.svg,.psd,.ai,.pdf,.ttf,.zip,.abr,.pat"
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (!file) return;
                  const isValid = validateFile(file);
                  if (!isValid) {
                    e.target.value = '';
                    return;
                  }
                  await uploadFileToServer(file);
                  e.target.value = '';
                }}
                style={{ display: 'none' }}
                ref={fileInputRef}
              />
              <ActionButton
                variant="outlined"
                color="blueGrey"
                icon={<SvgUploadIcon />}
                text={
                  estimateDataSalesOrder.fileExtraUrl
                    ? 'แก้ไขไฟล์'
                    : 'อัปโหลดไฟล์'
                }
                onClick={() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }}
              />
              <div className="text-validate">
                File types supported: JPG, PNG, SVG, PSD, AI, PDF, TTF, ZIP,
                ABR, PAT and more. Max size 5MB.
              </div>
            </div>
            <AnimatePresence mode="sync">
              {estimateDataSalesOrder.fileExtraUrl && (
                <motion.div
                  className="file"
                  key="file-item"
                  {...motionFadeConfig}
                >
                  <span>
                    {getFileName(estimateDataSalesOrder.fileExtraUrl)}
                  </span>
                  <a
                    href={estimateDataSalesOrder.fileExtraUrl}
                    download={getFileName(estimateDataSalesOrder.fileExtraUrl)}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <SvgDownloadIcon />
                  </a>
                </motion.div>
              )}
            </AnimatePresence>
            <div>
              <div className="label">ลิงก์ไฟล์</div>
              <Controller
                name="linkExtra"
                control={hookForm.control}
                render={({ field }) => (
                  <TextField
                    type="text"
                    fullWidth
                    value={watch('linkExtra') || ''}
                    placeholder="https://"
                    onChange={(e) => {
                      field.onChange(e.target.value);
                    }}
                    error={Boolean(hookFormErrors.linkExtra)}
                    helperText={hookFormErrors.linkExtra?.message as ReactNode}
                  />
                )}
              />
            </div>
          </UploadFileStyled>
        )}
      </AnimatePresence>
    </>
  );
};

const UploadFileStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  column-gap: 24px;
  margin-top: 24px;

  .file-zone {
    border-radius: 12px;
    border: 1px dashed #dbe2e5;
    padding: 16px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    row-gap: 8px;
    background: #f5f7f8;
    position: relative;
    overflow: hidden;
    .progress-bar {
      width: 0;
      height: 100%;
      position: absolute;
      left: 0;
      background: rgba(143, 217, 255, 0.36);
    }

    button {
      width: fit-content;
    }

    .text-validate {
      font-size: 10px;
      color: #b0bec5;
      text-align: center;
      width: 256px;
      max-width: calc(100% - 32px);
    }
  }
  .file {
    width: 100%;
    padding: 16px;
    background: #f5f7f8;
    border-radius: 12px;
    border: 1px solid #dbe2e5;
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 24px;
    max-width: 100%;
    overflow: hidden;
    will-change: opacity;
    span {
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    a {
      display: flex;
      align-items: center;
      justify-content: center;
      svg {
        cursor: pointer;
      }
    }
  }
`;
const DesignFormStyled = styled.div`
  width: 100%;
  display: grid;
  grid-gap: 16px;
  position: relative;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  margin-top: 8px;
  .card-selector {
    width: 100%;
    padding: 8px;
    border-radius: 16px;
    display: flex;
    column-gap: 16px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    background: white;
    align-items: center;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    &.active {
      box-shadow: #263238 0px 0px 0px 2px inset;
    }
    .checked {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 20px;
      height: 20px;
      min-width: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: black;
      border-radius: 50%;
      color: white;
      font-size: 14px;
    }
    .image-wrap {
      width: 52px;
      min-width: 52px;
      height: 52px;
      border-radius: 8px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .text-group {
      display: flex;
      flex-direction: column;
      row-gap: 2px;
      overflow: hidden;
      .name {
        font-weight: 600;
        max-width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .description {
        font-size: 12px;
        max-width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
`;
export default DesignForm;
