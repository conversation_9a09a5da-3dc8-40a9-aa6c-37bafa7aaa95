import React, { useEffect } from 'react';
import styled from 'styled-components';
import { FormControl, FormHelperText, MenuItem, Select } from '@mui/material';
import { LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/th';

import SvgDatePickerIcon from '@/components/svg-icon/SvgDatePickerIcon';
import { LoadingFadein } from '@/styles/share.styled';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import { useAppDispatch, useAppSelector } from '@/store';

const DetailLevelFormStyled = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  .label {
    margin-top: 0;
  }
  .MuiInputBase-root {
    cursor: pointer;
    input {
      cursor: pointer;
    }
  }
`;

type Props = { hookForm: any };

const DetailLevelForm = ({ hookForm }: Props) => {
  const { register, setValue, hookFormErrors, watch, isSubmitted } = hookForm;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [dateValue, setDateValue] = React.useState<Dayjs | null>();

  const detailLevelList = [
    { id: 1, name: 'งานทั่วไป', value: 1 },
    { id: 2, name: 'งานละเอียด', value: 2 },
  ];

  const handleChangeDate = (value: unknown) => {
    let parsed: Dayjs | null = null;

    if (dayjs.isDayjs(value)) {
      parsed = value;
    } else if (typeof value === 'string') {
      parsed = dayjs(value, 'DD/MM/YYYY');
    }

    if (parsed && parsed.isValid()) {
      setDateValue(parsed);
      setValue('scheduleDate', parsed.format('YYYY-MM-DD'), {
        shouldValidate: !!isSubmitted,
        shouldDirty: true,
      });
      const timestamp = parsed.valueOf();
      if (timestamp) {
        dispatch(
          setEstimateDataSalesOrder({
            ...estimateDataSalesOrder,
            scheduleDate: timestamp,
          })
        );
      }
    }
  };

  useEffect(() => {
    if (dateValue) {
      setValue('scheduleDate', dateValue.format('YYYY-MM-DD'), {
        shouldValidate: !!isSubmitted,
        shouldDirty: true,
      });
    }
  }, [dateValue, setValue]);

  useEffect(() => {
    const raw = watch('scheduleDate');
    if (!raw) return;

    const parsed = dayjs.isDayjs(raw)
      ? raw
      : dayjs(raw, ['YYYY-MM-DD', 'DD/MM/YYYY']);

    if (parsed.isValid()) {
      setDateValue(parsed);
    }
  }, []);

  const handleChangeDetallLevel = (value: number) => {
    setValue('detailLevel', value);
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        detailLevelEnum: value === 1 ? 'BASIC' : 'ADVANCED',
      })
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="th">
      <DetailLevelFormStyled>
        <div style={{ display: 'flex', alignItems: 'center', columnGap: 16 }}>
          {/* --- Select ความละเอียดงาน --- */}
          <div className="w-full">
            <div className="label">ระดับความละเอียด</div>
            <FormControl fullWidth>
              <Select
                displayEmpty
                {...register('detailLevel')}
                error={Boolean(hookFormErrors.detailLevel)}
                value={watch('detailLevel') || ''}
                onChange={(e) => {
                  handleChangeDetallLevel(e.target.value as number);
                }}
              >
                <MenuItem disabled value="">
                  <div className="text-[#78909C]">กรุณาเลือก</div>
                </MenuItem>
                {detailLevelList.map((item) => (
                  <MenuItem key={item.id} value={item.value}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              {hookFormErrors.detailLevel && (
                <FormHelperText error>
                  {hookFormErrors.detailLevel.message as React.ReactNode}
                </FormHelperText>
              )}
            </FormControl>
          </div>
          {/* --- Date Picker --- */}
          <div className="w-full">
            <div className="label">กำหนดส่งสินค้า</div>
            <MobileDatePicker
              value={dateValue}
              onChange={handleChangeDate}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: Boolean(hookFormErrors.scheduleDate),
                  helperText: hookFormErrors.scheduleDate
                    ?.message as React.ReactNode,
                  InputProps: { endAdornment: <SvgDatePickerIcon /> },
                  ...register('scheduleDate'),
                },
              }}
            />
          </div>
        </div>
      </DetailLevelFormStyled>
    </LocalizationProvider>
  );
};

export default DetailLevelForm;
