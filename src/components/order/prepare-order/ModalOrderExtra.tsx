import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  InputAdornment,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import styled from 'styled-components';
import { isEmpty } from 'lodash';
import useMediumZoom from '@/hooks/useMediumZoom';

type Props = {
  open: boolean;
  handleClose: () => void;
  handleSubmitExtra: (value: any) => void;
  initialValues: any;
  extraSideList: any;
  extraAreaList: any;
  extraConfigList: any;
};

const ModalOrderExtra = ({
  open,
  handleClose,
  initialValues,
  handleSubmitExtra,
  extraSideList,
  extraAreaList,
  extraConfigList,
}: Props) => {
  // const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [isQuantity, setIsQuantity] = useState<boolean>(false);
  const [isSize, setIsSize] = useState<boolean>(false);
  useMediumZoom('.zoom-imagez', {
    margin: 40,
    container: '#zoom-container',
  });

  const validationSchema = React.useMemo(
    () =>
      yup.object({
        extraMasterId: yup
          .number()
          .required('กรุณาเลือกประเภท')
          .typeError('กรุณาเลือกประเภท'),
        blogSubMaterialDetailId: yup
          .number()
          .required('กรุณาเลือกเทคนิค')
          .typeError('กรุณาเลือกเทคนิค'),
        subMaterialId: yup
          .number()
          .required('กรุณาเลือก')
          .typeError('กรุณาเลือก'),
        extraSideDimensionId: yup
          .number()
          .required('กรุณาเลือกด้าน')
          .typeError('กรุณาเลือกด้าน'),
        extraAreaDimensionId: yup
          .number()
          .transform((value, originalValue) =>
            originalValue === '' || originalValue === null ? undefined : value
          )
          .when([], {
            is: () => !isSize,
            then: (schema) =>
              schema
                .required('กรุณาเลือกพื้นที่')
                .typeError('กรุณาเลือกพื้นที่'),
            otherwise: (schema) => schema.notRequired(),
          }),
        widthEstimate: yup.string().when([], {
          is: () => isSize,
          then: (s) => s.required('กรุณากรอกความกว้าง'),
          otherwise: (s) => s.notRequired().nullable(),
        }),
        heightEstimate: yup.string().when([], {
          is: () => isSize,
          then: (s) => s.required('กรุณากรอกความสูง'),
          otherwise: (s) => s.notRequired().nullable(),
        }),
        quantity: yup.string().required('กรุณากรอกจำนวน'),
      }),
    [isSize]
  );

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors, isSubmitted },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      extraMasterId: initialValues.extraMasterId || '',
      blogSubMaterialDetailId: initialValues.blogSubMaterialDetailId || '',
      quantity: initialValues.quantity || 1,
      extraSideDimensionId: initialValues.extraSideDimensionId || '',
      extraAreaDimensionId: initialValues.extraAreaDimensionId || '',
      subMaterialId: initialValues.subMaterialId || '',
      widthEstimate: initialValues.widthEstimate || '',
      heightEstimate: initialValues.heightEstimate || '',
    },
  });
  useEffect(() => {
    if (open) {
      reset({
        extraMasterId: initialValues.extraMasterId || '',
        blogSubMaterialDetailId: initialValues.blogSubMaterialDetailId || '',
        quantity: initialValues.quantity || 1,
        extraSideDimensionId: initialValues.extraSideDimensionId || '',
        extraAreaDimensionId: initialValues.extraAreaDimensionId || '',
        subMaterialId: initialValues.subMaterialId || '',
        widthEstimate: initialValues.widthEstimate || '',
        heightEstimate: initialValues.heightEstimate || '',
      });
    }
  }, [open]);

  const onSubmit = async (values: any) => {
    setSubmitting(true);
    handleSubmitExtra(values);
    setSubmitting(false);
  };

  const groupExtraConfigByMasterName = (list: any) => {
    return list.reduce((group: any, item: any) => {
      if (!group[item.masterName]) {
        group[item.masterName] = [];
      }
      group[item.masterName].push(item);
      return group;
    }, {});
  };

  const groupedData = !isEmpty(extraConfigList)
    ? groupExtraConfigByMasterName(extraConfigList)
    : {};

  useEffect(() => {
    if (watch('subMaterialId')) {
      const isQuantityEnabled = extraConfigList.find(
        (item: any) => item.subMaterialId === watch('subMaterialId')
      )?.isQuantity;
      const isSizeEnabled = extraConfigList.find(
        (item: any) => item.subMaterialId === watch('subMaterialId')
      )?.isSize;
      if (!isQuantityEnabled) {
        setValue('quantity', 1);
        setIsQuantity(false);
      } else {
        setIsQuantity(true);
      }
      if (!isSizeEnabled) {
        setValue('widthEstimate', '');
        setValue('heightEstimate', '');
        setIsSize(false);
      } else {
        setValue('extraAreaDimensionId', '');
        setIsSize(true);
      }
    }
  }, [watch('subMaterialId')]);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`เทคนิคพิเศษ`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form
              onSubmit={(e: any) => {
                e.stopPropagation();
                handleSubmit(onSubmit)(e);
              }}
            >
              <ModalOrderExtraStyled>
                <p>ประเภท</p>
                <FormControl fullWidth>
                  <Select
                    displayEmpty
                    {...register('subMaterialId')}
                    error={Boolean(hookFormErrors.subMaterialId)}
                    value={watch('subMaterialId')}
                    onChange={(e) => {
                      setValue('subMaterialId', e.target.value, {
                        shouldValidate: isSubmitted,
                      });
                    }}
                  >
                    <MenuItem disabled value="">
                      <div className="text-[#78909C]">กรุณาเลือก</div>
                    </MenuItem>
                    {Object.entries(groupedData).flatMap(
                      ([masterName, items]: any) => [
                        <ListSubheader key={`header-${masterName}`}>
                          {masterName}
                        </ListSubheader>,
                        ...items.map((item: any, idx: number) => (
                          <MenuItem
                            key={idx}
                            value={item.subMaterialId}
                            onClick={() => {
                              setValue('extraMasterId', item.masterId, {
                                shouldValidate: isSubmitted,
                              });
                              setValue('blogSubMaterialDetailId', '', {
                                shouldValidate: isSubmitted,
                              });
                            }}
                          >
                            {item.name}
                          </MenuItem>
                        )),
                      ]
                    )}
                  </Select>
                  {hookFormErrors.subMaterialId && (
                    <FormHelperText error>
                      {hookFormErrors.subMaterialId.message as ReactNode}
                    </FormHelperText>
                  )}
                </FormControl>
                <p>เทคนิค</p>
                <FormControl fullWidth>
                  <Select
                    displayEmpty
                    {...register('blogSubMaterialDetailId')}
                    error={Boolean(hookFormErrors.blogSubMaterialDetailId)}
                    value={watch('blogSubMaterialDetailId')}
                    onChange={(e: any) => {
                      setValue('blogSubMaterialDetailId', e.target.value, {
                        shouldValidate: isSubmitted,
                      });
                    }}
                  >
                    <MenuItem disabled value="">
                      <div className="text-[#78909C]">กรุณาเลือก</div>
                    </MenuItem>
                    {watch('subMaterialId') &&
                      extraConfigList
                        .find(
                          (item: any) =>
                            item.subMaterialId === watch('subMaterialId')
                        )
                        ?.subMaterialDetail?.map(
                          (subDetailItem: any, idx: number) => {
                            return (
                              <MenuItem key={idx} value={subDetailItem.id}>
                                {subDetailItem.name}
                              </MenuItem>
                            );
                          }
                        )}
                  </Select>
                  {hookFormErrors.blogSubMaterialDetailId && (
                    <FormHelperText error>
                      {
                        hookFormErrors.blogSubMaterialDetailId
                          .message as ReactNode
                      }
                    </FormHelperText>
                  )}
                </FormControl>
                <div className="group">
                  <div className="input-group">
                    <div>
                      <p>ด้าน</p>
                      <FormControl fullWidth>
                        <Select
                          displayEmpty
                          {...register('extraSideDimensionId')}
                          error={Boolean(hookFormErrors.extraSideDimensionId)}
                          value={watch('extraSideDimensionId') || ''}
                          onChange={(e: any) => {
                            setValue('extraSideDimensionId', e.target.value, {
                              shouldValidate: isSubmitted,
                            });
                          }}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {extraSideList?.map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {hookFormErrors.extraSideDimensionId && (
                          <FormHelperText error>
                            {
                              hookFormErrors.extraSideDimensionId
                                .message as ReactNode
                            }
                          </FormHelperText>
                        )}
                      </FormControl>
                    </div>
                    <div>
                      <p>จำนวน</p>
                      <FormControl fullWidth>
                        <TextField
                          type="number"
                          {...register('quantity')}
                          placeholder="0"
                          fullWidth
                          InputProps={{
                            endAdornment: <div className="p-[2px]">จุด</div>,
                          }}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value);
                            if (value < 1) {
                              e.target.value = '';
                            } else {
                              setValue('quantity', String(value), {
                                shouldValidate: isSubmitted,
                              });
                            }
                          }}
                          onKeyDown={(e: any) => {
                            if (e.key === '-') {
                              e.preventDefault();
                            }
                          }}
                          error={Boolean(hookFormErrors.quantity)}
                          helperText={
                            hookFormErrors.quantity
                              ? (hookFormErrors.quantity.message as ReactNode)
                              : ''
                          }
                          disabled={!isQuantity}
                        />
                      </FormControl>
                    </div>
                  </div>
                  {!isSize && (
                    <div className="image-wrap">
                      {(() => {
                        const areaImage = extraAreaList?.find(
                          (item: any) =>
                            item.id === watch('extraAreaDimensionId')
                        )?.imageUrl;
                        return (
                          <img
                            src={
                              areaImage || '/images/product/empty-product.svg'
                            }
                            className={areaImage ? 'zoom-imagez' : ''}
                          />
                        );
                      })()}
                    </div>
                  )}
                </div>
                {!isSize ? (
                  <>
                    <p>พื้นที่</p>
                    <FormControl fullWidth>
                      <Select
                        displayEmpty
                        {...register('extraAreaDimensionId')}
                        error={Boolean(hookFormErrors.extraAreaDimensionId)}
                        value={watch('extraAreaDimensionId') || ''}
                        onChange={(e: any) => {
                          setValue('extraAreaDimensionId', e.target.value, {
                            shouldValidate: isSubmitted,
                          });
                        }}
                      >
                        <MenuItem disabled value="">
                          <div className="text-[#78909C]">กรุณาเลือก</div>
                        </MenuItem>
                        {extraAreaList?.map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {hookFormErrors.extraAreaDimensionId && (
                        <FormHelperText error>
                          {
                            hookFormErrors.extraAreaDimensionId
                              .message as ReactNode
                          }
                        </FormHelperText>
                      )}
                    </FormControl>
                  </>
                ) : (
                  <div
                    className="flex"
                    style={{
                      columnGap: '24px',
                    }}
                  >
                    <div>
                      <p>ขนาดคาดการณ์</p>
                      <TextField
                        type="number"
                        fullWidth
                        placeholder="Width"
                        value={watch('widthEstimate')}
                        onChange={(e) => {
                          const inputValue = parseFloat(e.target.value);
                          setValue('widthEstimate', String(inputValue));
                        }}
                        error={Boolean(hookFormErrors.widthEstimate)}
                        helperText={
                          hookFormErrors.widthEstimate &&
                          (hookFormErrors.widthEstimate.message as ReactNode)
                        }
                        sx={{
                          '.MuiInputBase-root': { paddingLeft: '0' },
                          '.MuiInputBase-input': { paddingLeft: '8px' },
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <div className="input-adornment-label r">W</div>
                            </InputAdornment>
                          ),
                          endAdornment: 'นิ้ว',
                        }}
                      />
                    </div>
                    <div>
                      <p>ขนาดคาดการณ์</p>
                      <TextField
                        type="number"
                        fullWidth
                        placeholder="Height"
                        value={watch('heightEstimate')}
                        onChange={(e) => {
                          const inputValue = parseFloat(e.target.value);
                          setValue('heightEstimate', String(inputValue));
                        }}
                        error={Boolean(hookFormErrors.heightEstimate)}
                        helperText={
                          hookFormErrors.heightEstimate &&
                          (hookFormErrors.heightEstimate.message as ReactNode)
                        }
                        sx={{
                          '.MuiInputBase-root': { paddingLeft: '0' },
                          '.MuiInputBase-input': { paddingLeft: '8px' },
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <div className="input-adornment-label g">H</div>
                            </InputAdornment>
                          ),
                          endAdornment: 'นิ้ว',
                        }}
                      />
                    </div>
                  </div>
                )}
              </ModalOrderExtraStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};
const ModalOrderExtraStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .group {
    width: 100%;
    display: flex;
    align-items: end;
    column-gap: 24px;
    .input-group {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
    }
    .image-wrap {
      height: 133px;
      width: 133px;
      min-width: 130px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #dbe2e5;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .input-adornment-label {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
    width: 40px;
    min-width: 40px;
    border-right: 1px solid #dbe2e5;
    line-height: 1;
    &.r {
      color: #fe4902;
    }
    &.g {
      color: #008910;
    }
    &.b {
      color: #0344dc;
    }
  }
`;

export default ModalOrderExtra;
