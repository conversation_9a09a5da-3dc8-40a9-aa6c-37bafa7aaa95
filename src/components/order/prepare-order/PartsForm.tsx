import React, { useState } from 'react';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import ActionButton from '@/components/ActionButton';
import { isEmpty } from 'lodash';
import SideDetail from '@/components/SideDetail';
import { Button, IconButton } from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import ProductConfigSideSelector from '@/components/order/prepare-order/ProductConfigSideSelector';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import {
  MaterialConfigItem,
  ProductConfigCategory,
} from '@/components/order/prepare-order/PrepareOrderFormZone';
import { FadeInStyled } from '@/styles/share.styled';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const PartsFromStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  column-gap: 24px;
  background: #f5f7f8;
  min-height: 59px;
  flex-direction: column;
  row-gap: 16px;
  margin-top: 8px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  will-change: height;
  .part-item {
    width: 100%;
    display: flex;
    align-items: center;
    column-gap: 24px;
    justify-content: space-between;
    position: relative;
    &:last-child {
      &:before {
        display: none;
      }
    }

    &:before {
      content: '';
      bottom: -8px;
      left: -8px;
      height: 1px;
      width: calc(100% + 16px);
      position: absolute;
      background: #dbe2e5;
      transform: translateY(50%);
    }
    &.hide-before {
      &:before {
        display: none !important;
      }
    }
    .info-group {
      display: flex;
      align-items: center;
      column-gap: 14px;
      max-width: 100%;
      overflow: hidden;
      .image {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 8px;
        object-fit: cover;
      }
      .text-group {
        display: flex;
        flex-direction: column;
        row-gap: 2px;
        overflow: hidden;
        .name {
          font-weight: 600;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .type {
          font-size: 12px;
          color: #90a4ae;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .action-group {
      display: flex;
      align-items: center;
    }
  }
`;

type Props = {
  hookForm: any;
  partsConfig: ProductConfigCategory;
};

const PartsForm = ({ hookForm, partsConfig }: Props) => {
  const dispatch = useAppDispatch();
  const { register, setValue, hookFormErrors, watch } = hookForm;
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [openSideSelector, setOpenSideSelector] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);
  const [subMaterialName, setSubMaterialName] = useState<string>('');
  const handleClickAddMaterial = () => {
    setOpenSideSelector(true);
  };

  const getSubMaterialName = (subMaterialId: number) => {
    const subMaterialName = partsConfig.productConfig?.find(
      (item: MaterialConfigItem) => item.subMaterialId === subMaterialId
    )?.subMaterialName;
    if (subMaterialName) {
      return subMaterialName;
    }
  };

  const handleRemove = (parts: any) => {
    const filteredParts = estimateDataSalesOrder.productParts.filter(
      (part: any) => part.subMaterialDetail.id !== parts.subMaterialDetail.id
    );
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        productParts: filteredParts,
      })
    );
    const currentParts = watch('productParts');
    const filteredFormParts = currentParts.filter(
      (part: any) => part.subMaterialDetailId !== parts.subMaterialDetail.id
    );
    setValue('productParts', filteredFormParts);
  };

  return (
    <>
      <SideDetail
        isOpen={openSideSelector}
        handleClickOutSide={() => {
          setOpenSideSelector(false);
        }}
      >
        <>
          <div className="header">
            {step === 2 ? (
              <FadeInStyled
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  columnGap: '16px',
                }}
              >
                <div
                  className="back"
                  onClick={() => {
                    setStep(step - 1);
                  }}
                >
                  <IconButton
                    sx={{
                      color: '#263238',
                    }}
                  >
                    <KeyboardBackspaceRoundedIcon />
                  </IconButton>
                </div>
                <div className="topic">{subMaterialName}</div>
              </FadeInStyled>
            ) : (
              <div>
                <FadeInStyled>
                  <div className="topic">Parts</div>
                </FadeInStyled>
              </div>
            )}
            <div
              className="x-close"
              onClick={() => {
                setOpenSideSelector(false);
              }}
            >
              <IconButton>
                <CloseIcon />
              </IconButton>
            </div>
          </div>
          <div className="content">
            <div className="body">
              <ProductConfigSideSelector
                productConfig={partsConfig}
                hookForm={{ register, setValue, hookFormErrors, watch }}
                isOpen={openSideSelector}
                step={step}
                setStep={setStep}
                setSubMaterialName={setSubMaterialName}
                setOpenSideSelector={setOpenSideSelector}
                type={'parts'}
              />
            </div>
          </div>
        </>
      </SideDetail>
      <FadeInStyled className="flex items-center justify-between">
        <span className="topic">ส่วนประกอบ</span>
        <div
          onClick={() => {
            if (!openSideSelector) handleClickAddMaterial();
          }}
        >
          <ActionButton
            variant="outlined"
            color="blueGrey"
            icon={<AddRoundedIcon />}
            text="เพิ่มรายการ"
            borderRadius="8px"
          />
        </div>
      </FadeInStyled>
      <PartsFromStyled
        as={motion.div}
        // layout
        style={{
          borderRadius: '16px',
        }}
      >
        {/* <LayoutGroup> */}
        {/* <AnimatePresence mode={'popLayout'} initial={false}> */}
        {!isEmpty(estimateDataSalesOrder.productParts) ? (
          estimateDataSalesOrder.productParts.map((parts: any) => (
            <motion.div
              key={parts.subMaterialDetail.id}
              // layout
              /* {...motionListConfig} */
              // className={partItemClass}
              className={'part-item'}
            >
              <div className="info-group">
                <Image
                  src={parts.subMaterialDetail.subMaterialImageUrl}
                  alt={parts.subMaterialDetail.name}
                  width={100}
                  height={100}
                  className="image"
                  style={{
                    border: '1px solid #DBE2E5',
                  }}
                />
                <div className="text-group">
                  <div className="name">{parts.subMaterialDetail.name}</div>
                  <div className="type">
                    {getSubMaterialName(parts.subMaterialDetail.subMaterialId)}
                  </div>
                </div>
              </div>
              <div className="action-group">
                <Button
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    width: '40px',
                    minWidth: '40px',
                    height: '40px',
                    minHeight: '40px',
                  }}
                  onClick={() => handleRemove(parts)}
                >
                  <img
                    src="/icons/delete-black.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                </Button>
              </div>
            </motion.div>
          ))
        ) : (
          <motion.div
            // layout
            /* {...motionFadeConfig} */
            className="h-[40px] w-full flex justify-center items-center text-[#dbe2e5]"
          >
            ไม่มีรายการ
          </motion.div>
        )}
        {/* </AnimatePresence> */}
        {/* </LayoutGroup> */}
      </PartsFromStyled>
    </>
  );
};

export default PartsForm;
