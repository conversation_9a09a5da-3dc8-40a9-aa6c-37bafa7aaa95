import React from 'react';
import styled from 'styled-components';
import { TextField } from '@mui/material';

const RemarkFormStyled = styled.div`
  width: 100%;
  margin-top: 8px;
`;

type Props = {
  hookForm: any;
};

const RemarkForm = ({ hookForm }: Props) => {
  // const dispatch = useAppDispatch();
  const { register } = hookForm;
  return (
    <>
      <p className="topic">หมายเหตุ</p>
      <RemarkFormStyled>
        <TextField
          {...register('note')}
          multiline
          rows={4}
          placeholder="ข้อมูลเพิ่มเติม"
        />
      </RemarkFormStyled>
    </>
  );
};

export default RemarkForm;
