import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ProductSizeFrom from '@/components/order/prepare-order/ProductSizeFrom';
import MaterialFrom from '@/components/order/prepare-order/MaterialFrom';
import apiMaster from '@/services/stock/master';
import PartsForm from '@/components/order/prepare-order/PartsForm';
import PrintSideForm from '@/components/order/prepare-order/PrintSideForm';
import { FadeInStyled } from '@/styles/share.styled';
import CoatingForm from '@/components/order/prepare-order/CoatingForm';
import ExtraForm from '@/components/order/prepare-order/ExtraForm';
import DesignForm from '@/components/order/prepare-order/DesignForm';
import ProductExampleForm from '@/components/order/prepare-order/ProductExampleForm';
import ServiceForm from '@/components/order/prepare-order/ServiceForm';
import RemarkForm from '@/components/order/prepare-order/RemarkForm';
import { Button, CircularProgress } from '@mui/material';
import { setSnackBar } from '@/store/features/alert';
import { useRouter } from 'next/router';
import { salesOrderSelector } from '@/store/features/estimate';
import DetailLevelForm from '@/components/order/prepare-order/DetailLevelForm';
import ImageExampleForm from '@/components/order/prepare-order/ImageExampleForm';
import apiEstimateProduct from '@/services/order/estimate-product';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const PrepareOrderFormZoneStyled = styled.div`
  width: 50%;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
  overflow: auto;

  .topic {
    all: unset;
    font-size: 16px;
    font-weight: 600;
  }
  .label {
    margin: 24px 0 8px 0;
  }
`;

export type MaterialConfigItem = {
  id: number;
  productId: number;
  masterId: number;
  subMaterialId: number;
  imageUrl: string | null;
  subMaterialName: string;
  materialId: number;
  materialName: string;
  count: number;
};

export type ProductConfigCategory = {
  id?: number;
  name?: string;
  sort?: number;
  productConfig?: MaterialConfigItem[];
};

export type ProductConfigurations = {
  material: ProductConfigCategory;
  parts: ProductConfigCategory;
  coating: ProductConfigCategory;
  extras: ProductConfigCategory;
};

type Props = {
  finishList: any[];
  productInfo: any;
  displayUnit: string;
};
const PrepareOrderFormZone = ({
  finishList,
  productInfo,
  displayUnit,
}: Props) => {
  const router = useRouter();
  const { salesOrderId } = router.query;
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const dispatch = useAppDispatch();
  const [productConfigurations, setProductConfigurations] = useState({
    material: {},
    parts: {},
    coating: {},
    extras: {},
  });
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const minWidthMM = productInfo.productSize.minWidth;
  const maxWidthMM = productInfo.productSize.maxWidth;
  const minHeightMM = productInfo.productSize.minHeight;
  const maxHeightMM = productInfo.productSize.maxHeight;
  const minLengthMM = productInfo.productSize.minLength;
  const maxLengthMM = productInfo.productSize.maxLength;

  const unitFactor =
    displayUnit === 'cm' ? 10 : displayUnit === 'in' ? 25.4 : 1;
  const minWidth = +(minWidthMM / unitFactor).toFixed(2);
  const maxWidth = +(maxWidthMM / unitFactor).toFixed(2);
  const minHeight = +(minHeightMM / unitFactor).toFixed(2);
  const maxHeight = +(maxHeightMM / unitFactor).toFixed(2);
  const minLength = +(minLengthMM / unitFactor).toFixed(2);
  const maxLength = +(maxLengthMM / unitFactor).toFixed(2);

  const validationSchema = yup.object().shape({
    id: yup.number().nullable(),

    width: yup
      .number()
      .required('กรุณากรอก Width')
      .min(minWidthMM, `Width ต้องมีค่าไม่ต่ำกว่า ${minWidth} ${displayUnit}`)
      .max(maxWidthMM, `Width ต้องมีค่าไม่เกิน ${maxWidth} ${displayUnit}`)
      .typeError('กรุณากรอก Width')
      .test(
        'width-length-validation',
        'Width ต้องไม่มากกว่า Length',
        function (value) {
          const { length } = this.parent;
          return !value || !length || value <= length;
        }
      ),

    height: yup
      .number()
      .required('กรุณากรอก Height')
      .min(
        minHeightMM,
        `Height ต้องมีค่าไม่ต่ำกว่า ${minHeight} ${displayUnit}`
      )
      .max(maxHeightMM, `Height ต้องมีค่าไม่เกิน ${maxHeight} ${displayUnit}`)
      .typeError('กรุณากรอก Height'),

    length: yup
      .number()
      .required('กรุณากรอก Length')
      .min(
        minLengthMM,
        `Length ต้องมีค่าไม่ต่ำกว่า ${minLength} ${displayUnit}`
      )
      .max(maxLengthMM, `Length ต้องมีค่าไม่เกิน ${maxLength} ${displayUnit}`)
      .typeError('กรุณากรอก Length'),
    detailLevel: yup
      .number()
      .required('กรุณาเลือกความละเอียด')
      .typeError('กรุณาเลือกความละเอียด'),
    scheduleDate: yup
      .string()
      .required('กรุณาระบุกำหนดส่งสินค้า')
      .typeError('กรุณาระบุกำหนดส่งสินค้า'),
    estimateQuantity: yup.array().of(
      yup.object().shape({
        quantity: yup
          .number()
          .required('กรุณากรอกจำนวน')
          .typeError('กรุณากรอกจำนวน'),
      })
    ),
    materialMasterId: yup
      .number()
      .required('กรุณาเลือกวัสดุ')
      .typeError('กรุณาเลือกวัสดุ'),
    subMaterialDetailId: yup.number().nullable(),
    productParts: yup.array().of(
      yup.object().shape({
        partMasterId: yup
          .number()
          .required('กรุณาเลือก Part')
          .typeError('กรุณาเลือก Part'),
        value: yup.number().required('กรุณากรอกค่า').typeError('กรุณากรอกค่า'),
        subMaterialDetailId: yup
          .number()
          .required('กรุณาเลือก SubMaterial')
          .typeError('กรุณาเลือก SubMaterial'),
      })
    ),
    printingRequest: yup.object().shape({
      // ต้องเลือกด้านพิมพ์เสมอ
      printingSideDimensionId: yup
        .number()
        .required('กรุณาเลือกด้านพิมพ์')
        .typeError('กรุณาเลือกด้านพิมพ์'),

      // ถ้าไม่ใช่ 20 ต้องเลือกระบบพิมพ์
      printSystemId: yup
        .number()
        .transform((value, originalValue) =>
          originalValue === '' ? null : value
        )
        .when('printingSideDimensionId', {
          is: (val: number) => val !== 20,
          then: (schema) =>
            schema
              .required('กรุณาเลือกระบบพิมพ์')
              .typeError('กรุณาเลือกระบบพิมพ์'),
          otherwise: (schema) => schema.optional().nullable(),
        }),

      // ถ้าเลือกพิมพ์หน้า (21) หรือ หน้า-หลัง (23) colorFront ต้องมี
      colorFront: yup
        .array()
        .of(
          yup.object().shape({
            printColorId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกสีด้านหน้า'),
            // colorCode: yup
            //   .string()
            //   .nullable()
            //   .typeError('กรุณาระบุรหัสสีด้านหน้า'),
            printAreaDimensionId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกพื้นที่ด้านหน้า'),
          })
        )
        .when('printingSideDimensionId', {
          is: (val: number) => val === 21 || val === 23,
          then: (schema) => schema.min(1, 'กรุณาเพิ่มข้อมูลสีด้านหน้า'),
          otherwise: (schema) => schema.optional(),
        }),

      // ถ้าเลือกพิมพ์หลัง (22) หรือ หน้า-หลัง (23) colorBack ต้องมี
      colorBack: yup
        .array()
        .of(
          yup.object().shape({
            printColorId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกสีด้านหลัง'),
            // colorCode: yup
            //   .string()
            //   .nullable()
            //   .typeError('กรุณาระบุรหัสสีด้านหน้า'),
            printAreaDimensionId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกพื้นที่ด้านหลัง'),
          })
        )
        .when('printingSideDimensionId', {
          is: (val: number) => val === 22 || val === 23,
          then: (schema) => schema.min(1, 'กรุณาเพิ่มข้อมูลสีด้านหลัง'),
          otherwise: (schema) => schema.optional(),
        }),
    }),

    coatingRequest: yup.object().shape({
      coatingSideDimensionId: yup
        .number()
        .required('กรุณาเลือกด้านเคลือบ')
        .typeError('กรุณาเลือกด้านเคลือบ'),

      finishFront: yup
        .array()
        .of(
          yup.object().shape({
            coatingMasterId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกเคลือบด้านหน้า'),
            finishSubMaterialDetailId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกน้ำยาด้านหน้า'),
          })
        )
        .when('coatingSideDimensionId', {
          is: (val: number) => val === 32 || val === 34, // ต้องมีเมื่อเคลือบหน้า
          then: (schema) => schema.min(1, 'กรุณาเพิ่มข้อมูล'),
          otherwise: (schema) => schema.optional(),
        }),

      finishBack: yup
        .array()
        .of(
          yup.object().shape({
            coatingMasterId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกเคลือบด้านหลัง'),
            finishSubMaterialDetailId: yup
              .number()
              .nullable()
              .typeError('กรุณาเลือกน้ำยาด้านหลัง'),
          })
        )
        .when('coatingSideDimensionId', {
          is: (val: number) => val === 33 || val === 34, // ต้องมีเมื่อเคลือบหลัง
          then: (schema) => schema.min(1, 'กรุณาเพิ่มข้อมูล'),
          otherwise: (schema) => schema.optional(),
        }),
    }),

    productDesignId: yup
      .number()
      .required('กรุณาเลือก Design')
      .typeError('กรุณาเลือก Design'),
    serviceLay: yup.array().of(
      yup.object().shape({
        serviceLayId: yup.number().nullable().typeError('กรุณาเลือก'),
        serviceLayTypeId: yup.number().nullable().typeError('กรุณาเลือก'),
      })
    ),
    linkExtra: yup
      .string()
      .nullable()
      .when(['productDesignId', '$fileExtraUrl'], {
        is: (productDesignId: number, fileExtraUrl: string | null) =>
          productDesignId === 2 && !fileExtraUrl,
        then: (schema) =>
          schema
            .required('กรุณากรอกลิงก์หรืออัปโหลดไฟล์')
            .test(
              'is-valid-url',
              'กรุณากรอกลิงก์ให้ถูกต้อง (เช่น https://...)',
              (value) => {
                try {
                  if (!value) return false;
                  // eslint-disable-next-line no-new
                  new URL(value);
                  return true;
                } catch {
                  return false;
                }
              }
            ),
        otherwise: (schema) => schema.notRequired().nullable(),
      }),
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors: hookFormErrors, isSubmitted },
  } = useForm<any>({
    resolver: yupResolver(validationSchema),
    context: {
      fileExtraUrl: estimateDataSalesOrder.fileExtraUrl,
    },
    defaultValues: {
      id: estimateDataSalesOrder.id || null,
      width: estimateDataSalesOrder.width || '',
      height: estimateDataSalesOrder.height || '',
      length: estimateDataSalesOrder.length || '',
      // mapping estimateQuantity โดยดึงเฉพาะ key quantity
      estimateQuantity:
        estimateDataSalesOrder.estimateQuantity.map((q: any) => ({
          quantity: q.quantity,
        })) || [],
      detailLevel: estimateDataSalesOrder.detailLevel || '',
      scheduleDate: estimateDataSalesOrder.scheduleDate
        ? dayjs(estimateDataSalesOrder.scheduleDate).format('YYYY-MM-DD')
        : '',
      // กำหนด materialMasterId จาก estimateDataSalesOrder
      materialMasterId: estimateDataSalesOrder.materialMasterId || null,
      // mapping subMaterialDetailId จาก estimateDataSalesOrder.subMaterialDetail.id
      subMaterialDetailId: estimateDataSalesOrder.subMaterialDetail?.id || null,
      // mapping productParts โดยดึง subMaterialDetail.id มาเป็น subMaterialDetailId
      productParts: estimateDataSalesOrder.productParts.map((part: any) => {
        const value = {
          ...part,
          subMaterialDetailId: part.subMaterialDetail.id,
        };
        delete value.subMaterialDetail;
        return value;
      }),
      // mapping printingRequest
      printingRequest: {
        // ดึง printingSideDimensionId จาก estimateDataSalesOrder.printingRequest.printingSideDimension
        printingSideDimensionId:
          estimateDataSalesOrder.printingRequest?.printingSideDimension?.id ||
          20,
        // ดึง printSystemId จาก estimateDataSalesOrder.printingRequest.printSystem (ถ้ามี)
        printSystemId: estimateDataSalesOrder.printingRequest?.printSystem
          ? estimateDataSalesOrder.printingRequest.printSystem.id
          : '',
        // mapping colorFront ให้เป็น array ของ object ตาม key ที่ต้องการ
        colorFront:
          estimateDataSalesOrder.printingRequest?.colorFront?.map(
            (item: any) => ({
              printColorId: item.printColor.id,
              colorCode: item.colorCode,
              printAreaDimensionId: item.printAreaDimension.id,
            })
          ) || [],
        // mapping colorBack
        colorBack:
          estimateDataSalesOrder.printingRequest?.colorBack?.map(
            (item: any) => ({
              printColorId: item.printColor.id,
              colorCode: item.colorCode,
              printAreaDimensionId: item.printAreaDimension.id,
            })
          ) || [],
      },
      // mapping coatingRequest
      coatingRequest: {
        // ดึง coatingSideDimensionId จาก estimateDataSalesOrder.coatingRequest.coatingSideDimension
        coatingSideDimensionId:
          estimateDataSalesOrder.coatingRequest?.coatingSideDimension?.id || 31,
        // mapping finishFront ให้เป็น array ของ object ตาม key ที่ต้องการ
        finishFront:
          estimateDataSalesOrder.coatingRequest?.finishFront.map(
            (item: any) => ({
              coatingPositionEnum:
                item.coatingPositionEnum === 'BEFORE'
                  ? 'ก่อนพิมพ์'
                  : 'หลังพิมพ์',
              coatingMasterId: item.coatingMasterId,
              finishSubMaterialDetailId: item.finishSubMaterialDetail.id,
              side: item.side || '',
            })
          ) || [],
        // mapping finishBack
        finishBack:
          estimateDataSalesOrder.coatingRequest?.finishBack.map(
            (item: any) => ({
              coatingPositionEnum:
                item.coatingPositionEnum === 'BEFORE'
                  ? 'ก่อนพิมพ์'
                  : 'หลังพิมพ์',
              coatingMasterId: item.coatingMasterId,
              finishSubMaterialDetailId: item.finishSubMaterialDetail.id,
              side: item.side || '',
            })
          ) || [],
      },
      // mapping extra
      extra:
        estimateDataSalesOrder.extra.map((ex: any) => ({
          extraMasterId: ex.extraMasterId,
          blogSubMaterialDetailId: ex.blogSubMaterialDetail.id,
          quantity: ex.quantity.toString(),
          extraSideDimensionId: ex.extraSideDimension.id,
          extraAreaDimensionId: ex.extraAreaDimension?.id || null,
          subMaterialId: ex.blogSubMaterialDetail.subMaterialId,
          widthEstimate: ex.widthEstimate,
          heightEstimate: ex.heightEstimate,
        })) || [],
      // กำหนด productDesignId จาก estimateDataSalesOrder.productDesign.productDesignId
      productDesignId:
        estimateDataSalesOrder.productDesign?.productDesignId || 1,
      // mapping masterExample โดยดึงเป็น array ของ id
      masterExample:
        estimateDataSalesOrder.masterExample.map(
          (item: any) => item.masterExampleId
        ) || [],
      // mapping serviceLay โดยแปลงข้อมูลให้มี key serviceTypeId กับ serviceId ตามที่ต้องการ
      serviceLay:
        estimateDataSalesOrder.serviceLay.map((item: any) => ({
          // serviceLayTypeId มาจาก estimateDataSalesOrder.serviceLay[].serviceLayType.id
          serviceLayTypeId: item.serviceLayType.id,
          // serviceLayId มาจาก estimateDataSalesOrder.serviceLay[].serviceLay.id
          serviceLayId: item.serviceLay.id,
        })) || [],
      // กำหนด note จาก estimateDataSalesOrder
      note: estimateDataSalesOrder.note || '',
      linkExtra: estimateDataSalesOrder.linkExtra || '',
    },
  });
  const getProductConfigurations = async (productId: number) => {
    const res = await apiMaster.getProductConfig(productId);
    if (!res.isError) {
      const materialConfig = res.data.find((item: any) => item.id === 1);
      const partsConfig = res.data.find((item: any) => item.id === 2);
      const coatingConfig = res.data.find((item: any) => item.id === 3);
      const extrasConfig = res.data.find((item: any) => item.id === 4);
      setProductConfigurations({
        material: materialConfig,
        parts: partsConfig,
        coating: coatingConfig,
        extras: extrasConfig,
      });
      setLoading(false);
    }
  };

  useEffect(() => {
    getProductConfigurations(
      estimateDataSalesOrder.productModel.productId
    ).then();
  }, []);

  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const isDDMMYYYY = /^\d{2}\/\d{2}\/\d{4}$/.test(values.scheduleDate);
    const parsedScheduleDate = isDDMMYYYY
      ? dayjs(values.scheduleDate, 'DD/MM/YYYY')
      : dayjs(values.scheduleDate);
    const body = {
      ...values,
      id: estimateDataSalesOrder.id,
      scheduleDate: parsedScheduleDate.format('YYYY-MM-DD'),
    };
    const res = await apiEstimateProduct.updateEstimateProduct(body);
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.message.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      await router.push(`/sales-order/${salesOrderId}/spec?step=สเปคสินค้า`);
    }
    setIsSubmitting(false);
  };
  // console.log('watch', watch());
  // console.log('hookFormErrors', hookFormErrors);

  return (
    <PrepareOrderFormZoneStyled>
      <form onSubmit={handleSubmit(onSubmit)}>
        <ProductSizeFrom
          hookForm={{
            register,
            setValue,
            hookFormErrors,
            watch,
            control,
            isSubmitted,
          }}
          productInfo={productInfo}
          displayUnit={displayUnit}
        />
        <div className="mt-[24px]">
          <DetailLevelForm
            hookForm={{
              register,
              setValue,
              hookFormErrors,
              watch,
              isSubmitted,
            }}
          />
        </div>
        {/* <LayoutGroup> */}
        {!loading && (
          <FadeInStyled>
            <div className="mt-[24px]">
              <MaterialFrom
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
                materialConfig={productConfigurations.material}
              />
            </div>
            <div className="mt-[24px]">
              <PartsForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
                partsConfig={productConfigurations.parts}
              />
            </div>
            <div className="mt-[24px]">
              <PrintSideForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
              />
            </div>
            <div className="mt-[24px]">
              <CoatingForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  control,
                  isSubmitted,
                }}
                coatingConfig={productConfigurations.coating}
                finishList={finishList}
              />
            </div>
            <div className="mt-[24px]">
              <ExtraForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
              />
            </div>
            <div className="mt-[24px]">
              <DesignForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                  control,
                }}
              />
            </div>
            <div className="mt-[24px]">
              <ProductExampleForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
              />
            </div>
            <div className="mt-[24px]">
              <ServiceForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
              />
            </div>
            <div className="mt-[24px]">
              <ImageExampleForm />
            </div>
            <div className="mt-[24px]">
              <RemarkForm
                hookForm={{
                  register,
                  setValue,
                  hookFormErrors,
                  watch,
                  isSubmitted,
                }}
              />
            </div>
            <div className="mt-[40px]">
              <Button type="submit" variant="contained" color="dark" fullWidth>
                {isSubmitting ? (
                  <CircularProgress
                    size={20}
                    style={{
                      color: 'white',
                    }}
                  />
                ) : (
                  'บันทึกสเปค'
                )}
              </Button>
            </div>
          </FadeInStyled>
        )}
        {/* </LayoutGroup> */}
      </form>
    </PrepareOrderFormZoneStyled>
  );
};
// @ts-ignore
export default PrepareOrderFormZone;
