import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Select,
} from '@mui/material';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import apiServiceLay from '@/services/stock/service-lay';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const ServiceFormStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  row-gap: 16px;

  .list {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border: 1px solid #dbe2e5;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: white;
    padding: 8px 0;
    &:before {
      content: '';
      height: 100%;
      width: 1px;
      position: absolute;
      background: #dbe2e5;
      left: calc(50%);
      transform: translateX(-50%);
    }
    .check-box-zone {
      left: 8px;
      position: relative;
    }
    .check-box-zone,
    .selector-zone {
      display: flex;
      align-items: center;
      flex: 1 1 0%;
      .MuiInputBase-root {
        box-shadow: none !important;
      }
    }
  }
`;

type Props = {
  hookForm: any;
};

const ServiceForm = ({ hookForm }: Props) => {
  const { setValue, hookFormErrors, watch, isSubmitted } = hookForm;
  const [serviceList, setServiceList] = useState<any[]>([]);
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);

  const getService = async () => {
    const res = await apiServiceLay.getServiceLayList({ type: 2 });
    if (res && !res.isError) {
      setServiceList(res.data);
    }
  };

  useEffect(() => {
    getService().then();
  }, []);

  // ฟังก์ชันจัดการเลือก/ยกเลิกเลือกบริการผ่าน checkbox
  // โดย checkbox จะเซ็ตค่า serviceLayTypeId (ซึ่งต้องมีค่า "ใหญ่กว่า" select)
  const handleChangeCheckbox = (service: any) => {
    // ตรวจสอบว่ามี service นี้ใน form state หรือยัง โดยใช้ key serviceLayTypeId
    const isSelected = watch('serviceLay').some(
      (item: any) => item.serviceLayTypeId === service.id
    );
    if (isSelected) {
      // ถ้าเลือกอยู่แล้วให้ลบออกจาก form state
      const filteredServices = watch('serviceLay').filter(
        (item: any) => item.serviceLayTypeId !== service.id
      );
      setValue('serviceLay', filteredServices);
      // อัพเดต redux state โดยลบรายการที่มี serviceLayType ตรงกับ service ที่ถูกยกเลิก
      const updatedLayDataOrder = estimateDataSalesOrder.serviceLay.filter(
        (entry: any) => entry.serviceLayType.id !== service.id
      );
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          serviceLay: updatedLayDataOrder,
        })
      );
    } else {
      // ถ้ายังไม่ได้เลือก ให้เพิ่ม object ใหม่ลงใน form state
      // โดย checkbox จะเซ็ตค่า serviceLayTypeId เป็น id ของ service
      // ส่วน select ยังไม่ได้เลือก จึงให้ serviceLayId เป็นค่าว่าง
      setValue('serviceLay', [
        ...watch('serviceLay'),
        {
          serviceLayTypeId: service.id, // ค่า checkbox
          serviceLayId: '', // ค่า select ยังไม่ได้เลือก
        },
      ]);
      // อัพเดต redux state เพิ่ม entry ใหม่ โดยเก็บข้อมูลใน key serviceLayType
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          serviceLay: [
            ...estimateDataSalesOrder.serviceLay,
            {
              serviceLayType: {
                id: service.id,
                name: service.name,
              },
              serviceLay: {}, // ยังไม่มีข้อมูลจาก select
            },
          ],
        })
      );
    }
  };
  // console.log('serviceList', serviceList);
  return (
    <>
      {!isEmpty(serviceList) && (
        <>
          <p className="topic">บริการ</p>
          <ServiceFormStyled>
            {serviceList.map((item: any) => {
              return (
                <>
                  <div className="list" key={item.id}>
                    {/* ส่วน checkbox สำหรับเลือก serviceLayType */}
                    <div className="check-box-zone">
                      <FormControlLabel
                        control={
                          <Checkbox
                            color="primary"
                            // ตรวจสอบโดยใช้ serviceLayTypeId เทียบกับ id ของ service
                            checked={watch('serviceLay').some(
                              (serviceItem: any) =>
                                serviceItem.serviceLayTypeId === item.id
                            )}
                            onChange={(_event: any) => {
                              handleChangeCheckbox(item);
                            }}
                            icon={<IconUnCheckbox />}
                            checkedIcon={<IconCheckboxBlack />}
                          />
                        }
                        label={`${item.name}`}
                      />
                    </div>
                    {/* ส่วน select สำหรับเลือก serviceLayId จาก dropdown */}
                    <div className="selector-zone">
                      {(() => {
                        // หา index ของ service ที่เลือก (เช็คจาก serviceLayTypeId ที่ถูกเซ็ตใน checkbox)
                        const valueIndex = watch('serviceLay').findIndex(
                          (serviceItem: any) =>
                            serviceItem.serviceLayTypeId === item.id
                        );
                        return (
                          <FormControl fullWidth>
                            <Select
                              displayEmpty
                              error={
                                valueIndex !== -1
                                  ? Boolean(
                                      hookFormErrors?.serviceLay?.[valueIndex]
                                        ?.serviceLayId
                                    )
                                  : false
                              }
                              // กำหนด value จาก serviceLayId ใน form state
                              value={
                                valueIndex !== -1
                                  ? watch('serviceLay')?.[valueIndex]
                                      ?.serviceLayId || ''
                                  : ''
                              }
                              onChange={(e: any) => {
                                if (valueIndex !== -1) {
                                  // ค่า id ที่เลือกจาก dropdown (select) จะเก็บใน serviceLayId
                                  const selectedId = e.target.value;
                                  // อัพเดต form state โดยเก็บค่าเดิมของ serviceLayTypeId และ update serviceLayId
                                  setValue(
                                    `serviceLay.${valueIndex}`,
                                    {
                                      serviceLayTypeId:
                                        watch('serviceLay')[valueIndex]
                                          .serviceLayTypeId,
                                      serviceLayId: selectedId,
                                    },
                                    { shouldValidate: !!isSubmitted }
                                  );
                                  // หา object ในตัวเลือก dropdown ที่มี id ตรงกับ selectedId
                                  const selectedOption = item.serviceLay.find(
                                    (option: any) => option.id === selectedId
                                  );
                                  // อัพเดต redux state โดยหา entry ที่มี serviceLayType.id ตรงกับ service ที่เลือก (จาก checkbox)
                                  dispatch(
                                    setEstimateDataSalesOrder({
                                      ...estimateDataSalesOrder,
                                      serviceLay:
                                        estimateDataSalesOrder.serviceLay.map(
                                          (entry: any) => {
                                            if (
                                              entry.serviceLayType.id ===
                                              item.id
                                            ) {
                                              return {
                                                serviceLayType:
                                                  entry.serviceLayType, // จาก checkbox
                                                serviceLay: {
                                                  id: selectedId,
                                                  name: selectedOption
                                                    ? selectedOption.name
                                                    : '',
                                                },
                                              };
                                            }
                                            return entry;
                                          }
                                        ),
                                    })
                                  );
                                }
                              }}
                            >
                              <MenuItem disabled value="">
                                <div className="text-[#78909C]">กรุณาเลือก</div>
                              </MenuItem>
                              {/* แสดงตัวเลือกจาก item.serviceLay สำหรับ select */}
                              {item.serviceLay?.map((option: any) => {
                                return (
                                  <MenuItem value={option.id} key={option.id}>
                                    {option.name}
                                  </MenuItem>
                                );
                              })}
                            </Select>
                          </FormControl>
                        );
                      })()}
                    </div>
                  </div>
                  {(() => {
                    const valueIndex = watch('serviceLay').findIndex(
                      (serviceItem: any) =>
                        serviceItem.serviceLayTypeId === item.id
                    );

                    const hasError =
                      valueIndex !== -1 &&
                      hookFormErrors?.serviceLay?.[valueIndex]?.serviceLayId;

                    return hasError ? (
                      <div
                        style={{
                          marginTop: '-16px',
                          display: 'flex',
                          width: '100%',
                          justifyContent: 'end',
                        }}
                      >
                        <FormHelperText error>กรุณาเลือก</FormHelperText>
                      </div>
                    ) : null;
                  })()}
                </>
              );
            })}
          </ServiceFormStyled>
        </>
      )}
    </>
  );
};

export default ServiceForm;
