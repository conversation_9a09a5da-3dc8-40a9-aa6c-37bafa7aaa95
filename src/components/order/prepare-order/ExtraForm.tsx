import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '@/store';
import ActionButton from '@/components/ActionButton';
import { isEmpty } from 'lodash';
import { Button } from '@mui/material';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { FadeInStyled } from '@/styles/share.styled';
import { motion } from 'framer-motion';
import Image from 'next/image';
import ModalOrderExtra from '@/components/order/prepare-order/ModalOrderExtra';
import apiProductConfig from '@/services/stock/product-config';
import apiDimensions from '@/services/stock/dimensions';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import { numberWithCommas } from '@/utils/number';

const ExtraFromStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  column-gap: 24px;
  background: #f5f7f8;
  min-height: 59px;
  flex-direction: column;
  row-gap: 16px;
  margin-top: 8px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  will-change: height;
  .part-item {
    width: 100%;
    display: flex;
    align-items: center;
    column-gap: 24px;
    justify-content: space-between;
    position: relative;

    &:last-child {
      &:before {
        display: none;
      }
    }

    &:before {
      content: '';
      bottom: -8px;
      left: -8px;
      height: 1px;
      width: calc(100% + 16px);
      position: absolute;
      background: #dbe2e5;
      transform: translateY(50%);
    }
    &.hide-before {
      &:before {
        display: none !important;
      }
    }
    .info-group {
      display: flex;
      align-items: center;
      column-gap: 14px;
      max-width: 100%;
      overflow: hidden;
      .image {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 8px;
        object-fit: cover;
      }
      .text-group {
        display: flex;
        flex-direction: column;
        row-gap: 2px;
        overflow: hidden;
        .name {
          font-weight: 600;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .type {
          font-size: 12px;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .action-group {
      display: flex;
      align-items: center;
      column-gap: 8px;
    }
  }
`;

type Props = {
  hookForm: any;
};

const ExtraForm = ({ hookForm }: Props) => {
  const dispatch = useAppDispatch();
  const { setValue, watch } = hookForm;
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [open, setOpen] = useState<boolean>(false);
  const [extraSideList, setExtraSideList] = useState<any[]>([]);
  const [extraAreaList, setExtraAreaList] = useState<any[]>([]);
  const [extraConfigList, setExtraConfigList] = useState<any>([]);
  const [initialValue, setInitialValue] = useState<any>({});
  const [indexEditExtra, setIndexEditExtra] = useState<number | null>(null);

  const getProductExtraConfig = async () => {
    const res = await apiProductConfig.getProductConfigOrder({
      productId: estimateDataSalesOrder.productModel.productId,
      masterCategoryId: 4, // 3 = เทคนิคพิเศษ
    });
    if (!res.isError) {
      setExtraConfigList(res.data);
    }
  };

  const getExtraSideList = async () => {
    const res = await apiDimensions.getListDimensionByDimensionTypeId(11); // 11 = ด้านเทคนิคพิเศษ
    if (!res.isError) {
      setExtraSideList(res.data);
    }
  };
  const getExtraAreaList = async () => {
    const res = await apiDimensions.getListDimensionByDimensionTypeId(9); // 9 = ขนาดพื้นที่ปั้ม
    if (!res.isError) {
      setExtraAreaList(res.data);
    }
  };
  useEffect(() => {
    getProductExtraConfig().then();
    getExtraSideList().then();
    getExtraAreaList().then();
  }, []);

  const handleClickAddExtra = () => {
    setInitialValue({});
    setOpen(true);
  };

  useEffect(() => {
    //
  }, [open]);

  function transformExtra(
    watchObj: any,
    extraSideList: any,
    extraAreaList: any,
    extraConfigList: any
  ) {
    // watchObj ควรมีโครงสร้าง { extra: [...] }
    return watchObj.extra.map((item: any) => {
      // หา extraSideDimension จาก extraSideList โดยจับคู่ id กับ extraSideDimensionId ของ item
      const extraSideDimension = extraSideList.find(
        (side: any) => side.id === item.extraSideDimensionId
      );

      // หา extraAreaDimension จาก extraAreaList โดยจับคู่ id กับ extraAreaDimensionId ของ item
      const extraAreaDimension = extraAreaList.find(
        (area: any) => area.id === item.extraAreaDimensionId
      );

      // หา config จาก extraConfigList โดยจับคู่ subMaterialId ของ config กับ subMaterialId ของ item
      const config = extraConfigList.find(
        (cfg: any) => cfg.subMaterialId === item.subMaterialId
      );

      // หา blogSubMaterialDetail จาก config โดยจับคู่ id กับ blogSubMaterialDetailId ของ item
      const blogSubMaterialDetail = config
        ? config.subMaterialDetail.find(
            (detail: any) => detail.id === item.blogSubMaterialDetailId
          )
        : null;

      // สร้าง object blogSubMaterialDetail ที่รวมข้อมูลจาก detail พร้อมเพิ่ม subMaterialId และ subMaterialImageUrl
      const blogSubMaterialDetailObj = blogSubMaterialDetail
        ? {
            ...blogSubMaterialDetail, // รวม id, name, side จาก detail
            subMaterialId: item.subMaterialId, // เพิ่ม subMaterialId จาก item
            subMaterialImageUrl: config.imageUrl, // เพิ่ม subMaterialImageUrl จาก config
          }
        : null;

      // คืนค่า object ที่มีโครงสร้างตามที่ต้องการ
      return {
        extraMasterId: item.extraMasterId, // ค่าจาก item เดิม
        quantity: Number(item.quantity), // แปลง quantity ให้เป็นตัวเลข
        blogSubMaterialDetail: blogSubMaterialDetailObj, // ข้อมูล blogSubMaterialDetail ที่แปลงแล้ว
        extraSideDimension: extraSideDimension, // ข้อมูล extraSideDimension ที่จับคู่ได้
        extraAreaDimension: extraAreaDimension, // ข้อมูล extraAreaDimension ที่จับคู่ได้
        widthEstimate: item.widthEstimate || null,
        heightEstimate: item.heightEstimate || null,
      };
    });
  }

  const handleDeleteExtra = (index: number) => {
    const currentExtra = watch('extra');
    const currentLayDataOrderExtra = estimateDataSalesOrder.extra;
    const updatedExtra = currentExtra.filter(
      (_: any, i: number) => i !== index
    );
    const updatedLayDataOrderExtra = currentLayDataOrderExtra.filter(
      (_: any, i: number) => i !== index
    );
    setValue('extra', updatedExtra);
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        extra: updatedLayDataOrderExtra,
      })
    );
  };

  const handleClickEditExtra = (index: number) => {
    const currentExtra = watch('extra');
    const findValueObj = currentExtra.find(
      (_obj: any, i: number) => i === index
    );
    if (findValueObj) {
      setInitialValue(findValueObj);
      setOpen(true);
      setIndexEditExtra(index);
    }
  };

  const handleSubmitExtra = (value: any) => {
    setOpen(false);
    let newExtraValue;
    if (!isEmpty(initialValue)) {
      newExtraValue = watch('extra').map((item: any, index: number) => {
        if (index === indexEditExtra) {
          return value;
        }
        return item;
      });
    } else {
      newExtraValue = [...watch('extra'), value];
    }
    setValue('extra', newExtraValue);
    const result = transformExtra(
      { extra: newExtraValue },
      extraSideList,
      extraAreaList,
      extraConfigList
    );
    if (result) {
      dispatch(
        setEstimateDataSalesOrder({
          ...estimateDataSalesOrder,
          extra: result,
        })
      );
    }
  };
  console.log('estimateDataSalesOrder', estimateDataSalesOrder);
  console.log('extraConfigList', extraConfigList);
  return (
    <>
      <ModalOrderExtra
        open={open}
        handleClose={() => {
          setOpen(false);
        }}
        initialValues={initialValue}
        handleSubmitExtra={handleSubmitExtra}
        extraSideList={extraSideList}
        extraAreaList={extraAreaList}
        extraConfigList={extraConfigList}
      />
      <FadeInStyled className="flex items-center justify-between">
        <span className="topic">เทคนิคพิเศษ</span>
        <ActionButton
          variant="outlined"
          color="blueGrey"
          icon={<AddRoundedIcon />}
          text="เพิ่มรายการ"
          borderRadius="8px"
          onClick={handleClickAddExtra}
        />
      </FadeInStyled>
      <ExtraFromStyled
        as={motion.div}
        // layout
        style={{
          borderRadius: '16px',
        }}
      >
        {/* <LayoutGroup> */}
        {/* <AnimatePresence mode={'popLayout'} initial={false}> */}
        {!isEmpty(estimateDataSalesOrder.extra) ? (
          estimateDataSalesOrder.extra.map((extraItem: any, index: number) => (
            <motion.div
              key={`${extraItem.extraMasterId}-${
                extraItem.blogSubMaterialDetail?.id || index
              }`}
              className="part-item"
            >
              <div className="info-group">
                <Image
                  src={
                    extraItem.blogSubMaterialDetail.subMaterialImageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  alt={extraItem.blogSubMaterialDetail.name}
                  width={100}
                  height={100}
                  className="image"
                  style={{ border: '1px solid #DBE2E5' }}
                />
                {extraItem.extraAreaDimension && (
                  <Image
                    src={
                      extraItem.extraAreaDimension?.imageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    alt={extraItem.extraAreaDimension.name}
                    width={100}
                    height={100}
                    className="image ml-[-4px]"
                    style={{ border: '1px solid #DBE2E5' }}
                  />
                )}
                <div className="text-group">
                  <div className="name">
                    {extraItem.blogSubMaterialDetail?.name || 'ไม่มีชื่อ'}
                  </div>
                  <div className="type">
                    {`${extraItem.extraSideDimension?.name || 'ไม่มีข้อมูล'}, ${
                      extraItem.quantity
                    } จุด, ${
                      extraItem.extraAreaDimension
                        ? `${extraItem.extraAreaDimension.name}ของใบพิมพ์`
                        : `${numberWithCommas(
                            extraItem.widthEstimate
                          )} x ${numberWithCommas(
                            extraItem.heightEstimate
                          )} นิ้ว`
                    }`}
                  </div>
                </div>
              </div>
              <div className="action-group">
                <Button
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    width: '40px',
                    minWidth: '40px',
                    height: '40px',
                    minHeight: '40px',
                  }}
                  onClick={() => {
                    handleClickEditExtra(index);
                  }}
                >
                  <img
                    src="/icons/edit-black.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                </Button>
                <Button
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    width: '40px',
                    minWidth: '40px',
                    height: '40px',
                    minHeight: '40px',
                  }}
                  onClick={() => {
                    handleDeleteExtra(index);
                  }}
                >
                  <img
                    src="/icons/delete-black.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                </Button>
              </div>
            </motion.div>
          ))
        ) : (
          <motion.div className="h-[40px] w-full flex justify-center items-center text-[#dbe2e5]">
            ไม่มีรายการ
          </motion.div>
        )}

        {/* </AnimatePresence> */}
        {/* </LayoutGroup> */}
      </ExtraFromStyled>
    </>
  );
};

export default ExtraForm;
