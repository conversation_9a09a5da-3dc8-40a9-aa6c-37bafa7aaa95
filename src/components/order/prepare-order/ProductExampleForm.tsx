import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiProductExample from '@/services/stock/productExample';
import { Checkbox, FormControlLabel } from '@mui/material';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const ProductExampleFormStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  .list {
    width: 100%;
    padding: 10px 0;
    display: flex;
    align-items: center;
    border-top: 1px solid #dbe2e5;
    &:last-child {
      border-bottom: 1px solid #dbe2e5;
    }
    .MuiFormControlLabel-root {
      margin-right: 8px !important;
      overflow: hidden;
      span {
        white-space: nowrap;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
`;

type Props = {
  hookForm: any;
};

const ProductExampleForm = ({ hookForm }: Props) => {
  // const dispatch = useAppDispatch();
  const { setValue, watch } = hookForm;
  const [productExampleList, setProductExampleList] = useState<any[]>([]);
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const getProductExample = async () => {
    const res = await apiProductExample.getList();
    if (res && !res.isError) {
      setProductExampleList(res.data);
    }
  };

  useEffect(() => {
    getProductExample().then();
  }, []);
  console.log('productExampleList', productExampleList);
  return (
    <>
      <p className="topic">
        ตัวอย่างสินค้า ({watch('masterExample').length}/
        {productExampleList.length})
      </p>
      <ProductExampleFormStyled>
        {productExampleList?.map((item: any) => {
          return (
            <div className="list" key={item.id}>
              <FormControlLabel
                control={
                  <Checkbox
                    color="primary"
                    checked={watch('masterExample').includes(item.id)}
                    onChange={(event: any) => {
                      if (event.target.checked) {
                        setValue('masterExample', [
                          ...watch('masterExample'),
                          item.id,
                        ]);
                        dispatch(
                          setEstimateDataSalesOrder({
                            ...estimateDataSalesOrder,
                            masterExample: [
                              ...estimateDataSalesOrder.masterExample,
                              item,
                            ],
                          })
                        );
                      } else {
                        const filtered = watch('masterExample').filter(
                          (id: number) => id !== item.id
                        );
                        setValue('masterExample', filtered);
                        const newMasterExample =
                          estimateDataSalesOrder.masterExample.filter(
                            (example: any) => example.id !== item.id
                          );
                        dispatch(
                          setEstimateDataSalesOrder({
                            ...estimateDataSalesOrder,
                            masterExample: newMasterExample,
                          })
                        );
                      }
                    }}
                    icon={<IconUnCheckbox />}
                    checkedIcon={<IconCheckboxBlack />}
                  />
                }
                label={`${item.name} ${item.description}`}
              />
            </div>
          );
        })}
      </ProductExampleFormStyled>
    </>
  );
};

export default ProductExampleForm;
