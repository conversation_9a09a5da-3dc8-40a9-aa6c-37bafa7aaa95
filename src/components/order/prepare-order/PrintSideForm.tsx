import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import apiDimensions from '@/services/stock/dimensions';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { isEmpty } from 'lodash';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import {
  Button,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import apiProduct from '@/services/stock/product';
import { PrintConfigResponseType } from '@/types/responses/stock/product';
import { AnimatePresence, motion } from 'framer-motion';
import { FadeInStyled } from '@/styles/share.styled';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import DeleteButton from '@/components/global/DeleteButton';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';
import { HexColorPicker } from 'react-colorful';
import { motionColorPickerConfig } from '@/utils/motion/motion-config';

const PrintSideFormStyled = styled.div`
  width: 100%;
  .content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(164px, 1fr));
    margin-top: 8px;
    grid-gap: 16px;
    .item-side {
      width: 100%;
      height: 110px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      row-gap: 8px;
      position: relative;
      padding: 10px;
      box-shadow: #dbe2e5 0px 0px 0px 1px inset;
      overflow: hidden;
      cursor: pointer;
      &.disabled {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
      }
      &.active {
        box-shadow: #263238 0px 0px 0px 2px inset;
      }
      .checked {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        min-width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: black;
        border-radius: 50%;
        transition: 0.15s;
      }
      .side-image {
        width: 64px;
        height: 64px;
        min-width: 64px;
      }
      .name {
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 100%;
        overflow: hidden;
        font-size: 12px;
      }
    }
  }
  .add-more-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dbe2e5;
    border-radius: 8px;
    cursor: pointer;
  }
  .color-selector {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    .select-group {
      display: flex;
      gap: 12px;
    }
  }
  .react-colorful {
    width: calc(100% + 2px);
    height: auto;
    .react-colorful__pointer {
      width: 16px;
      height: 16px;
    }
    .react-colorful__hue {
      height: 16px;
    }
    .react-colorful__saturation {
      border: none;
    }
  }
`;

type Props = {
  hookForm: any;
};

const PrintSideForm = ({ hookForm }: Props) => {
  const [printSide, setPrintSide] = useState<any>([]);
  const [printArea, setPrintArea] = useState<any>([]);
  const [openPickerIdx, setOpenPickerIdx] = useState<string | null>(null);
  const { register, setValue, hookFormErrors, watch, isSubmitted } = hookForm;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [productPrintSystemConfig, setProductPrintSystemConfig] = useState<
    PrintConfigResponseType[]
  >([]);
  const [selectedPrintSystemConfig, setSelectedPrintSystemConfig] =
    useState<PrintConfigResponseType>({
      imageUrl: '',
      name: '',
      printColorData: [],
      printSystemId: '',
    });
  const wrapperRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(target) &&
        !target.closest('.react-colorful') &&
        !target.closest('.color-preview')
      ) {
        setOpenPickerIdx(null);
      }
    };
    document.addEventListener('mousedown', handleOutside);
    return () => document.removeEventListener('mousedown', handleOutside);
  }, []);

  useEffect(() => {
    if (
      !isEmpty(productPrintSystemConfig) &&
      watch('printingRequest.printSystemId')
    ) {
      const findSelectedPrintSystemConfig = productPrintSystemConfig.find(
        (printSystemItem: PrintConfigResponseType) =>
          printSystemItem.printSystemId ===
          watch('printingRequest.printSystemId')
      );
      if (findSelectedPrintSystemConfig) {
        setSelectedPrintSystemConfig(findSelectedPrintSystemConfig);
      }
    }
  }, [watch('printingRequest.printSystemId'), productPrintSystemConfig]);

  // ด้านพิมพ์ id = 7
  const getPrintSide = async () => {
    const res = await apiDimensions.getListDimensionByDimensionTypeId(7);
    if (!res.isError) {
      setPrintSide(res.data);
    }
  };

  // พื้นที่ id = 8
  const getPrintArea = async () => {
    const res = await apiDimensions.getListDimensionByDimensionTypeId(8);
    if (!res.isError) {
      setPrintArea(res.data);
    }
  };

  const getProductPrintSystemConfig = async () => {
    const res = await apiProduct.getListProductConfigByProductId(
      estimateDataSalesOrder.productModel.productId
    );
    if (!res.isError) {
      setProductPrintSystemConfig(res.data);
    }
  };

  useEffect(() => {
    getPrintSide().then();
    getPrintArea().then();
    getProductPrintSystemConfig().then();
  }, []);

  const isDisabled = (item: any) => {
    const sideValue = estimateDataSalesOrder?.subMaterialDetail?.side;
    if (sideValue === 2) {
      return false;
    }
    if (sideValue === 1) {
      return item.name === 'พิมพ์ หน้า / หลัง';
    }
    return item.name !== 'ไม่พิมพ์';
  };

  useEffect(() => {
    if (!isEmpty(printSide)) {
      const findPrintSide = printSide.find(
        (item: any) =>
          item.id === watch('printingRequest.printingSideDimensionId')
      );
      if (findPrintSide !== undefined) {
        if (watch('printingRequest.printingSideDimensionId') === 20) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              printingRequest: {
                ...estimateDataSalesOrder.printingRequest,
                colorFront: [],
                colorBack: [],
                printingSideDimension: findPrintSide,
                printSystem: {},
              },
            })
          );
        } else if (watch('printingRequest.printingSideDimensionId') === 21) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              printingRequest: {
                ...estimateDataSalesOrder.printingRequest,
                colorBack: [],
                printingSideDimension: findPrintSide,
              },
            })
          );
        } else if (watch('printingRequest.printingSideDimensionId') === 22) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              printingRequest: {
                ...estimateDataSalesOrder.printingRequest,
                colorFront: [],
                printingSideDimension: findPrintSide,
              },
            })
          );
        } else if (watch('printingRequest.printingSideDimensionId') === 23) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              printingRequest: {
                ...estimateDataSalesOrder.printingRequest,
                printingSideDimension: findPrintSide,
              },
            })
          );
        }
      }
    }
  }, [watch('printingRequest.printingSideDimensionId'), printSide]);

  const handleClickPrintSide = (printSide: any) => {
    if (printSide.id === 20) {
      setValue('printingRequest', {
        printSystemId: '',
        colorFront: [],
        colorBack: [],
        printingSideDimensionId: 20,
      });
    } else if (printSide.id === 21) {
      setValue('printingRequest', {
        ...watch('printingRequest'),
        printingSideDimensionId: printSide.id,
        colorBack: [],
      });
    } else if (printSide.id === 22) {
      setValue('printingRequest', {
        ...watch('printingRequest'),
        printingSideDimensionId: printSide.id,
        colorFront: [],
      });
    } else if (printSide.id === 23) {
      setValue('printingRequest', {
        ...watch('printingRequest'),
        printingSideDimensionId: printSide.id,
      });
    }
  };

  const reorderCMYKFirst = <T extends Record<string, any>>(list: T[]) => {
    const getId = (item: any) =>
      item.printColorId ?? item.printColor?.id ?? null;

    const cmyk = list.filter((i) => getId(i) === 1);
    const others = list.filter((i) => getId(i) !== 1);
    return [...cmyk, ...others];
  };

  const handleClickAddColor = (side: 'back' | 'front') => {
    const fieldName = side === 'front' ? 'colorFront' : 'colorBack';

    // state RHF ปัจจุบัน
    const currentColors = watch(`printingRequest.${fieldName}`) || [];

    // new item (RHF)
    const newColorValue = {
      colorCode: '',
      printAreaDimensionId: '',
      printColorId: '',
    };

    // new item (Redux)
    const newColorRedux = {
      printColor: { id: '', name: '', imageUrl: '' },
      color: { id: '', name: '' },
      printAreaDimension: { id: '', name: '', imageUrl: '' },
    };

    /* ---------- RHF ---------- */
    setValue(
      `printingRequest.${fieldName}`,
      reorderCMYKFirst([newColorValue, ...currentColors])
    );

    /* ---------- Redux ---------- */
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        printingRequest: {
          ...estimateDataSalesOrder.printingRequest,
          [fieldName]: reorderCMYKFirst([
            newColorRedux,
            ...estimateDataSalesOrder.printingRequest[fieldName],
          ]),
        },
      })
    );
  };

  const handleRemoveColor = (side: 'front' | 'back', idx: number) => {
    const fieldName = side === 'front' ? 'colorFront' : 'colorBack';

    /* ---------- RHF ---------- */
    const current = watch(`printingRequest.${fieldName}`) || [];
    const removed = current.filter((_d: any, i: number) => i !== idx);
    setValue(`printingRequest.${fieldName}`, reorderCMYKFirst(removed));

    /* ---------- Redux ---------- */
    const reduxRemoved = estimateDataSalesOrder.printingRequest[
      fieldName
    ].filter((_d: any, i: number) => i !== idx);
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        printingRequest: {
          ...estimateDataSalesOrder.printingRequest,
          [fieldName]: reorderCMYKFirst(reduxRemoved),
        },
      })
    );
  };

  const handleChangePrintColor = (
    side: 'front' | 'back',
    index: number,
    value: number
  ) => {
    const fieldName = side === 'front' ? 'colorFront' : 'colorBack';

    /* ---------- RHF ---------- */

    // 1. ดึงลิสต์ปัจจุบัน
    const rhfList = watch(`printingRequest.${fieldName}`) || [];

    // 2. แก้ item ตาม index ที่เลือก
    const rhfUpdated = rhfList.map((item: any, idx: number) =>
      idx === index ? { ...item, printColorId: value, colorCode: '' } : item
    );

    // 3. รี-ออร์เดอร์ แล้ว set กลับเข้า RHF
    setValue(`printingRequest.${fieldName}`, reorderCMYKFirst(rhfUpdated), {
      shouldValidate: !!isSubmitted,
    });

    /* ---------- Redux ---------- */
    const printSystem = productPrintSystemConfig
      .find(
        (ps) =>
          ps.printSystemId ===
          estimateDataSalesOrder.printingRequest.printSystem.id
      )
      ?.printColorData.find((c) => c.printColorId === value);

    if (!printSystem) return;

    const reduxUpdated = estimateDataSalesOrder.printingRequest[fieldName].map(
      (item: any, idx: number) =>
        idx === index
          ? {
              ...item,
              colorCode: '',
              printColor: {
                id: value,
                name: printSystem.name,
                imageUrl: printSystem.imageUrl,
              },
            }
          : item
    );

    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        printingRequest: {
          ...estimateDataSalesOrder.printingRequest,
          [fieldName]: reorderCMYKFirst(reduxUpdated),
        },
      })
    );
  };

  const handleChangeColorCode = (
    side: 'front' | 'back',
    index: number,
    value: string
  ) => {
    const fieldName = side === 'front' ? 'colorFront' : 'colorBack';

    // อัปเดตค่าใน React Hook Form
    setValue(`printingRequest.${fieldName}.${index}.colorCode`, value, {
      shouldValidate: !!isSubmitted,
    });

    // อัปเดต Redux state โดย map ข้อมูลใน field ที่ต้องการแก้ไข
    const updatedColorArray = estimateDataSalesOrder.printingRequest[
      fieldName
    ].map((item: any, idx: number) => {
      if (index === idx) {
        return {
          ...item,
          colorCode: value,
        };
      }
      return item;
    });
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        printingRequest: {
          ...estimateDataSalesOrder.printingRequest,
          [fieldName]: updatedColorArray,
        },
      })
    );
  };

  const handleChangePrintAreaDimension = (
    side: 'front' | 'back',
    index: number,
    value: number
  ) => {
    const fieldName = side === 'front' ? 'colorFront' : 'colorBack';
    setValue(
      `printingRequest[${fieldName}][${index}].printAreaDimensionId`,
      value,
      {
        shouldValidate: !!isSubmitted,
      }
    );
    const findPrintArea = printArea.find(
      (printAreaItem: any) => printAreaItem.id === value
    );
    const updatedColorArray = estimateDataSalesOrder.printingRequest[
      fieldName
    ].map((item: any, idx: number) => {
      if (index === idx) {
        return {
          ...item,
          printAreaDimension: {
            id: findPrintArea.id,
            name: findPrintArea.name,
            imageUrl: findPrintArea.imageUrl || null,
          },
        };
      }
      return item;
    });

    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        printingRequest: {
          ...estimateDataSalesOrder.printingRequest,
          [fieldName]: reorderCMYKFirst(updatedColorArray),
        },
      })
    );
  };

  if (!isEmpty(printSide)) {
    return (
      <PrintSideFormStyled
        as={motion.div}
        // layout
      >
        {/* <LayoutGroup> */}
        <FadeInStyled>
          <p className="topic">การพิมพ์</p>
          <div className="content">
            {printSide.map((item: any) => {
              const disabled = isDisabled(item);
              return (
                <div
                  key={item.id}
                  className={`item-side ${disabled ? 'disabled' : ''} ${
                    estimateDataSalesOrder.printingRequest
                      ?.printingSideDimension?.id === item.id
                      ? 'active'
                      : ''
                  }`}
                  onClick={() => {
                    if (!disabled) handleClickPrintSide(item);
                  }}
                >
                  {estimateDataSalesOrder.printingRequest?.printingSideDimension
                    ?.id === item.id && (
                    <div className="checked">
                      <CheckRoundedIcon
                        sx={{ fontSize: '16px', color: 'white' }}
                      />
                    </div>
                  )}
                  <Image
                    src={item.imageUrl}
                    width={300}
                    height={300}
                    alt={item.name}
                    className="side-image"
                    draggable={false}
                  />
                  <span className="name">{item.name}</span>
                </div>
              );
            })}
          </div>
        </FadeInStyled>
        <div className="label">ระบบพิมพ์</div>
        {/* <AnimatePresence initial={false} mode={'wait'}> */}
        {watch('printingRequest').printingSideDimensionId === 20 ? (
          <motion.div
            /* {...motionFadeConfig} */
            key={'noSide'}
            style={{
              height: '40px',
              width: '100%',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#78909C',
              background: '#f5f7f8',
              boxShadow: '0 0 0 1px #DBE2E5',
            }}
          >
            ไม่พิมพ์
          </motion.div>
        ) : (
          <motion.div
            /* {...motionFadeConfig} */
            key={'isSide'}
          >
            <FormControl fullWidth>
              <Select
                displayEmpty
                {...register('printingRequest.printSystemId')}
                error={Boolean(hookFormErrors.printingRequest?.printSystemId)}
                value={watch('printingRequest').printSystemId || ''}
                onChange={(e: any) => {
                  const updatePrintSystem = productPrintSystemConfig.find(
                    (item: any) => {
                      if (item.printSystemId === e.target.value) {
                        const newObj = {
                          id: item.printSystemId,
                          name: item.name,
                        };
                        return newObj || null;
                      }
                      return null;
                    }
                  );
                  dispatch(
                    setEstimateDataSalesOrder({
                      ...estimateDataSalesOrder,
                      printingRequest: {
                        ...estimateDataSalesOrder.printingRequest,
                        printSystem: updatePrintSystem
                          ? {
                              id: updatePrintSystem.printSystemId,
                              name: updatePrintSystem.name,
                            }
                          : null,
                        colorFront: [],
                        colorBack: [],
                      },
                    })
                  );
                  setValue(
                    'printingRequest',
                    {
                      ...watch('printingRequest'),
                      printSystemId: e.target.value,
                      colorFront: [],
                      colorBack: [],
                    },
                    {
                      shouldValidate: !!isSubmitted,
                    }
                  );
                }}
                disabled={
                  watch('printingRequest.printingSideDimensionId') === 20
                }
              >
                <MenuItem disabled value="">
                  <div className="text-[#78909C]">กรุณาเลือก</div>
                </MenuItem>
                {productPrintSystemConfig?.map(
                  (item: PrintConfigResponseType) => (
                    <MenuItem
                      key={item.printSystemId}
                      value={item.printSystemId}
                    >
                      {item.name}
                    </MenuItem>
                  )
                )}
              </Select>
              {hookFormErrors.printingRequest?.printSystemId && (
                <FormHelperText error>
                  {
                    hookFormErrors.printingRequest.printSystemId
                      .message as React.ReactNode
                  }
                </FormHelperText>
              )}
            </FormControl>
          </motion.div>
        )}
        {/* </AnimatePresence> */}
        {/* </LayoutGroup> */}
        {/* <AnimatePresence initial={false} mode={'wait'}> */}
        {(watch('printingRequest.printingSideDimensionId') === 21 ||
          watch('printingRequest.printingSideDimensionId') === 23) && (
          <motion.div
          /* {...motionFadeConfig} */
          >
            <div className="flex items-center justify-between h-[40px] my-[8px] mt-[16px]">
              <div className="label !m-0">ด้านหน้า</div>
              <Button
                variant="contained"
                color="dark"
                style={{
                  height: '32px',
                  width: '32px',
                  minWidth: '32px',
                }}
                onClick={() => {
                  handleClickAddColor('front');
                }}
                disabled={watch('printingRequest.printSystemId') === ''}
              >
                <AddRoundedIcon />
              </Button>
            </div>
            <div className="color-selector">
              {!isEmpty(watch('printingRequest.colorFront')) ? (
                watch('printingRequest.colorFront').map(
                  (colorFrontItem: any, index: number) => {
                    // ดึง error
                    const errorObj =
                      hookFormErrors?.printingRequest?.colorFront?.[index];
                    console.log('colorFrontItem', colorFrontItem);
                    return (
                      <div className="select-group" key={index}>
                        {/* printColorId */}
                        {(() => {
                          const colorFrontList =
                            watch('printingRequest.colorFront') || [];
                          return (
                            <FormControl fullWidth>
                              <Select
                                displayEmpty
                                {...register(
                                  `printingRequest.colorFront[${index}].printColorId`
                                )}
                                error={Boolean(errorObj?.printColorId)}
                                value={colorFrontItem.printColorId || ''}
                                onChange={(e: any) =>
                                  handleChangePrintColor(
                                    'front',
                                    index,
                                    e.target.value
                                  )
                                }
                              >
                                <MenuItem disabled value="">
                                  <div className="text-[#78909C]">
                                    กรุณาเลือก
                                  </div>
                                </MenuItem>

                                {selectedPrintSystemConfig?.printColorData?.map(
                                  (item: any) => {
                                    // นับจำนวนของ printColorId นี้ใน list
                                    const sameColorCount =
                                      colorFrontList.filter(
                                        (cf: any) =>
                                          cf.printColorId === item.printColorId
                                      ).length;

                                    // ถ้าถูกเลือกครบ max แล้ว และตัวนี้ไม่ใช่ค่าที่เลือกอยู่ตอนนี้ → disable
                                    const isMaxColor =
                                      sameColorCount >= item.maxItem &&
                                      colorFrontItem.printColorId !==
                                        item.printColorId;

                                    return (
                                      <MenuItem
                                        key={item.printColorId}
                                        value={item.printColorId}
                                        disabled={isMaxColor}
                                      >
                                        {item.name}
                                      </MenuItem>
                                    );
                                  }
                                )}
                              </Select>

                              {errorObj?.printColorId && (
                                <FormHelperText error>
                                  {errorObj.printColorId.message}
                                </FormHelperText>
                              )}
                            </FormControl>
                          );
                        })()}

                        {/* colorCode */}
                        <div
                          style={{
                            width: '100%',
                            position: 'relative',
                          }}
                        >
                          <AnimatePresence initial={false} mode="sync">
                            {openPickerIdx === `front-${index}` && (
                              <motion.div
                                {...motionColorPickerConfig}
                                style={{
                                  position: 'relative',
                                  zIndex: 2,
                                }}
                              >
                                <HexColorPicker
                                  color={colorFrontItem.colorCode || '#ffffff'}
                                  onChange={(newColor: string) => {
                                    handleChangeColorCode(
                                      'front',
                                      index,
                                      newColor
                                    );
                                  }}
                                  style={{
                                    left: '50%',
                                    top: 0,
                                    transform: 'translate(-50%, 48px)',
                                    aspectRatio: '1/1',
                                    marginBottom: '8px',
                                    position: 'absolute',
                                    zIndex: 1,
                                  }}
                                />
                              </motion.div>
                            )}
                          </AnimatePresence>
                          {(() => {
                            const selectedPrintSystemId = watch(
                              'printingRequest.printSystemId'
                            );

                            const printSystem = productPrintSystemConfig.find(
                              (printSystemConfigItem: any) =>
                                printSystemConfigItem.printSystemId ===
                                selectedPrintSystemId
                            );

                            if (printSystem) {
                              const selectedPrintColorId = watch(
                                'printingRequest.colorFront'
                              )?.[index]?.printColorId;
                              const colorData: any =
                                printSystem.printColorData.find(
                                  (colorItem: any) =>
                                    colorItem.printColorId ===
                                    selectedPrintColorId
                                );
                              console.log('colorData', colorData);
                              if (colorData) {
                                if (colorData.isColorCode) {
                                  return (
                                    <TextField
                                      type="text"
                                      fullWidth
                                      placeholder={'Code'}
                                      {...register(
                                        `printingRequest.colorFront[${index}].colorCode`
                                      )}
                                      value={colorFrontItem.colorCode || ''}
                                      onChange={(e: any) =>
                                        handleChangeColorCode(
                                          'front',
                                          index,
                                          e.target.value
                                        )
                                      }
                                      error={Boolean(errorObj?.colorCode)}
                                      helperText={
                                        errorObj?.colorCode &&
                                        errorObj.colorCode.message
                                      }
                                      InputProps={{
                                        endAdornment: (
                                          <ColorCodeStyled
                                            className="color-preview"
                                            data-id={index}
                                            ref={wrapperRef}
                                            style={{
                                              background:
                                                colorFrontItem.colorCode ||
                                                'transparent',
                                            }}
                                            onClick={() => {
                                              if (
                                                colorData.isColorCode &&
                                                watch(
                                                  'printingRequest.colorFront'
                                                )?.[index]?.printColorId !== ''
                                              ) {
                                                setOpenPickerIdx((cur) =>
                                                  cur === `front-${index}`
                                                    ? null
                                                    : `front-${index}`
                                                );
                                              }
                                            }}
                                          />
                                        ),
                                      }}
                                    />
                                  );
                                }
                                return (
                                  <TextField
                                    type="text"
                                    fullWidth
                                    placeholder={colorData.name}
                                    value={colorData.name}
                                    {...register(
                                      `printingRequest.colorFront[${index}].colorCode`
                                    )}
                                    disabled={true}
                                  />
                                );
                              }
                            }
                            return (
                              <TextField
                                type="text"
                                fullWidth
                                placeholder={'Color'}
                                {...register(
                                  `printingRequest.colorFront[${index}].colorCode`
                                )}
                                disabled={true}
                              />
                            );
                          })()}
                        </div>
                        {/* printAreaDimensionId */}
                        <FormControl fullWidth>
                          <Select
                            displayEmpty
                            {...register(
                              `printingRequest.colorFront[${index}].printAreaDimensionId`
                            )}
                            error={Boolean(errorObj?.printAreaDimensionId)}
                            value={colorFrontItem.printAreaDimensionId || ''}
                            onChange={(e: any) =>
                              handleChangePrintAreaDimension(
                                'front',
                                index,
                                e.target.value
                              )
                            }
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {printArea?.map((areaItem: any) => (
                              <MenuItem key={areaItem.id} value={areaItem.id}>
                                {areaItem.name}
                              </MenuItem>
                            ))}
                          </Select>
                          {errorObj?.printAreaDimensionId && (
                            <FormHelperText error>
                              {errorObj.printAreaDimensionId.message}
                            </FormHelperText>
                          )}
                        </FormControl>

                        {/* Delete Button */}
                        <DeleteButton
                          onClick={() => handleRemoveColor('front', index)}
                        >
                          <Image
                            src="/icons/delete-white.svg"
                            width={24}
                            height={24}
                            alt=""
                          />
                        </DeleteButton>
                      </div>
                    );
                  }
                )
              ) : (
                <>
                  <motion.div
                    /* {...motionFadeConfig} */
                    key={'noSide'}
                    style={{
                      height: '40px',
                      width: '100%',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#78909C',
                      background: '#f5f7f8',
                      boxShadow: hookFormErrors.printingRequest?.colorFront
                        ? '0 0 0 1px #E91E63'
                        : '0 0 0 1px #DBE2E5',
                    }}
                  >
                    <input
                      type="radio"
                      {...register(`printingRequest.colorFront`)}
                      onChange={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      checked={false}
                      style={{
                        opacity: 0,
                        height: 0,
                        width: 0,
                      }}
                    />
                    ไม่มีรายการ
                  </motion.div>
                  {hookFormErrors.printingRequest?.colorFront && (
                    <div
                      style={{
                        marginTop: '-12px',
                      }}
                    >
                      <FormHelperText error>
                        กรุณาเพิ่มสีด้านหน้า
                      </FormHelperText>
                    </div>
                  )}
                </>
              )}
            </div>
          </motion.div>
        )}
        {(watch('printingRequest.printingSideDimensionId') === 22 ||
          watch('printingRequest.printingSideDimensionId') === 23) && (
          <motion.div
          /* {...motionFadeConfig} */
          >
            <div className="flex items-center justify-between h-[40px] my-[8px] mt-[16px]">
              <div className="label !m-0">ด้านหลัง</div>
              <Button
                variant="contained"
                color="dark"
                style={{
                  height: '32px',
                  width: '32px',
                  minWidth: '32px',
                }}
                onClick={() => {
                  handleClickAddColor('back');
                }}
                disabled={watch('printingRequest.printSystemId') === ''}
              >
                <AddRoundedIcon />
              </Button>
            </div>
            <div className="color-selector">
              {!isEmpty(watch('printingRequest.colorBack')) ? (
                watch('printingRequest.colorBack').map(
                  (colorBackItem: any, index: number) => {
                    const errorObj =
                      hookFormErrors?.printingRequest?.colorBack?.[index];
                    return (
                      <div className="select-group" key={index}>
                        {/* printColorId */}
                        {(() => {
                          const colorBackList =
                            watch('printingRequest.colorBack') || [];

                          return (
                            <FormControl fullWidth>
                              <Select
                                displayEmpty
                                {...register(
                                  `printingRequest.colorBack[${index}].printColorId`
                                )}
                                error={Boolean(errorObj?.printColorId)}
                                value={colorBackItem.printColorId || ''}
                                onChange={(e: any) =>
                                  handleChangePrintColor(
                                    'back',
                                    index,
                                    e.target.value
                                  )
                                }
                              >
                                <MenuItem disabled value="">
                                  <div className="text-[#78909C]">
                                    กรุณาเลือก
                                  </div>
                                </MenuItem>
                                {selectedPrintSystemConfig?.printColorData?.map(
                                  (item: any) => {
                                    const sameColorCount = colorBackList.filter(
                                      (cf: any) =>
                                        cf.printColorId === item.printColorId
                                    ).length;

                                    const isMaxColor =
                                      sameColorCount >= item.maxItem &&
                                      colorBackItem.printColorId !==
                                        item.printColorId;

                                    return (
                                      <MenuItem
                                        key={item.printColorId}
                                        value={item.printColorId}
                                        disabled={isMaxColor}
                                      >
                                        {item.name}
                                      </MenuItem>
                                    );
                                  }
                                )}
                              </Select>
                              {errorObj?.printColorId && (
                                <FormHelperText error>
                                  {errorObj.printColorId.message}
                                </FormHelperText>
                              )}
                            </FormControl>
                          );
                        })()}

                        {/* colorCode */}
                        <div
                          style={{
                            width: '100%',
                            position: 'relative',
                          }}
                        >
                          <AnimatePresence initial={false} mode="sync">
                            {openPickerIdx === `back-${index}` && (
                              <motion.div
                                {...motionColorPickerConfig}
                                style={{
                                  position: 'relative',
                                  zIndex: 2,
                                }}
                              >
                                <HexColorPicker
                                  color={colorBackItem.colorCode || '#ffffff'}
                                  onChange={(newColor: string) => {
                                    handleChangeColorCode(
                                      'back',
                                      index,
                                      newColor
                                    );
                                  }}
                                  style={{
                                    left: '50%',
                                    top: 0,
                                    transform: 'translate(-50%, 48px)',
                                    aspectRatio: '1/1',
                                    marginBottom: '8px',
                                    position: 'absolute',
                                    zIndex: 1,
                                  }}
                                />
                              </motion.div>
                            )}
                          </AnimatePresence>
                          {(() => {
                            const selectedPrintSystemId = watch(
                              'printingRequest.printSystemId'
                            );

                            const printSystem = productPrintSystemConfig.find(
                              (printSystemConfigItem: any) =>
                                printSystemConfigItem.printSystemId ===
                                selectedPrintSystemId
                            );

                            if (printSystem) {
                              const selectedPrintColorId = watch(
                                'printingRequest.colorBack'
                              )?.[index]?.printColorId;

                              const colorData: any =
                                printSystem.printColorData.find(
                                  (colorItem: any) =>
                                    colorItem.printColorId ===
                                    selectedPrintColorId
                                );

                              if (colorData) {
                                if (colorData.isColorCode) {
                                  return (
                                    <TextField
                                      type="text"
                                      fullWidth
                                      placeholder="Code"
                                      {...register(
                                        `printingRequest.colorBack[${index}].colorCode`
                                      )}
                                      value={colorBackItem.colorCode || ''}
                                      onChange={(e: any) =>
                                        handleChangeColorCode(
                                          'back',
                                          index,
                                          e.target.value
                                        )
                                      }
                                      error={Boolean(errorObj?.colorCode)}
                                      helperText={
                                        errorObj?.colorCode &&
                                        errorObj.colorCode.message
                                      }
                                      InputProps={{
                                        endAdornment: (
                                          <ColorCodeStyled
                                            className="color-preview"
                                            data-id={index}
                                            ref={wrapperRef}
                                            style={{
                                              background:
                                                colorBackItem.colorCode ||
                                                'transparent',
                                            }}
                                            onClick={() => {
                                              if (
                                                colorData.isColorCode &&
                                                selectedPrintColorId !== ''
                                              ) {
                                                setOpenPickerIdx((cur) =>
                                                  cur === `back-${index}`
                                                    ? null
                                                    : `back-${index}`
                                                );
                                              }
                                            }}
                                          />
                                        ),
                                      }}
                                    />
                                  );
                                }

                                // ถ้าไม่รองรับ colorCode → placeholder = ชื่อสี
                                return (
                                  <TextField
                                    type="text"
                                    fullWidth
                                    placeholder={colorData.name}
                                    {...register(
                                      `printingRequest.colorBack[${index}].colorCode`
                                    )}
                                    disabled
                                  />
                                );
                              }
                            }

                            // fallback ถ้าไม่มีข้อมูลเลย
                            return (
                              <TextField
                                type="text"
                                fullWidth
                                placeholder="Color"
                                {...register(
                                  `printingRequest.colorBack[${index}].colorCode`
                                )}
                                disabled
                              />
                            );
                          })()}
                        </div>

                        {/* printAreaDimensionId */}
                        <FormControl fullWidth>
                          <Select
                            displayEmpty
                            {...register(
                              `printingRequest.colorBack[${index}].printAreaDimensionId`
                            )}
                            error={Boolean(errorObj?.printAreaDimensionId)}
                            value={colorBackItem.printAreaDimensionId || ''}
                            onChange={(e: any) =>
                              handleChangePrintAreaDimension(
                                'back',
                                index,
                                e.target.value
                              )
                            }
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {printArea?.map((areaItem: any) => (
                              <MenuItem key={areaItem.id} value={areaItem.id}>
                                {areaItem.name}
                              </MenuItem>
                            ))}
                          </Select>
                          {errorObj?.printAreaDimensionId && (
                            <FormHelperText error>
                              {errorObj.printAreaDimensionId.message}
                            </FormHelperText>
                          )}
                        </FormControl>

                        {/* Delete Button */}
                        <DeleteButton
                          onClick={() => handleRemoveColor('back', index)}
                        >
                          <Image
                            src="/icons/delete-white.svg"
                            width={24}
                            height={24}
                            alt=""
                          />
                        </DeleteButton>
                      </div>
                    );
                  }
                )
              ) : (
                <>
                  <motion.div
                    /* {...motionFadeConfig} */
                    key={'noSide'}
                    style={{
                      height: '40px',
                      width: '100%',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#78909C',
                      background: '#f5f7f8',
                      boxShadow: hookFormErrors.printingRequest?.colorBack
                        ? '0 0 0 1px #E91E63'
                        : '0 0 0 1px #DBE2E5',
                    }}
                  >
                    <input
                      type="radio"
                      {...register(`printingRequest.colorBack`)}
                      onChange={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      checked={false}
                      style={{
                        opacity: 0,
                        height: 0,
                        width: 0,
                      }}
                    />
                    ไม่มีรายการ
                  </motion.div>
                  {hookFormErrors.printingRequest?.colorBack && (
                    <div
                      style={{
                        marginTop: '-12px',
                      }}
                    >
                      <FormHelperText error>
                        กรุณาเพิ่มสีด้านหลัง
                      </FormHelperText>
                    </div>
                  )}
                </>
              )}
            </div>
          </motion.div>
        )}
        {/* </AnimatePresence> */}
      </PrintSideFormStyled>
    );
  }
  return null;
};

const ColorCodeStyled = styled.div`
  width: 24px;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px dashed #abb0b2;
  cursor: pointer;
`;
export default PrintSideForm;
