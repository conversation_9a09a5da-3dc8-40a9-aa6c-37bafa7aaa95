import React, { ReactNode, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import ActionButton from '@/components/ActionButton';
import { isNull } from 'lodash';
import SideDetail from '@/components/SideDetail';
import { FormHelperText, IconButton } from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import ProductConfigSideSelector from '@/components/order/prepare-order/ProductConfigSideSelector';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import {
  MaterialConfigItem,
  ProductConfigCategory,
} from '@/components/order/prepare-order/PrepareOrderFormZone';
import { FadeInStyled, LoadingFadein } from '@/styles/share.styled';
import { salesOrderSelector } from '@/store/features/estimate';

const MaterialFromStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 16px;
  padding: 8px 20px 8px 8px;
  height: 80px;
  column-gap: 24px;
  margin-top: 8px;
  background: #f5f7f8;
  border: 1px solid #dbe2e5;
  .material-info {
    display: flex;
    align-items: center;
    column-gap: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    max-width: 100%;
    overflow: hidden;
    .material-image {
      width: 64px;
      height: 64px;
      border-radius: 6px;
    }
    .text-group {
      display: flex;
      flex-direction: column;
      row-gap: 2px;
      max-width: 100%;
      overflow: hidden;
      .name {
        font-weight: 600;
        max-width: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .type {
        font-size: 12px;
        color: #90a4ae;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
`;

type Props = {
  hookForm: any;
  materialConfig: ProductConfigCategory;
};

const MaterialFrom = ({ hookForm, materialConfig }: Props) => {
  const { register, setValue, hookFormErrors, watch } = hookForm;
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const [openSideSelector, setOpenSideSelector] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);
  const [subMaterialName, setSubMaterialName] = useState<string>('');
  const handleClickAddMaterial = () => {
    setOpenSideSelector(true);
  };

  const getSubMaterialName = () => {
    const subMaterialName = materialConfig.productConfig?.find(
      (item: MaterialConfigItem) =>
        item.subMaterialId ===
        estimateDataSalesOrder.subMaterialDetail.subMaterialId
    )?.subMaterialName;
    if (subMaterialName) {
      return subMaterialName;
    }
  };
  return (
    <>
      <SideDetail
        isOpen={openSideSelector}
        handleClickOutSide={() => {
          // setOpenSideSelector(false);
        }}
      >
        <>
          <div className="header">
            {step === 2 ? (
              <FadeInStyled
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  columnGap: '16px',
                }}
              >
                <div
                  className="back"
                  onClick={() => {
                    setStep(step - 1);
                  }}
                >
                  <IconButton
                    sx={{
                      color: '#263238',
                    }}
                  >
                    <KeyboardBackspaceRoundedIcon />
                  </IconButton>
                </div>
                <div className="topic">{subMaterialName}</div>
              </FadeInStyled>
            ) : (
              <div>
                <FadeInStyled>
                  <div className="topic">Base Material</div>
                </FadeInStyled>
              </div>
            )}
            <div
              className="x-close"
              onClick={() => {
                setOpenSideSelector(false);
              }}
            >
              <IconButton>
                <CloseIcon />
              </IconButton>
            </div>
          </div>
          <div className="content">
            <div className="body">
              <ProductConfigSideSelector
                productConfig={materialConfig}
                hookForm={{ register, setValue, hookFormErrors, watch }}
                isOpen={openSideSelector}
                step={step}
                setStep={setStep}
                setSubMaterialName={setSubMaterialName}
                setOpenSideSelector={setOpenSideSelector}
                type={'material'}
              />
            </div>
          </div>
        </>
      </SideDetail>
      <p className="topic">วัสดุ</p>
      <input
        type="file"
        style={{ opacity: 0, width: 0, height: 0 }}
        {...register('materialMasterId')}
      />
      <MaterialFromStyled>
        {(() => {
          const { subMaterialDetail } = estimateDataSalesOrder;
          if (!isNull(subMaterialDetail)) {
            return (
              <>
                <div
                  key={subMaterialDetail.subMaterialId}
                  className="material-info"
                >
                  <Image
                    src={
                      subMaterialDetail.subMaterialImageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    alt=""
                    width={128}
                    height={128}
                    className="material-image"
                  />
                  <div className="text-group">
                    <div className="name">{subMaterialDetail.name}</div>
                    <div className="type">{getSubMaterialName()}</div>
                  </div>
                </div>
                <FadeInStyled
                  onClick={() => {
                    if (!openSideSelector) {
                      handleClickAddMaterial();
                    }
                  }}
                >
                  <ActionButton
                    variant="contained"
                    color="dark"
                    icon={
                      <Image
                        src={'/icons/icon-change-circle.svg'}
                        alt=""
                        width={24}
                        height={24}
                      />
                    }
                    text="เปลี่ยนวัสดุ"
                    borderRadius="8px"
                  />
                </FadeInStyled>
              </>
            );
          }
          return (
            <FadeInStyled className="w-full flex justify-center">
              <div
                onClick={() => {
                  if (!openSideSelector) {
                    handleClickAddMaterial();
                  }
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<AddRoundedIcon />}
                  text="เพิ่มรายการ"
                  borderRadius="8px"
                />
              </div>
            </FadeInStyled>
          );
        })()}
      </MaterialFromStyled>
      {hookFormErrors.materialMasterId && (
        <FormHelperText error>
          {hookFormErrors.materialMasterId.message as ReactNode}
        </FormHelperText>
      )}
    </>
  );
};

export default MaterialFrom;
