import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { isEmpty } from 'lodash';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import {
  Button,
  FormControl,
  FormHelperText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { motion } from 'framer-motion';
import { FadeInStyled } from '@/styles/share.styled';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import apiDimensions from '@/services/stock/dimensions';
import { ProductConfigCategory } from '@/components/order/prepare-order/PrepareOrderFormZone';
import { useWatch } from 'react-hook-form';
import DeleteButton from '@/components/global/DeleteButton';
import {
  salesOrderSelector,
  setEstimateDataSalesOrder,
} from '@/store/features/estimate';

const CoatingFormFormStyled = styled.div`
  width: 100%;
  .content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(164px, 1fr));
    margin-top: 8px;
    grid-gap: 16px;
    .item-side {
      width: 100%;
      height: 110px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      row-gap: 8px;
      position: relative;
      padding: 10px;
      box-shadow: #dbe2e5 0px 0px 0px 1px inset;
      overflow: hidden;
      cursor: pointer;
      &.disabled {
        pointer-events: none;
        opacity: 0.5;
        cursor: not-allowed;
      }
      &.active {
        box-shadow: #263238 0px 0px 0px 2px inset;
      }
      .checked {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        min-width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: black;
        border-radius: 50%;
        transition: 0.15s;
      }
      .side-image {
        width: 64px;
        height: 64px;
        min-width: 64px;
      }
      .name {
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 100%;
        overflow: hidden;
        font-size: 12px;
      }
    }
  }
  .add-more-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dbe2e5;
    border-radius: 8px;
    cursor: pointer;
  }
  .coating-selector {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    .select-group {
      display: flex;
      gap: 12px;
    }
  }
`;

type Props = {
  hookForm: any;
  coatingConfig: ProductConfigCategory;
  finishList: any[];
};

const CoatingForm = ({ hookForm, coatingConfig, finishList }: Props) => {
  const [coatingSide, setCoatingSide] = useState<any>([]);
  const { register, setValue, hookFormErrors, watch, control, isSubmitted } =
    hookForm;
  const dispatch = useAppDispatch();
  const { estimateDataSalesOrder } = useAppSelector(salesOrderSelector);
  const watchCoatingRequest = useWatch({
    control,
    name: 'coatingRequest',
    defaultValue: {
      coatingSideDimensionId: 31,
      finishFront: [],
      finishBack: [],
    },
  });
  const getCoatingSize = async () => {
    const res = await apiDimensions.getListDimensionByDimensionTypeId(10);
    if (!res.isError) {
      setCoatingSide(res.data);
    }
  };
  useEffect(() => {
    getCoatingSize().then();
  }, []);

  const isDisabled = (item: any) => {
    const sideValue = estimateDataSalesOrder?.subMaterialDetail?.side;
    if (sideValue === 2) {
      return false;
    }
    if (sideValue === 1) {
      return item.name === 'เคลือบ หน้า / หลัง';
    }
    return item.name !== 'ไม่เคลือบ';
  };

  useEffect(() => {
    if (!isEmpty(coatingSide)) {
      const findCoatingSide = coatingSide.find(
        (item: any) => item.id === watchCoatingRequest.coatingSideDimensionId
      );
      if (findCoatingSide !== undefined) {
        if (watchCoatingRequest.coatingSideDimensionId === 31) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              coatingRequest: {
                ...estimateDataSalesOrder.coatingRequest,
                finishBack: [],
                finishFront: [],
                coatingSideDimension: findCoatingSide,
              },
            })
          );
        } else if (watchCoatingRequest.coatingSideDimensionId === 32) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              coatingRequest: {
                ...estimateDataSalesOrder.coatingRequest,
                finishBack: [],
                coatingSideDimension: findCoatingSide,
              },
            })
          );
        } else if (watchCoatingRequest.coatingSideDimensionId === 33) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              coatingRequest: {
                ...estimateDataSalesOrder.coatingRequest,
                finishFront: [],
                coatingSideDimension: findCoatingSide,
              },
            })
          );
        } else if (watchCoatingRequest.coatingSideDimensionId === 34) {
          dispatch(
            setEstimateDataSalesOrder({
              ...estimateDataSalesOrder,
              coatingRequest: {
                ...estimateDataSalesOrder.coatingRequest,
                coatingSideDimension: findCoatingSide,
              },
            })
          );
        }
      }
    }
  }, [watchCoatingRequest.coatingSideDimensionId, coatingSide]);

  const handleClickCoatingSide = (coatingSide: any) => {
    if (coatingSide.id === 31) {
      setValue('coatingRequest', {
        coatingSideDimensionId: coatingSide.id,
        finishFront: [],
        finishBack: [],
      });
    } else if (coatingSide.id === 32) {
      setValue('coatingRequest', {
        ...watchCoatingRequest,
        coatingSideDimensionId: coatingSide.id,
        finishBack: [],
      });
    } else if (coatingSide.id === 33) {
      setValue('coatingRequest', {
        ...watchCoatingRequest,
        coatingSideDimensionId: coatingSide.id,
        finishFront: [],
      });
    } else if (coatingSide.id === 34) {
      setValue('coatingRequest', {
        ...watchCoatingRequest,
        coatingSideDimensionId: coatingSide.id,
      });
    }
  };

  const handleClickAddCoating = (side: 'back' | 'front') => {
    const fieldName = side === 'front' ? 'finishFront' : 'finishBack';

    const newFinish = {
      coatingOrder: '',
      coatingOrderEnum: '',
      coatingMasterId: '',
      finishSubMaterialDetail: {
        id: '',
        name: '',
        side: null,
        subMaterialId: '',
        subMaterialImageUrl: null,
      },
    };

    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        coatingRequest: {
          ...estimateDataSalesOrder.coatingRequest,
          [fieldName]: [
            newFinish,
            ...estimateDataSalesOrder.coatingRequest[fieldName],
          ],
        },
      })
    );

    setValue(`coatingRequest[${fieldName}]`, [
      {
        coatingOrder: '',
        coatingMasterId: '',
        finishSubMaterialDetailId: '',
        side: '',
      },
      ...watchCoatingRequest[fieldName],
    ]);
  };

  const handleRemoveCoating = (side: 'front' | 'back', idx: number) => {
    const fieldName = side === 'front' ? 'finishFront' : 'finishBack';
    const currentCoatingData = watchCoatingRequest[fieldName] || [];

    const updatedCoatingData = currentCoatingData.filter(
      (_data: any, index: number) => index !== idx
    );

    setValue(`coatingRequest.${fieldName}`, updatedCoatingData);

    const updatedLayDataOrder = {
      ...estimateDataSalesOrder,
      coatingRequest: {
        ...estimateDataSalesOrder.coatingRequest,
        [fieldName]: estimateDataSalesOrder.coatingRequest[fieldName].filter(
          (_data: any, index: number) => index !== idx
        ),
      },
    };

    dispatch(setEstimateDataSalesOrder(updatedLayDataOrder));
  };

  const handleChangeCoatingMaster = (
    side: 'front' | 'back',
    index: number,
    value: number
  ) => {
    const configData: any = coatingConfig?.productConfig?.find(
      (configItem: any) => configItem.masterId === value
    );
    const order =
      configData.positionEnum === 'BEFORE' ? 'ก่อนพิมพ์' : 'หลังพิมพ์';
    const fieldName = side === 'front' ? 'finishFront' : 'finishBack';
    setValue(`coatingRequest[${fieldName}][${index}].coatingMasterId`, value, {
      shouldValidate: !!isSubmitted,
    });
    setValue(
      `coatingRequest[${fieldName}][${index}].finishSubMaterialDetailId`,
      '',
      {
        shouldValidate: !!isSubmitted,
      }
    );
    setValue(
      `coatingRequest[${fieldName}][${index}].coatingPositionEnum`,
      order,
      {
        shouldValidate: !!isSubmitted,
      }
    );
    const updatedCoatingArray = estimateDataSalesOrder.coatingRequest[
      fieldName
    ].map((item: any, idx: number) => {
      if (index === idx) {
        return {
          ...item,
          coatingMasterId: value,
          coatingOrderEnum: order,
          finishSubMaterialDetail: {
            id: '',
            name: '',
            side: null,
            subMaterialId: '',
            subMaterialImageUrl: null,
          },
        };
      }
      return item;
    });
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        coatingRequest: {
          ...estimateDataSalesOrder.coatingRequest,
          [fieldName]: updatedCoatingArray,
        },
      })
    );
  };

  const handleChangeFinishSubMaterialDetail = (
    side: 'front' | 'back',
    index: number,
    value: number
  ) => {
    const fieldName = side === 'front' ? 'finishFront' : 'finishBack';
    setValue(
      `coatingRequest[${fieldName}][${index}].finishSubMaterialDetailId`,
      value,
      {
        shouldValidate: !!isSubmitted,
      }
    );
    const findFinish = finishList.find(
      (fItem: any) =>
        fItem.masterId ===
        watch('coatingRequest')[fieldName][index].coatingMasterId
    );
    const findFinishSubMaterialDetail = findFinish.subMaterialDetail.find(
      (fSubDetail: any) => fSubDetail.id === value
    );
    const updatedCoatingArray = estimateDataSalesOrder.coatingRequest[
      fieldName
    ].map((item: any, idx: number) => {
      if (index === idx) {
        return {
          ...item,
          finishSubMaterialDetail: {
            id: findFinishSubMaterialDetail.id,
            name: findFinishSubMaterialDetail.name,
            side: null,
            subMaterialId: findFinish.subMaterialId,
            subMaterialImageUrl: null,
          },
        };
      }
      return item;
    });
    dispatch(
      setEstimateDataSalesOrder({
        ...estimateDataSalesOrder,
        coatingRequest: {
          ...estimateDataSalesOrder.coatingRequest,
          [fieldName]: updatedCoatingArray,
        },
      })
    );
  };

  if (!isEmpty(coatingSide)) {
    return (
      <CoatingFormFormStyled
        as={motion.div}
        // layout
      >
        <FadeInStyled>
          <p className="topic">เคลือบ</p>
          <div className="content">
            {coatingSide.map((item: any) => {
              const disabled = isDisabled(item);
              return (
                <div
                  key={item.id}
                  className={`item-side ${disabled ? 'disabled' : ''} ${
                    estimateDataSalesOrder.coatingRequest?.coatingSideDimension
                      ?.id === item.id
                      ? 'active'
                      : ''
                  }`}
                  onClick={() => {
                    if (!disabled) handleClickCoatingSide(item);
                  }}
                >
                  {estimateDataSalesOrder.coatingRequest?.coatingSideDimension
                    ?.id === item.id && (
                    <div className="checked">
                      <CheckRoundedIcon
                        sx={{ fontSize: '14px', color: 'white' }}
                      />
                    </div>
                  )}
                  <Image
                    src={item.imageUrl}
                    width={300}
                    height={300}
                    alt={item.name}
                    className="side-image"
                    draggable={false}
                  />
                  <span className="name">{item.name}</span>
                </div>
              );
            })}
          </div>
        </FadeInStyled>
        {/* <AnimatePresence initial={false} mode={'wait'}> */}
        {(watch('coatingRequest.coatingSideDimensionId') === 32 ||
          watch('coatingRequest.coatingSideDimensionId') === 34) && (
          <motion.div
          /* {...motionFadeConfig} */
          >
            <div className="flex items-center justify-between h-[40px] my-[8px] mt-[16px]">
              <div className="label !m-0">ด้านหน้า</div>
              <Button
                variant="contained"
                color="dark"
                style={{
                  height: '32px',
                  width: '32px',
                  minWidth: '32px',
                }}
                onClick={() => {
                  handleClickAddCoating('front');
                }}
                disabled={watch('printingRequest.printSystemId') === ''}
              >
                <AddRoundedIcon />
              </Button>
            </div>

            <div className="coating-selector">
              {!isEmpty(watch('coatingRequest.finishFront')) ? (
                watch('coatingRequest.finishFront').map(
                  (colorFrontItem: any, index: number) => {
                    // ดึง error
                    const errorObj =
                      hookFormErrors?.coatingRequest?.finishFront?.[index];

                    return (
                      <div className="select-group" key={index}>
                        {/* Coating Master */}
                        <FormControl fullWidth>
                          <Select
                            displayEmpty
                            {...register(
                              `coatingRequest.finishFront[${index}].coatingMasterId`
                            )}
                            error={Boolean(errorObj?.coatingMasterId)}
                            value={colorFrontItem.coatingMasterId || ''}
                            onChange={(e: any) => {
                              handleChangeCoatingMaster(
                                'front',
                                index,
                                e.target.value
                              );
                            }}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {coatingConfig.productConfig?.map(
                              (item: any, idx: number) => (
                                <MenuItem key={idx} value={item.masterId}>
                                  {item.materialName}
                                </MenuItem>
                              )
                            )}
                          </Select>
                          {errorObj?.coatingMasterId && (
                            <FormHelperText error>
                              {errorObj.coatingMasterId.message}
                            </FormHelperText>
                          )}
                        </FormControl>

                        {/* Finish Sub Material Detail */}
                        <FormControl fullWidth>
                          <Select
                            displayEmpty
                            {...register(
                              `coatingRequest.finishFront[${index}].finishSubMaterialDetailId`
                            )}
                            error={Boolean(errorObj?.finishSubMaterialDetailId)}
                            value={
                              colorFrontItem.finishSubMaterialDetailId || ''
                            }
                            onChange={(e: any) => {
                              handleChangeFinishSubMaterialDetail(
                                'front',
                                index,
                                e.target.value
                              );
                            }}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>

                            {/* Group & SubMaterial Mapping */}
                            {finishList
                              .filter(
                                (finishGroup) =>
                                  finishGroup.masterId ===
                                  watchCoatingRequest.finishFront[index]
                                    ?.coatingMasterId
                              )
                              .flatMap((finishGroupFiltered, groupIndex) => [
                                <ListSubheader
                                  key={`subheader-${finishGroupFiltered.masterId}-${groupIndex}`}
                                >
                                  {finishGroupFiltered.name}
                                </ListSubheader>,
                                ...(finishGroupFiltered.subMaterialDetail?.map(
                                  (finishItem: any) => (
                                    <MenuItem
                                      key={`menuitem-${finishItem.id}`}
                                      value={finishItem.id}
                                    >
                                      {finishItem.name}
                                    </MenuItem>
                                  )
                                ) || []),
                              ])}
                          </Select>
                          {errorObj?.finishSubMaterialDetailId && (
                            <FormHelperText error>
                              {errorObj.finishSubMaterialDetailId.message}
                            </FormHelperText>
                          )}
                        </FormControl>
                        {/* Coating Order */}
                        <TextField
                          type="text"
                          placeholder="ลำดับการเคลือบ"
                          value={
                            watch('coatingRequest').finishFront[index]
                              ?.coatingPositionEnum ?? ''
                          }
                          InputProps={{
                            readOnly: true,
                            style: { textAlign: 'center' },
                          }}
                          inputProps={{
                            style: { textAlign: 'center' },
                          }}
                        />

                        {/* Delete Button */}
                        <DeleteButton
                          onClick={() => handleRemoveCoating('front', index)}
                        >
                          <Image
                            src="/icons/delete-white.svg"
                            width={24}
                            height={24}
                            alt=""
                          />
                        </DeleteButton>
                      </div>
                    );
                  }
                )
              ) : (
                <>
                  <motion.div
                    /* {...motionFadeConfig} */
                    key="noSide"
                    style={{
                      height: '40px',
                      width: '100%',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#78909C',
                      background: '#f5f7f8',
                      boxShadow: hookFormErrors.coatingRequest?.finishFront
                        ? '0 0 0 1px #E91E63'
                        : '0 0 0 1px #DBE2E5',
                    }}
                  >
                    <input
                      type="radio"
                      {...register(`coatingRequest.finishFront`)}
                      onChange={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      checked={false}
                      style={{
                        opacity: 0,
                        height: 0,
                        width: 0,
                      }}
                    />
                    ไม่มีรายการ
                  </motion.div>
                  {hookFormErrors.coatingRequest?.finishFront && (
                    <div
                      style={{
                        marginTop: '-12px',
                      }}
                    >
                      <FormHelperText error>
                        กรุณาเพิ่มเคลือบด้านหน้า
                      </FormHelperText>
                    </div>
                  )}
                </>
              )}
            </div>
          </motion.div>
        )}
        {(watch('coatingRequest.coatingSideDimensionId') === 33 ||
          watch('coatingRequest.coatingSideDimensionId') === 34) && (
          <motion.div
          /* {...motionFadeConfig} */
          >
            <div className="flex items-center justify-between h-[40px] my-[8px] mt-[16px]">
              <div className="label !m-0">ด้านหลัง</div>
              <Button
                variant="contained"
                color="dark"
                style={{
                  height: '32px',
                  width: '32px',
                  minWidth: '32px',
                }}
                onClick={() => {
                  handleClickAddCoating('back');
                }}
                disabled={watch('printingRequest.printSystemId') === ''}
              >
                <AddRoundedIcon />
              </Button>
            </div>
            <div className="coating-selector">
              {!isEmpty(watch('coatingRequest.finishBack')) ? (
                watch('coatingRequest.finishBack').map(
                  (finishBackItem: any, index: number) => (
                    <div className="select-group" key={index}>
                      <FormControl fullWidth>
                        <Select
                          displayEmpty
                          {...register(
                            `coatingRequest.finishBack[${index}].coatingMasterId`
                          )}
                          error={Boolean(
                            hookFormErrors.coatingRequest?.finishBack?.[index]
                              ?.coatingMasterId
                          )}
                          value={
                            watch('coatingRequest').finishBack[index]
                              ?.coatingMasterId || ''
                          }
                          onChange={(e: any) => {
                            handleChangeCoatingMaster(
                              'back',
                              index,
                              e.target.value
                            );
                          }}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {coatingConfig.productConfig?.map(
                            (item: any, idx: number) => (
                              <MenuItem key={idx} value={item.masterId}>
                                {item.materialName}
                              </MenuItem>
                            )
                          )}
                        </Select>
                        {hookFormErrors.coatingRequest?.finishBack?.[index]
                          ?.coatingMasterId && (
                          <FormHelperText error>
                            {
                              hookFormErrors.coatingRequest?.finishBack[index]
                                .coatingMasterId.message as React.ReactNode
                            }
                          </FormHelperText>
                        )}
                      </FormControl>

                      <FormControl fullWidth>
                        <Select
                          displayEmpty
                          {...register(
                            `coatingRequest.finishBack[${index}].finishSubMaterialDetailId`
                          )}
                          error={Boolean(
                            hookFormErrors.coatingRequest?.finishBack?.[index]
                              ?.finishSubMaterialDetailId
                          )}
                          value={
                            watch('coatingRequest').finishBack[index]
                              ?.finishSubMaterialDetailId || ''
                          }
                          onChange={(e: any) => {
                            handleChangeFinishSubMaterialDetail(
                              'back',
                              index,
                              e.target.value
                            );
                          }}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {finishList
                            .filter(
                              (finishGroup: any) =>
                                finishGroup.masterId ===
                                watchCoatingRequest.finishBack[index]
                                  .coatingMasterId
                            )
                            .flatMap(
                              (
                                finishGroupFiltered: any,
                                groupIndex: number
                              ) => [
                                <ListSubheader
                                  key={`subheader-${finishGroupFiltered.masterId}-${groupIndex}`}
                                >
                                  {finishGroupFiltered.name}
                                </ListSubheader>,
                                ...(finishGroupFiltered.subMaterialDetail?.map(
                                  (finishItem: any) => (
                                    <MenuItem
                                      key={`menuitem-${finishItem.id}`}
                                      value={finishItem.id}
                                    >
                                      {finishItem.name}
                                    </MenuItem>
                                  )
                                ) || []),
                              ]
                            )}
                        </Select>
                        {hookFormErrors.coatingRequest?.finishBack?.[index]
                          ?.finishSubMaterialDetailId && (
                          <FormHelperText error>
                            {
                              hookFormErrors.coatingRequest?.finishBack[index]
                                .finishSubMaterialDetailId
                                .message as React.ReactNode
                            }
                          </FormHelperText>
                        )}
                      </FormControl>
                      <TextField
                        type="text"
                        placeholder="ลำดับการเคลือบ"
                        value={
                          watch('coatingRequest').finishBack[index]
                            ?.coatingPositionEnum ?? ''
                        }
                        InputProps={{
                          readOnly: true,
                          style: { textAlign: 'center' },
                        }}
                        inputProps={{
                          style: { textAlign: 'center' },
                        }}
                      />
                      <DeleteButton
                        onClick={() => {
                          handleRemoveCoating('back', index);
                        }}
                      >
                        <Image
                          src="/icons/delete-white.svg"
                          width={24}
                          height={24}
                          alt=""
                        />
                      </DeleteButton>
                    </div>
                  )
                )
              ) : (
                <>
                  <motion.div
                    /* {...motionFadeConfig} */
                    key={'noSide'}
                    style={{
                      height: '40px',
                      width: '100%',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#78909C',
                      background: '#f5f7f8',
                      boxShadow: hookFormErrors.coatingRequest?.finishBack
                        ? '0 0 0 1px #E91E63'
                        : '0 0 0 1px #DBE2E5',
                    }}
                  >
                    <input
                      type="radio"
                      {...register(`coatingRequest.finishBack`)}
                      onChange={(e: any) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      checked={false}
                      style={{
                        opacity: 0,
                        height: 0,
                        width: 0,
                      }}
                    />
                    ไม่มีรายการ
                  </motion.div>
                  {hookFormErrors.coatingRequest?.finishBack && (
                    <div
                      style={{
                        marginTop: '-12px',
                      }}
                    >
                      <FormHelperText error>
                        กรุณาเพิ่มเคลือบด้านหลัง
                      </FormHelperText>
                    </div>
                  )}
                </>
              )}
            </div>
          </motion.div>
        )}
        {/* </AnimatePresence> */}
      </CoatingFormFormStyled>
    );
  }
  return null;
};

export default CoatingForm;
