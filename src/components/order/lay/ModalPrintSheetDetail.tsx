import React from 'react';
import { Dialog, DialogContent, IconButton } from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import styled from 'styled-components';
import Image from 'next/image';
import { isNull } from 'lodash';

const ModalPrintSheetDetailStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  padding: 24px 0 4px;
  .card {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    max-width: 100%;
    .card-header {
      padding: 24px;
      border-bottom: 1px solid #dbe2e5;
      height: 100%;
      .profile {
        display: flex;
        align-items: center;
        column-gap: 12px;
        .image {
          border-radius: 50%;
          overflow: hidden;
          height: 40px;
          width: 40px;
          min-width: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 6px;
          overflow: hidden;
          .name {
            font-weight: 600;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
          }
          .role {
            white-space: nowrap;
            margin-top: -6px;
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
          }
        }
      }
    }
    .detail-wrap {
      display: flex;
      width: 100%;
      .detail {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        padding: 18px 24px;
        row-gap: 2px;
        overflow: hidden;
        @media screen and (max-width: 450px) {
          * {
            text-align: center;
          }
        }
        .title {
          display: flex;
          align-items: center;
          column-gap: 8px;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: 600;
          white-space: nowrap;
          @media screen and (max-width: 450px) {
            flex-direction: column;
            row-gap: 4px;
          }
        }
        .value {
          font-size: 14px;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &:nth-child(2) {
          border-left: 1px solid #dbe2e5;
          border-right: 1px solid #dbe2e5;
        }
      }
    }
  }
  .image-wrap {
    display: flex;
    width: 100%;
    gap: 24px;
    margin-top: 12px;
    height: 100%;
    .group {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      height: 100%;
      .topic {
        font-size: 14px !important;
      }
      .image {
        height: 100%;
        position: relative;
        border-radius: 16px;
        overflow: hidden;
        border: 2px dashed #dbe2e5;
        aspect-ratio: 16/9;
        z-index: 0;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
`;
type Props = {
  data: any;
  onClose: () => void;
};
const ModalPrintSheetDetail = ({ data, onClose }: Props) => {
  const dataForRender = data?.data;
  // console.log(data);
  // console.log('dataForRender', dataForRender);
  return (
    <Dialog
      open={data.open}
      onClose={() => {
        onClose();
      }}
    >
      <DialogContent>
        <FormModalStyle $width={1150}>
          <div className="content-wrap">
            <div className="header">
              <div
                className="title"
                style={{
                  transform: 'translateX(50%)',
                  position: 'absolute',
                  margin: '0',
                  right: '50%',
                }}
              >
                {dataForRender.name}
              </div>
              <div className="x-close" onClick={() => onClose()}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form
                style={{
                  rowGap: '0',
                }}
              >
                <ModalPrintSheetDetailStyle>
                  <div className="card">
                    <div className="card-header">
                      <div className="profile">
                        <div className="image">
                          <Image
                            src={
                              dataForRender.subMaterialDetail.imageUrl ||
                              '/images/product/empty-product.svg'
                            }
                            width={80}
                            height={80}
                            alt=""
                          />
                        </div>
                        <div className="text-group">
                          <div className="name">
                            {dataForRender.subMaterialDetail.name}
                          </div>
                          <div className="role">
                            ใบเต็ม {dataForRender.itemSize.itemSizeName},
                            ใบพิมพ์ {dataForRender.subItemSize.subItemName}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="detail-wrap">
                      <div className="detail">
                        <div className="title">การพิมพ์</div>
                        <div className="value uppercase">
                          <div>
                            ระบบพิมพ์ :{' '}
                            {dataForRender.printPlateLayData[0].layData
                              .printMaster?.name || '-'}
                          </div>
                          <div>
                            เครื่องพิมพ์ :{' '}
                            {dataForRender.machineComponent?.name || '-'}
                          </div>
                        </div>
                      </div>
                      <div className="detail">
                        <div className="title">ตั้งค่าใบพิมพ์</div>
                        <div className="value">
                          <div>
                            เพลท : {dataForRender.plateRawMaterial?.name || '-'}
                          </div>
                          <div>
                            บล็อกไดคัท :{' '}
                            {dataForRender.dieCutRawMaterial?.name ||
                              'ไม่ใช้บล็อกไดคัท'}
                          </div>
                        </div>
                      </div>
                      <div className="detail">
                        <div className="title">ระยะห่างเลย์</div>
                        <div className="value">
                          <div>
                            ระหว่างเลย์ : {dataForRender.laySpace} {'mm'}
                          </div>
                          <div>
                            {' '}
                            Margin : ซ้าย {dataForRender.left} ขวา{' '}
                            {dataForRender.right} บน {dataForRender.top}{' '}
                            กริปเปอร์ {dataForRender.griper} {'mm'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="image-wrap">
                    <div className="group">
                      <div className="topic">ด้านหน้า</div>
                      <div
                        className={`image ${
                          dataForRender.layoutFrontUrl
                            ? '!border-0 bg-[#dbe2e5]'
                            : ''
                        }`}
                      >
                        {!isNull(dataForRender.layoutFrontUrl) && (
                          <Image
                            src={dataForRender.layoutFrontUrl}
                            alt=""
                            fill
                          />
                        )}
                      </div>
                    </div>
                    <div className="group">
                      <div className="topic">ด้านหลัง</div>
                      <div
                        className={`image ${
                          dataForRender.layoutBackUrl
                            ? '!border-0 bg-[#dbe2e5]'
                            : ''
                        }`}
                      >
                        {!isNull(dataForRender.layoutBackUrl) && (
                          <Image
                            src={dataForRender.layoutBackUrl}
                            alt=""
                            fill
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </ModalPrintSheetDetailStyle>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalPrintSheetDetail;
