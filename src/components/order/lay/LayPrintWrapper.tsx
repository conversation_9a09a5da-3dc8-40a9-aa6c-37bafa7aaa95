import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { LoadingFadein } from '@/styles/share.styled';
import apiPrintPlate from '@/services/order/printPlate';
import { isEmpty, isNull } from 'lodash';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import SideDetail from '@/components/SideDetail';
import { Button, IconButton } from '@mui/material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import LayDataSideDetailContent from '@/components/order/spec/LayDataSideDetailContent';
import ModalPrintSheetDetail from '@/components/order/lay/ModalPrintSheetDetail';
import AccessTimeRoundedIcon from '@mui/icons-material/AccessTimeRounded';
import { numberWithCommas } from '@/utils/number';
import TaskAltRoundedIcon from '@mui/icons-material/TaskAltRounded';
import ActionButton from '@/components/ActionButton';

const LaySpecWrapperStyled = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 4px;
  animation: ${LoadingFadein} 0.3s ease-in;
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  .ld-list-header {
    height: 72px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    gap: 24px;
    .left-side {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 16px;
      overflow: hidden;
      max-width: 100%;
      .image {
        width: 40px;
        min-width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
      }
      * {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ld-code {
        font-size: 16px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
      }
      .vertical {
        color: #dbe2e5;
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      gap: 16px;
      .spec-status {
        display: flex;
        align-items: center;
        column-gap: 4px;
        font-size: 14px;
        height: 40px;
        justify-content: center;
        border-radius: 20px;
        padding: 0 16px 0 12px;
        overflow: hidden;
        &.pending {
          color: #fbc02d;
          background: #fff9e6;
        }
        &.done {
          color: #8bc34a;
          background: #f1fce3;
        }
        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
      }
      .MuiButtonBase-root {
        white-space: nowrap;
      }
    }
  }
  .spec-zone {
    width: 100%;
    display: flex;
    padding: 16px;
    column-gap: 24px;
    .spec-item {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      overflow: hidden;
      .topic {
        font-weight: 600;
      }
      .spec-group {
        display: flex;
        flex-direction: column;
        > div {
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .material {
        display: flex;
        column-gap: 8px;
        > div {
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .image {
          width: 40px;
          height: 40px;
          min-width: 40px;
          border-radius: 50%;
          overflow: hidden;
        }
      }
    }
  }
`;
type LayPrintWrapperProp = {
  orderById: any;
  makeCreatePrintSheetData: (createPrintSheetData: any) => void;
  handleClickEditPrintPlate: (data: any) => void;
  reloadOrder: () => void;
  printPlateById: any;
};

const LayPrintWrapper = ({
  orderById,
  makeCreatePrintSheetData,
  handleClickEditPrintPlate,
  reloadOrder,
  printPlateById,
}: LayPrintWrapperProp) => {
  const [deletePrintPlate, setDeletePrintPlate] = useState<any>({
    open: false,
  });
  const [submittingDelete, setSubmittingDelete] = useState<boolean>(false);
  const [sideDetailLayData, setSideDetailLayData] = useState<any>({
    open: false,
    data: {},
  });
  const [modalPrintSheetDetail, setModalPrintSheetDetail] = useState<any>({
    open: false,
    data: {},
  });

  const handleDeletePrintPlate = async () => {
    setSubmittingDelete(true);
    const res = await apiPrintPlate.deletePrintPlate(deletePrintPlate.id);
    if (!res.isError) {
      setDeletePrintPlate({
        ...deletePrintPlate,
        open: false,
      });
      reloadOrder();
    }
    setSubmittingDelete(false);
  };

  const handleClickViewLayDataDetail = (ld: any) => {
    if (ld !== null) {
      const findPrintPlateLayData = printPlateById.find(
        (item: any) => item.id === ld.printPlateId
      ).printPlateLayData[0];
      const additionalData = {
        ...findPrintPlateLayData,
        dieCutSubMaterialDetailIdName: ld.dieCutSubMaterialDetailIdName || '',
        plateRawMaterialName: ld.plateRawMaterialName || '',
      };
      if (findPrintPlateLayData) {
        setSideDetailLayData({
          open: true,
          data: additionalData,
        });
      }
    }
  };

  const getDefaultQuantity = (layDataQuantity: any) => {
    const layDataDefaultQuantity = layDataQuantity.find(
      (quantityItem: any) => quantityItem.isDefault
    );
    if (layDataDefaultQuantity) {
      return numberWithCommas(layDataDefaultQuantity.quantity, 2);
    }
    return '-';
  };
  const getAmountExtra = (extra: any, side: string) => {
    if (extra) {
      const amountExtra = extra.filter(
        (extraItem: any) => extraItem.sideName === side
      ).length;
      if (amountExtra) {
        return `${numberWithCommas(amountExtra, 2)} รายการ`;
      }
    }
    return '-';
  };
  // console.log('orderById', orderById);
  // console.log('printPlateByOrderId', printPlateByOrderId);
  const handleCreatePrintSheet = async (ldIds: number[]) => {
    const selectedLayData = ldIds
      .map((id: number) =>
        orderById.layData.filter((latData: any) => latData.id === id)
      )
      .flat();
    const mergedCreatePrintSheetData = {
      ...orderById,
      layDataPrintSheet: selectedLayData,
    };
    delete mergedCreatePrintSheetData.layData;
    makeCreatePrintSheetData(mergedCreatePrintSheetData);
  };

  // console.log('orderById', orderById);
  return (
    <>
      {!isEmpty(modalPrintSheetDetail.data) && (
        <ModalPrintSheetDetail
          data={modalPrintSheetDetail}
          onClose={() => {
            setModalPrintSheetDetail({
              open: false,
              data: modalPrintSheetDetail.data,
            });
          }}
        />
      )}
      <SideDetail
        isOpen={sideDetailLayData.open}
        handleClickOutSide={() => {
          setSideDetailLayData({
            ...sideDetailLayData,
            open: false,
          });
        }}
      >
        {!isEmpty(sideDetailLayData.data) && (
          <>
            <div className="header">
              <div className="topic">สเปคสินค้า</div>
              <div
                className="x-close"
                onClick={() => {
                  setSideDetailLayData({
                    ...sideDetailLayData,
                    open: false,
                  });
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="content"
              style={{
                maxHeight: 'initial !important',
              }}
            >
              <div className="body">
                <LayDataSideDetailContent
                  layoutDataDetail={sideDetailLayData.data}
                />
              </div>
            </div>
          </>
        )}
      </SideDetail>
      <AppModalConfirm
        open={deletePrintPlate.open}
        isReason={false}
        onClickClose={() => {
          setDeletePrintPlate({
            ...deletePrintPlate,
            open: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยกเลิกรายการ
          </div>
        }
        confirmDescription={`คุณต้องการลบใบพิมพ์ “${deletePrintPlate.name}”`}
        loadingConfirm={submittingDelete}
        onConfirm={async () => {
          await handleDeletePrintPlate();
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      {orderById.layData.map((ldItem: any, index: number) => {
        console.log('ldItem', ldItem);
        return (
          <LaySpecWrapperStyled key={index}>
            <div className="ld-list-header">
              <div className="left-side">
                <Image
                  src={
                    ldItem.productModel.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={80}
                  height={80}
                  alt=""
                  className="image"
                />
                <div className="ld-code">{ldItem.ldCode}</div>
                <div className="vertical">|</div>
                <div>{ldItem.productModel.productModelName}</div>
                <div className="vertical">|</div>
                <div>
                  ขนาดสินค้า {ldItem.length} x {ldItem.width} x {ldItem.height}{' '}
                  mm.
                </div>
                <div className="vertical">|</div>
                <div>
                  จำนวนผลิต {getDefaultQuantity(ldItem.layDataQuantity)} ชิ้น
                </div>
              </div>
              <div className="right-side">
                {ldItem.printPlateId === null ? (
                  <div className="spec-status pending">
                    <AccessTimeRoundedIcon />
                    <span>ยังไม่ได้ระบุสเปคสเปคการผลิต</span>
                  </div>
                ) : (
                  <div className="spec-status done">
                    <TaskAltRoundedIcon />
                    <span>ปรับแต่งสำเร็จ</span>
                  </div>
                )}
                {orderById.status === 1 && (
                  <div
                    onClick={async () => {
                      if (isNull(ldItem.printPlateId)) {
                        await handleCreatePrintSheet([ldItem.id]);
                      } else {
                        const findPrintPlateLayData = printPlateById.find(
                          (item: any) => item.id === ldItem.printPlateId
                        );
                        handleClickEditPrintPlate(findPrintPlateLayData);
                      }
                    }}
                  >
                    <ActionButton
                      variant="outlined"
                      color="blueGrey"
                      icon={
                        <Image
                          src={'/icons/icon-edit-document.svg'}
                          width={24}
                          height={24}
                          alt=""
                        />
                      }
                      text="Config"
                      borderRadius="8px"
                    />
                  </div>
                )}
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  disabled={isNull(ldItem.printPlateId)}
                  onClick={() => {
                    handleClickViewLayDataDetail(ldItem);
                  }}
                >
                  สเปคการผลิต
                </Button>
              </div>
            </div>
            <div className="spec-zone">
              <div className="spec-item">
                <div className="topic">Material</div>
                <div className="material">
                  <Image
                    src={
                      ldItem.subMaterialDetail.subMaterialImageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    width={80}
                    height={80}
                    alt=""
                    className="image"
                  />
                  <div className="spec-group">
                    <div>{ldItem.subMaterialDetail.name}</div>
                    <div>ใบพิมพ์: {ldItem.printPlateName || '-'}</div>
                  </div>
                </div>
              </div>
              <div className="spec-item">
                <div className="topic">การเคลือบ</div>
                <div className="spec-group">
                  <div>
                    หน้า :{' '}
                    {ldItem.coatingRequest.finishFront.length > 0
                      ? ldItem.coatingRequest.finishFront.map(
                          (finishItem: any, index: number) => (
                            <span key={index}>
                              {/* {finishItem.coatingMaster.name}{' '} */}
                              {finishItem.finishSubMaterialDetail.name}
                              {index <
                              ldItem.coatingRequest.finishFront.length - 1
                                ? ', '
                                : ''}
                            </span>
                          )
                        )
                      : '-'}
                  </div>
                  <div>
                    หลัง :{' '}
                    {ldItem.coatingRequest.finishBack.length > 0
                      ? ldItem.coatingRequest.finishBack.map(
                          (finishItem: any, index: number) => (
                            <span key={index}>
                              {/* {finishItem.coatingMaster.name}{' '} */}
                              {finishItem.finishSubMaterialDetail.name}
                              {index <
                              ldItem.coatingRequest.finishBack.length - 1
                                ? ', '
                                : ''}
                            </span>
                          )
                        )
                      : '-'}
                  </div>
                </div>
              </div>
              <div className="spec-item">
                <div className="topic">
                  การพิมพ์{' '}
                  {ldItem.printingRequest.printSystem?.name || 'ไม่พิมพ์'}
                </div>
                <div className="spec-group">
                  <div>
                    หนัา :{' '}
                    {ldItem.printingRequest.colorFront.map(
                      (colorItem: any, index: number) => {
                        return (
                          <span key={index}>
                            {colorItem.printColor.name}, {colorItem.color.name},{' '}
                            {colorItem.printAreaDimension.name}
                          </span>
                        );
                      }
                    )}
                    {isEmpty(ldItem.printingRequest.colorFront) && '-'}
                  </div>
                  <div>
                    หลัง :{' '}
                    {ldItem.printingRequest.colorBack.map(
                      (colorItem: any, index: number) => {
                        return (
                          <span key={index}>
                            {colorItem.printColor.name}, {colorItem.color.name},{' '}
                            {colorItem.printAreaDimension.name}
                          </span>
                        );
                      }
                    )}
                    {isEmpty(ldItem.printingRequest.colorBack) && '-'}
                  </div>
                </div>
              </div>
              <div className="spec-item">
                <div className="topic">เทคนิคพิเศษ</div>
                <div className="spec-group">
                  <div>หน้า : {getAmountExtra(ldItem.extra, 'FRONT')}</div>
                  <div>หลัง : {getAmountExtra(ldItem.extra, 'BACK')}</div>
                </div>
              </div>
              <div className="spec-item">
                <div className="topic">แม่พิมพ์</div>
                <div className="spec-group">
                  <div>เพลต : {ldItem.plateRawMaterialName || '-'}</div>
                  <div>
                    Die Cut : {ldItem.dieCutSubMaterialDetailIdName || '-'}
                  </div>
                </div>
              </div>
            </div>
            {/* {!isEmpty(printPlateByOrderId) && */}
            {/*  printPlateByOrderId.map((item: any, index: number) => { */}
            {/*    return ( */}
            {/*      <PrintPlateList */}
            {/*        key={index} */}
            {/*        data={item} */}
            {/*        handleClickEditPrintPlate={handleClickEditPrintPlate} */}
            {/*        makeRemovePrintPlate={makeRemovePrintPlate} */}
            {/*        handleClickViewDetail={handleClickViewDetail} */}
            {/*        handleClickViewLayDataDetail={handleClickViewLayDataDetail} */}
            {/*        orderById={orderById} */}
            {/*      /> */}
            {/*    ); */}
            {/*  })} */}
          </LaySpecWrapperStyled>
        );
      })}
    </>
  );
};
export default LayPrintWrapper;
