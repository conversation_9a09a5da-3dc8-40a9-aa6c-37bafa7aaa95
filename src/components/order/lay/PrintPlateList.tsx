import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { Button } from '@mui/material';
import { isEmpty } from 'lodash';
import TaskAltRoundedIcon from '@mui/icons-material/TaskAltRounded';
import apiPrintPlate from '@/services/order/printPlate';

const PrintPlateListStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  overflow: hidden;
  .print-plate-header {
    width: inherit;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 24px;
    flex-wrap: wrap;
    min-height: 40px;
    border-bottom: 1px solid #dbe2e5;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .action-wrap {
      display: flex;
      column-gap: 8px;
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.3s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
  }
  .detail-wrap {
    display: flex;
    width: 100%;
    overflow: auto;
    border-bottom: 1px solid #dbe2e5;
    .detail {
      flex: 1 1 0%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      font-size: 12px;
      border-right: 1px solid #dbe2e5;
      min-width: 200px;
      .detail-content {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
      }
      &:last-child {
        border: 0;
      }
      .topic {
        font-weight: 600;
        margin-bottom: 4px;
      }
    }
  }
  .lay-data-list {
    width: inherit;
    background: #f5f7f8;
    display: flex;
    flex-direction: column;
    .list {
      width: inherit;
      display: flex;
      align-items: center;
      gap: 24px;
      justify-content: space-between;
      overflow: auto;
      padding: 16px;
      border-bottom: 1px solid #dbe2e5;
      background: white;
      &:last-child {
        border: none;
      }
      .lay-data {
        display: flex;
        align-items: center;
        column-gap: 16px;
        min-width: 260px;
        .image {
          width: 48px;
          height: 48px;
          min-width: 48px;
          border-radius: 4px;
          overflow: hidden;
          position: relative;
        }
        .text-group {
          display: flex;
          flex-direction: column;
          .ld-code {
            font-size: 16px;
            font-weight: 600;
          }
          .info-text {
            text-decoration: underline;
          }
        }
      }
      .detail {
        font-size: 12px;
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        min-width: 188px;
        .topic {
          font-weight: 600;
        }
        .chip-wrap {
          width: 100%;
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          .chip {
            min-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px 8px;
            border: 1px solid #263238;
            border-radius: 12px;
          }
        }
      }
      .print-image {
        width: 100px;
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        align-items: center;
        .image-wrap {
          display: flex;
          column-gap: 8px;
          width: 100%;
          .image {
            flex: 1 1 0%;
            aspect-ratio: 1/1;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dbe2e5;
            padding: 8px;
            position: relative;
            border-radius: 4px;
            overflow: hidden;
            img {
              object-fit: cover;
            }
          }
        }
        .status {
          min-height: 24px;
          padding: 0 12px 0 8px;
          background: #f1fce3;
          color: #8bc34a;
          display: flex;
          align-items: center;
          column-gap: 4px;
          font-size: 12px;
          white-space: nowrap;
          border-radius: 16px;
          svg {
            font-size: 16px;
          }
        }
      }
    }
  }
`;
type Props = {
  data: any;
  handleClickEditPrintPlate: (data: any) => void;
  makeRemovePrintPlate: (data: any) => void;
  handleClickViewDetail: (data: any) => void;
  handleClickViewLayDataDetail: (data: any) => void;
  orderById: any;
};
const PrintPlateList = ({
  data,
  handleClickEditPrintPlate,
  makeRemovePrintPlate,
  handleClickViewDetail,
  handleClickViewLayDataDetail,
  orderById,
}: Props) => {
  const getPrintPlateById = async (id: number, action: string) => {
    const res = await apiPrintPlate.getPrintPlateById(id);
    if (!res.isError) {
      if (action === 'edit') {
        handleClickEditPrintPlate(res.data);
      } else if (action === 'view') {
        handleClickViewDetail(res.data);
      }
    }
  };
  return (
    <PrintPlateListStyle>
      <div className="print-plate-header">
        <div className="title">{data.name}</div>
        <div className="action-wrap">
          <Button
            type="button"
            variant="outlined"
            color="blueGrey"
            sx={{
              minHeight: '40px',
              maxHeight: '40px',
              fontWeight: '400',
            }}
            onClick={async () => {
              await getPrintPlateById(data.id, 'view');
            }}
          >
            ข้อมูลใบพิมพ์
          </Button>
          {orderById.status === 1 && (
            <>
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                onClick={async () => {
                  await getPrintPlateById(data.id, 'edit');
                }}
                sx={{
                  minWidth: '40px',
                  maxWidth: '40px',
                  minHeight: '40px',
                  maxHeight: '40px',
                }}
              >
                <Image
                  src={'/icons/edit-black.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </Button>
              <div
                className="remove-btn"
                onClick={() => {
                  makeRemovePrintPlate(data);
                }}
              >
                <Image
                  src="/icons/delete-white.svg"
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
            </>
          )}
        </div>
      </div>
      <div className="detail-wrap">
        <div className="detail">
          <div className="topic">วัสดุ</div>
          <div className="detail-content">{data.subMaterialDetail.name}</div>
        </div>
        <div className="detail">
          <div className="topic">ขนาด</div>
          <div className="detail-content">
            <div>ใบเต็ม : {data.itemSize.itemSizeName}</div>
            <div>ใบพิมพ์ : {data.subItemSize.subItemName}</div>
          </div>
        </div>
        {data.machine !== null && (
          <div className="detail">
            <div className="topic">การพิมพ์</div>
            <div className="detail-content">
              <div className="uppercase">
                ระบบพิมพ์ :{' '}
                {data.printPlateLayData[0].layData.printMaster?.name || '-'}
              </div>
              <div>เครื่องพิมพ์ : {data.machineComponent?.name || '-'}</div>
            </div>
          </div>
        )}
        <div className="detail">
          <div className="topic">เพลท/บล็อก</div>
          <div className="detail-content">
            {data.machine !== null && (
              <div>
                เพลท : {data.plateRawMaterial?.name || '-'}
                {/* {' • '} */}
                {/* {data.rawMaterialPlate.itemSizeName} */}
              </div>
            )}
            <div>
              บล็อกไดคัท : {data.dieCutRawMaterial?.name || 'ไม่ใช้บล็อกไดคัท'}
            </div>
          </div>
        </div>
        <div className="detail">
          <div className="topic">ระยะห่างเลย์</div>
          <div className="detail-content">
            <div>ระหว่างเลย์ : {data.laySpace} mm.</div>
            <div>
              Margin : ซ้าย {data.left} ขวา {data.right} บน {data.top} กริปเปอร์{' '}
              {data.griper}
            </div>
          </div>
        </div>
      </div>
      <div className="lay-data-list">
        {data.printPlateLayData.map((item: any, index: number) => {
          return (
            <div key={index} className="list">
              <div className="lay-data">
                <div className="image">
                  <Image
                    src={
                      item.layData.productModel.imageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    alt=""
                    fill
                  />
                </div>
                <div className="text-group">
                  <div className="ld-code">{item.layData.ldCode}</div>
                  <div
                    className="info-text cursor-pointer"
                    onClick={() => {
                      handleClickViewLayDataDetail(item);
                    }}
                  >
                    รายละเอียด
                  </div>
                </div>
              </div>
              <div className="detail">
                <div className="topic">ขนาดสินค้า</div>
                <div>
                  {item.layData.width} x {item.layData.height} x{' '}
                  {item.layData.length} mm.
                </div>
              </div>
              <div className="detail">
                <div className="topic">ขนาดกางออก</div>
                <div>
                  {item.dimensionWidth} x {item.dimensionHeight} mm.
                </div>
              </div>
              <div className="detail">
                <div className="topic">
                  {`${item.layData.productModel.productModelName}` ||
                    'ไม่พบต้นแบบโมเดล'}
                </div>
                <div className="chip-wrap">
                  {item.modelCustomObject.map((mcObj: any, index: number) => {
                    return (
                      <div
                        className="chip"
                        key={index}
                      >{`${mcObj.key} • ${mcObj.value}`}</div>
                    );
                  })}
                </div>
              </div>
              <div className="detail">
                <div className="topic">จำนวน/ใบพิมพ์</div>
                <div>{item.amount}</div>
              </div>
              <div className="print-image">
                <div className="image-wrap">
                  <div className="image">
                    {!isEmpty(item.dimensionFrontUrl) && (
                      <Image src={item.dimensionFrontUrl} alt="" fill />
                    )}
                  </div>
                  <div className="image">
                    {!isEmpty(item.dimensionBackUrl) && (
                      <Image src={item.dimensionBackUrl} alt="" fill />
                    )}
                  </div>
                </div>
                <div className="status">
                  <TaskAltRoundedIcon />
                  ตั้งค่าสำเร็จ
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </PrintPlateListStyle>
  );
};

export default PrintPlateList;
