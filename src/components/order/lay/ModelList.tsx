import React, { ReactNode, useEffect, useState } from 'react';
import Image from 'next/image';
import AccessTimeRoundedIcon from '@mui/icons-material/AccessTimeRounded';
import ActionButton from '@/components/ActionButton';
import { isEmpty } from 'lodash';
import TaskAltRoundedIcon from '@mui/icons-material/TaskAltRounded';
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormHelperText,
  IconButton,
  Menu,
  MenuItem,
  TextField,
} from '@mui/material';
import { FadeInStyled, FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import apiModelCustomObject from '@/services/order/modelCustomObject';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import UploadIcon from '@mui/icons-material/Upload';
import { numberWithCommas } from '@/utils/number';
import apiPrintPlate from '@/services/order/printPlate';
import styled, { css } from 'styled-components';

const ModelListStyle = styled.div<{ $isErrorModelCustomObject: boolean }>`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  padding: 0 24px;
  .title {
    font-size: 20px;
    font-weight: 600;
  }
  .list {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.08);
    display: flex;
    flex-direction: column;
    overflow: auto;
    ${({ $isErrorModelCustomObject }) =>
      $isErrorModelCustomObject
        ? css`
            border: 1px solid #e81621;
          `
        : css`
            border: 1px solid #dbe2e5;
          `}
    .setting-model-btn-wrap {
      ${({ $isErrorModelCustomObject }) =>
        $isErrorModelCustomObject &&
        css`
          button {
            border: 1px solid #e81621 !important;
          }
        `}
    }
    .list-header {
      display: flex;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #dbe2e5;
      flex-wrap: wrap;
      gap: 16px;
      .left {
        display: flex;
        align-items: center;
        column-gap: 12px;
        .image {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          overflow: hidden;
          position: relative;
        }
        .ld-code {
          font-size: 16px;
          font-weight: 600;
        }
        .model-status {
          display: flex;
          align-items: center;
          column-gap: 4px;
          font-size: 14px;
          height: 40px;
          justify-content: center;
          border-radius: 20px;
          padding: 0 16px 0 12px;
          &.pending {
            color: #fbc02d;
            background: #fff9e6;
          }
          &.done {
            color: #8bc34a;
            background: #f1fce3;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        column-gap: 12px;
        .remove-btn {
          height: 40px;
          width: 40px;
          border-radius: 6px;
          cursor: pointer;
          transition: 0.3s ease-out;
          background: #d32f2f;
          display: flex;
          align-items: center;
          justify-content: center;
          &:hover {
            filter: brightness(0.9);
          }
        }
      }
    }
    .detail-wrap {
      display: flex;
      width: 100%;
      overflow: auto;
      .detail {
        min-width: 200px;
        flex: 1 1 0%;
        padding: 16px;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        border-right: 1px solid #dbe2e5;
        .detail-content {
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }
        .chip-wrap {
          width: 100%;
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          .chip {
            min-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px 8px;
            border: 1px solid #263238;
            border-radius: 12px;
          }
        }
        &:last-child {
          border: 0;
        }
        .topic {
          font-weight: 600;
          margin-bottom: 4px;
        }
      }
    }
  }
`;
const SettingModelStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  margin-top: 24px;
  .MuiFormHelperText-root {
    margin-top: 4px !important;
  }
  .setting-model-header {
    width: inherit;
    display: flex;
    align-items: center;
    column-gap: 16px;
    .image {
      height: 80px;
      width: 80px;
      min-width: 80px;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
    }
    .text-zone {
      display: flex;
      flex-direction: column;
      font-size: 14px;
      .ld-code {
        font-size: 20px;
        font-weight: 600;
      }
      .size {
        font-weight: 400;
      }
    }
  }
  .model-zone {
    width: inherit;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    .model-title-wrap {
      width: inherit;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .model-title {
        font-size: 20px;
        font-weight: 600;
      }
    }
    .custom-model-object-form {
      width: inherit;
      display: grid;
      grid-gap: 16px;
      position: relative;
      grid-template-columns: repeat(auto-fill, minmax(128px, 1fr));
      p {
        margin-top: 0;
      }
    }
  }
  .image-zone {
    width: inherit;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    display: none;
    .image-topic {
      font-weight: 600;
      margin-top: 8px;
    }
    .image-wrap {
      width: inherit;
      display: flex;
      column-gap: 16px;
      .group {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        .side {
          font-size: 12px;
        }
        .image {
          width: 100%;
          aspect-ratio: 1/1;
          overflow: hidden;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          position: relative;
          border: 2px dashed #dbe2e5;
          background: #f5f7f8;
          cursor: pointer;
          &:hover {
            .backdrop {
              opacity: 1;
              visibility: visible;
              transition: 0.15s ease-out;
            }
          }
          .image-bg {
            width: 100%;
            height: 100%;
            background: #f5f7f8;
            z-index: 1;
            position: absolute;
          }
          img {
            object-fit: cover;
            z-index: 2;
          }
          .backdrop {
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 5;
            visibility: hidden;
            opacity: 0;
            background: rgba(38, 50, 56, 0.4);
            cursor: default;
            .zoom {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              cursor: pointer;
              svg {
                color: white;
                font-size: 32px;
              }
            }
            .remove-btn {
              height: 40px;
              width: 40px;
              border-radius: 6px;
              cursor: pointer;
              transition: 0.15s ease-out;
              background: #d32f2f;
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              top: 8px;
              right: 8px;
              &:hover {
                filter: brightness(0.9);
              }
            }
          }
        }
      }
    }
  }
  .dimen-start-adornment {
    border-right: 1px solid #dbe2e5;
    height: 100%;
    width: 32px;
    display: flex;
    align-items: center;
    font-weight: 700;
  }
`;

type Props = {
  layDataPrintSheet: any;
  handleUpdateCreatePrintSheetData: (data: any) => void;
  isErrorModelCustomObject: boolean;
  handleClearIsErrorModelCustomObject: () => void;
};
type CustomObjType = {
  id: number;
  key: string;
};
const ModelList = ({
  layDataPrintSheet,
  handleUpdateCreatePrintSheetData,
  isErrorModelCustomObject,
  handleClearIsErrorModelCustomObject,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [additionalModelCustomObject, setAdditionalModelCustomObject] =
    useState<any>([]);
  const [dataSettingModel, setDataSettingModel] = useState<any>({});
  const [anchorEl, setAnchorEl] = useState(null);
  const [modelCustomObjList, setModelCustomObjList] = useState<any>([]);
  const [selectedCustomObj, setSelectedCustomObj] = useState<CustomObjType[]>(
    []
  );
  const [uploadingDimension, setUploadingDimension] = useState<any>({
    front: false,
    back: false,
  });
  const [errorImageUpload, setErrorImageUpload] = useState<string>('');
  const validationSchema = yup.object().shape({
    ...selectedCustomObj.reduce((schema: any, obj) => {
      schema[obj.key] = yup
        .number()
        .typeError(`กรุณากรอก ${obj.key}`)
        .required(`กรุณากรอก ${obj.key}`);
      return schema;
    }, {}),
    dimensionWidth: yup
      .number()
      .typeError('กรุณากรอกความกว้าง')
      .required('กรุณากรอกความกว้าง'),
    dimensionHeight: yup
      .number()
      .typeError('กรุณากรอกความสูง')
      .required('กรุณากรอกความสูง'),
    // amount: yup.number().typeError('กรุณากรอกจำนวน').required('กรุณากรอกจำนวน'),
  });
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm<Record<string, any>>({
    resolver: yupResolver(validationSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (!isEmpty(layDataPrintSheet)) {
      const additional = layDataPrintSheet.map((item: any, index: number) => {
        return {
          ...item,
          isDefault: index === 0,
          printPlateLayData: {
            layDataId: item.printPlateLayData?.layDataId || item.id,
            dimensionWidth: item.printPlateLayData?.dimensionWidth || '',
            dimensionHeight: item.printPlateLayData?.dimensionHeight || '',
            dimensionFrontUrl:
              item.printPlateLayData?.dimensionFrontUrl || null,
            dimensionBackUrl: item.printPlateLayData?.dimensionBackUrl || null,
            masterUnitSizeId: item.productModel.masterUnitSizeId,
            amount: item.printPlateLayData?.amount || '',
            modelCustomObject: item.printPlateLayData?.modelCustomObject || [],
            isDefault: index === 0,
          },
        };
      });
      setAdditionalModelCustomObject(additional);
    }
  }, [layDataPrintSheet]);
  const handleRemoveLayData = (removeLayDataId: number) => {
    const removeLayData = additionalModelCustomObject.filter(
      (item: any) => item.id !== removeLayDataId
    );
    setAdditionalModelCustomObject(removeLayData);
  };
  const onClose = () => {
    setOpen(false);
  };
  const onOpen = async (layDataId: number) => {
    const findDataSettingModel = await additionalModelCustomObject.find(
      (item: any) => item.id === layDataId
    );
    setDataSettingModel(findDataSettingModel);
    reset();
    setValue(
      'dimensionHeight',
      findDataSettingModel.printPlateLayData.dimensionHeight
    );
    setValue(
      'dimensionWidth',
      findDataSettingModel.printPlateLayData.dimensionWidth
    );
    setValue('A', findDataSettingModel.width);
    setValue('B', findDataSettingModel.height);
    setValue('amount', findDataSettingModel.printPlateLayData.amount);
    const selectedCustomObj =
      await findDataSettingModel.printPlateLayData.modelCustomObject.map(
        (item: any) => {
          const { modelCustomObjectId } = item;
          const findModelCustomObject = modelCustomObjList.find(
            (mcObj: any) => mcObj.id === modelCustomObjectId
          );
          setValue(`${findModelCustomObject.key}`, item.value);
          return findModelCustomObject;
        }
      );
    let defaultSelectedCustomObj;
    if (findDataSettingModel.length > 0) {
      setValue('C', findDataSettingModel.length);
      defaultSelectedCustomObj = await modelCustomObjList.filter(
        (item: any) => item.key === 'A' || item.key === 'B' || item.key === 'C'
      );
    } else {
      defaultSelectedCustomObj = await modelCustomObjList.filter(
        (item: any) => item.key === 'A' || item.key === 'B'
      );
    }
    if (
      selectedCustomObj.some(
        (item: any) => item.key === 'A' || item.key === 'B' || item.key === 'C'
      )
    ) {
      setSelectedCustomObj(selectedCustomObj);
    } else {
      setSelectedCustomObj([...selectedCustomObj, ...defaultSelectedCustomObj]);
    }
    setOpen(true);
  };

  const handleClickSetting = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseSetting = () => {
    setAnchorEl(null);
  };
  const getModelCustomObj = async () => {
    const res = await apiModelCustomObject.getModelCustomObj();
    if (!res.isError) {
      setModelCustomObjList(res);
    }
  };
  useEffect(() => {
    getModelCustomObj();
  }, []);

  const handleClickCustomObj = (data: any) => {
    const findSelectedCustomObj = selectedCustomObj.some(
      (item: any) => item.id === data.id
    );
    if (findSelectedCustomObj) {
      const removeSelectedCustomObj = selectedCustomObj.filter(
        (item: any) => item.id !== data.id
      );
      setSelectedCustomObj(removeSelectedCustomObj);
    } else {
      setSelectedCustomObj([...selectedCustomObj, data]);
    }
  };
  const handleImageUpload = async (event: any, side: string) => {
    setUploadingDimension({
      ...uploadingDimension,
      [side]: true,
    });
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      const res = await apiPrintPlate.uploadDimension(formData);
      if (!res.isError) {
        updateAdditionalModelCustomObject(res.data, side);
      }
    }
    setUploadingDimension({
      ...uploadingDimension,
      [side]: false,
    });
  };

  const updateAdditionalModelCustomObject = (url: string, side: string) => {
    const findAdditionalModelCustomObject = additionalModelCustomObject.find(
      (item: any) => item.id === dataSettingModel.id
    );

    if (!isEmpty(findAdditionalModelCustomObject)) {
      const updatedPrintPlateLayData = {
        ...findAdditionalModelCustomObject.printPlateLayData,
        [side === 'front' ? 'dimensionFrontUrl' : 'dimensionBackUrl']: url,
      };

      const addImage = {
        ...findAdditionalModelCustomObject,
        printPlateLayData: updatedPrintPlateLayData,
      };

      const filterToUpdateAdditionalModelCustomObject =
        additionalModelCustomObject.filter(
          (item: any) => item.id !== dataSettingModel.id
        );

      setAdditionalModelCustomObject([
        ...filterToUpdateAdditionalModelCustomObject,
        addImage,
      ]);

      const updateDataSettingModel = {
        ...dataSettingModel,
        printPlateLayData: {
          ...dataSettingModel.printPlateLayData,
          [side === 'front' ? 'dimensionFrontUrl' : 'dimensionBackUrl']: url,
        },
      };

      setDataSettingModel(updateDataSettingModel);
      const updatedAdditionalModelCustomObject =
        additionalModelCustomObject.map((item: any) =>
          item.id === dataSettingModel.id ? updateDataSettingModel : item
        );
      handleUpdateCreatePrintSheetData(updatedAdditionalModelCustomObject);
    }
  };

  const handleRemoveDimensionUrl = (side: string) => {
    const findAdditionalModelCustomObject = additionalModelCustomObject.find(
      (item: any) => item.id === dataSettingModel.id
    );

    if (!isEmpty(findAdditionalModelCustomObject)) {
      const updatedPrintPlateLayData = {
        ...findAdditionalModelCustomObject.printPlateLayData,
        [side === 'front' ? 'dimensionFrontUrl' : 'dimensionBackUrl']: null,
      };

      const updatedModel = {
        ...findAdditionalModelCustomObject,
        printPlateLayData: updatedPrintPlateLayData,
      };

      const filteredAdditionalModelCustomObject =
        additionalModelCustomObject.filter(
          (item: any) => item.id !== dataSettingModel.id
        );

      setAdditionalModelCustomObject([
        ...filteredAdditionalModelCustomObject,
        updatedModel,
      ]);

      setDataSettingModel({
        ...dataSettingModel,
        printPlateLayData: {
          ...dataSettingModel.printPlateLayData,
          [side === 'front' ? 'dimensionFrontUrl' : 'dimensionBackUrl']: null,
        },
      });
      const updatedAdditionalModelCustomObject =
        additionalModelCustomObject.map((item: any) =>
          item.id === dataSettingModel.id
            ? {
                ...dataSettingModel,
                printPlateLayData: {
                  ...dataSettingModel.printPlateLayData,
                  [side === 'front' ? 'dimensionFrontUrl' : 'dimensionBackUrl']:
                    null,
                },
              }
            : item
        );
      handleUpdateCreatePrintSheetData(updatedAdditionalModelCustomObject);
    }
  };

  const onSubmitCustomModel = async (values: any) => {
    const selectedKeys = selectedCustomObj.map((obj) => obj.key);
    const filteredModelCustomObj = modelCustomObjList
      .filter((obj: any) => selectedKeys.includes(obj.key))
      .map((obj: any) => ({
        value: values[obj.key],
        // masterUnitSizeId: dataSettingModel.productModel.masterUnitSizeId,
        modelCustomObjectId: obj.id,
      }));

    const newPrintPlateLayData = {
      ...dataSettingModel.printPlateLayData,
      modelCustomObject: filteredModelCustomObj,
      amount: 1,
      dimensionHeight: Number(values.dimensionHeight),
      dimensionWidth: Number(values.dimensionWidth),
    };

    const updatedDataSettingModel = {
      ...dataSettingModel,
      printPlateLayData: newPrintPlateLayData,
    };

    setDataSettingModel(updatedDataSettingModel);

    const updatedAdditionalModelCustomObject = additionalModelCustomObject.map(
      (item: any) =>
        item.id === dataSettingModel.id ? updatedDataSettingModel : item
    );

    setAdditionalModelCustomObject(updatedAdditionalModelCustomObject);
    onClose();
    handleUpdateCreatePrintSheetData(updatedAdditionalModelCustomObject);
    if (isErrorModelCustomObject) {
      handleClearIsErrorModelCustomObject();
    }
  };

  const validateFile = async (value: any) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedSize = 2 * 1024 * 1024; // 2MB in bytes
    if (value.length > 0) {
      if (!allowedTypes.includes(value[0].type)) {
        return 'Please select a valid image file (JPEG, PNG, SVG, or GIF)';
      }
      if (value[0].size > allowedSize) {
        return 'Icon sizes should be less than 2MB';
      }
    }
    return true;
  };
  // console.log('additionalModelCustomObject', additionalModelCustomObject);
  // console.log('modelCustomObjList', modelCustomObjList);
  return (
    <>
      {!isEmpty(dataSettingModel) && (
        <Dialog
          open={open}
          onClose={() => {
            onClose();
          }}
        >
          <DialogContent>
            <FormModalStyle $width={524}>
              <div className="content-wrap">
                <div className="header">
                  <div className="title">ตั้งค่าโมเดล</div>
                  <div
                    className="x-close"
                    onClick={() => {
                      onClose();
                    }}
                  >
                    <IconButton>
                      <CloseIcon />
                    </IconButton>
                  </div>
                </div>
                <div className="form-wrap">
                  <form>
                    <SettingModelStyle>
                      <div className="setting-model-header">
                        <div className="image">
                          <Image
                            src={
                              dataSettingModel.productModel.imageUrl ||
                              '/images/product/empty-product.svg'
                            }
                            fill
                            alt=""
                          />
                        </div>
                        <div className="text-zone">
                          <div className="ld-code">
                            {dataSettingModel.ldCode}
                          </div>
                          <div className="size">
                            ขนาดสินค้า : {dataSettingModel.length} x{' '}
                            {dataSettingModel.width} x {dataSettingModel.height}{' '}
                            mm.
                          </div>
                          <div className="size">
                            ขนาดกางออก :{' '}
                            {watch('dimensionWidth')
                              ? numberWithCommas(watch('dimensionWidth'))
                              : '0'}{' '}
                            x{' '}
                            {watch('dimensionHeight')
                              ? numberWithCommas(watch('dimensionHeight'))
                              : '0'}{' '}
                            mm.
                          </div>
                        </div>
                      </div>
                      <div className="model-zone">
                        <div className="model-title-wrap">
                          <div className="model-title">
                            {dataSettingModel.productModel.layModel?.code ||
                              dataSettingModel.productModel.productModelName}
                          </div>
                          <div>
                            <Button
                              type="button"
                              variant="outlined"
                              color="blueGrey"
                              style={{
                                width: '40px',
                                height: '40px',
                                minHeight: '40px',
                                minWidth: '40px',
                              }}
                              onClick={(event: any) => {
                                handleClickSetting(event);
                              }}
                            >
                              <SettingsOutlinedIcon />
                            </Button>
                            <Menu
                              anchorEl={anchorEl}
                              open={Boolean(anchorEl)}
                              onClose={handleCloseSetting}
                              sx={{
                                marginTop: '12px',
                                '.MuiList-root': {
                                  padding: '8px',
                                  width: '192px',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  rowGap: '4px',
                                  height: '276px',
                                  overflow: 'auto',
                                },
                                li: {
                                  width: 'auto',
                                },
                                '.MuiCheckbox-root': {
                                  padding: '0',
                                },
                              }}
                            >
                              {!isEmpty(modelCustomObjList) &&
                                modelCustomObjList.map(
                                  (item: any, index: number) => {
                                    return (
                                      <MenuItem
                                        sx={{
                                          padding: '0 8px',
                                          height: '40px',
                                          width: '100px',
                                          borderRadius: '8px',
                                        }}
                                        key={index}
                                      >
                                        <div
                                          className="drop-menu"
                                          onClick={() => {
                                            handleClickCustomObj(item);
                                          }}
                                        >
                                          <Checkbox
                                            checked={selectedCustomObj.some(
                                              (selectedCustomObjItem: any) =>
                                                selectedCustomObjItem.id ===
                                                item.id
                                            )}
                                            icon={<IconUnCheckbox />}
                                            checkedIcon={<IconCheckboxBlack />}
                                          />
                                          <span className="uppercase">
                                            {item.key}
                                          </span>
                                        </div>
                                      </MenuItem>
                                    );
                                  }
                                )}
                            </Menu>
                          </div>
                        </div>
                        <div className="custom-model-object-form">
                          {!isEmpty(selectedCustomObj) &&
                            selectedCustomObj.map((item) => (
                              <div key={item.id}>
                                <FadeInStyled>
                                  <p className="uppercase">{item.key}</p>
                                  <TextField
                                    type="number"
                                    placeholder={item.key.toUpperCase()}
                                    onKeyDown={(e: React.KeyboardEvent) => {
                                      if (e.key === '-') {
                                        e.preventDefault();
                                      }
                                    }}
                                    onPaste={(e: React.ClipboardEvent) => {
                                      e.preventDefault();
                                    }}
                                    {...register(item.key, {
                                      onChange: (
                                        e: React.ChangeEvent<HTMLInputElement>
                                      ) => {
                                        if (Number(e.target.value) < 0) {
                                          e.preventDefault();
                                          e.target.value = '';
                                        }
                                      },
                                    })}
                                    error={Boolean(hookFormErrors[item.key])}
                                    helperText={
                                      hookFormErrors[item.key]
                                        ? (hookFormErrors[item.key]
                                            ?.message as ReactNode)
                                        : ''
                                    }
                                    InputProps={{
                                      endAdornment: <div>mm</div>,
                                    }}
                                  />
                                </FadeInStyled>
                              </div>
                            ))}
                        </div>
                      </div>
                      <div className="image-zone">
                        <div className="image-topic">รูปขนาดกางออก</div>
                        <div className="image-wrap">
                          <div className="group">
                            <div className="side">ด้านหน้า</div>
                            <label
                              className={`image ${
                                isEmpty(
                                  dataSettingModel.printPlateLayData
                                    .dimensionFrontUrl
                                )
                                  ? ''
                                  : '!border-0'
                              }`}
                            >
                              {!isEmpty(
                                dataSettingModel.printPlateLayData
                                  .dimensionFrontUrl
                              ) ? (
                                <>
                                  <div className="backdrop">
                                    <div
                                      className="remove-btn"
                                      onClick={(event: any) => {
                                        event.preventDefault();
                                        handleRemoveDimensionUrl('front');
                                      }}
                                    >
                                      <Image
                                        src="/icons/delete-white.svg"
                                        width={24}
                                        height={24}
                                        alt=""
                                      />
                                    </div>
                                    {/* <div className="zoom"> */}
                                    {/*  <ZoomInRoundedIcon /> */}
                                    {/* </div> */}
                                  </div>
                                  <div className="image-bg" />
                                  <Image
                                    src={
                                      dataSettingModel.printPlateLayData
                                        .dimensionFrontUrl
                                    }
                                    fill
                                    alt=""
                                  />
                                </>
                              ) : (
                                <input
                                  type="file"
                                  style={{ display: 'none' }}
                                  onChange={async (event: any) => {
                                    const validationResult = await validateFile(
                                      event.target.files
                                    );
                                    if (validationResult === true) {
                                      if (!isEmpty(errorImageUpload)) {
                                        setErrorImageUpload('');
                                      }
                                      await handleImageUpload(event, 'front');
                                    } else {
                                      setErrorImageUpload(validationResult);
                                    }
                                  }}
                                />
                              )}
                              {uploadingDimension.front ? (
                                <CircularProgress size={24} />
                              ) : (
                                <>
                                  <div className="icon">
                                    <UploadIcon />
                                  </div>
                                  <div>อัปโหลดรูป</div>
                                </>
                              )}
                            </label>
                          </div>
                          <div className="group">
                            <div className="side">ด้านหลัง</div>
                            <label
                              className={`image ${
                                isEmpty(
                                  dataSettingModel.printPlateLayData
                                    .dimensionBackUrl
                                )
                                  ? ''
                                  : '!border-0'
                              }`}
                            >
                              {!isEmpty(
                                dataSettingModel.printPlateLayData
                                  .dimensionBackUrl
                              ) ? (
                                <>
                                  <div className="backdrop">
                                    <div
                                      className="remove-btn"
                                      onClick={(event: any) => {
                                        event.preventDefault();
                                        handleRemoveDimensionUrl('back');
                                      }}
                                    >
                                      <Image
                                        src="/icons/delete-white.svg"
                                        width={24}
                                        height={24}
                                        alt=""
                                      />
                                    </div>
                                    {/* <div className="zoom"> */}
                                    {/*  <ZoomInRoundedIcon /> */}
                                    {/* </div> */}
                                  </div>
                                  <div className="image-bg" />
                                  <Image
                                    src={
                                      dataSettingModel.printPlateLayData
                                        .dimensionBackUrl
                                    }
                                    fill
                                    alt=""
                                  />
                                </>
                              ) : (
                                <input
                                  type="file"
                                  style={{ display: 'none' }}
                                  onChange={async (event: any) => {
                                    const validationResult = await validateFile(
                                      event.target.files
                                    );
                                    if (validationResult === true) {
                                      if (!isEmpty(errorImageUpload)) {
                                        setErrorImageUpload('');
                                      }
                                      await handleImageUpload(event, 'back');
                                    } else {
                                      setErrorImageUpload(validationResult);
                                    }
                                  }}
                                />
                              )}
                              {uploadingDimension.back ? (
                                <CircularProgress size={24} />
                              ) : (
                                <>
                                  <div className="icon">
                                    <UploadIcon />
                                  </div>
                                  <div>อัปโหลดรูป</div>
                                </>
                              )}
                            </label>
                          </div>
                        </div>
                      </div>
                      {!isEmpty(errorImageUpload) && (
                        <div className="w-full flex justify-center items-center">
                          <FormHelperText
                            error
                            sx={{
                              margin: '4px 14px 0',
                            }}
                          >
                            {errorImageUpload}
                          </FormHelperText>
                        </div>
                      )}
                      <div>
                        <p className="!mt-[8px]">ขนาดกางออก</p>
                        <div
                          className="flex"
                          style={{
                            columnGap: '16px',
                          }}
                        >
                          <TextField
                            type="number"
                            placeholder="Width"
                            onKeyDown={(e: any) => {
                              if (e.key === '-') {
                                e.preventDefault();
                              }
                            }}
                            onPaste={(e) => {
                              e.preventDefault();
                            }}
                            {...register('dimensionWidth', {
                              onChange: (e: any) => {
                                if (Number(e.target.value) < 0) {
                                  e.preventDefault();
                                  e.target.value = '';
                                }
                              },
                            })}
                            error={Boolean(hookFormErrors.dimensionWidth)}
                            helperText={
                              hookFormErrors.dimensionWidth
                                ? (hookFormErrors.dimensionWidth
                                    .message as ReactNode)
                                : ''
                            }
                            InputProps={{
                              startAdornment: (
                                <div className="dimen-start-adornment">W</div>
                              ),
                            }}
                          />
                          <TextField
                            type="number"
                            placeholder="Height"
                            onKeyDown={(e: any) => {
                              if (e.key === '-') {
                                e.preventDefault();
                              }
                            }}
                            onPaste={(e) => {
                              e.preventDefault();
                            }}
                            {...register('dimensionHeight', {
                              onChange: (e: any) => {
                                if (Number(e.target.value) < 0) {
                                  e.preventDefault();
                                  e.target.value = '';
                                }
                              },
                            })}
                            error={Boolean(hookFormErrors.dimensionHeight)}
                            helperText={
                              hookFormErrors.dimensionHeight
                                ? (hookFormErrors.dimensionHeight
                                    .message as ReactNode)
                                : ''
                            }
                            InputProps={{
                              startAdornment: (
                                <div className="dimen-start-adornment">H</div>
                              ),
                            }}
                          />
                        </div>
                      </div>
                      {/* <div> */}
                      {/*  <p className="!mt-[8px]">จำนวน/ใบพิมพ์</p> */}
                      {/*  <TextField */}
                      {/*    type="number" */}
                      {/*    placeholder="จำนวน/ใบพิมพ์" */}
                      {/*    onKeyDown={(e: any) => { */}
                      {/*      if (e.key === '-') { */}
                      {/*        e.preventDefault(); */}
                      {/*      } */}
                      {/*    }} */}
                      {/*    onPaste={(e) => { */}
                      {/*      e.preventDefault(); */}
                      {/*    }} */}
                      {/*    {...register('amount', { */}
                      {/*      onChange: (e: any) => { */}
                      {/*        if (Number(e.target.value) < 0) { */}
                      {/*          e.preventDefault(); */}
                      {/*          e.target.value = ''; */}
                      {/*        } */}
                      {/*      }, */}
                      {/*    })} */}
                      {/*    error={Boolean(hookFormErrors.amount)} */}
                      {/*    helperText={ */}
                      {/*      hookFormErrors.amount */}
                      {/*        ? (hookFormErrors.amount.message as ReactNode) */}
                      {/*        : '' */}
                      {/*    } */}
                      {/*  /> */}
                      {/* </div> */}
                    </SettingModelStyle>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      sx={{
                        fontSize: '16px',
                        maxHeight: '40px',
                        marginTop: '24px',
                      }}
                      onClick={handleSubmit(onSubmitCustomModel)}
                    >
                      บันทึก
                    </Button>
                  </form>
                </div>
              </div>
            </FormModalStyle>
          </DialogContent>
        </Dialog>
      )}
      <ModelListStyle
        $isErrorModelCustomObject={isErrorModelCustomObject}
        id="model-list"
      >
        <div className="title">Model Dieline</div>
        {!isEmpty(additionalModelCustomObject) &&
          additionalModelCustomObject.map((item: any, index: number) => {
            return (
              <div className="list" key={index}>
                <div className="list-header">
                  <div className="left">
                    <div className="image">
                      <Image
                        src={
                          item.productModel.imageUrl ||
                          '/images/product/empty-product.svg'
                        }
                        fill
                        alt=""
                      />
                    </div>
                    <div className="ld-code">{item.ldCode}</div>
                    {isEmpty(item.printPlateLayData.modelCustomObject) ? (
                      <div className="model-status pending">
                        <AccessTimeRoundedIcon />
                        ยังไม่ได้ตั้งค่าโมเดล
                      </div>
                    ) : (
                      <div className="model-status done">
                        <TaskAltRoundedIcon />
                        ตั้งค่าสำเร็จ
                      </div>
                    )}
                  </div>
                  <div className="right">
                    <div
                      onClick={async () => {
                        await onOpen(item.id);
                      }}
                      className="setting-model-btn-wrap"
                    >
                      <ActionButton
                        variant="outlined"
                        color="blueGrey"
                        icon={
                          <Image
                            src={'/icons/icon-package.svg'}
                            width={24}
                            height={24}
                            alt=""
                          />
                        }
                        text="ตั้งค่าโมเดล"
                        borderRadius={'8px'}
                      />
                    </div>
                    {!item.isDefault && (
                      <div
                        className="remove-btn"
                        onClick={() => {
                          handleRemoveLayData(item.id);
                        }}
                      >
                        <Image
                          src="/icons/delete-white.svg"
                          width={24}
                          height={24}
                          alt=""
                        />
                      </div>
                    )}
                  </div>
                </div>
                <div className="detail-wrap">
                  <div className="detail">
                    <div className="topic">ขนาด</div>
                    <div>
                      ขนาดสินค้า : {item.length} x {item.width} x {item.height}{' '}
                      mm.
                    </div>
                    <div>
                      ขนาดกางออก :{' '}
                      {numberWithCommas(item.printPlateLayData.dimensionWidth)}{' '}
                      x{' '}
                      {numberWithCommas(item.printPlateLayData.dimensionHeight)}{' '}
                      mm.
                    </div>
                  </div>
                  <div className="detail">
                    <div className="topic">
                      {`${item.productModel.productModelName}` ||
                        'ไม่พบต้นแบบโมเดล'}
                    </div>
                    {!isEmpty(item.printPlateLayData.modelCustomObject) ? (
                      <div className="chip-wrap">
                        {!isEmpty(modelCustomObjList) &&
                          item.printPlateLayData.modelCustomObject.map(
                            (mcObj: any, index: number) => {
                              const mcObjId = mcObj.modelCustomObjectId;
                              const { value } = mcObj;
                              const modelObj = modelCustomObjList.find(
                                (it: any) => it.id === mcObjId
                              );
                              if (modelObj) {
                                const { key } = modelObj;
                                return (
                                  <div
                                    className="chip"
                                    key={index}
                                  >{`${key} • ${value}`}</div>
                                );
                              }
                              return null;
                            }
                          )}
                      </div>
                    ) : (
                      <div className="text-[#CFD8DC]">ไม่มีข้อมูล</div>
                    )}
                  </div>
                  <div className="detail">
                    <div className="topic">วัสดุ</div>
                    <div className="detail-content">
                      {item.subMaterialDetail.name}
                    </div>
                  </div>
                  {item.print !== null && (
                    <div className="detail">
                      <div className="topic uppercase">
                        การพิมพ์ {item.printMaster?.name || '-'}
                      </div>
                      <div className="detail-content">
                        <div>
                          หนัา :{' '}
                          {item.colorFront.map(
                            (colorItem: any, index: number) => {
                              return (
                                <span key={index}>
                                  {colorItem.colorSubMaterialDetail.name}
                                  {index < item.colorFront.length - 1
                                    ? ', '
                                    : ''}
                                </span>
                              );
                            }
                          )}
                          {isEmpty(item.colorFront) && '-'}
                        </div>
                        <div>
                          หลัง :{' '}
                          {item.colorBack.map(
                            (colorItem: any, index: number) => {
                              return (
                                <span key={index}>
                                  {colorItem.colorSubMaterialDetail.name}
                                  {index < item.colorBack.length - 1
                                    ? ', '
                                    : ''}
                                </span>
                              );
                            }
                          )}
                          {isEmpty(item.colorBack) && '-'}
                        </div>
                      </div>
                    </div>
                  )}
                  {(!isEmpty(item.finishBack) ||
                    !isEmpty(item.finishFront)) && (
                    <div className="detail">
                      <div className="topic">การเคลือบ</div>
                      <div className="detail-content">
                        <div>
                          หน้า :{' '}
                          {item.finishFront.length > 0
                            ? item.finishFront.map(
                                (finishItem: any, index: number) => (
                                  <span key={index}>
                                    {finishItem.coatingMaster.name}{' '}
                                    {finishItem.finishSubMaterialDetail.name}
                                    {index < item.finishFront.length - 1
                                      ? ', '
                                      : ''}
                                  </span>
                                )
                              )
                            : '-'}
                        </div>
                        <div>
                          หลัง :{' '}
                          {item.finishBack.length > 0
                            ? item.finishBack.map(
                                (finishItem: any, index: number) => (
                                  <span key={index}>
                                    {finishItem.coatingMaster.name}{' '}
                                    {finishItem.finishSubMaterialDetail.name}
                                    {index < item.finishBack.length - 1
                                      ? ', '
                                      : ''}
                                  </span>
                                )
                              )
                            : '-'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
      </ModelListStyle>
    </>
  );
};

export default ModelList;
