import React from 'react';
import styled, { css } from 'styled-components';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import { EmptyField, FadeInStyled } from '@/styles/share.styled';
import { FormControl, FormHelperText, MenuItem, Select } from '@mui/material';
import { useRouter } from 'next/router';

const PrintPlateColorConfigStyle = styled.div<{ $isLastChild: boolean }>`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  min-height: 40px;
  padding: 16px 0;
  ${({ $isLastChild }) =>
    $isLastChild &&
    css`
      border-bottom: 1px solid #e0e0e0;
      margin-bottom: 24px;
    `}
  border-top: 1px solid #e0e0e0;
  .name-group {
    display: flex;
    align-items: center;
    column-gap: 8px;
    .color-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      min-width: 24px;
    }
    .name {
      font-weight: 600;
    }
  }
  .machine-group {
    display: flex;
    column-gap: 8px;
  }
`;
type Props = {
  color: any;
  isLastChild: boolean;
  printerList: any;
  hookFormErrors: any;
  watchColorFront: any;
  watchColorBack: any;
  side: string;
  idx: number;
  handleChangeMachine: (
    id: number,
    side: string,
    machineComponentId: number
  ) => void;
  // handleChangeColor: (id: number, side: string, colorId: number) => void;
};
const PrintPlateColorConfig = ({
  color,
  isLastChild,
  printerList,
  hookFormErrors,
  watchColorFront,
  watchColorBack,
  side,
  idx,
  handleChangeMachine,
}: // handleChangeColor,
Props) => {
  const router = useRouter();
  // const [listColor, setListColor] = useState<any>([]);

  // const getListColorsBySubMaterialDetailId = async () => {
  //   const res = await apiColor.getListColorsBySubMaterialDetailId(
  //     color.colorSubMaterialDetailId
  //   );
  //   if (!res.isError) {
  //     setListColor(res.data);
  //   }
  // };
  //
  // useEffect(() => {
  //   if (color) {
  //     getListColorsBySubMaterialDetailId();
  //   }
  // }, [color]);

  // console.log('hookFormErrors', hookFormErrors);
  // console.log('watchColorBack', watchColorBack);
  // console.log('listColor', listColor);
  console.log('color', color);
  return (
    <PrintPlateColorConfigStyle
      $isLastChild={isLastChild}
      data-test-id={`color-${side}-selector-${idx + 1}`}
    >
      <div className="name-group">
        <Image
          src={
            color.colorSubMaterialDetail.subMaterialImageUrl ||
            '/images/product/empty-product.svg'
          }
          width={80}
          height={80}
          alt=""
          className="color-image"
        />
        <div className="name">{color.color.name}</div>
      </div>
      <div className="machine-group">
        {!isEmpty(printerList) ? (
          <FadeInStyled
            style={{
              width: '100%',
            }}
          >
            <FormControl
              sx={{
                height: '40px',
                width: '300px',
              }}
              fullWidth
            >
              <Select
                error={
                  side === 'front'
                    ? Boolean(
                        hookFormErrors.colorFront?.[idx]?.machineComponentId
                      )
                    : Boolean(
                        hookFormErrors.colorBack?.[idx]?.machineComponentId
                      )
                }
                displayEmpty
                value={
                  side === 'front'
                    ? watchColorFront[idx]?.machineComponentId || ''
                    : watchColorBack[idx]?.machineComponentId || ''
                }
                onChange={(e: any) => {
                  handleChangeMachine(
                    side === 'front'
                      ? watchColorFront[idx].id
                      : watchColorBack[idx].id,
                    side,
                    e.target.value
                  );
                }}
              >
                <MenuItem
                  disabled
                  value=""
                  sx={{
                    fontSize: '14px',
                  }}
                >
                  <div className="text-[#78909C]">เลือกเครื่องพิมพ์</div>
                </MenuItem>
                {printerList.map((item: any, index: React.Key) => (
                  <MenuItem key={index} value={item.component.id}>
                    {item.component.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {side === 'front'
              ? hookFormErrors.colorFront &&
                hookFormErrors.colorFront[idx]?.machineComponentId && (
                  <FormHelperText
                    error
                    sx={{
                      margin: '4px 14px 0',
                    }}
                  >
                    {hookFormErrors.colorFront[idx]?.machineComponentId.message}
                  </FormHelperText>
                )
              : hookFormErrors.colorBack &&
                hookFormErrors.colorBack[idx]?.machineComponentId && (
                  <FormHelperText
                    error
                    sx={{
                      margin: '4px 14px 0',
                    }}
                  >
                    {hookFormErrors.colorBack[idx]?.machineComponentId.message}
                  </FormHelperText>
                )}
          </FadeInStyled>
        ) : (
          printerList !== undefined && (
            <FadeInStyled
              onClick={async () => {
                await router.push('/stock/master/component');
              }}
              style={{
                width: '100%',
              }}
            >
              <p>เครื่องพิมพ์</p>
              <EmptyField>กรุณาจับคู่ Master config component</EmptyField>
            </FadeInStyled>
          )
        )}
        {/* {!isEmpty(listColor) && ( */}
        {/*  <FadeInStyled */}
        {/*    style={{ */}
        {/*      width: '100%', */}
        {/*    }} */}
        {/*  > */}
        {/*    <FormControl */}
        {/*      sx={{ */}
        {/*        height: '40px', */}
        {/*        width: '132px', */}
        {/*      }} */}
        {/*      fullWidth */}
        {/*    > */}
        {/*      <Select */}
        {/*        displayEmpty */}
        {/*        value={ */}
        {/*          side === 'front' */}
        {/*            ? watchColorFront[idx].colorId || '' */}
        {/*            : watchColorBack[idx].colorId || '' */}
        {/*        } */}
        {/*        onChange={(e: any) => { */}
        {/*          handleChangeColor( */}
        {/*            side === 'front' */}
        {/*              ? watchColorFront[idx].id */}
        {/*              : watchColorBack[idx].id, */}
        {/*            side, */}
        {/*            e.target.value */}
        {/*          ); */}
        {/*        }} */}
        {/*        error={ */}
        {/*          side === 'front' */}
        {/*            ? Boolean(hookFormErrors.colorFront?.[idx]?.colorId) */}
        {/*            : Boolean(hookFormErrors.colorBack?.[idx]?.colorId) */}
        {/*        } */}
        {/*      > */}
        {/*        <MenuItem */}
        {/*          disabled */}
        {/*          value="" */}
        {/*          sx={{ */}
        {/*            fontSize: '14px', */}
        {/*          }} */}
        {/*        > */}
        {/*          <div className="text-[#78909C]">เลือกสี</div> */}
        {/*        </MenuItem> */}
        {/*        {listColor.map((item: any, index: React.Key) => ( */}
        {/*          <MenuItem key={index} value={item.id}> */}
        {/*            {item.name} */}
        {/*          </MenuItem> */}
        {/*        ))} */}
        {/*      </Select> */}
        {/*    </FormControl> */}
        {/*    {side === 'front' */}
        {/*      ? hookFormErrors.colorFront && */}
        {/*        hookFormErrors.colorFront[idx]?.colorId && ( */}
        {/*          <FormHelperText */}
        {/*            error */}
        {/*            sx={{ */}
        {/*              margin: '4px 14px 0', */}
        {/*            }} */}
        {/*          > */}
        {/*            {hookFormErrors.colorFront[idx]?.colorId.message} */}
        {/*          </FormHelperText> */}
        {/*        ) */}
        {/*      : hookFormErrors.colorBack && */}
        {/*        hookFormErrors.colorBack[idx]?.colorId && ( */}
        {/*          <FormHelperText */}
        {/*            error */}
        {/*            sx={{ */}
        {/*              margin: '4px 14px 0', */}
        {/*            }} */}
        {/*          > */}
        {/*            {hookFormErrors.colorBack[idx]?.colorId.message} */}
        {/*          </FormHelperText> */}
        {/*        )} */}
        {/*  </FadeInStyled> */}
        {/* )} */}
      </div>
    </PrintPlateColorConfigStyle>
  );
};

export default PrintPlateColorConfig;
