import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  IconButton,
} from '@mui/material';
import {
  CustomIosSwitchStyle,
  FadeInStyled,
  FormModalStyle,
} from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import styled from 'styled-components';
import { IOSSwitch } from '@/styles/mui-switch.styled';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';

export type FormMode = 'create' | 'edit';

type ModalLayCreatePrintSheetProps = {
  open: boolean;
  onClose: () => void;
  orderById: any;
  makeCreatePrintSheetData: (createPrintSheetData: any) => void;
};

const ModalLayCreatePrintSheet = ({
  open,
  onClose,
  orderById,
  makeCreatePrintSheetData,
}: ModalLayCreatePrintSheetProps) => {
  // const router = useRouter();
  // const { orderId } = router.query;
  const [isShowDetail, setIsShowDetail] = useState<boolean>(false);
  const [layDataList, setLayDataList] = useState<any>([]);
  const [selectedLayDataId, setSelectedLayDataId] = useState<number[]>([]);
  // console.log('orderById', orderById);
  useEffect(() => {
    if (!isEmpty(orderById.layData)) {
      setLayDataList(orderById.layData);
    }
  }, [orderById]);
  const handleSelectLayData = async (layDataId: any) => {
    if (selectedLayDataId.includes(layDataId)) {
      const removeSelectId = selectedLayDataId.filter(
        (id: number) => id !== layDataId
      );
      if (removeSelectId) {
        setSelectedLayDataId(removeSelectId);
      }
    } else {
      setSelectedLayDataId([layDataId]);
    }
  };
  const handleCreatePrintSheet = async () => {
    const selectedLayData = selectedLayDataId
      .map((id: number) =>
        orderById.layData.filter((latData: any) => latData.id === id)
      )
      .flat();
    const mergedCreatePrintSheetData = {
      ...orderById,
      layDataPrintSheet: selectedLayData,
    };
    delete mergedCreatePrintSheetData.layData;
    makeCreatePrintSheetData(mergedCreatePrintSheetData);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        onClose();
      }}
    >
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header">
              <div className="title">สร้างสเปคการผลิต</div>
              <div
                className="x-close"
                onClick={() => {
                  onClose();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <form>
                <PrintSheetSelectorStyle>
                  <div className="selector-header">
                    <div className="count">
                      {numberWithCommas(
                        layDataList.filter(
                          (item: any) => item.printPlateId === null
                        ).length
                      )}{' '}
                      รายการ
                    </div>
                    <CustomIosSwitchStyle>
                      <IOSSwitch
                        checked={isShowDetail}
                        onClick={() => {
                          setIsShowDetail(!isShowDetail);
                        }}
                      />
                    </CustomIosSwitchStyle>
                  </div>
                  {layDataList &&
                    layDataList.map((item: any, index: number) => {
                      if (item.printPlateId === null) {
                        return (
                          <div
                            className={`card ${
                              selectedLayDataId.includes(item.id)
                                ? 'active'
                                : ''
                            }`}
                            key={index}
                            onClick={async () => {
                              await handleSelectLayData(item.id);
                            }}
                          >
                            <div
                              className={`card-header ${
                                !isShowDetail ? '!border-0' : ''
                              }`}
                            >
                              <div className="profile">
                                <div className="image">
                                  <Image
                                    src={
                                      item.productModel.imageUrl ||
                                      '/images/company/company-info-empty-profile.svg'
                                    }
                                    width={80}
                                    height={80}
                                    alt=""
                                  />
                                </div>
                                <div className="text-group">
                                  <div className="name">{item.ldCode}</div>
                                  <div className="order-code">
                                    {item.productModel.productModelName}
                                  </div>
                                </div>
                              </div>
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  columnGap: '4px',
                                }}
                              >
                                {selectedLayDataId.includes(item.id) && (
                                  <FadeInStyled
                                    className="flex items-center"
                                    style={{
                                      columnGap: '4px',
                                    }}
                                  >
                                    <div className="parent">Selected</div>
                                  </FadeInStyled>
                                )}
                                <Checkbox
                                  color="primary"
                                  checked={selectedLayDataId.includes(item.id)}
                                  icon={<IconUnCheckbox />}
                                  checkedIcon={<IconCheckbox />}
                                />
                              </div>
                            </div>
                            {isShowDetail && (
                              <FadeInStyled className="detail-wrap">
                                <div className="detail">
                                  <div className="title">วัสดุผลิต</div>
                                  <div className="value uppercase">
                                    {item.subMaterialDetail.name}
                                  </div>
                                </div>
                                <div className="detail">
                                  <div className="title uppercase">
                                    การพิมพ์{' | '}
                                    {item.printMaster?.name || 'ไม่พิมพ์'}
                                  </div>
                                  <div className="value uppercase">
                                    <div>
                                      หนัา :{' '}
                                      {item.colorFront.map(
                                        (colorItem: any, index: number) => {
                                          return (
                                            <span key={index}>
                                              {
                                                colorItem.colorSubMaterialDetail
                                                  .name
                                              }
                                              {index <
                                              item.colorFront.length - 1
                                                ? ', '
                                                : ''}
                                            </span>
                                          );
                                        }
                                      )}
                                      {isEmpty(item.colorFront) && '-'}
                                    </div>
                                    <div>
                                      หลัง :{' '}
                                      {item.colorBack.map(
                                        (colorItem: any, index: number) => {
                                          return (
                                            <span key={index}>
                                              {
                                                colorItem.colorSubMaterialDetail
                                                  .name
                                              }
                                              {index < item.colorBack.length - 1
                                                ? ', '
                                                : ''}
                                            </span>
                                          );
                                        }
                                      )}
                                      {isEmpty(item.colorBack) && '-'}
                                    </div>
                                  </div>
                                </div>
                                <div className="detail">
                                  <div className="title">การเคลือบ</div>
                                  <div className="value uppercase">
                                    <div>
                                      หน้า :{' '}
                                      {item.finishFront.length > 0
                                        ? item.finishFront.map(
                                            (
                                              finishItem: any,
                                              index: number
                                            ) => (
                                              <span key={index}>
                                                {finishItem.coatingMaster.name}{' '}
                                                {
                                                  finishItem
                                                    .finishSubMaterialDetail
                                                    .name
                                                }
                                                {index <
                                                item.finishFront.length - 1
                                                  ? ', '
                                                  : ''}
                                              </span>
                                            )
                                          )
                                        : '-'}
                                    </div>
                                    <div>
                                      หลัง :{' '}
                                      {item.finishBack.length > 0
                                        ? item.finishBack.map(
                                            (
                                              finishItem: any,
                                              index: number
                                            ) => (
                                              <span key={index}>
                                                {finishItem.coatingMaster.name}{' '}
                                                {
                                                  finishItem
                                                    .finishSubMaterialDetail
                                                    .name
                                                }
                                                {index <
                                                item.finishBack.length - 1
                                                  ? ', '
                                                  : ''}
                                              </span>
                                            )
                                          )
                                        : '-'}
                                    </div>
                                  </div>
                                </div>
                              </FadeInStyled>
                            )}
                          </div>
                        );
                      }
                    })}
                </PrintSheetSelectorStyle>
                <Button
                  type="button"
                  variant="contained"
                  color="dark"
                  fullWidth
                  disabled={selectedLayDataId.length === 0}
                  sx={{
                    fontSize: '16px',
                    maxHeight: '40px',
                    marginTop: '24px',
                  }}
                  onClick={async () => {
                    await handleCreatePrintSheet();
                  }}
                >
                  สร้าง
                </Button>
              </form>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

const PrintSheetSelectorStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  margin-top: 20px;
  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .count {
      font-weight: 600;
    }
  }

  .card {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    border: 1px solid #dbe2e5;
    max-width: 100%;
    position: relative;
    cursor: pointer;

    &.active {
      &:before {
        box-shadow: #16d5c5 0px 0px 0px 2px inset;
      }
    }

    &:before {
      content: '';
      border-radius: 16px;
      position: absolute;
      z-index: 1;
      width: 100%;
      height: 100%;
    }

    .card-header {
      padding: 12px;
      border-bottom: 1px solid #dbe2e5;
      height: 100%;
      display: flex;
      width: 100%;
      justify-content: space-between;
      .parent {
        padding: 0 12px;
        border-radius: 16px;
        background: #f1fce3;
        font-size: 12px;
        color: #8bc34a;
      }
      .profile {
        display: flex;
        align-items: center;
        column-gap: 12px;

        .image {
          border-radius: 4px;
          overflow: hidden;
          height: 40px;
          width: 40px;
          min-width: 40px;
          user-select: none;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 8px;
          overflow: hidden;

          .name {
            font-weight: 600;
            font-size: 16px;
            line-height: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
          }

          .order-code {
            font-size: 10px;
            color: #90a4ae;
            line-height: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
          }
        }
      }
    }

    .detail-wrap {
      display: flex;
      width: 100%;

      .detail {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        padding: 12px 12px;
        row-gap: 2px;
        overflow: hidden;
        @media screen and (max-width: 450px) {
          * {
            text-align: center;
          }
        }

        .title {
          display: flex;
          align-items: center;
          font-size: 10px;
          font-weight: 600;
          column-gap: 8px;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          @media screen and (max-width: 450px) {
            flex-direction: column;
            row-gap: 4px;
          }
        }

        .value {
          font-size: 10px;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow-wrap: break-word;
        }

        &:nth-child(2) {
          border-left: 1px solid #dbe2e5;
          border-right: 1px solid #dbe2e5;
        }
      }
    }
  }
`;
export default ModalLayCreatePrintSheet;
