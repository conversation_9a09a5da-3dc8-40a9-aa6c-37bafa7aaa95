import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import TaskAltRoundedIcon from '@mui/icons-material/TaskAltRounded';
import { AddCircle } from '@mui/icons-material';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import { DataGrid, GridColDef, GridEventListener } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import LinkIcon from '@mui/icons-material/Link';
import { Avatar, Button } from '@mui/material';
import { dateThaiFormat } from '@/utils/date';
import KebabTable from '@/components/KebabTable';
import { isEmpty, isNull } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import Image from 'next/image';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import TaskAltIcon from '@mui/icons-material/TaskAlt';

const TableArtworkWithTopicStyle = styled.div`
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(224, 224, 224, 1) !important;
  .header {
    width: 100%;
    background: #f5f7f8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 8px 8px 16px;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
    .left-side {
      display: flex;
      align-items: center;
      .topic-table {
        font-size: 16px;
        font-weight: 600;
      }
    }
    .right-side {
      display: flex;
      align-items: center;
      column-gap: 8px;
    }
  }
`;
const ConfirmChipStyle = styled.div`
  display: flex;
  align-items: center;
  column-gap: 6px;
  color: #7cb342;
  font-weight: 600;
  svg:first-child {
    margin-top: -2px;
  }
`;
type Props = {
  step: number;
  data: any;
  artworkConfig: any;
  handleClickAdd: (step: number) => void;
  handleClickDetail: (step: number, row: any, index?: number) => void;
  handleOpenEdit: (data: any, step: number) => void;
  handleConfirmDelete: (data: any, step: number) => void;
  handleConfirmNextStep: () => void;
  reFetchArtworkDetail: () => void;
  handleOpenModalConfirmArtwork?: (row: any, step: number) => void;
  handleOpenModalReportEditArtwork?: (row: any, step: number) => void;
  handleConfirmExample: (row: any) => void;
  handleConfirmFile: () => void;
};
const TableArtworkWithTopic = ({
  step,
  data,
  artworkConfig,
  handleClickAdd,
  handleClickDetail,
  handleOpenEdit,
  handleConfirmDelete,
  handleConfirmNextStep,
  handleOpenModalConfirmArtwork,
  reFetchArtworkDetail,
  handleOpenModalReportEditArtwork,
  handleConfirmExample,
  handleConfirmFile,
}: Props) => {
  const dispatch = useAppDispatch();
  const [rows, setRows] = useState<any>([]);
  const [columns, setColumns] = useState<GridColDef[]>([]);
  const handleCellEditCommit: GridEventListener<'cellEditCommit'> = async (
    params: any
  ) => {
    const updatedRows = rows.map((row: any) =>
      row.id === params.id ? { ...row, [params.field]: params.value } : row
    );
    setRows(updatedRows);
    if (params.row.title !== params.value) {
      const newValue = {
        artworkConfigId: data.id,
        title: params.value,
        description: params.row.description,
        link: params.row.link,
      };
      const res = await apiArtwork.updateArtworkDetail(newValue, params.row.id);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
        reFetchArtworkDetail();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.error.response.data.message,
            severity: 'error',
          })
        );
      }
    }
  };
  useEffect(() => {
    if (!isEmpty(data)) {
      setRows(data.artworkDetail);
    }
  }, [data]);

  useEffect(() => {
    if (step === 1) {
      setColumns([
        {
          field: 'title',
          headerName: 'หัวข้อ',
          editable: true,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
        },
        {
          field: 'file',
          headerName: 'ไฟล์',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 142,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <div
                onClick={async () => {
                  if (params.row.link) {
                    await navigator.clipboard.writeText(params.row.link);
                  }
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<LinkIcon />}
                  text="คัดลอก"
                />
              </div>
            );
          },
        },
        {
          field: 'createdBy',
          headerName: 'ผู้สร้าง',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <Avatar
                alt={params.row.user.name[0] || '#'}
                src={params.row.user.imageUrl || ''}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'updatedDate',
          headerName: 'อัปเดตล่าสุด',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 224,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{dateThaiFormat(params.row.modifiedDate)}</div>;
          },
        },
        {
          field: '#',
          headerName: 'จัดการ',
          editable: false,
          headerAlign: 'right',
          align: 'right',
          minWidth: 170,
          disableColumnMenu: true,
          sortable: false,
          cellClassName: 'stickyCell',
          renderCell: (params: any) => {
            const index = rows.findIndex((row: any) => row.id === params.id);
            return (
              <>
                <div className="flex items-center gap-[8px]">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    style={{
                      width: '84px',
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '12px',
                    }}
                    onClick={() => {
                      handleClickDetail(step, params.row, index);
                    }}
                  >
                    รายละเอียด
                  </Button>
                  {params.row.approvedUser === null && index !== 0 && (
                    <KebabTable
                      item={params.row}
                      handleRemove={(item: any) => {
                        handleConfirmDelete(item, step);
                      }}
                      isEdit={{
                        status: true,
                        action: () => handleOpenEdit(params.row, step),
                      }}
                      isRemove={true}
                    />
                  )}
                </div>
              </>
            );
          },
        },
      ]);
    } else if (step === 2) {
      setColumns([
        {
          field: 'title',
          headerName: 'หัวข้อ',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
        },
        {
          field: 'reference',
          headerName: 'อ้างอิงจาก',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{params.row.reference?.name || '-'}</div>;
          },
        },
        {
          field: 'artwork',
          headerName: 'Artwork',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            if (params.row.artwork !== null) {
              return (
                <div className="flex items-center gap-2">
                  <Image
                    src={'/icons/icon-artwork-shape.svg'}
                    width={24}
                    height={24}
                    alt=""
                  />
                  <div
                    style={{
                      marginTop: '3px',
                    }}
                  >
                    {numberWithCommas(params.row.artwork, 2)}
                  </div>
                </div>
              );
            }
            return <div>-</div>;
          },
        },
        {
          field: 'comment',
          headerName: 'ความคิดเห็น',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            if (params.row.comment !== null) {
              return (
                <div className="flex items-center gap-2">
                  <Image
                    src={'/icons/icon-comment.svg'}
                    width={24}
                    height={24}
                    alt=""
                  />
                  <div
                    style={{
                      marginTop: '3px',
                    }}
                  >
                    {numberWithCommas(params.row.comment, 2)}
                  </div>
                </div>
              );
            }
            return <div>-</div>;
          },
        },
        {
          field: 'status',
          headerName: 'สถานะ',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 172,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <>
                {!isNull(params.row.approvedUser) ? (
                  <ConfirmChipStyle>
                    <TaskAltRoundedIcon />
                    <span>ยืนยันแบบ</span>
                  </ConfirmChipStyle>
                ) : (
                  '-'
                )}
              </>
            );
          },
        },
        {
          field: 'approvedUser',
          headerName: 'ผู้ยืนยัน',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <Avatar
                alt={'S'}
                src={params.approvedUser?.imageUrl || '#'}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'file',
          headerName: 'ไฟล์',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 142,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <div
                onClick={async () => {
                  if (params.row.link) {
                    await navigator.clipboard.writeText(params.row.link);
                  }
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<LinkIcon />}
                  text="คัดลอก"
                />
              </div>
            );
          },
        },
        {
          field: 'person',
          headerName: 'ผู้รับผิดชอบ',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (_params: any) => {
            return (
              <Avatar
                alt={'S'}
                src={'#'}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'updatedDate',
          headerName: 'อัปเดตล่าสุด',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 224,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{dateThaiFormat(params.row.modifiedDate)}</div>;
          },
        },
        {
          field: '#',
          headerName: 'จัดการ',
          editable: false,
          headerAlign: 'right',
          align: 'right',
          minWidth: 170,
          disableColumnMenu: true,
          sortable: false,
          cellClassName: 'stickyCell',
          renderCell: (params: any) => {
            return (
              <>
                <div className="flex items-center gap-[8px]">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    style={{
                      width: '84px',
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '12px',
                    }}
                    onClick={() => {
                      handleClickDetail(step, params.row);
                    }}
                  >
                    รายละเอียด
                  </Button>
                  {isNull(params.row.approvedUser) && (
                    <KebabTable
                      item={params.row}
                      handleRemove={(item: any) => {
                        handleConfirmDelete(item, step);
                      }}
                      isEdit={{
                        status: true,
                        action: () => handleOpenEdit(params.row, step),
                      }}
                      isRemove={true}
                      handleConfirmArtwork={() => {
                        if (handleOpenModalConfirmArtwork) {
                          handleOpenModalConfirmArtwork(params.row, step);
                        }
                      }}
                    />
                  )}
                </div>
              </>
            );
          },
        },
      ]);
    } else if (step === 3) {
      setColumns([
        {
          field: 'title',
          headerName: 'หัวข้อ',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
        },
        {
          field: 'reference',
          headerName: 'อ้างอิงจาก',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{params.row.reference?.name || '-'}</div>;
          },
        },
        {
          field: 'artwork',
          headerName: 'Artwork',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            if (params.row.artwork !== null) {
              return (
                <div className="flex items-center gap-2">
                  <Image
                    src={'/icons/icon-artwork-shape.svg'}
                    width={24}
                    height={24}
                    alt=""
                  />
                  <div
                    style={{
                      marginTop: '3px',
                    }}
                  >
                    {numberWithCommas(params.row.artwork, 2)}
                  </div>
                </div>
              );
            }
            return <div>-</div>;
          },
        },
        {
          field: 'comment',
          headerName: 'ความคิดเห็น',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            if (params.row.comment !== null) {
              return (
                <div className="flex items-center gap-2">
                  <Image
                    src={'/icons/icon-comment.svg'}
                    width={24}
                    height={24}
                    alt=""
                  />
                  <div
                    style={{
                      marginTop: '3px',
                    }}
                  >
                    {numberWithCommas(params.row.comment, 2)}
                  </div>
                </div>
              );
            }
            return <div>-</div>;
          },
        },
        {
          field: 'status',
          headerName: 'สถานะ',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 172,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <>
                {!isNull(params.row.approvedUser) ? (
                  <ConfirmChipStyle>
                    <TaskAltRoundedIcon />
                    <span>ยืนยันแบบ</span>
                  </ConfirmChipStyle>
                ) : (
                  '-'
                )}
              </>
            );
          },
        },
        {
          field: 'approvedUser',
          headerName: 'ผู้ยืนยัน',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <Avatar
                alt={'S'}
                src={params.approvedUser?.imageUrl || '#'}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'person',
          headerName: 'ผู้รับผิดชอบ',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (_params: any) => {
            return (
              <Avatar
                alt={'S'}
                src={'#'}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'updatedDate',
          headerName: 'อัปเดตล่าสุด',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 224,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{dateThaiFormat(params.row.modifiedDate)}</div>;
          },
        },
        {
          field: '#',
          headerName: 'จัดการ',
          editable: false,
          headerAlign: 'right',
          align: 'right',
          minWidth: 170,
          disableColumnMenu: true,
          sortable: false,
          cellClassName: 'stickyCell',
          renderCell: (params: any) => {
            const isReported = artworkConfig
              .find((configItem: any) => configItem.artworkStatus.id === 2)
              ?.artworkDetail?.some(
                (artworkDetailItem: any) =>
                  artworkDetailItem.reference?.refId === params.row.id
              );
            const isConfirmed = params.row.approvedUser !== null;
            const isDone = artworkConfig.find(
              (item: any) => item.artworkStatus.id === 3
            ).isSuccess;
            return (
              <>
                <div className="flex items-center gap-[8px]">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    style={{
                      width: '84px',
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '12px',
                    }}
                    onClick={() => {
                      handleClickDetail(step, params.row);
                    }}
                  >
                    รายละเอียด
                  </Button>
                  {!isReported && !isConfirmed && !isDone && (
                    <KebabTable
                      item={params.row}
                      handleRemove={(_item: any) => {
                        // handleConfirmDelete(item, step);
                      }}
                      isEdit={{
                        status: true,
                        action: () => handleOpenEdit(params.row, step),
                      }}
                      isRemove={false}
                      handleConfirmExample={() => {
                        handleConfirmExample(params.row);
                      }}
                      handleReportEditArtwork={() => {
                        if (handleOpenModalReportEditArtwork) {
                          handleOpenModalReportEditArtwork(params.row, step);
                        }
                      }}
                    />
                  )}
                </div>
              </>
            );
          },
        },
      ]);
    } else if (step === 4) {
      setColumns([
        {
          field: 'title',
          headerName: 'หัวข้อ',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 228,
          flex: 1,
          disableColumnMenu: true,
          sortable: false,
        },
        {
          field: 'file',
          headerName: 'ไฟล์',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 142,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <div
                onClick={async () => {
                  if (params.row.link) {
                    await navigator.clipboard.writeText(params.row.link);
                  }
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={<LinkIcon />}
                  text="คัดลอก"
                />
              </div>
            );
          },
        },
        {
          field: 'user',
          headerName: 'ผู้สร้าง',
          editable: false,
          headerAlign: 'center',
          align: 'center',
          minWidth: 64,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return (
              <Avatar
                alt={`${params.row.user.name[0]}`}
                src={`${params.row.user.imageUrl || '#'}`}
                sx={{
                  width: 34,
                  height: 34,
                  backgroundColor: '#30D5C7',
                  textTransform: 'uppercase',
                }}
              />
            );
          },
        },
        {
          field: 'updatedDate',
          headerName: 'อัปเดตล่าสุด',
          editable: false,
          headerAlign: 'left',
          align: 'left',
          minWidth: 224,
          disableColumnMenu: true,
          sortable: false,
          renderCell: (params: any) => {
            return <div>{dateThaiFormat(params.row.modifiedDate)}</div>;
          },
        },
        {
          field: '#',
          headerName: 'จัดการ',
          editable: false,
          headerAlign: 'right',
          align: 'right',
          minWidth: 170,
          disableColumnMenu: true,
          sortable: false,
          cellClassName: 'stickyCell',
          renderCell: (params: any) => {
            const isConfirmed = params.row.approvedUser !== null;
            const isDone = artworkConfig.find(
              (item: any) => item.artworkStatus.id === 4
            ).isSuccess;
            return (
              <>
                <div className="flex items-center gap-[8px]">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    style={{
                      width: '84px',
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '12px',
                    }}
                    onClick={() => {
                      handleClickDetail(step, params.row);
                    }}
                  >
                    รายละเอียด
                  </Button>
                  {!isConfirmed && !isDone && (
                    <KebabTable
                      item={params.row}
                      handleRemove={(item: any) => {
                        handleConfirmDelete(item, step);
                      }}
                      isEdit={{
                        status: true,
                        action: () => handleOpenEdit(params.row, step),
                      }}
                      isRemove={true}
                    />
                  )}
                </div>
              </>
            );
          },
        },
      ]);
    }
  }, [step, artworkConfig, rows]);
  return (
    <TableArtworkWithTopicStyle>
      <div className="header">
        <div className="left-side">
          <div className="topic-table">{data.artworkStatus.name}</div>
        </div>
        <div className="right-side">
          {step === 1 && !isEmpty(rows) && !data.isSuccess && (
            <div
              onClick={() => {
                handleConfirmNextStep();
              }}
            >
              <ActionButton
                variant="outlined"
                color="blueGrey"
                icon={<TaskAltRoundedIcon />}
                text="ยืนยัน"
                borderRadius={'8px'}
              />
            </div>
          )}
          {step === 4 &&
            !artworkConfig.find((config: any) => config.artworkStatus.id === 4)
              .isSuccess && (
              <div
                onClick={() => {
                  if (!isEmpty(data.artworkDetail)) {
                    handleConfirmFile();
                  }
                }}
              >
                <ActionButton
                  variant="contained"
                  color="dark"
                  icon={<TaskAltIcon />}
                  text="ยืนยันไฟล์ส่งผลิต"
                  borderRadius={'8px'}
                  disabled={isEmpty(data.artworkDetail)}
                />
              </div>
            )}
          {step !== 3 && (
            <div
              onClick={(event: any) => {
                if (
                  step !== 1 &&
                  !artworkConfig.find(
                    (item: any) => item.artworkStatus.id === step - 1
                  ).isSuccess
                ) {
                  event.preventDefault();
                } else {
                  handleClickAdd(step);
                }
              }}
            >
              <ActionButton
                variant="outlined"
                color="blueGrey"
                icon={<AddCircle />}
                text="เพิ่มข้อมูล"
                borderRadius={'8px'}
                disabled={
                  step === 1
                    ? false
                    : !artworkConfig.find(
                        (item: any) => item.artworkStatus.id === step - 1
                      ).isSuccess
                }
              />
            </div>
          )}
        </div>
      </div>
      <AppTableStyle $rows={rows} $disableLastRowBorder={true}>
        <div className="content-wrap">
          <ScrollBarStyled>
            <HeaderColumnAction text="จัดการ" width={170} />
            <DataGrid
              hideFooter={true}
              rows={rows || []}
              columns={columns}
              paginationMode="server"
              rowCount={rows.length || 0}
              // pageSize={filters.sizes}
              disableSelectionOnClick={false}
              autoHeight={true}
              sortModel={[]}
              getRowHeight={() => 56}
              headerHeight={48}
              components={{
                NoRowsOverlay: () => <TableNoRowsOverlay />,
                LoadingOverlay: () => <TableLoadingOverlay />,
              }}
              onCellEditCommit={handleCellEditCommit}
            />
          </ScrollBarStyled>
        </div>
      </AppTableStyle>
    </TableArtworkWithTopicStyle>
  );
};

export default TableArtworkWithTopic;
