import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ArtworkList from '@/components/order/artwork/ArtworkList';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import { isEmpty } from 'lodash';
import apiArtwork from '@/services/order/artwork';
import apiLayData from '@/services/order/layData';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

const ArtworkZoneStyle = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 24px;
  border-radius: 16px;
`;
type Props = {
  orderById: any;
  reloadOrder: () => void;
  ldData: any;
};
const ArtworkZone = ({ orderById, reloadOrder, ldData }: Props) => {
  const dispatch = useAppDispatch();
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const [openConfirmSendProduction, setOpenConfirmSendProduction] =
    useState<any>({
      status: false,
      data: {},
    });
  const [artworkList, setArtworkList] = useState<any>([]);
  const [artwork, setArtwork] = useState<any>({});
  const getArtworkList = async () => {
    const res = await apiArtwork.getArtworkList(orderById.id);
    if (!res.isError) {
      setArtworkList(res.data);
    }
  };
  useEffect(() => {
    if (!isEmpty(orderById)) {
      getArtworkList().then();
    }
  }, [orderById]);
  const handleSendToProd = async () => {
    setLoadingConfirm(true);
    const res = await apiLayData.approvedLayData(
      openConfirmSendProduction.data.layDataArtwork.layDataId
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setOpenConfirmSendProduction({
        ...openConfirmSendProduction,
        status: false,
      });
      await getArtworkList();
      await reloadOrder();
    }
    setLoadingConfirm(false);
  };
  // console.log('openConfirmSendProduction', openConfirmSendProduction);

  useEffect(() => {
    if (!isEmpty(artworkList)) {
      const artworkData = artworkList.find(
        (artworkItem: any) => artworkItem.id === ldData.id
      );
      if (artworkData) {
        setArtwork(artworkData);
      }
    }
  }, [ldData, artworkList]);
  console.log('artworkData', artwork);
  return (
    <ArtworkZoneStyle>
      <AppModalConfirm
        open={openConfirmSendProduction.status}
        isReason={false}
        onClickClose={() => {
          setOpenConfirmSendProduction(false);
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ยืนยันส่งผลิตสินค้า`}
        confirmDescription={`คุณได้ตรวจสอบข้อมูลอาร์ตเวิร์กเรียบร้อยแล้ว จะทำการเข้าสู่การผลิตต่อไป
`}
        maxWidth={'324px'}
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleSendToProd();
        }}
      />
      {!isEmpty(artwork) && (
        <ArtworkList
          data={artwork}
          handleClickSendProduction={() => {
            setOpenConfirmSendProduction({
              status: true,
              data: artwork,
            });
          }}
        />
      )}
    </ArtworkZoneStyle>
  );
};

// @ts-ignore
export default ArtworkZone;
