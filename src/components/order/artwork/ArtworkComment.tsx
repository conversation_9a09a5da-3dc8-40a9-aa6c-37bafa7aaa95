import React, { useState, useEffect, useRef, ReactNode } from 'react';
import styled, { css } from 'styled-components';
import {
  Avatar,
  Button,
  CircularProgress,
  Menu,
  MenuItem,
  TextField,
} from '@mui/material';
import { dateThaiFormat } from '@/utils/date';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { LoadingFadein } from '@/styles/share.styled';

const ArtworkCommentStyle = styled.div<{ $isExtend: boolean }>`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  margin-bottom: 4px;
  padding-top: 16px;
  border-top: 1px solid #dbe2e5;
  animation: ${LoadingFadein} 0.3s ease-in;
  &:last-child {
    margin-bottom: 0;
  }
  .MuiInputBase-root {
    padding: 10px;
  }
  .MuiInputBase-inputMultiline {
    min-height: 20px;
    resize: vertical;
  }
  .comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right {
      display: flex;
      align-items: center;
      column-gap: 12px;
      .comment-profile-text-group {
        display: flex;
        flex-direction: column;
        .comment-profile-name {
          font-weight: 600;
        }
        .comment-date {
          font-size: 10px;
        }
      }
    }
  }
  .comment-content {
    ${({ $isExtend }) =>
      !$isExtend &&
      css`
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow-wrap: break-word;
      `};
  }
  .extend-comment {
    font-weight: 600;
    margin-top: -8px;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  .comment-image {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    overflow: hidden;
  }
`;

type Props = {
  data: any;
  reFetchComment: () => Promise<void>;
  handleClickEditImage: (commentId: number) => void;
};
const validationSchema = yup.object({
  comment: yup.string().required('กรุณากรอก'),
});
const ArtworkComment = ({
  data,
  reFetchComment,
  handleClickEditImage,
}: Props) => {
  const router = useRouter();
  const { artworkId } = router.query;
  const { user } = useAppSelector(userSelector);
  const dispatch = useAppDispatch();
  const [isExtend, setIsExtend] = useState<boolean>(false);
  const [showExtendButton, setShowExtendButton] = useState<boolean>(false);
  const commentRef = useRef<HTMLDivElement>(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      comment: '',
    },
  });
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  useEffect(() => {
    if (commentRef.current) {
      const lineHeight = 24;
      const maxHeight = lineHeight * 2;
      const contentHeight = commentRef.current.scrollHeight;
      setShowExtendButton(contentHeight > maxHeight);
    }
  }, []);
  const handleDelete = async () => {
    const res = await apiArtwork.deleteComment(data.id);
    if (!res.isError) {
      await reFetchComment();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
  };
  const onSubmit = async (values: any) => {
    setIsSubmitting(true);
    const sendValue = {
      ...values,
      artworkDetailId: Number(artworkId),
    };
    const res = await apiArtwork.updateComment(data.id, sendValue);
    if (!res.isError) {
      await reFetchComment();
      setIsEdit(false);
      reset();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setIsSubmitting(false);
  };
  useEffect(() => {
    setValue('comment', data.content);
  }, [data]);
  return (
    <ArtworkCommentStyle $isExtend={isExtend}>
      <div className="comment-header">
        <div className="right">
          <Avatar src={data.user.imageUrl || '#'}>{data.user.name[0]}</Avatar>
          <div className="comment-profile-text-group">
            <div className="comment-profile-name">{data.user.name}</div>
            <div className="comment-date">
              {dateThaiFormat(data.createdDate)}
            </div>
          </div>
        </div>
        {user.id === data.user.id && (
          <>
            <Button
              size="small"
              onClick={handleClick}
              sx={{
                borderRadius: '8px',
                maxHeight: '32px',
                maxWidth: '32px',
                minHeight: '32px',
                minWidth: '32px',
              }}
              className="left"
            >
              <div className="action-dot">
                <div className="dot" />
              </div>
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{
                '.MuiList-root': {
                  padding: '8px',
                },
              }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div
                  className="drop-menu edit"
                  onClick={() => {
                    if (data.mediaTypeId === 2) {
                      handleClickEditImage(data.id);
                    } else {
                      setIsEdit(true);
                    }
                  }}
                >
                  <Image
                    src="/icons/edit-black.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                  แก้ไข
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleDelete}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FDE8EF',
                  },
                }}
              >
                <div className="drop-menu text-[#D32F2F]">
                  <Image
                    src="/icons/icon-trash-red.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                  ลบ
                </div>
              </MenuItem>
            </Menu>
          </>
        )}
      </div>
      {data.mediaType.id === 1 &&
        (!isEdit ? (
          <>
            <div className="comment-content" ref={commentRef}>
              {data.content}
            </div>
            {showExtendButton && (
              <div
                className="extend-comment"
                onClick={() => setIsExtend(!isExtend)}
              >
                {isExtend ? 'อ่านน้อยลง' : 'อ่านเพิ่มเติม'}
              </div>
            )}
          </>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <TextField
              type="text"
              placeholder="พิมพ์ข้อความ..."
              {...register('comment')}
              multiline
              rows={2}
              error={Boolean(hookFormErrors.comment)}
              helperText={hookFormErrors.comment?.message as ReactNode}
            />
            <div className="w-full flex items-center gap-2 mt-4 justify-end">
              <Button
                type="button"
                variant="outlined"
                color="blueGrey"
                onClick={async () => {
                  if (!isSubmitting) {
                    setIsEdit(false);
                    setValue('comment', data.content);
                  }
                }}
                sx={{
                  minHeight: '32px !important',
                  height: '32px !important',
                  minWidth: '62px !important',
                }}
              >
                <span className="mt-[2px]">ยกเลิก</span>
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="dark"
                disabled={isSubmitting || isEmpty(watch('comment'))}
                onClick={async () => {
                  if (!isSubmitting) {
                    //
                  }
                }}
                sx={{
                  minHeight: '32px !important',
                  height: '32px !important',
                  minWidth: '62px !important',
                }}
              >
                {isSubmitting ? (
                  <CircularProgress size={20} />
                ) : (
                  <span className="mt-[2px]">แก้ไข</span>
                )}
              </Button>
            </div>
          </form>
        ))}
      {data.mediaType.id === 2 && (
        <div className="comment-image-grid">
          <Image
            src={data.content}
            alt=""
            width={500}
            height={500}
            draggable={false}
            className="comment-image"
          />
        </div>
      )}
    </ArtworkCommentStyle>
  );
};

export default ArtworkComment;
