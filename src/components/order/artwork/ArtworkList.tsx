import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import ArtworkProgress from '@/components/order/artwork/ArtworkProgress';
import { LoadingFadein } from '@/styles/share.styled';
import { useRouter } from 'next/router';

const ArtworkListStyle = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 4px;
  animation: ${LoadingFadein} 0.3s ease-in;
  border-radius: 16px;
  .header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 24px;
    padding: 16px;
    flex-wrap: wrap;
    row-gap: 16px;
    .right-side {
      display: flex;
      align-items: center;
      column-gap: 8px;
      width: 100%;
      justify-content: end;
      button {
        position: absolute;
        top: 16px;
        right: -8px;
        &:nth-child(1) {
          right: 116px;
        }
      }
    }
  }
`;
type Props = {
  handleClickSendProduction: () => void;
  data: any;
};
const ArtworkList = ({ handleClickSendProduction, data }: Props) => {
  const router = useRouter();
  const { orderId } = router.query;
  const [isCompleted, setIsCompleted] = useState<boolean>(false);
  useEffect(() => {
    const numOfStep = data.layDataArtwork.artworkConfig.length;
    setIsCompleted(data.layDataArtwork.artworkConfig[numOfStep - 1].isSuccess);
  }, [data]);
  return (
    <ArtworkListStyle>
      <div className="header">
        <div className="right-side">
          <ActionButton
            variant="outlined"
            color="blueGrey"
            icon={
              <Image
                src={'/icons/icon-export-notes.svg'}
                width={24}
                height={24}
                alt=""
              />
            }
            text="รายละเอียด"
            borderRadius="8px"
            onClick={async () => {
              await router.push(
                `/orders/${orderId}/artwork/${data.layDataArtwork.id}`
              );
            }}
          />
          <ActionButton
            variant="contained"
            color="Hon"
            // ส่งผลิตแล้ว
            text={`ส่งผลิตสินค้า`}
            borderRadius="8px"
            disabled={
              !isCompleted ||
              data.layDataArtwork.layDataStatus.name === 'การผลิตสินค้า'
            }
            onClick={() => {
              if (
                isCompleted ||
                data.layDataArtwork.layDataStatus.name !== 'การผลิตสินค้า'
              ) {
                handleClickSendProduction();
              }
            }}
          />
        </div>
      </div>
      <ArtworkProgress processData={data.layDataArtwork} />
    </ArtworkListStyle>
  );
};

export default ArtworkList;
