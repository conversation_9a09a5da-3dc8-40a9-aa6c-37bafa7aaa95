import React from 'react';
import styled from 'styled-components';
import ArtworkStep from '@/components/order/artwork/ArtworkStep';

const ArtworkProgressStyle = styled.div`
  width: 100%;
  display: flex;
  column-gap: 8px;
  padding: 0 16px 16px;
`;
type Props = {
  processData: any;
};
const ArtworkProgress = ({ processData }: Props) => {
  return (
    <ArtworkProgressStyle>
      {processData.artworkConfig.map((processItem: any, index: number) => {
        return (
          <ArtworkStep data={processData} dataItem={processItem} key={index} />
        );
      })}
    </ArtworkProgressStyle>
  );
};

export default ArtworkProgress;
