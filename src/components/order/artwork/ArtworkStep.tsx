import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import { dateThaiFormat } from '@/utils/date';

const ArtworkStepStyle = styled.div`
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  row-gap: 8px;

  .progress-bar {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #16d5c5;
    @keyframes blink {
      0% {
        filter: brightness(1);
        box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.3);
      }
      50% {
        filter: brightness(1.2);
      }
      100% {
        filter: brightness(1);
        box-shadow: 0 0 0 6px rgba(0, 0, 0, 0);
      }
    }
    &.pending {
      background: #fbc02d;
      animation: blink 1.8s ease-in-out infinite forwards;
      position: relative;
    }
    &.disabled {
      background: #f5f7f8;
    }
  }
  .topic-wrap {
    display: flex;
    align-items: center;
    column-gap: 8px;
    .topic {
      font-weight: 600;
      &.pending {
        color: #fbc02d;
      }
      &.disabled {
        color: #cfd8dc;
      }
    }
  }
  .detail {
    font-size: 12px;
    &.disabled {
      color: #cfd8dc;
    }
  }
`;
type Props = {
  data: any;
  dataItem: any;
};
const ArtworkStep = ({ data, dataItem }: Props) => {
  const [isCurrent, setIsCurrent] = useState<boolean>(false);
  useEffect(() => {
    setIsCurrent(data.artworkStatusId === dataItem.artworkStatusId);
  }, [dataItem]);
  return (
    <ArtworkStepStyle>
      <div
        className={`progress-bar ${
          dataItem.isSuccess ? '' : isCurrent ? 'pending' : 'disabled'
        }`}
      />
      <div className="topic-wrap">
        {dataItem.isSuccess ? (
          <Image src={'/icons/task-done.svg'} alt="" width={24} height={24} />
        ) : isCurrent ? (
          <Image src={'/icons/schedule.svg'} alt="" width={24} height={24} />
        ) : null}
        <div
          className={`topic ${
            dataItem.isSuccess ? '' : isCurrent ? 'pending' : 'disabled'
          }`}
        >
          {dataItem.artworkStatus.name}
        </div>
      </div>
      {dataItem.isSuccess ||
        (isCurrent && (
          <div className={`detail ${isCurrent ? 'disabled' : ''}`}>
            {dataItem.isSuccess
              ? dateThaiFormat(dataItem.modifiedDate)
              : 'กำลังดำเนินการ'}
          </div>
        ))}
    </ArtworkStepStyle>
  );
};

export default ArtworkStep;
