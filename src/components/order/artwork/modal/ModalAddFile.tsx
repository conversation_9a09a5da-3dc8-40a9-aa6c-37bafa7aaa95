import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import LinkIcon from '@mui/icons-material/Link';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

type Props = {
  open: boolean;
  artworkConfigId: number;
  initialValues: any;
  handleClose: () => void;
  reFetchArtworkDetail: () => void;
};

const validationSchema = yup.object({
  title: yup.string().required('กรุณากรอกชื่อไฟล์'),
  link: yup.string().required('กรุณากรอกลิงก์ดาวน์โหลด'),
});

const ModalAddFile = ({
  open,
  handleClose,
  reFetchArtworkDetail,
  artworkConfigId,
  initialValues,
}: Props) => {
  // const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const {
    register,
    handleSubmit,
    reset,
    // watch,
    // setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValues,
  });
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (initialValues.artworkDetailId) {
      const sendValue = {
        ...values,
        artworkConfigId,
      };
      delete sendValue.artworkDetailId;
      const res = await apiArtwork.updateArtworkDetail(
        sendValue,
        values.artworkDetailId
      );
      if (!res.isError) {
        handleClose();
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
        reFetchArtworkDetail();
        handleClose();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.error.response.data.message,
            severity: 'error',
          })
        );
      }
    } else {
      const res = await apiArtwork.createArtworkDetail({
        ...values,
        artworkConfigId,
      });
      if (!res.isError) {
        handleClose();
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
        reFetchArtworkDetail();
        handleClose();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.error.response.data.message,
            severity: 'error',
          })
        );
      }
    }
    setSubmitting(false);
  };
  useEffect(() => {
    if (open) {
      reset(initialValues);
    }
  }, [open]);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`ไฟล์ผลิต`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div>
                <p>ชื่อไฟล์</p>
                <TextField
                  type="text"
                  placeholder="ชื่อไฟล์"
                  {...register('title')}
                  error={Boolean(hookFormErrors.title)}
                  helperText={
                    hookFormErrors.title
                      ? (hookFormErrors.title.message as ReactNode)
                      : ''
                  }
                />
              </div>
              <div>
                <p>รายละเอียด</p>
                <TextField
                  {...register('description')}
                  multiline
                  rows={5}
                  placeholder={'อธิบายรายละเอียด'}
                />
              </div>
              <div>
                <p>ลิงก์ดาวน์โหลด</p>
                <TextField
                  type="text"
                  placeholder="https://"
                  {...register('link')}
                  error={Boolean(hookFormErrors.link)}
                  helperText={
                    hookFormErrors.link
                      ? (hookFormErrors.link.message as ReactNode)
                      : ''
                  }
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LinkIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="text-error w-full flex justify-center text-center !m-0 !mt-[24px]">
                การเพิ่มไฟล์ส่งผลิต หากคุณยืนยันไฟล์ส่งผลิตแล้ว
                จำเป็นจะต้องเปลี่ยนแปลงสถานะ
                คุณจะต้องทำการยืนยันไฟล์ส่งผลิตอีกครั้ง
              </div>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalAddFile;
