import {
  <PERSON><PERSON>,
  Circular<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  FormHelperText,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import Image from 'next/image';
import styled from 'styled-components';
import apiArtwork from '@/services/order/artwork';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { isEmpty } from 'lodash';
import { validateImageFiles } from '@/utils/size';

const ImageZoneStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  margin-top: 24px;
  .image {
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    background: #f5f7f8;
    cursor: pointer;
    &:hover {
      .backdrop {
        opacity: 1;
        visibility: visible;
        transition: 0.15s ease-out;
      }
    }
    .image-bg {
      width: 100%;
      height: 100%;
      background: #f5f7f8;
      z-index: 1;
      position: absolute;
    }
    img {
      object-fit: cover;
      z-index: 2;
    }
    .backdrop {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: 5;
      visibility: hidden;
      opacity: 0;
      background: rgba(38, 50, 56, 0.4);
      cursor: default;
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.15s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 8px;
        right: 8px;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
  }
`;
type Props = {
  open: boolean;
  handleClose: () => void;
  reFetchArtworkDetail: () => void;
  initialValues: any;
};

const validationSchema = yup.object({
  title: yup.string().required('กรุณากรอกหัวข้ออาร์ตเวิร์ก'),
});

const ModalAddArtwork = ({
  open,
  handleClose,
  reFetchArtworkDetail,
  initialValues,
}: Props) => {
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [errorImageUpload, setErrorImageUpload] = useState<any>({
    status: false,
    message: '',
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    // setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValues,
  });
  useEffect(() => {
    if (open) {
      reset(initialValues);
    }
  }, [open]);
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    const res = await apiArtwork.updateArtworkDetail(
      values,
      values.artworkDetailId
    );
    if (!res.isError) {
      handleClose();
      reFetchArtworkDetail();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setSubmitting(false);
  };

  const handleImageUpload = async (files: File[]) => {
    if (files) {
      const formData = new FormData();
      formData.append('file', files[0]);
      formData.append('mediaTypeId', '2');
      formData.append('artworkDetailId', watch('artworkDetailId'));
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };
      const res = await apiArtwork.uploadMediaArtwork(formData, config);
      if (!res.isError) {
        reFetchArtworkDetail();
      }
    }
  };
  const handleDeleteArtworkImage = async (image: any) => {
    const res = await apiArtwork.deleteArtworkMedia(image.id);
    if (!res.isError) {
      reFetchArtworkDetail();
    }
  };
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`แบบอาร์ตเวิร์ก`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div>
                <p>หัวข้อ</p>
                <TextField
                  type="text"
                  placeholder="หัวข้ออาร์ตเวิร์ก"
                  {...register('title')}
                  error={Boolean(hookFormErrors.title)}
                  helperText={
                    hookFormErrors.title
                      ? (hookFormErrors.title.message as ReactNode)
                      : ''
                  }
                />
              </div>
              <div>
                <p>รายละเอียด</p>
                <TextField
                  {...register('description')}
                  multiline
                  rows={5}
                  placeholder={'รายละเอียด'}
                />
              </div>
              <div className="flex justify-between items-center mt-[24px]">
                <div>อาร์ตเวิร์ก</div>
                <label
                  onClick={() => {
                    fileInputRef.current?.click();
                  }}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    style={{ display: 'none' }}
                    onChange={async (event: any) => {
                      const { files } = event.target;
                      const validationResult = await validateImageFiles(
                        files,
                        25,
                        true,
                        true
                      );
                      if (validationResult.status) {
                        const newFiles = Array.from(validationResult.files);
                        await handleImageUpload(newFiles);
                        setErrorImageUpload({
                          status: false,
                          message: '',
                        });
                      } else {
                        setErrorImageUpload(validationResult.message);
                      }
                    }}
                  />
                  <ActionButton
                    variant="outlined"
                    color="blueGrey"
                    icon={<AddCircle />}
                    text="อัปโหลดรูป"
                  />
                </label>
              </div>
              {!isEmpty(initialValues.artworkImages) && (
                <ImageZoneStyle>
                  {initialValues.artworkImages.map(
                    (image: { url: string }, index: number) => (
                      <div className="image" key={index}>
                        <div className="backdrop">
                          <div
                            className="remove-btn"
                            onClick={async (
                              event: React.MouseEvent<HTMLDivElement>
                            ) => {
                              event.preventDefault();
                              await handleDeleteArtworkImage(image);
                            }}
                          >
                            <Image
                              src="/icons/delete-white.svg"
                              width={24}
                              height={24}
                              alt="Delete icon"
                            />
                          </div>
                        </div>
                        <div className="image-bg" />
                        <Image
                          src={image.url}
                          fill
                          alt={`Artwork ${index + 1}`}
                        />
                      </div>
                    )
                  )}
                </ImageZoneStyle>
              )}

              {errorImageUpload.status && (
                <FormHelperText
                  error
                  sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    paddingTop: '24px',
                    '&.MuiFormHelperText-root.Mui-error': {
                      margin: '0 !important',
                    },
                  }}
                >
                  {errorImageUpload.message}
                </FormHelperText>
              )}
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalAddArtwork;
