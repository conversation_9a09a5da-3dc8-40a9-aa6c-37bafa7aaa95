import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormHelperText,
  InputAdornment,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import LinkIcon from '@mui/icons-material/Link';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { validateImageFiles } from '@/utils/size';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { isEmpty, isNull } from 'lodash';
import Image from 'next/image';
import styled from 'styled-components';
import { ArtworkFileStyled } from '@/components/order/artwork/modal/ModalDataArtworkDetail';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import { AnimatePresence, motion } from 'framer-motion';
import { motionProgress } from '@/utils/motion/motion-config';

type Props = {
  open: boolean;
  artworkConfigId: number;
  initialValues: any;
  handleClose: () => void;
  reFetchArtworkDetail: () => void;
};

const ImageZoneStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  margin-top: 24px;
  .image {
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    background: #f5f7f8;
    cursor: pointer;
    &:hover {
      .backdrop {
        opacity: 1;
        visibility: visible;
        transition: 0.15s ease-out;
      }
    }
    .image-bg {
      width: 100%;
      height: 100%;
      background: #f5f7f8;
      z-index: 1;
      position: absolute;
    }
    img {
      object-fit: cover;
      z-index: 2;
    }
    .backdrop {
      position: absolute;
      z-index: 5;
      visibility: hidden;
      opacity: 0;
      background: rgba(38, 50, 56, 0.4);
      cursor: default;
      top: 0;
      right: 0;
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.15s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 8px;
        right: 8px;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
  }
`;

const UploadProgressStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  margin-top: 24px;
  position: absolute;
  bottom: -20px;
  @keyframes loading {
    0% {
      background: #16d5c5;
    }
    50% {
      background: #0bf8e4;
    }
    100% {
      background: #16d5c5;
    }
  }

  .progress-bar {
    animation: loading 1s linear infinite;
    height: 2px;
    position: relative;
    transition: 0.15s linear;

    .text {
      font-size: 14px;
      position: absolute;
      top: -20px;
      right: 0;
      color: rgb(144, 164, 174);
      white-space: nowrap;
    }
  }
`;

const validationSchema = yup.object({
  title: yup.string().required('กรุณากรอกชื่อรายการ'),
  link: yup.string().required('กรุณากรอกลิงก์ดาวน์โหลด'),
});

const ModalAddDataArtwork = ({
  open,
  handleClose,
  reFetchArtworkDetail,
  artworkConfigId,
  initialValues,
}: Props) => {
  // const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const artworkFileInputRef = useRef<HTMLInputElement>(null);
  const [errorImageUpload, setErrorImageUpload] = useState<any>({
    status: false,
    message: '',
  });
  const [progressList, setProgressList] = useState<number[]>([]);
  const totalProgress =
    progressList.length > 0
      ? Math.round(
          progressList.reduce((a, b) => a + b, 0) / progressList.length
        )
      : 0;
  const [files, setFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [artworkFile, setArtworkFile] = useState<File | null>(null);
  const [progressUploadArtworkFile, setProgressUploadArtworkFile] =
    useState<number>(0);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: initialValues,
  });
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (initialValues.artworkDetailId) {
      const sendValue = {
        ...values,
        artworkConfigId,
      };
      delete sendValue.artworkDetailId;
      const res = await apiArtwork.updateArtworkDetail(
        sendValue,
        values.artworkDetailId
      );
      if (!res.isError) {
        handleClose();
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
        reFetchArtworkDetail();
        handleClose();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.error.response.data.message,
            severity: 'error',
          })
        );
      }
    } else {
      const res = await apiArtwork.createArtworkDetail({
        ...values,
        artworkConfigId,
      });
      if (!res.isError) {
        setValue('artworkDetailId', res.data.id);
        await uploadMedia(files, 'image');
        if (!isNull(artworkFile)) {
          await uploadArtworkFile(artworkFile);
        }
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
        reFetchArtworkDetail();
        handleClose();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.error.response.data.message,
            severity: 'error',
          })
        );
      }
    }
    setSubmitting(false);
  };
  useEffect(() => {
    if (open) {
      reset(initialValues);
      setFiles([]);
      setPreviewUrls([]);
    }
  }, [open]);

  const uploadArtworkFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('artworkDetailId', watch('artworkDetailId'));
    formData.append('mediaTypeId', '5'); // Assuming 5 is any file type

    const config = {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent: ProgressEvent) => {
        const percent = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
        setProgressUploadArtworkFile(percent);
      },
    };

    const res = await apiArtwork.uploadMediaArtwork(formData, config);

    if (!res.isError) {
      reFetchArtworkDetail();
      setArtworkFile(null);
      setProgressUploadArtworkFile(0);
    }
  };

  const uploadMedia = async (incomingFiles: File[], fileType: string) => {
    const newProgressList = [...progressList];
    const totalFiles = incomingFiles.length;

    for (let i = 0; i < totalFiles; i++) {
      const file = incomingFiles[i];
      const formData = new FormData();
      formData.append('file', file);
      formData.append('artworkDetailId', watch('artworkDetailId'));

      if (fileType === 'image') {
        formData.append('mediaTypeId', '2');
      }

      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent: ProgressEvent) => {
          const percent = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );

          newProgressList[i] = percent;
          setProgressList([...newProgressList]);
        },
      };

      const res = await apiArtwork.uploadMediaArtwork(formData, config);

      if (!res.isError) {
        reFetchArtworkDetail();
        if (fileType === 'image') {
          setErrorImageUpload({ status: false, message: '' });
        }
      }
    }
    setProgressList([]);
  };

  const handleFileUpload = async (incomingFiles: File[], fileType: string) => {
    if (!incomingFiles || incomingFiles.length === 0) return;

    if (initialValues.artworkDetailId) {
      await uploadMedia(incomingFiles, fileType);
    } else {
      setFiles((prev) => [...prev, ...incomingFiles]);
    }
  };

  useEffect(() => {
    if (initialValues.artworkDetailId || files.length === 0) return;

    const objectUrls = files.map((file) => URL.createObjectURL(file));
    setPreviewUrls(objectUrls);

    // cleanup URLs เมื่อ component ถูก unmount หรือ files เปลี่ยน
    return () => {
      objectUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, [files, initialValues.artworkDetailId]);

  const handleDeleteArtworkMedia = async (image: any) => {
    const res = await apiArtwork.deleteArtworkMedia(image.id);
    if (!res.isError) {
      reFetchArtworkDetail();
    }
  };

  const handleRemovePreviewImage = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));

    setPreviewUrls((prev) => {
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const getFileName = (url: string): string => {
    try {
      return url.split('/').pop() || '';
    } catch {
      return '';
    }
  };

  console.log('initialValues', initialValues);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`ข้อมูลสินค้า`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div>
                <p>หัวข้อ</p>
                <TextField
                  type="text"
                  placeholder="ชื่อรายการ"
                  {...register('title')}
                  error={Boolean(hookFormErrors.title)}
                  helperText={
                    hookFormErrors.title
                      ? (hookFormErrors.title.message as ReactNode)
                      : ''
                  }
                />
              </div>
              <div>
                <p>รายละเอียด</p>
                <TextField
                  {...register('description')}
                  multiline
                  rows={5}
                  placeholder={'รายละเอียด'}
                />
              </div>
              <div>
                <p>ลิงก์ดาวน์โหลด</p>
                <TextField
                  type="text"
                  placeholder="https://"
                  {...register('link')}
                  error={Boolean(hookFormErrors.link)}
                  helperText={
                    hookFormErrors.link
                      ? (hookFormErrors.link.message as ReactNode)
                      : ''
                  }
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LinkIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="flex justify-between items-center mt-[24px]">
                <div
                  style={{
                    fontWeight: 600,
                  }}
                >
                  รูปตัวอย่างงานออกแบบ
                </div>
                <label
                  onClick={() => {
                    fileInputRef.current?.click();
                  }}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    style={{ display: 'none' }}
                    onChange={async (event: any) => {
                      const { files } = event.target;
                      const validationResult = await validateImageFiles(
                        files,
                        25,
                        true,
                        true
                      );
                      if (validationResult.status) {
                        const newFiles = Array.from(validationResult.files);
                        await handleFileUpload(newFiles, 'image');
                        setErrorImageUpload({
                          status: false,
                          message: '',
                        });
                      } else {
                        setErrorImageUpload({
                          status: true,
                          message: validationResult.message,
                        });
                      }
                    }}
                  />
                  <ActionButton
                    variant="outlined"
                    color="blueGrey"
                    icon={<AddCircle />}
                    text="อัปโหลด"
                  />
                </label>
              </div>
              <div className="w-full relative">
                {!isEmpty(initialValues.artworkImages) ? (
                  <ImageZoneStyle>
                    {initialValues.artworkImages.map(
                      (image: { url: string }, index: number) => (
                        <div className="image" key={index}>
                          <div className="backdrop">
                            <div
                              className="remove-btn"
                              onClick={async (
                                event: React.MouseEvent<HTMLDivElement>
                              ) => {
                                event.preventDefault();
                                await handleDeleteArtworkMedia(image);
                              }}
                            >
                              <Image
                                src="/icons/delete-white.svg"
                                width={24}
                                height={24}
                                alt="Delete icon"
                              />
                            </div>
                          </div>
                          <div className="image-bg" />
                          <Image
                            src={image.url}
                            fill
                            alt={`Artwork ${index + 1}`}
                          />
                        </div>
                      )
                    )}
                  </ImageZoneStyle>
                ) : (
                  <ImageZoneStyle>
                    {previewUrls.map((image: string, index: number) => (
                      <div className="image" key={index}>
                        <div className="backdrop">
                          <div
                            className="remove-btn"
                            onClick={(event) => {
                              event.preventDefault();
                              handleRemovePreviewImage(index);
                            }}
                          >
                            <Image
                              src="/icons/delete-white.svg"
                              width={24}
                              height={24}
                              alt="Delete icon"
                            />
                          </div>
                        </div>
                        <div className="image-bg" />
                        <Image src={image} fill alt={`Artwork ${index + 1}`} />
                      </div>
                    ))}
                  </ImageZoneStyle>
                )}
                {totalProgress !== 0 && (
                  <UploadProgressStyle>
                    <div
                      className="progress-bar"
                      style={{
                        minWidth: '24%',
                        width: `${totalProgress}%`,
                      }}
                    >
                      <div className="text">
                        กำลังอัปโหลด...{totalProgress}%
                      </div>
                    </div>
                  </UploadProgressStyle>
                )}
              </div>

              {errorImageUpload.status && (
                <FormHelperText
                  error
                  sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    paddingTop: '24px',
                    '&.MuiFormHelperText-root.Mui-error': {
                      margin: '0 !important',
                    },
                  }}
                >
                  {errorImageUpload.message}
                </FormHelperText>
              )}

              <div className="flex justify-between items-center mt-[24px]">
                <div
                  style={{
                    fontWeight: 600,
                  }}
                >
                  ไฟล์อาร์ตเวิร์ก
                </div>
                <div>
                  <input
                    ref={artworkFileInputRef}
                    type="file"
                    style={{ display: 'none' }}
                    onChange={async (
                      event: React.ChangeEvent<HTMLInputElement>
                    ) => {
                      const file = event.target.files?.[0];
                      if (!file) return;
                      if (file.size > 10 * 1024 * 1024) {
                        dispatch(
                          setSnackBar({
                            status: true,
                            text: 'ไฟล์ต้องมีขนาดไม่เกิน 10MB',
                            severity: 'error',
                          })
                        );
                        return;
                      }
                      if (initialValues.artworkDetailId) {
                        await uploadArtworkFile(file);
                      } else {
                        setArtworkFile(file);
                      }
                      event.target.value = '';
                    }}
                  />
                  <ActionButton
                    variant="outlined"
                    color="blueGrey"
                    icon={<AddCircle />}
                    text="อัปโหลด"
                    disabled={!isEmpty(initialValues.artworkFile)}
                    onClick={() => {
                      if (isEmpty(initialValues.artworkFile)) {
                        artworkFileInputRef.current?.click();
                      }
                    }}
                  />
                </div>
              </div>
              <ArtworkFileStyled style={{ marginTop: '24px' }}>
                <AnimatePresence mode="sync" initial={false}>
                  {progressUploadArtworkFile && (
                    <motion.div
                      key="progress-bar"
                      className="progress-bar"
                      custom={progressUploadArtworkFile}
                      variants={motionProgress}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                    />
                  )}
                </AnimatePresence>
                {artworkFile || !isEmpty(initialValues.artworkFile) ? (
                  <>
                    <div className="file">
                      <div className="name">
                        {artworkFile?.name ||
                          getFileName(initialValues.artworkFile.url)}
                      </div>
                    </div>
                    <div
                      className="download-btn"
                      onClick={async () => {
                        setArtworkFile(null);
                        if (!isEmpty(initialValues.artworkFile)) {
                          await handleDeleteArtworkMedia(
                            initialValues.artworkFile
                          );
                        }
                      }}
                    >
                      <SvgDeleteIcon />
                    </div>
                  </>
                ) : (
                  <div
                    className="w-full flex justify-center"
                    style={{
                      color: '#B0BEC5',
                      maxWidth: '100%',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    ไม่มีไฟล์อาร์ตเวิร์กที่อัปโหลด
                  </div>
                )}
              </ArtworkFileStyled>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

// @ts-ignore
export default ModalAddDataArtwork;
