import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import LinkIcon from '@mui/icons-material/Link';
import apiArtwork from '@/services/order/artwork';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

type Props = {
  open: boolean;
  handleClose: () => void;
  handleReFetchArtworkDetail: () => void;
  data: any;
};

const validationSchema = yup.object({
  title: yup.string().required('กรุณากรอกชื่อ'),
  link: yup.string().required('กรุณากรอกลิงก์ดาวน์โหลด'),
});

const ModalConfirmArtwork = ({
  open,
  handleClose,
  data,
  handleReFetchArtworkDetail,
}: Props) => {
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const {
    register,
    handleSubmit,
    reset,
    // watch,
    // setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: '',
      link: '',
    },
  });
  const onSubmit = async (values: any) => {
    setSubmitting(true);
    const sendValue = {
      ...values,
      artworkDetailId: data.id,
    };
    const res = await apiArtwork.approvalArtworkDesign(sendValue);
    if (!res.isError) {
      handleClose();
      dispatch(
        setSnackBar({
          status: true,
          text: 'อนุมัติอาร์ตเวิร์กสำเร็จ',
          severity: 'success',
        })
      );
      handleReFetchArtworkDetail();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
    setSubmitting(false);
  };
  useEffect(() => {
    if (open) {
      reset();
    }
  }, [open]);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`การอนุมัติ อาร์ตเวิร์ก`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div>
                <p>สร้างชื่อตัวอย่างสินค้า</p>
                <TextField
                  type="text"
                  placeholder="ชื่อตัวอย่างสินค้า"
                  {...register('title')}
                  error={Boolean(hookFormErrors.title)}
                  helperText={
                    hookFormErrors.title
                      ? (hookFormErrors.title.message as ReactNode)
                      : ''
                  }
                />
              </div>
              <div>
                <p>ลิงก์ไฟล์อาร์ตเวิร์ก</p>
                <TextField
                  type="text"
                  placeholder="https://"
                  {...register('link')}
                  error={Boolean(hookFormErrors.link)}
                  helperText={
                    hookFormErrors.link
                      ? (hookFormErrors.link.message as ReactNode)
                      : ''
                  }
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LinkIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="w-full flex justify-center">
                <div className="text-error w-[80%] flex justify-center text-center !m-0 !mt-[24px]">
                  *คุณต้องการอนุมัติอาร์ตเวิร์ก “{data.title}” เพื่อส่งสร้าง
                  ตัวอย่างสินค้าเพื่อตรวจสอบความถูกต้องก่อนส่งผลิต
                </div>
              </div>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      sx={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </Button>
              </div>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalConfirmArtwork;
