import { Dialog, DialogContent, Tooltip } from '@mui/material';
import React from 'react';
import 'dayjs/locale/th';
import FormModal from '@/components/global/form/FormModal';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import LinkIcon from '@mui/icons-material/Link';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import apiProxyImage from '@/services/apiProxyImage';
import SvgDownloadIcon from '@/components/svg-icon/SvgDownloadIcon';

const ModalDataArtworkDetailStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  p {
    font-weight: 600;
  }
  .value {
    //
  }
  .url-action-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    .url-action {
      height: 40px;
      border-radius: 8px;
      background: #f5f7f8;
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
      overflow: hidden;
      border: 1px dashed #dbe2e5;
      column-gap: 8px;
      .url-icon {
        rotate: -45deg;
        font-size: 20px;
      }
      .url {
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
      }
      .copy-icon {
        font-size: 20px;
        cursor: pointer;
      }
    }
    .btn-wrap {
      width: 172px;
    }
  }
`;
export const ArtworkFileStyled = styled.div`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 24px;
  padding: 14px 16px;
  height: 62px;
  position: relative;
  overflow: hidden;
  .file {
    overflow: hidden;
    .name {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: 100%;
      font-weight: 600;
    }
    .size {
      color: #b0bec5;
    }
  }
  .download-btn {
    cursor: pointer;
    display: flex;
    align-content: center;
    svg {
      margin-top: 2px;
    }
  }
  .progress-bar {
    width: 0;
    height: 100%;
    position: absolute;
    left: 0;
    background: rgba(143, 217, 255, 0.36);
  }
`;
const ImageZoneStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  .image {
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    background: #f5f7f8;
    cursor: pointer;
    &:hover {
      .backdrop {
        opacity: 1;
        visibility: visible;
        transition: 0.15s ease-out;
      }
    }
    .image-bg {
      width: 100%;
      height: 100%;
      background: #f5f7f8;
      z-index: 1;
      position: absolute;
    }
    img {
      object-fit: cover;
      z-index: 2;
    }
    .backdrop {
      position: absolute;
      z-index: 5;
      visibility: hidden;
      opacity: 0;
      background: rgba(38, 50, 56, 0.4);
      cursor: default;
      top: 0;
      right: 0;
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.15s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 8px;
        right: 8px;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
  }
`;

type Props = {
  open: boolean;
  handleClose: () => void;
  data: any;
};
const ModalDataArtworkDetail = ({ open, handleClose, data }: Props) => {
  const handleCopyUrl = async () => {
    await navigator.clipboard.writeText(data.link);
  };

  const handleDownloadAllImages = async () => {
    const images = data.artworkImages;
    if (!images || images.length === 0) return;

    const zip = new JSZip();
    const folder = zip.folder('artwork_images');

    const promises = images.map(async (image: any, index: number) => {
      const blob = await apiProxyImage.fetchImageBlob(image.url);
      if (blob) {
        const fileName = `artwork_${image.id || index + 1}.webp`;
        folder?.file(fileName, blob);
      }
    });

    await Promise.all(promises);
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, `artwork_images_${data.id}.zip`);
  };
  const getFileName = (url: string): string => {
    try {
      return url.split('/').pop() || '';
    } catch {
      return '';
    }
  };

  console.log('data', data);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`รายละเอียดข้อมูล`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <form>
              <ModalDataArtworkDetailStyle>
                <div>
                  <p>หัวข้อ</p>
                  <div className="value">{data.title}</div>
                </div>
                <div>
                  <p>รายละเอียด</p>
                  <div className="value">{data.description || '-'}</div>
                </div>
                <div className="flex justify-between items-center mt-[24px] mb-[8px]">
                  <div
                    style={{
                      fontWeight: 600,
                    }}
                  >
                    รูปตัวอย่างงานออกแบบ
                  </div>
                  <div
                    style={{
                      textDecoration: !isEmpty(data.artworkImages)
                        ? 'underline'
                        : 'none',
                      color: !isEmpty(data.artworkImages) ? '' : '#b0bec5',
                      cursor: !isEmpty(data.artworkImages)
                        ? 'pointer'
                        : 'initial',
                    }}
                    onClick={async () => {
                      if (!isEmpty(data.artworkImages)) {
                        await handleDownloadAllImages();
                      }
                    }}
                  >
                    {!isEmpty(data.artworkImages)
                      ? 'ดาวน์โหลดรูปทั้งหมด'
                      : 'ไม่มีรูปภาพ'}
                  </div>
                </div>
                {!isEmpty(data.artworkImages) && (
                  <ImageZoneStyle>
                    {data.artworkImages.map((image: any, index: number) => (
                      <div className="image" key={index}>
                        <div className="backdrop">
                          <div
                            className="remove-btn !bg-[#fff]"
                            onClick={async (event) => {
                              event.preventDefault();
                              const blob = await apiProxyImage.fetchImageBlob(
                                image.url
                              );
                              if (blob) {
                                const fileName = `artwork_${image.id}.jpg`;
                                saveAs(blob, fileName);
                              }
                            }}
                          >
                            <Image
                              src="/icons/icon-download.svg"
                              width={24}
                              height={24}
                              alt="download icon"
                            />
                          </div>
                        </div>
                        <div className="image-bg" />
                        <Image
                          src={image.url}
                          fill
                          alt={`Artwork ${index + 1}`}
                        />
                      </div>
                    ))}
                  </ImageZoneStyle>
                )}
                <div className="flex justify-between items-center mt-[24px] mb-[8px]">
                  <div
                    style={{
                      fontWeight: 600,
                    }}
                  >
                    ไฟล์อาร์ตเวิร์ก
                  </div>
                  <div
                    style={{
                      color: data.artworkFile ? '' : '#b0bec5',
                    }}
                  >
                    {data.artworkFile ? '' : 'ไม่มีไฟล์'}
                  </div>
                </div>
                {data.artworkFile && (
                  <ArtworkFileStyled>
                    <div className="file">
                      <div className="name">
                        {getFileName(data.artworkFile.url)}
                      </div>
                    </div>
                    <div className="download-btn">
                      <a
                        href={data.artworkFile.url}
                        download={getFileName(data.artworkFile.url)}
                        rel="noopener noreferrer"
                      >
                        <SvgDownloadIcon />
                      </a>
                    </div>
                  </ArtworkFileStyled>
                )}
                <div>
                  <p>ลิงก์ดาวน์โหลด</p>
                  <div className="url-action-wrapper">
                    <div className="url-action">
                      <LinkIcon className="url-icon" />
                      <div className="url">
                        {data.linkExtra || data.link || '-'}
                      </div>
                      {(data.linkExtra || data.link) && (
                        <Tooltip title={`คัดลอก`} placement="top" arrow>
                          <ContentCopyIcon
                            className="copy-icon"
                            onClick={() => handleCopyUrl()}
                          />
                        </Tooltip>
                      )}
                    </div>
                    <div
                      onClick={() => {
                        window.open(data.linkExtra || data.link, '_blank');
                      }}
                      className={'btn-wrap'}
                    >
                      <ActionButton
                        variant="contained"
                        color="dark"
                        icon={<OpenInNewIcon />}
                        text="Open link"
                        borderRadius={'8px'}
                        disabled={
                          data.index === 0 ? !data.linkExtra : !data.link
                        }
                        fullWidth
                      />
                    </div>
                  </div>
                </div>
              </ModalDataArtworkDetailStyle>
            </form>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalDataArtworkDetail;
