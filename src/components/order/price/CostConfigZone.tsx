import React from 'react';
import styled from 'styled-components';
import { useAppSelector } from '@/store';
import { settingCostSelector } from '@/store/features/selling-price/setting-cost';
import CostConfigGroup from '@/components/order/price/CostConfigGroup';

const CostConfigZoneStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  padding: 0 24px;
  .cost-config-group-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
  }
`;
type Props = {
  resetDirtyTrigger: boolean;
  handleDisableBtn: () => void;
};
const CostConfigZone = ({ resetDirtyTrigger, handleDisableBtn }: Props) => {
  const { costData } = useAppSelector(settingCostSelector);
  return (
    <CostConfigZoneStyle>
      <div className="cost-config-group-wrap">
        {costData.map((costItem: any, index: number) => {
          return (
            <CostConfigGroup
              key={index}
              resetDirtyTrigger={resetDirtyTrigger}
              costItem={costItem}
              handleDisableBtn={handleDisableBtn}
            />
          );
        })}
      </div>
    </CostConfigZoneStyle>
  );
};

export default CostConfigZone;
