import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import ModalServiceCharge from '@/components/order/price/modal/ModalServiceCharge';
import Image from 'next/image';
import ListServiceChargeCalculate from '@/components/order/price/ListServiceChargeCalculate';
import { isEmpty } from 'lodash';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import apiLayDataQuantity from '@/services/order/layDataQuantity';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { useRouter } from 'next/router';
import {
  setCostServiceChargeData,
  settingCostSelector,
} from '@/store/features/selling-price/setting-cost';
import { numberWithCommas } from '@/utils/number';

const ServiceChargeStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 24px;
  margin-top: 40px;
  .header-action {
    width: inherit;
    display: inherit;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    .topic {
      font-size: 20px;
      font-weight: 600;
    }
    .action-group {
      display: inherit;
      align-items: inherit;
      column-gap: 12px;
    }
  }
  .cost-config-group {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    padding: 0 !important;
    .cost-list-wrap {
      width: inherit;
      border: 1px solid #dbe2e5;
      border-radius: 16px;
    }
    .sum-cost-wrap {
      height: 68px;
      display: inherit;
      align-items: center;
      margin-top: 8px;
      .sum-cost-left {
        flex: 2 1 0%;
      }
      .sum-cost-right {
        flex: 1 1 0%;
        min-height: 68px;
        background: #f5f7f8;
        border-radius: 16px;
        display: inherit;
        align-items: inherit;
        justify-content: space-between;
        column-gap: 24px;
        padding: 0 24px;
        min-width: 300px;
        max-width: 100%;
        .cost {
          font-size: 20px;
          font-weight: 600;
          &.wait {
            color: #f9a925;
          }
        }
      }
    }
  }
`;
type Props = {
  resetDirtyTrigger: boolean;
  handleDisableBtn: () => void;
};
const ServiceCharge = ({ resetDirtyTrigger, handleDisableBtn }: Props) => {
  const { costServiceChargeData } = useAppSelector(settingCostSelector);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { layDataQuantityId } = router.query;
  const [open, setOpen] = useState<boolean>(false);
  const [selectedToRemoveIds, setSelectedToRemoveIds] = useState<number[]>([]);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [totalCostServiceCharge, setTotalCostServiceCharge] =
    useState<number>(0);
  const [initialValuesServiceCharge, setInitialValuesServiceCharge] =
    useState<any>({});
  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [init, setInit] = useState<boolean>(false);
  const handleSelectRemove = (id: number) => {
    if (selectedToRemoveIds.includes(id)) {
      const removeId = selectedToRemoveIds.filter(
        (itemId: number) => itemId !== id
      );
      setSelectedToRemoveIds(removeId);
    } else {
      setSelectedToRemoveIds([...selectedToRemoveIds, id]);
    }
  };

  const handleRemoveServiceChargeCache = () => {
    const updateCostServiceChargeData = costServiceChargeData.filter(
      (costServiceChargeDataItem: any) =>
        !selectedToRemoveIds.includes(
          costServiceChargeDataItem.optionCost[0].id
        )
    );
    dispatch(setCostServiceChargeData(updateCostServiceChargeData));
    setSelectedToRemoveIds([]);
  };

  const handleRemoveServiceCharge = async () => {
    setIsSubmitting(true);
    const res = await apiLayDataQuantity.removeServiceCharge({
      delete: selectedToRemoveIds,
    });
    if (!res.isError) {
      handleRemoveServiceChargeCache();
      setOpenDelete(false);
      setIsDirty(true);
      handleDisableBtn();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message || 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setIsSubmitting(false);
  };
  const handleOpenEdit = (data: any) => {
    const { costType, title } = data;
    const values = {
      layDataQuantityId: Number(layDataQuantityId),
      costType,
      title,
      componentId: data.optionCost[0].componentId,
      name: data.optionCost[0].name,
      id: data.optionCost[0].id,
    };
    setInitialValuesServiceCharge(values);
    setOpen(true);
  };
  useEffect(() => {
    if (!isEmpty(costServiceChargeData)) {
      const sumCostServiceCharge = costServiceChargeData.reduce(
        (sum: any, item: any) => {
          return (
            sum +
            item.optionCost.reduce(
              (innerSum: any, option: any) =>
                innerSum + option.priceCostConfigDetail,
              0
            )
          );
        },
        0
      );
      setTotalCostServiceCharge(sumCostServiceCharge);
    }
  }, [costServiceChargeData]);

  useEffect(() => {
    setInit(true);
  }, []);

  useEffect(() => {
    if (init) {
      setIsDirty(false);
    }
  }, [resetDirtyTrigger]);
  console.log('costServiceChargeData', costServiceChargeData);
  return (
    <>
      <ModalServiceCharge
        open={open}
        handleClose={() => {
          setOpen(false);
          setTimeout(() => {
            setInitialValuesServiceCharge({});
          }, 600);
        }}
        initialValues={initialValuesServiceCharge}
        handleDisableBtn={handleDisableBtn}
      />
      <AppModalConfirm
        open={openDelete}
        isReason={false}
        onClickClose={() => {
          //
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete-black.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={`ลบค่าบริการ`}
        confirmDescription={`คุณต้องการที่จะลบค่าบริการที่เลือก?”`}
        loadingConfirm={isSubmitting}
        onConfirm={async () => {
          await handleRemoveServiceCharge();
        }}
      />
      <ServiceChargeStyle>
        <div className="header-action">
          <div className="topic">ค่าบริการ</div>
          <div className="action-group">
            {/* {!isEmpty(selectedToRemoveIds) && ( */}
            {/*  <FadeInStyled> */}
            {/*    <DeleteButton */}
            {/*      onClick={() => { */}
            {/*        setOpenDelete(true); */}
            {/*      }} */}
            {/*    > */}
            {/*      <Image */}
            {/*        src="/icons/delete-white.svg" */}
            {/*        width={24} */}
            {/*        height={24} */}
            {/*        alt="" */}
            {/*      /> */}
            {/*    </DeleteButton> */}
            {/*  </FadeInStyled> */}
            {/* )} */}
            {/* <div */}
            {/*  onClick={() => { */}
            {/*    setOpen(true); */}
            {/*  }} */}
            {/* > */}
            {/*  <ActionButton */}
            {/*    variant="outlined" */}
            {/*    color="blueGrey" */}
            {/*    icon={<AddIcon />} */}
            {/*    text="เลือกค่าบริการ" */}
            {/*    borderRadius={'8px'} */}
            {/*  /> */}
            {/* </div> */}
          </div>
        </div>
        <div className="cost-config-group">
          <div className="cost-list-wrap">
            {costServiceChargeData?.map((item: any, index: number) => {
              return (
                <ListServiceChargeCalculate
                  data={item}
                  key={index}
                  selectedToRemoveIds={selectedToRemoveIds}
                  handleSelectRemove={(id: number) => {
                    handleSelectRemove(id);
                  }}
                  handleOpenEdit={handleOpenEdit}
                  resetDirtyTrigger={resetDirtyTrigger}
                  handleDirty={() => {
                    setIsDirty(true);
                  }}
                  handleDisableBtn={handleDisableBtn}
                />
              );
            })}
          </div>
          <div className="sum-cost-wrap">
            <div className="sum-cost-left" />
            <div className="sum-cost-right">
              <div>ราคารวม</div>
              {totalCostServiceCharge !== 0 && !isDirty && (
                <div className="cost">
                  {numberWithCommas(totalCostServiceCharge, 2)} บาท
                </div>
              )}
              {(totalCostServiceCharge === 0 || isDirty) && (
                <div className="cost wait">รอคำนวณราคา</div>
              )}
            </div>
          </div>
        </div>
      </ServiceChargeStyle>
    </>
  );
};

export default ServiceCharge;
