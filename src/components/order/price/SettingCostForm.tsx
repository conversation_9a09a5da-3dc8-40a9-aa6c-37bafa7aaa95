import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';
import { Button, CircularProgress } from '@mui/material';
import { numberWithCommas } from '@/utils/number';
import ServiceCharge from '@/components/order/price/ServiceCharge';
import CostConfigZone from '@/components/order/price/CostConfigZone';
import ActionButton from '@/components/ActionButton';
import ModalCreateSellingPrice from '@/components/order/price/modal/ModalCreateSellingPrice';
import { useAppSelector } from '@/store';
import { settingCostSelector } from '@/store/features/selling-price/setting-cost';
import apiCostCalculation from '@/services/order/costCalculation';
import { isEmpty } from 'lodash';
import MandatoryServiceCharge from '@/components/order/price/MandatoryServiceCharge';

const SettingCostFormStyle = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  height: calc(100dvh - 64px);
  position: relative;
  @media screen and (max-width: 820px) {
    height: calc(100dvh - 64px - 64px);
    margin-top: 64px;
  }
  form {
    display: flex;
    flex-direction: column;
    .sheet-header {
      width: 100%;
      position: sticky;
      top: 64px;
      z-index: 1;
      background: white;
      .content {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 24px;
        flex-wrap: wrap;
        gap: 24px;
        align-items: center;
        .left-group {
          display: flex;
          align-items: center;
          column-gap: 16px;
          .image {
            width: 68px;
            min-width: 68px;
            height: 68px;
            border-radius: 8px;
            overflow: hidden;
          }
          .text-group {
            display: flex;
            flex-direction: column;
            row-gap: 2px;
            .title {
              font-size: 24px;
              font-weight: 600;
            }
            .sub-title {
              font-size: 14px;
            }
          }
        }
        .right-group {
          display: flex;
          column-gap: 16px;
        }
      }
      .border-bar {
        border-top: 1px solid #dbe2e5;
        height: 8px;
        background: #f5f7f8;
      }
    }
    .content-cost-wrap {
      width: 100%;
      overflow: auto;
      max-height: calc(100dvh - 188px);
      padding-top: 24px;
      .bottom-section {
        display: flex;
        width: 100%;
        padding: 0 24px 24px;
        .left {
          flex: 2 1 0%;
        }
        .right {
          flex: 1 1 0%;
          min-width: 300px;
          max-width: 100%;
        }
      }
    }
  }
`;
type Props = {
  layDataById: any;
  layDataQuantity: any;
  handleReFetchCost: () => void;
  handleClearState: () => void;
  resetDirtyTrigger: boolean;
};
const SettingCostForm = ({
  layDataById,
  layDataQuantity,
  handleReFetchCost,
  resetDirtyTrigger,
  handleClearState,
}: Props) => {
  const {
    costData,
    calculateCostValues,
    calculateServiceChargeValues,
    calculateMandatoryServiceChargeValues,
  } = useAppSelector(settingCostSelector);
  const [submittingCalc, setSubmittingCalc] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isCalculated, setIsCalculated] = useState<boolean>(false);
  // const [disableCalculate, setDisableCalculate] = useState<boolean>(false);

  const handleCalculate = async () => {
    setSubmittingCalc(true);

    const normalizedCostValues = normalizeValues(calculateCostValues);
    const normalizedServiceChargeValues = normalizeValues(
      calculateServiceChargeValues
    );

    const normalizedMandatoryServiceChargeValues = normalizeValues(
      calculateMandatoryServiceChargeValues
    );

    const resCalculateCost = await apiCostCalculation.saveCalculateCost(
      normalizedCostValues
    );

    const resCalculateServiceCharge =
      await apiCostCalculation.saveCalculateServiceCharge(
        normalizedServiceChargeValues
      );

    const resCalculateMandatoryServiceCharge =
      await apiCostCalculation.saveCalculateMandatoryServiceCharge(
        normalizedMandatoryServiceChargeValues
      );

    if (
      !resCalculateCost.isError &&
      !resCalculateServiceCharge.isError &&
      !resCalculateMandatoryServiceCharge.isError
    ) {
      handleReFetchCost();
    }

    setIsCalculated(true);
    setSubmittingCalc(false);
  };

  const normalizeValues = (data: typeof calculateCostValues) => {
    return {
      ...data,
      costConfig: data.costConfig.map((cost: any) => ({
        ...cost,
        optionCost: cost.optionCost.map((option: any) => ({
          ...option,
          optionsFormulaId:
            option.optionsFormulaId === 'none' ? null : option.optionsFormulaId,
          optionsPresetId:
            option.optionsPresetId === 'none' ? null : option.optionsPresetId,
          priceCostConfigDetail:
            option.optionsFormulaId === 'none'
              ? option.priceCostConfigDetail ?? 0
              : null,
        })),
      })),
    };
  };

  useEffect(() => {
    // initComponent();
    const isNotCalculated = costData.map((costItem: any) =>
      costItem.optionCost.some(
        (optionCostItem: any) => optionCostItem.priceCostConfigDetail === 0
      )
    );
    if (isNotCalculated.includes(true)) {
      setIsCalculated(false);
    } else {
      setIsCalculated(true);
    }
  }, [costData]);

  // const initComponent = () => {
  //   const hasFormulaFalse =
  //     document.querySelector('[data-formula="false"]') !== null;
  //   if (hasFormulaFalse) {
  //     setDisableCalculate(true);
  //   } else {
  //     setIsCalculated(false);
  //   }
  // };
  // console.log('calculateServiceChargeValues', calculateServiceChargeValues);
  return (
    <SettingCostFormStyle>
      <ModalCreateSellingPrice
        layDataId={layDataById.id}
        open={open}
        handleClose={() => {
          setOpen(false);
        }}
        handleReload={() => {
          handleReFetchCost();
        }}
        isEdit={true}
        initialValue={layDataQuantity}
      />
      <form>
        <div className="sheet-header">
          <div className="content">
            <div className="left-group">
              <div className="image">
                <Image
                  src={
                    layDataById.productModel.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={160}
                  height={160}
                  alt=""
                  className="image"
                />
              </div>
              <div className="text-group">
                <div className="title">{layDataById.ldCode}</div>
                <div className="sub-title">
                  {`${
                    layDataById.productModel.productModelName
                  } • ขนาดสินค้า ${numberWithCommas(
                    layDataById.width
                  )} x ${numberWithCommas(
                    layDataById.height
                  )} x ${numberWithCommas(layDataById.length)}
                  mm.`}
                </div>
              </div>
            </div>
            <div className="right-group">
              <div
                onClick={() => {
                  setOpen(true);
                }}
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={
                    <Image
                      src={'/icons/icon-package.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  }
                  text="ตั้งค่าจำนวนผลิต"
                  borderRadius="8px"
                />
              </div>
              <div>
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={
                    submittingCalc ? (
                      <CircularProgress size={20} />
                    ) : (
                      <Image
                        src={'/icons/icon-table.svg'}
                        width={24}
                        height={24}
                        alt=""
                      />
                    )
                  }
                  text="คำนวณราคาต้นทุน"
                  borderRadius="8px"
                  fullWidth
                  disabled={submittingCalc}
                  onClick={async () => {
                    if (!submittingCalc) {
                      await handleCalculate();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <div className="border-bar" />
        </div>
        <div className="content-cost-wrap">
          <CostConfigZone
            resetDirtyTrigger={resetDirtyTrigger}
            handleDisableBtn={() => {
              setIsCalculated(false);
            }}
          />
          <ServiceCharge
            resetDirtyTrigger={resetDirtyTrigger}
            handleDisableBtn={() => {
              setIsCalculated(false);
            }}
          />
          <MandatoryServiceCharge
            resetDirtyTrigger={resetDirtyTrigger}
            handleDisableBtn={() => {
              setIsCalculated(false);
            }}
          />
          <div className="bottom-section">
            <div className="left" />
            <div className="right">
              <div className="flex gap-[24px] mt-[24px]">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    //
                  }}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  color="dark"
                  fullWidth
                  disabled={
                    !isCalculated ||
                    isEmpty(calculateCostValues.costConfig) ||
                    isEmpty(calculateServiceChargeValues.costConfig) ||
                    isEmpty(calculateMandatoryServiceChargeValues.costConfig)
                  }
                  onClick={() => {
                    handleClearState();
                  }}
                >
                  {submittingCalc ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : isCalculated ||
                    !isEmpty(calculateCostValues.costConfig) ||
                    !isEmpty(calculateServiceChargeValues.costConfig) ||
                    !isEmpty(
                      calculateMandatoryServiceChargeValues.costConfig
                    ) ? (
                    'บันทึก'
                  ) : (
                    'รอคำนวณ'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </SettingCostFormStyle>
  );
};

export default SettingCostForm;
