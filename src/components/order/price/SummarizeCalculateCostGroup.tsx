import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';

const SummarizeCalculateCostGroupStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  overflow: hidden;
  .print-plate-header {
    width: inherit;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 24px;
    flex-wrap: wrap;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .action-wrap {
      display: flex;
      column-gap: 8px;
      .remove-btn {
        height: 40px;
        width: 40px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.3s ease-out;
        background: #d32f2f;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          filter: brightness(0.9);
        }
      }
    }
  }
  .detail-wrap {
    display: flex;
    width: 100%;
    overflow: auto;
    border-bottom: 1px solid #dbe2e5;
    gap: 16px;
    padding: 0 16px 16px;
    .detail {
      flex: 1 1 0%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      font-size: 12px;
      min-width: 200px;
      background-color: #f5f7f8;
      border-radius: 8px;
      align-items: center;
      text-align: center;
      .topic {
        font-weight: 600;
      }
      .price {
        font-size: 24px;
        font-weight: 600;
      }
      .per-unit {
        font-size: 12px;
      }
    }
  }
  .lay-data-list {
    width: inherit;
    background: #f5f7f8;
    display: flex;
    flex-direction: column;
    .list {
      width: inherit;
      display: flex;
      align-items: center;
      gap: 24px;
      justify-content: space-between;
      overflow: auto;
      padding: 16px;
      border-bottom: 1px solid #dbe2e5;
      background: white;
      &:last-child {
        border: none;
      }
      .lay-data {
        display: flex;
        align-items: center;
        column-gap: 16px;
        min-width: 260px;
        .image {
          width: 48px;
          height: 48px;
          min-width: 48px;
          border-radius: 4px;
          overflow: hidden;
          position: relative;
        }
        .text-group {
          display: flex;
          flex-direction: column;
          .ld-code {
            font-size: 16px;
            font-weight: 600;
          }
          .info-text {
            //text-decoration: underline;
          }
        }
      }
      .detail {
        font-size: 12px;
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        min-width: 128px;
        .topic {
          font-weight: 600;
          .percentage {
            color: #605dec;
          }
        }
      }
    }
  }
`;
type Props = {
  data: any;
  orderById: any;
};
const SummarizeCalculateCostGroup = ({ data }: Props) => {
  // console.log('data', data);
  return (
    <SummarizeCalculateCostGroupStyle>
      <div className="print-plate-header">
        <div className="title">{data.name}</div>
        <div className="action-wrap"></div>
      </div>
      <div className="detail-wrap">
        <div className="detail">
          <div className="topic">ต้นทุนการผลิต (100%)</div>
          <div className="price">1,800.00</div>
          <div className="per-unit">ราคา/ใบพิมพ์</div>
        </div>
        <div className="detail">
          <div className="topic">ต้นทุนวัตถุดิบ (100%)</div>
          <div className="price">259.75</div>
          <div className="per-unit">ราคา/ใบพิมพ์</div>
        </div>
        <div className="detail">
          <div className="topic">บล็อกเทคนิคพิเศษ</div>
          <div className="price">506.00</div>
          <div className="per-unit">ราคา/ใบพิมพ์</div>
        </div>
        <div className="detail">
          <div className="topic">ต้นทุนเทคนิคพิเศษ</div>
          <div className="price">1.45</div>
          <div className="per-unit">ราคา/ใบพิมพ์</div>
        </div>
        <div className="detail">
          <div className="topic">รวมต้นทุนเริ่มต้นผลิต</div>
          <div className="price">2,567.20</div>
          <div className="per-unit">ราคา/ใบพิมพ์</div>
        </div>
      </div>
      <div className="lay-data-list">
        {data.printPlateLayData.map((item: any, index: number) => {
          return (
            <div key={index} className="list">
              <div className="lay-data">
                <div className="image">
                  <Image
                    src={
                      item.layData.productModel.imageUrl ||
                      '/images/product/empty-product.svg'
                    }
                    alt=""
                    fill
                  />
                </div>
                <div className="text-group">
                  <div className="ld-code">{item.layData.ldCode}</div>
                  <div
                    className="info-text cursor-pointer"
                    onClick={() => {
                      //
                    }}
                  >
                    {`${item.layData.productModel.productName} • ${item.layData.productModel.productModelName}`}
                  </div>
                </div>
              </div>
              <div className="detail">
                <div className="topic">ขนาดกางออก</div>
                <div>
                  {item.dimensionWidth} x {item.dimensionHeight} mm.
                </div>
              </div>
              <div className="detail">
                <div className="topic">จำนวน/ใบพิมพ์</div>
                <div>{item.amount}</div>
              </div>
              <div className="detail">
                <div className="topic">
                  ต้นทุนการผลิต <span className="percentage">(50%)</span>
                </div>
                <div>129.87 (64.93/ชิ้น)</div>
              </div>
              <div className="detail">
                <div className="topic">
                  ต้นทุนวัตถุดิบ <span className="percentage">(50%)</span>
                </div>
                <div>129.87</div>
              </div>
              <div className="detail">
                <div className="topic">บล็อกเทคนิคพิเศษ</div>
                <div>405.87</div>
              </div>
              <div className="detail">
                <div className="topic">ต้นทุนเทคนิคพิเศษ</div>
                <div>1.20 (0.60/ชิ้น)</div>
              </div>
              <div className="detail">
                <div className="topic">รวม</div>
                <div>1,436.86</div>
              </div>
            </div>
          );
        })}
      </div>
    </SummarizeCalculateCostGroupStyle>
  );
};

export default SummarizeCalculateCostGroup;
