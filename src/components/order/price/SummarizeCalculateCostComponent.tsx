import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import apiPrintPlate from '@/services/order/printPlate';
import { isEmpty } from 'lodash';
import SummarizeCalculateCostGroup from '@/components/order/price/SummarizeCalculateCostGroup';

const SummarizeCalculateCostComponentStyled = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 16px;
  animation: ${LoadingFadein} 0.3s ease-in;
  .lay-print-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px 0;
    border-bottom: 1px solid #dbe2e5;
    .print-info {
      display: flex;
      column-gap: 24px;
      align-items: center;
      flex-wrap: wrap;
      row-gap: 16px;
      .ld-group {
        display: flex;
        column-gap: 8px;
        align-items: center;
        .text-group {
          display: flex;
          flex-direction: column;
          .ld-code {
            font-size: 16px;
            font-weight: 600;
          }
          .status {
            display: flex;
            align-items: center;
            column-gap: 4px;
            color: #90a4ae;
            font-size: 12px;
            font-weight: 600;
            &.done {
              color: #8bc34a;
            }
          }
        }
      }
    }
  }
`;
type Props = {
  orderById: any;
};

const SummarizeCalculateCostComponent = ({ orderById }: Props) => {
  const [printPlateByOrderId, setPrintPlateByOrderId] = useState<any>({});
  const getPrintPlateById = async () => {
    const res = await apiPrintPlate.getPrintPlateByOrderId(orderById.id);
    if (!res.isError) {
      setPrintPlateByOrderId(res.data);
    }
  };
  useEffect(() => {
    if (!isEmpty(orderById)) {
      getPrintPlateById();
    }
  }, [orderById]);

  return (
    <>
      <SummarizeCalculateCostComponentStyled>
        {!isEmpty(printPlateByOrderId) &&
          printPlateByOrderId.map((item: any, index: number) => {
            return (
              <SummarizeCalculateCostGroup
                key={index}
                data={item}
                orderById={orderById}
              />
            );
          })}
      </SummarizeCalculateCostComponentStyled>
    </>
  );
};
export default SummarizeCalculateCostComponent;
