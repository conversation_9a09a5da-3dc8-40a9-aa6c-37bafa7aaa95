import React, { useState } from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { numberWithCommas } from '@/utils/number';
import {
  Button,
  CircularProgress,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import apiLayDataQuantity from '@/services/order/layDataQuantity';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { isEmpty } from 'lodash';
import PopoverAction from '@/components/PopoverActionn';
import SvgReplyIcon from '@/components/svg-icon/SvgReplyIcon';
import apiEstimatePrintPlate from '@/services/order/estimate-print-plate';
import { AnimatePresence, motion } from 'framer-motion';
import { motionFadeConfig } from '@/utils/motion/motion-config';

const LayDataOfferStyled = styled.div`
  width: 100%;
  border: 1px solid #dbe2e5;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
`;

type Props = {
  layDataItem: any;
  handleReload: () => void;
  handleSelectQuantity: (layDataId: number, estimateQuantity: any) => void;
  setExtend: (extend: boolean) => void;
  confirmSalePriceSelected: any;
  salesOrderById: any;
};

const LayDataOffer = ({
  layDataItem,
  handleReload,
  confirmSalePriceSelected,
  handleSelectQuantity,
  salesOrderById,
  setExtend,
}: Props) => {
  const dispatch = useAppDispatch();
  const [layQuantityIdToDelete, setLayQuantityIdToDelete] = useState<number>(0);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [submittingDelete, setSubmittingDelete] = useState<boolean>(false);
  const [isDownload, setIsDownload] = useState<boolean>(false);
  const [selectedDownload, setSelectedDownload] = useState<any>([]);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  const columns: GridColDef[] = [
    {
      field: 'option',
      headerName: '#',
      editable: false,
      headerAlign: 'center',
      align: 'center',
      maxWidth: 56,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <AnimatePresence mode="wait">
            {!isDownload ? (
              <motion.div {...motionFadeConfig} key={`radio-${params.row.id}`}>
                <RadioGroup>
                  <FormControlLabel
                    value={params.row.id}
                    control={
                      <Radio
                        sx={{
                          '.MuiTouchRipple-root': {
                            display: 'none',
                          },
                          '&:hover': {
                            backgroundColor: 'transparent',
                          },
                        }}
                        onClick={() => {
                          if (
                            salesOrderById.estimateStatus.name === 'เสนอราคา'
                          ) {
                            handleSelectQuantity(layDataItem.id, params.row);
                          }
                        }}
                        checked={
                          !isEmpty(confirmSalePriceSelected.layData)
                            ? confirmSalePriceSelected.layData.some(
                                (item: any) =>
                                  item.estimateProductId === layDataItem.id &&
                                  item.estimateQuantityId === params.row.id
                              )
                            : params.row.isConfirm
                        }
                      />
                    }
                    label=""
                  />
                </RadioGroup>
              </motion.div>
            ) : (
              <motion.div
                {...motionFadeConfig}
                key={`checkBox-${params.row.id}`}
              >
                {/* Checkbox functionality removed as it's now in header */}
              </motion.div>
            )}
          </AnimatePresence>
        );
      },
    },
    {
      field: 'quantity',
      headerName: 'จำนวนผลิต',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 124,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div
            style={{
              marginTop: '4px',
            }}
          >
            {numberWithCommas(params.row.quantity)} ชิ้น
          </div>
        );
      },
    },
    {
      field: 'costPrice',
      headerName: 'ราคาต้นทุน (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        const costPricePerPiece =
          params.row.quantity > 0 && params.row.costPrice !== 0
            ? params.row.costPrice / params.row.quantity
            : 0;

        return (
          <div
            style={{
              marginTop: '4px',
              display: 'flex',
              alignItems: 'center',
              columnGap: '4px',
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {params.row.costPrice !== 0
              ? numberWithCommas(params.row.costPrice, 2)
              : '-'}
            {params.row.costPrice !== 0 && (
              <span
                style={{
                  fontSize: '14px',
                  color: '#90A4AE',
                  maxWidth: '100%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                ({numberWithCommas(costPricePerPiece, 2)} บาท/ชิ้น)
              </span>
            )}
          </div>
        );
      },
    },
    {
      field: 'profit',
      headerName: 'กำไร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 140,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        const profitPerPiece =
          params.row.quantity > 0 ? params.row.profit / params.row.quantity : 0;

        return (
          <div
            style={{
              marginTop: '4px',
              display: 'flex',
              alignItems: 'center',
              columnGap: '4px',
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {numberWithCommas(params.row.profit, 2)}
            <span
              style={{
                fontSize: '14px',
                color: '#90A4AE',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              ({numberWithCommas(profitPerPiece, 2)} บาท/ชิ้น)
            </span>
          </div>
        );
      },
    },
    {
      field: 'sumSalePrice',
      headerName: 'สรุปราคาเสนอขาย (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 180,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        const pricePerPiece =
          params.row.quantity > 0
            ? params.row.totalSalePrice / params.row.quantity
            : 0;

        return (
          <div
            style={{
              marginTop: '4px',
              display: 'flex',
              alignItems: 'center',
              columnGap: '4px',
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {numberWithCommas(params.row.totalSalePrice, 2)}
            <span
              style={{
                fontSize: '14px',
                color: '#90A4AE',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              ({numberWithCommas(pricePerPiece, 2)} บาท/ชิ้น)
            </span>
          </div>
        );
      },
    },
  ];

  const handleDelete = async () => {
    setSubmittingDelete(true);
    const res = await apiLayDataQuantity.deleteQuantity(layQuantityIdToDelete);
    if (!res.isError) {
      handleReload();
    }
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    setLayQuantityIdToDelete(0);
    setOpenDelete(false);
    setSubmittingDelete(false);
  };

  const handleConvert = async (estimateProductId: number) => {
    const res = await apiEstimatePrintPlate.convertEstimateToStatusPrintPlate(
      estimateProductId
    );
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleReload();
      setExtend(false);
    }
  };

  const toggleDownloadSelection = () => {
    const allIds = layDataItem.estimateQuantity.map((item: any) => item.id);

    if (selectedDownload.length === allIds.length) {
      setSelectedDownload([]);
    } else {
      setSelectedDownload(allIds);
    }
  };

  const handleClickDownload = async () => {
    setIsProcessing(true);
    // This functionality is now handled in the header
    setIsProcessing(false);
  };

  console.log('layDataItem.estimateQuantity', layDataItem?.estimateQuantity);

  return (
    <>
      <AnimatePresence mode="wait">
        {salesOrderById.estimateStatus.name === 'เสนอราคา' && !isDownload && (
          <motion.div
            className="w-full flex justify-end gap-[16px] items-center"
            {...motionFadeConfig}
            key="calc-action"
          >
            <PopoverAction
              triggerElement={
                <div className="kebab">
                  <div className="dot" />
                </div>
              }
              customItems={[
                {
                  IconElement: () => <SvgReplyIcon />,
                  title: 'แก้ไขข้อมูลเลย์เอาท์',
                  onAction: async () => {
                    await handleConvert(layDataItem.id);
                  },
                },
              ]}
            />
          </motion.div>
        )}

        {salesOrderById.estimateStatus.name === 'เสนอราคา' && isDownload && (
          <motion.div
            className="w-full flex justify-end gap-[16px] items-center"
            {...motionFadeConfig}
            key="download-action"
          >
            <span
              style={{ textDecoration: 'underline', cursor: 'pointer' }}
              onClick={toggleDownloadSelection}
            >
              {layDataItem.estimateQuantity.length === selectedDownload.length
                ? 'ยกเลิกทั้งหมด'
                : 'เลือกทั้งหมด'}
            </span>
            <Button
              type="button"
              variant="outlined"
              color="blueGrey"
              onClick={() => {
                setIsDownload(false);
                setSelectedDownload([]);
              }}
              sx={{
                width: '122px',
              }}
            >
              <span>ยกเลิก</span>
            </Button>
            <Button
              type="button"
              variant="contained"
              color="dark"
              onClick={async () => {
                await handleClickDownload();
              }}
              sx={{
                width: '146px',
              }}
              disabled={isEmpty(selectedDownload) || isProcessing}
            >
              {isProcessing ? (
                <CircularProgress
                  size={20}
                  style={{
                    color: 'white',
                  }}
                />
              ) : (
                'ดาวน์โหลด'
              )}
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      <LayDataOfferStyled>
        <AppModalConfirm
          open={openDelete}
          onClickClose={() => {
            setLayQuantityIdToDelete(0);
            setOpenDelete(false);
          }}
          confirmTitle={`ยืนยันการลบ`}
          confirmDescription={'คุณต้องการที่จะลบข้อมูลจำนวนเสนอขายนี้?'}
          loadingConfirm={submittingDelete}
          onConfirm={async () => {
            await handleDelete();
          }}
        />
        <AppTableStyle $rows={layDataItem?.estimateQuantity}>
          <div className="content-wrap">
            <ScrollBarStyled>
              <DataGrid
                hideFooter={true}
                rows={
                  layDataItem?.estimateQuantity
                    ? layDataItem.estimateQuantity
                    : []
                }
                columns={columns}
                paginationMode="server"
                rowCount={layDataItem?.estimateQuantity.length || 0}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
        </AppTableStyle>
      </LayDataOfferStyled>
    </>
  );
};

export default LayDataOffer;
