import React from 'react';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';

const SummaryCardStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  animation: ${LoadingFadein} 0.3s ease-in;
  margin-bottom: 16px;
`;

const ProductIconStyled = styled.div`
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ContentStyled = styled.div`
  flex: 1;
`;

const TitleStyled = styled.h3`
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 4px 0;
`;

const DescriptionStyled = styled.p`
  color: #424242;
  margin: 0;
  line-height: 1.4;
`;

type Props = {
  layData: any;
  index: number;
};

const EstimateExportHeaderTable = ({ layData }: Props) => {
  // Calculate totals for the selected quantities
  const selectedQuantities = layData.estimateQuantity || [];

  // Get the default or first quantity for display
  const defaultQuantity =
    selectedQuantities.find((q: any) => q.isDefault) || selectedQuantities[0];

  if (!defaultQuantity) return null;

  const formatDimensions = () => {
    return `${layData.length}x${layData.width}x${layData.height} mm`;
  };

  const formatScheduleDate = () => {
    if (!layData.scheduleDate) return '';
    return dayjs(layData.scheduleDate).format('DD/MM/YYYY');
  };

  return (
    <SummaryCardStyled>
      <ProductIconStyled>
        <Image
          src={layData.productModel?.imageUrl || '/icons/box-default.svg'}
          width={160}
          height={160}
          alt={layData.productModel?.productModelName || 'Product'}
        />
      </ProductIconStyled>

      <ContentStyled>
        <TitleStyled>{layData.ldCode}</TitleStyled>
        <DescriptionStyled>
          {layData.productModel?.productModelName} (
          {layData.productModel?.modelCodePacdora}) {formatDimensions()} |
          ระดับความละเอียด:{' '}
          {layData.detailLevel === 1 ? 'งานทั่วไป' : 'งานละเอียด'} |
          กำหนดส่งสินค้า: {formatScheduleDate()}
        </DescriptionStyled>
      </ContentStyled>
    </SummaryCardStyled>
  );
};

export default EstimateExportHeaderTable;
