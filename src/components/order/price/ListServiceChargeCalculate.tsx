import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  FormControl,
  FormHelperText,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { LoadingFadein } from '@/styles/share.styled';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  setCalculateServiceChargeValues,
  settingCostSelector,
} from '@/store/features/selling-price/setting-cost';
import { numberWithCommas } from '@/utils/number';
import { isEmpty } from 'lodash';

const CostListCalculateStyle = styled.div`
  width: inherit;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  border-bottom: 1px solid #dbe2e5;
  min-height: 72px;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  &:last-child {
    border-bottom: none;
  }
  .left-group {
    display: inherit;
    align-items: inherit;
    column-gap: 12px;
    flex: 1 1 0%;
    min-height: 72px;
    padding: 0 16px;
    overflow: hidden;
    min-width: 180px;
    cursor: pointer;
    .image {
      width: 40px;
      height: 40px;
      min-width: 40px;
      border-radius: 50%;
      overflow: hidden;
      object-fit: cover;
    }
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }
  .right-group {
    display: inherit;
    align-items: inherit;
    min-height: 72px;
    .item-wrap {
      display: inherit;
      align-items: inherit;
    }
    .item {
      flex: 1 1 0%;
      min-height: 72px;
      display: inherit;
      align-items: inherit;
      padding-right: 16px;
      &.sum {
        border-left: 1px solid #dbe2e5;
        justify-content: space-between;
        gap: 16px;
        padding: 0 16px;
        width: 200px;
        min-width: 200px;
        max-width: 200px;
        > div:first-child {
          font-weight: 600;
        }
        &.wait {
          justify-content: center;
          color: #f9a925;
        }
      }
    }
  }
`;
type Props = {
  data: any;
  selectedToRemoveIds: number[];
  handleSelectRemove: (id: number) => void;
  handleOpenEdit: (data: any) => void;
  resetDirtyTrigger: boolean;
  handleDirty: () => void;
  handleDisableBtn: () => void;
};
const validationSchema = yup.object({
  optionsFormulaId: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
});
const ListServiceChargeCalculate = ({
  data,
  // selectedToRemoveIds,
  // handleSelectRemove,
  // handleOpenEdit,
  resetDirtyTrigger,
  handleDirty,
  handleDisableBtn,
}: Props) => {
  const dispatch = useAppDispatch();
  const { calculateServiceChargeValues } = useAppSelector(settingCostSelector);
  const [init, setInit] = useState<boolean>(false);
  const [priceCostConfigDetail, setPriceCostConfigDetail] = useState<number>(0);
  const {
    register,
    setValue,
    getFieldState,
    control,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      optionsFormulaId: 'none',
      optionsPresetId: 'none',
    },
    mode: 'onChange',
  });

  const watchOptionsFormulaId = useWatch({
    control,
    name: 'optionsFormulaId',
    defaultValue: 'none',
  });
  const watchOptionsPresetId = useWatch({
    control,
    name: 'optionsPresetId',
    defaultValue: 'none',
  });
  const isOptionsPresetIdDirty = getFieldState('optionsPresetId').isDirty;
  const isOptionsFormulaIdDirty = getFieldState('optionsFormulaId').isDirty;
  const handleChangeFormula = (value: any) => {
    handleDisableBtn();
    setValue('optionsFormulaId', value, { shouldDirty: true });
    if (value === 'none') {
      setValue('optionsPresetId', 'none', { shouldDirty: true });
    }
    handleDirty();
    const updatedCostConfig = calculateServiceChargeValues.costConfig.map(
      (serviceChargeValuesItem: any) => {
        if (serviceChargeValuesItem.costType === data.costType) {
          return {
            ...serviceChargeValuesItem,
            optionCost: serviceChargeValuesItem.optionCost.map(
              (option: any) => ({
                ...option,
                optionsFormulaId: value,
                optionsPresetId:
                  value === 'none' ? 'none' : option.optionsPresetId,
              })
            ),
          };
        }
        return serviceChargeValuesItem;
      }
    );

    const updatedCalculateServiceChargeValues = {
      ...calculateServiceChargeValues,
      costConfig: updatedCostConfig,
    };
    if (updatedCalculateServiceChargeValues) {
      dispatch(
        setCalculateServiceChargeValues(updatedCalculateServiceChargeValues)
      );
    }
  };
  const handleChangePreset = (value: any) => {
    handleDisableBtn();
    setValue('optionsPresetId', value, { shouldDirty: true });
    handleDirty();
    const updatedCostConfig = calculateServiceChargeValues.costConfig.map(
      (serviceChargeValuesItem: any) => {
        if (serviceChargeValuesItem.costType === data.costType) {
          return {
            ...serviceChargeValuesItem,
            optionCost: serviceChargeValuesItem.optionCost.map(
              (option: any) => ({
                ...option,
                optionsPresetId: value,
              })
            ),
          };
        }
        return serviceChargeValuesItem;
      }
    );

    const updatedCalculateServiceChargeValues = {
      ...calculateServiceChargeValues,
      costConfig: updatedCostConfig,
    };

    if (updatedCalculateServiceChargeValues) {
      dispatch(
        setCalculateServiceChargeValues(updatedCalculateServiceChargeValues)
      );
    }
  };

  const handleRenderValue = (keepDirty: boolean) => {
    const optionsFormulaIdValue =
      calculateServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.optionsFormulaId ?? 'none';

    const optionsPresetIdValue =
      calculateServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.optionsPresetId ?? 'none';

    setValue('optionsFormulaId', optionsFormulaIdValue, {
      shouldDirty: !keepDirty,
    });
    setValue('optionsPresetId', optionsPresetIdValue, {
      shouldDirty: !keepDirty,
    });

    if (!keepDirty) {
      reset(
        {
          optionsFormulaId: optionsFormulaIdValue,
          optionsPresetId: optionsPresetIdValue,
        },
        { keepDirty: false }
      );
    }
  };

  useEffect(() => {
    if (data) {
      handleRenderValue(true);
      if (data.priceCostConfig === 0) {
        handleDirty();
      }
    }
  }, [calculateServiceChargeValues, data, resetDirtyTrigger]);

  useEffect(() => {
    if (init) {
      handleRenderValue(false);
    }
  }, [resetDirtyTrigger]);

  useEffect(() => {
    setInit(true);
  }, []);

  const handleChangeStaticPrice = (value: number) => {
    const updatedCostConfig = calculateServiceChargeValues.costConfig.map(
      (item: any) => {
        if (item.costType === data.costType) {
          return {
            ...item,
            optionCost: item.optionCost.map((option: any) => ({
              ...option,
              priceCostConfigDetail: value,
            })),
          };
        }
        return item;
      }
    );

    dispatch(
      setCalculateServiceChargeValues({
        ...calculateServiceChargeValues,
        costConfig: updatedCostConfig,
      })
    );
  };

  useEffect(() => {
    const detail =
      calculateServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.priceCostConfigDetail ?? 0;

    setPriceCostConfigDetail(detail);
  }, [calculateServiceChargeValues, data.costType]);

  return (
    <CostListCalculateStyle>
      <label className="left-group">
        {/* <Checkbox */}
        {/*  color="primary" */}
        {/*  checked={selectedToRemoveIds?.includes(data.optionCost[0].id)} */}
        {/*  onChange={() => { */}
        {/*    if (handleSelectRemove) { */}
        {/*      handleSelectRemove(data.optionCost[0].id); */}
        {/*    } */}
        {/*  }} */}
        {/*  icon={<IconUnCheckbox />} */}
        {/*  checkedIcon={<IconCheckboxBlack />} */}
        {/*  sx={{ */}
        {/*    marginRight: '-8px', */}
        {/*  }} */}
        {/* /> */}
        <div className="name">{data?.optionCost[0]?.name || '-'}</div>
      </label>
      <div className="right-group">
        <div className="item-wrap">
          {/* <div className="item"> */}
          {/*  <div */}
          {/*    onClick={() => { */}
          {/*      if (handleOpenEdit) { */}
          {/*        handleOpenEdit(data); */}
          {/*      } */}
          {/*    }} */}
          {/*  > */}
          {/*    <ActionButton */}
          {/*      variant="outlined" */}
          {/*      color="blueGrey" */}
          {/*      icon={ */}
          {/*        <Image */}
          {/*          src={'/icons/edit-black.svg'} */}
          {/*          width={24} */}
          {/*          height={24} */}
          {/*          alt="" */}
          {/*        /> */}
          {/*      } */}
          {/*      text="แก้ไข" */}
          {/*      borderRadius={'8px'} */}
          {/*    /> */}
          {/*  </div> */}
          {/* </div> */}
          {
            !isEmpty(data?.optionCost[0].optionsFormula) ? (
              <div className="item">
                <FormControl
                  sx={{
                    width: '142px',
                  }}
                >
                  <Select
                    error={false}
                    displayEmpty
                    {...register('optionsFormulaId')}
                    value={watchOptionsFormulaId}
                    onChange={(e: any) => {
                      handleChangeFormula(e.target.value);
                    }}
                  >
                    <MenuItem value="none">
                      <div className="text-[#78909C]">ไม่ใช้สูตร</div>
                    </MenuItem>
                    {data?.optionCost[0].optionsFormula.map(
                      (item: any, index: number) => {
                        return (
                          <MenuItem key={index} value={item.id}>
                            {item.name}
                          </MenuItem>
                        );
                      }
                    )}
                  </Select>
                  {hookFormErrors.optionsFormulaId && (
                    <FormHelperText error>
                      {hookFormErrors.optionsFormulaId.message as ReactNode}
                    </FormHelperText>
                  )}
                </FormControl>
              </div>
            ) : null
            // <EmptyField
            //   style={{
            //     width: '142px',
            //     marginRight: '16px',
            //   }}
            //   onClick={async () => {
            //     const mergedValue = {
            //       calculateCostValues: calculateCostValues,
            //       calculateServiceChargeValues: calculateServiceChargeValues,
            //     };
            //     const mergedStringValue = JSON.stringify(mergedValue);
            //     // encode Base64 UTF-8
            //     const encoded64MergedStringValue = btoa(
            //       encodeURIComponent(mergedStringValue).replace(
            //         /%([0-9A-F]{2})/g,
            //         (match, p1) => String.fromCharCode(parseInt(p1, 16))
            //       )
            //     );
            //     setCookie('costValueCache', encoded64MergedStringValue, {
            //       maxAge: 60 * 60 * 24,
            //     });
            //     const encoded64FromPath = btoa(router.asPath);
            //     if (data.componentId === null) {
            //       await router.push(
            //         `/stock/setting/material?fromPath=${encoded64FromPath}`
            //       );
            //     } else {
            //       await router.push(
            //         `/stock/component?fromPath=${encoded64FromPath}`
            //       );
            //     }
            //   }}
            //   // data-formula={!isEmpty(data.optionsFormula)}
            // >
            //   เพิ่มสูตร
            // </EmptyField>
          }
          {watchOptionsFormulaId !== 'none' && (
            <div className="item">
              <FormControl
                sx={{
                  width: '142px',
                }}
              >
                <Select
                  error={false}
                  displayEmpty
                  {...register('optionsPresetId')}
                  value={watchOptionsPresetId}
                  onChange={(e: any) => {
                    handleChangePreset(e.target.value);
                  }}
                >
                  <MenuItem value="none">
                    <div className="text-[#78909C]">ไม่ใช้พรีเซ็ต</div>
                  </MenuItem>
                  {data?.optionCost[0].optionsPreset.map(
                    (item: any, index: number) => {
                      return (
                        <MenuItem key={index} value={item.id}>
                          {item.name}
                        </MenuItem>
                      );
                    }
                  )}
                </Select>
              </FormControl>
            </div>
          )}
        </div>
        {watchOptionsFormulaId !== 'none' ? (
          priceCostConfigDetail !== 0 &&
          !isOptionsPresetIdDirty &&
          !isOptionsFormulaIdDirty ? (
            <div className="item sum">
              <div>{numberWithCommas(priceCostConfigDetail, 2)}</div>
              <div>บาท</div>
            </div>
          ) : priceCostConfigDetail === 0 ||
            isOptionsPresetIdDirty ||
            isOptionsFormulaIdDirty ? (
            <div className="item sum wait">
              <div>รอคำนวณ</div>
            </div>
          ) : null
        ) : (
          <div className="item sum">
            <TextField
              type="number"
              fullWidth
              placeholder="กรอก"
              value={priceCostConfigDetail}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                if (value < 1) {
                  e.target.value = '';
                } else {
                  handleDirty();
                  handleChangeStaticPrice(value);
                }
              }}
              onKeyDown={(e: any) => {
                if (e.key === '-') {
                  e.preventDefault();
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start">
                    <div>บาท</div>
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
      </div>
    </CostListCalculateStyle>
  );
};

export default ListServiceChargeCalculate;
