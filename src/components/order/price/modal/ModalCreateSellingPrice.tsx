import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useForm } from 'react-hook-form';
import { useAppDispatch } from '@/store';
import apiLayDataQuantity from '@/services/order/layDataQuantity';
import { setSnackBar } from '@/store/features/alert';
import styled from 'styled-components';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useRouter } from 'next/router';

const EditQuantityConditionStyle = styled.div`
  width: 100%;
  height: 58px;
  border-radius: 8px;
  background: #fff5d3;
  display: flex;
  align-items: center;
  column-gap: 12px;
  padding: 12px 24px 12px 12px;
  margin-top: 28px;
`;
type Props = {
  open: boolean;
  handleClose: () => void;
  handleReload: () => void;
  layDataId: number;
  isEdit?: boolean;
  initialValue?: any;
};
const ModalCreateSellingPrice = ({
  open,
  handleClose,
  handleReload,
  layDataId,
  isEdit,
  initialValue,
}: Props) => {
  const dispatch = useAppDispatch();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const router = useRouter();
  const { layDataQuantityId } = router.query;
  const {
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    watch,
    reset,
  } = useForm<any>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      quantity: initialValue?.quantity || '',
      quantityAllowance: initialValue?.quantityAllowance || '',
      quantityAllowancePercentage: '',
    },
  });

  const onSubmit = async (values: any) => {
    setSubmitting(true);
    if (!isEdit) {
      const sendValue = {
        ...values,
        layDataId,
      };
      const res = await apiLayDataQuantity.createQuantity(sendValue);
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        handleClose();
        handleReload();
      }
    } else {
      const sendValue = {
        ...values,
        id: layDataQuantityId,
      };
      const res = await apiLayDataQuantity.updateQuantity(sendValue);
      dispatch(
        setSnackBar({
          status: true,
          text: !res.isError ? res.message : res.error.response.data.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
      if (!res.isError) {
        handleClose();
        handleReload();
      }
    }
    setSubmitting(false);
  };

  const handleChangQuantityAllowancePercentage = (value: number) => {
    const quantity = watch('quantity');
    const quantityAllowance = Math.floor(
      (Number(quantity) * Number(value)) / 100
    );
    setValue('quantityAllowance', quantityAllowance.toString(), {
      shouldValidate: true,
    });
  };

  const handleChangeQuantityAllowance = (value: number) => {
    const quantity = watch('quantity');
    const quantityAllowancePercentage =
      (Number(value) / Number(quantity)) * 100;
    setValue(
      'quantityAllowancePercentage',
      quantityAllowancePercentage.toString(),
      { shouldValidate: true }
    );
    setValue('quantityAllowance', Math.floor(value).toString(), {
      shouldValidate: true,
    });
  };

  useEffect(() => {
    if (open) {
      reset();
      handleChangeQuantityAllowance(watch('quantityAllowance'));
    }
  }, [open]);

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModal
          width={500}
          title={'สร้างจำนวนเสนอราคา'}
          handleClose={handleClose}
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              {isEdit && (
                <EditQuantityConditionStyle>
                  <InfoOutlinedIcon
                    sx={{
                      color: '#F9A925',
                      fontSize: '32px',
                    }}
                  />
                  <div>
                    การปรับเปลี่ยนค่าจำนวนผลิตจะมีผลทำให้การตั้งค่าราคาทั้งหมดกลับสู่ค่าเริ่มต้น
                    คุณจะต้องทำการปรับแต่งราคาใหม่
                  </div>
                </EditQuantityConditionStyle>
              )}
              <div>
                <p>จำนวนผลิต</p>
                <TextField
                  placeholder="กรอกจำนวนผลิต"
                  {...register('quantity', { required: 'กรอกจำนวนผลิต' })}
                  error={!!hookFormErrors.quantity}
                  helperText={
                    hookFormErrors.quantity
                      ? (hookFormErrors.quantity.message as ReactNode)
                      : ''
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">ชิ้น</InputAdornment>
                    ),
                  }}
                  onChange={() => {
                    setValue('quantityAllowance', '');
                    setValue('quantityAllowancePercentage', '');
                  }}
                />
              </div>
              <div>
                <p>จำนวนเผื่อเสีย</p>
                <div className="flex gap-3">
                  <TextField
                    type="number"
                    placeholder="0"
                    {...register('quantityAllowancePercentage')}
                    error={Boolean(hookFormErrors.quantityAllowance)}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (value < 1) {
                        e.target.value = '';
                      } else if (value > 100) {
                        e.target.value = '100';
                      } else {
                        handleChangQuantityAllowancePercentage(value);
                      }
                    }}
                    onKeyDown={(e: any) => {
                      if (e.key === '-') {
                        e.preventDefault();
                      }
                    }}
                    InputProps={{
                      endAdornment: <div>%</div>,
                    }}
                    inputProps={{
                      step: '0.01',
                    }}
                    sx={{
                      width: '112px',
                    }}
                  />
                  <TextField
                    type="number"
                    placeholder="จำนวนเผื่อเสีย"
                    {...register('quantityAllowance', {
                      required: 'กรอกจำนวนเผื่อเสีย',
                    })}
                    error={Boolean(hookFormErrors.quantityAllowance)}
                    helperText={
                      hookFormErrors.quantityAllowance
                        ? (hookFormErrors.quantityAllowance
                            .message as ReactNode)
                        : ''
                    }
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (value < 1) {
                        e.target.value = '';
                      } else {
                        handleChangeQuantityAllowance(value);
                      }
                    }}
                    onKeyDown={(e: any) => {
                      if (e.key === '-') {
                        e.preventDefault();
                      }
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">ชิ้น</InputAdornment>
                      ),
                    }}
                  />
                </div>
              </div>
              <div className="w-full flex justify-between mt-[34px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  disabled={submitting}
                  fullWidth
                >
                  {submitting ? <CircularProgress size={20} /> : 'บันทึก'}
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalCreateSellingPrice;
