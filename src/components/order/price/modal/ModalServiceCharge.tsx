import React, { ReactNode, useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useAppDispatch, useAppSelector } from '@/store';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import { Check } from '@mui/icons-material';
import { RadioStyle } from '@/components/raw-material/RawMaterialForm';
import { FadeInStyled } from '@/styles/share.styled';
import apiLayDataQuantity from '@/services/order/layDataQuantity';
import { setSnackBar } from '@/store/features/alert';
import {
  setCostServiceChargeData,
  settingCostSelector,
} from '@/store/features/selling-price/setting-cost';

type Props = {
  open: boolean;
  handleClose: () => void;
  handleDisableBtn: () => void;
  initialValues: any;
};

const validationSchema = yup.object({
  costType: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
  componentId: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
});

const ModalServiceCharge = ({
  open,
  handleClose,
  initialValues,
  handleDisableBtn,
}: Props) => {
  const { costServiceChargeData } = useAppSelector(settingCostSelector);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { layDataQuantityId } = router.query;
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [componentSelectList, setComponentSelectList] = useState<any>([]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    control,
    formState: { errors: hookFormErrors, isSubmitted },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      layDataQuantityId: Number(layDataQuantityId),
      costType: '',
      title: '',
      componentId: '',
      name: '',
    },
  });

  const watchCostType = useWatch({
    control,
    name: 'costType',
    defaultValue: '',
  });

  const watchComponentId = useWatch({
    control,
    name: 'componentId',
    defaultValue: '',
  });

  // useEffect(() => {
  //   if (watchCostType) {
  //     const findComponent = costServiceChargeSelectList.find(
  //       (costTypeItem: any) => costTypeItem.id === watchCostType
  //     );
  //     if (!isEmpty(findComponent)) {
  //       setValue('title', findComponent.name);
  //     }
  //     if (!isEmpty(findComponent.component)) {
  //       setComponentSelectList(findComponent.component);
  //     } else {
  //       setComponentSelectList([]);
  //     }
  //   }
  // }, [watchCostType]);

  useEffect(() => {
    if (open) {
      reset(initialValues);
      if (isEmpty(initialValues)) {
        setComponentSelectList([]);
      }
    }
  }, [open]);

  const onSubmit = async (value: any) => {
    setIsSubmitting(true);
    if (isEmpty(initialValues)) {
      const res = await apiLayDataQuantity.saveServiceCost({
        ...value,
        layDataQuantityId,
      });
      if (!res.isError) {
        handleClose();
        const addedCostServiceChargeDataItem = [
          ...costServiceChargeData,
          res.data,
        ];
        if (addedCostServiceChargeDataItem) {
          dispatch(setCostServiceChargeData(addedCostServiceChargeDataItem));
        }
      }
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
    } else {
      const res = await apiLayDataQuantity.updateServiceCost({
        ...value,
        layDataQuantityId,
      });
      if (!res.isError) {
        handleClose();
        const addedCostServiceChargeDataItem = costServiceChargeData.map(
          (costServiceChargeDataItem: any) => {
            if (costServiceChargeDataItem.costType === res.data.costType) {
              return res.data;
            }
            return costServiceChargeDataItem;
          }
        );
        if (addedCostServiceChargeDataItem) {
          dispatch(setCostServiceChargeData(addedCostServiceChargeDataItem));
        }
      }
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    handleDisableBtn();
    setIsSubmitting(false);
  };
  useEffect(() => {
    if (open) {
      reset(initialValues);
    }
  }, [initialValues, open]);
  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModal
          width={500}
          title={'เลือกค่าบริการ'}
          handleClose={handleClose}
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p>บริการ</p>
              <FormControl>
                <Select
                  displayEmpty
                  {...register('costType')}
                  error={Boolean(hookFormErrors.costType) && isSubmitted}
                  value={watchCostType || ''}
                  onChange={(e: any) => {
                    setValue('costType', e.target.value);
                    setValue('componentId', '');
                  }}
                  disabled={!isEmpty(initialValues)}
                >
                  <MenuItem disabled value="">
                    <div className="text-[#78909C]">กรุณาเลือก</div>
                  </MenuItem>
                  {/* {costServiceChargeSelectList.map((item: any) => ( */}
                  {/*  <MenuItem */}
                  {/*    key={item.id} */}
                  {/*    value={item.id} */}
                  {/*    disabled={costServiceChargeData.some( */}
                  {/*      (costServiceChargeItem: any) => */}
                  {/*        costServiceChargeItem.costType === item.id */}
                  {/*    )} */}
                  {/*  > */}
                  {/*    {item.name} */}
                  {/*  </MenuItem> */}
                  {/* ))} */}
                </Select>
                {hookFormErrors.costType && isSubmitted && (
                  <FormHelperText error>
                    {hookFormErrors.costType.message as ReactNode}
                  </FormHelperText>
                )}
              </FormControl>
              {!isEmpty(componentSelectList) && (
                <FadeInStyled>
                  <RadioGroup
                    value={watchComponentId || ''}
                    onChange={(e) => {
                      const selectedItem = componentSelectList.find(
                        (item: any) => item.id === Number(e.target.value)
                      );
                      setValue('componentId', e.target.value, {
                        shouldValidate: !!isSubmitted,
                      });
                      setValue('name', selectedItem?.name || '');
                    }}
                    sx={{
                      marginTop: '16px',
                    }}
                  >
                    {componentSelectList.map((item: any, index: number) => {
                      return (
                        <FormControlLabel
                          key={index}
                          value={item.id}
                          control={
                            <Radio
                              icon={<Check />}
                              checkedIcon={<Check className={'p-1'} />}
                              sx={RadioStyle}
                            />
                          }
                          label={item.name}
                        />
                      );
                    })}
                  </RadioGroup>
                  {hookFormErrors.componentId && isSubmitted && (
                    <FormHelperText error>
                      {hookFormErrors.componentId.message as ReactNode}
                    </FormHelperText>
                  )}
                </FadeInStyled>
              )}
              <div className="w-full flex justify-between mt-[24px] gap-5">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  fullWidth
                  onClick={() => {
                    handleClose();
                  }}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  disabled={false}
                  fullWidth
                >
                  {isSubmitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'ยืนยัน'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default ModalServiceCharge;
