import React, { useEffect, useState } from 'react';
import ListCostCalculate from '@/components/order/price/ListCostCalculate';
import { numberWithCommas } from '@/utils/number';
import styled from 'styled-components';
import { isEmpty } from 'lodash';
import { useAppSelector } from '@/store';
import { settingCostSelector } from '@/store/features/selling-price/setting-cost';

const CostConfigGroupStyle = styled.div`
  .topic {
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 16px;
  }
  .cost-list-wrap {
    width: inherit;
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    .list {
      width: inherit;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 24px;
      border-bottom: 1px solid #dbe2e5;
      min-height: 72px;
      overflow: auto;
      &:last-child {
        border-bottom: none;
      }
      .left-group {
        display: inherit;
        align-items: inherit;
        column-gap: 12px;
        flex: 1 1 0%;
        min-height: 72px;
        padding: 0 16px;
        overflow: hidden;
        min-width: 350px;
        .image {
          width: 40px;
          height: 40px;
          min-width: 40px;
          border-radius: 50%;
          overflow: hidden;
          object-fit: cover;
        }
        .name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }
      .right-group {
        display: inherit;
        align-items: inherit;
        flex: 1 1 0%;
        min-height: 72px;
        .item {
          flex: 1 1 0%;
          min-height: 72px;
          display: inherit;
          align-items: inherit;
          padding-right: 16px;
          &:last-child {
            border-left: 1px solid #dbe2e5;
            justify-content: space-between;
            gap: 16px;
            padding: 0 16px;
            > div:first-child {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .sum-cost-wrap {
    height: 68px;
    display: flex;
    align-items: center;
    margin-top: 16px;
    .sum-cost-left {
      flex: 2 1 0%;
    }
    .sum-cost-right {
      flex: 1 1 0%;
      min-height: 68px;
      background: #f5f7f8;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
      padding: 0 24px;
      min-width: 300px;
      max-width: 100%;
      .cost {
        font-size: 20px;
        font-weight: 600;
        &.wait {
          color: #f9a925;
        }
      }
    }
  }
`;
type Props = {
  resetDirtyTrigger: boolean;
  costItem: any;
  handleDisableBtn: () => void;
};
const CostConfigGroup = ({
  resetDirtyTrigger,
  costItem,
  handleDisableBtn,
}: Props) => {
  const { costData, calculateCostValues } = useAppSelector(settingCostSelector);
  const [isDirty, setIsDirty] = useState<boolean>(false);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [init, setInit] = useState<boolean>(false);

  useEffect(() => {
    if (!isEmpty(costData)) {
      const targetCostData = costData.find(
        (item: any) => item.costType === costItem.costType
      );
      if (targetCostData) {
        const sumCost = targetCostData.optionCost.reduce(
          (sum: number, option: any) =>
            sum + (option.priceCostConfigDetail || 0),
          0
        );
        setTotalCost(sumCost);
      }
    }
  }, [costData, calculateCostValues]);

  useEffect(() => {
    setInit(true);
  }, []);

  useEffect(() => {
    if (init) {
      setIsDirty(false);
    }
  }, [resetDirtyTrigger]);

  return (
    <CostConfigGroupStyle>
      <div className="topic">{costItem.title}</div>
      <div className="cost-list-wrap">
        {costItem.optionCost.map((costItemOptionCost: any, idx: number) => {
          return (
            <ListCostCalculate
              idx={idx}
              key={idx}
              isImage={false}
              data={costItemOptionCost}
              handleDirty={() => {
                setIsDirty(true);
              }}
              resetDirtyTrigger={resetDirtyTrigger}
              costItem={costItem}
              handleDisableBtn={handleDisableBtn}
            />
          );
        })}
      </div>
      <div className="sum-cost-wrap">
        <div className="sum-cost-left" />
        <div className="sum-cost-right">
          <div>ราคารวม</div>
          {totalCost !== 0 && !isDirty && (
            <div className="cost">{numberWithCommas(totalCost, 2)} บาท</div>
          )}
          {(totalCost === 0 || isDirty) && (
            <div className="cost wait">รอคำนวณราคา</div>
          )}
        </div>
      </div>
    </CostConfigGroupStyle>
  );
};

export default CostConfigGroup;
