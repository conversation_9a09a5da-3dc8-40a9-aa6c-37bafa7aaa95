import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  FormControl,
  FormHelperText,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { LoadingFadein } from '@/styles/share.styled';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  setCalculateMandatoryServiceChargeValues,
  settingCostSelector,
} from '@/store/features/selling-price/setting-cost';
import { numberWithCommas } from '@/utils/number';

const MandatoryCostListCalculateStyle = styled.div`
  width: inherit;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  border-bottom: 1px solid #dbe2e5;
  min-height: 72px;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  &:last-child {
    border-bottom: none;
  }
  .left-group {
    display: inherit;
    align-items: inherit;
    column-gap: 12px;
    flex: 1 1 0%;
    min-height: 72px;
    padding: 0 16px;
    overflow: hidden;
    min-width: 180px;
    cursor: pointer;
    .image {
      width: 40px;
      height: 40px;
      min-width: 40px;
      border-radius: 50%;
      overflow: hidden;
      object-fit: cover;
    }
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }
  .right-group {
    display: inherit;
    align-items: inherit;
    min-height: 72px;
    .item-wrap {
      display: inherit;
      align-items: inherit;
    }
    .item {
      flex: 1 1 0%;
      min-height: 72px;
      display: inherit;
      align-items: inherit;
      padding-right: 16px;
      &.sum {
        border-left: 1px solid #dbe2e5;
        justify-content: space-between;
        gap: 16px;
        padding: 0 16px;
        width: 200px;
        min-width: 200px;
        max-width: 200px;
        > div:first-child {
          font-weight: 600;
        }
        &.wait {
          justify-content: center;
          color: #f9a925;
        }
      }
    }
  }
`;
type Props = {
  data: any;
  selectedToRemoveIds: number[];
  handleSelectRemove: (id: number) => void;
  handleOpenEdit: (data: any) => void;
  resetDirtyTrigger: boolean;
  handleDirty: () => void;
  handleDisableBtn: () => void;
};
const validationSchema = yup.object({
  optionCostId: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
  optionsFormulaId: yup.number().required('กรุณาเลือก').typeError('กรุณาเลือก'),
});
const ListMandatoryServiceChargeCalculate = ({
  data,
  // selectedToRemoveIds,
  // handleSelectRemove,
  // handleOpenEdit,
  resetDirtyTrigger,
  handleDirty,
  handleDisableBtn,
}: Props) => {
  const dispatch = useAppDispatch();
  const { calculateMandatoryServiceChargeValues } =
    useAppSelector(settingCostSelector);
  const [init, setInit] = useState<boolean>(false);
  const [priceCostConfigDetail, setPriceCostConfigDetail] = useState<number>(0);
  const {
    register,
    setValue,
    getFieldState,
    control,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      optionCostId: 'none',
      optionsFormulaId: 'none',
      optionsPresetId: 'none',
    },
    mode: 'onChange',
  });

  const watchOptionCostId = useWatch({
    control,
    name: 'optionCostId',
    defaultValue: 'none',
  });
  const watchOptionsFormulaId = useWatch({
    control,
    name: 'optionsFormulaId',
    defaultValue: 'none',
  });
  const watchOptionsPresetId = useWatch({
    control,
    name: 'optionsPresetId',
    defaultValue: 'none',
  });

  const isOptionsPresetIdDirty = getFieldState('optionsPresetId').isDirty;
  const isOptionsFormulaIdDirty = getFieldState('optionsFormulaId').isDirty;
  const handleChangeFormula = (value: any) => {
    handleDisableBtn();
    setValue('optionsFormulaId', value, { shouldDirty: true });
    // if (value === 'none') {
    setValue('optionsPresetId', 'none', { shouldDirty: true });
    // }
    handleDirty();
    const updatedCostConfig =
      calculateMandatoryServiceChargeValues.costConfig.map(
        (serviceChargeValuesItem: any) => {
          if (serviceChargeValuesItem.costType === data.costType) {
            return {
              ...serviceChargeValuesItem,
              optionCost: serviceChargeValuesItem.optionCost.map(
                (option: any) => ({
                  ...option,
                  optionsFormulaId: value,
                  // optionsPresetId:
                  //   value === 'none' ? 'none' : option.optionsPresetId,
                  optionsPresetId: 'none',
                })
              ),
            };
          }
          return serviceChargeValuesItem;
        }
      );

    const updatedCalculateServiceChargeValues = {
      ...calculateMandatoryServiceChargeValues,
      costConfig: updatedCostConfig,
    };
    if (updatedCalculateServiceChargeValues) {
      dispatch(
        setCalculateMandatoryServiceChargeValues(
          updatedCalculateServiceChargeValues
        )
      );
    }
  };
  const handleChangePreset = (value: any) => {
    handleDisableBtn();
    setValue('optionsPresetId', value, { shouldDirty: true });
    handleDirty();
    const updatedCostConfig =
      calculateMandatoryServiceChargeValues.costConfig.map(
        (serviceChargeValuesItem: any) => {
          if (serviceChargeValuesItem.costType === data.costType) {
            return {
              ...serviceChargeValuesItem,
              optionCost: serviceChargeValuesItem.optionCost.map(
                (option: any) => ({
                  ...option,
                  optionsPresetId: value,
                })
              ),
            };
          }
          return serviceChargeValuesItem;
        }
      );

    const updatedCalculateServiceChargeValues = {
      ...calculateMandatoryServiceChargeValues,
      costConfig: updatedCostConfig,
    };

    if (updatedCalculateServiceChargeValues) {
      dispatch(
        setCalculateMandatoryServiceChargeValues(
          updatedCalculateServiceChargeValues
        )
      );
    }
  };

  const handleRenderValue = (keepDirty: boolean) => {
    const optionsCostIdValue =
      calculateMandatoryServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.id ?? 'none';

    const optionsFormulaIdValue =
      calculateMandatoryServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.optionsFormulaId ?? 'none';

    const optionsPresetIdValue =
      calculateMandatoryServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.optionsPresetId ?? 'none';

    setValue('optionCostId', optionsCostIdValue, {
      shouldDirty: !keepDirty,
    });
    setValue('optionsFormulaId', optionsFormulaIdValue, {
      shouldDirty: !keepDirty,
    });
    setValue('optionsPresetId', optionsPresetIdValue, {
      shouldDirty: !keepDirty,
    });

    if (!keepDirty) {
      reset(
        {
          optionCostId: optionsCostIdValue,
          optionsFormulaId: optionsFormulaIdValue,
          optionsPresetId: optionsPresetIdValue,
        },
        { keepDirty: false }
      );
    }
  };

  useEffect(() => {
    if (data) {
      handleRenderValue(true);
      if (data.priceCostConfig === 0) {
        handleDirty();
      }
    }
  }, [calculateMandatoryServiceChargeValues, data, resetDirtyTrigger]);

  useEffect(() => {
    if (init) {
      handleRenderValue(false);
    }
  }, [resetDirtyTrigger]);

  useEffect(() => {
    setInit(true);
  }, []);

  const handleChangeStaticPrice = (value: number) => {
    const updatedCostConfig =
      calculateMandatoryServiceChargeValues.costConfig.map((item: any) => {
        if (item.costType === data.costType) {
          return {
            ...item,
            optionCost: item.optionCost.map((option: any) => ({
              ...option,
              priceCostConfigDetail: value,
            })),
          };
        }
        return item;
      });

    dispatch(
      setCalculateMandatoryServiceChargeValues({
        ...calculateMandatoryServiceChargeValues,
        costConfig: updatedCostConfig,
      })
    );
  };

  useEffect(() => {
    const detail =
      calculateMandatoryServiceChargeValues.costConfig.find(
        (item: any) => item.costType === data.costType
      )?.optionCost[0]?.priceCostConfigDetail ?? 0;

    setPriceCostConfigDetail(detail);
  }, [calculateMandatoryServiceChargeValues, data.costType]);

  console.log(
    'calculateMandatoryServiceChargeValues',
    calculateMandatoryServiceChargeValues
  );

  const handleChangeOptionCost = (value: any) => {
    handleDisableBtn();
    setValue('optionCostId', value, { shouldDirty: true });
    setValue('optionsFormulaId', 'none', { shouldDirty: true });
    setValue('optionsPresetId', 'none', { shouldDirty: true });
    handleDirty();
    const findOptionCost = data.optionCost.find(
      (optionCostItem: any) => optionCostItem.id === value
    );
    if (findOptionCost) {
      const updatedCostConfig =
        calculateMandatoryServiceChargeValues.costConfig.map(
          (serviceChargeValuesItem: any) => {
            if (serviceChargeValuesItem.costType === data.costType) {
              return {
                ...serviceChargeValuesItem,
                optionCost: [
                  {
                    id: findOptionCost.id,
                    name: findOptionCost.name,
                    rawMaterialId: findOptionCost.rawMaterialId,
                    componentId: findOptionCost.componentId,
                    optionsId: findOptionCost.optionsId,
                    serviceLayId: findOptionCost.serviceLayId,
                    optionsFormulaId: 'none',
                    optionsPresetId: 'none',
                    priceCostConfigDetail: null,
                  },
                ],
              };
            }
            return serviceChargeValuesItem;
          }
        );

      const updatedCalculateMandatoryServiceChargeValues = {
        ...calculateMandatoryServiceChargeValues,
        costConfig: updatedCostConfig,
      };
      if (updatedCalculateMandatoryServiceChargeValues) {
        dispatch(
          setCalculateMandatoryServiceChargeValues(
            updatedCalculateMandatoryServiceChargeValues
          )
        );
      }
    }
  };

  return (
    <MandatoryCostListCalculateStyle>
      <label className="left-group">
        <div className="name">{data.title || '-'}</div>
      </label>
      <div className="right-group">
        <div className="item-wrap">
          <div className="item">
            <FormControl
              sx={{
                width: '142px',
              }}
            >
              <Select
                error={false}
                displayEmpty
                {...register('optionCostId')}
                value={watchOptionCostId}
                onChange={(e: any) => {
                  handleChangeOptionCost(e.target.value);
                }}
              >
                <MenuItem value="none">
                  <div className="text-[#78909C]">กรุณาเลือก</div>
                </MenuItem>
                {data?.optionCost?.map((item: any, index: number) => {
                  return (
                    <MenuItem key={index} value={item.id}>
                      {item.name}
                    </MenuItem>
                  );
                })}
              </Select>
              {hookFormErrors.optionCostId && (
                <FormHelperText error>
                  {hookFormErrors.optionCostId.message as ReactNode}
                </FormHelperText>
              )}
            </FormControl>
          </div>
          {watchOptionCostId !== 'none' && (
            <div className="item">
              <FormControl
                sx={{
                  width: '142px',
                }}
              >
                <Select
                  error={false}
                  displayEmpty
                  {...register('optionsFormulaId')}
                  value={watchOptionsFormulaId}
                  onChange={(e: any) => {
                    handleChangeFormula(e.target.value);
                  }}
                >
                  <MenuItem value="none">
                    <div className="text-[#78909C]">ไม่ใช้สูตร</div>
                  </MenuItem>
                  {data?.optionCost
                    ?.find(
                      (optionCostItem: any) =>
                        optionCostItem.id === watchOptionCostId
                    )
                    ?.optionsFormula?.map(
                      (optionsFormulaItem: any, index: number) => (
                        <MenuItem key={index} value={optionsFormulaItem.id}>
                          {optionsFormulaItem.name}
                        </MenuItem>
                      )
                    )}
                </Select>
                {hookFormErrors.optionsFormulaId && (
                  <FormHelperText error>
                    {hookFormErrors.optionsFormulaId.message as ReactNode}
                  </FormHelperText>
                )}
              </FormControl>
            </div>
          )}

          {watchOptionsFormulaId !== 'none' && (
            <div className="item">
              <FormControl
                sx={{
                  width: '142px',
                }}
              >
                <Select
                  error={false}
                  displayEmpty
                  {...register('optionsPresetId')}
                  value={watchOptionsPresetId}
                  onChange={(e: any) => {
                    handleChangePreset(e.target.value);
                  }}
                >
                  <MenuItem value="none">
                    <div className="text-[#78909C]">ไม่ใช้พรีเซ็ต</div>
                  </MenuItem>
                  {data?.optionCost[0].optionsPreset.map(
                    (item: any, index: number) => {
                      return (
                        <MenuItem key={index} value={item.id}>
                          {item.name}
                        </MenuItem>
                      );
                    }
                  )}
                </Select>
              </FormControl>
            </div>
          )}
        </div>
        {watchOptionsFormulaId !== 'none' ? (
          priceCostConfigDetail !== 0 &&
          !isOptionsPresetIdDirty &&
          !isOptionsFormulaIdDirty ? (
            <div className="item sum">
              <div>{numberWithCommas(priceCostConfigDetail, 2)}</div>
              <div>บาท</div>
            </div>
          ) : priceCostConfigDetail === 0 ||
            isOptionsPresetIdDirty ||
            isOptionsFormulaIdDirty ? (
            <div className="item sum wait">
              <div>รอคำนวณ</div>
            </div>
          ) : null
        ) : (
          watchOptionCostId !== 'none' && (
            <div className="item sum">
              <TextField
                type="number"
                fullWidth
                placeholder="กรอก"
                value={priceCostConfigDetail}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  if (value < 1) {
                    e.target.value = '';
                  } else {
                    handleDirty();
                    handleChangeStaticPrice(value);
                  }
                }}
                onKeyDown={(e: any) => {
                  if (e.key === '-') {
                    e.preventDefault();
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <div>บาท</div>
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          )
        )}
      </div>
    </MandatoryCostListCalculateStyle>
  );
};

export default ListMandatoryServiceChargeCalculate;
