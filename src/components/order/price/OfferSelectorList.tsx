import React from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import LayDataOffer from './LayDataOffer';

const OfferSelectorListStyled = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 16px;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
type Props = {
  salesOrderById: any;
  handleReload: () => void;
  handleSelectQuantity: (layDataId: number, layDataQuantity: any) => void;
  setExtend: (extend: boolean) => void;
  confirmSalePriceSelected: any;
  ldData: any;
};

const OfferSelectorList = ({
  salesOrderById,
  handleReload,
  confirmSalePriceSelected,
  handleSelectQuantity,
  ldData,
  setExtend,
}: Props) => {
  // console.log('orderById', orderById);
  // console.log('ldData', ldData);
  return (
    <OfferSelectorListStyled>
      <LayDataOffer
        layDataItem={ldData}
        handleReload={handleReload}
        confirmSalePriceSelected={confirmSalePriceSelected}
        handleSelectQuantity={handleSelectQuantity}
        salesOrderById={salesOrderById}
        setExtend={setExtend}
      />
    </OfferSelectorListStyled>
  );
};
export default OfferSelectorList;
