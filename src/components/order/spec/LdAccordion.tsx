import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import AccessTimeFilledRoundedIcon from '@mui/icons-material/AccessTimeFilledRounded';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import ActionButton from '@/components/ActionButton';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { useRouter } from 'next/router';
import { AnimatePresence, motion } from 'framer-motion';
import { motionFadeDelayConfig } from '@/utils/motion/motion-config';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import OfferSelectorList from '@/components/order/price/OfferSelectorList';
import { salesOrderSelector } from '@/store/features/estimate';
import { isEmpty, isNull } from 'lodash';
import { numberWithCommas } from '@/utils/number';
import dayjs from 'dayjs';
import { dateThaiFormat } from '@/utils/date';
import { orderSelector } from '@/store/features/order';
import ArtworkZone from '@/components/order/artwork/ArtworkZone';
import CountUp from '@/components/CountUp';

const LdAccordionStyled = styled.div<{ $isExtend: boolean }>`
  width: 100%;
  min-height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #dbe2e5;
  position: relative;
  flex-direction: column;

  @keyframes pulse {
    0% {
      filter: brightness(1);
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.8);
    }
    50% {
      filter: brightness(1.2);
    }
    100% {
      filter: brightness(1);
      box-shadow: 0 0 0 8px rgba(0, 0, 0, 0);
    }
  }

  @keyframes pulseSuccess {
    0% {
      filter: brightness(1);
      box-shadow: 0 0 0 0px rgba(22, 213, 197, 0.8);
    }
    50% {
      filter: brightness(1.2);
    }
    100% {
      filter: brightness(1);
      box-shadow: 0 0 0 8px rgba(22, 213, 197, 0);
    }
  }

  &:last-child {
    .child {
      .child-line {
        height: calc(50% - 12px);
        transform: translate(-50%, calc(-50%));
      }
      .child-progress-dot {
        //
      }
    }
  }

  .accordion-parent {
    display: flex;
    align-items: center;
    column-gap: 10px;
    position: relative;
    width: 100%;
    padding: 12px 16px;
    height: 64px;
    .name {
      font-weight: 600;
    }
    .progress-dot {
      height: 16px;
      width: 16px;
      min-width: 16px;
      border-radius: 50%;
      border: 1px solid #cfd8dc;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &.current {
        border: 1px solid black;
        &:before {
          background: black;
        }
        &:after {
          background: black;
        }
      }
      &.success {
        border: none;
        &:before {
          background: #16d5c5;
          width: 16px;
          height: 16px;
        }
        &:after {
          background: #16d5c5;
        }
      }
      &:before {
        content: '';
        position: absolute;
        height: 8px;
        width: 8px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #cfd8dc;
        border-radius: 50%;
      }
      &:after {
        content: '';
        position: absolute;
        height: 8px;
        width: 8px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
      }
    }
    .name {
      font-weight: 600;
      &.disabled {
        color: #cfd8dc;
      }
    }
    .arrow-wrap {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      * {
        transition: 0.15s ease-out;
      }
      &:hover {
        background: #dbe2e5;
      }
    }
  }

  .child {
    width: 100%;
    padding: 0 24px;
    background: white;
    display: flex;
    align-items: center;
    > div:nth-child(3) {
      min-height: 0;
    }
    &.current {
      background: #f5f7f8;
    }
    .child-progress-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: black;
      position: absolute;
      transform: translate(-50%, 1px);
      z-index: 3;
      &.current {
        &:after {
          content: '';
          position: absolute;
          height: 8px;
          width: 8px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          animation: pulse 1.8s linear infinite;
        }
      }
      &.success {
        background: #16d5c5;
        &:after {
          content: '';
          position: absolute;
          height: 8px;
          width: 8px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          animation: pulseSuccess 1.8s linear infinite;
        }
      }
    }
    .child-line {
      position: absolute;
      height: calc(100% - 20px);
      width: 2px;
      background: black;
      border-radius: 24px;
      z-index: 1;
      transform: translate(-50%, 0.5px);
      &.disabled {
        background: #dbe2e5;
      }
      &.success {
        background: #16d5c5;
      }
      &.current {
        &:before {
          content: '';
          position: absolute;
          height: 50%;
          top: 50%;
          width: 2px;
          background: #dbe2e5;
        }
      }
    }
    .text-no-data {
      color: #90a4ae;
      padding: 24px 16px;
      display: flex;
      position: relative;
      flex-direction: column;
      width: 100%;
      &.spec {
        color: #263238;
      }
      .topic {
        font-weight: 600;
      }
    }
    .action-btn-wrap {
      position: absolute;
      top: 80px;
      right: 16px;
      display: flex;
      align-items: center;
      column-gap: 12px;
    }
  }
`;

const StatusChipStyled = styled.div`
  display: flex;
  align-items: center;
  column-gap: 4px;
  font-size: 14px;
  justify-content: center;
  &.pending {
    color: #fbc02d;
  }
  &.done {
    color: #8bc34a;
  }
`;

const SpecDetailStyled = styled.div`
  background: white;
  margin-top: 28px;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  padding: 12px 16px;
  display: flex;
  gap: 16px;
  .item {
    display: flex;
    flex-direction: column;
    flex: 1 1 0%;
    .key {
      font-size: 12px;
      color: #90a4ae;
    }
    .value {
      display: flex;
      flex-direction: column;
    }
  }
`;
type Props = {
  data: any;
  ldData: any;
  makeConfirmSpec: () => void;
  makeConfirmLayout: () => void;
  makePrintPlateData: () => void;
  reloadOrder: () => void;
  confirmSalePriceSelected: any;
  handleSelectQuantity: (layDataId: number, layDataQuantity: any) => void;
  isSaleOrder?: boolean;
};

const LdAccordion = ({
  data,
  ldData,
  makeConfirmSpec,
  makeConfirmLayout,
  makePrintPlateData,
  reloadOrder,
  confirmSalePriceSelected,
  handleSelectQuantity,
  isSaleOrder,
}: Props) => {
  const router = useRouter();
  const { orderId } = router.query;
  const [isExtend, setIsExtend] = useState<boolean>(false);
  const { orderById } = useAppSelector(orderSelector);
  const { salesOrderById } = useAppSelector(salesOrderSelector);
  const { isSpec, isConfirm } = ldData;
  const layDataStatus: any = isSaleOrder
    ? ldData.estimateProductStatus
    : ldData.layDataStatus;
  const isDisabled = layDataStatus.id < data.id;
  const isCurrentChild = layDataStatus.id === data.id;
  const [isConfirmPrice, setIsConfirmPrice] = useState<boolean>(false);
  const handleClickChildActionButton = async () => {
    //
    if (data.id === 1) {
      await router.push(
        `/sales-order/${salesOrderById.id}/prepare/${ldData.id}`
      );
    }
  };

  useEffect(() => {
    if (ldData && isSaleOrder) {
      const someConfirmPrice = ldData.estimateQuantity.some(
        (eqty: any) => eqty.isConfirm
      );
      setIsConfirmPrice(someConfirmPrice);
    }
  }, [ldData]);
  // console.log('ldData', ldData);
  // console.log('layDataStatus', layDataStatus);
  // console.log('orderById', orderById);
  return (
    <LdAccordionStyled $isExtend={isExtend}>
      <div
        className={`accordion-parent ${
          isDisabled ? 'disabled cursor-default' : 'cursor-pointer'
        }`}
        onClick={async () => {
          if (!isDisabled) {
            setIsExtend((prev) => !prev);
          }
          if (['อาร์ตเวิร์ก', 'การผลิตสินค้า', 'จัดส่ง'].includes(data.name)) {
            if (data.name === 'การผลิตสินค้า' && !isDisabled) {
              await router.push(`/job`);
            } else {
              //
            }
          }
        }}
      >
        {data.name === 'สเปคสินค้า' && (
          <>
            <div
              className={`progress-dot ${isDisabled ? '' : 'current'} ${
                isConfirm ? 'success' : ''
              }`}
            >
              {isConfirm && (
                <CheckRoundedIcon
                  sx={{
                    fontSize: '12px',
                    zIndex: '1',
                    color: 'white',
                  }}
                />
              )}
            </div>
            <div className={`name ${isDisabled ? 'disabled' : ''}`}>
              {data.name}
            </div>
            {layDataStatus.id === data.id && (
              <StatusChipStyled
                className={
                  !isSpec || !isConfirm ? 'pending' : isConfirm ? 'done' : ''
                }
              >
                {!isSpec || !isConfirm ? (
                  <AccessTimeFilledRoundedIcon />
                ) : isConfirm ? (
                  <CheckCircleRoundedIcon />
                ) : null}

                {!isSpec ? 'รอกำหนดสเปคสินค้า' : !isConfirm ? 'รอยืนยัน' : null}
              </StatusChipStyled>
            )}
            {!isNull(ldData.confirmSpecDate) && (
              <StatusChipStyled className="done">
                ยืนยันสเปคแล้ว
                <span
                  style={{
                    color: '#B0BEC5',
                  }}
                >
                  วันที่{' '}
                  {dayjs(ldData.confirmSpecDate).format('DD/MM/YYYY, HH:mm น.')}
                </span>
              </StatusChipStyled>
            )}
          </>
        )}
        {data.name === 'เลย์เอาท์' && (
          <>
            <div
              className={`progress-dot ${isDisabled ? '' : 'current'} ${
                ldData.printPlate?.isConfirm ? 'success' : ''
              }`}
            >
              {ldData.printPlate?.isConfirm && (
                <CheckRoundedIcon
                  sx={{
                    fontSize: '12px',
                    zIndex: '1',
                    color: 'white',
                  }}
                />
              )}
            </div>
            <div className={`name ${isDisabled ? 'disabled' : ''}`}>
              {data.name}
            </div>
            {layDataStatus.id === data.id && (
              <StatusChipStyled
                className={
                  !ldData.printPlate?.isConfirm
                    ? 'pending'
                    : ldData.printPlate?.isConfirm
                    ? 'done'
                    : ''
                }
              >
                {!ldData.printPlate?.isConfirm ? (
                  <AccessTimeFilledRoundedIcon />
                ) : ldData.printPlate?.isConfirm ? (
                  <CheckCircleRoundedIcon />
                ) : null}

                {isNull(ldData.printPlate?.itemSize)
                  ? 'รอกำหนดเลย์เอาท์'
                  : !ldData.printPlate?.isConfirm
                  ? 'รอยืนยันเลย์เอาท์'
                  : null}
              </StatusChipStyled>
            )}
            {!isNull(ldData.confirmLayoutDate) && (
              <StatusChipStyled className="done">
                ยืนยันเลย์เอาท์แล้ว{' '}
                <span style={{ color: '#B0BEC5' }}>
                  วันที่{' '}
                  {dayjs(ldData.confirmLayoutDate).format(
                    'DD/MM/YYYY, HH:mm น.'
                  )}
                </span>
              </StatusChipStyled>
            )}
          </>
        )}
        {data.name === 'เสนอราคา' && (
          <>
            <div
              className={`progress-dot ${isDisabled ? '' : 'current'} ${
                isConfirmPrice ? 'success' : ''
              }`}
            >
              {isConfirmPrice && (
                <CheckRoundedIcon
                  sx={{
                    fontSize: '12px',
                    zIndex: '1',
                    color: 'white',
                  }}
                />
              )}
            </div>
            <div className={`name ${isDisabled ? 'disabled' : ''}`}>
              {data.name}
            </div>
            {layDataStatus.id === data.id && (
              <StatusChipStyled
                className={`${
                  !isConfirmPrice ? 'pending' : isConfirmPrice ? 'done' : ''
                }`}
              >
                {!isConfirmPrice ? (
                  <AccessTimeFilledRoundedIcon />
                ) : isConfirmPrice ? (
                  <CheckCircleRoundedIcon />
                ) : null}
                {!isConfirmPrice ? 'รอยืนยันราคาขาย' : null}
              </StatusChipStyled>
            )}
            {!isNull(ldData.confirmQuotationDate) && (
              <StatusChipStyled className="done">
                ยืนยันราคาขายแล้ว{' '}
                <span style={{ color: '#B0BEC5' }}>
                  วันที่{' '}
                  {dayjs(ldData.confirmQuotationDate).format(
                    'DD/MM/YYYY, HH:mm น.'
                  )}
                </span>
              </StatusChipStyled>
            )}
          </>
        )}
        {data.name === 'ยืนยันคำสั่งซื้อ' && (
          <>
            <div
              className={`progress-dot ${isDisabled ? '' : 'current'} ${
                isConfirmPrice ? 'success' : ''
              }`}
            >
              {isConfirmPrice && (
                <CheckRoundedIcon
                  sx={{
                    fontSize: '12px',
                    zIndex: '1',
                    color: 'white',
                  }}
                />
              )}
            </div>
            <div className={`name ${isDisabled ? 'disabled' : ''}`}>
              {data.name}
            </div>
          </>
        )}
        {data.name === 'การชำระเงิน' && (
          <>
            <div
              className={`progress-dot ${isDisabled ? '' : 'current'} ${
                layDataStatus.id > 1 ? 'success' : ''
              }`}
            >
              {layDataStatus.id > 1 && (
                <CheckRoundedIcon
                  sx={{
                    fontSize: '12px',
                    zIndex: '1',
                    color: 'white',
                  }}
                />
              )}
            </div>
            <div className={`name ${isDisabled ? 'disabled' : ''}`}>
              {data.name}
            </div>
            {layDataStatus.id === data.id && (
              <StatusChipStyled
                className={
                  ldData.layDataStatus.id === 1
                    ? 'pending'
                    : ldData.layDataStatus.id > 1
                    ? 'done'
                    : ''
                }
              >
                {ldData.layDataStatus.id === 1 ? (
                  <AccessTimeFilledRoundedIcon />
                ) : ldData.layDataStatus.id > 1 ? (
                  <CheckCircleRoundedIcon />
                ) : null}

                {ldData.layDataStatus.id === 1
                  ? 'รอชำระเงิน'
                  : ldData.layDataStatus.id > 1
                  ? 'ชำระเงินแล้ว'
                  : null}
              </StatusChipStyled>
            )}
            {ldData.layDataStatus.id > 1 && (
              <StatusChipStyled className="done">
                {ldData.layDataStatus.id === 1 ? (
                  <AccessTimeFilledRoundedIcon />
                ) : ldData.layDataStatus.id > 1 ? (
                  <CheckCircleRoundedIcon />
                ) : null}
                ชำระเงินแล้ว {/* <span style={{ color: '#B0BEC5' }}> */}
                {/*  วันที่{' '} */}
                {/*  {dayjs(ldData.confirmLayoutDate).format( */}
                {/*    'DD/MM/YYYY, HH:mm น.' */}
                {/*  )} */}
                {/* </span> */}
              </StatusChipStyled>
            )}
          </>
        )}
        {![
          'สเปคสินค้า',
          'เลย์เอาท์',
          'เสนอราคา',
          'ยืนยันคำสั่งซื้อ',
          'การชำระเงิน',
        ].includes(data.name) &&
          (data.name === 'อาร์ตเวิร์ก' ? (
            <>
              <div
                className={`progress-dot ${isDisabled ? '' : 'current'} ${
                  layDataStatus.name === 'การผลิตสินค้า' ? 'success' : ''
                }`}
              >
                {layDataStatus.name === 'การผลิตสินค้า' && (
                  <CheckRoundedIcon
                    sx={{
                      fontSize: '12px',
                      zIndex: '1',
                      color: 'white',
                    }}
                  />
                )}
              </div>
              <div className={`name ${!isDisabled ? '' : 'disabled'}`}>
                {data.name}
              </div>
            </>
          ) : data.name === 'การผลิตสินค้า' ? (
            <>
              <div
                className={`progress-dot ${isDisabled ? '' : 'current'} ${
                  layDataStatus.name === 'จัดส่ง' ? 'success' : ''
                }`}
              ></div>
              <div className={`name ${!isDisabled ? '' : 'disabled'}`}>
                {data.name}
              </div>
            </>
          ) : (
            <>
              <div className="progress-dot"></div>
              <div
                className={`name ${layDataStatus.id >= 6 ? '' : 'disabled'}`}
              >
                {data.name}
              </div>
            </>
          ))}
        {[
          'สเปคสินค้า',
          'เลย์เอาท์',
          'เสนอราคา',
          'การชำระเงิน',
          'อาร์ตเวิร์ก',
          // 'การผลิตสินค้า',
          // 'จัดส่ง',
        ].includes(data.name) && (
          <div
            className={`arrow-wrap ${
              isDisabled
                ? 'cursor-default hover:!bg-transparent'
                : 'cursor-pointer'
            }`}
          >
            <KeyboardArrowDownRoundedIcon
              sx={{
                rotate: isExtend ? '180deg' : 'none',
              }}
            />
          </div>
        )}
      </div>
      {data.name === 'สเปคสินค้า' && (
        <motion.div
          key={`child-${data.name}`}
          initial={{ height: 0 }}
          animate={{
            height: isExtend ? 'auto' : 0,
            transition: {
              height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
            },
          }}
          className={`child ${isCurrentChild ? 'current' : ''}`}
        >
          <AnimatePresence initial={false} mode="sync">
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: isExtend ? 1 : 0,
                scale: isExtend ? 1 : 0,
                x: '-50%',
                transition: {
                  opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                  scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
                },
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: { opacity: { duration: 0.3 } },
              }}
              className={`child-progress-dot current ${
                isConfirm ? 'success' : ''
              }`}
            />
            <div
              className={`child-line ${isDisabled ? 'disabled' : ''}${
                layDataStatus.id === data.id ? 'current' : ''
              } ${isConfirm ? 'success' : ''}`}
            />
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="text-no-data spec"
                key="text-no-data"
              >
                <span className="topic">ข้อมูลสเปคสินค้า</span>
                <AnimatePresence initial={true} mode="sync">
                  {isSpec && (
                    <SpecDetailStyled
                      as={motion.div}
                      {...motionFadeDelayConfig}
                      key="spec-detail"
                      style={{
                        marginTop: isConfirm ? '20px' : '28px',
                      }}
                    >
                      <div className="item">
                        <div className="key">สินค้า</div>
                        <div className="value">
                          <div>
                            ขนาด : {ldData.length} x {ldData.width} x{' '}
                            {ldData.height} mm
                          </div>
                          <div>
                            จำนวนผลิต{' '}
                            {numberWithCommas(
                              ldData.estimateQuantity[0].quantity
                            )}{' '}
                            ชิ้น
                          </div>
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">วัสดุ</div>
                        <div className="value">
                          <div>{ldData.subMaterialDetail.name}</div>
                          <div>
                            พิมพ์{' '}
                            {ldData.printingRequest.printSystem !== null
                              ? !isEmpty(ldData.printingRequest.colorFront) &&
                                !isEmpty(ldData.printingRequest.colorBack)
                                ? 'ด้านหน้า / ด้านหลัง'
                                : !isEmpty(ldData.printingRequest.colorFront)
                                ? 'ด้านหน้า'
                                : !isEmpty(ldData.printingRequest.colorBack)
                                ? 'ด้านหลัง'
                                : 'ไม่พิมพ์'
                              : 'ไม่พิมพ์'}
                          </div>
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">การพิมพ์</div>
                        <div className="value">
                          <div>
                            หน้า :{' '}
                            {(() => {
                              const { colorFront } = ldData.printingRequest;
                              if (isEmpty(colorFront)) return 'ไม่พิมพ์';

                              const colorCounts = new Map<string, number>();
                              colorFront.forEach((item: any) => {
                                const name =
                                  item.printColor?.name === 'PANTONE'
                                    ? 'Pantone'
                                    : item.printColor?.name;
                                if (!name) return;
                                colorCounts.set(
                                  name,
                                  (colorCounts.get(name) || 0) + 1
                                );
                              });

                              return Array.from(colorCounts.entries())
                                .map(([name, count]) =>
                                  name === 'Pantone' && count > 1
                                    ? `${name} ${count} สี`
                                    : name
                                )
                                .join(', ');
                            })()}
                          </div>

                          {/* ด้านหลัง */}
                          <div>
                            หลัง :{' '}
                            {(() => {
                              const { colorBack } = ldData.printingRequest;
                              if (isEmpty(colorBack)) return 'ไม่พิมพ์';

                              const colorCounts = new Map<string, number>();
                              colorBack.forEach((item: any) => {
                                const name =
                                  item.printColor?.name === 'PANTONE'
                                    ? 'Pantone'
                                    : item.printColor?.name;
                                if (!name) return;
                                colorCounts.set(
                                  name,
                                  (colorCounts.get(name) || 0) + 1
                                );
                              });

                              return Array.from(colorCounts.entries())
                                .map(([name, count]) =>
                                  name === 'Pantone' && count > 1
                                    ? `${name} ${count} สี`
                                    : name
                                )
                                .join(', ');
                            })()}
                          </div>
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">การเคลือบ</div>
                        <div className="value">
                          <div>
                            หน้า :{' '}
                            {!isEmpty(ldData.coatingRequest?.finishFront)
                              ? ldData.coatingRequest.finishFront
                                  .map(
                                    (item: any) =>
                                      item.finishSubMaterialDetail?.name
                                  )
                                  .filter(Boolean)
                                  .join(', ')
                              : 'ไม่เคลือบ'}
                          </div>
                          <div>
                            หลัง :{' '}
                            {!isEmpty(ldData.coatingRequest?.finishBack)
                              ? ldData.coatingRequest.finishBack
                                  .map(
                                    (item: any) =>
                                      item.finishSubMaterialDetail?.name
                                  )
                                  .filter(Boolean)
                                  .join(', ')
                              : 'ไม่เคลือบ'}
                          </div>
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">เทคนิคพิเศษ</div>
                        <div className="value">
                          {/* หน้า */}
                          <div>
                            หน้า :{' '}
                            {ldData.extra.filter(
                              (item: any) =>
                                item.extraSideDimension?.name === 'ด้านหน้า'
                            ).length || 'ไม่มี'}
                            {ldData.extra.filter(
                              (item: any) =>
                                item.extraSideDimension?.name === 'ด้านหน้า'
                            ).length
                              ? ' รายการ'
                              : ''}
                          </div>
                          {/* หลัง */}
                          <div>
                            หลัง :{' '}
                            {ldData.extra.filter(
                              (item: any) =>
                                item.extraSideDimension?.name === 'ด้านหลัง'
                            ).length || 'ไม่มี'}
                            {ldData.extra.filter(
                              (item: any) =>
                                item.extraSideDimension?.name === 'ด้านหลัง'
                            ).length
                              ? ' รายการ'
                              : ''}
                          </div>
                        </div>
                      </div>
                      <div className="item">
                        <div className="key">อาร์ตเวิร์ก</div>
                        <div className="value">
                          {/* แสดงการออกแบบ */}
                          <div>
                            ออกแบบ :{' '}
                            {ldData.productDesign?.name
                              ? ldData.productDesign.name
                              : '-'}
                          </div>

                          {/* แสดงตัวอย่างสินค้า */}
                          <div>
                            ตัวอย่างสินค้า :{' '}
                            {ldData.masterExample?.length > 0
                              ? ldData.masterExample
                                  .map(
                                    (item: any) =>
                                      `${item.masterExampleId} ${item.name}`
                                  )
                                  .join(', ')
                              : 'ไม่มี'}
                          </div>
                        </div>
                      </div>
                    </SpecDetailStyled>
                  )}
                </AnimatePresence>
              </motion.div>
            )}
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="action-btn-wrap"
                key="action-btn-wrap"
              >
                {isSpec && !isConfirm && (
                  <ActionButton
                    variant="contained"
                    color="Hon"
                    icon={
                      <CheckCircleRoundedIcon
                        sx={{
                          fontSize: '22px !important',
                        }}
                      />
                    }
                    text="ยืนยันสเปคถูกต้อง"
                    borderRadius="8px"
                    onClick={async () => {
                      makeConfirmSpec();
                    }}
                  />
                )}
                {!isConfirm && (
                  <ActionButton
                    variant="contained"
                    color="dark"
                    icon={
                      <Image
                        src="/icons/icon-deployed-code.svg"
                        alt=""
                        width={24}
                        height={24}
                      />
                    }
                    text="กำหนดสเปคสินค้า"
                    borderRadius="8px"
                    onClick={async () => {
                      await handleClickChildActionButton();
                    }}
                  />
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
      {data.name === 'เลย์เอาท์' && (
        <motion.div
          key={`child-${data.name}`}
          initial={{ height: 0 }}
          animate={{
            height: isExtend ? 'auto' : 0,
            transition: {
              height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
            },
          }}
          className={`child ${isCurrentChild ? 'current' : ''}`}
        >
          <AnimatePresence initial={false} mode="sync">
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: isExtend ? 1 : 0,
                scale: isExtend ? 1 : 0,
                x: '-50%',
                transition: {
                  opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                  scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
                },
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: { opacity: { duration: 0.3 } },
              }}
              className={`child-progress-dot current ${
                ldData.printPlate?.isConfirm ? 'success' : ''
              }`}
            />
            <div
              className={`child-line ${isDisabled ? 'disabled' : ''}${
                layDataStatus.id === data.id ? 'current' : ''
              } ${ldData.printPlate?.isConfirm ? 'success' : ''}`}
            />
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="text-no-data spec"
                key="layout-spec-detail"
              >
                <span className="topic">ข้อมูลเลย์เอาท์</span>
                <SpecDetailStyled
                  as={motion.div}
                  {...motionFadeDelayConfig}
                  style={{
                    marginTop: ldData.printPlate?.isConfirm ? '20px' : '28px',
                  }}
                >
                  <div className="item">
                    <div className="key">Model</div>
                    <div className="value">
                      <div>{ldData.productModel?.productModelName ?? '-'}</div>
                      <div>
                        ขนาดกางออก {ldData.length} x {ldData.width} x{' '}
                        {ldData.height} mm
                      </div>
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">ขนาดวัสดุ</div>
                    <div className="value">
                      <div>
                        ใบเต็ม : ใบเต็ม :{' '}
                        {ldData.printPlate?.itemSize?.itemSizeName ?? '-'}
                      </div>
                      <div>
                        ใบพิมพ์ :{' '}
                        {ldData.printPlate?.subItemSize?.subItemName ?? '-'}
                      </div>
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">เคลือบ ก่อนพิมพ์</div>
                    <div className="value">
                      <div>
                        ด้านหน้า:{' '}
                        {ldData.coatingRequest?.finishFront?.some(
                          (item: any) => item.coatingPositionEnum === 'BEFORE'
                        )
                          ? ldData.coatingRequest.finishFront
                              .filter(
                                (item: any) =>
                                  item.coatingPositionEnum === 'BEFORE'
                              )
                              .map(
                                (item: any) =>
                                  item.finishSubMaterialDetail?.name
                              )
                              .filter(Boolean)
                              .join(', ')
                          : '-'}
                      </div>
                      <div>
                        ด้านหลัง:{' '}
                        {ldData.coatingRequest?.finishBack?.some(
                          (item: any) => item.coatingPositionEnum === 'BEFORE'
                        )
                          ? ldData.coatingRequest.finishBack
                              .filter(
                                (item: any) =>
                                  item.coatingPositionEnum === 'BEFORE'
                              )
                              .map(
                                (item: any) =>
                                  item.finishSubMaterialDetail?.name
                              )
                              .filter(Boolean)
                              .join(', ')
                          : '-'}
                      </div>
                    </div>
                  </div>

                  <div className="item">
                    <div className="key">เคลือบ หลังพิมพ์</div>
                    <div className="value">
                      <div>
                        ด้านหน้า:{' '}
                        {ldData.coatingRequest?.finishFront?.some(
                          (item: any) => item.coatingPositionEnum === 'AFTER'
                        )
                          ? ldData.coatingRequest.finishFront
                              .filter(
                                (item: any) =>
                                  item.coatingPositionEnum === 'AFTER'
                              )
                              .map(
                                (item: any) =>
                                  item.finishSubMaterialDetail?.name
                              )
                              .filter(Boolean)
                              .join(', ')
                          : 'ไม่เคลือบ'}
                      </div>
                      <div>
                        ด้านหลัง:{' '}
                        {ldData.coatingRequest?.finishBack?.some(
                          (item: any) => item.coatingPositionEnum === 'AFTER'
                        )
                          ? ldData.coatingRequest.finishBack
                              .filter(
                                (item: any) =>
                                  item.coatingPositionEnum === 'AFTER'
                              )
                              .map(
                                (item: any) =>
                                  item.finishSubMaterialDetail?.name
                              )
                              .filter(Boolean)
                              .join(', ')
                          : 'ไม่เคลือบ'}
                      </div>
                    </div>
                  </div>

                  <div className="item">
                    <div className="key">แม่พิมพ์</div>
                    <div className="value">
                      <div>
                        เพลต :{' '}
                        {ldData.printPlate?.plateRawMaterial?.subMaterialDetail
                          ?.name
                          ? ldData.printPlate.plateRawMaterial.subMaterialDetail
                              .name
                          : '-'}
                      </div>
                      <div>
                        ไดคัท :{' '}
                        {ldData.printPlate?.dieCutRawMaterial?.subMaterialDetail
                          ?.name
                          ? ldData.printPlate.dieCutRawMaterial
                              .subMaterialDetail.name
                          : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">รูปภาพ</div>
                    <div className="value">
                      <div>
                        ด้านหน้า :{' '}
                        {ldData.printPlate?.layoutFrontUrl ? (
                          <a
                            href={ldData.printPlate.layoutFrontUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              textDecoration: 'underline',
                            }}
                          >
                            ดูภาพ
                          </a>
                        ) : (
                          '-'
                        )}
                      </div>
                      <div>
                        ด้านหลัง :{' '}
                        {ldData.printPlate?.layoutBackUrl ? (
                          <a
                            href={ldData.printPlate.layoutBackUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              textDecoration: 'underline',
                            }}
                          >
                            ดูภาพ
                          </a>
                        ) : (
                          '-'
                        )}
                      </div>
                    </div>
                  </div>
                </SpecDetailStyled>
                {!isNull(ldData.printingRequest.printSystem) && (
                  <SpecDetailStyled
                    as={motion.div}
                    {...motionFadeDelayConfig}
                    style={{
                      marginTop: ldData.printPlate?.isConfirm ? '20px' : '28px',
                    }}
                  >
                    <div className="item">
                      <div className="key">การพิมพ์</div>
                      <div className="value">
                        <div className="flex gap-[20px] items-center">
                          {ldData.printingRequest.colorFront.map(
                            (cf: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-[8px]"
                                >
                                  <Image
                                    src={
                                      cf.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    alt=""
                                    width={16}
                                    height={16}
                                    style={{
                                      borderRadius: '50%',
                                    }}
                                  />
                                  <div
                                    style={{
                                      fontWeight: 600,
                                    }}
                                  >
                                    {cf.printColor.name}
                                    {!isEmpty(cf.colorCode) &&
                                    cf.colorCode !== 'CMYK'
                                      ? ` / ${cf.colorCode}`
                                      : ''}{' '}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      • ด้านหน้า
                                    </span>
                                  </div>
                                </div>
                              );
                            }
                          )}
                          {ldData.printingRequest.colorBack.map(
                            (cb: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-[8px]"
                                >
                                  <Image
                                    src={
                                      cb.imageUrl ||
                                      '/images/product/empty-product.svg'
                                    }
                                    alt=""
                                    width={16}
                                    height={16}
                                    style={{
                                      borderRadius: '50%',
                                    }}
                                  />
                                  <div
                                    style={{
                                      fontWeight: 600,
                                    }}
                                  >
                                    {cb.printColor.name}
                                    {!isEmpty(cb.colorCode) &&
                                    cb.colorCode !== 'CMYK'
                                      ? ` / ${cb.colorCode}`
                                      : ''}{' '}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      • ด้านหลัง
                                    </span>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </div>
                      </div>
                    </div>
                  </SpecDetailStyled>
                )}
                {(!isEmpty(ldData.coatingRequest.finishBack) ||
                  !isEmpty(ldData.coatingRequest.finishFront)) && (
                  <SpecDetailStyled
                    as={motion.div}
                    {...motionFadeDelayConfig}
                    style={{
                      marginTop: ldData.printPlate?.isConfirm ? '20px' : '28px',
                    }}
                  >
                    <div className="item">
                      <div className="key">เทคนิคพิเศษ</div>
                      <div className="value">
                        <div className="flex gap-[20px] items-center">
                          {ldData.coatingRequest.finishFront.map(
                            (ff: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-[8px]"
                                >
                                  <div
                                    style={{
                                      fontWeight: 600,
                                    }}
                                  >
                                    {ff.finishSubMaterialDetail.name}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      {' '}
                                      •{' '}
                                      {ff.coatingPositionEnum === 'BEFORE'
                                        ? 'ก่อนพิมพ์'
                                        : 'หลังพิมพ์'}
                                    </span>{' '}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      • ด้านหน้า
                                    </span>
                                  </div>
                                </div>
                              );
                            }
                          )}
                          {ldData.coatingRequest.finishBack.map(
                            (fb: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-[8px]"
                                >
                                  <div
                                    style={{
                                      fontWeight: 600,
                                    }}
                                  >
                                    {fb.finishSubMaterialDetail.name}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      {' '}
                                      •{' '}
                                      {fb.coatingPositionEnum === 'BEFORE'
                                        ? 'ก่อนพิมพ์'
                                        : 'หลังพิมพ์'}
                                    </span>{' '}
                                    <span
                                      style={{
                                        fontWeight: 400,
                                      }}
                                    >
                                      • ด้านหน้า
                                    </span>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </div>
                      </div>
                    </div>
                  </SpecDetailStyled>
                )}
              </motion.div>
            )}
            {isExtend && !ldData.printPlate?.isConfirm && (
              <motion.div
                {...motionFadeDelayConfig}
                className="action-btn-wrap"
                key="action-btn-wrap"
              >
                <ActionButton
                  variant="contained"
                  color="Hon"
                  icon={
                    <CheckCircleRoundedIcon
                      sx={{ fontSize: '22px !important' }}
                    />
                  }
                  text="ยืนยันเลย์เอาท์ถูกต้อง"
                  borderRadius="8px"
                  onClick={async () => {
                    makeConfirmLayout();
                  }}
                  disabled={isNull(ldData.printPlate.itemSize)}
                />
                {!ldData.printPlate?.isConfirm && (
                  <ActionButton
                    variant="contained"
                    color="dark"
                    icon={
                      <Image
                        src="/icons/icon-deployed-code.svg"
                        alt=""
                        width={24}
                        height={24}
                      />
                    }
                    text="กำหนดเลย์เอาท์"
                    borderRadius="8px"
                    onClick={() => {
                      makePrintPlateData();
                    }}
                  />
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
      {data.name === 'เสนอราคา' && (
        <motion.div
          key={`child-${data.name}`}
          initial={{ height: 0 }}
          animate={{
            height: isExtend ? 'auto' : 0,
            transition: {
              height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
            },
          }}
          className={`child !pr-[16px] ${isCurrentChild ? 'current' : ''}`}
        >
          <AnimatePresence initial={false} mode="sync">
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: isExtend ? 1 : 0,
                scale: isExtend ? 1 : 0,
                x: '-50%',
                transition: {
                  opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                  scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
                },
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: { opacity: { duration: 0.3 } },
              }}
              className={`child-progress-dot current ${
                isConfirmPrice ? 'success' : ''
              }`}
            />
            <div
              className={`child-line ${isDisabled ? 'disabled' : ''}${
                layDataStatus.id === data.id ? 'current' : ''
              } ${isConfirmPrice ? 'success' : ''}`}
            />
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                animate={{
                  opacity: 1,
                  transition: { opacity: { duration: 0.3, delay: 0.6 } },
                }}
                key="price-selector"
                className="w-full"
                style={{
                  padding: '16px 0 16px 16px',
                }}
              >
                <OfferSelectorList
                  salesOrderById={salesOrderById}
                  handleReload={reloadOrder}
                  confirmSalePriceSelected={confirmSalePriceSelected}
                  handleSelectQuantity={handleSelectQuantity}
                  ldData={ldData}
                  setExtend={setIsExtend}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
      {data.name === 'การชำระเงิน' && (
        <motion.div
          key={`child-${data.name}`}
          initial={{ height: 0 }}
          animate={{
            height: isExtend ? 'auto' : 0,
            transition: {
              height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
            },
          }}
          className={`child ${isCurrentChild ? 'current' : ''}`}
        >
          <AnimatePresence initial={false} mode="sync">
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: isExtend ? 1 : 0,
                scale: isExtend ? 1 : 0,
                x: '-50%',
                transition: {
                  opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                  scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
                },
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: { opacity: { duration: 0.3 } },
              }}
              className={`child-progress-dot current ${
                ldData.layDataStatus.id > 1 ? 'success' : ''
              }`}
            />
            <div
              className={`child-line ${isDisabled ? 'disabled' : ''}${
                layDataStatus.id === data.id ? 'current' : ''
              } ${ldData.layDataStatus.id > 1 ? 'success' : ''}`}
            />
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="text-no-data spec"
                key="layout-spec-detail"
              >
                <span className="topic">ข้อมูลการชำระเงิน</span>
                <SpecDetailStyled
                  as={motion.div}
                  {...motionFadeDelayConfig}
                  style={{
                    marginTop: '28px',
                  }}
                >
                  <div className="item">
                    <div className="key">เลขที่</div>
                    <div className="value">
                      {ldData.paymentQuotations?.map(
                        (item: any, index: number) => {
                          return (
                            <div key={index}>{item.quotationNo || '-'}</div>
                          );
                        }
                      )}
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">ราคารวม (บาท)</div>
                    <div className="value">
                      <div>
                        {ldData.paymentQuotations?.map(
                          (item: any, index: number) => {
                            return (
                              <div key={index}>
                                <CountUp
                                  from={0}
                                  to={item.totalPrice || 0}
                                  separator=","
                                  duration={0.3}
                                  decimals={2}
                                />
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">สถานะ</div>
                    <div className="value">
                      <div>
                        {ldData.paymentQuotations?.map(
                          (item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                style={{
                                  color:
                                    item.status.name === 'ตอบรับแล้ว'
                                      ? '#8BC34A'
                                      : item.status.name === 'แบบร่าง'
                                      ? 'gray'
                                      : item.status.name === 'รออนุมัติ'
                                      ? 'orange'
                                      : item.status.name === 'รอตอบรับ'
                                      ? 'orange'
                                      : item.status.name === 'ปฏิเสธ'
                                      ? 'red'
                                      : 'initial',
                                }}
                              >
                                {item.status.name || '-'}
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="item">
                    <div className="key">วันครบกำหนด</div>
                    <div className="value">
                      <div>
                        {ldData.paymentQuotations?.map(
                          (item: any, index: number) => {
                            return (
                              <div key={index}>
                                {dateThaiFormat(item.dueDate)}
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="item">
                    <div className="key">วันที่สร้าง</div>
                    <div className="value">
                      <div>
                        {ldData.paymentQuotations?.map(
                          (item: any, index: number) => {
                            return (
                              <div key={index}>
                                {dateThaiFormat(item.createdDate)}
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  </div>
                </SpecDetailStyled>
              </motion.div>
            )}
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="action-btn-wrap"
                key="action-btn-wrap"
              >
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  icon={
                    <Image
                      src={'/icons/icon-export-notes.svg'}
                      alt=""
                      width={24}
                      height={24}
                    />
                  }
                  text="รายละเอียด"
                  borderRadius="8px"
                  onClick={async () => {
                    await router.push(
                      `/orders/${orderId}/${ldData.id}/payment`
                    );
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}

      {data.name === 'อาร์ตเวิร์ก' && (
        <motion.div
          key={`child-${data.name}`}
          initial={{ height: 0 }}
          animate={{
            height: isExtend ? 'auto' : 0,
            transition: {
              height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
            },
          }}
          className={`child ${isCurrentChild ? 'current' : ''}`}
        >
          <AnimatePresence initial={false} mode="sync">
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: isExtend ? 1 : 0,
                scale: isExtend ? 1 : 0,
                x: '-50%',
                transition: {
                  opacity: { duration: 0.3, delay: isExtend ? 0.3 : 0 },
                  scale: { duration: 0.3, delay: isExtend ? 0.15 : 0 },
                },
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: { opacity: { duration: 0.3 } },
              }}
              className={`child-progress-dot current ${
                layDataStatus.id > 2 ? 'success' : ''
              }`}
            />
            <div
              className={`child-line ${isDisabled ? 'disabled' : ''}${
                layDataStatus.id === data.id ? 'current' : ''
              } ${layDataStatus.id > 2 ? 'success' : ''}`}
            />
            {isExtend && (
              <motion.div
                {...motionFadeDelayConfig}
                className="text-no-data spec"
                key="artwork-detail"
              >
                <span className="topic">ข้อมูลออกแบบอาร์ตเวิร์ก</span>
                <SpecDetailStyled
                  as={motion.div}
                  {...motionFadeDelayConfig}
                  style={{
                    marginTop: '28px',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    style={{
                      width: '100%',
                      height: '118px',
                    }}
                  >
                    <ArtworkZone
                      orderById={orderById}
                      reloadOrder={reloadOrder}
                      ldData={ldData}
                    />
                  </div>
                </SpecDetailStyled>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}

      {![
        'สเปคสินค้า',
        'เลย์เอาท์',
        'เสนอราคา',
        'ยืนยันคำสั่งซื้อ',
        'การชำระเงิน',
        'อาร์ตเวิร์ก',
      ].includes(data.name) &&
        (data.name === 'การผลิตสินค้า' ? (
          <motion.div
            key={`child-${data.name}`}
            initial={{ height: 0 }}
            animate={{
              height: isExtend ? 'auto' : 0,
              transition: {
                height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
              },
            }}
            className={`child !pr-[16px] ${isCurrentChild ? 'current' : ''}`}
          >
            <AnimatePresence initial={false} mode="sync">
              <div
                className={`child-line disabled  ${
                  layDataStatus.name === 'จัดส่ง' ? 'success' : ''
                }`}
              />
            </AnimatePresence>
          </motion.div>
        ) : (
          <motion.div
            key={`child-${data.name}`}
            initial={{ height: 0 }}
            animate={{
              height: isExtend ? 'auto' : 0,
              transition: {
                height: { duration: 0.6, delay: isExtend ? 0 : 0.3 },
              },
            }}
            className={`child !pr-[16px] ${isCurrentChild ? 'current' : ''}`}
          >
            <AnimatePresence initial={false} mode="sync">
              <div className="child-line disabled" />
            </AnimatePresence>
          </motion.div>
        ))}
    </LdAccordionStyled>
  );
};

// @ts-ignore
export default LdAccordion;
