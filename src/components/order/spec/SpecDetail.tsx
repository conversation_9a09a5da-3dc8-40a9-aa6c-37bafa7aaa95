import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiProduct from '@/services/stock/product';
import apiSubMaterialDetail from '@/services/stock/subMaterialDetail';
import { isEmpty } from 'lodash';
import apiProductColor from '@/services/stock/productColor';
import apiProductExample from '@/services/stock/productExample';
import apiProductCoating from '@/services/stock/productCoating';
import apiProductExtra from '@/services/stock/productExtra';
import apiProductDesign from '@/services/stock/product-design';
import { LoadingFadein } from '@/styles/share.styled';

const SpecDetailStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: -24px;
  .list {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    padding: 8px 0;
    column-gap: 40px;
    min-height: 40px;
    animation: ${LoadingFadein} 0.3s ease-in;
    .key {
      font-size: 12px;
      white-space: nowrap;
    }
    .value {
      font-size: 12px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .tag-wrap {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
        .tag {
          height: 24px;
          padding: 0 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 20px;
          border: 1px solid #263238;
        }
      }

      &.link {
        color: #90a4ae;
      }
    }
  }
`;
type SpecDetailProps = {
  data: any;
};
const SpecDetail = ({ data }: SpecDetailProps) => {
  const [specForRender, setSpecForRender] = useState<any>({});
  const [foundDesign, setFoundDesign] = useState<any>({});
  const getProductInfo = async () => {
    const res = await apiProduct.getProductById(data.product.id);
    if (res && !res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        productInfo: res.data,
      }));
    }
  };
  const getSubMaterialDetailById = async () => {
    const res = await apiSubMaterialDetail.getSubMaterialDetailById(
      data.product.subMaterialDetailId
    );
    if (!res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        subMaterialDetail: res.data,
      }));
    }
  };
  const getProductColor = async () => {
    const res = await apiProductColor.getList(data.product.id);
    if (!res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        productColor: res.data,
      }));
    }
  };
  const getProductExample = async () => {
    const res = await apiProductExample.getList();
    if (res && !res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        productExample: res.data,
      }));
    }
  };
  const getProductCoating = async () => {
    const res = await apiProductCoating.getList(`${data.product.id}`);
    if (res && !res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        productCoating: res.data,
      }));
    }
  };
  const getExtraList = async () => {
    const res = await apiProductExtra.getList(`${data.product.id}`, '');
    if (res && !res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        extraList: res.data,
      }));
    }
  };
  const getDesign = async () => {
    const res = await apiProductDesign.getList();
    if (res && !res.isError) {
      setSpecForRender((prevSpec: any) => ({
        ...prevSpec,
        designList: res.data,
      }));
    }
  };
  useEffect(() => {
    if (!isEmpty(specForRender.designList)) {
      const design = specForRender.designList.find(
        (item: any) => item.id === data.optional.productDesignId
      );
      setFoundDesign(design);
    }
  }, [specForRender.designList]);
  useEffect(() => {
    getProductInfo();
    getSubMaterialDetailById();
    getProductColor();
    getProductExample();
    getProductCoating();
    getExtraList();
    getDesign();
  }, []);
  // console.log('data', data);
  // console.log('specForRender', specForRender);
  const getCoatingAndFinishForRender = () => {
    const find = data.optional.productFinish.map((item: any) => {
      const coatingForRender = specForRender.productCoating.find(
        (coatingForRender: any) => coatingForRender.coatingId === item.coatingId
      );
      const coatingForRenderExtended = {
        ...coatingForRender,
        ...item,
      };
      const finish = coatingForRenderExtended
        ? coatingForRenderExtended.finishes.find(
            (finish: any) => finish.id === item.finishId
          )
        : null;
      return finish
        ? {
            ...coatingForRenderExtended,
            finishes: finish,
          }
        : null;
    });
    return find.map((item: any, index: number) => (
      <div className="list" key={index}>
        <div className="key">
          เคลือบผิว{item.isSpecial ? 'หลังพิมพ์ ' : 'ก่อนพิมพ์ '}
          {item.typeLayDataFinishId === 1 ? 'ด้านหน้า' : 'ด้านหลัง'}
        </div>
        <div className="value">
          <div className="tag-wrap">
            <div className="tag">
              {item.coating.name} • {item.finishes.name}
            </div>
          </div>
        </div>
      </div>
    ));
  };

  return (
    <SpecDetailStyled>
      {!isEmpty(specForRender.productInfo) && (
        <div className="list">
          <div className="key">ขนาด</div>
          <div className="value">
            {`${data.product.length}*${data.product.width}*${data.product.height}`}{' '}
            {specForRender.productInfo.unitSizeName}
          </div>
        </div>
      )}
      {!isEmpty(specForRender.subMaterialDetail) && (
        <div className="list">
          <div className="key">วัสดุ</div>
          <div className="value">{specForRender.subMaterialDetail.name}</div>
        </div>
      )}
      {typeof data.product.printId === 'number' &&
        !isEmpty(specForRender.productColor) && (
          <div className="list">
            <div className="key">
              การพิมพ์{' '}
              {specForRender.productColor.map((item: any) => {
                if (item.print.id === data.product.printId) {
                  return item.print.name;
                }
              })}
            </div>
            <div className="value">
              <div className="tag-wrap">
                {data.product.colorFront &&
                  data.product.colorFront.length > 0 && (
                    <div className="tag">
                      ด้านหน้า{' '}
                      {data.product.colorFront
                        .map(
                          (id: number) =>
                            specForRender.productColor[0].color.find(
                              (color: any) => color.id === id
                            )?.name
                        )
                        .join(' • ')}
                    </div>
                  )}
                {data.product.colorBack &&
                  data.product.colorBack.length > 0 && (
                    <div className="tag">
                      ด้านหลัง{' '}
                      {data.product.colorBack
                        .map(
                          (id: number) =>
                            specForRender.productColor[0].color.find(
                              (color: any) => color.id === id
                            )?.name
                        )
                        .join(' • ')}
                    </div>
                  )}
              </div>
            </div>
          </div>
        )}
      {!isEmpty(data.example) && !isEmpty(specForRender.productExample) && (
        <div className="list">
          <div className="key">ตัวอย่างสินค้า</div>
          <div className="value">
            {data.example.map((exampleId: number, index: number) => {
              const example = specForRender.productExample.find(
                (ex: any) => ex.id === exampleId
              );
              return (
                <span key={index}>
                  {example.name}
                  {index < data.example.length - 1 ? ' • ' : ''}
                </span>
              );
            })}
          </div>
        </div>
      )}
      {!isEmpty(specForRender.productCoating) && getCoatingAndFinishForRender()}
      {!isEmpty(data.optional.productExtra) &&
        !isEmpty(specForRender.extraList) &&
        data.optional.productExtra.map((item: any, index: number) => {
          const extra = specForRender.extraList.find(
            (extra: any) => extra.id === item.id
          );
          return (
            <div className="list" key={index}>
              <div className="key">เทคนิคพิเศษ {`(${item.printSide})`}</div>
              <div className="value">{`${extra.name} ขนาด ${item.length}x${
                item.height
              } ${item.masterUnitSizeName} ${item.amount} จุด ${
                item.isBlock ? `• ${item.rawMaterialName}` : ''
              }`}</div>
            </div>
          );
        })}
      {!isEmpty(foundDesign) && (
        <div className="list">
          <div className="key">ออกแบบกราฟิก</div>
          <div className="value">
            {foundDesign.name} • {foundDesign.description}
          </div>
        </div>
      )}
      {!isEmpty(data.optional.artworkUrl) && (
        <div className="list">
          <div className="key">ลิงก์อาร์ตเวิร์ก/ตัวอย่างสินค้า</div>
          <div className="value link">{data.optional.artworkUrl}</div>
        </div>
      )}
      {(data.note !== '' || data.extraNote !== '') && (
        <div className="remark-wrap">
          {data.extraNote !== '' && (
            <div className="remark">
              <span>Note:</span> {data.extraNote}
            </div>
          )}
          {data.note !== '' && (
            <div className="remark">
              <span>หมายเหตุ:</span> {data.note}
            </div>
          )}
        </div>
      )}
    </SpecDetailStyled>
  );
};

export default SpecDetail;
