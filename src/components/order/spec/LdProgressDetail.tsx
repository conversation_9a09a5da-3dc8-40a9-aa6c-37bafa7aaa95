import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

const LdProgressDetailStyle = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  column-gap: 40px;
  //row-gap: 16px;
  position: relative;
  align-items: center;
  animation: ${LoadingFadein} 0.3s ease-in;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  //padding: 16px;
  border-radius: 16px;

  .remove-btn {
    height: 40px;
    width: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.3s ease-out;
    background: #d32f2f;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      filter: brightness(0.9);
    }
  }
`;

type LdSpecDetailProps = {
  children: ReactNode;
};
const LdProgressDetail = ({ children }: LdSpecDetailProps) => {
  return (
    <>
      <LdProgressDetailStyle>{children}</LdProgressDetailStyle>
    </>
  );
};

export default LdProgressDetail;
