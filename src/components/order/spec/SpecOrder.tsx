import React, { useEffect, useState } from 'react';
import ProductOrderStep from '@/components/corporate/product-order/ProductOrderStep';
import styled from 'styled-components';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import LdProgressDetail from '@/components/order/spec/LdProgressDetail';
import { useRouter } from 'next/router';
import OrderDetailHeader from '@/components/order/spec/OrderDetailHeader';
import BadgeOutlinedIcon from '@mui/icons-material/BadgeOutlined';
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import { useAppSelector } from '@/store';
import apiLayData from '@/services/order/layData';
import { Tooltip } from '@mui/material';
import ModalAddLayData from '@/components/layout-data/ModalAddLayData';
import dayjs from 'dayjs';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import CreditCardOutlinedIcon from '@mui/icons-material/CreditCardOutlined';
import LdProgressHeader from '@/components/order/spec/LdProgressHeader';
import LdAccordionWrapper from '@/components/order/spec/LdAccordionWrapper';
import LdAccordion from '@/components/order/spec/LdAccordion';
import { orderSelector } from '@/store/features/order';
import { numberWithCommas } from '@/utils/number';
import DateRangeRoundedIcon from '@mui/icons-material/DateRangeRounded';
import LocalAtmRoundedIcon from '@mui/icons-material/LocalAtmRounded';

const SpecOrderStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  //max-height: calc(100vh - 64px);
  //overflow: auto;
  @media screen and (max-width: 820px) {
    max-height: calc(100vh - 64px + 72px);
    margin-top: 72px;
  }
  .content {
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    padding: 24px;
    max-height: calc(100vh - 189px);
    overflow: auto;
    z-index: 1;
    @media screen and (max-width: 820px) {
      padding: 16px;
      max-height: calc(100vh - 256px);
    }
    @media screen and (max-width: 650px) {
      max-height: calc(100vh - 234px);
    }
    .card-wrap {
      display: flex;
      gap: 24px;
      width: 100%;
      flex-wrap: wrap;
      max-width: 100%;
      @media screen and (max-width: 820px) {
        gap: 16px;
      }
      .card {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        border-radius: 16px;
        border: 1px solid #dbe2e5;
        max-width: 100%;
        .card-header {
          padding: 24px;
          border-bottom: 1px solid #dbe2e5;
          height: 100%;
          display: flex;
          align-items: center;
          .profile {
            display: flex;
            align-items: center;
            column-gap: 12px;
            .image {
              border-radius: 50%;
              overflow: hidden;
              height: 40px;
              width: 40px;
              min-width: 40px;
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            .text-group {
              display: flex;
              flex-direction: column;
              row-gap: 8px;
              overflow: hidden;
              .name {
                font-weight: 600;
                font-size: 16px;
                line-height: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 100%;
                overflow: hidden;
              }
              .role {
                font-size: 10px;
                color: #90a4ae;
                line-height: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 100%;
                overflow: hidden;
              }
            }
          }
          .payment {
            display: flex;
            align-items: center;
            column-gap: 24px;
            .amount {
              display: flex;
              flex-direction: column;
              .label {
                font-size: 12px;
              }
              .value {
                font-size: 20px;
                font-weight: 600;
              }
            }
          }
        }
        .detail-wrap {
          display: flex;
          width: 100%;
          .detail {
            flex: 1 1 0%;
            display: flex;
            flex-direction: column;
            padding: 18px 24px;
            row-gap: 2px;
            overflow: hidden;
            justify-content: center;
            @media screen and (max-width: 450px) {
              * {
                text-align: center;
              }
            }
            .title {
              display: flex;
              align-items: center;
              column-gap: 8px;
              font-size: 12px;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              @media screen and (max-width: 450px) {
                flex-direction: column;
                row-gap: 4px;
              }
              span {
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .value {
              font-size: 14px;
              font-weight: 600;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &:nth-child(2) {
              border-left: 1px solid #dbe2e5;
              border-right: 1px solid #dbe2e5;
            }
            .user {
              display: flex;
              column-gap: 12px;
              align-items: center;
              max-width: 100%;
              overflow: hidden;
              .user-image {
                width: 32px;
                min-width: 32px;
                height: 32px;
                border-radius: 50%;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .text-group {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                .name {
                  font-weight: 600;
                  font-size: 14px;
                  white-space: nowrap;
                  max-width: 100%;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
                .role {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }
    }
    .ld-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: 8px;
      row-gap: 24px;
    }
    .remark-wrap {
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      margin-top: 24px;
      .remark {
        font-size: 12px;
        span {
          font-weight: 600;
          text-decoration: underline;
        }
      }
    }
  }
`;

type SpecOrderProps = {
  reloadOrder: () => Promise<void>;
};

const SpecOrder = ({ reloadOrder }: SpecOrderProps) => {
  const router = useRouter();
  const { orderId, step } = router.query;
  // const dispatch = useAppDispatch();
  // const { order } = useAppSelector(orderSelector);
  const [openAddLd, setOpenAddLd] = useState<boolean>(false);
  const [forceOpenSettingModal, setForceOpenSettingModal] =
    useState<boolean>(false);
  const { orderById } = useAppSelector(orderSelector);
  const [layDataStatus, setLayDataStatus] = useState<any>([]);
  const [dataPayment, setDataPayment] = useState<any>({});
  const getDataPayment = async () => {
    const res = await apiLayData.getLayDataPayment(Number(orderId));
    if (!res.isError) {
      setDataPayment(res.data);
    }
  };

  const getLayDataStatus = async () => {
    const res = await apiLayData.getLayDataStatus();
    if (!res.isError) {
      const removedStatus = res.data.filter(
        (item: any) => item.name !== 'ยกเลิก' && item.name !== 'สำเร็จ'
      );
      setLayDataStatus(removedStatus);
    }
  };

  useEffect(() => {
    getLayDataStatus().then();
  }, []);

  useEffect(() => {
    if (step === 'การชำระเงิน') {
      getDataPayment().then();
    }
  }, [step]);

  const calculateDaysUntilExpiration = (
    createdDate: number,
    expireDay: number
  ) => {
    const createdDateObj = dayjs(createdDate);
    const expirationDate = createdDateObj.add(expireDay, 'day');
    const today = dayjs();
    const remainingDays = expirationDate.diff(today, 'day');
    return remainingDays;
  };

  console.log('orderById', orderById);
  // console.log('layDataStatus', layDataStatus);
  return (
    <>
      <SpecOrderStyled id="side-detail-zoom-container">
        <OrderDetailHeader
          reloadOrder={async () => {
            await reloadOrder();
          }}
          forceOpenSettingModal={forceOpenSettingModal}
          closeSettingModal={() => {
            setForceOpenSettingModal(false);
          }}
        />
        <div className="content">
          <ProductOrderStep />
          <div className="card-wrap">
            <div className="card">
              <div className="card-header">
                <div className="profile">
                  <div className="image">
                    <Image
                      src={
                        orderById.contact?.imageUrl
                          ? orderById.contact?.imageUrl
                          : '/images/company/company-info-empty-profile.svg'
                      }
                      width={80}
                      height={80}
                      alt=""
                    />
                  </div>
                  <div className="text-group">
                    <div className="name">{orderById.contact?.name}</div>
                    <div className="role">
                      ลูกค้า • {orderById.contact?.contactType?.name}
                    </div>
                  </div>
                </div>
              </div>
              <div className="detail-wrap">
                <div className="detail">
                  <div className="title">
                    <BadgeOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>เลขผู้เสียภาษี</span>
                  </div>
                  <div className="value">{orderById.contact?.taxNumber}</div>
                </div>
                <div className="detail">
                  <div className="title">
                    <LocalPhoneOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>โทรศัพท์</span>
                  </div>
                  <div className="value">{orderById.contact?.phoneNumber}</div>
                </div>
                <div className="detail">
                  <div className="title">
                    <EmailOutlinedIcon
                      sx={{
                        fontSize: '16px',
                      }}
                    />
                    <span>อีเมล์</span>
                  </div>
                  <div className="value">{orderById.contact?.email}</div>
                </div>
              </div>
            </div>
            {!isEmpty(dataPayment) && dataPayment.totalPriceOrder !== 0 ? (
              <div className="card">
                <div className="card-header">
                  <div className="payment">
                    <div className="amount">
                      <div className="label">มูลค่าสินค้ารวม</div>
                      <div className="value">
                        {numberWithCommas(dataPayment.totalPriceOrder, 2)}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="detail-wrap">
                  <div className="detail">
                    <div className="title">
                      <LocalAtmRoundedIcon
                        sx={{
                          fontSize: '14px',
                        }}
                      />
                      <span>ยอดชำระเงินรวม </span>
                    </div>
                    <div className="value">
                      {numberWithCommas(dataPayment.totalPriceOrder, 2)}
                    </div>
                  </div>
                  <div className="detail">
                    <div className="title">
                      <DateRangeRoundedIcon
                        sx={{
                          fontSize: '14px',
                        }}
                      />
                      <span>วันที่สร้าง </span>
                    </div>
                    <div className="value">
                      {dayjs(dataPayment.createdDate).format('D/M/YYYY HH:mm')}
                    </div>
                  </div>
                  <div className="detail">
                    <div className="user">
                      <div className="user-image">
                        <Image
                          src={
                            dataPayment.createUser?.imageUrl ||
                            '/images/product/empty-product.svg'
                          }
                          alt=""
                          width={64}
                          height={64}
                        />
                      </div>
                      <div className="text-group">
                        <div className="name">
                          {dataPayment.createUser?.name}
                        </div>
                        <div className="role">ผู้ดูแล</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="card">
                <div className="card-header">
                  <div className="profile">
                    <div className="image">
                      <Image
                        src={
                          orderById.createUser?.imageUrl
                            ? orderById.createUser?.imageUrl
                            : '/images/company/company-info-empty-profile.svg'
                        }
                        width={80}
                        height={80}
                        alt=""
                      />
                    </div>
                    <div className="text-group">
                      <div className="name">{orderById.createUser?.name}</div>
                      <div className="role">ผู้ดูแลออร์เดอร์</div>
                    </div>
                  </div>
                </div>
                <div className="detail-wrap">
                  <div className="detail">
                    <div className="title">
                      <span>สร้างเมื่อ </span>
                      <Tooltip
                        title={`อีก ${calculateDaysUntilExpiration(
                          orderById.createdDate,
                          orderById.expireDay
                        )} วันหมดอายุ`}
                        placement="top"
                        arrow
                      >
                        <InfoOutlinedIcon
                          sx={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            color: '#cfd8dc',
                          }}
                        />
                      </Tooltip>
                    </div>
                    <div className="value">
                      {dayjs(orderById.createdDate).format('D/M/YYYY HH:mm')}
                    </div>
                  </div>
                  <div className="detail">
                    <div className="title">
                      <LocalPhoneOutlinedIcon
                        sx={{
                          fontSize: '16px',
                        }}
                      />
                      <span>วันที่ส่งมอบ</span>
                    </div>
                    <div className="value">
                      {orderById.deliveryDate
                        ? dayjs(orderById.deliveryDate).format('D/M/YYYY')
                        : '-'}
                    </div>
                  </div>
                  <div className="detail">
                    <div className="title">
                      <CreditCardOutlinedIcon
                        sx={{
                          fontSize: '16px',
                        }}
                      />
                      <span>เครดิตการชำระเงิน</span>
                    </div>
                    <div className="value">
                      {orderById.credit ? `${orderById.credit.day} วัน` : '-'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <>
            {!isEmpty(orderById) && (
              <>
                <ModalAddLayData
                  open={openAddLd}
                  makeCloseModal={() => {
                    setOpenAddLd(false);
                  }}
                  handleReloadOrder={async () => {
                    await reloadOrder();
                  }}
                />
                <div className="ld-list">
                  {orderById.layData.map((item: any, index: number) => (
                    <LdProgressDetail key={index}>
                      <LdProgressHeader
                        data={item}
                        handleOpenLayDataSideDetail={() => {
                          //
                        }}
                      />
                      {!isEmpty(layDataStatus) && (
                        <LdAccordionWrapper>
                          {layDataStatus.map((itemStatus: any, idx: number) => {
                            return (
                              <LdAccordion
                                key={idx}
                                data={itemStatus}
                                ldData={item}
                                makeConfirmSpec={() => {
                                  //
                                }}
                                makeConfirmLayout={() => {
                                  //
                                }}
                                reloadOrder={reloadOrder}
                                confirmSalePriceSelected={() => {
                                  //
                                }}
                                handleSelectQuantity={() => {
                                  //
                                }}
                                isSaleOrder={false}
                                makePrintPlateData={() => {
                                  //
                                }}
                              />
                            );
                          })}
                        </LdAccordionWrapper>
                      )}
                    </LdProgressDetail>
                  ))}
                </div>
                {/* {step === 'การชำระเงิน' && ( */}
                {/*  <> */}
                {/*    <HrSpaceStyle /> */}
                {/*    <PaymentStatus dataPayment={dataPayment} /> */}
                {/*  </> */}
                {/* )} */}
                {/* {step === 'อาร์ตเวิร์ก' && !isEmpty(orderById.layData) && ( */}
                {/*  <> */}
                {/*    <ArtworkZone orderById={orderById} /> */}
                {/*  </> */}
                {/* )} */}
              </>
            )}
          </>
        </div>
      </SpecOrderStyled>
    </>
  );
};

export default SpecOrder;
