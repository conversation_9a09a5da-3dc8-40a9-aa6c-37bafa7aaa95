import React from 'react';
import Image from 'next/image';
import { isEmpty, isNull } from 'lodash';
import styled from 'styled-components';
import useMediumZoom from '@/hooks/useMediumZoom';
import dayjs from 'dayjs';

type LayDataSideDetailContentProps = {
  layoutDataDetail: any;
};
const LayDataSideDetailContent = ({
  layoutDataDetail,
}: LayDataSideDetailContentProps) => {
  const currentData = layoutDataDetail;
  useMediumZoom('.zoom-image-side-detail, [class^="zoom-image-example-"]', {
    margin: 40,
    container: '#side-detail-zoom-container',
  });
  const handlePreviewClick = (event: any, imageSelector: string) => {
    event.preventDefault();
    const zoomElement = document.querySelector(imageSelector) as HTMLElement;
    if (zoomElement) {
      zoomElement.click();
    }
  };
  console.log('currentData', currentData);

  return (
    <LayDataSideDetailStyle>
      <div className="side-detail-header">
        <div className="image cursor-pointer">
          <Image
            src={
              currentData.productModel.imageUrl ||
              '/images/product/empty-product.svg'
            }
            fill
            alt={'img'}
          />
        </div>
        <div className="text-zone">
          <div className="ld-code">{currentData.ldCode}</div>
          <div className="product-name">
            {currentData.productModel.productModelName} {currentData.length} x{' '}
            {currentData.width} x {currentData.height} mm
          </div>
          <div className="product-name">
            ระดับความละเอียด:{' '}
            {currentData.detailLevelEnum === 'ADVANCED'
              ? 'งานละเอียด'
              : 'งานทั่วไป'}
          </div>
          <div className="product-name">
            กำหนดส่งสินค้า:{' '}
            {dayjs(currentData.scheduleDate).format('DD/MM/YYYY')}
          </div>
        </div>
      </div>
      <div className="spec-list">
        <div className="title !m-0">วัสดุ</div>
        <div className="list">
          <div className="key">กระดาษ</div>
          <div className="value">
            {currentData.subMaterialDetail?.name || '-'}
          </div>
        </div>
        <div className="list">
          <div className="key">ใบเต็ม</div>
          <div className="value">
            {currentData.printPlate?.itemSize?.itemSizeName || '-'}
          </div>
        </div>
        <div className="list">
          <div className="key">ใบพิมพ์</div>
          <div className="value">
            {currentData.printPlate?.subItemSize?.subItemName || '-'}
          </div>
        </div>
        <div className="title">แม่พิมพ์</div>
        <div className="list">
          <div className="key">เพลต</div>
          <div className="value">
            {currentData.printPlate?.plateRawMaterial?.subMaterialDetail?.name
              ? currentData.printPlate.plateRawMaterial.subMaterialDetail.name
              : '-'}
          </div>
        </div>
        <div className="list">
          <div className="key">ไดคัท</div>
          <div className="value">
            {currentData.printPlate?.dieCutRawMaterial?.subMaterialDetail?.name
              ? currentData.printPlate.dieCutRawMaterial.subMaterialDetail.name
              : '-'}
          </div>
        </div>
        <div className="title">Model Dieline</div>
        <div className="list">
          <div className="key">Model</div>
          <div className="value">{'-'}</div>
        </div>
        <div className="list">
          <div className="key">ขนาดกางออก</div>
          <div className="value">{'-'}</div>
        </div>
        <div className="list">
          <div className="key">Dieline Detail</div>
          <div className="value">{'-'}</div>
        </div>
        <>
          <div className="title">สเปคด้านหน้า</div>
          <div className="list">
            <div className="key uppercase">การพิมพ์</div>
            <div className="value">
              {currentData.printingRequest?.printSystem?.name || '-'}
            </div>
          </div>
          {currentData.printingRequest?.colorFront &&
            currentData.printingRequest.colorFront.map(
              (item: any, index: number) => {
                return (
                  <div className="list" key={index}>
                    <div className="key">สีพิมพ์</div>
                    <div className="value">
                      <div className="tag-wrap">
                        <div className="tag">
                          <Image
                            src={
                              item.imageUrl ||
                              '/images/product/empty-product.svg'
                            }
                            width={16}
                            height={16}
                            alt=""
                            style={{ borderRadius: '50%' }}
                          />
                          {/* ชื่อสี เช่น CMYK, PANTONE */}
                          {item.printColor?.name || '-'}
                          {/* แสดง code Pantone ถ้ามี */}
                          {item.colorCode ? ` (${item.colorCode})` : ''}
                          {/* พื้นที่พิมพ์ เช่น พื้นที่พิมพ์ 30% */}
                          {item.printAreaDimension?.name
                            ? ` • ${item.printAreaDimension.name}`
                            : ''}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
            )}

          {/* เคลือบก่อน/หลังพิมพ์ */}
          {currentData.coatingRequest?.finishFront?.map(
            (item: any, index: number) => (
              <div key={index} className="list">
                <div className="key">
                  {item.coatingPositionEnum === 'AFTER'
                    ? 'เคลือบ หลังพิมพ์'
                    : 'เคลือบ ก่อนพิมพ์'}
                </div>
                <div className="value">
                  <div className="tag-wrap">
                    <div className="tag">
                      {item.finishSubMaterialDetail?.name || '-'}
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {/* เทคนิคพิเศษ */}
          {!isEmpty(currentData.extra) &&
            currentData.extra
              .filter(
                (item: any) => item.extraSideDimension?.name === 'ด้านหน้า'
              )
              .map((frontExtra: any, index: number) => (
                <div key={index} className="list">
                  <div className="key">เทคนิคพิเศษ</div>
                  <div className="value">
                    <div className="tag-wrap">
                      <div className="tag">
                        {frontExtra.masterName} •{' '}
                        {frontExtra.blogSubMaterialDetail?.name || '-'} •{' '}
                        {frontExtra.quantity} จุด •{' '}
                        {frontExtra.extraAreaDimension?.name || '-'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
        </>
        <>
          <div className="title">สเปคด้านหลัง</div>

          {/* ระบบพิมพ์ */}
          <div className="list">
            <div className="key uppercase">การพิมพ์</div>
            <div className="value">
              {currentData.printingRequest?.printSystem?.name || '-'}
            </div>
          </div>

          {/* สีพิมพ์ */}
          {currentData.printingRequest?.colorBack?.map(
            (item: any, index: number) => (
              <div className="list" key={index}>
                <div className="key">สีพิมพ์</div>
                <div className="value">
                  <div className="tag-wrap">
                    <div className="tag">
                      <Image
                        src={
                          item.imageUrl || '/images/product/empty-product.svg'
                        }
                        width={16}
                        height={16}
                        alt=""
                        style={{ borderRadius: '50%' }}
                      />
                      {item.printColor?.name || '-'}
                      {item.colorCode ? ` (${item.colorCode})` : ''}
                      {item.printAreaDimension?.name
                        ? ` • ${item.printAreaDimension.name}`
                        : ''}
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {/* เคลือบก่อน/หลังพิมพ์ */}
          {currentData.coatingRequest?.finishBack?.map(
            (item: any, index: number) => (
              <div key={index} className="list">
                <div className="key">
                  {item.coatingPositionEnum === 'AFTER'
                    ? 'เคลือบ หลังพิมพ์'
                    : 'เคลือบ ก่อนพิมพ์'}
                </div>
                <div className="value">
                  <div className="tag-wrap">
                    <div className="tag">
                      {item.finishSubMaterialDetail?.name || '-'}
                    </div>
                  </div>
                </div>
              </div>
            )
          )}

          {/* เทคนิคพิเศษ */}
          {!isEmpty(currentData.extra) &&
            currentData.extra
              .filter(
                (item: any) => item.extraSideDimension?.name === 'ด้านหลัง'
              )
              .map((backExtra: any, index: number) => (
                <div key={index} className="list">
                  <div className="key">เทคนิคพิเศษ</div>
                  <div className="value">
                    <div className="tag-wrap">
                      <div className="tag">
                        {backExtra.masterName} •{' '}
                        {backExtra.blogSubMaterialDetail?.name || '-'} •{' '}
                        {backExtra.quantity} จุด •{' '}
                        {backExtra.extraAreaDimension?.name || '-'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
        </>
        {!isEmpty(currentData.masterExample) && (
          <>
            <div className="title">ตัวอย่างสินค้า</div>
            {currentData.masterExample.map((item: any, index: number) => (
              <div className="list" key={index}>
                <div className="key">ตัวอย่าง</div>
                <div className="value">{item.name}</div>
              </div>
            ))}
          </>
        )}
        {!isEmpty(currentData.serviceLay) && (
          <>
            <div className="title">บริการ</div>
            {currentData.serviceLay.map((item: any, index: number) => (
              <div className="list" key={index}>
                <div className="key">{item.serviceLayType.name}</div>
                <div className="value">{item.serviceLay.name}</div>
              </div>
            ))}
          </>
        )}
      </div>
      <div
        style={{
          fontSize: '12px',
          marginTop: '24px',
        }}
      >
        <span
          style={{
            textDecoration: 'underline',
          }}
        >
          หมายเหตุ
        </span>{' '}
        {currentData.note || '-'}
      </div>
      <>
        <div className="title">รูปขนาดกางออก</div>
        <div className="print-plate-image">
          <div className="group">
            <div className="topic">ด้านหน้า</div>
            <div
              className={`${
                isNull(currentData.printPlate) ||
                isNull(currentData.printPlate?.layoutFrontUrl)
                  ? ''
                  : '!border-0'
              } image`}
            >
              {!isNull(currentData.printPlate) &&
                !isNull(currentData.printPlate?.layoutFrontUrl) && (
                  <img
                    src={currentData.printPlate?.layoutFrontUrl}
                    className="zoom-image-side-detail front"
                    onClick={(event) =>
                      handlePreviewClick(event, '.zoom-image-side-detail.front')
                    }
                  />
                )}
            </div>
          </div>
          <div className="group">
            <div className="topic">ด้านหลัง</div>
            <div
              className={`${
                isNull(currentData.printPlate) ||
                isNull(currentData.printPlate?.layoutBackUrl)
                  ? ''
                  : '!border-0'
              } image`}
            >
              {!isNull(currentData.printPlate) &&
                !isNull(currentData.printPlate?.layoutBackUrl) && (
                  <img
                    src={currentData.printPlate?.layoutBackUrl}
                    className="zoom-image-side-detail back"
                    onClick={(event) =>
                      handlePreviewClick(event, '.zoom-image-side-detail.back')
                    }
                  />
                )}
            </div>
          </div>
        </div>
      </>
      {!isEmpty(currentData.imageResolution) && (
        <>
          <div className="title">รูปสินค้า/ตัวอย่าง</div>
          <ProductExampleImageWrapper>
            {currentData.imageResolution.map((item: any, index: number) => (
              <div className="image" key={index}>
                <img
                  src={item.imageResolutionUrl}
                  className={`zoom-image-side-detail img-${index}`}
                  onClick={(event) =>
                    handlePreviewClick(
                      event,
                      `.zoom-image-side-detail.img-${index}`
                    )
                  }
                />
              </div>
            ))}
          </ProductExampleImageWrapper>
        </>
      )}
    </LayDataSideDetailStyle>
  );
};

const ProductExampleImageWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  .image {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
`;
const LayDataSideDetailStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  column-gap: 24px;
  &:before {
    content: '';
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    position: absolute;
    z-index: 1;
    height: 24px;
    left: 0;
    width: 100%;
    bottom: 0;
  }
  .side-detail-header {
    display: flex;
    width: inherit;
    align-items: center;
    column-gap: 16px;
    position: sticky;
    top: 0px;
    padding: 24px 0;
    background: white;
    z-index: 1;

    &:before {
      content: '';
      background: linear-gradient(
        to top,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 1) 100%
      );
      position: absolute;
      z-index: 1;
      height: 24px;
      left: 0;
      width: 100%;
      bottom: -24px;
    }
    .image {
      width: 100px;
      height: 100px;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
    }
    .text-zone {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .ld-code {
        font-size: 22px;
        font-weight: 600;
      }
      .product-name {
        font-size: 14px;
      }
    }
  }
  .spec-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .title {
      font-weight: 600;
      margin-top: 16px;
      width: inherit;
      padding: 8px 0;
      border-bottom: 1px solid #dbe2e5;
    }
    .list {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dbe2e5;
      padding: 8px 0;
      column-gap: 40px;
      min-height: 40px;
      .key {
        font-size: 12px;
        white-space: nowrap;
      }
      .value {
        font-size: 12px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .tag-wrap {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-wrap: wrap;
          .tag {
            height: 24px;
            padding: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            border: 1px solid #dbe2e5;
            column-gap: 6px;
          }
        }

        &.link {
          color: #90a4ae;
        }
      }
    }
  }
  .title {
    font-weight: 600;
    margin-top: 16px;
    width: inherit;
    padding: 8px 0;
  }
  .print-plate-image {
    display: flex;
    width: 100%;
    gap: 24px;
    height: 214px;
    margin-top: 12px;

    .group {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      .topic {
        font-size: 12px;
      }
      .image {
        flex: 1 1 0%;
        position: relative;
        border-radius: 16px;
        overflow: hidden;
        border: 2px dashed #dbe2e5;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 100%;
          min-height: 184px;
        }
      }
    }
  }
`;

export default LayDataSideDetailContent;
