import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  TextField,
} from '@mui/material';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import Image from 'next/image';
import AppModalConfirm from '@/components/global/AppModalConfirm';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import FileCopyRoundedIcon from '@mui/icons-material/FileCopyRounded';
import PeopleAltRoundedIcon from '@mui/icons-material/PeopleAltRounded';
import AccountCircleRoundedIcon from '@mui/icons-material/AccountCircleRounded';
import RestoreRoundedIcon from '@mui/icons-material/RestoreRounded';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { HrSpaceStyle } from '@/components/purchase-order/PoDetailHeader';
import apiOrder from '@/services/order/order';
import { useRouter } from 'next/router';
import apiContact from '@/services/core/contact';
// import { ModalCreateProductContentStyled } from '@/components/layout-data/ModalCreateOrder';
import { Search } from '@mui/icons-material';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import ModalSetting from '@/components/order/ModalSetting';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { orderSelector } from '@/store/features/order';
import ActionButton from '@/components/ActionButton';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import apiPaymentQuotation from '@/services/order/payment-quotation';
import apiEstimate from '@/services/order/estimate';
import apiLayDataOrder from '@/services/order/layDataOrder';

dayjs.extend(utc);
const OrderDetailHeaderStyled = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28px 24px 28px;
  column-gap: 40px;
  row-gap: 24px;
  flex-wrap: wrap;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 820px) {
    padding: 28px 16px 28px;
  }
  @media screen and (max-width: 650px) {
    row-gap: 16px;
    padding: 24px 16px 24px;
  }
  .od-number {
    font-size: 40px;
    font-weight: 600;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @media screen and (max-width: 650px) {
      font-size: 28px;
    }
    @media screen and (max-width: 350px) {
      font-size: 22px;
    }
  }
`;

type OrderDetailHeaderProps = {
  reloadOrder: (action?: string) => void;
  forceOpenSettingModal: boolean;
  closeSettingModal: () => void;
};
const OrderDetailHeader = ({
  reloadOrder,
  forceOpenSettingModal,
  closeSettingModal,
}: OrderDetailHeaderProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { orderId } = router.query;
  const [anchorEl, setAnchorEl] = useState(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [modalCancel, setModalCancel] = useState<any>({
    open: false,
    title: '',
    description: '',
  });
  const [openChangeCustomer, setOpenChangeCustomer] = useState<boolean>(false);
  const [openModalSetting, setOpenModalSetting] = useState<boolean>(false);
  const [, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  // const [submittingChangeCustomer, setSubmittingChangeCustomer] =
  //   useState<boolean>(false);
  const [paymentType, setPaymentType] = useState<any>([]);
  const [customerFilters, setCustomerFilters] = useState({
    page: 0,
    size: 100,
    search: '',
  });
  const [isSomePriceConfirmed] = useState<boolean>(false);
  const [openCreateSalePrice, setOpenCreateSalePrice] = useState<any>({
    status: false,
  });
  const [loadingConfirm, setLoadingConfirm] = useState<boolean>(false);
  const { orderById } = useAppSelector(orderSelector);
  const [, setSelectedCustomerId] = useState<number>(0);
  const [openSendToJob, setOpenSendToJob] = useState<any>({
    isPayment: false,
    status: false,
  });
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCancelOrder = async (value: any) => {
    setSubmitting(true);
    const sendValue = { reason: value, orderId: orderById.id };
    const res = await apiOrder.cancelOrder(sendValue);
    if (!res.isError) {
      handleCloseModalCancel();
      await router.push('/orders');
    }
    setSubmitting(false);
  };
  const handleChangeCustomer = () => {
    handleOpen().then();
  };
  const handleOpen = async () => {
    setContactList([]);
    setCustomerFilters({
      page: 0,
      size: 100,
      search: '',
    });
    setSearchInput('');
    setSelectedCustomerId(0);
    await getContactList();
    setOpenChangeCustomer(true);
  };

  useEffect(() => {
    getContactList().then();
  }, [customerFilters.search]);

  const handleSearch = (event: any) => {
    setLoadingSearch(true);
    setSearchInput(event.target.value);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setCustomerFilters({
        ...customerFilters,
        search: event.target.value,
      });
    }, 1000);

    setTimer(newTimer);
  };
  const handleClose = () => {
    setAnchorEl(null);
    setOpenChangeCustomer(false);
  };
  const getContactList = async () => {
    const res = await apiContact.getContactOptions(customerFilters);
    if (res && !res.isError) {
      setContactList(res.data);
    }
  };

  const handleCloseModalCancel = () => {
    setModalCancel({
      ...modalCancel,
      open: false,
    });
  };
  const handleOpenModalCancel = () => {
    setModalCancel({
      open: true,
      title: 'AA',
      description: 'BB',
    });
  };
  // const handleSelectCustomer = (id: number) => {
  //   if (selectedCustomerId === id) {
  //     setSelectedCustomerId(0);
  //   } else {
  //     setSelectedCustomerId(id);
  //   }
  // };
  // const handleSubmitChangeCustomer = async () => {
  //   setSubmittingChangeCustomer(true);
  //   const sendData = {
  //     orderId: orderById.id,
  //     contactId: selectedCustomerId,
  //   };
  //   const res = await apiOrder.changeCustomer(sendData);
  //   if (!res.isError) {
  //     handleClose();
  //     reloadOrder();
  //     dispatch(
  //       setSnackBar({
  //         status: true,
  //         text: 'เปลี่ยนลูกค้าสำเร็จ',
  //         severity: 'success',
  //       })
  //     );
  //   } else {
  //     dispatch(
  //       setSnackBar({
  //         status: true,
  //         text: 'เกิดข้อผิดพลาด',
  //         severity: 'error',
  //       })
  //     );
  //   }
  //   setSubmittingChangeCustomer(false);
  // };

  const handleOpenModalSetting = () => {
    setOpenModalSetting(true);
  };

  const getPaymentType = async () => {
    const res = await apiEstimate.getListPaymentTypeEnum();
    if (!res.isError) {
      setPaymentType(res.data);
    }
  };
  console.log('orderById', orderById);

  useEffect(() => {
    getPaymentType().then();
  }, [orderById]);

  const handleClickCreateSalePrice = () => {
    setOpenCreateSalePrice({
      ...openCreateSalePrice,
      status: true,
    });
  };

  const handleSendCreateSalePrice = async (value: any) => {
    setLoadingConfirm(true);
    const body = {
      layDataOrdersId: orderById.id,
      paymentType: value.paymentType,
    };
    const res = await apiPaymentQuotation.approve(body);
    dispatch(
      setSnackBar({
        status: true,
        text: res.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      reloadOrder();
      setOpenCreateSalePrice({
        ...openCreateSalePrice,
        status: false,
      });
      await router.push(`/orders/${orderId}/spec?step=ชำระเงิน`);
    }
    setLoadingConfirm(false);
  };

  const handleSendToArtwork = async () => {
    setLoadingConfirm(true);
    const res = await apiLayDataOrder.approveLayDataOrder(orderById.id);
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? res.message : res.error.response.data.message,
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      setOpenSendToJob({
        ...openSendToJob,
        status: false,
      });
      await router.push(`/orders/${orderId}/spec?step=อาร์ตเวิร์ก`);
      reloadOrder();
    }
    setLoadingConfirm(false);
  };
  return (
    <>
      {isSomePriceConfirmed && (
        <AppModalConfirm
          open={openCreateSalePrice.status}
          isReason={false}
          onClickClose={() => {
            setOpenCreateSalePrice({
              ...openCreateSalePrice,
              status: false,
            });
          }}
          icon={
            <Image
              src={'/icons/icon-export-notes.svg'}
              alt=""
              width={40}
              height={40}
            />
          }
          confirmTitle={`สร้างใบราคาเสนอขาย`}
          confirmDescription={`สินค้าในรายการสั่งผลิตนี้ได้เสนอราคาขายเรียบร้อยแล้ว การสร้างราคาเสนอขายใหม่ คุณจะต้องทำการยืนยันราคาขายอีกครั้ง`}
          loadingConfirm={loadingConfirm}
          onConfirm={async (value: any) => {
            await handleSendCreateSalePrice(value);
          }}
          maxWidth={'380px'}
          paymentType={paymentType}
        />
      )}
      <AppModalConfirm
        open={openSendToJob.status}
        isReason={false}
        onClickClose={() => {
          setOpenSendToJob({
            ...openSendToJob,
            status: false,
          });
        }}
        icon={
          <Image
            src={'/icons/icon-export-notes.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          openSendToJob.isPayment
            ? `ส่งดำเนินการผลิต`
            : 'ยืนยันที่จะส่งดำเนินการผลิต'
        }
        confirmDescription={
          openSendToJob.isPayment
            ? `คุณต้องการที่จะส่งดำเนินการผลิตใช่หรือไม่`
            : `คุณต้องการที่จะส่งดำเนินการผลิตโดยที่ยังไม่ได้ชำระเงินใช่หรือไม่`
        }
        loadingConfirm={loadingConfirm}
        onConfirm={async () => {
          await handleSendToArtwork();
        }}
        maxWidth={'380px'}
      />
      <ModalSetting
        open={openModalSetting || forceOpenSettingModal}
        handleClose={() => {
          closeSettingModal();
          setOpenModalSetting(false);
        }}
        reloadOrder={() => {
          reloadOrder('confirm');
        }}
      />
      <Dialog
        open={openChangeCustomer}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div
                  className="title"
                  style={{
                    transform: 'translateX(50%)',
                    position: 'absolute',
                    margin: '0',
                    right: '50%',
                  }}
                >
                  เปลี่ยนลูกค้า
                </div>
                <div
                  className="x-close"
                  onClick={() => setOpenChangeCustomer(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหาลูกค้า"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                      '.MuiInputBase-input': {
                        paddingLeft: '0',
                      },
                    }}
                  />
                  {/* <ModalCreateProductContentStyled> */}
                  {/*  {!isEmpty(contactList) && ( */}
                  {/*    <> */}
                  {/*      <div className="contact-item-wrap"> */}
                  {/*        {contactList.map((item: any, index: number) => ( */}
                  {/*          <div */}
                  {/*            key={index} */}
                  {/*            className={`contact-item ${ */}
                  {/*              selectedCustomerId === item.id ? 'active' : '' */}
                  {/*            }`} */}
                  {/*            onClick={() => { */}
                  {/*              handleSelectCustomer(item.id); */}
                  {/*            }} */}
                  {/*          > */}
                  {/*            <Avatar */}
                  {/*              src={item.imageUrl} */}
                  {/*              sx={{ */}
                  {/*                height: '40px', */}
                  {/*                width: '40px', */}
                  {/*              }} */}
                  {/*            /> */}
                  {/*            <div className=""> */}
                  {/*              <h4>{item.name}</h4> */}
                  {/*              <p> */}
                  {/*                <span>บุคคลธรรมดา</span> */}
                  {/*                {!isEmpty(item.phoneNumber) && ( */}
                  {/*                  <> */}
                  {/*                    <span>•</span> */}
                  {/*                    <span>{item.phoneNumber}</span> */}
                  {/*                  </> */}
                  {/*                )} */}
                  {/*                {!isEmpty(item.note) && ( */}
                  {/*                  <> */}
                  {/*                    <span>•</span> */}
                  {/*                    <span>{item.note}</span> */}
                  {/*                  </> */}
                  {/*                )} */}
                  {/*              </p> */}
                  {/*            </div> */}
                  {/*          </div> */}
                  {/*        ))} */}
                  {/*      </div> */}
                  {/*      <Button */}
                  {/*        type="button" */}
                  {/*        variant="contained" */}
                  {/*        color="dark" */}
                  {/*        disabled={selectedCustomerId === 0} */}
                  {/*        sx={{ */}
                  {/*          boxShadow: 'none', */}
                  {/*          fontWeight: '400', */}
                  {/*          margin: '0 0 24px', */}
                  {/*          height: '40px !important', */}
                  {/*        }} */}
                  {/*        fullWidth */}
                  {/*        onClick={async () => { */}
                  {/*          await handleSubmitChangeCustomer(); */}
                  {/*        }} */}
                  {/*      > */}
                  {/*        {submittingChangeCustomer ? ( */}
                  {/*          <CircularProgress */}
                  {/*            size={20} */}
                  {/*            style={{ */}
                  {/*              color: 'white', */}
                  {/*            }} */}
                  {/*          /> */}
                  {/*        ) : ( */}
                  {/*          'ยืนยัน' */}
                  {/*        )} */}
                  {/*      </Button> */}
                  {/*    </> */}
                  {/*  )} */}
                  {/* </ModalCreateProductContentStyled> */}
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
      <AppModalConfirm
        open={modalCancel.open}
        isReason
        onClickClose={() => {
          handleCloseModalCancel();
        }}
        icon={
          <Image
            src={'/icons/icon-scan-delete.svg'}
            alt=""
            width={40}
            height={40}
          />
        }
        confirmTitle={
          <div
            style={{
              color: '#D32F2F',
            }}
          >
            ยกเลิกรายการ
          </div>
        }
        confirmDescription={`คุณต้องการยกเลิกรายการ “${orderById.layDataOrderNo}” รายการนี้จะถูกเปลี่ยนสถานะเป็น “ยกเลิก”`}
        loadingConfirm={submitting}
        onConfirm={async (reason: {
          annotationId: any;
          note: any;
          reason: string;
        }) => {
          await handleCancelOrder(reason);
        }}
        maxWidth={'340px'}
        bgIcon={'#FDE8EF'}
      />
      <OrderDetailHeaderStyled>
        <div className="od-number">{orderById.layDataOrderNo}</div>
        <div className="flex items-center gap-[16px]">
          {orderById.layDataOrderStatus.name === 'การชำระเงิน' && (
            <>
              <Button
                variant="contained"
                color="Hon"
                onClick={() => {
                  setOpenSendToJob({
                    isPayment: orderById.isPayment,
                    status: true,
                  });
                }}
                sx={{
                  lineHeight: 0,
                }}
                disabled={!orderById.isPayment}
              >
                ส่งดำเนินการผลิต
              </Button>
            </>
          )}
          {isSomePriceConfirmed && (
            <ActionButton
              variant="contained"
              color="Hon"
              icon={<AddRoundedIcon />}
              text={`สร้างราคาเสนอขาย`}
              borderRadius={'8px'}
              onClick={handleClickCreateSalePrice}
            />
          )}
          <div>
            <Button
              variant="outlined"
              size="small"
              color="blueGrey"
              sx={{
                minWidth: '40px',
              }}
              onClick={handleClick}
            >
              <div className="action-dot">
                <div className="dot" />
              </div>
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              sx={{
                marginTop: '12px',
                '.MuiList-root': {
                  padding: '8px',
                  width: '192px',
                  display: 'flex',
                  flexDirection: 'column',
                  rowGap: '4px',
                },
                li: {
                  width: 'auto',
                },
              }}
            >
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div
                  className="drop-menu"
                  onClick={() => {
                    handleOpenModalSetting();
                  }}
                >
                  <SettingsOutlinedIcon />
                  ตั้งค่าออเดอร์
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
                disabled
              >
                <div className="drop-menu" onClick={() => {}}>
                  <FileCopyRoundedIcon />
                  ทำสำเนา
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
                disabled
              >
                <div className="drop-menu">
                  <PeopleAltRoundedIcon />
                  ผู้ดูแลออเดอร์
                </div>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  handleClose();
                  handleChangeCustomer();
                }}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
              >
                <div className="drop-menu">
                  <AccountCircleRoundedIcon />
                  เปลี่ยนลูกค้า
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
                disabled
              >
                <div className="drop-menu">
                  <RestoreRoundedIcon />
                  ประวัติรายการ
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                }}
                disabled
              >
                <div className="drop-menu">
                  <InfoOutlinedIcon />
                  วิธีใช้งาน
                </div>
              </MenuItem>
              <MenuItem
                onClick={handleClose}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FDE8EF',
                  },
                }}
              >
                <div
                  className="drop-menu text-[#D32F2F]"
                  onClick={() => {
                    handleOpenModalCancel();
                  }}
                >
                  <Image
                    src="/icons/icon-scan-delete.svg"
                    alt=""
                    width={24}
                    height={24}
                  />
                  ยกเลิกรายการ
                </div>
              </MenuItem>
            </Menu>
          </div>
        </div>
      </OrderDetailHeaderStyled>
      <HrSpaceStyle />
    </>
  );
};

export default OrderDetailHeader;
