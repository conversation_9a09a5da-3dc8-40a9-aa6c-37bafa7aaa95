import React from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import ActionButton from '@/components/ActionButton';
import dayjs from 'dayjs';

const LdProgressHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  overflow: hidden;
  width: 100%;
  padding: 16px 16px 16px 16px;

  .image {
    height: 72px;
    width: 72px;
    min-width: 72px;
    overflow: hidden;
    border-radius: 12px;
    background: #aadaff;
    display: flex;
    align-items: center;
    justify-content: center;
    @media screen and (max-width: 350px) {
      height: 100px;
      width: 100px;
      min-width: 100px;
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .layout-group {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .top-group {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;

      .text-group {
        display: flex;
        align-items: center;
        gap: 6px;

        .ld {
          font-size: 22px;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-top: -4px;
        }

        .name {
          font-size: 14px;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .bottom-group {
      width: 100%;
      display: flex;
      align-items: center;
      column-gap: 8px;

      .detail {
        display: flex;
        align-items: center;
      }
    }
  }
`;
type Props = {
  data: any;
  handleOpenLayDataSideDetail: (layDataId: number) => void;
};
const LdProgressHeader = ({ data, handleOpenLayDataSideDetail }: Props) => {
  return (
    <LdProgressHeaderStyled>
      <div className="image">
        <Image
          src={data.productModel.imageUrl || '/images/Mailer_Box.png'}
          width={200}
          height={200}
          alt=""
        />
      </div>
      <div className="layout-group">
        <div className="top-group">
          <div className="text-group">
            <div className="ld">{data.ldCode}</div>
            <div className="name"></div>
          </div>
          <ActionButton
            variant="outlined"
            color="blueGrey"
            icon={
              <Image
                src="/icons/icon-edit-document.svg"
                alt=""
                width={24}
                height={24}
              />
            }
            text="รายละเอียดสินค้า"
            borderRadius={'8px'}
            onClick={async () => {
              handleOpenLayDataSideDetail(data.id);
            }}
          />
        </div>
        <div className="bottom-group">
          <div className="detail">
            {' '}
            {`${
              data.productModel.productName
                ? `${data.productModel.productName} • `
                : ''
            }`}
            {data.productModel.productModelName} • {data.length} x {data.width}{' '}
            x {data.height} mm •{' '}
            {data.detailLevelEnum === 'BASIC' ? 'งานทั่วไป' : 'งานละเอียด'} •
            กำหนดส่งสินค้า: {dayjs(data.scheduleDate).format('DD/MM/YYYY')}
          </div>
        </div>
      </div>
    </LdProgressHeaderStyled>
  );
};

export default LdProgressHeader;
