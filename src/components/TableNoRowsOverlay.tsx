import React from 'react';
import { GridOverlay } from '@mui/x-data-grid';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

const NoRowsOverlayStyled = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 56px;
  font-size: 14px;
  color: #cfd8dc;
  animation: ${LoadingFadein} 0.3s ease-in;
`;

const TableNoRowsOverlay = () => {
  return (
    <GridOverlay>
      <NoRowsOverlayStyled>ไม่มีรายการ</NoRowsOverlayStyled>
    </GridOverlay>
  );
};

export default TableNoRowsOverlay;
