import { useFormik } from 'formik';
import * as yup from 'yup';
import {
  Button,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import styled from 'styled-components';
import ImageFinder from '@/components/article/ImageFinder';
import React, { useState } from 'react';

// const { publicRuntimeConfig } = getConfig();

type ArticleFormProps = {
  initialValues?: any;
  handleSubmit: (values: any) => void;
};

const validationSchema = yup.object({
  title: yup.string().required('กรุณาใส่ชื่อบทความ'),
});

const EditorFormStyle = styled.div`
  padding: 20px;
  p {
    margin-bottom: 0;
    font-size: 0.9em;
  }
  input,
  textarea {
    font-size: 0.9em;
  }
  .img-finder {
    position: absolute;
    right: 20px;
    top: 0;
    transform: translateY(calc(-100% - 10px));
  }
`;

const ArticleForm = ({ initialValues, handleSubmit }: ArticleFormProps) => {
  const [isOpenGalleryDialog, setOpenGalleryDialog] = useState(false);
  const formik = useFormik({
    initialValues:
      initialValues != null
        ? initialValues
        : {
            title: '',
            description: '',
            keywords: '',
            content: '',
            urlSlug: '',
            isPublished: false,
            coverImage: '',
          },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      handleSubmit(values);
    },
  });

  // const handleEditorChange = (content: any) => {
  //   formik.setFieldValue('content', content);
  // };

  const addImage = (imageUrl: string) => {
    formik.setFieldValue(
      'content',
      `${formik.values.content}<img src="${imageUrl}">`
    );
  };

  // const handleCustomButtonClick = () => {
  //   setOpenGalleryDialog(true);
  // };

  return (
    <EditorFormStyle>
      <div className="img-finder">
        <ImageFinder
          handleInsertImage={(imageUrl: string) => addImage(imageUrl)}
          isOpen={isOpenGalleryDialog}
          handleOpenDialog={(status: boolean) => setOpenGalleryDialog(status)}
        />
      </div>
      <form onSubmit={formik.handleSubmit}>
        <div className="flex flex-row flex-wrap gap-8">
          <div className="flex-1 min-w-[300px] min-h-[400px]">
            {/* <Editor */}
            {/*  apiKey={publicRuntimeConfig.TINY_API_KEY} */}
            {/*  value={formik.values.content} */}
            {/*  onEditorChange={handleEditorChange} */}
            {/*  init={{ */}
            {/*    height: '100%', */}
            {/*    branding: false, */}
            {/*    plugins: 'image link lists', */}
            {/*    toolbar: */}
            {/*      "blocks fontsize | bold italic underline | link image imgGallery | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat'", */}
            {/*    menubar: false, */}
            {/*    contextmenu: 'paste copy cut | link', */}
            {/*    setup: (editor) => { */}
            {/*      editor.ui.registry.addButton('imgGallery', { */}
            {/*        text: 'Gallery', */}
            {/*        onAction: () => { */}
            {/*          handleCustomButtonClick(); */}
            {/*        }, */}
            {/*      }); */}
            {/*    }, */}
            {/*  }} */}
            {/* /> */}
          </div>
          <div className="w-[400px] max-w-full sm:w-[350px]">
            <p>Title</p>
            <TextField
              type="text"
              name="title"
              value={formik.values.title}
              onChange={formik.handleChange}
              placeholder="ชื่อบทความ"
              error={formik.touched.title && Boolean(formik.errors.title)}
              helperText={
                (formik.touched.title && formik.errors.title) as string
              }
            />
            <p>Description</p>
            <TextField
              type="text"
              name="description"
              multiline
              rows={5}
              value={formik.values.description}
              onChange={formik.handleChange}
              placeholder="คำอธิบาย"
              error={
                formik.touched.description && Boolean(formik.errors.description)
              }
              helperText={
                (formik.touched.description &&
                  formik.errors.description) as string
              }
            />
            <p>Keywords</p>
            <TextField
              type="text"
              name="keywords"
              value={formik.values.keywords}
              onChange={formik.handleChange}
              placeholder="คีย์เวิร์ด"
              error={formik.touched.keywords && Boolean(formik.errors.keywords)}
              helperText={
                (formik.touched.keywords && formik.errors.keywords) as string
              }
            />{' '}
            <p>Url</p>
            <TextField
              type="text"
              name="urlSlug"
              value={formik.values.urlSlug}
              onChange={formik.handleChange}
              placeholder="ex. article-1"
              error={formik.touched.urlSlug && Boolean(formik.errors.urlSlug)}
              helperText={
                (formik.touched.urlSlug && formik.errors.urlSlug) as string
              }
            />
            <p>Url รูปปก</p>
            <TextField
              type="text"
              name="coverImage"
              value={formik.values.coverImage}
              onChange={formik.handleChange}
              placeholder="ex. article-1"
              error={
                formik.touched.coverImage && Boolean(formik.errors.coverImage)
              }
              helperText={
                (formik.touched.coverImage &&
                  formik.errors.coverImage) as string
              }
            />
            <p>Status</p>
            <RadioGroup
              row
              name="isPublished"
              value={formik.values.isPublished}
              onChange={formik.handleChange}
            >
              <FormControlLabel
                value={false}
                control={<Radio />}
                label="ฉบับร่าง"
              />
              <FormControlLabel
                value={true}
                control={<Radio />}
                label="เผยแพร่"
              />
            </RadioGroup>
            <div className="pt-4 pb-[8em] flex flex-row gap-4 items-center justify-center">
              <Button
                type="submit"
                variant="contained"
                color="dark"
                size="large"
                fullWidth
              >
                บันทึก
              </Button>
            </div>
          </div>
        </div>
      </form>
    </EditorFormStyle>
  );
};

export default ArticleForm;
