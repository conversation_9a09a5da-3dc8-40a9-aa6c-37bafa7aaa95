import { useEffect, useState } from 'react';
import apiArticleImages from '@/services/core/articleImage';
import { Dialog, IconButton } from '@mui/material';
import styled from 'styled-components';
import { dataURItoFile } from '@/utils/dataURItoFile';
import Resizer from 'react-image-file-resizer';
import { Trash2 } from 'react-feather';
import Swal from 'sweetalert2';
import { BackupOutlined } from '@mui/icons-material';

const ImageListStyle = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-gap: 15px;
  background: white;
  padding: 20px;

  .img-item {
    overflow: hidden;
    cursor: pointer;
    position: relative;

    .img-container {
      height: 170px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: 0.3s;
        &:hover {
          filter: brightness(0.5);
        }
      }
    }

    .del-btn {
      position: absolute;
      right: 5px;
      top: 5px;
      background: #a40202;
      color: white;
    }
  }
`;

const UploadBTNStyle = styled.label`
  display: flex;
  flex-direction: row;
  gap: 10px;
  background: #333;
  color: white;
  padding: 10px 20px;
  border-radius: 32px;
  transition: 0.3s;
  cursor: pointer;
  &:hover {
    background: #555;
  }
`;
type ImageFinderProps = {
  isOpen: boolean;
  handleInsertImage: (imageUrl: string) => void;
  handleOpenDialog: (status: boolean) => void;
};
const ImageFinder = ({
  handleInsertImage,
  isOpen,
  handleOpenDialog,
}: ImageFinderProps) => {
  const [rows, setRows] = useState<any[]>([]);
  const [filters] = useState({
    page: 0,
    pageSize: 10,
  });
  useEffect(() => {
    getImages();
  }, []);

  const getImages = async () => {
    const res = await apiArticleImages.getAll(filters);
    if (res && !res.isError) {
      setRows(res.data.content);
    }
  };
  const handleImageUpload = (event: any) => {
    const file = event.target.files[0];
    if (file) {
      Resizer.imageFileResizer(
        file,
        600, // new width in pixels
        600, // new height in pixels - set this proportionally if needed
        'PNG', // output file type (JPEG, PNG, etc.)
        100, // quality (0 to 100)
        0, // rotation (0, 90, 180, 270)
        async (uri: any) => {
          const resizedFile = await dataURItoFile(
            uri,
            event.target.files[0].name
          );
          uploadImage(resizedFile);
        },
        'base64' // output type (base64, blob, file)
      );
    }
  };

  const uploadImage = async (uploadImage: any) => {
    const formData = new FormData();
    formData.append('files', uploadImage as any);
    const res = await apiArticleImages.create(formData);
    if (res && !res.isError) {
      getImages();
    }
  };

  const deleteImage = async (imageId: string) => {
    Swal.fire({
      title: 'ลบรูป',
      text: 'ยืนยันการลบรุป ?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await apiArticleImages.remove(imageId);
        if (res && !res.isError) {
          getImages();
        }
      }
    });
  };

  const chooseImage = (imageUrl: string) => {
    handleInsertImage(imageUrl);
    handleOpenDialog(false);
  };
  return (
    <>
      <Dialog open={isOpen} onClose={() => handleOpenDialog(false)}>
        <div className="w-[700px] max-w-full h-[600px] flex flex-col">
          <div className="flex flex-row justify-between w-full items-center gap-4 p-4">
            <h2 className="m-0 text-[1.2em]">แกลเลอรี่รูป</h2>
            <UploadBTNStyle>
              <BackupOutlined />
              อัปโหลด
              <input
                type="file"
                style={{ display: 'none' }}
                onChange={handleImageUpload}
              />
            </UploadBTNStyle>
          </div>
          <div className="flex-1 overflow-y-auto">
            <ImageListStyle>
              {rows.map((item) => (
                <div key={item.id} className="img-item">
                  <div className="img-container">
                    <img
                      src={`${item.imageUrl}`}
                      alt=""
                      onClick={() => chooseImage(item.imageUrl)}
                    />
                  </div>
                  <IconButton
                    className="del-btn"
                    size="small"
                    onClick={() => deleteImage(item.id)}
                  >
                    <Trash2 />
                  </IconButton>
                </div>
              ))}
            </ImageListStyle>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default ImageFinder;
