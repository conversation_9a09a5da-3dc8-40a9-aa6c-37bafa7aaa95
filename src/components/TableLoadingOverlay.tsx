import React from 'react';
import { GridOverlay } from '@mui/x-data-grid';
import styled from 'styled-components';

const LoadingOverlayStyled = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 56px;
  font-size: 14px;
  color: #cfd8dc;
`;

const TableLoadingOverlay = () => {
  return (
    <GridOverlay>
      <LoadingOverlayStyled>Loading...</LoadingOverlayStyled>
    </GridOverlay>
  );
};

export default TableLoadingOverlay;
