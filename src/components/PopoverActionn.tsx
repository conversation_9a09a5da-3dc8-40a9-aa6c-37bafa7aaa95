import React, { useState } from 'react';
import { Menu, MenuItem } from '@mui/material';

type PopoverProps = {
  triggerElement?: React.ReactNode;
  customItems?: {
    IconElement?: () => any;
    title: string;
    disabled?: boolean;
    onAction: () => any;
    cssProps?: any;
  }[];
};

const PopoverAction = ({ triggerElement, customItems }: PopoverProps) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <>
      <div
        className="kebab-wrap"
        style={{ cursor: 'pointer' }}
        onClick={(e: any) => {
          e.stopPropagation();
          handleClick(e);
        }}
      >
        {triggerElement && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {triggerElement}
          </div>
        )}
      </div>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        sx={{
          marginTop: '12px',
          '.MuiList-root': {
            padding: '8px',
            minWidth: '150px',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            rowGap: '4px',
          },
          li: {
            width: 'auto',
          },
        }}
      >
        {customItems &&
          customItems.map((item, index) => {
            return (
              <MenuItem
                key={index}
                onClick={() => (item.disabled ? null : handleClose())}
                sx={{
                  padding: '0 8px',
                  height: '40px',
                  width: '100px',
                  borderRadius: '8px',
                  cursor: item.disabled ? 'not-allowed' : 'pointer',
                  opacity: item.disabled ? 0.2 : 1,
                  ...item.cssProps,
                }}
              >
                <div
                  className="drop-menu"
                  onClick={() => (item.disabled ? null : item.onAction())}
                >
                  {item.IconElement && item.IconElement()}
                  {item.title}
                </div>
              </MenuItem>
            );
          })}
      </Menu>
    </>
  );
};

export default PopoverAction;
