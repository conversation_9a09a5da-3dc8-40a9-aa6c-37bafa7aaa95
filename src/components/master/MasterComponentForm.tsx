import React, { ReactNode, useEffect, useState } from 'react';
import {
  CircularProgress,
  Dialog,
  DialogContent,
  Divider,
  FormControl,
  FormHelperText,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Controller,
  SubmitHandler,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import FormModal from '@/components/global/form/FormModal';
import { useAppDispatch } from '@/store';
import { FormModalProps } from '@/utils/form';
import { LoadingButton } from '@mui/lab';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import Image from 'next/image';
import _, { isEmpty } from 'lodash';
import DeleteButton from '@/components/global/DeleteButton';
import apiMasterComponent from '@/services/stock/master-component';
import { setSnackBar } from '@/store/features/alert';
import apiComponentType from '@/services/stock/component-type';

type FormValues = {
  configComponentRequest: {
    masterId: any;
    componentIds: any[];
  }[];
};

const MasterComponentForm: React.FC<FormModalProps> = (props) => {
  const { title, initialValues, openModal, onClose, reloadData, masterList } =
    props;
  const dispatch = useAppDispatch();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors: hookFormErrors, isSubmitted },
    reset,
    watch,
  } = useForm<FormValues>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: initialValues,
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'configComponentRequest',
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [listComponents, setListComponents] = useState<
    { index: number; masterId: number; components: [] }[]
  >([]);

  useEffect(() => {
    reset({ configComponentRequest: [] });
  }, []);

  const fetcherComponentById = async (masterId: number, index: number) => {
    const response = await apiComponentType.getAllByMaster({
      masterId: masterId,
    });
    if (response.data) {
      const arr: any = [];
      response.data.forEach((item: any) => {
        arr.push({ ...item, isSelect: false });
        item.component.forEach((com: any) => {
          arr.push({ ...com, isSelect: true });
        });
      });
      const indexList = _.findIndex(listComponents, (o: any) => {
        return o.index === index;
      });
      if (indexList < 0) {
        setListComponents((prevState: any) => [
          ...prevState,
          { masterId: masterId, index: index, components: arr },
        ]);
      } else {
        const clone = _.clone(listComponents);
        clone[index].components = arr;
        clone[index].masterId = masterId;
        setListComponents(clone);
      }
    }
  };

  function handleClose() {
    onClose();
  }
  useEffect(() => {
    if (openModal) {
      reset();
      append({
        masterId: null,
        componentIds: [],
      });
    }
  }, [openModal]);

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    setSubmitting(true);
    const body: {
      masterId: number;
      componentId: number;
      isProductConfig: boolean;
    }[] = [];
    values.configComponentRequest.map((master: any) => {
      master.componentIds.map((componentId: any) => {
        const bodyItem: {
          masterId: number;
          componentId: number;
          isProductConfig: boolean;
        } = {
          masterId: master.masterId,
          componentId: componentId,
          isProductConfig: false,
        };
        body.push(bodyItem);
      });
    });
    const res = await apiMasterComponent.create({
      configComponentRequest: body,
    });
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      handleClose();
      reloadData();
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
    }
    setSubmitting(false);
  };

  watch('configComponentRequest');

  return (
    <Dialog open={openModal} onClose={handleClose}>
      <DialogContent
        sx={{
          paddingBottom: 0,
          paddingLeft: 0,
          paddingRight: 0,
        }}
      >
        <FormModal
          notTranslateXHeader={true}
          width={800}
          title={title}
          handleClose={handleClose}
          headerButton={
            <ActionButton
              variant="outlined"
              color="dark"
              icon={<AddCircle />}
              text="เพิ่มรายการ"
              borderRadius="10px"
              onClick={() =>
                append({
                  masterId: null,
                  componentIds: [],
                })
              }
            />
          }
        >
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)} className="mt-2">
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow
                      sx={{
                        width: '100%',
                        display: 'grid',
                        gridTemplateColumns: '6fr 6fr 1fr',
                      }}
                    >
                      <TableCell component="th">Master</TableCell>
                      <TableCell component="th">Component</TableCell>
                      <TableCell />
                    </TableRow>
                  </TableHead>
                  {!isEmpty(fields) ? (
                    <TableBody
                      sx={{
                        minHeight: '500px',
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      {fields.map((field: any, idx: any) => {
                        const componentsByMaster = _.find(
                          listComponents,
                          (o: any) => {
                            return o.index === idx;
                          }
                        );
                        return (
                          <TableRow
                            key={field.id}
                            sx={{
                              '&:last-child td, &:last-child th': { border: 0 },
                              width: '100%',
                              display: 'grid',
                              gridTemplateColumns:
                                'minmax(auto, 4fr) minmax(400px, 6fr) 1fr',
                              columnGap: '8px',
                              padding: '8px',
                            }}
                          >
                            <TableCell
                              sx={{
                                padding: '0',
                                borderBottom: 'unset',
                              }}
                            >
                              <FormControl
                                fullWidth
                                sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  justifyContent: 'start',
                                }}
                              >
                                <Controller
                                  name={`configComponentRequest.${idx}.masterId`}
                                  control={control}
                                  rules={{
                                    required: 'เลือก Master',
                                    validate: (value): any => {
                                      const master = watch(
                                        'configComponentRequest'
                                      );
                                      const checkList = _.filter(
                                        master,
                                        (o: any) => {
                                          return o.masterId === value;
                                        }
                                      );
                                      if (checkList.length > 1) {
                                        return 'Master นี้มีการเลือกซ้ำ';
                                      }
                                    },
                                  }}
                                  render={({
                                    field: { onChange, value, ref },
                                  }) => {
                                    return (
                                      <Select
                                        {...register(
                                          `configComponentRequest.${idx}.masterId`
                                        )}
                                        onChange={(event) => {
                                          onChange(event.target.value);
                                          fetcherComponentById(
                                            event.target.value,
                                            idx
                                          );
                                        }}
                                        value={value || ''}
                                        ref={ref}
                                        displayEmpty
                                        error={
                                          Boolean(
                                            hookFormErrors
                                              .configComponentRequest?.[idx]
                                              ?.masterId
                                          ) && isSubmitted
                                        }
                                      >
                                        <MenuItem disabled value="">
                                          <div className="text-[#78909C]">
                                            กรุณาเลือก
                                          </div>
                                        </MenuItem>
                                        {masterList &&
                                          masterList.content.map(
                                            (item: any) => {
                                              return (
                                                <MenuItem
                                                  key={item.id}
                                                  value={item.id}
                                                >
                                                  {item.name}
                                                </MenuItem>
                                              );
                                            }
                                          )}
                                      </Select>
                                    );
                                  }}
                                />
                                {hookFormErrors.configComponentRequest?.[idx]
                                  ?.masterId && (
                                  <FormHelperText error>
                                    {
                                      hookFormErrors.configComponentRequest?.[
                                        idx
                                      ]?.masterId?.message as ReactNode
                                    }
                                  </FormHelperText>
                                )}
                              </FormControl>
                            </TableCell>
                            <TableCell
                              className="w-full"
                              sx={{
                                padding: '0',
                                borderBottom: 'unset',
                              }}
                            >
                              <FormControl className="w-full">
                                <Controller
                                  control={control}
                                  name={`configComponentRequest.${idx}.componentIds`}
                                  rules={{
                                    validate: (value): any => {
                                      if (value.length < 1) {
                                        return 'เลือก Component';
                                      }
                                    },
                                  }}
                                  render={({ field: { onChange, value } }) => {
                                    return (
                                      <Select
                                        {...register(
                                          `configComponentRequest.${idx}.componentIds`
                                        )}
                                        multiple
                                        displayEmpty
                                        onChange={onChange}
                                        value={value}
                                        defaultValue={[]}
                                        renderValue={(selected: any) => {
                                          const text: any = [];
                                          selected.forEach((id: any) => {
                                            const info: any = _.find(
                                              componentsByMaster?.components,
                                              (s: any) => s.id === id
                                            );
                                            if (info) {
                                              text.push(info.name);
                                            }
                                          });
                                          return text.join(', ');
                                        }}
                                        error={
                                          Boolean(
                                            hookFormErrors
                                              .configComponentRequest?.[idx]
                                              ?.componentIds
                                          ) && isSubmitted
                                        }
                                      >
                                        <MenuItem value="">
                                          <div className="text-[#78909C]">
                                            กรุณาเลือก
                                          </div>
                                        </MenuItem>
                                        {componentsByMaster &&
                                          componentsByMaster.components.map(
                                            (item: any) => {
                                              const checked = _.some(
                                                value,
                                                (o: any) => o === item.id
                                              );
                                              if (item.isSelect === false) {
                                                return (
                                                  <MenuItem
                                                    key={item.id}
                                                    value={item.id}
                                                    disabled
                                                    sx={{
                                                      fontSize: '14px',
                                                    }}
                                                  >
                                                    {`${item.name} (${
                                                      item.countComponent || 0
                                                    })`}
                                                  </MenuItem>
                                                );
                                              }
                                              return (
                                                <MenuItem
                                                  key={item.id}
                                                  value={item.id}
                                                  sx={{
                                                    display: 'flex',
                                                    justifyContent: 'end',
                                                  }}
                                                >
                                                  <div
                                                    className={
                                                      'w-[90%] gap-2 flex justify-between items-center'
                                                    }
                                                  >
                                                    <div className={'truncate'}>
                                                      {item.name}
                                                    </div>
                                                    {checked && (
                                                      <Image
                                                        src={
                                                          '/icons/check-empty.png'
                                                        }
                                                        width={12}
                                                        height={12}
                                                        alt=""
                                                      />
                                                    )}
                                                  </div>
                                                </MenuItem>
                                              );
                                            }
                                          )}
                                      </Select>
                                    );
                                  }}
                                />
                                {hookFormErrors.configComponentRequest?.[idx]
                                  ?.componentIds && (
                                  <FormHelperText error>
                                    {
                                      hookFormErrors.configComponentRequest?.[
                                        idx
                                      ]?.componentIds?.message as ReactNode
                                    }
                                  </FormHelperText>
                                )}
                              </FormControl>
                            </TableCell>
                            <TableCell
                              align="center"
                              className={'pt-1 justify-center'}
                              sx={{
                                padding: '0',
                                borderBottom: 'unset',
                                display: idx > 0 ? 'flex' : 'none',
                              }}
                            >
                              <DeleteButton
                                onClick={() => {
                                  remove(idx);
                                  if (listComponents.length > 1) {
                                    _.remove(
                                      listComponents,
                                      (o: any) => o.index === idx
                                    );
                                  } else {
                                    const have = _.some(
                                      listComponents,
                                      (o: any) => o.index === idx
                                    );
                                    if (have) {
                                      setListComponents([]);
                                    }
                                  }
                                }}
                              >
                                <Image
                                  src="/icons/delete-white.svg"
                                  width={24}
                                  height={24}
                                  alt=""
                                />
                              </DeleteButton>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  ) : (
                    <TableBody className="py-3">
                      <TableRow
                        sx={{
                          '&:last-child td, &:last-child th': { border: 0 },
                        }}
                      >
                        <TableCell colSpan={3} align="center" className="py-12">
                          NO DATA
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  )}
                </Table>
              </TableContainer>
              <Divider />
              <div className="flex gap-[24px] p-6">
                <LoadingButton
                  type="button"
                  disabled={false}
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    fontWeight: '600',
                    fontSize: '16px',
                  }}
                  fullWidth
                  onClick={handleClose}
                >
                  ยกเลิก
                </LoadingButton>
                <LoadingButton
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    fontWeight: '600',
                    fontSize: '16px',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </LoadingButton>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default MasterComponentForm;
