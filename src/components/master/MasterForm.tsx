import React, { ReactNode, useEffect, useState } from 'react';
import {
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { ACTION } from '@/utils/helper';
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form';
import FormModal from '@/components/global/form/FormModal';
import { useAppDispatch } from '@/store';
import { FormModalProps } from '@/utils/form';
import { LoadingButton } from '@mui/lab';
import ImageField from '@/components/ImageField';
import { isEmpty } from 'lodash';
import apiMaster from '@/services/stock/master';
import { setSnackBar } from '@/store/features/alert/actions';

type FormValues = {
  masterCategoryId: number;
  name: string;
  description: string;
  file: any;
  imageUrl: string;
  position: number;
};

const MasterForm: React.FC<FormModalProps> = (props) => {
  const dispatch = useAppDispatch();
  const {
    title,
    initialValues,
    action,
    openModal,
    onClose,
    reloadData,
    masterCategoryList,
  } = props;
  const {
    control,
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    watch,
    reset,
  } = useForm<FormValues>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: initialValues,
  });
  const watchImageUrl = useWatch({
    control,
    name: 'imageUrl',
    defaultValue: !isEmpty(initialValues) ? initialValues.imageUrl : '',
  });
  const [submitting, setSubmitting] = useState<boolean>(false);

  useEffect(() => {
    if (action === ACTION.UPDATE && initialValues) {
      setValue('name', initialValues.name);
      setValue('description', initialValues.description);
      setValue('masterCategoryId', initialValues.masterCategory.id);
      setValue('position', initialValues.position);
      setValue('imageUrl', initialValues.imageUrl);
    }
    return () => {
      reset();
    };
  }, [initialValues]);

  function handleClose() {
    onClose();
  }

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    setSubmitting(true);
    const data: any = {
      name: values.name,
      description: values.description || null,
      masterCategoryId: values.masterCategoryId,
      position: values.position || 0,
    };
    if (action === ACTION.UPDATE) {
      data.id = initialValues?.id;
    }
    let res: any;
    if (action === ACTION.CREATE) {
      const res = await apiMaster.create(data);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('masterId', res.data.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiMaster.uploadImage(formData);
          if (!resUploadFile.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Success!',
                severity: 'success',
              })
            );
            reloadData();
            handleClose();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Error!',
                severity: 'error',
              })
            );
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'Success!',
              severity: 'success',
            })
          );
          reloadData();
          handleClose();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'Error!',
            severity: 'error',
          })
        );
      }
    } else if (action === ACTION.UPDATE) {
      res = await apiMaster.update(data);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('masterId', initialValues.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiMaster.uploadImage(formData);
          if (!resUploadFile.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Success!',
                severity: 'success',
              })
            );
            reloadData();
            handleClose();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Error!',
                severity: 'error',
              })
            );
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'Success!',
              severity: 'success',
            })
          );
          reloadData();
          handleClose();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'Error!',
            severity: 'error',
          })
        );
      }
    }
    setSubmitting(false);
  };
  useEffect(() => {
    if (action === ACTION.CREATE && !watch('masterCategoryId')) {
      setValue('masterCategoryId', 3);
      setValue('position', 1);
    }
  }, [watch('masterCategoryId')]);
  useEffect(() => {
    if (watch('masterCategoryId') !== 3) {
      setValue('position', 0);
    } else {
      setValue('position', 1);
    }
  }, [watch('masterCategoryId')]);
  useEffect(() => {
    if (openModal && action !== ACTION.UPDATE) {
      reset();
    }
  }, [openModal]);
  return (
    <Dialog open={openModal} onClose={handleClose}>
      <DialogContent>
        <FormModal width={492} title={title} handleClose={handleClose}>
          <div className="form-wrap">
            <form
              onSubmit={handleSubmit(onSubmit)}
              style={{
                marginTop: '24px',
              }}
            >
              <ImageField
                handleChange={(file: any) => {
                  setValue('file', file);
                }}
                defaultBackground={watchImageUrl || '/images/add-image.svg'}
                borderRadius="14px"
                alertRequire={false}
                conditionText={
                  'Upload JPG, PNG or SVG best sizes 160x160 file maximum 2 mb.'
                }
                textUploadBtn="Upload"
              />
              <div>
                <p>Master Category</p>
                {masterCategoryList && (
                  <FormControl fullWidth>
                    <Controller
                      render={({ field: { onChange, value, ref } }) => {
                        return (
                          <Select
                            {...register('masterCategoryId', {
                              required: 'เลือก Master Category',
                            })}
                            onChange={onChange}
                            value={value || ''}
                            ref={ref}
                            displayEmpty
                            error={!!hookFormErrors.masterCategoryId}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            {masterCategoryList.map((masterCategory: any) => {
                              return (
                                <MenuItem
                                  key={masterCategory.id}
                                  value={masterCategory.id}
                                >
                                  {masterCategory.name}
                                </MenuItem>
                              );
                            })}
                          </Select>
                        );
                      }}
                      name="masterCategoryId"
                      control={control}
                    />
                  </FormControl>
                )}
                {!!hookFormErrors.masterCategoryId?.message && (
                  <FormHelperText error>
                    {hookFormErrors.masterCategoryId.message}
                  </FormHelperText>
                )}
              </div>
              {watch('masterCategoryId') === 3 && (
                <div>
                  <p>เลือกก่อนพิมพ์ - หลังพิมพ์</p>
                  <FormControl fullWidth>
                    <Controller
                      render={({ field: { onChange, value, ref } }) => {
                        return (
                          <Select
                            {...register('position', {
                              required: 'กรุณาเลือก position',
                            })}
                            onChange={onChange}
                            value={value || ''}
                            ref={ref}
                            displayEmpty
                            error={!!hookFormErrors.position}
                          >
                            <MenuItem disabled value="">
                              <div className="text-[#78909C]">กรุณาเลือก</div>
                            </MenuItem>
                            <MenuItem value={1}>ก่อนพิมพ์</MenuItem>
                            <MenuItem value={2}>หลังพิมพ์</MenuItem>
                          </Select>
                        );
                      }}
                      name="position"
                      control={control}
                    />
                  </FormControl>
                  {!!hookFormErrors.position?.message && (
                    <FormHelperText error>
                      {hookFormErrors.position.message}
                    </FormHelperText>
                  )}
                </div>
              )}

              <div>
                <p>ชื่อ</p>
                <TextField
                  placeholder="กรอกชื่อ"
                  {...register('name', { required: 'กรุณากรอกชื่อ' })}
                  error={!!hookFormErrors.name}
                  helperText={
                    hookFormErrors.name
                      ? (hookFormErrors.name.message as ReactNode)
                      : ''
                  }
                />
              </div>
              <div className={'pb-[6rem]'}>
                <p>รายละเอียด</p>
                <TextField
                  placeholder="กรอกรายละเอียด"
                  {...register('description')}
                  multiline
                  rows={4}
                />
              </div>
              <div className="btn-bottom">
                <LoadingButton
                  type="button"
                  disabled={false}
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    fontWeight: '600',
                    fontSize: '16px',
                  }}
                  fullWidth
                  onClick={handleClose}
                >
                  ยกเลิก
                </LoadingButton>
                <LoadingButton
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    fontWeight: '600',
                    fontSize: '16px',
                  }}
                  fullWidth
                >
                  {submitting ? (
                    <CircularProgress
                      size={20}
                      style={{
                        color: 'white',
                      }}
                    />
                  ) : (
                    'บันทึก'
                  )}
                </LoadingButton>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default MasterForm;
