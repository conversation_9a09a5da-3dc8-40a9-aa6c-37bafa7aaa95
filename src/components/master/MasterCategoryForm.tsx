import React, { ReactNode, useEffect } from 'react';
import { <PERSON><PERSON>, Di<PERSON>, DialogContent, TextField } from '@mui/material';
import apiMasterCategory from '@/services/stock/master-category';
import { ACTION } from '@/utils/helper';
import { SubmitHandler, useForm } from 'react-hook-form';
import FormModal from '@/components/global/form/FormModal';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { FormModalProps } from '@/utils/form';

type FormValues = {
  name: string;
  description: string;
};

const MasterCategoryForm: React.FC<FormModalProps> = (props) => {
  const dispatch = useAppDispatch();
  const { title, initialValues, action, openModal, onClose, reloadData } =
    props;
  const {
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    reset,
  } = useForm<FormValues>({
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  useEffect(() => {
    if (action === ACTION.UPDATE && initialValues) {
      setValue('name', initialValues.name);
      setValue('description', initialValues.description);
    }

    return () => {
      reset();
    };
  }, [initialValues]);

  function handleClose() {
    onClose();
  }

  useEffect(() => {
    if (openModal && action !== ACTION.UPDATE) {
      reset();
    }
  }, [openModal]);

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    const data: any = {
      name: values.name,
      description: values.description || null,
    };
    if (action === ACTION.UPDATE) {
      data.id = initialValues?.id;
    }
    let res: any;
    if (action === ACTION.CREATE) {
      res = await apiMasterCategory.create(data);
    } else if (action === ACTION.UPDATE) {
      res = await apiMasterCategory.update(data);
    }
    if (res && (res.isError || res.error)) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'An error occurred!',
          severity: 'error',
        })
      );
    } else {
      handleClose();
      reloadData();
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Success!',
          severity: 'success',
        })
      );
    }
  };

  return (
    <Dialog open={openModal}>
      <DialogContent>
        <FormModal width={500} title={title} handleClose={handleClose}>
          <div className="form-wrap">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col justify-center">
                <div className="flex flex-col ">
                  <p>ชื่อ</p>
                  <TextField
                    {...register('name', { required: 'กรอกชื่อ' })}
                    error={Boolean(hookFormErrors.name)}
                    helperText={hookFormErrors.name?.message as ReactNode}
                  />
                </div>
                <div className="flex flex-col">
                  <p>รายละเอียด</p>
                  <TextField {...register('description')} multiline rows={3} />
                </div>
                <div className="w-full flex justify-between mt-[34px] gap-5">
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                    onClick={handleClose}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                  >
                    บันทึก
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

export default MasterCategoryForm;
