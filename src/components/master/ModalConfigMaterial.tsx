import React, { <PERSON>actNode, useEffect, useState } from 'react';
import {
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Select,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import {
  Control,
  Controller,
  SubmitHandler,
  useForm,
  useWatch,
} from 'react-hook-form';
import { LoadingButton } from '@mui/lab';
import useSWR from 'swr';
import apiMaterial from '@/services/stock/material';
import apiMaster from '@/services/stock/master';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';

type Props = {
  open: boolean;
  handleClose: () => void;
  title: string;
  handleReloadList: () => void;
};
type configMaterialRequest = {
  subMaterialId: number | null;
  isProductConfig: boolean;
};
type FormValues = {
  materialId: number | string;
  configMaterialRequest: configMaterialRequest[];
};

const fetcherMaterialList = async (_url: string) => {
  const response = await apiMaterial.getListAll();
  return response.data;
};
const validationSchema = yup.object().shape({
  materialId: yup.number().typeError('กรุณาเลือก').required('กรุณาเลือก'),
  configMaterialRequest: yup
    .array()
    .of(
      yup.object().shape({
        subMaterialId: yup.number().nullable(),
        isProductConfig: yup.boolean(),
      })
    )
    .test(
      'at-least-one-subMaterialId',
      'กรุณาเลือก อย่างน้อย 1 รายการ',
      (value: any) => {
        return value.some(
          (item: any) =>
            item.subMaterialId !== null && item.subMaterialId !== undefined
        );
      }
    ),
});

const ModalConfigMaterial = ({
  open,
  handleClose,
  title,
  handleReloadList,
}: Props) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { masterId } = router.query;
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [subMaterialList, setSubMaterialList] = useState<any>([]);
  const [defaultValues] = useState<any>({
    masterId: null,
    configMaterialRequest: [
      {
        subMaterialId: null,
        isProductConfig: false,
      },
    ],
  });
  const {
    control,
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    reset,
    clearErrors,
  } = useForm<FormValues>({
    resolver: yupResolver(validationSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: defaultValues,
  });
  const watchMaterialId = useWatch({
    control,
    name: 'materialId',
    defaultValue: '',
  });
  const watchConfigMaterialRequest = useWatch({
    control,
    name: 'configMaterialRequest',
    defaultValue: [],
  });
  useEffect(() => {
    if (!isEmpty(subMaterialList)) {
      const resetValue = {
        masterId: Number(masterId),
        materialId: watchMaterialId,
        configMaterialRequest: subMaterialList.map(() => ({
          subMaterialId: null,
          isProductConfig: false,
        })),
      };
      reset(resetValue);
    }
  }, [subMaterialList]);
  useEffect(() => {
    if (open) {
      reset();
      setValue('materialId', '');
      setSubMaterialList([]);
    }
  }, [open]);
  const { data: materialList } = useSWR(['/material/list'], ([url]) =>
    fetcherMaterialList(url)
  );
  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    setSubmitting(true);
    const removeNullSubMaterial = values.configMaterialRequest.filter(
      (cmr: any) =>
        cmr.subMaterialId !== null && cmr.subMaterialId !== undefined
    );
    const sendValue = {
      ...values,
      configMaterialRequest: removeNullSubMaterial,
    };
    const res = await apiMaster.createMaterialMasterConfig(sendValue);
    dispatch(
      setSnackBar({
        status: true,
        text: !res.isError ? 'บันทึกสำเร็จ' : 'เกิดข้อผิดพลาด',
        severity: !res.isError ? 'success' : 'error',
      })
    );
    if (!res.isError) {
      handleReloadList();
      handleClose();
    }
    setSubmitting(false);
  };
  const getListSubMaterialByMasterId = async () => {
    const res = await apiMaster.getListSubMaterialByMasterId(
      Number(masterId),
      Number(watchMaterialId)
    );
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setSubMaterialList(res.data);
      } else {
        setSubMaterialList(null);
      }
    }
  };

  useEffect(() => {
    if (watchMaterialId) {
      getListSubMaterialByMasterId();
    }
  }, [watchMaterialId]);

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent>
        <FormModal width={492} title={title} handleClose={handleClose}>
          <div className="content-wrap">
            <div className="form-wrap">
              <form onSubmit={handleSubmit(onSubmit)}>
                <div>
                  <p>เลือกรายการ</p>
                  <FormControl fullWidth>
                    <Controller
                      name="materialId"
                      control={control as Control<FormValues>}
                      render={({ field: { onChange, value, ref } }) => (
                        <Select
                          {...register('materialId', {
                            required: 'กรุณาเลือก',
                          })}
                          onChange={(e: any) => {
                            clearErrors('configMaterialRequest');
                            onChange(e);
                          }}
                          value={value || ''}
                          ref={ref}
                          displayEmpty
                          error={!!hookFormErrors.materialId}
                        >
                          <MenuItem disabled value="">
                            <div className="text-[#78909C]">กรุณาเลือก</div>
                          </MenuItem>
                          {materialList?.map((masterCategory: any) => {
                            return (
                              <MenuItem
                                key={masterCategory.id}
                                value={masterCategory.id}
                              >
                                {masterCategory.name}
                              </MenuItem>
                            );
                          })}
                        </Select>
                      )}
                    />
                  </FormControl>
                  {!!hookFormErrors.materialId?.message && (
                    <FormHelperText error>
                      {hookFormErrors.materialId.message}
                    </FormHelperText>
                  )}
                </div>
                {!isEmpty(subMaterialList) && (
                  <>
                    <CheckBoxWrapperStyle>
                      {subMaterialList.map((item: any, index: number) => {
                        return (
                          <div className="check-list" key={index}>
                            <Controller
                              {...register(
                                `configMaterialRequest.${index}.subMaterialId`
                              )}
                              control={control}
                              render={({ field: { ref } }) => (
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      ref={ref}
                                      color="primary"
                                      checked={watchConfigMaterialRequest.some(
                                        (cmr: any) =>
                                          cmr.subMaterialId === item.id
                                      )}
                                      onChange={(e: any) => {
                                        if (e.target.checked) {
                                          clearErrors('configMaterialRequest');
                                          setValue(
                                            `configMaterialRequest.${index}`,
                                            {
                                              subMaterialId: item.id,
                                              isProductConfig:
                                                watchConfigMaterialRequest[
                                                  index
                                                ]?.isProductConfig ?? false,
                                            }
                                          );
                                        } else {
                                          setValue(
                                            `configMaterialRequest.${index}`,
                                            {
                                              ...watchConfigMaterialRequest[
                                                index
                                              ],
                                              subMaterialId: null,
                                              isProductConfig: false,
                                            }
                                          );
                                        }
                                      }}
                                      icon={<IconUnCheckbox />}
                                      checkedIcon={<IconCheckbox />}
                                    />
                                  }
                                  label={item.name}
                                />
                              )}
                            />
                            <Controller
                              {...register(
                                `configMaterialRequest.${index}.isProductConfig`
                              )}
                              control={control}
                              render={({ field: { ref } }) => (
                                <FormControlLabel
                                  sx={{
                                    background: 'transparent !important',
                                    '& *': {
                                      background: 'transparent !important',
                                    },
                                  }}
                                  control={
                                    <Checkbox
                                      ref={ref}
                                      color="primary"
                                      checked={
                                        watchConfigMaterialRequest[index]
                                          ?.isProductConfig || false
                                      }
                                      onChange={(e: any) => {
                                        setValue(
                                          `configMaterialRequest.${index}`,
                                          {
                                            ...watchConfigMaterialRequest[
                                              index
                                            ],
                                            isProductConfig: e.target.checked,
                                          }
                                        );
                                      }}
                                      icon={<IconUnCheckbox />}
                                      checkedIcon={<IconCheckboxBlack />}
                                      disabled={
                                        !watchConfigMaterialRequest.some(
                                          (cmr: any) =>
                                            cmr.subMaterialId === item.id
                                        )
                                      }
                                    />
                                  }
                                  label="Configure"
                                />
                              )}
                            />
                          </div>
                        );
                      })}
                    </CheckBoxWrapperStyle>
                    {!!hookFormErrors.configMaterialRequest && (
                      <FormHelperText error>
                        {
                          hookFormErrors.configMaterialRequest
                            ?.message as ReactNode
                        }
                      </FormHelperText>
                    )}
                  </>
                )}

                {subMaterialList === null && <EmptySubMaterialStyle />}
                <div className="flex gap-[24px] mt-[34px]">
                  <LoadingButton
                    type="button"
                    disabled={false}
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      fontWeight: '600',
                      fontSize: '16px',
                    }}
                    fullWidth
                    onClick={handleClose}
                  >
                    ยกเลิก
                  </LoadingButton>
                  <LoadingButton
                    type="submit"
                    variant="contained"
                    color="dark"
                    sx={{
                      fontWeight: '600',
                      fontSize: '16px',
                    }}
                    fullWidth
                  >
                    {submitting ? (
                      <CircularProgress
                        size={20}
                        style={{
                          color: 'white',
                        }}
                      />
                    ) : (
                      'บันทึก'
                    )}
                  </LoadingButton>
                </div>
              </form>
            </div>
          </div>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};

const CheckBoxWrapperStyle = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  animation: ${LoadingFadein} 0.3s ease-in;
  .check-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    border-bottom: 1px solid #dbe2e5;
  }
`;

const EmptySubMaterialStyle = styled.div`
  width: 100%;
  height: 40px;
  border-radius: 8px;
  border: 1px dashed #dbe2e5;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-top: 16px;
  animation: ${LoadingFadein} 0.3s ease-in;
  &:before {
    content: 'ไม่มีรายการ';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #dbe2e5;
  }
`;

export default ModalConfigMaterial;
