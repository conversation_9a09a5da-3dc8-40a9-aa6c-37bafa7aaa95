import React, { <PERSON>actNode, useEffect, useState } from 'react';
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { ACTION } from '@/utils/helper';
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form';
import FormModal from '@/components/global/form/FormModal';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { FormModalProps } from '@/utils/form';
import apiComponent from '@/services/stock/component';
import { FadeInStyled } from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import styled from 'styled-components';
import { useRouter } from 'next/router';
import apiFormula from '@/services/stock/formula';
import ImageField from '@/components/ImageField';

type FormValues = {
  componentTypeId: number | string;
  name: string;
  description: string;
  optionsCategoryId: number | string;
  optionsId: number | string;
  imageUrl: string;
  file: any;
};

const ComponentForm: React.FC<FormModalProps> = (props) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [optionsCategoryList, setOptionsCategoryList] = useState<any>([]);
  const [optionsList, setOptionsList] = useState<any>([]);
  const {
    title,
    initialValues,
    action,
    openModal,
    onClose,
    reloadData,
    componentTypeList,
  } = props;
  const {
    control,
    register,
    handleSubmit,
    formState: { errors: hookFormErrors },
    setValue,
    reset,
  } = useForm<FormValues>({
    mode: 'onChange',
    reValidateMode: 'onChange',
  });
  const watchOptionsCategoryId = useWatch({
    control,
    name: 'optionsCategoryId',
    defaultValue: '',
  });
  const watchImageUrl = useWatch({
    control,
    name: 'imageUrl',
    defaultValue: !isEmpty(initialValues) ? initialValues.imageUrl : '',
  });
  const getOptionsCategoryList = async () => {
    const res = await apiFormula.getOptionsCategoryList();
    if (!res.isError) {
      setOptionsCategoryList(res.data);
    }
  };

  const getOptionsByCategoryList = async () => {
    const res = await apiFormula.getOptionsList({
      optionsCategoryId: watchOptionsCategoryId,
    });
    if (!res.isError) {
      if (!isEmpty(res.data)) {
        setOptionsList(res.data);
      } else {
        setOptionsList(null);
      }
    }
  };

  useEffect(() => {
    getOptionsCategoryList();
  }, []);

  useEffect(() => {
    if (watchOptionsCategoryId) {
      getOptionsByCategoryList();
    }
  }, [watchOptionsCategoryId]);

  useEffect(() => {
    if (action === ACTION.UPDATE && initialValues) {
      setValue('name', initialValues.name);
      setValue('description', initialValues.description);
      setValue('componentTypeId', initialValues.componentType.id);
      setValue('optionsCategoryId', initialValues.options?.optionsCategory.id);
      setValue('optionsId', initialValues.options?.id);
      setValue('imageUrl', initialValues.imageUrl);
    }
    return () => {
      reset();
    };
  }, [initialValues]);

  function handleClose() {
    onClose();
  }

  useEffect(() => {
    if (openModal && action !== ACTION.UPDATE) {
      reset();
    }
  }, [openModal]);

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    const data: any = {
      name: values.name,
      description: values.description || null,
      componentTypeId: values.componentTypeId,
      optionsId: values.optionsId,
      componentDetail: [],
    };
    if (action === ACTION.UPDATE) {
      data.id = initialValues?.id;
      data.componentDetail = initialValues?.componentDetail;
    }

    let res: any;
    if (action === ACTION.CREATE) {
      res = await apiComponent.create(data);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('id', res.data.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiComponent.uploadImage(formData);
          if (!resUploadFile.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Success!',
                severity: 'success',
              })
            );
            reloadData();
            handleClose();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Error!',
                severity: 'error',
              })
            );
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'Success!',
              severity: 'success',
            })
          );
          reloadData();
          handleClose();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'Error!',
            severity: 'error',
          })
        );
      }
    } else if (action === ACTION.UPDATE) {
      res = await apiComponent.update(data);
      if (!res.isError) {
        if (values.file) {
          const formData = new FormData();
          if (values.file) {
            formData.append('id', data.id);
            formData.append('file', values.file[0]);
          }
          const resUploadFile = await apiComponent.uploadImage(formData);
          if (!resUploadFile.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Success!',
                severity: 'success',
              })
            );
            reloadData();
            handleClose();
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message || 'Error!',
                severity: 'error',
              })
            );
          }
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message || 'Success!',
              severity: 'success',
            })
          );
          reloadData();
          handleClose();
        }
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message || 'Error!',
            severity: 'error',
          })
        );
      }
    }
  };

  return (
    <Dialog open={openModal}>
      <DialogContent>
        <FormModal width={500} title={title} handleClose={handleClose}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            style={{
              marginTop: '24px',
            }}
          >
            <ImageField
              handleChange={(file: any) => {
                setValue('file', file);
              }}
              defaultBackground={watchImageUrl || '/images/add-image.svg'}
              borderRadius="14px"
              alertRequire={false}
              conditionText={
                'Upload JPG, PNG or SVG best sizes 160x160 file maximum 2 mb.'
              }
              textUploadBtn="Upload"
            />
            <div className="flex flex-col justify-center">
              <div className="flex flex-col ">
                <p>Component Type</p>
                {componentTypeList && (
                  <FormControl>
                    <Controller
                      render={({ field: { onChange, value, ref } }) => {
                        return (
                          <Select
                            {...register('componentTypeId', {
                              required: 'เลือก Component Type',
                            })}
                            onChange={onChange}
                            value={value}
                            ref={ref}
                            displayEmpty={true}
                            error={!!hookFormErrors.componentTypeId}
                          >
                            <MenuItem value={undefined} disabled>
                              <div className="text-[#78909C]">
                                เลือก Component Type
                              </div>
                            </MenuItem>
                            {componentTypeList.map((item: any) => {
                              return (
                                <MenuItem key={item.id} value={item.id}>
                                  {item.name}
                                </MenuItem>
                              );
                            })}
                          </Select>
                        );
                      }}
                      name="componentTypeId"
                      control={control}
                    />
                  </FormControl>
                )}
                {hookFormErrors.componentTypeId && (
                  <FormHelperText error>
                    {hookFormErrors.componentTypeId.message}
                  </FormHelperText>
                )}
              </div>
              <div className="flex flex-col">
                <p>ชื่อ</p>
                <TextField
                  {...register('name', { required: 'กรอกชื่อ' })}
                  placeholder="กรอกชื่อ"
                  error={Boolean(hookFormErrors.name)}
                  helperText={hookFormErrors.name?.message as ReactNode}
                />
              </div>
              <FadeInStyled>
                <p>หมวดหมู่สูตร</p>
                <FormControl fullWidth>
                  <Controller
                    render={({ field: { onChange, value, ref } }) => {
                      return (
                        <Select
                          {...register('optionsCategoryId', {
                            required: 'เลือกหมวดหมู่',
                          })}
                          onChange={onChange}
                          value={value}
                          ref={ref}
                          displayEmpty={true}
                          error={!!hookFormErrors.optionsCategoryId}
                        >
                          <MenuItem value={undefined} disabled>
                            <div className="text-[#78909C]">เลือกหมวดหมู่</div>
                          </MenuItem>
                          {optionsCategoryList.map(
                            (name: any, index: number) => (
                              <MenuItem key={index} value={name.id}>
                                {name.name}
                              </MenuItem>
                            )
                          )}
                        </Select>
                      );
                    }}
                    name="optionsCategoryId"
                    control={control}
                  />
                </FormControl>
                {hookFormErrors.optionsCategoryId && (
                  <FormHelperText error>
                    {hookFormErrors.optionsCategoryId.message}
                  </FormHelperText>
                )}
              </FadeInStyled>
              {optionsList === null && (
                <FadeInStyled>
                  <p>สูตร</p>
                  <EmptyTypeStyle
                    onClick={() => {
                      router.push(
                        `/company/formula/category/${watchOptionsCategoryId}`
                      );
                    }}
                  >
                    สร้างประเภท
                  </EmptyTypeStyle>
                </FadeInStyled>
              )}
              {optionsList !== null && (
                <FadeInStyled>
                  <p>สูตร</p>
                  <FormControl fullWidth>
                    <Controller
                      render={({ field: { onChange, value, ref } }) => {
                        return (
                          <Select
                            {...register('optionsId', {
                              required: 'เลือกประเภท',
                            })}
                            onChange={onChange}
                            value={value}
                            ref={ref}
                            displayEmpty={true}
                            error={!!hookFormErrors.optionsId}
                          >
                            <MenuItem value={undefined} disabled>
                              <div className="text-[#78909C]">เลือกประเภท</div>
                            </MenuItem>
                            {!isEmpty(optionsList) &&
                              optionsList.map((name: any, index: number) => (
                                <MenuItem key={index} value={name.id}>
                                  {name.name}
                                </MenuItem>
                              ))}
                          </Select>
                        );
                      }}
                      name="optionsId"
                      control={control}
                    />
                  </FormControl>
                  {hookFormErrors.optionsId && (
                    <FormHelperText error>
                      {hookFormErrors.optionsId.message}
                    </FormHelperText>
                  )}
                </FadeInStyled>
              )}
              <div className="flex flex-col pb-[6rem]">
                <p>รายละเอียด</p>
                <TextField
                  {...register('description')}
                  placeholder="กรอกรายละเอียด"
                  multiline
                  rows={3}
                />
              </div>
              <div className="btn-bottom">
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleClose}
                >
                  <span>ยกเลิก</span>
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                >
                  บันทึก
                </Button>
              </div>
            </div>
          </form>
        </FormModal>
      </DialogContent>
    </Dialog>
  );
};
const EmptyTypeStyle = styled.div`
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #263238;
  border-radius: 8px;
  border: 1px dashed #dbe2e5;
  cursor: pointer;
  text-align: center;

  &:hover {
    text-decoration: underline;
  }
`;
export default ComponentForm;
