import React from 'react';
import { QuotationStatus } from '@/store/features/quotation/types';
import styled from 'styled-components';
import SvgScheduleFillIcon from '@/components/svg-icon/SvgScheduleFillIcon';
import SvgCheckCircleFillIcon from '@/components/svg-icon/SvgCheckCircleFillIcon';
import SvgPaperCancelIcon from '@/components/svg-icon/SvgPaperCancelIcon';

const QuotationRenderStatus = ({ status }: { status: number }) => {
  switch (status) {
    case QuotationStatus.DRAFT:
      return (
        <QtStatus
          color={'#000'}
          background={'#f5f7f8'}
          borderColor={'#dbe2e5'}
          radius={'8px'}
          isIcon={false}
        >
          แบบร่าง
        </QtStatus>
      );
    case QuotationStatus.PENDING:
      return (
        <QtStatus
          color={'#F9A925'}
          background={'#FFF5D3'}
          borderColor={'#F9A925'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgScheduleFillIcon />
          <span>รออนุมัติ</span>
        </QtStatus>
      );
    case QuotationStatus.WAITING:
      return (
        <QtStatus
          color={'#F9A925'}
          background={'#FFF5D3'}
          borderColor={'#F9A925'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgScheduleFillIcon />
          <span>รอตอบรับ</span>
        </QtStatus>
      );
    case QuotationStatus.APPROVED:
      return (
        <QtStatus
          color={'#8BC34A'}
          background={'#E6F8CF'}
          borderColor={'#8BC34A'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgCheckCircleFillIcon />
          <span>ตอบรับแล้ว</span>
        </QtStatus>
      );
    case QuotationStatus.REJECT:
      return (
        <QtStatus
          color={'#D32F2F'}
          background={'#FDE8EF'}
          borderColor={'#D32F2F'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgPaperCancelIcon />
          <span>ปฏิเสธ</span>
        </QtStatus>
      );
    case QuotationStatus.EXPIRED:
      return (
        <QtStatus
          color={'#D32F2F'}
          background={'#FDE8EF'}
          borderColor={'#D32F2F'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgPaperCancelIcon />
          <span>พ้นกำหนด</span>
        </QtStatus>
      );
    case QuotationStatus.CANCEL:
      return (
        <QtStatus
          color={'#D32F2F'}
          background={'#FDE8EF'}
          borderColor={'#D32F2F'}
          radius={'50px'}
          isIcon={true}
        >
          <SvgPaperCancelIcon />
          <span>ยกเลิก</span>
        </QtStatus>
      );
    default:
  }

  return null;
};
export default QuotationRenderStatus;

const QtStatus = styled.div<{
  color: string;
  background: string;
  borderColor: string;
  radius: string;
  isIcon: boolean;
}>`
  display: flex;
  height: 32px;
  padding: 8px ${({ isIcon }) => (isIcon ? '8px' : '16px')};
  align-items: center;
  gap: 8px;
  border-radius: ${({ radius }) => radius};
  border: 1px solid ${({ borderColor }) => borderColor};
  background: ${({ background }) => background};
  color: ${({ color }) => color};
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.14px;
  svg {
    path {
      fill: ${({ color }) => color};
    }
  }
`;
