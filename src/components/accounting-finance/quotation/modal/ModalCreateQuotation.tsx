import React, { useEffect, useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  IconButton,
  TextField,
  Button,
  Radio,
  RadioGroup,
  InputAdornment,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import SearchIcon from '@mui/icons-material/Search';
import Image from 'next/image';
import styled from 'styled-components';
import apiQuotation from '@/services/order/qutation';
import { useRouter } from 'next/router';
import { isNull } from 'lodash';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
};

const ModalCreateQuotation = ({ open, handleCloseModal }: Props) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [layDataOrders, setLayDataOrders] = useState<any[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (open) {
      setSearchTerm('');
      setSelectedOrderId(null);
    }
  }, [open]);

  useEffect(() => {
    fetchLayDataOrders();
  }, [setSearchTerm]);

  const fetchLayDataOrders = async () => {
    const response = await apiQuotation.getLayDataOrders({
      search: searchTerm,
    });
    if (response.status) {
      setLayDataOrders(response.data);
    } else {
      setLayDataOrders([]);
    }
  };

  const handleSubmit = async () => {
    if (selectedOrderId && layDataOrders) {
      setIsSubmitting(true);
      const response = await apiQuotation.createQuotation({
        layDataOrderId: selectedOrderId,
      });
      if (response.status) {
        setTimeout(() => {
          router.push(
            `/accounting-finance/quotation/${response.data.id}/create`
          );
          setIsSubmitting(false);
          handleCloseModal();
        }, 1000);
      } else {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent sx={{ padding: '0 !important' }}>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header" style={{ transform: 'none' }}>
              <div className="title">เลือกรายการสั่งผลิต</div>
              <div
                className="x-close"
                onClick={() => {
                  handleCloseModal();
                }}
              >
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div
              className="form-wrap"
              style={{
                marginTop: '24px',
                rowGap: '0',
              }}
            >
              <TextField
                fullWidth
                placeholder="ค้นหารายการสั่งผลิต"
                style={{
                  padding: '24px 24px 0 24px',
                }}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <div
                style={{
                  fontSize: '14px',
                  fontWeight: '400',
                  margin: '0 24px',
                  borderBottom: '1px solid #dbe2e5',
                }}
              >
                {`${layDataOrders?.length || 0} รายการ`}
              </div>
              <div
                style={{
                  maxHeight: '300px',
                  overflowY: 'auto',
                  padding: '16px 24px 0 24px',
                  rowGap: '16px',
                }}
              >
                {layDataOrders && (
                  <RadioGroup
                    value={selectedOrderId}
                    onChange={(e) => setSelectedOrderId(Number(e.target.value))}
                  >
                    {layDataOrders.length > 0 ? (
                      layDataOrders.map((order) => (
                        <OrderItem
                          key={order.id}
                          isSelected={selectedOrderId === order.id}
                        >
                          <div className={'div1'}>
                            <div
                              style={{
                                fontSize: '14px',
                                fontWeight: '500',
                              }}
                            >
                              {order.layDataOrderNo}
                            </div>
                            <div>
                              <Radio
                                value={order.id}
                                sx={{
                                  padding: 0,
                                  '&.Mui-checked': {
                                    color: '#263238',
                                  },
                                }}
                              />
                            </div>
                          </div>
                          <div className={'div2'}>
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px',
                              }}
                            >
                              <Image
                                src={
                                  order.contact.imageUrl ||
                                  '/images/product/empty-product.svg'
                                }
                                width={32}
                                height={32}
                                alt=""
                                style={{
                                  borderRadius: '50%',
                                  objectFit: 'cover',
                                }}
                              />
                              <div>
                                <div style={{ fontSize: '14px' }}>
                                  {order.contact.name}
                                </div>
                                <div
                                  style={{
                                    fontSize: '12px',
                                    color: '#757575',
                                  }}
                                >
                                  {/* {order.contact.customerType} ·{' '} */}
                                  {order.contact.customerType}
                                  {`ลูกค้า • ${
                                    order.contact.contactType.name
                                  } ${
                                    !isNull(order.contact.creditType)
                                      ? `• เครดิต ${order.contact.creditType.day} วัน`
                                      : ''
                                  }`}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className={'div3'}>
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'flex-start',
                              }}
                            >
                              <div
                                style={{ fontSize: '12px', color: '#757575' }}
                              >
                                รายการสินค้า
                              </div>
                              <div
                                style={{ fontSize: '14px', fontWeight: '500' }}
                              >
                                {order.countLayData || 'n/a'} รายการ
                              </div>
                            </div>
                          </div>
                        </OrderItem>
                      ))
                    ) : (
                      <div
                        style={{
                          textAlign: 'center',
                          padding: '20px',
                          color: '#757575',
                        }}
                      >
                        ไม่พบรายการที่ค้นหา
                      </div>
                    )}
                  </RadioGroup>
                )}
              </div>
              <div
                className="w-full flex justify-between"
                style={{
                  boxShadow: '0px 0px 6px 0px #26323814',
                  padding: '24px',
                  gap: '24px',
                }}
              >
                <Button
                  type="button"
                  variant="outlined"
                  color="blueGrey"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  onClick={handleCloseModal}
                >
                  <span>ยกเลิก</span>
                </Button>
                <LoadingButton
                  type="button"
                  loading={isSubmitting}
                  variant="contained"
                  color="dark"
                  sx={{
                    boxShadow: 'none',
                    fontWeight: '400',
                  }}
                  fullWidth
                  disabled={!selectedOrderId}
                  onClick={handleSubmit}
                >
                  เลือก
                </LoadingButton>
              </div>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ModalCreateQuotation;

const OrderItem = styled.div<{ isSelected: boolean }>`
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  background-color: ${(props) => (props.isSelected ? '#F5F5F5' : 'white')};
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  //grid-template-rows: repeat(2, 1fr);
  margin-bottom: 16px;
  .div1 {
    grid-column: span 3 / span 3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dbe2e5;
    padding: 10px 20px;
  }

  .div2 {
    grid-column: span 2 / span 2;
    grid-row-start: 2;
    padding: 10px 20px;
    border-right: 1px solid #dbe2e5;
  }

  .div3 {
    grid-column-start: 3;
    grid-row-start: 2;
    padding: 10px 20px;
  }
`;
