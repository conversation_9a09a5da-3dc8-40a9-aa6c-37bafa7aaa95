import React, { useEffect, useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  IconButton,
  Button,
  FormControl,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import styled from 'styled-components';
import { useForm } from 'react-hook-form';
import { ArrowBack } from '@mui/icons-material';
import apiPeakProduct from '@/services/stock/peak-product';
import apiLayData from '@/services/order/layData';
import _ from 'lodash';

type Props = {
  open: boolean;
  handleCloseModal: () => void;
  onSubmit?: (selected: any) => void;
  layDataOrdersId: number;
};

const ModalSelectLD = ({
  open,
  handleCloseModal,
  onSubmit,
  layDataOrdersId,
}: Props) => {
  const [step, setStep] = useState<'Products' | 'LD'>('Products');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { register, handleSubmit, reset, setValue, watch } = useForm<{
    id: number;
    productId: number;
    layDataId: string;
    layDataNo: string;
    name: string;
    description: string;
    quantity: number;
    priceUnit: number;
    discount: number;
    price: number;
  }>();

  const [peakProducts, setPeakProducts] = useState<any[]>([]);
  const [layDataList, setLayDataList] = useState<any[]>([]);

  useEffect(() => {
    fetchPeakProducts().then();
  }, []);

  useEffect(() => {
    fetchLayData().then();
  }, [layDataOrdersId]);

  const fetchPeakProducts = async () => {
    const response = await apiPeakProduct.getPeakProducts();
    if (response.status) {
      setPeakProducts(response.data);
    } else {
      setPeakProducts([]);
    }
  };

  const fetchLayData = async () => {
    if (layDataOrdersId) {
      const response = await apiLayData.getLayDataList({
        layDataOrdersId: layDataOrdersId,
      });
      if (response.status) {
        setLayDataList(response.data);
      } else {
        setLayDataList([]);
      }
    }
  };

  useEffect(() => {
    if (open) {
      setStep('Products');
      resetForm();
    }
  }, [open]);

  const resetForm = () => {
    setIsSubmitting(false);
    reset({
      id: 0,
      productId: 0,
      layDataId: '0',
      layDataNo: '',
      name: '',
      description: '',
      quantity: 0,
      priceUnit: 0,
      price: 0,
      discount: 0,
    });
  };

  useEffect(() => {
    if (step === 'Products') {
      resetForm();
    }
  }, [step]);

  const submit = (data: any) => {
    if (data) {
      setIsSubmitting(true);
      const findPeakProduct = _.find(
        peakProducts,
        (o) => o.id === data.productId
      );
      const findLayData = _.find(
        layDataList,
        (o) => o.id === Number(data.layDataId)
      );
      if (findPeakProduct && findLayData) {
        data.layDataNo = findLayData.ldCode;
        data.name = findPeakProduct.name;
        data.layDataId = Number(data.layDataId);
        if (onSubmit) {
          onSubmit(data);
        }
        setIsSubmitting(false);
        handleCloseModal();
      } else {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        handleCloseModal();
      }}
    >
      <DialogContent sx={{ padding: '0 !important' }}>
        <form onSubmit={handleSubmit(submit)}>
          <FormModalStyle $width={640}>
            <div className="content-wrap">
              <div className="header" style={{ transform: 'none' }}>
                <IconButton
                  className={`ml-4 ${step === 'LD' ? '' : 'hidden'}`}
                  onClick={() => {
                    setStep('Products');
                    reset();
                  }}
                >
                  <ArrowBack className={'text-black text-md'} />
                </IconButton>
                <div className="title">
                  {step === 'Products' ? 'เพิ่มรายการ' : 'รายละเอียด'}
                </div>
                <div
                  className="x-close"
                  onClick={() => {
                    handleCloseModal();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div
                className="form-wrap"
                style={{
                  marginTop: '24px',
                  rowGap: '0',
                  height: step === 'Products' ? '570px' : '450px',
                }}
              >
                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    margin: '20px 40px 0 40px',
                    paddingBottom: '10px',
                    borderBottom: '1px solid #dbe2e5',
                    display: step === 'Products' ? '' : 'none',
                  }}
                >
                  {`${peakProducts.length} รายการ`}
                </div>
                <div
                  style={{
                    maxHeight: '570px',
                    overflowY: 'auto',
                    padding: '16px 40px 0 40px',
                    rowGap: '16px',
                    display: step === 'Products' ? '' : 'none',
                  }}
                >
                  <ProductListContainer>
                    {peakProducts &&
                      peakProducts.map((item: any) => {
                        return (
                          <div
                            key={item.id}
                            className={'pd-item'}
                            onClick={() => {
                              setValue('productId', item.id);
                              setStep('LD');
                            }}
                          >
                            <span>{item.name}</span>
                          </div>
                        );
                      })}
                  </ProductListContainer>
                </div>
                <div
                  style={{
                    height: '450px',
                    overflowY: 'auto',
                    padding: '40px 40px 0 40px',
                    rowGap: '40px',
                    display: step === 'LD' ? 'flex' : 'none',
                    flexDirection: 'column',
                  }}
                >
                  <div className="flex flex-col gap-4">
                    <div className="font-[600]">รายการสินค้า (อ้างอิง)</div>
                    <FormControl fullWidth>
                      <Select
                        {...register('layDataId')}
                        value={watch('layDataId')}
                        placeholder={'เลือกรายการสินค้า (อ้างอิง)'}
                        displayEmpty
                        sx={{
                          fontSize: '16px',
                          fieldset: {
                            borderWidth: '1px !important',
                          },
                        }}
                      >
                        <MenuItem disabled value="0">
                          <span style={{ color: '#999' }}>
                            เลือกรายการสินค้า (อ้างอิง)
                          </span>
                        </MenuItem>
                        {layDataList &&
                          layDataList.map((item: any) => {
                            return (
                              <MenuItem key={item.id} value={String(item.id)}>
                                {item.ldCode}
                              </MenuItem>
                            );
                          })}
                      </Select>
                    </FormControl>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="font-[600]">รายละเอียด</div>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      placeholder="รายละเอียด"
                      {...register('description')}
                    />
                  </div>
                </div>
                <div
                  className={`w-full flex justify-between ${
                    step === 'Products' ? 'hidden' : ''
                  }`}
                  style={{
                    padding: '40px',
                    gap: '24px',
                  }}
                >
                  <Button
                    type="button"
                    variant="outlined"
                    color="blueGrey"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                    onClick={handleCloseModal}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <LoadingButton
                    type="submit"
                    loading={isSubmitting}
                    variant="contained"
                    color="dark"
                    sx={{
                      boxShadow: 'none',
                      fontWeight: '400',
                    }}
                    fullWidth
                    disabled={
                      !watch('productId') ||
                      !watch('layDataId') ||
                      watch('layDataId') === '0'
                    }
                  >
                    บันทึก
                  </LoadingButton>
                </div>
              </div>
            </div>
          </FormModalStyle>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ModalSelectLD;

const ProductListContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .pd-item {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    border-radius: 8px;
    cursor: pointer;
    &:hover {
      background: #f5f7f8;
    }
  }
`;
