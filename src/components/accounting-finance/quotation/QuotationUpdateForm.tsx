import React, { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import {
  TextField,
  IconButton,
  Button,
  FormControl,
  Select,
  MenuItem,
  TableContainer,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Dialog,
} from '@mui/material';
import { Add as AddIcon, QuestionMark } from '@mui/icons-material';
import styled from 'styled-components';
import SvgDeleteIcon from '@/components/svg-icon/SvgDeleteIcon';
import { NumericFormat } from 'react-number-format';
import { numberWithCommas } from '@/utils/number';
import _, { isEmpty } from 'lodash';
import Image from 'next/image';
import moment from 'moment';
import { Calendar } from 'react-date-range';
import { th } from 'date-fns/locale';
import ModalSelectLD from '@/components/accounting-finance/quotation/modal/ModalSelelctLD';
import { useAppDispatch, useAppSelector } from '@/store';
import { quotationSelector } from '@/store/features/quotation/reducer';
import apiQuotation from '@/services/order/qutation';
import { useRouter } from 'next/router';
import { QuotationUpdateRequest } from '@/types/quotation';
import {
  confirmModalInit,
  ConfirmModalQuotationType,
} from '@/pages/accounting-finance/quotation/[quotationId]';
import { QuotationStatus } from '@/store/features/quotation/types';
import { setSnackBar } from '@/store/features/alert';
import ConfirmModal from '@/components/global/ConfirmModal';
import apiPeakProduct from '@/services/stock/peak-product';
import apiLayData from '@/services/order/layData';

interface QuotationFormData {
  quotationNo: string;
  quotationId: number;
  layDataId: string;
  layDataOrderId: string;
  dueDate: string;
  remark: string | null;
  netPrice: number;
  totalPrice: number;
  balancePayment: number;
  discount: number;
  discountRate: number;
  vatRate: number;
  vatAmount: number;
  // shippingAmount: number;
  quotationItem: {
    id: number;
    description: string; // req
    productId: number; // req
    layDataId: number; // req
    quantity: number; // req
    priceUnit: number; // req
    price: number; // req
    discount: number; // req
  }[];
}

const QuotationForm = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { quotationId } = router.query;
  const [datePickerOpen, setDatePickerOpen] = useState<boolean>(false);
  const [selectLdOpen, setSelectLdOpen] = useState<boolean>(false);
  const [layDataOrderList, setLayDataOrderList] = useState<any[]>();
  const { quotation } = useAppSelector(quotationSelector);
  const [peakProducts, setPeakProducts] = useState<any[]>([]);
  const [layDataList, setLayDataList] = useState<any[]>([]);

  const { register, control, handleSubmit, watch, getValues, setValue, reset } =
    useForm<QuotationFormData>({
      defaultValues: {},
    });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'quotationItem',
  });
  watch();

  const [confirmModal, setConfirmModal] =
    useState<ConfirmModalQuotationType>(confirmModalInit);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const onCloseConfirmModal = () => {
    setConfirmModal({ ...confirmModal, open: false });
    setTimeout(() => {
      setConfirmModal({ ...confirmModalInit });
      setConfirmLoading(false);
    }, 500);
  };
  useEffect(() => {
    if (quotation && quotation.detail) {
      reset({
        quotationId: Number(quotationId),
        quotationNo: quotation.detail.quotationNo,
        layDataId: quotation.detail.layData?.layDataId || '',
        layDataOrderId: String(quotation.detail.layDataOrder.layDataOrderId),
        dueDate: moment(quotation.detail.dueDate).format('MM/DD/YYYY'),
        remark: quotation.detail.remark,
        netPrice: quotation.detail.netPrice,
        totalPrice: quotation.detail.totalPrice,
        balancePayment: quotation.detail.balancePayment,
        discount: quotation.detail.discount,
        discountRate: quotation.detail.discountRate,
        vatRate: quotation.detail.vatRate,
        vatAmount: quotation.detail.vatAmount,
        // shippingAmount: 200,
        quotationItem: quotation.detail.quotationItem.map((item: any) => {
          return {
            id: item.id,
            name: item.name,
            description: item.description,
            productId: item.peakProduct.id,
            layDataId: item.layData.id,
            layDataNo: item.layData.ldCode,
            quantity: item.quantity,
            priceUnit: item.pricePerUnit,
            price: item.price,
            discount: item.discount || 0,
          };
        }),
      });
    }
  }, [quotation, reset]);

  const getListLayData = async () => {
    const response = await apiQuotation.getLayDataOrders({ search: '' });
    if (!response.isError) {
      setLayDataOrderList(response.data);
    } else {
      setLayDataOrderList([]);
    }
  };

  useEffect(() => {
    if (quotation.detail?.layDataOrder?.layDataOrderId) {
      getListLayData().then();
    }
  }, [quotation]);

  useEffect(() => {
    handleSummary();
  }, [watch('quotationItem')]);

  const handleSummary = (index?: any) => {
    const items: any[] = watch('quotationItem');
    if (items.length > 0) {
      if (index >= 0) {
        const quantity = convert2numeric(
          watch(`quotationItem.${index}.quantity`)
        );
        const priceUnit = convert2numeric(
          watch(`quotationItem.${index}.priceUnit`)
        );
        const productPrice = quantity * priceUnit;
        const discount = convert2numeric(
          watch(`quotationItem.${index}.discount`)
        );
        const price = productPrice - discount * quantity;
        setValue(`quotationItem.${index}.price`, price);
      }
      const netPrice = _.sumBy(items, (item) => item.price);
      setValue('netPrice', netPrice);
    } else {
      setValue('netPrice', 0);
      setValue('discountRate', 0);
      setValue('discount', 0);
    }
  };

  useEffect(() => {
    if (watch('netPrice') > 0) {
      setValue('vatAmount', watch('netPrice') * 0.07);
      setValue(
        'totalPrice',
        watch('netPrice') - watch('discount') + watch('vatAmount')
      );
    } else {
      setValue('vatAmount', 0);
      setValue('totalPrice', 0);
    }
  }, [watch('netPrice'), watch('discount'), watch('discountRate')]);

  useEffect(() => {
    if (watch('netPrice') > 0) {
      setValue('discount', watch('netPrice') * (watch('discountRate') / 100));
    } else {
      setValue('discount', 0);
    }
  }, [watch('netPrice')]);

  const onSaveDraft = async () => {
    const data: QuotationFormData = watch();
    const draftPayload: QuotationUpdateRequest = {
      quotationId: data.quotationId,
      layDataOrderId: Number(data.layDataOrderId),
      dueDate: moment(data.dueDate).format('YYYY-MM-DD'),
      remark: data.remark,
      netPrice: data.netPrice,
      totalPrice: data.totalPrice,
      balancePayment: data.totalPrice,
      discount: data.discount,
      discountRate: Number(data.discountRate),
      vatRate: data.vatRate,
      vatAmount: data.vatAmount,
      quotationItem: data.quotationItem.map((item: any) => {
        return {
          productId: item.productId,
          layDataId: item.layDataId,
          description: item.description,
          quantity: convert2numeric(item.quantity),
          priceUnit: convert2numeric(item.priceUnit),
          price: convert2numeric(item.price),
          discount: convert2numeric(item.discount),
        };
      }),
    };
    setConfirmModal({
      ...confirmModal,
      open: true,
      title: 'ยืนยันที่จะบันทึกแบบร่าง',
      description:
        `คุณได้ตรวจสอบ "${quotation.quotationNo}" เรียบร้อยแล้ว\n` +
        `จะทำการบันทึกแบบร่างและไม่ทำการเปลี่ยนสถานะ`,
      iconElement: () => <QuestionMark />,
      confirmLabel: 'ยืนยัน',
      confirmAction: async () => {
        const response = await apiQuotation.updateQuotation(draftPayload);
        if (response.status) {
          dispatch(
            setSnackBar({
              status: true,
              text: response.message || 'บันทึกแบบร่างสำเร็จ',
              severity: 'success',
            })
          );
          await router.push(`/accounting-finance/quotation`);
          onCloseConfirmModal();
        }
      },
    });
  };

  const onSubmit = async (data: QuotationFormData) => {
    const payload = {
      quotationId: data.quotationId,
      layDataOrderId: Number(data.layDataOrderId),
      dueDate: moment(data.dueDate).format('YYYY-MM-DD'),
      remark: data.remark,
      netPrice: data.netPrice,
      totalPrice: data.totalPrice,
      balancePayment: data.totalPrice,
      discount: data.discount,
      discountRate: Number(data.discountRate),
      vatRate: data.vatRate,
      vatAmount: data.vatAmount,
      quotationItem: data.quotationItem.map((item: any) => {
        return {
          productId: item.productId,
          layDataId: item.layDataId,
          description: item.description,
          quantity: convert2numeric(item.quantity),
          priceUnit: convert2numeric(item.priceUnit),
          price: convert2numeric(item.price),
          discount: convert2numeric(item.discount),
        };
      }),
    };
    setConfirmModal({
      ...confirmModal,
      open: true,
      title: 'ยืนยันที่จะบันทึกและขออนุมัติใบเสนอราคา',
      description:
        `คุณได้ตรวจสอบ "${quotation.quotationNo}" เรียบร้อยแล้ว\n` +
        `จะทำการบันทึกและทำการเปลี่ยนสถานะเป็น "รออนุมัติ" ในขั้นตอนต่อไป`,
      iconElement: () => <QuestionMark />,
      confirmLabel: 'ส่งคำขอ',
      confirmAction: async () => {
        const response = await apiQuotation.updateQuotation(payload);
        if (response.status) {
          if (quotation.status === QuotationStatus.DRAFT) {
            const res = await apiQuotation.updateQuotationStatus({
              paymentQuotationId: response.data.id,
            });
            if (res.status) {
              dispatch(
                setSnackBar({
                  status: true,
                  text:
                    response.message || 'บันทึกและขออนุมัติใบเสนอราคาสำเร็จ',
                  severity: 'success',
                })
              );
              await router.push(
                `/accounting-finance/quotation/${response.data.id}`
              );
              onCloseConfirmModal();
            }
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text:
                  response.message ||
                  'มีข้อผิดผลาดในการบันทึกและขออนุมัติใบเสนอราคา',
                severity: 'error',
              })
            );
            await router.push(
              `/accounting-finance/quotation/${response.data.id}`
            );
            onCloseConfirmModal();
          }
        }
      },
    });
  };

  const fetchLayData = async () => {
    const response = await apiLayData.getLayDataList();
    if (response.status) {
      setLayDataList(response.data);
    } else {
      setLayDataList([]);
    }
  };

  const convert2numeric = (value: any) => {
    return Number(String(value).replace(/,/g, ''));
  };

  useEffect(() => {
    fetchLayData().then();
    fetchPeakProducts().then();
  }, []);

  const fetchPeakProducts = async () => {
    const response = await apiPeakProduct.getPeakProducts();
    if (response.status) {
      setPeakProducts(response.data);
    } else {
      setPeakProducts([]);
    }
  };

  const getProductInfo = (field: any) => {
    const layData = layDataList.find((ld: any) => ld.id === field.layDataId);
    if (!layData) {
      return field.description || '-';
    }
    return `${layData.ldCode} • ${field.description}`;
  };

  return (
    <QuotationFormStyled>
      <ConfirmModal
        open={confirmModal.open}
        handleCloseModal={onCloseConfirmModal}
        loading={confirmLoading}
        title={confirmModal.title}
        description={confirmModal.description}
        iconElement={confirmModal.iconElement}
        confirmLabel={confirmModal.confirmLabel}
        confirmAction={confirmModal.confirmAction}
      />
      <ModalSelectLD
        open={selectLdOpen}
        handleCloseModal={() => setSelectLdOpen(false)}
        layDataOrdersId={Number(watch('layDataOrderId'))}
        onSubmit={(values) => {
          append({
            id: 0,
            productId: values.productId,
            layDataId: values.layDataId,
            description: values.description || '',
            quantity: values.quantity,
            priceUnit: values.priceUnit,
            price: values.price,
            discount: values.discount,
          });
        }}
      />
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="form-section qt-info">
          <div className="section-title">ข้อมูลเสนอราคา</div>
          <div className={'qt-info-content'}>
            <div className="content-item bd">
              <div className={'form-label'}>รายการสั่งผลิต (อ้างอิง)</div>
              <Controller
                control={control}
                name="layDataOrderId"
                render={({ field }) => (
                  <FormControl fullWidth>
                    <Select
                      {...field}
                      displayEmpty
                      value={field.value || ''}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        setValue('quotationItem', []);
                      }}
                    >
                      <MenuItem disabled value="">
                        <div className="text-[#78909C]">เลือก</div>
                      </MenuItem>
                      {layDataOrderList?.map((item: any) => (
                        <MenuItem key={item.id} value={String(item.id)}>
                          {item.layDataOrderNo}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </div>
            <div className="content-item">
              <div className={'form-label'}>วันที่ครบกำหนด</div>
              <Dialog
                open={datePickerOpen}
                onClose={() => setDatePickerOpen(false)}
              >
                <CalendarStyle>
                  <Calendar
                    date={
                      new Date(
                        moment(getValues('dueDate')).format('MM/DD/YYYY')
                      )
                    }
                    onChange={(date) => {
                      setValue('dueDate', String(date));
                      setDatePickerOpen(false);
                    }}
                    locale={th}
                  />
                </CalendarStyle>
              </Dialog>
              <CustomTextField>
                <div
                  className={`action-wrap`}
                  onClick={() => {
                    if (quotation?.detail?.isDefault === false) {
                      setDatePickerOpen(true);
                    }
                  }}
                />
                <TextField
                  fullWidth
                  value={moment(getValues('dueDate')).format('DD/MM/YYYY')}
                  disabled={quotation?.detail?.isDefault}
                  InputProps={{
                    endAdornment: (
                      <span className={'flex items-center'}>
                        <Image
                          src="/icons/icon-calendar.svg"
                          width={24}
                          height={24}
                          alt=""
                        />
                      </span>
                    ),
                  }}
                />
              </CustomTextField>
            </div>
          </div>
        </div>
        <div className="form-section customer-info">
          <div className="section-title">ข้อมูลลูกค้า</div>
          <div className={'customer-info-content'}>
            <div className={'customer-title'}>
              <div className={'avatar'}>
                <Image
                  src={
                    quotation?.detail?.customerData?.imageUrl ||
                    '/images/product/empty-product.svg'
                  }
                  width={24}
                  height={24}
                  alt=""
                  style={{
                    borderRadius: '50%',
                    objectFit: 'cover',
                  }}
                />
              </div>
              <div className={'name'}>
                {quotation?.detail?.customerData?.name}
              </div>
            </div>
            <div className={'customer-detail'}>
              <div className={'text'}>
                {`${quotation?.detail?.customerData?.contactType.name} • เลขประจำตัวผู้เสียภาษี • ${quotation?.detail?.customerData?.taxNumber}`}
              </div>
              <div className={'text'}>
                {`โทร ${quotation?.detail?.customerData?.phoneNumber} • ${quotation?.detail?.customerData?.email}`}
              </div>
              <div className={'text'}>
                {`${quotation?.detail?.customerData?.taxAddress || '-'} ${
                  quotation?.detail?.customerData?.subDistrict?.name || '-'
                } ${quotation?.detail?.customerData?.district?.name || '-'} ${
                  quotation?.detail?.customerData?.province?.name || '-'
                } ${quotation?.detail?.customerData?.zipcode || '-'}`}
              </div>
            </div>
          </div>
        </div>
        <div className="form-section ld-list">
          <div className={'flex items-center justify-between'}>
            <div className="section-title">รายการเสนอราคา</div>
            <Button
              type="button"
              variant="contained"
              color="dark"
              startIcon={<AddIcon />}
              disabled={
                quotation?.detail?.isDefault || isEmpty(watch('layDataOrderId'))
              }
              onClick={() => {
                if (quotation?.detail?.isDefault === false) {
                  setSelectLdOpen(true);
                }
              }}
            >
              เพิ่มรายการ
            </Button>
          </div>
          <TableWrapper>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                '& .MuiTable-root': {
                  borderCollapse: 'separate',
                  borderSpacing: '0px',
                },
                '& .MuiTableHead-root .MuiTableRow-root': {
                  height: '32px',
                },
                '& .MuiTableHead-root .MuiTableCell-root': {
                  padding: '4px 16px',
                  fontSize: '12px',
                },
                '& .MuiTableBody-root .MuiTableRow-root': {
                  height: '72px',
                },
              }}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell align="center" sx={{ width: '50px' }}>
                      #
                    </TableCell>
                    <TableCell>รายการ</TableCell>
                    <TableCell>รายการสั่งผลิต (อ้างอิง)</TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      จำนวน
                    </TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      ราคา/หน่วย
                    </TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      ราคาสินค้า (บาท)
                    </TableCell>
                    <TableCell align="center" sx={{ width: '180px' }}>
                      ส่วนลด/หน่วย
                    </TableCell>
                    <TableCell align="right" sx={{ width: '120px' }}>
                      ราคารวม
                    </TableCell>
                    <TableCell align="center" sx={{ width: '100px' }}>
                      จัดการ
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fields.length > 0 &&
                    fields.map((field, index) => (
                      <TableRow key={field.id}>
                        <TableCell align="center">{index + 1}</TableCell>
                        <TableCell>
                          {
                            peakProducts.find((p) => p.id === field.productId)
                              ?.name
                          }
                        </TableCell>
                        <TableCell>{getProductInfo(field)}</TableCell>
                        <TableCell align="center">
                          <Controller
                            name={`quotationItem.${index}.quantity`}
                            control={control}
                            rules={{
                              required: 'โปรดระบุจำนวนสินค้า',
                              validate: (value) => {
                                const numericValue = convert2numeric(value);
                                if (numericValue <= 0) {
                                  return 'ต้องมากกว่า 0';
                                }
                                return true;
                              },
                            }}
                            render={({ field, fieldState: { error } }) => (
                              <NumericFormat
                                customInput={TextField}
                                thousandSeparator=","
                                decimalScale={0}
                                fixedDecimalScale
                                allowNegative={false}
                                disabled={quotation?.detail?.isDefault}
                                {...field}
                                onValueChange={(values) => {
                                  const val = values.floatValue ?? 0;
                                  field.onChange(val);
                                  handleSummary(index);
                                }}
                                helperText={error ? error.message : ''}
                                error={Boolean(error)}
                                FormHelperTextProps={{
                                  style: {
                                    fontSize: '10px',
                                    // position: 'absolute',
                                    // bottom: 0,
                                    // right: 0,
                                  },
                                }}
                              />
                            )}
                          />
                          {/* <NumericFormat */}
                          {/*  customInput={TextField} */}
                          {/*  thousandSeparator="," */}
                          {/*  decimalScale={0} */}
                          {/*  fixedDecimalScale={true} */}
                          {/*  allowNegative={false} */}
                          {/*  disabled={quotation?.detail?.isDefault} */}
                          {/*  {...register(`quotationItem.${index}.quantity`)} */}
                          {/*  onValueChange={(values) => { */}
                          {/*    setValue( */}
                          {/*      `quotationItem.${index}.quantity`, */}
                          {/*      values.floatValue ?? 0 */}
                          {/*    ); */}
                          {/*    handleSummary(index); */}
                          {/*  }} */}
                          {/*  defaultValue={getValues( */}
                          {/*    `quotationItem.${index}.quantity` */}
                          {/*  )} */}
                          {/* /> */}
                        </TableCell>
                        <TableCell align="right">
                          <Controller
                            name={`quotationItem.${index}.priceUnit`}
                            control={control}
                            rules={{
                              required: 'โปรดระบุราคาสินค้า',
                              validate: (value) => {
                                const numericValue = convert2numeric(value);
                                if (numericValue <= 0) {
                                  return 'ต้องมากกว่า 0';
                                }
                                return true;
                              },
                            }}
                            render={({ field, fieldState: { error } }) => (
                              <NumericFormat
                                customInput={TextField}
                                thousandSeparator=","
                                decimalSeparator="."
                                decimalScale={2}
                                fixedDecimalScale={false}
                                allowNegative={false}
                                disabled={quotation?.detail?.isDefault}
                                {...field}
                                onValueChange={(values) => {
                                  const val = values.floatValue ?? 0;
                                  field.onChange(val);
                                  handleSummary(index);
                                }}
                                helperText={error ? error.message : ''}
                                error={Boolean(error)}
                                FormHelperTextProps={{
                                  style: {
                                    fontSize: '10px',
                                    // position: 'absolute',
                                    // bottom: 0,
                                    // right: 0,
                                  },
                                }}
                              />
                            )}
                          />
                          {/* <NumericFormat */}
                          {/*  customInput={TextField} */}
                          {/*  thousandSeparator="," */}
                          {/*  decimalSeparator="." */}
                          {/*  decimalScale={2} */}
                          {/*  fixedDecimalScale={false} */}
                          {/*  allowNegative={false} */}
                          {/*  disabled={quotation?.detail?.isDefault} */}
                          {/*  {...register(`quotationItem.${index}.priceUnit`)} */}
                          {/*  onValueChange={(values) => { */}
                          {/*    setValue( */}
                          {/*      `quotationItem.${index}.priceUnit`, */}
                          {/*      values.floatValue ?? 0 */}
                          {/*    ); */}
                          {/*    handleSummary(index); */}
                          {/*  }} */}
                          {/*  defaultValue={getValues( */}
                          {/*    `quotationItem.${index}.priceUnit` */}
                          {/*  )} */}
                          {/* /> */}
                        </TableCell>
                        <TableCell align="right">
                          <NumericFormat
                            customInput={TextField}
                            thousandSeparator=","
                            decimalSeparator="."
                            decimalScale={2}
                            fixedDecimalScale={false}
                            allowNegative={false}
                            disabled
                            value={
                              convert2numeric(
                                watch(`quotationItem.${index}.quantity`)
                              ) *
                              convert2numeric(
                                watch(`quotationItem.${index}.priceUnit`)
                              )
                            }
                          />
                        </TableCell>
                        <TableCell align="right">
                          {/* <NumericFormat */}
                          {/*  customInput={TextField} */}
                          {/*  thousandSeparator="," */}
                          {/*  decimalSeparator="." */}
                          {/*  decimalScale={2} */}
                          {/*  fixedDecimalScale={false} */}
                          {/*  allowNegative={false} */}
                          {/*  disabled={quotation?.detail?.isDefault} */}
                          {/*  {...register(`quotationItem.${index}.discount`)} */}
                          {/*  defaultValue={getValues( */}
                          {/*    `quotationItem.${index}.discount` */}
                          {/*  )} */}
                          {/*  onValueChange={(values) => { */}
                          {/*    setValue( */}
                          {/*      `quotationItem.${index}.discount`, */}
                          {/*      values.floatValue ?? 0 */}
                          {/*    ); */}
                          {/*    handleSummary(index); */}
                          {/*  }} */}
                          {/* /> */}
                          <Controller
                            name={`quotationItem.${index}.discount`}
                            control={control}
                            rules={{
                              required: 'โปรดระบุส่วนลดสินค้า',
                              validate: (value) => {
                                const numericValue = convert2numeric(value);
                                const totalPrice = convert2numeric(
                                  watch(`quotationItem.${index}.price`)
                                );
                                if (numericValue > totalPrice) {
                                  return 'ต้องน้อยกว่าราคารวม';
                                }
                                return true;
                              },
                            }}
                            render={({ field, fieldState: { error } }) => (
                              <NumericFormat
                                customInput={TextField}
                                thousandSeparator=","
                                decimalSeparator="."
                                decimalScale={2}
                                fixedDecimalScale={false}
                                allowNegative={false}
                                disabled={quotation?.detail?.isDefault}
                                {...field}
                                onValueChange={(values) => {
                                  const val = values.floatValue ?? 0;
                                  field.onChange(val);
                                  handleSummary(index);
                                }}
                                helperText={error ? error.message : ''}
                                error={Boolean(error)}
                                FormHelperTextProps={{
                                  style: {
                                    fontSize: '10px',
                                    // position: 'absolute',
                                    // bottom: 0,
                                    // right: 0,
                                  },
                                }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <div>
                            {numberWithCommas(
                              watch(`quotationItem.${index}.price`),
                              2
                            )}
                          </div>
                        </TableCell>
                        <TableCell align="center">
                          <CustomDeleteIcon
                            sx={{
                              borderRadius: '6px',
                              width: '40px',
                              height: '40px',
                              border: '1px solid #DBE2E5',
                            }}
                            onClick={() => {
                              if (quotation?.detail?.isDefault === false) {
                                remove(index);
                              }
                            }}
                            disabled={quotation?.detail?.isDefault}
                          >
                            <SvgDeleteIcon />
                          </CustomDeleteIcon>
                        </TableCell>
                      </TableRow>
                    ))}
                  {fields.length < 1 && (
                    <TableRow>
                      <TableCell
                        align="center"
                        colSpan={9}
                        sx={{ color: '#dbe2e5' }}
                      >
                        ไม่มีรายการเสนอราคา
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TableWrapper>
        </div>

        <div className="form-section qt-remark">
          <div className="section-title">หมายเหตุ</div>
          <TextField
            fullWidth
            multiline
            rows={10}
            placeholder="หมายเหตุ"
            disabled={quotation?.detail?.isDefault}
            {...register('remark')}
          />
        </div>
        <div className="form-section qt-summary">
          <div className="section-title">ยอดแจ้งชำระเงิน</div>
          <div className="summary-content">
            <div className="summary-row">
              <span className={'label'}>รวมเป็นเงิน</span>
              <span className={'result'}>{`${numberWithCommas(
                getValues(`netPrice`),
                2
              )} บาท`}</span>
            </div>
            <div className="summary-row">
              <span className={'label'}>ส่วนลด</span>
              <div className="flex items-center gap-2">
                <Controller
                  name="discountRate"
                  control={control}
                  rules={{
                    required: 'กรอกส่วนลด (%)',
                    validate: (v) => (v > 100 ? 'ต้องไม่เกิน 100' : true), //  ตรวจไม่เกิน 100%
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <NumericFormat
                      customInput={TextField}
                      size="small"
                      sx={{ width: '120px' }}
                      thousandSeparator="," // ใส่คอมมาให้อัตโนมัติ
                      decimalSeparator="."
                      decimalScale={2} //  จำกัด 2 ตำแหน่ง
                      fixedDecimalScale={false} // พิมพ์ได้ 0-2 หลัก
                      allowNegative={false}
                      suffix="%" // แสดง % ต่อท้าย
                      disabled={quotation?.detail?.isDefault}
                      {...field}
                      onValueChange={({ floatValue }) => {
                        // ⤵︎ จำกัดไม่เกิน 100 และปัดเศษ 2 ทศนิยม
                        const rate = Math.min(
                          Number((floatValue ?? 0).toFixed(2)),
                          100
                        );
                        field.onChange(rate); // อัปเดตฟอร์ม
                        setValue('discount', (watch('netPrice') * rate) / 100); // คำนวณส่วนลด (บาท)
                      }}
                      helperText={error?.message}
                      error={Boolean(error)}
                      FormHelperTextProps={{ style: { fontSize: 10 } }}
                    />
                  )}
                />

                <CustomNumericFormat endAdornment={'บาท'}>
                  <NumericFormat
                    customInput={TextField}
                    thousandSeparator=","
                    decimalSeparator="."
                    decimalScale={2}
                    fixedDecimalScale={false}
                    allowNegative={false}
                    disabled={quotation?.detail?.isDefault}
                    {...register(`discount`)}
                    value={numberWithCommas(Number(watch(`discount`)), 2)}
                    onValueChange={(values) => {
                      if ((values.floatValue ?? 0) >= watch('netPrice')) {
                        setValue(`discount`, watch('netPrice'));
                        setValue('discountRate', 100);
                      } else {
                        setValue(`discount`, values.floatValue ?? 0);
                        const cal = 100 * (values.floatValue ?? 0);
                        setValue('discountRate', cal / watch('netPrice'));
                      }
                    }}
                    className={'numeric'}
                    sx={{ width: '120px' }}
                  />
                </CustomNumericFormat>
              </div>
            </div>
            <div className="summary-row">
              <span className={'label'}>{`ภาษีมูลค่าเพิ่ม ${getValues(
                'vatRate'
              )}%`}</span>
              <span className={'result'}>{`${numberWithCommas(
                getValues(`vatAmount`),
                2
              )} บาท`}</span>
            </div>
            {/* <div className="summary-row"> */}
            {/*  <span className={'label'}>จัดส่งด้วยวิธีการส่งพิเศษ</span> */}
            {/*  <div className="flex items-center gap-2"> */}
            {/*    <CustomNumericFormat endAdornment={'บาท'}> */}
            {/*      <NumericFormat */}
            {/*        customInput={TextField} */}
            {/*        thousandSeparator="," */}
            {/*        decimalSeparator="." */}
            {/*        decimalScale={2} */}
            {/*        fixedDecimalScale={false} */}
            {/*        allowNegative={false} */}
            {/*        className={'numeric'} */}
            {/*        sx={{ width: '250px' }} */}
            {/*        {...register(`shippingAmount`)} */}
            {/*        defaultValue={getValues(`shippingAmount`)} */}
            {/*        onValueChange={(values) => { */}
            {/*          setValue(`shippingAmount`, values.floatValue ?? 0); */}
            {/*        }} */}
            {/*      /> */}
            {/*    </CustomNumericFormat> */}
            {/*  </div> */}
            {/* </div> */}
            <div className="summary-row final">
              <span className={'label bold'}>มูลค่ารวมสุทธิ</span>
              <span className={'result large'}>{`${numberWithCommas(
                getValues(`totalPrice`),
                2
              )} บาท`}</span>
            </div>
            <div className="actions">
              <Button
                fullWidth
                variant="outlined"
                disabled={quotation?.detail?.isDefault}
                onClick={() => onSaveDraft()}
              >
                บันทึกแบบร่าง
              </Button>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                disabled={
                  quotation?.detail?.isDefault ||
                  watch('quotationItem')?.length < 1
                }
              >
                บันทึก และขออนุมัติใบเสนอราคา
              </Button>
            </div>
          </div>
        </div>
      </form>
    </QuotationFormStyled>
  );
};

export default QuotationForm;

const CustomNumericFormat = ({
  endAdornment,
  children,
}: {
  endAdornment: string;
  children: React.ReactNode;
}) => {
  return (
    <ContainerNumeric>
      {children}
      <div className={'end'}>{endAdornment}</div>
    </ContainerNumeric>
  );
};

const CustomTextField = styled.div`
  position: relative;
  .action-wrap {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    cursor: pointer;
  }
`;

const CalendarStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrSelected {
    background: #263238 !important;
  }
`;

const ContainerNumeric = styled.div`
  position: relative;
  input {
    width: calc(100% - 50px) !important;
  }
  .end {
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

const CustomDeleteIcon = styled(IconButton)`
  svg {
    path {
      fill: #000000;
    }
  }
`;

const QuotationFormStyled = styled.div`
  padding: 24px;
  form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    //grid-template-rows: repeat(3, 1fr);
    gap: 0 38px;
  }
  .form-section {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }
  .qt-info {
    .qt-info-content {
      border: 1px solid #dbe2e5;
      border-radius: 16px;
      .content-item {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
        padding: 0 24px;
        height: 82.5px;
        .form-label {
          color: #263238;
          font-size: 14px;
          font-weight: 600;
        }
      }
      .content-item.bd {
        border-bottom: 1px solid #dbe2e5;
      }
    }
  }
  .customer-info {
    .customer-info-content {
      display: flex;
      width: 100%;
      height: 165px;
      padding: 24px;
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      border-radius: 16px;
      background: #f5f7f8;
      .customer-title {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        background: #fff;
        display: flex;
        align-items: center;
        column-gap: 8px;
        padding: 16px;
        .avatar {
          display: flex;
          align-items: center;
        }
        .name {
          font-size: 14px;
          font-weight: 400;
        }
      }
      .customer-detail {
        .text {
          font-size: 12px;
          font-weight: 400;
          letter-spacing: 0.12px;
        }
      }
    }
  }
  .ld-list {
    grid-column: span 2 / span 2;
    .items-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 24px;
      th,
      td {
        padding: 12px;
        border: 1px solid #e0e0e0;
      }

      th {
        background-color: #f5f5f5;
        text-align: left;
      }
    }
  }
  .qt-remark {
    grid-row-start: 3;
  }
  .qt-summary {
    grid-row-start: 3;
    display: flex;
    justify-content: flex-end;
    .summary-content {
      width: 100%;
      .actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;
        button {
          font-weight: 600;
          font-size: 14px;
        }
      }
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      padding: 24px 0px;
      align-items: center;
      align-self: stretch;
      border-bottom: 1px solid #263238;
      height: 70px;
      .label {
        color: #263238;
        font-size: 14px;
        font-weight: 400;
      }
      .result {
        color: #263238;
        font-size: 16px;
        font-weight: 600;
      }
      .bold {
        font-weight: 600;
      }
      .large {
        font-size: 28px;
      }
    }
    .summary-row.final {
      border-bottom: none;
      background: #f5f7f8;
      padding: 0 24px;
    }
  }
  .section-title {
    font-size: 18px;
    font-weight: 600;
  }
`;

const TableWrapper = styled.div`
  .MuiPaper-root {
    box-shadow: none !important;
    border: 1px solid #dbe2e5;
    border-radius: 16px !important;
  }
  .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root {
    border-bottom: none;
  }
`;
