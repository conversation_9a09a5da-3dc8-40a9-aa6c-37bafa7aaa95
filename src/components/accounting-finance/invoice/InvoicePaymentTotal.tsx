import React from 'react';
import { Button, InputAdornment, TextField } from '@mui/material';
import styled from 'styled-components';
import ModalConfirmCreateInvoice from '@/components/accounting-finance/invoice/modal/MiodalConfirmCreateInvoice';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector, setChangeInvoice } from '@/store/features/invoice';
import { isNull } from 'lodash';
import { useRouter } from 'next/router';
import { numberWithCommas } from '@/utils/number';

export const InvoicePaymentTotalStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  flex: 2;
  gap: 40px;
  .invoice-description {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
  .invoice-total-1 {
    width: 100%;
    display: flex;
    flex-direction: column;
    .total-section {
      width: 100%;
      display: flex;
      flex-direction: column;
      .total-section-item {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        > p {
          margin: 0;
        }
        .input-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;
          gap: 8px;
        }
      }
      .item-total-vat {
        padding: 24px 0px;
      }
      .item-discount-shipping {
        padding: 16px 0;
      }
      .item-totals {
        padding: 26px 24px;
        background: #f5f7f8;
        margin-bottom: 40px;
        border-radius: 8px;
        p {
          margin: 0;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
        h3 {
          margin: 0;
          font-size: 28px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          letter-spacing: 0.28px;
        }
      }
      .border-bottom {
        border-bottom: 1px solid #263238;
      }
    }
    .total-section-button-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 40px;
    }
  }
  .invoice-total-2 {
    width: 100%;
    display: flex;
    flex-direction: column;
    .total-section {
      width: 100%;
      display: flex;
      flex-direction: column;
      .total-section-item {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        > p {
          margin: 0;
        }
        .input-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;
          gap: 8px;
        }
      }
      .item-total-vat {
        padding-bottom: 24px;
      }
      .item-total-vat-2 {
        padding-top: 24px;
      }
      .item-discount-shipping {
        padding: 24px 0;
      }
      .item-totals {
        margin-top: 14px;
        padding: 26px 0;
        border-top: 2px solid #263238;
        border-bottom: 2px solid #263238;
        p {
          margin: 0;
          font-size: 22px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
        h3 {
          margin: 0;
          font-size: 22px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
      }
      .border-gray {
        border-bottom: 1px solid #dbe2e5;
      }
    }
    .total-section-button-group {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 40px;
    }
  }
  .card-title > p {
    margin: 0 0 24px 0;
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.22px;
  }
`;

const InvoicePaymentTotal = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { invoice } = useAppSelector(invoiceSelector);

  const handleChangeRemark = (e: any) => {
    dispatch(
      setChangeInvoice({
        ...invoice,
        remark: e.target.value,
      })
    );
  };

  const handleChangeDiscount = (type: string, e: any) => {
    if (type === 'discountRate') {
      const discountRate: number =
        Number(e.target.value) < 0 ? 0 : Number(e.target.value);
      const calculateDiscount: number = Number(
        (invoice.netPrice * (discountRate / 100)).toFixed(2)
      );
      const calculateVat: number =
        invoice.netPrice > 0
          ? Number(
              (
                (invoice.netPrice - calculateDiscount) *
                (invoice.vatRate / 100)
              ).toFixed(2)
            )
          : 0;

      const calculateTotalPrice: number =
        calculateVat > 0
          ? Number(
              (invoice.netPrice - calculateDiscount + calculateVat).toFixed(2)
            )
          : 0;

      dispatch(
        setChangeInvoice({
          ...invoice,
          totalPrice: calculateTotalPrice,
          discountRate: discountRate,
          discount: calculateDiscount,
          vatAmount: calculateVat,
        })
      );
    }

    if (type === 'discountBaht') {
      const discountBaht: number =
        Number(e.target.value) < 0 ? 0 : Number(e.target.value);
      const calculateVat: number =
        invoice.netPrice > 0
          ? Number(
              (
                (invoice.netPrice - discountBaht) *
                (invoice.vatRate / 100)
              ).toFixed(2)
            )
          : 0;

      const calculateTotalPrice: number =
        calculateVat > 0
          ? Number((invoice.netPrice - discountBaht + calculateVat).toFixed(2))
          : 0;

      dispatch(
        setChangeInvoice({
          ...invoice,
          totalPrice: calculateTotalPrice,
          discount: discountBaht,
          vatAmount: calculateVat,
        })
      );
    }
  };

  return (
    <>
      {!isNull(invoice) && (
        <InvoicePaymentTotalStyled>
          <div className="invoice-description">
            {invoice.invoicesStatus.id === 1 && (
              <>
                <div className="card-title">
                  <p>หมายเหตุ</p>
                </div>
                <TextField
                  multiline
                  rows={9}
                  placeholder="หมายเหตุ"
                  fullWidth
                  variant="outlined"
                  value={invoice.remark}
                  disabled={!isNull(invoice.paymentQuotationNo)}
                  onChange={(e) => handleChangeRemark(e)}
                />
              </>
            )}
          </div>
          <div
            className={`${
              invoice.invoicesStatus.id === 1
                ? 'invoice-total-1'
                : 'invoice-total-2'
            }`}
          >
            {invoice.invoicesStatus.id === 1 && (
              <div className="card-title">
                <p>ยอดชำระเงิน</p>
              </div>
            )}
            <div className="total-section">
              <div className="total-section-item border-bottom border-gray item-total-vat">
                <p>
                  {invoice.invoicesStatus.id === 1 ? 'รวมเป็นเงิน' : 'ยอดรวม'}
                </p>
                <p>{numberWithCommas(invoice.netPrice, 2)} บาท</p>
              </div>
              <div className="total-section-item border-bottom item-discount-shipping">
                <p>ส่วนลด</p>
                <div className="input-group">
                  {invoice.invoicesStatus.id === 1 &&
                  isNull(invoice.paymentQuotationNo) ? (
                    <>
                      <TextField
                        value={invoice.discountRate}
                        size="small"
                        type="number"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">%</InputAdornment>
                          ),
                        }}
                        sx={{ minWidth: 100, maxWidth: 150 }}
                        onChange={(e) =>
                          handleChangeDiscount('discountRate', e)
                        }
                      />
                      <TextField
                        size="small"
                        type="number"
                        value={invoice.discount}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">บาท</InputAdornment>
                          ),
                        }}
                        sx={{ minWidth: 100, maxWidth: 150 }}
                        onChange={(e) =>
                          handleChangeDiscount('discountBaht', e)
                        }
                      />
                    </>
                  ) : (
                    `${numberWithCommas(invoice.discount, 2)} บาท`
                  )}
                </div>
              </div>
              <div className="total-section-item border-bottom item-total-vat total-vat-2">
                <p>ภาษีมูลค่าเพิ่ม {invoice.vatRate}%</p>
                <p>{numberWithCommas(invoice.vatAmount, 2)} บาท</p>
              </div>
              <div className="total-section-item item-totals">
                <p>มูลค่ารวมสุทธิ</p>
                <h3>{numberWithCommas(invoice.totalPrice, 2)} บาท</h3>
              </div>
            </div>
            {invoice.invoicesStatus.id === 1 && (
              <div className="total-section-button-group">
                <Button
                  type="button"
                  variant="outlined"
                  color="dark"
                  fullWidth
                  sx={{
                    fontSize: '16px',
                    maxHeight: '40px',
                  }}
                  onClick={() => router.push(`/accounting-finance/invoice`)}
                >
                  ยกเลิก
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  color="dark"
                  fullWidth
                  sx={{
                    fontSize: '16px',
                    maxHeight: '40px',
                    width: '100%',
                  }}
                  disabled={
                    invoice.invoiceItem.length === 0 || invoice.totalPrice <= 0
                  }
                >
                  <ModalConfirmCreateInvoice
                    qtRef={!isNull(invoice.paymentQuotationNo)}
                  >
                    <div>บันทึก และขออนุมัติใบแจ้งหนี้</div>
                  </ModalConfirmCreateInvoice>
                </Button>
              </div>
            )}
          </div>
        </InvoicePaymentTotalStyled>
      )}
    </>
  );
};

export default InvoicePaymentTotal;
