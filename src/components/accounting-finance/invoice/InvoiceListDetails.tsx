import React, { useState } from 'react';
import { <PERSON><PERSON>, TextField } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { numberWithCommas } from '@/utils/number';
import ModalInvoiceList from '@/components/accounting-finance/invoice/modal/ModalAddInvoiceList';
import { isEmpty, isNull } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector, setChangeInvoice } from '@/store/features/invoice';
import Image from 'next/image';

export const InvoiceListDetailStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  flex: 2;
  gap: 16px;

  .card-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    .header-title > p {
      margin: 0;
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
  .card-body {
    width: 100%;
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
  }
  .card-title > p {
    margin: 0 0 24px 0;
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
`;

const InvoiceListDetails = () => {
  const dispatch = useAppDispatch();
  const { invoice } = useAppSelector(invoiceSelector);
  const [totalElements] = useState(0);
  const [loading] = useState<boolean>(false);

  const columns: GridColDef[] = [
    {
      field: 'index',
      headerName: '#',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 50,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return params.row.index;
      },
    },
    {
      field: 'name',
      headerName: 'รายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 100,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: 'description',
      headerName: 'รายการสั่งผลิต (อ้างอิง)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 300,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="truncate">
            {/* {invoice.invoicesStatus.id === 1 && */}
            {/* isNull(invoice.paymentQuotationNo) */}
            {/*  ? `${params.row.layDataNo} • ${params.row.description}` */}
            {/*  : params.row.description} */}
            {params.row.layDataNo} • {params.row.description}
          </div>
        );
      },
    },
    {
      field: 'quantity',
      headerName: 'จำนวน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 120,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return invoice.invoicesStatus.id === 1 &&
          isNull(invoice.paymentQuotationNo) ? (
          <TextField
            type="number"
            placeholder="0.0"
            fullWidth
            variant="outlined"
            value={params.row.quantity}
            onChange={(e) =>
              handleChangeItem(params.row.index, 'quantity', e.target.value)
            }
            inputProps={{
              step: '1',
              min: 0,
              max: '99999999999999999999',
            }}
          />
        ) : (
          numberWithCommas(params.row.quantity)
        );
      },
    },
    {
      field: 'priceUnit',
      headerName: 'ราคา/หน่วย',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 120,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return invoice.invoicesStatus.id === 1 &&
          isNull(invoice.paymentQuotationNo) ? (
          <TextField
            type="number"
            placeholder="0.0"
            fullWidth
            variant="outlined"
            value={params.row.priceUnit}
            onChange={(e) =>
              handleChangeItem(params.row.index, 'priceUnit', e.target.value)
            }
            inputProps={{
              step: '0.1',
              min: 0,
              max: '99999999999999999999',
            }}
          />
        ) : (
          numberWithCommas(params.row.priceUnit, 2)
        );
      },
    },
    {
      field: 'price',
      headerName: 'ราคาสินค้า (บาท)',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 120,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return invoice.invoicesStatus.id === 1 &&
          isNull(invoice.paymentQuotationNo) ? (
          <TextField
            disabled
            type="number"
            placeholder="0.0"
            fullWidth
            variant="outlined"
            value={params.row.priceUnit * params.row.quantity}
          />
        ) : (
          numberWithCommas(params.row.priceUnit * params.row.quantity, 2)
        );
      },
    },
    {
      field: 'discount',
      headerName: 'ส่วนลด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 120,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return invoice.invoicesStatus.id === 1 &&
          isNull(invoice.paymentQuotationNo) ? (
          <TextField
            type="number"
            placeholder="0.0"
            fullWidth
            variant="outlined"
            value={params.row.discount}
            onChange={(e) =>
              handleChangeItem(params.row.index, 'discount', e.target.value)
            }
            inputProps={{
              step: '0.1',
              min: 0,
              max: '99999999999999999999',
            }}
          />
        ) : (
          numberWithCommas(params.row.discount, 2)
        );
      },
    },
    {
      field: 'total',
      headerName: 'ราคารวม',
      editable: true,
      headerAlign:
        invoice.invoicesStatus.id === 1
          ? !isNull(invoice.paymentQuotationNo)
            ? 'right'
            : 'left'
          : 'right',
      align:
        invoice.invoicesStatus.id === 1
          ? !isNull(invoice.paymentQuotationNo)
            ? 'right'
            : 'left'
          : 'right',
      width: 150,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return params.row.price <= 0
          ? 0.0
          : numberWithCommas(params.row.price, 2);
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 100,
      disableColumnMenu: true,
      hide:
        invoice.invoicesStatus.id === 1
          ? !isNull(invoice.paymentQuotationNo)
          : true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div
            className="flex items-center p-[8px] rounded-[8px] hover:cursor-pointer border border-solid border-[#DBE2E5]"
            onClick={() => handleDeleteItem(params.row.id)}
          >
            <Image
              src={'/icons/ic-delete.svg'}
              alt="ic delete"
              width={23}
              height={23}
            />
          </div>
        );
      },
    },
  ];

  const handleAddItem = (layData: any) => {
    const items = [
      ...invoice.invoiceItem,
      {
        index: invoice.invoiceItem.length + 1,
        layDataId: layData.id,
        layDataNo: layData.ldCode,
        name: layData.peakProductName,
        description: layData.description,
        quantity: 1,
        price: 1.0,
        priceUnit: 1.0,
        discount: 0.0,
        vatType: 1,
        peakId: layData.peakProductId,
      },
    ];

    calculatePaymentDetail(items);
  };

  const handleChangeItem = (index: number, field: string, value: any) => {
    const regex = field !== 'quantity' ? /^\d{0,18}(\.\d{0,2})?$/ : /^\d+$/;
    if (regex.test(value) || value === '') {
      const newItems = invoice.invoiceItem.map((item: any) => {
        if (item.index === index) {
          const changeItem = { ...item, [field]: Number(value) };
          changeItem.price =
            (changeItem.priceUnit - changeItem.discount) * changeItem.quantity;

          return changeItem;
        }
        return item;
      });
      calculatePaymentDetail(newItems);
    }
  };

  const handleDeleteItem = (id: number) => {
    const filterItems = invoice.invoiceItem.filter(
      (item: any) => item.id !== id
    );
    const newItems = filterItems.map((item: any, index: number) => {
      return {
        ...item,
        index: index + 1,
      };
    });
    calculatePaymentDetail(newItems);
  };

  const calculatePaymentDetail = (items: any[]) => {
    if (!isEmpty(items)) {
      const initialValue = 0;
      const summaryNetPrice: number = items.reduce(
        (sum: number, item: any) => sum + Number(item.price),
        initialValue
      );

      const summaryVat: number =
        summaryNetPrice > 0
          ? Number(
              (
                (summaryNetPrice - invoice.discount) *
                (invoice.vatRate / 100)
              ).toFixed(2)
            )
          : 0;

      const summaryTotalPrice: number =
        summaryVat > 0
          ? Number((summaryNetPrice - invoice.discount + summaryVat).toFixed(2))
          : 0;

      dispatch(
        setChangeInvoice({
          ...invoice,
          totalPrice: summaryTotalPrice,
          vatAmount: summaryVat,
          netPrice: summaryNetPrice,
          invoiceItem: items,
        })
      );
    } else {
      dispatch(
        setChangeInvoice({
          ...invoice,
          totalPrice: 0,
          vatAmount: 0,
          discount: 0,
          discountRate: 0,
          netPrice: 0,
          invoiceItem: [],
        })
      );
    }
  };

  return (
    <InvoiceListDetailStyled>
      <div className="card-header">
        <div className="header-title">
          <p>
            {invoice.invoicesStatus.id === 1 && 'รายการเรียกเก็บ'}
            {invoice.invoicesStatus.id !== 1 && 'รายการแจ้งหนี้'}
          </p>
        </div>
        {isNull(invoice.paymentQuotationNo) &&
          invoice.invoicesStatus.id === 1 && (
            <ModalInvoiceList handleAddItem={handleAddItem}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                sx={{
                  bgcolor: '#1e293b',
                  '&:hover': { bgcolor: '#334155' },
                }}
              >
                เพิ่มรายการ
              </Button>
            </ModalInvoiceList>
          )}
      </div>
      <div className="card-body">
        <AppTableStyle $rows={invoice.invoiceItem}>
          <div className="content-wrap">
            <ScrollBarStyled>
              <DataGrid
                hideFooter={true}
                rows={invoice.invoiceItem}
                getRowId={(row) => row.index}
                columns={columns}
                paginationMode="server"
                rowCount={totalElements}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                loading={loading}
                components={{
                  NoRowsOverlay: TableNoRowsOverlay,
                  LoadingOverlay: TableLoadingOverlay,
                }}
              />
            </ScrollBarStyled>
          </div>
        </AppTableStyle>
      </div>
    </InvoiceListDetailStyled>
  );
};

export default InvoiceListDetails;
