import React, { useState } from 'react';
import { Avatar, Dialog } from '@mui/material';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import styled from 'styled-components';
import ModalQuotationReference from '@/components/accounting-finance/invoice/modal/ModalQuotationReference';
import { isUndefined } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector, setChangeInvoice } from '@/store/features/invoice';
import { Calendar } from 'react-date-range';
import moment from 'moment/moment';
import { th } from 'date-fns/locale';
import Image from 'next/image';
import { InvoiceType } from '@/store/features/invoice/types';

export const InvoiceCustomerDetailStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  flex: 2;
  gap: 40px;
  .invoice-details {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .detail-card {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      align-self: stretch;
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      .detail-item {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        flex: 2;
        .title {
          width: 100%;
          padding: 17px 24px;
          > p {
            margin: 0;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0.14px;
          }
        }
        .input {
          width: 100%;
          padding: 17px 24px;
          .select {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border: 1px solid #dbe2e5;
            border-radius: 8px;
            > p {
              margin: 0;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              color: #cfd8dc;
            }
            &:hover {
              cursor: pointer;
            }
          }
          &:hover {
            cursor: pointer;
          }
        }
      }
      .border-bottom {
        border-bottom: 1px solid #dbe2e5;
      }
    }
  }
  .invoice-customer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .detail-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      gap: 24px;
      width: 100%;
      padding: 24px;
      background: #f5f7f8;
      border-radius: 16px;
      .profile {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        background: #fff;
        > p {
          margin: 0;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          letter-spacing: 0.14px;
        }
      }
      .profile-detail {
        width: 100%;
        > p {
          margin: 0;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          letter-spacing: 0.12px;
        }
      }
    }
  }
  .card-title > p {
    margin: 0 0 24px 0;
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.22px;
  }
`;
const CalendarStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrSelected {
    background: #263238 !important;
  }
`;

const InvoiceCustomerDetails = () => {
  const dispatch = useAppDispatch();
  const { invoice } = useAppSelector(invoiceSelector);
  const [datePickerOpen, setDatePickerOpen] = useState<boolean>(false);

  const handleDateChange = (date: Date) => {
    const changeInvoice: InvoiceType = {
      ...invoice,
      dueDate: moment(date).format('YYYY-MM-DD'),
    };
    dispatch(setChangeInvoice(changeInvoice));
  };
  return (
    <>
      {!isUndefined(invoice) && (
        <InvoiceCustomerDetailStyled>
          <div className="invoice-details">
            <div className="card-title">
              <p>ข้อมูลใบเรียกเก็บ</p>
            </div>
            <div className="detail-card">
              <div className="detail-item border-bottom">
                <div className="title">
                  <p>รายการใบเสนอราคา (อ้างอิง)</p>
                </div>
                <div className="input">
                  <ModalQuotationReference>
                    <div className="select">
                      <div>
                        {invoice.paymentQuotationNo || 'เลือกรายการใบเสนอราคา'}
                      </div>
                      <span
                        className={'flex items-center hover:cursor-pointer'}
                      >
                        <KeyboardArrowDownRoundedIcon className="w-[24px] h-[24px]" />
                      </span>
                    </div>
                  </ModalQuotationReference>
                </div>
              </div>
              <div className="detail-item">
                <div className="title">
                  <p>วันที่ครบกำหนด</p>
                </div>
                <div className="input">
                  <Dialog
                    open={datePickerOpen}
                    onClose={() => setDatePickerOpen(false)}
                  >
                    <CalendarStyle>
                      <Calendar
                        date={
                          new Date(moment(invoice.dueDate).format('MM/DD/YYYY'))
                        }
                        onChange={(date) => {
                          handleDateChange(date);
                          setDatePickerOpen(false);
                        }}
                        locale={th}
                      />
                    </CalendarStyle>
                  </Dialog>
                  <div className="select">
                    <div>{moment(invoice.dueDate).format('DD/MM/YYYY')}</div>
                    <span
                      className={'flex items-center hover:cursor-pointer'}
                      onClick={() =>
                        // isNull(invoice.paymentQuotationNo) &&
                        setDatePickerOpen(true)
                      }
                    >
                      <Image
                        src="/icons/icon-calendar.svg"
                        width={24}
                        height={24}
                        alt=""
                      />
                    </span>
                  </div>
                  {/* <TextField */}
                  {/*  fullWidth */}
                  {/*  value={moment(invoice.dueDate).format('DD/MM/YYYY')} */}
                  {/*  disabled={!isNull(invoice.paymentQuotationNo)} */}
                  {/*  InputProps={{ */}
                  {/*    endAdornment: ( */}
                  {/*      <span */}
                  {/*        className={'flex items-center hover:cursor-pointer'} */}
                  {/*        onClick={() => */}
                  {/*          isNull(invoice.paymentQuotationNo) && */}
                  {/*          setDatePickerOpen(true) */}
                  {/*        } */}
                  {/*      > */}
                  {/*        <Image */}
                  {/*          src="/icons/icon-calendar.svg" */}
                  {/*          width={24} */}
                  {/*          height={24} */}
                  {/*          alt="" */}
                  {/*        /> */}
                  {/*      </span> */}
                  {/*    ), */}
                  {/*  }} */}
                  {/* /> */}
                </div>
              </div>
            </div>
          </div>
          <div className="invoice-customer">
            <div className="card-title">
              <p>ข้อมูลลูกค้า</p>
            </div>
            <div className="detail-card">
              <div className="profile">
                <Avatar
                  key={1}
                  alt="Remy Sharp"
                  src={
                    invoice.customer.imageUrl || '/images/profile-mockup.svg'
                  }
                  sx={{
                    width: 24,
                    height: 24,
                  }}
                />
                <p>{invoice.customer.name}</p>
              </div>
              <div className="profile-detail">
                <p>
                  {invoice.customer.contactType.name || '-'} •
                  เลขประจำตัวผู้เสียภาษี {invoice.customer.taxNumber || '-'}
                </p>
                <p>
                  โทร. {invoice.customer.phoneNumber || '-'}{' '}
                  {invoice.customer.email && `• ${invoice.customer.email}`}
                </p>
                <p>
                  {invoice.customer?.taxAddress &&
                  invoice.customer?.subDistrict &&
                  invoice.customer?.district &&
                  invoice.customer?.province &&
                  invoice.customer?.zipcode ? (
                    <>
                      {invoice.customer?.taxAddress || '-'}
                      {invoice.customer?.subDistrict || '-'}
                      {invoice.customer?.district || '-'}
                      {invoice.customer?.province || '-'}
                      {invoice.customer?.zipcode || '-'}
                    </>
                  ) : (
                    <>ไม่พบที่อยู่จัดส่ง</>
                  )}
                </p>
              </div>
            </div>
          </div>
        </InvoiceCustomerDetailStyled>
      )}
    </>
  );
};

export default InvoiceCustomerDetails;
