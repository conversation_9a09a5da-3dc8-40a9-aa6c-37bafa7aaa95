import styled, { css } from 'styled-components';
import { KeyboardBackspace } from '@mui/icons-material';
import Image from 'next/image';
import React, { useState } from 'react';
import { ActionGroupStyle, LoadingFadein } from '@/styles/share.styled';
import Link from 'next/link';
import { isEmpty, isNull } from 'lodash';
import { IconButton } from '@mui/material';
import KebabTable from '@/components/KebabTable';
import { KebabMenu } from '@/types/kebab-menu';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import ActionButton from '@/components/ActionButton';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import ModalConfirmCreateInvoice from '@/components/accounting-finance/invoice/modal/MiodalConfirmCreateInvoice';
import ModalConfirms from '@/components/accounting-finance/invoice/modal/ModalConfirms';
import { setSnackBar } from '@/store/features/alert';
import apiExportPdf from '@/services/order/export-pdf';
import { useRouter } from 'next/router';

const InvoiceDetailKebabWrap = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dbe2e5;
  border-radius: 8px;
`;
const InvoiceNavStyle = styled.div<{
  $borderBottom?: boolean;
  $backUrl?: string;
}>`
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  animation: ${LoadingFadein} 0.3s ease-in;
  padding: 40px;
  > div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
  }
  @media screen and (max-width: 820px) {
    padding: 0 16px;
  }
  ${({ $backUrl }) =>
    !isEmpty($backUrl)
      ? css`
          justify-content: space-between;
        `
      : css`
          justify-content: end;
        `}
  display: flex;
  flex-direction: row;
  align-items: center;
  background: white;
  ${({ $borderBottom }) =>
    !isEmpty($borderBottom) &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  column-gap: 16px;
  h1 {
    font-size: 22px;
    margin: 0;
    font-weight: 600;
  }
  @media screen and (max-width: 820px) {
    top: 64px;
  }
  @media screen and (max-width: 650px) {
    h1 {
      font-size: 1.2em;
    }
  }
  a {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #000 !important;
    height: 40px;
    width: 40px;
    svg {
      width: 24px;
      height: 24px;
    }
  }
  ${({ $borderBottom }) =>
    $borderBottom &&
    css`
      border-bottom: 1px solid #dbe2e5;
    `}
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
  }
  .back-button {
    a {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    svg {
      width: 24px;
      height: 24px;
      color: #263238;
    }
  }
  }
  .iv-nav-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
    animation: ${LoadingFadein} 0.3s ease-in;
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    
    ${({ $backUrl }) =>
      $backUrl
        ? css`
            //position: absolute;
            //left: 50%;
            //transform: translateX(-50%);
          `
        : css`
            position: absolute;
            left: 0;
            transform: translateX(16px);
          `};
  }
  .children {
    display: flex;
    column-gap: 12px;
  }
`;

type InvoiceNavProps = {
  title?: string;
  backUrl?: string;
  children?: React.ReactNode;
  showBorderBottom?: boolean;
  showAvatar?: boolean;
  centerTitle?: boolean;
  animate?: boolean;
  backUrlEvent?: () => void;
  actionMenuList?: KebabMenu;
};
const InvoiceNav = ({
  title,
  backUrl,
  children,
  showBorderBottom,
  showAvatar,
  backUrlEvent,
  actionMenuList,
}: InvoiceNavProps) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { invoiceId } = router.query;
  const { invoice } = useAppSelector(invoiceSelector);
  const [statusText, setStatusText] = useState<string>('');
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [loadingExport, setLoadingExport] = useState<boolean>(false);
  const handleClose = () => {
    setIsOpen(false);
  };

  const handleDownloadInvoice = async () => {
    setLoadingExport(true);
    const res = await apiExportPdf.exportInvoice(Number(invoiceId));
    if (res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message || 'Download failed',
          severity: !res.isError ? 'success' : 'error',
        })
      );
    }
    setLoadingExport(false);
  };

  return (
    <>
      {!isNull(invoice) && (
        <InvoiceNavStyle $borderBottom={showBorderBottom} $backUrl={backUrl}>
          <ModalConfirms
            status={statusText}
            isOpen={isOpen}
            handleClose={handleClose}
          />
          <div>
            {backUrl && !backUrlEvent && (
              <div className="back-button">
                <IconButton size={'small'}>
                  <Link href={backUrl}>
                    <KeyboardBackspace />
                  </Link>
                </IconButton>
              </div>
            )}
            {backUrlEvent && (
              <div className="back-button">
                <IconButton size={'small'}>
                  <Link
                    href={'#'}
                    onClick={(event: any) => {
                      event.preventDefault();
                      backUrlEvent();
                    }}
                  >
                    <KeyboardBackspace />
                  </Link>
                </IconButton>
              </div>
            )}
            {!isEmpty(title) && <div className="iv-nav-title">{title}</div>}
            {showAvatar && (
              <div className="avatar">
                <Image src={'/icons/icon-blank-profile.svg'} alt="" fill />
              </div>
            )}
            <div className="children">{children}</div>
          </div>
          <div>
            <ActionGroupStyle>
              {invoice.invoicesStatus.id === 2 && (
                <div>
                  <ModalConfirmCreateInvoice
                    qtRef={!isNull(invoice.paymentQuotationNo)}
                  >
                    <ActionButton
                      variant="contained"
                      color="Hon"
                      text="ยืนยันอนุมัติ"
                      borderRadius={'8px'}
                    />
                  </ModalConfirmCreateInvoice>
                </div>
              )}
              {(invoice.invoicesStatus.id === 3 ||
                invoice.invoicesStatus.id === 5) && (
                <div>
                  <ActionButton
                    variant="contained"
                    color="Hon"
                    disabled={invoice.balancePayment !== 0}
                    text="ยืนยันตรวจสอบการชำระเงิน"
                    borderRadius={'8px'}
                    onClick={() => {
                      setStatusText('ยืนยันตรวจสอบการชำระเงิน');
                      setIsOpen(true);
                    }}
                  />
                </div>
              )}
              <div>
                <ActionButton
                  variant="outlined"
                  color="blueGrey"
                  text="ดาวน์โหลดไฟล์"
                  borderRadius={'8px'}
                  onClick={handleDownloadInvoice}
                  disabled={loadingExport}
                />
              </div>
              <InvoiceDetailKebabWrap>
                <KebabTable
                  item={''}
                  isReplyEdit={invoice.invoicesStatus.id === 2}
                  handleReplyEdit={() => {
                    setStatusText('ยืนยันตีกลับไปแก้ไข');
                    setIsOpen(true);
                  }}
                  isRejectInvoice={invoice.invoicesStatus.id <= 2}
                  handleRejectInvoice={() => {
                    setStatusText('ยืนยันไม่อนุมัติใบแจ้งหนี้');
                    setIsOpen(true);
                  }}
                  isHowTo={true}
                  isHistory={{
                    status: true,
                  }}
                  isCancelInvoice={invoice.invoicesStatus.id <= 2}
                  handleCancelInvoice={() => {
                    setStatusText('ยืนยันยกเลิกใบแจ้งหนี้');
                    setIsOpen(true);
                  }}
                  {...(actionMenuList && { ...actionMenuList })}
                  iconHorizonDot={<MoreHorizRoundedIcon />}
                />
              </InvoiceDetailKebabWrap>
            </ActionGroupStyle>
          </div>
        </InvoiceNavStyle>
      )}
    </>
  );
};

export default InvoiceNav;
