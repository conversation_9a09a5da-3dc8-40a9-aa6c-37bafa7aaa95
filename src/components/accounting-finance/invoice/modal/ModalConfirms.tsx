import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@mui/material';
import React, { useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import { useRouter } from 'next/router';
import { setSnackBar } from '@/store/features/alert';
import apiInvoice from '@/services/order/invoice';

export const ModalConfirmCreateInvoiceStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 20px;
  .confirm-ref {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    h3 {
      margin: 40px 0 16px 0;
      text-align: center;
      font-size: 28px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.28px;
    }
    p {
      margin: 0;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.16px;
      text-align: center;
    }
  }
  .form-btn {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 40px 0 0 0;
  }
`;
type InvoiceListDialogProps = {
  status: string;
  isOpen: boolean;
  handleClose: () => void;
};

const ConfirmsDialog = ({
  status,
  isOpen,
  handleClose,
}: InvoiceListDialogProps) => {
  const router = useRouter();
  const { invoice } = useAppSelector(invoiceSelector);
  const dispatch = useAppDispatch();
  const [loadingConfirmPayment, setLaodingConfirmPayment] = useState(false);

  const handleConfirm = async () => {
    switch (status) {
      case 'ยืนยันตีกลับไปแก้ไข':
        return handleReplyEdit();
      case 'ยืนยันไม่อนุมัติใบแจ้งหนี้':
        return handleRevokeInvoice(6);
      case 'ยืนยันยกเลิกใบแจ้งหนี้':
        return handleRevokeInvoice(7);
      case 'ยืนยันตรวจสอบการชำระเงิน':
        return handleApproveValidatePayment();
      default:
        return () => {};
    }
  };

  const handleReplyEdit = async () => {
    const res = await apiInvoice.reverseInvoice(invoice.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    handleClose();
    return router.push(`/accounting-finance/invoice?status=รออนุมัติ`);
  };

  const handleRevokeInvoice = async (status: number) => {
    const res = await apiInvoice.revokeInvoice({
      paymentInvoiceId: invoice.id,
      paymentInvoiceStatusId: status,
    });
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          // text:
          //   status === 6
          //     ? 'ปฏิเสธใบแจ้งหนี้สำเร็จ'
          //     : status === 7
          //     ? 'ยกเลิกใบแจ้งหนี้สำเร็จ'
          //     : '',
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    handleClose();
    return router.push(`/accounting-finance/invoice?status=รออนุมัติ`);
  };

  const handleApproveValidatePayment = async () => {
    setLaodingConfirmPayment(true);
    const res = await apiInvoice.updateInvoiceStatus(invoice.id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    setLaodingConfirmPayment(false);
    handleClose();
    return router.push(`/accounting-finance/invoice?status=รออนุมัติ`);
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        handleClose();
      }}
    >
      <DialogContent>
        <FormModalStyle $width={492}>
          <div className="content-wrap">
            <div className="header border-none">
              <div className="x-close" onClick={() => handleClose()}>
                <IconButton>
                  <CloseIcon />
                </IconButton>
              </div>
            </div>
            <div className="form-wrap">
              <ModalConfirmCreateInvoiceStyled>
                {status === 'ยืนยันตีกลับไปแก้ไข' && (
                  <div className="confirm-ref">
                    <Image
                      src={'/icons/accounting-finance/export-note.svg'}
                      width={100}
                      height={100}
                      alt=""
                    />
                    <h3>ยืนยันตีกลับไปแก้ไข</h3>
                    <p>
                      {` คุณต้องการตีกลับไปแก้ไขใบแจ้งหนี้ “${invoice.invoiceNo}” ใช่หรือไม่`}
                    </p>
                  </div>
                )}
                {status === 'ยืนยันไม่อนุมัติใบแจ้งหนี้' && (
                  <div className="confirm-ref">
                    <Image
                      src={'/icons/icon-contract-delete.svg'}
                      width={100}
                      height={100}
                      alt=""
                    />
                    <h3>ยืนยันไม่อนุมัติใบแจ้งหนี้</h3>
                    <p>
                      {` คุณต้องการปฏิเสธการอนุมัติใบแจ้งหนี้ “${invoice.invoiceNo}” ใช่หรือไม่`}
                    </p>
                  </div>
                )}
                {status === 'ยืนยันยกเลิกใบแจ้งหนี้' && (
                  <div className="confirm-ref">
                    <Image
                      src={'/icons/icon-contract-delete.svg'}
                      width={100}
                      height={100}
                      alt=""
                    />
                    <h3>ยืนยันยกเลิกใบแจ้งหนี้</h3>
                    <p>
                      {` คุณต้องการยกเลิกใบแจ้งหนี้ “${invoice.invoiceNo}” ใช่หรือไม่?`}
                    </p>
                  </div>
                )}
                {status === 'ยืนยันตรวจสอบการชำระเงิน' && (
                  <div className="confirm-ref">
                    <Image
                      src={'/icons/accounting-finance/export-note.svg'}
                      width={100}
                      height={100}
                      alt=""
                    />
                    <h3>ยืนยันตรวจสอบการชำระเงิน</h3>
                    <p>
                      {` คุณได้ตรวจสอบข้อมูลใบแจ้งหนี้ “${invoice.invoiceNo}” เรียบร้อยแล้ว จะทำการเข้าสู่สถานะ “รับชำระแล้ว” เป็นอันสำเร็จ`}
                    </p>
                  </div>
                )}
                <div className="form-btn">
                  <Button
                    type="button"
                    variant="outlined"
                    color="dark"
                    fullWidth
                    sx={{
                      fontSize: '16px',
                      maxHeight: '40px',
                    }}
                    onClick={() => handleClose()}
                  >
                    ยกเลิก
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color={
                      status === 'ยืนยันไม่อนุมัติใบแจ้งหนี้' ||
                      status === 'ยืนยันยกเลิกใบแจ้งหนี้'
                        ? 'error'
                        : 'dark'
                    }
                    fullWidth
                    sx={{
                      fontSize: '16px',
                      maxHeight: '40px',
                    }}
                    onClick={() => handleConfirm()}
                    disabled={loadingConfirmPayment}
                  >
                    {loadingConfirmPayment ? (
                      <CircularProgress size={20} />
                    ) : (
                      <div>บันทึก</div>
                    )}
                  </Button>
                </div>
              </ModalConfirmCreateInvoiceStyled>
            </div>
          </div>
        </FormModalStyle>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmsDialog;
