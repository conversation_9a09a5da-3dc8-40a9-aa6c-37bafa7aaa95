import {
  <PERSON><PERSON>,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import Image from 'next/image';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import apiAnnotation from '@/services/stock/annotation';
import apiInvoice from '@/services/order/invoice';
import { setSnackBar } from '@/store/features/alert';
import moment from 'moment/moment';

export const ModalConfirmCreateInvoiceStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 20px;
  .confirm-ref {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    h3 {
      margin: 40px 0 16px 0;
      text-align: center;
      font-size: 28px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.28px;
    }
    p {
      margin: 0;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.16px;
      text-align: center;
    }
  }
  .confirm-none-ref {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    h3 {
      margin: 40px 0 16px 0;
      text-align: center;
      font-size: 28px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.28px;
    }
    p {
      margin: 0;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.16px;
      text-align: center;
    }
    .form-input {
      padding-top: 40px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: start;
      gap: 24px;
      p {
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.14px;
        text-align: left;
        margin-bottom: 16px;
      }
    }
  }
  .form-btn {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 40px 0 0 0;
  }
`;
type InvoiceListDialogProps = {
  children: React.ReactNode;
  qtRef: boolean;
};

const initialValue = {
  annotationId: '',
  annotationRemark: '',
};

const ConfirmCreateInvoiceDialog = ({
  children,
  qtRef,
}: InvoiceListDialogProps) => {
  const router = useRouter();
  const { invoice } = useAppSelector(invoiceSelector);
  const dispatch = useAppDispatch();
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [isShowContactModal, setShowContactModal] = useState(false);
  const [annotationList, setAnnotationList] = useState<any>([]);

  const validationSchema = yup.object({
    annotationRemark: yup.string().when([], {
      is: () => (invoice.invoicesStatus.id === 1 ? !qtRef : false),
      then: () => yup.string().required('กรุณากรอกหมายเหตุ'),
      otherwise: () => yup.string().notRequired(),
    }),
    annotationId: yup.number().when([], {
      is: () => (invoice.invoicesStatus.id === 1 ? !qtRef : false),
      then: () =>
        yup.number().typeError('กรุณาเลือกสาเหตุ').required('กรุณาเลือกสาเหตุ'),
      otherwise: () => yup.string().notRequired(),
    }),
  });
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      ...initialValue,
      annotationRemark: '',
      annotationId: '',
    },
  });

  useEffect(() => {
    if (isShowContactModal && !qtRef && invoice.invoicesStatus.id === 1) {
      getAnnotation().then();
    }
  }, [isShowContactModal]);

  const getAnnotation = async () => {
    const res = await apiAnnotation.annotationList(3);
    if (!res.isError) {
      setAnnotationList(res.data);
    }
  };

  const handleClose = () => {
    setShowContactModal(false);
  };

  const handleOpen = async () => {
    setValue('annotationRemark', '');
    setValue('annotationId', '');
    setShowContactModal(true);
  };

  const handleConfirm = async (values: any) => {
    const { id } = invoice.invoicesStatus;
    if (id === 1) {
      if (qtRef) {
        setLoadingApprove(true);
        const res = await apiInvoice.updateInvoiceStatus(invoice.id);
        if (!res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message,
              severity: 'success',
            })
          );
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message.message,
              severity: 'error',
            })
          );
        }
        setLoadingApprove(false);
        setShowContactModal(false);
        handleClose();
        return router.push(`/accounting-finance/invoice`);
      }

      const setItems = !isEmpty(invoice.invoiceItem)
        ? invoice.invoiceItem.map((item: any) => {
            console.log(item);
            return {
              layDataId: item.layDataId,
              peakProductId: item.peakId,
              quantity: item.quantity,
              priceUnit: item.priceUnit,
              price: item.price,
              discount: item.discount,
              description: item.description,
            };
          })
        : [];
      const req = {
        invoiceId: invoice.id,
        paymentQuotationId: null,
        dueDate: moment(invoice.dueDate).format('YYYY-MM-DD'),
        totalPrice: invoice.totalPrice,
        netPrice: invoice.netPrice,
        discount: invoice.discount,
        distcountRate: invoice.discountRate,
        vatAmount: invoice.vatAmount,
        remark: invoice.remark,
        paymentInvoicesItem: setItems,
      };

      const updateInvoice = await apiInvoice.updateInvoice(req);
      if (!updateInvoice.isError) {
        const req = {
          paymentInvoiceId: invoice.id,
          annotationId: values.annotationId,
          annotationRemark: values.annotationRemark,
        };
        const updateAnnotation = await apiInvoice.updateInvoiceAnnotation(req);
        if (!updateAnnotation.isError) {
          setLoadingApprove(true);
          const res = await apiInvoice.updateInvoiceStatus(invoice.id);
          if (!res.isError) {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message,
                severity: 'success',
              })
            );
          } else {
            dispatch(
              setSnackBar({
                status: true,
                text: res.message.message,
                severity: 'error',
              })
            );
          }
          setLoadingApprove(false);
          setShowContactModal(false);
          handleClose();
          return router.push(`/accounting-finance/invoice`);
        }
        dispatch(
          setSnackBar({
            status: true,
            text: updateAnnotation.message.message,
            severity: 'error',
          })
        );
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: updateInvoice.message.message,
            severity: 'error',
          })
        );
      }
    }

    if (id === 2) {
      setLoadingApprove(true);
      const res = await apiInvoice.updateInvoiceStatus(invoice.id);
      if (!res.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message,
            severity: 'success',
          })
        );
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: res.message.message,
            severity: 'error',
          })
        );
      }
      setLoadingApprove(false);
      setShowContactModal(false);
      handleClose();
      return router.push(`/accounting-finance/invoice?status=รออนุมัติ`);
    }
  };

  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header border-none">
                <div className="x-close" onClick={() => handleClose()}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <ModalConfirmCreateInvoiceStyled>
                  <form
                    onSubmit={handleSubmit(handleConfirm)}
                    style={{
                      width: '100%',
                      rowGap: '0',
                    }}
                  >
                    {invoice.invoicesStatus.id === 1 && (
                      <>
                        {qtRef ? (
                          <div className="confirm-ref">
                            <Image
                              src={'/icons/accounting-finance/export-note.svg'}
                              width={100}
                              height={100}
                              alt=""
                            />
                            <h3>ยืนยันขออนุมัติใบแจ้งหนี้</h3>
                            <p>
                              {` คุณได้ตรวจสอบข้อมูลใบแจ้งหนี้ “${invoice.invoiceNo}”
                          เรียบร้อยแล้ว จะทำการเข้าสู่สถานะ “รออนุมัติ”
                          ในขั้นตอนต่อไป`}
                            </p>
                          </div>
                        ) : (
                          <div className="confirm-none-ref">
                            <Image
                              src={
                                '/icons/accounting-finance/priority-high.svg'
                              }
                              width={100}
                              height={100}
                              alt=""
                            />
                            <h3>ยืนยันขออนุมัติใบแจ้งหนี้</h3>
                            <p>
                              {` คุณได้สร้างข้อมูลใบแจ้งหนี้ “${invoice.invoiceNo}”
                          โดยไม่อ้างอิงเลขที่ใบเสนอราคาด้วยสาเหตุดังนี้`}
                            </p>
                            <div className="form-input">
                              <div className="ld-select">
                                <p>สาเหตุไม่อ้างอิงใบเสนอราคา</p>
                                <FormControl
                                  fullWidth
                                  sx={{ boxShadow: 'none' }}
                                >
                                  <Select
                                    {...register('annotationId')}
                                    error={Boolean(hookFormErrors.annotationId)}
                                    displayEmpty
                                    value={watch('annotationId')}
                                    sx={{
                                      fontSize: '14px',
                                      height: '40px',
                                    }}
                                  >
                                    <MenuItem
                                      disabled
                                      value=""
                                      sx={{
                                        fontSize: '14px',
                                      }}
                                    >
                                      <div className="text-[#78909C]">
                                        เลือกสาเหตุไม่อ้างอิงเลขที่ใบเสนอราคา
                                      </div>
                                    </MenuItem>
                                    {!isEmpty(annotationList) &&
                                      annotationList.map(
                                        (item: any, index: number) => (
                                          <MenuItem
                                            key={index}
                                            value={item.id}
                                            sx={{
                                              fontSize: '14px',
                                            }}
                                          >
                                            {item.name}
                                          </MenuItem>
                                        )
                                      )}
                                  </Select>
                                </FormControl>
                                {hookFormErrors.annotationId && (
                                  <FormHelperText
                                    error
                                    sx={{
                                      margin: '4px 14px 0',
                                    }}
                                  >
                                    {hookFormErrors.annotationId.message}
                                  </FormHelperText>
                                )}
                              </div>
                              <div className="description">
                                <p>หมายเหตุ</p>
                                <TextField
                                  multiline
                                  rows={4}
                                  type="text"
                                  placeholder="อธิบาย"
                                  fullWidth
                                  variant="outlined"
                                  {...register('annotationRemark')}
                                  error={Boolean(
                                    hookFormErrors.annotationRemark
                                  )}
                                  helperText={
                                    hookFormErrors.annotationRemark
                                      ? (hookFormErrors.annotationRemark
                                          .message as ReactNode)
                                      : ''
                                  }
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                    {invoice.invoicesStatus.id === 2 && (
                      <div className="confirm-ref">
                        <Image
                          src={'/icons/accounting-finance/export-note.svg'}
                          width={100}
                          height={100}
                          alt=""
                        />
                        <h3>ยืนยันอนุมัติใบแจ้งหนี้</h3>
                        <p>
                          {` คุณได้ตรวจสอบข้อมูลใบแจ้งหนี้ “${invoice.invoiceNo}”
                          เรียบร้อยแล้วจะทำการเข้าสู่สถานะ “รอรับชำระ” ในขั้นตอนต่อไป`}
                        </p>
                      </div>
                    )}
                    <div className="form-btn">
                      <Button
                        type="button"
                        variant="outlined"
                        color="dark"
                        fullWidth
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        onClick={() => handleClose()}
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        color="dark"
                        fullWidth
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        disabled={loadingApprove}
                      >
                        {loadingApprove ? (
                          <CircularProgress size={20} />
                        ) : (
                          <div>บันทึก</div>
                        )}
                      </Button>
                    </div>
                  </form>
                </ModalConfirmCreateInvoiceStyled>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ConfirmCreateInvoiceDialog;
