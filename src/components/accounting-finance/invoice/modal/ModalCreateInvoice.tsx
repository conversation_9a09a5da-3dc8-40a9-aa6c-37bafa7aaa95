import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiContact from '@/services/core/contact';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty, isNull } from 'lodash';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import apiInvoice from '@/services/order/invoice';

export const ModalCreateInvoiceContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;
  .empty-contact {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .contact-item-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .contact-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 16px;
      padding: 10px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }
      h4 {
        margin: 0;
        line-height: 1.3;
      }
      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
        font-weight: 400;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }
  .active {
    background: #f5f7f8;
  }
  .btn-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;
  }
`;
export const InvoiceContactTypeSelect = styled.div`
  margin-top: 24px;
  width: 100%;
  display: flex;
  justify-content: start;
  gap: 8px;
  .btn-type {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 12px;
    border: 1px solid #dbe2e5;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    &.active {
      color: white;
      background: #263238;
      font-weight: 600;
    }
  }
`;

type ContactDialogProps = {
  children: React.ReactNode;
  handleReloadList: () => void;
};

const initialFilters = {
  search: '',
};

const ContactDialog = ({ children, handleReloadList }: ContactDialogProps) => {
  const dispatch = useAppDispatch();
  const [contactList, setContactList] = useState([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingCreateInvoice, setLoadingCreateInvoice] = useState(false);
  const [selectedContactData, setSelectedContactData] = useState<any>({});
  const [contactType, setContactType] = useState<number>(1);
  const [timer, setTimer] = useState<any>(null);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [isShowContactModal, setShowContactModal] = useState(false);

  useEffect(() => {
    getContactList().then();
  }, [filters.search, contactType]);

  const getContactList = async () => {
    const res = await apiContact.getContactOptions(
      contactType !== 0 ? { ...filters, contactTypeId: contactType } : filters
    );
    if (res && !res.isError) {
      setContactList(res.data);
    }
  };

  const handleSearch = (event: any) => {
    const currentFilters = filters;
    setSearchInput(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...currentFilters,
        search: event.target.value,
        searchName: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  const handleClose = () => {
    setShowContactModal(false);
  };

  const handleOpen = async () => {
    setContactList([]);
    setFilters(initialFilters);
    setSearchInput('');
    setContactType(0);
    setSelectedContactData({});
    await getContactList();
    setShowContactModal(true);
  };

  const handleCreateInvoice = async () => {
    setLoadingCreateInvoice(true);
    const sendData = {
      contactId: selectedContactData.id,
    };
    const res = await apiInvoice.createInvoice(sendData);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
      handleReloadList();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    setLoadingCreateInvoice(false);
    handleClose();
  };

  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">รายชื่อผู้ติดต่อ</div>
                <div
                  className="x-close"
                  onClick={() => setShowContactModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหาลูกค้า"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                    }}
                  />
                  <InvoiceContactTypeSelect>
                    <div
                      className={`btn-type ${contactType === 0 && 'active'}`}
                      onClick={() => {
                        setContactType(0);
                      }}
                    >
                      ทั้งหมด
                    </div>
                    <div
                      className={`btn-type ${contactType === 2 && 'active'}`}
                      onClick={() => {
                        setContactType(2);
                      }}
                    >
                      บุคคลธรรมดา
                    </div>
                    <div
                      className={`btn-type ${contactType === 1 && 'active'}`}
                      onClick={() => {
                        setContactType(1);
                      }}
                    >
                      นิติบุคคล
                    </div>
                  </InvoiceContactTypeSelect>
                  <p className="m-0 pt-[16px] text-[#CFD8DC]">ค้นหาล่าสุด</p>
                  <ModalCreateInvoiceContentStyled>
                    <div className="contact-item-wrap">
                      {contactList.map((item: any, index: number) => (
                        <div
                          key={index}
                          className={
                            !isNull(selectedContactData) &&
                            selectedContactData.id === item.id
                              ? `contact-item active`
                              : `contact-item`
                          }
                          onClick={() => {
                            setSelectedContactData(item);
                          }}
                        >
                          <Avatar
                            src={item.imageUrl}
                            sx={{
                              height: '40px',
                              width: '40px',
                            }}
                          />
                          <div className="">
                            <h4>{item.name}</h4>
                            <p>
                              <span>{item.contactType.name}</span>
                              {!isEmpty(item.phoneNumber) && (
                                <>
                                  <span>•</span>
                                  <span>{item.phoneNumber}</span>
                                </>
                              )}
                              {!isEmpty(item.email) && (
                                <>
                                  <span>•</span>
                                  <span>{item.email}</span>
                                </>
                              )}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="btn-group">
                      <Button
                        type="button"
                        variant="outlined"
                        color="dark"
                        fullWidth
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        onClick={() => {
                          setShowContactModal(false);
                        }}
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        type="button"
                        variant="contained"
                        color="dark"
                        fullWidth
                        disabled={
                          isEmpty(selectedContactData) || loadingCreateInvoice
                        }
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        onClick={async () => {
                          await handleCreateInvoice();
                        }}
                      >
                        {loadingCreateInvoice ? (
                          <CircularProgress size={20} />
                        ) : (
                          <div>สร้าง</div>
                        )}
                      </Button>
                    </div>
                  </ModalCreateInvoiceContentStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ContactDialog;
