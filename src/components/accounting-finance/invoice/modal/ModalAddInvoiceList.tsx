import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiLayData from '@/services/order/layData';
import { isEmpty, isNull, isUndefined } from 'lodash';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import apiPeakProduct from '@/services/stock/peak-product';

export const ModalPeakProductStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;
  gap: 16px;
  padding-top: 20px;
  .empty-qt {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .qto-count {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    padding: 16px 0;
    border-bottom: 1px solid #dbe2e5;
    p {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.14px;
    }
  }
  .qt-items-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .qt-items {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 8px;
      padding: 8px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }
      p {
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
    .active {
      background: #f5f7f8;
    }
  }
  .btn-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;
  }
`;
export const ModalInvoiceListStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  .empty-qt {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .qt-items-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .qt-items {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 8px;
      padding: 16px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }
      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
  }
  .btn-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;
  }
  .invoice-list-detail {
    width: 100%;
    height: 422px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    .form-input {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: start;
      gap: 40px;
      p {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.14px;
      }
      .ld-select {
      }
      .description {
        width: 100%;
        height: 100px;
      }
    }
    .form-btn {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 24px;
    }
  }
`;

const initialValue = {
  layDataId: '',
  description: '',
};

type InvoiceListDialogProps = {
  children: React.ReactNode;
  handleAddItem: (item: any) => void;
};

const InvoiceListDialog = ({
  children,
  handleAddItem,
}: InvoiceListDialogProps) => {
  const [layDataList, setLayDataList] = useState<any>([]);
  const [peakProductList, setPeakProductList] = useState<any>();
  const [selectedPeakProduct, setSelectedPeakProduct] = useState<any | null>(
    null
  );
  const [selectedLayData, setSelectedLayData] = useState<any>({});
  const [isShowContactModal, setShowContactModal] = useState(false);
  const [step, setStep] = useState<number>(1);

  const validationSchema = yup.object({
    description: yup.string().required('กรุณาระบุรายละเอียด'),
    layDataId: yup
      .number()
      .typeError('กรุณาเลือกรายการสินค้า')
      .required('กรุณาเลือกรายการสินค้า'),
  });
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      ...initialValue,
      layDataId: '',
      description: '',
    },
  });

  useEffect(() => {
    if (isShowContactModal) {
      setStep(1);
      setSelectedPeakProduct(null);
      getLayDataList().then();
      getPeakProductList().then();
    }
  }, [isShowContactModal]);

  const getLayDataList = async () => {
    const res = await apiLayData.getLayDataList();
    if (!res.isError) {
      setLayDataList(res.data);
    }
  };

  const getPeakProductList = async () => {
    const res = await apiPeakProduct.getPeakProducts();
    if (!res.isError) {
      setPeakProductList(res.data);
    }
  };

  const handleClose = () => {
    setShowContactModal(false);
  };

  const handleOpen = async () => {
    setValue('layDataId', '');
    setValue('description', '');
    setLayDataList([]);
    setShowContactModal(true);
  };

  const onSubmit = async (values: any) => {
    const setItem = {
      ...selectedLayData,
      description: values.description,
      peakProductId: selectedPeakProduct.id,
      peakProductName: selectedPeakProduct.name,
    };
    handleAddItem(setItem);
    setShowContactModal(false);
  };
  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">
                  {step === 1 ? 'เพิ่มรายการ' : 'รายละเอียด'}
                </div>
                <div className="x-close" onClick={() => handleClose()}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                {step === 1 && (
                  <ModalPeakProductStyled>
                    <div className="qto-count">
                      <p className="m-0">
                        {!isEmpty(peakProductList) ? peakProductList.length : 0}{' '}
                        รายการ
                      </p>
                    </div>
                    <div className="qt-items-wrap">
                      {!isEmpty(peakProductList) &&
                        !isUndefined(peakProductList) &&
                        peakProductList.map((item: any, index: number) => (
                          <div
                            key={index}
                            className={
                              !isNull(selectedPeakProduct) &&
                              selectedPeakProduct.id === item.id
                                ? 'qt-items active'
                                : 'qt-items'
                            }
                            onClick={() => {
                              setSelectedPeakProduct(item);
                              setStep(2);
                            }}
                          >
                            <p>{item.name}</p>
                          </div>
                        ))}
                    </div>
                  </ModalPeakProductStyled>
                )}
                {step === 2 && (
                  <ModalInvoiceListStyled>
                    <form
                      onSubmit={handleSubmit(onSubmit)}
                      style={{
                        width: '100%',
                        rowGap: '0',
                      }}
                    >
                      <div className="invoice-list-detail">
                        <div className="form-input">
                          <div className="ld-select">
                            <p>รายการสินค้า (อ้างอิง)</p>
                            <FormControl fullWidth sx={{ boxShadow: 'none' }}>
                              <Select
                                {...register('layDataId')}
                                error={Boolean(hookFormErrors.layDataId)}
                                displayEmpty
                                value={watch('layDataId')}
                                sx={{
                                  fontSize: '14px',
                                  height: '40px',
                                }}
                              >
                                <MenuItem
                                  disabled
                                  value=""
                                  sx={{
                                    fontSize: '14px',
                                  }}
                                >
                                  <div className="text-[#78909C]">เลือก</div>
                                </MenuItem>
                                {!isEmpty(layDataList) &&
                                  layDataList.map(
                                    (item: any, index: number) => (
                                      <MenuItem
                                        key={index}
                                        value={item.id}
                                        sx={{
                                          fontSize: '14px',
                                        }}
                                        onClick={() => {
                                          setSelectedLayData({
                                            ...item,
                                            layDataId: item.id,
                                          });
                                        }}
                                      >
                                        {item.ldCode}
                                      </MenuItem>
                                    )
                                  )}
                              </Select>
                            </FormControl>
                            {hookFormErrors.layDataId && (
                              <FormHelperText
                                error
                                sx={{
                                  margin: '4px 14px 0',
                                }}
                              >
                                {hookFormErrors.layDataId.message}
                              </FormHelperText>
                            )}
                          </div>
                          <div className="description">
                            <p>รายละเอียด</p>
                            <TextField
                              multiline
                              rows={4}
                              type="text"
                              placeholder="รายละเอียด"
                              fullWidth
                              variant="outlined"
                              {...register('description')}
                              error={Boolean(hookFormErrors.description)}
                              helperText={
                                hookFormErrors.description
                                  ? (hookFormErrors.description
                                      .message as ReactNode)
                                  : ''
                              }
                            />
                          </div>
                        </div>
                        <div className="form-btn">
                          <Button
                            type="button"
                            variant="outlined"
                            color="dark"
                            fullWidth
                            sx={{
                              fontSize: '16px',
                              maxHeight: '40px',
                            }}
                            onClick={() => handleClose()}
                          >
                            ยกเลิก
                          </Button>
                          <Button
                            type="submit"
                            variant="contained"
                            color="dark"
                            fullWidth
                            sx={{
                              fontSize: '16px',
                              maxHeight: '40px',
                            }}
                          >
                            บันทึก
                          </Button>
                        </div>
                      </div>
                    </form>
                  </ModalInvoiceListStyled>
                )}
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default InvoiceListDialog;
