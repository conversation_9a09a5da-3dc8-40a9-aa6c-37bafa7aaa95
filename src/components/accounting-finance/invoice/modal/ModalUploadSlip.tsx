import {
  <PERSON>ton,
  <PERSON><PERSON>,
  DialogContent,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiBankCompany from '@/services/core/bank-company';
import { isEmpty, isNull, isUndefined } from 'lodash';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Image from 'next/image';
import MoneyRoundedIcon from '@mui/icons-material/MoneyRounded';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import { Calendar } from 'react-date-range';
import moment from 'moment';
import { th } from 'date-fns/locale';
import { MultiSectionDigitalClock } from '@mui/x-date-pickers/MultiSectionDigitalClock';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useAppDispatch, useAppSelector } from '@/store';
import { getInvoice, invoiceSelector } from '@/store/features/invoice';
import { numberWithCommas } from '@/utils/number';
import apiPaymentTransaction from '@/services/order/paymentTransaction';
import { setSnackBar } from '@/store/features/alert';
import dayjs from 'dayjs';

export const ModalUploadSlipStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 40px 20px 20px 20px;
  p {
    margin: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.14px;
  }
  .total-paid {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 26px 24px;
    border-radius: 16px;
    background: #f5f7f8;
    h6 {
      margin: 0;
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.22px;
    }
  }
  .payment-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .total-received {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .date-time {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    gap: 40px;
    .item {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      flex-grow: 1;
    }
  }
  .slip {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .description {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
`;
const CalendarStyle = styled.div`
  font-family: Prompt, sans-serif;
  select {
    font-family: Prompt, sans-serif;
  }
  .rdrSelected {
    background: #263238 !important;
  }
`;
const DateTimeInputStyled = styled(TextField)(() => ({
  '& .MuiInputAdornment-root': {
    borderRight: '1px solid #e0e0e0',
    height: '100%',
    maxHeight: 'none',
    paddingRight: '9px',
    color: '#333',
  },
}));
const FilePickerStyled = styled.label`
  .empty-upload {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    text-align: center;
    gap: 8px;
    padding: 24px 8px;
    border-radius: 6px;
    border: 1px dashed #90a4ae;
    cursor: pointer;
    transition: 0.15s ease-out;
  }
  &:hover {
    background: #f5f7f8;
  }
  p {
    margin: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #cfd8dc;
  }
  h6 {
    margin: 0;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .is-upload {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    gap: 16px;
    text-align: left;
    padding: 16px;
    border-radius: 6px;
    border: 1px dashed #90a4ae;
    cursor: pointer;
    transition: 0.15s ease-out;
    .text {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: center;
      gap: 16px;
    }
  }
`;

type UploadSlipDialogProps = {
  children: React.ReactNode;
};

const initialValue = {
  bankCompanyId: '',
  price: '',
  date: '',
  time: '',
  description: '',
  file: '',
};

const UploadSlipDialog = ({ children }: UploadSlipDialogProps) => {
  const dispatch = useAppDispatch();
  const { invoice } = useAppSelector(invoiceSelector);
  const [isShowUploadSlipModal, setShowUploadSlipModal] = useState(false);
  const [bankCompanies, setBankCompanies] = useState<any>([]);
  const [datePickerOpen, setDatePickerOpen] = useState<boolean>(false);
  const [timePickerOpen, setTimePickerOpen] = useState<boolean>(false);
  const [file, setFile] = useState<any>(null);

  const isValidFileType = (value: any) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    return !(value.length > 0 && !allowedTypes.includes(value[0].type));
  };

  const isValidateFileSize = (value: any) => {
    const allowedSize = 5 * 1024 * 1024; // 5MB in bytes
    if (value) {
      return value[0].size <= allowedSize;
    }

    return false;
  };

  const validationSchema = yup.object({
    bankCompanyId: yup
      .number()
      .typeError('กรุณาเลือกช่องทางชำระเงิน')
      .required('กรุณาเลือกช่องทางชำระเงิน'),
    date: yup.string().required('กรุณาระบุวันที่'),
    time: yup.string().required('กรุณาระบุเวลา'),
    price: yup
      .number()
      .typeError('กรุณาระบุยอดที่ชำระ')
      .required('กรุณาระบุยอดที่ชำระ')
      .min(
        invoice.balancePayment,
        `ยอดที่ต้องชำระ ${invoice.balancePayment} บาท`
      )
      .max(invoice.balancePayment, 'ชำระเงินเกินยอดที่ต้องชำระ'),
    file: yup
      .mixed()
      .test('required', 'กรุณาแนบหลักฐานการชำระเงิน', (value: any) => !!value)
      .test(
        'required',
        'Please select a valid image file (JPEG, PNG, SVG, or GIF)',
        (value: any) => isValidFileType(value)
      )
      .test('required', 'File should be less than 5MB', (value: any) =>
        isValidateFileSize(value)
      ),
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      ...initialValue,
      bankCompanyId: '',
      price: '',
      date: '',
      time: '',
      description: '',
      file: '',
    },
  });

  useEffect(() => {
    if (isShowUploadSlipModal) {
      getBankCompany().then();
    }
  }, [isShowUploadSlipModal]);

  useEffect(() => {
    const file: any = watch('file');
    if (!isUndefined(file[0])) {
      setFile(URL.createObjectURL(file[0]));
    }
  }, [watch('file')]);

  const handleClose = () => {
    setShowUploadSlipModal(false);
  };

  const handleOpen = async () => {
    setValue('bankCompanyId', '');
    setValue('price', '');
    setValue('date', dayjs().format('YYYY-MM-DD'));
    setValue('time', dayjs().format('HH:mm'));
    setValue('description', '');
    setValue('file', '');
    setFile(null);
    setShowUploadSlipModal(true);
  };

  const getBankCompany = async () => {
    const res = await apiBankCompany.getBankCompanies();
    if (!res.isError) {
      setBankCompanies(res.data);
    }
  };

  const onchangePrice = (e: any) => {
    const { value } = e.target;
    const regex = /^\d{0,18}(\.\d{0,2})?$/;
    if (regex.test(value) || value === '') {
      setValue('price', value);
    }
  };

  const handleConfirm = async (values: any) => {
    const req = {
      price: values.price,
      paymentInvoiceId: invoice.id,
      paidType: values.bankCompanyId === 0 ? 1 : 2,
      paymentDate: values.date,
      paymentTime: values.time,
      remark: values.description,
      bankCompanyId: values.bankCompanyId === 0 ? null : values.bankCompanyId,
    };
    const res = await apiPaymentTransaction.createTransaction(req);
    if (!res.isError) {
      const formData = new FormData();
      formData.append('file', values.file[0]);
      formData.append('paymentTransactionId', res.data.id);
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      };
      const uploadSlip =
        values.bankCompanyId === 0
          ? await apiPaymentTransaction.uploadPayment(formData, config)
          : await apiPaymentTransaction.uploadSlip(formData, config);
      if (!uploadSlip.isError) {
        dispatch(
          setSnackBar({
            status: true,
            text: uploadSlip.message,
            severity: 'success',
          })
        );
        dispatch(getInvoice(invoice.id));
        setShowUploadSlipModal(false);
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: uploadSlip.message.message,
            severity: 'error',
          })
        );
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
  };

  return (
    <>
      <div
        onClick={() => {
          handleOpen().then();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowUploadSlipModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เพิ่มการชำระเงิน</div>
                <div
                  className="x-close"
                  onClick={() => setShowUploadSlipModal(false)}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={handleSubmit(handleConfirm)}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <ModalUploadSlipStyled>
                    <div className="total-paid">
                      <p>ยอดที่ต้องรับชำระทั้งสิ้น</p>
                      <h6>{numberWithCommas(invoice.balancePayment, 2)} บาท</h6>
                    </div>
                    <div className="payment-list">
                      <p>ช่องทางชำระเงิน</p>
                      <FormControl fullWidth sx={{ boxShadow: 'none' }}>
                        <Select
                          {...register('bankCompanyId')}
                          error={Boolean(hookFormErrors.bankCompanyId)}
                          value={watch('bankCompanyId')}
                          onChange={(e: any) => {
                            setValue('bankCompanyId', e.target.value);
                          }}
                          displayEmpty
                          sx={{
                            fontSize: '14px',
                            height: '40px',
                          }}
                        >
                          <MenuItem
                            disabled
                            value=""
                            sx={{
                              fontSize: '14px',
                            }}
                          >
                            <div>เลือกช่องทางชำระเงิน</div>
                          </MenuItem>
                          <MenuItem
                            value={0}
                            sx={{
                              fontSize: '14px',
                              padding: 0,
                            }}
                          >
                            <div className="flex items-center gap-[16px] py-[8px] w-full">
                              <div className="w-[32px] h-[32px] bg-[#F5F7F8] rounded-[8px] flex items-center justify-center">
                                <MoneyRoundedIcon className="w-[24px] h-[24px]" />
                              </div>
                              เงินสด - ชำระหน้าร้าน
                            </div>
                          </MenuItem>
                          {!isEmpty(bankCompanies) &&
                            bankCompanies.map((item: any, index: number) => (
                              <MenuItem
                                key={index}
                                value={item.id}
                                sx={{
                                  fontSize: '14px',
                                  padding: 0,
                                }}
                              >
                                <div className="flex items-center gap-[16px] py-[8px]">
                                  {item.bankAccount.logoUrl && (
                                    <Image
                                      className="rounded-[6px] overflow-hidden"
                                      src={item.bankAccount.logoUrl}
                                      width={32}
                                      height={32}
                                      alt=""
                                    />
                                  )}
                                  {`${item.bankAccount.name} - ${item.nameTH}`}
                                </div>
                              </MenuItem>
                            ))}
                        </Select>
                      </FormControl>
                      {hookFormErrors.bankCompanyId && (
                        <FormHelperText
                          error
                          sx={{
                            margin: '4px 14px 0',
                          }}
                        >
                          {hookFormErrors.bankCompanyId.message}
                        </FormHelperText>
                      )}
                    </div>
                    <div className="total-received">
                      <p>ยอดที่ชำระ</p>
                      <TextField
                        type="number"
                        placeholder="0.00"
                        InputProps={{
                          endAdornment: (
                            <span
                              className={
                                'flex items-center hover:cursor-pointer'
                              }
                            >
                              บาท
                            </span>
                          ),
                        }}
                        {...register('price')}
                        error={Boolean(hookFormErrors.price)}
                        value={watch('price')}
                        helperText={
                          hookFormErrors.price
                            ? (hookFormErrors.price.message as ReactNode)
                            : ''
                        }
                        onChange={(e) => onchangePrice(e)}
                      />
                      {/* {Number(watch('price')) > 0 && */}
                      {/*  invoice.balancePayment - Number(watch('price')) > */}
                      {/*    0.1 && ( */}
                      {/*    <span> */}
                      {/*      ต้องรับชำระเงินอีก :{' '} */}
                      {/*      {numberWithCommas( */}
                      {/*        ( */}
                      {/*          invoice.balancePayment - Number(watch('price')) */}
                      {/*        ).toFixed(2), */}
                      {/*        2 */}
                      {/*      )} */}
                      {/*    </span> */}
                      {/*  )} */}
                    </div>
                    <div className="date-time">
                      <div className="item">
                        <p>วันที่ชำระเงิน</p>
                        <Dialog
                          open={datePickerOpen}
                          onClose={() => setDatePickerOpen(false)}
                        >
                          <CalendarStyle>
                            <Calendar
                              date={new Date(moment().format('MM/DD/YYYY'))}
                              onChange={async (date) => {
                                setValue(
                                  'date',
                                  `${moment(date).format('YYYY-MM-DD')}`
                                );
                                setDatePickerOpen(false);
                              }}
                              locale={th}
                            />
                          </CalendarStyle>
                        </Dialog>
                        <DateTimeInputStyled
                          variant="outlined"
                          placeholder="ระบุวันที่"
                          fullWidth
                          InputProps={{
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                className="flex items-center hover:cursor-pointer"
                              >
                                <CalendarTodayOutlinedIcon
                                  onClick={() => setDatePickerOpen(true)}
                                />
                              </InputAdornment>
                            ),
                          }}
                          {...register('date')}
                          error={Boolean(hookFormErrors.date)}
                          value={watch('date')}
                          helperText={
                            hookFormErrors.date
                              ? (hookFormErrors.date.message as ReactNode)
                              : ''
                          }
                          onChange={() => {}}
                        />
                      </div>
                      <div className="item">
                        <p>เวลา</p>
                        <Dialog
                          open={timePickerOpen}
                          onClose={() => setTimePickerOpen(false)}
                        >
                          <div className="w-full h-full p-[12px] flex flex-col gap-2">
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <MultiSectionDigitalClock
                                views={['hours', 'minutes']}
                                ampm={false}
                                timeSteps={{ minutes: 1 }}
                                onChange={(time) => {
                                  setValue(
                                    'time',
                                    `${time.$H
                                      .toString()
                                      .padStart(2, '0')}:${time.$m
                                      .toString()
                                      .padStart(2, '0')}`
                                  );
                                }}
                              />
                            </LocalizationProvider>
                            <Button
                              variant="contained"
                              sx={{
                                bgcolor: '#1e293b',
                                '&:hover': { bgcolor: '#334155' },
                              }}
                              onClick={() => setTimePickerOpen(false)}
                            >
                              ตกลง
                            </Button>
                          </div>
                        </Dialog>
                        <DateTimeInputStyled
                          variant="outlined"
                          type="text"
                          placeholder="ระบุเวลา"
                          fullWidth
                          InputProps={{
                            startAdornment: (
                              <InputAdornment
                                position="start"
                                className="flex items-center hover:cursor-pointer"
                              >
                                <AccessTimeOutlinedIcon
                                  onClick={() => setTimePickerOpen(true)}
                                />
                              </InputAdornment>
                            ),
                          }}
                          {...register('time')}
                          error={Boolean(hookFormErrors.time)}
                          value={watch('time')}
                          helperText={
                            hookFormErrors.time
                              ? (hookFormErrors.time.message as ReactNode)
                              : ''
                          }
                          onChange={() => {}}
                        />
                      </div>
                    </div>
                    <div className="slip">
                      <p>หลักฐานการชำระเงิน</p>
                      <FilePickerStyled>
                        <input
                          type="file"
                          style={{ display: 'none' }}
                          {...register('file')}
                        />
                        {isNull(file) ? (
                          <div className="empty-upload">
                            <div>
                              <Image
                                src={'/icons/icon-upload.svg'}
                                width={24}
                                height={24}
                                alt=""
                              />
                            </div>
                            <p>JPEG, PDF, or GIF ขาดไฟล์ไม่เกิน 100 MB</p>
                            <h6>
                              ลากไฟล์มาวาง หรือ <u>เลือกไฟล์</u>
                            </h6>
                          </div>
                        ) : (
                          <div className="is-upload">
                            <div>
                              <Image
                                src={file}
                                width={80}
                                height={80}
                                alt="slip"
                              />
                            </div>
                            <div className="text">
                              <p>JPEG, PDF, or GIF ขาดไฟล์ไม่เกิน 100 MB</p>
                              <h6>
                                <u>เปลี่ยนรูป</u> หรือ{' '}
                                <u
                                  className="text-[#D32F2F]"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setFile(null);
                                    setValue('file', '');
                                  }}
                                >
                                  ลบรูปภาพ
                                </u>
                              </h6>
                            </div>
                          </div>
                        )}
                      </FilePickerStyled>
                      {hookFormErrors.file && (
                        <FormHelperText
                          error
                          sx={{
                            margin: '4px 14px 0',
                          }}
                        >
                          {hookFormErrors.file.message}
                        </FormHelperText>
                      )}
                    </div>
                    <div className="description">
                      <p>หมายเหตุ</p>
                      <TextField
                        multiline
                        rows={4}
                        placeholder="อธิบาย"
                        fullWidth
                        variant="outlined"
                        {...register('description')}
                        error={Boolean(hookFormErrors.description)}
                        value={watch('description')}
                      />
                    </div>
                    <Button
                      type="submit"
                      variant="contained"
                      color="dark"
                      fullWidth
                      sx={{
                        fontSize: '16px',
                        maxHeight: '40px',
                      }}
                    >
                      <div>สร้าง</div>
                    </Button>
                  </ModalUploadSlipStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadSlipDialog;
