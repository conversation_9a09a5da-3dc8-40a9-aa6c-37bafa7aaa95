import {
  <PERSON><PERSON>,
  CircularP<PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { Search } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FormModalStyle, LoadingFadein } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import apiQuotation from '@/services/order/qutation';
import { isEmpty, isNull, isUndefined } from 'lodash';
import apiInvoice from '@/services/order/invoice';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import { invoiceSelector, getInvoice } from '@/store/features/invoice';

import InfiniteScroll from 'react-infinite-scroll-component';

export const ModalQuotationReferenceStyled = styled.div`
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  max-height: 650px;
  min-height: 174px;
  align-items: center;
  gap: 16px;
  .empty-qt {
    width: 238px;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 12px;
    text-align: center;
    color: #cfd8dc;
    height: 350px;
    margin-top: 40px;
    font-size: 12px;
    animation: ${LoadingFadein} 0.3s ease-in;
    h4 {
      margin: 0;
      font-size: 16px;
    }
  }
  .qto-count {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    padding: 16px 0;
    border-bottom: 1px solid #dbe2e5;
    p {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.14px;
    }
  }
  .qt-items-wrap {
    min-height: 390px;
    max-height: 390px;
    width: 100%;
    overflow: auto;
    padding: 16px 0 16px;
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .qt-items {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      column-gap: 14px;
      cursor: pointer;
      border-radius: 8px;
      padding: 16px;
      transition: 0.3s;
      width: 100%;
      &:hover {
        background: #f5f7f8;
      }
      p {
        font-size: 12px;
        margin: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 4px;
      }
      &.active {
        background-color: #dbe2e5;
      }
    }
    .active {
      background: #f5f7f8;
    }
  }
  .btn-group {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    overflow: auto;
    position: relative;
  }
`;

type QuotationDialogProps = {
  children: React.ReactNode;
};

const initialFilters = {
  paymentQuotationStatusId: 4,
  page: 0,
  size: 10,
  startDate: null,
  endDate: null,
  search: '',
};

const QuotationDialog = ({ children }: QuotationDialogProps) => {
  const dispatch = useAppDispatch();
  const [quotationList, setQuotationList] = useState<any>([]);
  const [searchInput, setSearchInput] = useState<string>('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [timer, setTimer] = useState<any>(null);
  const [loadingInsertQuotation, setLoadingInsertQuotation] = useState(false);
  const [filters, setFilters] = useState<any>(initialFilters);
  const [selectedQuotation, setSelectedQuotation] = useState<any | null>(null);
  const [isShowContactModal, setShowQuotationModal] = useState(false);
  const { invoice } = useAppSelector(invoiceSelector);

  useEffect(() => {
    getQuotationList().then();
  }, []);

  useEffect(() => {
    getQuotationList().then();
  }, [filters.search]);

  const handleClose = () => {
    setShowQuotationModal(false);
  };

  const handleOpen = async () => {
    setQuotationList([]);
    setFilters(initialFilters);
    setSearchInput('');
    await getQuotationList();
    setShowQuotationModal(true);
  };

  const getQuotationList = async () => {
    const res = await apiQuotation.getQuotations(filters);
    if (!res.isError) {
      setQuotationList(res.data.content);
    }
  };

  const handleSearch = (event: any) => {
    const currentFilters = filters;
    setSearchInput(event.target.value);
    setLoadingSearch(true);
    clearTimeout(timer);
    const newTimer = setTimeout(() => {
      setLoadingSearch(false);
      setFilters({
        ...currentFilters,
        search: event.target.value,
      });
    }, 1000);
    setTimer(newTimer);
  };

  const handleInsertQuotation = async () => {
    setLoadingInsertQuotation(true);
    const req = {
      paymentInvoiceId: invoice.id,
      paymentQuotationId: isNull(selectedQuotation)
        ? null
        : selectedQuotation.id,
    };
    const res = await apiInvoice.insertQuotationToPaymentInvoice(req);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message,
          severity: 'success',
        })
      );
      dispatch(getInvoice(invoice.id));
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: res.message.message,
          severity: 'error',
        })
      );
    }
    setLoadingInsertQuotation(false);
    handleClose();
  };
  const fetchMoreData = () => {
    setFilters((value: any) => {
      return {
        ...value,
        size: value.size + 10,
      };
    });
  };
  return (
    <>
      <div
        onClick={() => {
          handleOpen();
        }}
      >
        {children}
      </div>
      <Dialog
        open={isShowContactModal}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เลือกรายการใบเสนอราคา (อ้างอิง)</div>
                <div className="x-close" onClick={() => handleClose()}>
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  style={{
                    rowGap: '0',
                  }}
                >
                  <TextField
                    className="fade-in"
                    fullWidth
                    value={searchInput}
                    onChange={(event: any) => {
                      handleSearch(event);
                    }}
                    placeholder="ค้นหา"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {loadingSearch ? (
                            <div className="h-[24px] w-[24px] flex items-center justify-center">
                              <CircularProgress size={20} />
                            </div>
                          ) : (
                            <Search />
                          )}
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      marginTop: '24px',
                    }}
                  />
                  <ModalQuotationReferenceStyled>
                    <div className="qto-count">
                      <p className="m-0">
                        {!isUndefined(quotationList) && quotationList.length}{' '}
                        รายการ
                      </p>
                    </div>
                    <div className="qt-items-wrap">
                      <InfiniteScroll
                        dataLength={quotationList?.length || 0}
                        next={fetchMoreData}
                        hasMore={true}
                        height={390}
                        style={
                          {
                            // height: refHeight.current?.clientHeight,
                          }
                        }
                        loader={''}
                      >
                        <div
                          className={
                            selectedQuotation === null
                              ? 'qt-items active'
                              : 'qt-items'
                          }
                          onClick={() => setSelectedQuotation(null)}
                        >
                          <p>ไม่อ้างอิงเลขที่ใบเสนอราคา</p>
                        </div>
                        {!isEmpty(quotationList) &&
                          quotationList.map((item: any, index: number) => (
                            <div
                              key={index}
                              className={
                                !isNull(selectedQuotation) &&
                                selectedQuotation.id === item.id
                                  ? 'qt-items active'
                                  : 'qt-items'
                              }
                              onClick={() => setSelectedQuotation(item)}
                            >
                              <p>{item.paymentQuotationNo}</p>
                              <p>{item.productQuantity} ชิ้น </p>
                            </div>
                          ))}
                      </InfiniteScroll>
                    </div>
                    <div className="btn-group">
                      <Button
                        type="button"
                        variant="outlined"
                        color="dark"
                        fullWidth
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        onClick={() => handleClose()}
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        type="button"
                        variant="contained"
                        color="dark"
                        fullWidth
                        disabled={
                          !isUndefined(selectedQuotation)
                            ? loadingInsertQuotation
                            : loadingInsertQuotation
                        }
                        sx={{
                          fontSize: '16px',
                          maxHeight: '40px',
                        }}
                        onClick={() => handleInsertQuotation()}
                      >
                        {loadingInsertQuotation ? (
                          <CircularProgress size={20} />
                        ) : (
                          <div>บันทึก</div>
                        )}
                      </Button>
                    </div>
                  </ModalQuotationReferenceStyled>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QuotationDialog;
