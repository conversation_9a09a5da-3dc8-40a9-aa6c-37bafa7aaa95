import React from 'react';
import styled from 'styled-components';
import { Avatar } from '@mui/material';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import { useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import { isNull } from 'lodash';
import moment from 'moment';

export const InvoiceHeaderDetailStyled = styled.div`
  width: 100%;
  padding: 40px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  border-bottom: 1px solid #dbe2e5;
  .header-details {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    gap: 40px;
    .header-item {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: stretch;
      flex-basis: 50%;
      .item-card {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        border: 1px solid #dbe2e5;
        border-radius: 16px 16px 16px 16px;
        .card-row {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          flex-basis: 50%;
          padding: 24px;
          border-bottom: 1px solid #dbe2e5;
          gap: 16px;
          p {
            margin: 0;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 0.12px;
          }
          h6 {
            margin: 0;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0.14px;
          }
        }
        .card-column {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: stretch;
          justify-content: stretch;
          flex-basis: 50%;
          .column-items {
            width: 100%;
            flex-grow: 1;
            text-align: left;
            padding: 24px;
            p {
              margin: 0;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              letter-spacing: 0.12px;
            }
            h6 {
              margin: 0;
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: normal;
              letter-spacing: 0.14px;
            }
            .item-title {
              width: 100%;
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
            }
            .item-profile {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: flex-start;
              gap: 12px;
            }
          }
          .border-left {
            border-left: 1px solid #dbe2e5;
          }
        }
      }
    }
  }
`;

const InvoiceHeaderDetails = () => {
  const { invoice } = useAppSelector(invoiceSelector);
  return (
    <>
      {!isNull(invoice) && (
        <InvoiceHeaderDetailStyled>
          <div className="header-details">
            <div className="header-item">
              <div className="item-card">
                <div className="card-row">
                  <Avatar
                    key={1}
                    alt="Remy Sharp"
                    src={
                      invoice.customer.imageUrl || '/images/profile-mockup.svg'
                    }
                    sx={{
                      width: 40,
                      height: 40,
                    }}
                  />
                  <div>
                    <h6>{invoice.customer.name}</h6>
                    <p>{`ลูกค้า • ${invoice.customer.contactType.name} ${
                      !isNull(invoice.customer.creditType)
                        ? `• เครดิต ${invoice.customer.creditType.day} วัน`
                        : ''
                    }`}</p>
                  </div>
                </div>
                <div className="card-column">
                  <div className="column-items">
                    <div className="item-title">
                      <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />
                      <p>รายการเสนอราคา</p>
                    </div>
                    <h6>
                      {isNull(invoice.paymentQuotationNo)
                        ? 'ไม่อ้างอิง'
                        : invoice.paymentQuotationNo}
                    </h6>
                  </div>
                  <div className="column-items border-left">
                    <div className="item-title">
                      <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />
                      <p>รายการเสนอราคา</p>
                    </div>
                    <h6>
                      {isNull(invoice.layOrderNo)
                        ? 'ไม่อ้างอิง'
                        : invoice.layOrderNo}
                    </h6>
                  </div>
                  <div className="column-items border-left">
                    <div className="item-title">
                      <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />
                      <p>รายการเสนอราคา</p>
                    </div>
                    <h6>{invoice.productQuantity} รายการ</h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="header-item">
              <div className="item-card">
                <div className="card-row">
                  <p>หมายเหตุ {invoice.remark}</p>
                </div>
                <div className="card-column">
                  <div className="column-items">
                    <div className="item-title">
                      <CalendarTodayOutlinedIcon
                        sx={{ width: 16, height: 16 }}
                      />
                      <p>วันที่สร้าง</p>
                    </div>
                    <h6>{moment(invoice.createdDate).format('DD/MM/YYYY')}</h6>
                  </div>
                  <div className="column-items border-left">
                    <div className="item-title">
                      <CalendarTodayOutlinedIcon
                        sx={{ width: 16, height: 16 }}
                      />
                      <p>วันที่ใช้ได้ถึง</p>
                    </div>
                    <h6>{moment(invoice.dueDate).format('DD/MM/YYYY')}</h6>
                  </div>
                  <div className="column-items border-left">
                    <div className="item-profile">
                      <Avatar
                        key={1}
                        alt="Remy Sharp"
                        src={
                          invoice.createUser.limageUrl ||
                          '/images/profile-mockup.svg'
                        }
                        sx={{
                          width: 32,
                          height: 32,
                        }}
                      />
                      <div>
                        <h6>{invoice.createUser.name}</h6>
                        <p>ผู้ดูแล</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </InvoiceHeaderDetailStyled>
      )}
    </>
  );
};

export default InvoiceHeaderDetails;
