import React, { useState } from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { numberWithCommas } from '@/utils/number';
import { Button } from '@mui/material';
import ModalUploadSlip from '@/components/accounting-finance/invoice/modal/ModalUploadSlip';
import RequestQuoteOutlinedIcon from '@mui/icons-material/RequestQuoteOutlined';
import { useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import { isNull } from 'lodash';
import MoneyRoundedIcon from '@mui/icons-material/MoneyRounded';
import Image from 'next/image';
import dayjs from 'dayjs';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import RequestPageOutlinedIcon from '@mui/icons-material/RequestPageOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import { useRouter } from 'next/router';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import utc from 'dayjs/plugin/utc';

dayjs.extend(customParseFormat);
dayjs.extend(utc);
export const InvoicePaymentDetailStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px;
  margin-top: 8px;
  border-top: 1px solid #dbe2e5;
  border-bottom: 1px solid #dbe2e5;
  .card-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-left: 1px solid #dbe2e5;
    border-top: 1px solid #dbe2e5;
    border-right: 1px solid #dbe2e5;
    border-radius: 24px 24px 0 0;
    overflow: hidden;
    padding: 16px 24px;
    h3 {
      margin: 0;
      font-size: 22px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      letter-spacing: 0.22px;
    }
  }
  .card-body {
    width: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #dbe2e5;
    border-radius: 0 0 24px 24px;
    overflow: hidden;
    .body-header {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: stretch;
      border-bottom: 1px solid #dbe2e5;
      .header-item {
        width: 100%;
        flex-grow: 1;
        padding: 24px;
        display: flex;
        flex-direction: column;
        justify-content: start;
        gap: 16px;
        h6 {
          margin: 0;
          font-size: 22px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          letter-spacing: 0.22px;
        }
        p {
          margin: 0;
          font-size: 12px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          letter-spacing: 0.12px;
        }
      }
      .border-left {
        border-left: 1px solid #dbe2e5;
      }
    }
    .body-table {
      width: 100%;
      display: flex;
      flex-direction: column;
    }
  }
`;

const InvoicePaymentDetails = () => {
  const router = useRouter();
  const { invoice } = useAppSelector(invoiceSelector);
  const [totalElements] = useState(0);
  const [loading] = useState<boolean>(false);
  const columns: GridColDef[] = [
    {
      field: 'index',
      headerName: 'รายการ',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 180,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="row-id">
            <span>รับชำระเงินครั้งที่ {params.row.index}</span>
          </div>
        );
      },
    },
    {
      field: 'bankCompany',
      headerName: 'ช่องทางการรับชำระเงิน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <>
            {!isNull(params.row.bankCompany) ? (
              <div className="flex items-center gap-[16px] py-[8px]">
                {params.row.bankCompany.bankAccount.logoUrl && (
                  <Image
                    className="rounded-[6px] overflow-hidden"
                    src={params.row.bankCompany.bankAccount.logoUrl}
                    width={32}
                    height={32}
                    alt=""
                  />
                )}
                {`${params.row.bankCompany.bankAccount.name} - ${params.row.bankCompany.nameTH}`}
              </div>
            ) : (
              <div className="flex items-center gap-[16px] py-[8px] w-full">
                <div className="w-[32px] h-[32px] min-w-[32px] bg-[#F5F7F8] rounded-[8px] flex items-center justify-center">
                  <MoneyRoundedIcon className="w-[24px] h-[24px]" />
                </div>
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100%',
                  }}
                >
                  เงินสด - ชำระหน้าร้าน
                </span>
              </div>
            )}
          </>
        );
      },
    },
    {
      field: 'price',
      headerName: 'มูลค่าที่รับชำระรวม',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 250,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return numberWithCommas(params.row.price, 2);
      },
    },
    {
      field: 'urlFile',
      headerName: 'หลักฐาน',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 250,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <>
            {!isNull(params.row.urlFile) ? (
              <Image
                className="rounded-[6px] overflow-hidden"
                src={params.row.urlFile}
                width={32}
                height={32}
                alt=""
              />
            ) : (
              <>-</>
            )}
          </>
        );
      },
    },
    {
      field: 'paymentDate',
      headerName: 'รับชำระเมื่อวันที่',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 250,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        const { paymentDate, paymentTime } = params.row;

        const combinedTimestamp = paymentDate + paymentTime;

        const paymentAt = dayjs(combinedTimestamp).utc();

        return <div>{paymentAt.format('DD/MM/YYYY HH:mm')} น.</div>;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      width: 250,
      disableColumnMenu: true,
      sortable: false,
      cellClassName: 'stickyCell',
      renderCell: () => {
        return (
          <div
            className="flex items-center p-[8px] rounded-[8px] hover:cursor-pointer border border-solid border-[#DBE2E5]"
            // onClick={() => handleDeleteItem(params.row.id)}
          >
            <MoreHorizIcon />
          </div>
        );
      },
    },
  ];

  return (
    <InvoicePaymentDetailStyled>
      <div className="card-header">
        <h3>ข้อมูลชำระเงิน</h3>
        {invoice.invoicesStatus.id < 6 && invoice.balancePayment > 0 && (
          <ModalUploadSlip>
            <Button
              variant="contained"
              startIcon={<RequestQuoteOutlinedIcon />}
              sx={{
                bgcolor: '#1e293b',
                '&:hover': { bgcolor: '#334155' },
              }}
            >
              แจ้งชำระเงิน
            </Button>
          </ModalUploadSlip>
        )}
        {invoice.invoicesStatus.id === 4 && (
          <div className="flex flex-row items-center justify-end gap-[24px]">
            <div
              className="flex flex-row items-center justify-end gap-[8px] hover:cursor-pointer"
              onClick={() =>
                router.push(
                  `/accounting-finance/receipt/${invoice.paymentReceiptId}`
                )
              }
            >
              <RequestPageOutlinedIcon />
              <p>
                <u>{invoice.paymentReceiptNo}</u>
              </p>
            </div>
            <div className="flex items-center p-[8px] rounded-[8px] hover:cursor-pointer border border-solid border-[#DBE2E5]">
              <FileDownloadOutlinedIcon />
            </div>
          </div>
        )}
      </div>
      <div className="card-body">
        <div className="body-header">
          <div className="header-item">
            <p>มูลค่าที่รับชำระแล้ว {invoice.transaction.length} รายการ</p>
            <h6>{numberWithCommas(invoice.paidPayment, 2)}</h6>
          </div>
          <div className="header-item border-left">
            <p>ยอดที่ต้องรับชำระทั้งสิ้น</p>
            <h6>{numberWithCommas(invoice.totalPrice, 2)}</h6>
          </div>
          <div className="header-item border-left">
            <p>ค้างชำระเงิน (บาท)</p>
            <h6>{numberWithCommas(invoice.balancePayment, 2)}</h6>
          </div>
        </div>
        <div className="body-table">
          <AppTableStyle $rows={invoice.transaction}>
            <div className="content-wrap">
              <ScrollBarStyled>
                {/* <HeaderColumnAction text="จัดการ" width={144} /> */}
                <DataGrid
                  hideFooter={true}
                  rows={invoice.transaction}
                  getRowId={(row) => row.index}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  loading={loading}
                  components={{
                    NoRowsOverlay: TableNoRowsOverlay,
                    LoadingOverlay: TableLoadingOverlay,
                  }}
                />
              </ScrollBarStyled>
            </div>
          </AppTableStyle>
        </div>
      </div>
    </InvoicePaymentDetailStyled>
  );
};

export default InvoicePaymentDetails;
