import React from 'react';
import styled from 'styled-components';
import { useAppSelector } from '@/store';
import { invoiceSelector } from '@/store/features/invoice';
import { isNull } from 'lodash';

export const CreateInvoiceHeaderStyled = styled.div`
  position: sticky;
  width: 100%;
  padding: 40px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  border-bottom: 1px solid #dbe2e5;
  .invoice-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
    > h1 {
      margin: 0;
      font-size: 40px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
    .header-status {
      padding: 4px 8px;
      border-radius: 8px;
      border: 1px solid #dbe2e5;
      background-color: #f5f7f8;
      color: #263238;
      > p {
        margin: 0;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.14px;
      }
    }
  }
`;

const InvoiceHeaderStatus = () => {
  const { invoice } = useAppSelector(invoiceSelector);
  return (
    <>
      {!isNull(invoice) && (
        <CreateInvoiceHeaderStyled>
          <div className="invoice-header">
            <h1>{invoice.invoiceNo}</h1>
            <div className="header-status">
              <p>{invoice.invoicesStatus.name}</p>
            </div>
          </div>
        </CreateInvoiceHeaderStyled>
      )}
    </>
  );
};

export default InvoiceHeaderStatus;
