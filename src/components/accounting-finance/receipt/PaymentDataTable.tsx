import React, { useEffect, useState } from 'react';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableTools from '@/components/global/TableTools';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import styled from 'styled-components';
import KebabTable from '@/components/KebabTable';
import HeaderColumnAction from '@/components/HeaderColumnAction';

const PaymentDataTableStyles = styled.div`
  min-height: calc(100vh - 88px);
  .box-content-wrap {
    height: 100%;
    > div {
      height: 100%;
      .content-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .box-pagination {
          padding-bottom: 1rem;
        }
      }
    }
  }
`;
type Props = {
  data: any;
  handleOpen: () => void;
  setDataEdit: (data: any) => void;
  onDelete: (id: number) => void;
};
const PaymentDataTable = ({
  data,
  handleOpen,
  setDataEdit,
  onDelete,
}: Props) => {
  const [rows, setRows] = useState<any[]>([]);
  const [totalElements] = useState(0);
  const columns: GridColDef[] = [
    {
      field: 'no',
      headerName: '#',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.3,
    },
    {
      field: 'name',
      headerName: 'ชื่อรายการสินค้าใน PEAK',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'รายละเอียด',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return params.row.description || '-';
      },
    },
    {
      field: 'actions',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 128,
      flex: 0.3,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <KebabTable
            item={''}
            isEdit={{
              status: true,
              action: () => onOpenSetDataEdit(params.row),
            }}
            isDelete={{ status: true, action: () => onDelete(params.row.id) }}
          />
        );
      },
    },
  ];
  const onOpenSetDataEdit = (data: any) => {
    setDataEdit(data);
    handleOpen();
  };
  useEffect(() => {
    if (data) {
      const newData = data.map((item: any, index: number) => {
        return { ...item, no: index + 1 };
      });
      setRows(newData);
    }
  }, [data]);
  return (
    <PaymentDataTableStyles>
      <div className="box-content-wrap">
        <AppTableStyle $rows={rows}>
          <div className="content-wrap">
            <div>
              <TableTools
                tools={['search']}
                title={`${rows.length || 0} รายการ`}
                makeNewFilter={(_newFilter: any) => {}}
              />
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={88} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={totalElements || 0}
                  // pageSize={filters.size}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
            {/* <div className="box-pagination px-[16px]"> */}
            {/*  <AppPagination */}
            {/*    filters={filters} */}
            {/*    totalElements={totalElements || 0} */}
            {/*    handleChangeFilters={(newValues: any) => { */}
            {/*      setFilters(newValues); */}
            {/*    }} */}
            {/*  /> */}
            {/* </div> */}
          </div>
        </AppTableStyle>
      </div>
    </PaymentDataTableStyles>
  );
};

export default PaymentDataTable;
