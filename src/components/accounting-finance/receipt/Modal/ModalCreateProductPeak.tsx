import React, { ReactNode, useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import styled from 'styled-components';
import { TextField } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { isEmpty } from 'lodash';

const ModalCreateProductPeakStyle = styled.div`
  max-width: 580px;
  width: 580px !important;
  header {
    position: relative;
    h2 {
      text-align: center;
      border-bottom: 1px solid#DBE2E5;
    }
    button {
      position: absolute;
      top: 10px;
      right: 5px;
      border: none;
      padding: 5px !important;
      min-width: 40px !important;
      min-height: 40px !important;
      border-radius: 50%;
      &:hover {
        border: none;
      }
      svg {
        color: #cfd8dc;
      }
    }
  }

  .MuiDialogContent-root {
    padding: 24px !important;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    .field-label {
      .MuiInputBase-root {
        &:hover,
        &:focus,
        .Mui-focus {
          box-shadow: unset;
        }
      }
      fieldset {
        &:hover,
        &:focus {
          border-color: unset !important;
        }
      }
      .Mui-error {
        font-size: 12px !important;
      }

      p {
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        margin: 0;
        padding-bottom: 0.2rem;
      }
    }
  }
  .MuiDialogActions-root {
    padding: 0 24px 24px 24px !important;
    justify-content: center;
    button {
      &:first-child {
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        background: #fff;
        box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.08);
      }
    }
  }
`;
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อรายการสินค้า PEAK'),
});
type Props = {
  open: boolean;
  handleClose: () => void;
  onActionPeakProduct: (data: any) => void;
  dataEdit: any;
};
const ModalCreateProductPeak = ({
  open,
  handleClose,
  onActionPeakProduct,
  dataEdit,
}: Props) => {
  const [initialValue] = useState<any>({
    name: '',
    description: '',
  });
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });
  const onSubmit = async (values: any) => {
    const data = isEmpty(dataEdit)
      ? {
          ...values,
          description: values.description || null,
        }
      : {
          ...dataEdit,
          name: values.name,
          description: values.description || null,
        };
    onActionPeakProduct(data);
    reset();
  };
  useEffect(() => {
    if (!isEmpty(dataEdit)) {
      setValue('name', dataEdit.name);
      setValue('description', dataEdit.description);
    } else {
      reset();
    }
  }, [dataEdit]);
  return (
    <Dialog open={open} onClose={handleClose}>
      <ModalCreateProductPeakStyle>
        <header>
          <DialogTitle>สร้างรายการสินค้า PEAK</DialogTitle>
          <Button onClick={handleClose} variant={'outlined'}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent>
            <div className={'field-label'}>
              <p>ชื่อรายการสินค้า PEAK</p>
              <TextField
                {...register('name')}
                variant="outlined"
                placeholder={'ระบุรายการสินค้า PEAK เช่น ค่าเร่งการผลิต'}
                error={Boolean(hookFormErrors.name)}
                helperText={hookFormErrors.name?.message as ReactNode}
              />
            </div>
            <div className={'field-label'}>
              <p>รายละเอียด</p>
              <TextField
                {...register('description')}
                variant="outlined"
                placeholder={'รายละเอียด'}
                multiline={true}
                rows={6}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              className={'w-1/2'}
              variant={'outlined'}
              onClick={handleClose}
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              className={'w-1/2'}
              variant={'contained'}
              autoFocus
            >
              สร้าง
            </Button>
          </DialogActions>
        </form>
      </ModalCreateProductPeakStyle>
    </Dialog>
  );
};

export default ModalCreateProductPeak;
