import React from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';
import AddCircleRoundedIcon from '@mui/icons-material/AddCircleRounded';
import Image from 'next/image';
import AppDateRange from '@/components/global/AppDateRange';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

type Props = {
  title: string;
  onAction?: () => void;
  filters?: any;
  setFilters?: (date: any) => void;
};
const PaymentNavStyle = styled.div`
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  h2 {
    margin: 0;
  }
  .box-profile-action {
    display: flex;
    align-items: center;
    gap: 1rem;
    button {
      color: #fff;
      padding: 8px 24px;
      border-radius: 8px;
      border: 1px solid #00c1af;
      background: linear-gradient(180deg, #16d5c5 0%, #12beb2 100%);
    }
    .profile {
      border-radius: 50%;
      overflow: hidden;
      width: 40px;
      height: 40px;
    }
  }
`;
const PaymentNav = ({ title, onAction, setFilters, filters }: Props) => {
  const { permissions } = useAppSelector(permissionSelector);
  return (
    <PaymentNavStyle>
      <div>
        <h2>{title}</h2>
      </div>
      <div className={'box-profile-action'}>
        {onAction && (
          <Button
            sx={{
              background: !isAllowed(permissions, 'company.payment.create')
                ? '#dedede !important'
                : '',
              border: !isAllowed(permissions, 'company.payment.create')
                ? 'unset !important'
                : '',
            }}
            disabled={!isAllowed(permissions, 'company.payment.create')}
            startIcon={<AddCircleRoundedIcon />}
            {...(onAction && { ...{ onClick: onAction } })}
          >
            สร้างรายการ
          </Button>
        )}
        {setFilters && (
          <AppDateRange
            data={{
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            handleChange={(dateRange: any) => {
              setFilters({
                ...filters,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              });
            }}
          />
        )}
        <div className={'profile'}>
          <Image
            src={'/images/product/empty-product.svg'}
            alt={'profile'}
            width={40}
            height={40}
          />
        </div>
      </div>
    </PaymentNavStyle>
  );
};

export default PaymentNav;
