import React, { useState } from 'react';
import Image from 'next/image';
import { Button, CircularProgress } from '@mui/material';
import { CompanyCardWrapStyle } from '@/components/company/CompanyList';
import apiCompany from '@/services/core/company';
import { useAppDispatch, useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import EastRoundedIcon from '@mui/icons-material/EastRounded';
import { setCookie } from 'cookies-next';
import { useRouter } from 'next/router';
import { setSnackBar } from '@/store/features/alert';

type CompanyCardProps = {
  data: any;
  type: string;
  reLoadData?: () => void;
};
const CompanyCard = (props: CompanyCardProps) => {
  const { data, type, reLoadData } = props;
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { user } = useAppSelector(userSelector);
  const [loading, setLoading] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const handleAcceptInvite = async (companyId: number, name: string) => {
    setLoading(true);
    const data = {
      email: user.email,
      companyId,
    };
    const res = await apiCompany.acceptInviteCompany(data);
    if (!res.isError) {
      setDisable(true);
      dispatch(
        setSnackBar({
          status: true,
          text: `เข้าร่วม ${name} สำเร็จ`,
          severity: 'success',
        })
      );
      if (reLoadData) {
        reLoadData();
      }
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: `เกิดข้อผิดพลาด`,
          severity: 'error',
        })
      );
    }
    setLoading(false);
  };
  const selectCompany = async (id: number) => {
    const res = await apiCompany.switchCompany(id);
    if (!res.isError) {
      setCookie('selected-company-id', id);
      await router.push('/dashboard');
    }
  };
  return (
    <>
      <CompanyCardWrapStyle
        onClick={() => {
          if (type === 'myCompany') {
            selectCompany(data.id);
          }
        }}
        style={{
          cursor: type === 'invite' ? 'default' : 'pointer',
        }}
      >
        <div className="group">
          <div className="logo">
            <Image
              src={data.logo || '/images/company/default-logo.png'}
              width={92}
              height={92}
              alt=""
            />
          </div>
          <div className="text-wrap">
            <div className="name">{data.name}</div>
            <div className="description">
              {`${data.role} • ${data.businessType.name}`}
            </div>
          </div>
        </div>
        <div className="group">
          {type === 'invite' ? (
            <Button
              type="button"
              variant="contained"
              color="dark"
              fullWidth
              disabled={disable}
              sx={{
                width: '108px !important',
                height: '40px !important',
                fontWeight: '400',
              }}
              onClick={() => {
                handleAcceptInvite(data.id, data.name);
              }}
            >
              {loading ? (
                <CircularProgress size={20} />
              ) : (
                <span className="mt-[2px]">เข้าร่วม</span>
              )}
            </Button>
          ) : (
            <div className="arrow">
              <EastRoundedIcon fontSize="small" />
            </div>
          )}
        </div>
      </CompanyCardWrapStyle>
    </>
  );
};

export default CompanyCard;
