import React, { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import { FadeInStyled, LoadingFadein } from '@/styles/share.styled';
import ImageField from '@/components/ImageField';
import CameraAltRoundedIcon from '@mui/icons-material/CameraAltRounded';
import {
  Button,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
} from '@mui/material';
import { isEmpty } from 'lodash';
import { useFormik } from 'formik';
import * as yup from 'yup';
import apiCredit from '@/services/core/credit';
import { RadioStyle } from '@/components/raw-material/RawMaterialForm';
import apiDeposit from '@/services/core/depositType';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { FocusError } from 'focus-formik-error';

const ContactFormStyle = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 40px;
  animation: ${LoadingFadein} 0.3s ease-in;
  width: 100%;
  form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 640px;
    max-width: 100%;
    padding: 0 24px;
  }
  .field-title {
    margin-top: 24px;
    margin-bottom: 8px;
    font-weight: 600;
  }
`;

export const ContactTypeSelect = styled.div`
  width: 100%;
  display: flex;
  gap: 8px;
  .btn-type {
    display: flex;
    flex: 1 1 50%;
    align-items: center;
    justify-content: center;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    &.active {
      box-shadow: #263238 0px 0px 0px 2px inset;
      font-weight: 600;
    }
    .check-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: #263238;
    }
  }
`;
type ContactFormProps = {
  submitting: boolean;
  disable: boolean;
  handleSubmitContact: (value: any, file: any) => void;
  initialValues?: any;
  contactRoleTypeId?: number;
};
const ContactForm = ({
  submitting,
  disable,
  handleSubmitContact,
  initialValues,
  contactRoleTypeId,
}: ContactFormProps) => {
  const [regexContactType, setRegexContactType] = useState({
    regex: /^[\u0E00-\u0E7Fa-zA-Z0-9\s]+$/,
    message: 'กรุณากรอกตัวอักษาไทย, อังกฤษ, ตัวเลข',
    placeholder: 'ระบุชื่อจริง นามสกุล, ชื่อบริษัท',
  });
  const [file, setFile] = useState<any>(null);
  const [creditOption, setCreditOption] = useState([]);
  const [depositOption, setDepositOption] = useState<any>([]);
  const validationSchema = yup.object({
    name: yup
      .string()
      .required('กรุณากรอกชื่อ นามสกุล, ชื่อบริษัท')
      .matches(regexContactType.regex, regexContactType.message),
    taxNumber: yup
      .string()
      .matches(/^[0-9]{13}$/, 'ต้องมี 13 หลัก')
      .required('กรุณากรอกหมายเลข'),
    creditTypeId: yup.number().when('isCredit', {
      is: true,
      then: (schema) => schema.required('กรุณาระบุเครดิต'),
      otherwise: (schema) => schema.notRequired(),
    }),
    phoneNumber: yup
      .string()
      .min(10, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
      .max(10, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
      .matches(/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
      .required('กรุณากรอกเบอร์โทรศัพท์'),
    depositTypeId: yup.number().required('กรุณากรอกเลือก'),
    customerLevel: yup.number().when('contactRoleId', {
      is: 1,
      then: (schema) => schema.required('กรุณากรอกเลือกระดับลูกค้า'),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
  const formik = useFormik({
    initialValues: !isEmpty(initialValues)
      ? {
          id: initialValues.id,
          imageUrl: initialValues?.imageUrl,
          name: initialValues.name,
          taxNumber: initialValues.taxNumber,
          creditTypeId: initialValues.creditType?.id || null,
          contactTypeId: initialValues.contactType.id,
          phoneNumber: initialValues.phoneNumber,
          email: initialValues.email,
          contactRoleId: initialValues.contactRole.id,
          isCredit: initialValues.credit ?? false,
          depositTypeId: initialValues.depositType?.id,
          customerLevel: initialValues.customerLevel,
          note: initialValues.note,
        }
      : {
          name: '',
          taxNumber: '',
          creditTypeId: '',
          contactTypeId: 2,
          phoneNumber: '',
          email: '',
          contactRoleId: contactRoleTypeId || 1,
          depositTypeId: 1,
          customerLevel: null,
          note: '',
        },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      const sendData = {
        ...values,
      };
      handleSubmitContact(sendData, file);
    },
  });
  useEffect(() => {
    if (formik.values.contactTypeId === 1) {
      setRegexContactType({
        regex: /^[\u0E00-\u0E7Fa-zA-Z0-9\s]+$/,
        message: 'กรุณากรอกตัวอักษาไทย, อังกฤษ, ตัวเลข',
        placeholder: 'ระบุชื่อจริง นามสกุล, ชื่อบริษัท',
      });
    } else {
      setRegexContactType({
        regex: /^[\u0E00-\u0E7Fa-zA-Z\s]+$/,
        message: 'กรุณากรอกตัวอักษาไทย, อังกฤษ',
        placeholder: 'ระบุชื่อจริง นามสกุล',
      });
    }
  }, [formik.values.contactTypeId]);
  const makeFile = async (imageFile: any) => {
    if (imageFile && imageFile.length === 1) {
      const formData = new FormData();
      formData.append('file', imageFile[0]);
      setFile(formData);
    } else {
      setFile(null);
    }
  };
  const handleChangeCredit = (value: number) => {
    formik.setFieldValue('creditTypeId', value);
  };
  const handleChangeDepositType = (value: number) => {
    formik.setFieldValue('depositTypeId', value);
  };

  const getCreditOption = async () => {
    const res = await apiCredit.getCreditOption();
    if (!res.isError) {
      setCreditOption(res);
    }
  };

  const getDepositOption = async () => {
    const res = await apiDeposit.getDepositType();
    if (!res.isError) {
      setDepositOption(res.data);
    }
  };
  useEffect(() => {
    getCreditOption();
    getDepositOption();
  }, []);
  useEffect(() => {
    if (!formik.values.isCredit) {
      formik.setFieldValue('creditTypeId', null);
    }
  }, [formik.values.isCredit]);
  return (
    <ContactFormStyle>
      <form onSubmit={formik.handleSubmit}>
        <FocusError formik={formik} />
        <ImageField
          textUploadBtn="Upload"
          defaultBackground={
            initialValues?.imageUrl || '/icons/blank-profile.svg'
          }
          handleChange={(files: any) => {
            makeFile(files);
          }}
          iconButton={
            <CameraAltRoundedIcon
              sx={{
                fontSize: '20px',
              }}
            />
          }
        />

        <div className="mt-[12px]" />
        <p className="field-title">
          {contactRoleTypeId === 2 ? `ประเภทตัวแทนจำหน่าย` : `ประเภทลูกค้า`}
        </p>
        <Select
          name="contactTypeId"
          value={formik.values.contactTypeId}
          onChange={(e) =>
            formik.setFieldValue('contactTypeId', e.target.value)
          }
          displayEmpty
          sx={{
            fontSize: '14px',
            height: '40px',
          }}
        >
          <MenuItem
            disabled
            value=""
            sx={{
              fontSize: '14px',
            }}
          >
            <em className="text-[#78909C]">เลือกประเภทลูกค้า</em>
          </MenuItem>
          <MenuItem
            value={2}
            sx={{
              fontSize: '14px',
            }}
          >
            บุคคลธรรมดา
          </MenuItem>
          <MenuItem
            value={1}
            sx={{
              fontSize: '14px',
            }}
          >
            นิติบุคคล
          </MenuItem>
        </Select>
        <p className="field-title">ชื่อ</p>
        <TextField
          type="text"
          name="name"
          placeholder={regexContactType.placeholder}
          value={formik.values.name}
          onChange={formik.handleChange}
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && (formik.errors.name as ReactNode)}
        />
        <p className="field-title">
          {formik.values.contactTypeId === 2
            ? 'เลขบัตรประชาชน'
            : 'เลขผู้เสียภาษี'}
        </p>
        <TextField
          type="text"
          name="taxNumber"
          placeholder={
            formik.values.contactTypeId === 2
              ? `หมายเลขบัตรประชาชน 13 หลัก`
              : `หมายเลขผู้เสียภาษี`
          }
          value={formik.values.taxNumber}
          onChange={formik.handleChange}
          inputProps={{ maxLength: 13 }}
          onKeyPress={(event) => {
            if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
              event.preventDefault();
            }
          }}
          error={formik.touched.taxNumber && Boolean(formik.errors.taxNumber)}
          helperText={
            formik.touched.taxNumber && (formik.errors.taxNumber as ReactNode)
          }
        />
        <p className="field-title">โทรศัพท์</p>
        <TextField
          name="phoneNumber"
          placeholder="หมายเลขโทรศัพท์"
          value={formik.values.phoneNumber}
          onChange={formik.handleChange}
          inputProps={{ maxLength: 10 }}
          onKeyPress={(event) => {
            if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
              event.preventDefault();
            }
          }}
          error={
            formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)
          }
          helperText={
            formik.touched.phoneNumber &&
            (formik.errors.phoneNumber as ReactNode)
          }
        />
        <p className="field-title">อีเมล</p>
        <TextField
          type="email"
          name="email"
          placeholder="อีเมล"
          value={formik.values.email}
          onChange={formik.handleChange}
          error={formik.touched.email && Boolean(formik.errors.email)}
          helperText={
            formik.touched.email && (formik.errors.email as ReactNode)
          }
        />
        {contactRoleTypeId !== 2 && (
          <>
            <p className="field-title">ระดับลูกค้า</p>
            <RadioGroup
              name="customerLevel"
              value={formik.values.customerLevel}
              onChange={(e) =>
                formik.setFieldValue('customerLevel', Number(e.target.value))
              }
            >
              <FormControlLabel
                value={1}
                control={
                  <Radio
                    icon={<FiberManualRecordIcon />}
                    checkedIcon={<FiberManualRecordIcon className={'p-1'} />}
                    sx={RadioStyle}
                  />
                }
                label={'ลูกค้าทั่วไป'}
              />
              <FormControlLabel
                value={2}
                control={
                  <Radio
                    icon={<FiberManualRecordIcon />}
                    checkedIcon={<FiberManualRecordIcon className={'p-1'} />}
                    sx={RadioStyle}
                  />
                }
                label={'VIP'}
              />
              <FormControlLabel
                value={3}
                control={
                  <Radio
                    icon={<FiberManualRecordIcon />}
                    checkedIcon={<FiberManualRecordIcon className={'p-1'} />}
                    sx={RadioStyle}
                  />
                }
                label={'OEM'}
              />
            </RadioGroup>
            {formik.touched.customerLevel && formik.errors.customerLevel && (
              <FormHelperText error>
                {formik.errors.customerLevel as ReactNode}
              </FormHelperText>
            )}
          </>
        )}

        {(initialValues === undefined || !isEmpty(initialValues)) &&
          !isEmpty(creditOption) && (
            <>
              <p className="field-title">ประเภทการชำระเงิน</p>
              <RadioGroup
                name="isCredit"
                value={formik.values.isCredit}
                onChange={(e) =>
                  formik.setFieldValue('isCredit', e.target.value === 'true')
                }
              >
                <FormControlLabel
                  value={false}
                  control={
                    <Radio
                      icon={<FiberManualRecordIcon />}
                      checkedIcon={<FiberManualRecordIcon className={'p-1'} />}
                      sx={RadioStyle}
                    />
                  }
                  label={'เงินสด หรือเงินโอน'}
                />
                <FormControlLabel
                  value={true}
                  control={
                    <Radio
                      icon={<FiberManualRecordIcon />}
                      checkedIcon={<FiberManualRecordIcon className={'p-1'} />}
                      sx={RadioStyle}
                    />
                  }
                  label={'ชำระเงินเครดิต'}
                />
              </RadioGroup>
              {formik.values.isCredit && (
                <FadeInStyled>
                  <p className="field-title">เครดิต</p>
                  <FormControl
                    fullWidth
                    error={
                      formik.touched.creditTypeId &&
                      Boolean(formik.errors.creditTypeId)
                    }
                    sx={{ boxShadow: 'none' }}
                  >
                    <Select
                      name="credit"
                      value={formik.values.creditTypeId || ''}
                      onChange={(e) => handleChangeCredit(e.target.value)}
                      displayEmpty
                      sx={{
                        fontSize: '14px',
                        height: '40px',
                      }}
                    >
                      <MenuItem
                        disabled
                        value=""
                        sx={{
                          fontSize: '14px',
                        }}
                      >
                        <div className="text-[#78909C]">เลือก</div>
                      </MenuItem>
                      {creditOption.map((item: any, index: React.Key) => (
                        <MenuItem
                          key={index}
                          value={item.id}
                          sx={{
                            fontSize: '14px',
                          }}
                        >
                          {item.day} วัน
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.creditTypeId &&
                      formik.errors.creditTypeId && (
                        <FormHelperText error>
                          {formik.errors.creditTypeId as ReactNode}
                        </FormHelperText>
                      )}
                  </FormControl>
                </FadeInStyled>
              )}
            </>
          )}
        <FadeInStyled>
          <p className="field-title">มัดจำ</p>
          <FormControl
            fullWidth
            error={
              formik.touched.depositTypeId &&
              Boolean(formik.errors.depositTypeId)
            }
            sx={{ boxShadow: 'none' }}
          >
            <Select
              name="credit"
              value={formik.values.depositTypeId || 1}
              onChange={(e) => handleChangeDepositType(e.target.value)}
              displayEmpty
              sx={{
                fontSize: '14px',
                height: '40px',
              }}
            >
              <MenuItem
                disabled
                value=""
                sx={{
                  fontSize: '14px',
                }}
              >
                <div className="text-[#78909C]">เลือก</div>
              </MenuItem>
              {depositOption.map((item: any, index: React.Key) => (
                <MenuItem
                  key={index}
                  value={item.id}
                  sx={{
                    fontSize: '14px',
                  }}
                >
                  {item.name}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.depositTypeId && formik.errors.depositTypeId && (
              <FormHelperText error>
                {formik.errors.depositTypeId as ReactNode}
              </FormHelperText>
            )}
          </FormControl>
        </FadeInStyled>
        <p className="field-title">Note</p>
        <TextField
          type="note"
          name="note"
          placeholder="ข้อความ"
          multiline
          minRows={6}
          value={formik.values.note}
          onChange={formik.handleChange}
          error={formik.touched.note && Boolean(formik.errors.note)}
          helperText={formik.touched.note && (formik.errors.note as ReactNode)}
        />

        <Button
          type="submit"
          variant="contained"
          color="dark"
          disabled={disable}
          fullWidth
          sx={{ fontSize: '16px', margin: '40px 0 24px 0' }}
        >
          {submitting ? (
            <CircularProgress
              size={25}
              sx={{
                color: 'white',
              }}
            />
          ) : (
            'บันทึก'
          )}
        </Button>
      </form>
    </ContactFormStyle>
  );
};

export default ContactForm;
