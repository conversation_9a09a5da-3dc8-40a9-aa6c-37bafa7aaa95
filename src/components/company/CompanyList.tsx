import React from 'react';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { isEmpty } from 'lodash';
import CompanyCard from '@/components/company/CompanyCard';
import { LoadingFadein } from '@/styles/share.styled';
import { isAllowed } from '@/utils/permission';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';

const CompanySelectWrapStyle = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: -100px;
  max-height: calc(100vh - 200px);
  width: 480px;
  animation: ${LoadingFadein} 0.3s ease-in;
  @media screen and (max-width: 480px) {
    max-height: calc(100vh - 180px);
  }
  .my-company {
    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }
  .invite-company {
    width: 100%;
    margin-top: 24px;
    .invite-text {
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
`;
export const CompanyCardWrapStyle = styled.div`
  padding: 12px 20px 12px 16px;
  border: 1px solid #dbe2e5;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: 0.3s;
  height: 80px;
  &:hover {
    box-shadow: 0 0 8px rgba(38, 50, 56, 0.15);
  }
  .group {
    display: flex;
    align-items: center;
    .arrow {
      display: flex;
      align-items: center;
      transition: 0.3s;
      color: #cfd8dc;
    }
    .logo {
      display: flex;
      align-items: center;
      margin-right: 16px;
      border-radius: 50%;
      overflow: hidden;
      width: 48px;
      height: 48px;
      min-width: 48px;
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
    .text-wrap {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: auto;
      .name {
        font-size: 16px;
        font-weight: 600;

        @media screen and (max-width: 480px) {
          font-size: 16px;
        }
      }
      .description {
        font-size: 12px;
        font-weight: 400;
        color: #263238;

        @media screen and (max-width: 480px) {
          font-size: 12px;
        }
      }
    }
  }
`;
type CompanyListProps = {
  data: any;
  reLoadData: () => void;
};
function CompanyList(props: CompanyListProps) {
  const { data, reLoadData } = props;
  const router = useRouter();
  const { permissions } = useAppSelector(permissionSelector);

  return (
    <>
      {!isEmpty(data) && (
        <CompanySelectWrapStyle>
          <div className="list-header">
            <div className="topic">บริษัทของฉัน</div>
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="สร้างบริษัท"
              borderRadius={'20px'}
              disabled={!isAllowed(permissions, 'company.info.create')}
              onClick={(_e: any) => {
                router.push('/company/create');
              }}
            />
          </div>
          <div className="my-company">
            {data
              .filter((item: any) => item.inviteVerified)
              .map((item: any, index: number) => (
                <CompanyCard key={index} type={'myCompany'} data={item} />
              ))}
          </div>
          <div className="invite-company">
            {data.some((item: any) => !item.inviteVerified) && (
              <div className="invite-text">คำเชิญเข้าร่วมบริษัท</div>
            )}
            {data
              .filter((item: any) => !item.inviteVerified)
              .map((item: any, index: number) => (
                <CompanyCard
                  key={index}
                  type={'invite'}
                  data={item}
                  reLoadData={() => {
                    reLoadData();
                  }}
                />
              ))}
          </div>
        </CompanySelectWrapStyle>
      )}
    </>
  );
}

export default CompanyList;
