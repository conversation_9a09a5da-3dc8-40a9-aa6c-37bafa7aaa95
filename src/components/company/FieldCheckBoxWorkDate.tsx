import React from 'react';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import styled from 'styled-components';

const FieldCheckBoxWorkDateStyles = styled.div`
  .box-form-group {
    border-radius: 16px;
    border: 1px solid #dbe2e5;
  }
  .MuiFormGroup-root {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    label {
      padding: 0.7rem;
      margin: 0 !important;
      border-bottom: 1px solid #dbe2e5;
      &:last-child {
        border-bottom: none;
      }
      > .MuiButtonBase-root {
        padding: 5px;
      }
    }
  }
`;
type Props = {
  formik: any;
};
const FieldCheckBoxWorkDate = ({ formik }: Props) => {
  const workDate = [
    {
      day: 1,
      name: 'จันทร์',
      defaultChecked: true,
      color: '#FFFF00',
    },
    {
      day: 2,
      name: 'อังคาร',
      defaultChecked: true,
      color: '#FFC0CB',
    },
    {
      day: 3,
      name: 'พุธ',
      defaultChecked: true,
      color: '#00FF00',
    },
    {
      day: 4,
      name: 'พฤหัสบดี',
      defaultChecked: true,
      color: '#FFA500',
    },
    {
      day: 5,
      name: 'ศุกร์',
      defaultChecked: true,
      color: '#0000FF',
    },
    {
      day: 6,
      name: 'เสาร์',
      defaultChecked: false,
      color: '#800080',
    },
    {
      day: 7,
      name: 'อาทิตย์',
      defaultChecked: false,
      color: '#FF0000',
    },
  ];
  return (
    <FieldCheckBoxWorkDateStyles>
      <p>วันที่ทำงาน</p>
      <div className={'box-form-group'}>
        <FormGroup>
          {formik.values.workingDay.map((item: any, index: number) => {
            const data = workDate.find((data) => data.day === item.day);
            return (
              <FormControlLabel
                key={index}
                control={
                  <Checkbox
                    checked={item.active}
                    onChange={(e) => {
                      formik.setFieldValue(`workingDay.${index}`, {
                        day: item.day,
                        active: e.target.checked,
                      });
                    }}
                  />
                }
                label={data?.name}
              />
            );
          })}
        </FormGroup>
      </div>
    </FieldCheckBoxWorkDateStyles>
  );
};

export default FieldCheckBoxWorkDate;
