import React, { ReactNode, useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import styled from 'styled-components';
import { FormHelperText, TextField } from '@mui/material';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';

import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { isEmpty } from 'lodash';
import apiBankAccount from '@/services/core/bank-account';
import Image from 'next/image';

const ModalCreatePaymentOptionStyle = styled.div`
  max-width: 580px;
  width: 580px !important;
  header {
    position: relative;
    h2 {
      text-align: center;
      border-bottom: 1px solid#DBE2E5;
    }
    button {
      position: absolute;
      top: 10px;
      right: 5px;
      border: none;
      padding: 5px !important;
      min-width: 40px !important;
      min-height: 40px !important;
      border-radius: 50%;
      &:hover {
        border: none;
      }
      svg {
        color: #cfd8dc;
      }
    }
  }
  .Mui-error {
    font-weight: 400 !important;
    font-size: 12px !important;
  }
  .MuiDialogContent-root {
    padding: 24px !important;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    .field-label {
      //border: 1px solid #DBE2E5;
      > .MuiInputBase-root {
        width: 100%;
        fieldset {
          border: 1px solid #dbe2e5 !important;
        }
      }
      p {
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        margin: 0;
        padding-bottom: 0.2rem;
      }
    }
  }
  .MuiDialogActions-root {
    padding: 0 24px 24px 24px !important;
    justify-content: center;
    button {
      &:first-child {
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        background: #fff;
        box-shadow: 0px 0px 6px 0px rgba(38, 50, 56, 0.08);
      }
    }
  }
`;
const validationSchema = yup.object({
  nameTH: yup.string().required('กรุณาระบุชื่อบัญชีธนาคาร'),
  bankAccountId: yup.string().required('กรุณาเลือกธนาคาร'),
  accountNumber: yup
    .string()
    .min(10, 'กรุณาระบุเลขบัญชีธนาคารอย่างน้อย 10 หลัก')
    .max(10, 'กรุณาระบุเลขบัญชีธนาคารไม่เกิน 10 หลัก')
    .required('กรุณาระบุเลขบัญชีธนาคาร'),
  paymentMethodNo: yup.string().required('กรุณาระบุรหัสอ้างอิง'),
});
type Props = {
  open: boolean;
  handleClose: () => void;
  dataEdit: any;
  onActionBankCompany: (data: any) => void;
};
const ModalCreatePaymentOption = ({
  open,
  handleClose,
  dataEdit,
  onActionBankCompany,
}: Props) => {
  const [dataBankList, setDataBankList] = useState<any[]>([]);
  const [initialValue] = useState<any>({
    nameTH: '',
    bankAccountId: '',
    accountNumber: '',
    paymentMethodNo: '',
  });
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors: hookFormErrors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: { ...initialValue },
  });

  const onSubmit = (values: any) => {
    const data = isEmpty(dataEdit)
      ? {
          ...values,
        }
      : {
          id: dataEdit.id,
          isActive: dataEdit.active,
          companyId: dataEdit.companyId,
          ...values,
        };
    onActionBankCompany(data);
  };
  const getAllBankAccount = async () => {
    const res = await apiBankAccount.getAllBankAccount();
    if (res && !res.isError) {
      setDataBankList(res.data);
    }
  };
  useEffect(() => {
    getAllBankAccount();
  }, []);
  useEffect(() => {
    if (!isEmpty(dataEdit)) {
      setValue('nameTH', dataEdit.nameTH);
      setValue('bankAccountId', dataEdit.bankAccountId);
      setValue('accountNumber', dataEdit.accountNumber);
      setValue('paymentMethodNo', dataEdit.paymentMethodNo);
    } else {
      reset();
    }
  }, [dataEdit]);
  return (
    <Dialog open={open} onClose={handleClose}>
      <ModalCreatePaymentOptionStyle>
        <header>
          <DialogTitle>สร้างช่องทางการชำระเงิน</DialogTitle>
          <Button onClick={handleClose} variant={'outlined'}>
            <CloseRoundedIcon />
          </Button>
        </header>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogContent>
            <div className={'field-label'}>
              <p>ชื่อบัญชีธนาคาร</p>
              <TextField
                type="text"
                {...register('nameTH')}
                variant="outlined"
                placeholder={'ระบุชื่อบัญชีธนาคาร'}
                error={Boolean(hookFormErrors.nameTH)}
                helperText={hookFormErrors.nameTH?.message as ReactNode}
              />
            </div>
            <div className={'field-label'}>
              <p>ธนาคาร</p>
              <Select
                displayEmpty
                value={watch('bankAccountId')}
                {...register('bankAccountId')}
                error={Boolean(hookFormErrors.bankAccountId)}
              >
                <MenuItem disabled value="">
                  <em>กรุณาเลือกธนาคาร</em>
                </MenuItem>
                {!isEmpty(dataBankList) &&
                  dataBankList.map((item: any) => {
                    return (
                      <MenuItem value={item.id} key={item.id}>
                        <div className={'flex items-center gap-2'}>
                          <Image
                            className={'rounded-md'}
                            src={item.logoUrl}
                            alt={'logo bank'}
                            width={30}
                            height={30}
                          />
                          <div className={'name'}>{item.name}</div>
                        </div>
                      </MenuItem>
                    );
                  })}
              </Select>
              {Boolean(hookFormErrors.bankAccountId) && (
                <FormHelperText error>
                  {hookFormErrors.bankAccountId?.message as ReactNode}
                </FormHelperText>
              )}
            </div>
            <div className={'field-label'}>
              <p>เลขบัญชีธนาคาร</p>
              <TextField
                {...register('accountNumber')}
                variant="outlined"
                placeholder={'ระบุเลขบัญชีธนาคาร'}
                inputProps={{ maxLength: 10 }}
                onKeyPress={(event) => {
                  if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                    event.preventDefault();
                  }
                }}
                onFocus={(e) =>
                  e.target.addEventListener(
                    'wheel',
                    function (e) {
                      e.preventDefault();
                    },
                    { passive: false }
                  )
                }
                error={Boolean(hookFormErrors.accountNumber)}
                helperText={hookFormErrors.accountNumber?.message as ReactNode}
              />
            </div>
            <div className={'field-label'}>
              <p>รหัสอ้างอิง Peak</p>
              <TextField
                type="text"
                {...register('paymentMethodNo')}
                value={watch('paymentMethodNo').toUpperCase()}
                onChange={(e) => {
                  if (
                    e.target.value.length <= 6 &&
                    /^[a-zA-Z0-9 ]*$/.test(e.target.value)
                  ) {
                    setValue('paymentMethodNo', e.target.value.toUpperCase());
                  }
                }}
                variant="outlined"
                placeholder={'รหัสอ้างอิง 6 หลัก CSH003'}
                error={Boolean(hookFormErrors.paymentMethodNo)}
                helperText={
                  hookFormErrors.paymentMethodNo?.message as ReactNode
                }
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              className={'w-1/2'}
              variant={'outlined'}
              onClick={handleClose}
            >
              ยกเลิก
            </Button>
            <Button
              className={'w-1/2'}
              variant={'contained'}
              type="submit"
              autoFocus
            >
              {!isEmpty(dataEdit) ? 'บันทึก' : 'สร้าง'}
            </Button>
          </DialogActions>
        </form>
      </ModalCreatePaymentOptionStyle>
    </Dialog>
  );
};

export default ModalCreatePaymentOption;
