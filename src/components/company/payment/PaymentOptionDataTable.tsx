import React, { useEffect, useState } from 'react';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableTools from '@/components/global/TableTools';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import styled from 'styled-components';
import Image from 'next/image';
import IOSSwitch from '@/components/job/IOSSwitch';
import AppPagination from '@/components/global/AppPagination';
import HeaderColumnAction from '@/components/HeaderColumnAction';
import dayjs from 'dayjs';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import PopoverAction from '@/components/PopoverActionn';
import SvgPencilIcon from '@/components/svg-icon/SvgPencilIcon';
import SvgTrashIcon from '@/components/svg-icon/SvgTrashIcon';

const PaymentOptionDataTableStyles = styled.div`
  min-height: calc(100vh - 88px);
  .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
    background-color: #16d5c5 !important;
  }
  .box-content-wrap {
    height: 100%;
    > div {
      height: 100%;
      .content-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .box-pagination {
          padding-bottom: 1rem;
        }
      }
    }
  }
`;
type Props = {
  data: any;
  handleOpen: () => void;
  setDataEdit: (data: any) => void;
  onDelete: (id: number) => void;
  filters: any;
  setFilters: (filters: any) => void;
  updateStatusBankCompany: (data: any, isActive?: boolean) => void;
};
const PaymentOptionDataTable = ({
  data,
  handleOpen,
  setDataEdit,
  onDelete,
  filters,
  setFilters,
  updateStatusBankCompany,
}: Props) => {
  const { permissions } = useAppSelector(permissionSelector);
  const [rows, setRows] = useState<any[]>([]);
  const columns: GridColDef[] = [
    {
      field: 'no',
      headerName: '#',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 100,
      flex: 0.3,
    },
    {
      field: 'nameTH',
      headerName: 'ชื่อบัญชีธนาคาร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
    },
    {
      field: 'accountNumber',
      headerName: 'เลขบัญชีธนาคาร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.7,
      renderCell: (params: any) => {
        return formatAccountNumber(params.row.accountNumber);
      },
    },

    {
      field: 'bankAccount',
      headerName: 'ธนาคาร',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <div className={'flex items-center justify-center gap-2'}>
            <Image
              className={'rounded-lg'}
              src={params.row.bankAccount.logoUrl}
              alt={'logoUrl'}
              width={35}
              height={35}
            />
            <div>{params.row.bankAccount.name}</div>
          </div>
        );
      },
    },
    {
      field: 'paymentMethodNo',
      headerName: 'รหัสอ้างอิง Peak',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.6,
      renderCell: (params: any) => {
        return params.row.paymentMethodNo || '-';
      },
    },
    {
      field: 'createdDate',
      headerName: 'วันที่สร้าง',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.8,
      renderCell: (params: any) => {
        if (!params.row.createdDate) return '-';
        return `${dayjs(params.row.createdDate).format('YYYY-MM-DD HH:mm')} น.`;
      },
    },
    {
      field: 'active',
      headerName: '',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 108,
      flex: 0.3,
      renderCell: (params: any) => {
        if (isAllowed(permissions, 'company.payment.update')) {
          return (
            <IOSSwitch
              value={params.row.active}
              onChangeActive={(checked: boolean) => {
                const data = {
                  id: params.row.id,
                  companyId: params.row.companyId,
                  nameTH: params.row.nameTH,
                  bankAccountId: params.row.bankAccountId,
                  accountNumber: params.row.accountNumber,
                  isActive: checked,
                };
                updateStatusBankCompany(data, true);
              }}
            />
          );
        }
        return '';
      },
    },
    {
      field: 'actions',
      headerName: 'จัดการ',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 50,
      flex: 0.5,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <PopoverAction
            triggerElement={
              <div className="kebab">
                <div className="dot" />
              </div>
            }
            customItems={[
              {
                disabled: !isAllowed(permissions, 'company.payment.update'),
                IconElement: () => <SvgPencilIcon />,
                title: 'แก้ไข',
                onAction: () => {
                  onOpenSetDataEdit(params.row);
                },
              },
              {
                disabled: !isAllowed(permissions, 'company.payment.delete'),
                IconElement: () => <SvgTrashIcon fill={'#D32F2F'} />,
                title: 'ลบรายการ',
                onAction: () => {
                  onDelete(params.row.id);
                },
                cssProps: {
                  color: '#D32F2F',
                  '&:hover': {
                    backgroundColor: '#FDE8EF',
                  },
                },
              },
            ]}
          />
        );
      },
    },
  ];
  const formatAccountNumber = (input: string) => {
    const cleaned = input.replace(/\D/g, '');
    return cleaned.replace(
      /^(\d{3})(\d{1})(\d{5})(\d{1})?$/,
      (match, g1, g2, g3, g4) => {
        return [g1, g2, g3, g4].filter(Boolean).join(' ');
      }
    );
  };
  const onOpenSetDataEdit = (data: any) => {
    setDataEdit(data);
    handleOpen();
  };
  useEffect(() => {
    if (data) {
      const newData = data.content.map((item: any, index: number) => {
        return { ...item, no: index + 1 };
      });
      console.log(newData);
      setRows(newData);
    }
  }, [data]);
  return (
    <PaymentOptionDataTableStyles>
      <div className="box-content-wrap">
        <AppTableStyle $rows={rows}>
          <div className="content-wrap">
            <div>
              <TableTools
                tools={['search']}
                title={`${data?.totalElements || 0} รายการ`}
                makeNewFilter={(_newFilter: any) => {
                  // console.log(newFilter);
                }}
              />
              <ScrollBarStyled>
                <HeaderColumnAction text="จัดการ" width={88} />
                <DataGrid
                  hideFooter={true}
                  rows={rows || []}
                  columns={columns}
                  paginationMode="server"
                  rowCount={data?.totalElements || 0}
                  // pageSize={filters.size}
                  disableSelectionOnClick={false}
                  autoHeight={true}
                  sortModel={[]}
                  getRowHeight={() => 56}
                  headerHeight={48}
                  components={{
                    NoRowsOverlay: () => <TableNoRowsOverlay />,
                    LoadingOverlay: () => <TableLoadingOverlay />,
                  }}
                />
              </ScrollBarStyled>
            </div>
            <div className="box-pagination px-[16px]">
              <AppPagination
                filters={filters}
                totalElements={data?.totalElements || 0}
                handleChangeFilters={(newValues: any) => {
                  setFilters(newValues);
                }}
              />
            </div>
          </div>
        </AppTableStyle>
      </div>
    </PaymentOptionDataTableStyles>
  );
};

export default PaymentOptionDataTable;
