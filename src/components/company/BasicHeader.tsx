import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
import { Button } from '@mui/material';
import styled from 'styled-components';
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

const HeaderStyle = styled.div`
  position: sticky;
  top: 0;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100px;
  background-color: white;
  z-index: 5;
  padding: 0 40px;
  justify-content: space-between;
  @media screen and (max-width: 750px) {
    justify-content: space-between;
  }
  @media screen and (max-width: 480px) {
    img {
      transform: scale(0.85);
    }
    height: 80px;
    padding: 0 18px;
  }

  .logo {
    font-size: 32px;
    font-weight: 600;
    white-space: nowrap;
    display: flex;
    align-items: center;
  }
  .login-btn {
    width: 200px;
  }
  .create-btn {
    position: absolute;
    right: 40px;
    @media screen and (max-width: 750px) {
      position: relative;
      right: auto;
    }
  }
`;
type BasicHeaderProps = {
  path?: string;
};
export default function BasicHeader(props: BasicHeaderProps) {
  const { path } = props;
  const router = useRouter();
  const client = publicRuntimeConfig.AUTH_CLIENT;
  const authUrl = publicRuntimeConfig.AUTH_ENDPOINT;
  const redirectUrl = publicRuntimeConfig.REDIRECT_URL;

  function login() {
    const link = `${authUrl}/oauth2/authorize?response_type=code&client_id=${client}&scope=openid&redirect_uri=${redirectUrl}`;
    window.location.href = link;
  }

  return (
    <HeaderStyle>
      <div className="logo">
        <Image src="/icons/hon.svg" alt="" width={120} height={40} />
      </div>
      <div className="login-btn">
        <Button
          variant="contained"
          color="dark"
          fullWidth
          sx={{ fontSize: '16px' }}
          onClick={() => login()}
        >
          เข้าสู่ระบบ
        </Button>
      </div>
      {path && path === '/company/create' ? (
        <Button
          type="button"
          variant="outlined"
          color="blueGrey"
          style={{
            color: '#78909C',
            width: '120px',
            height: '40px',
          }}
          onClick={(_e: any) => {
            router.push('/company');
          }}
        >
          ยกเลิก
        </Button>
      ) : null}
    </HeaderStyle>
  );
}
