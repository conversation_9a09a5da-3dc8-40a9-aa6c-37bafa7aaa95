import React from 'react';
import styled from '@emotion/styled';
import { FormHelperText } from '@mui/material';
import { LocalizationProvider, TimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import QueryBuilderRoundedIcon from '@mui/icons-material/QueryBuilderRounded';

const FieldTimePickerStyles = styled.div`
  > .box-time-range-picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    .select-time-range-picker {
      display: flex;
      align-items: center;
      border: 1px solid #dbe2e5;
      border-radius: 8px;
      padding: 5px 0;
      > svg {
        margin-left: 0.5rem;
      }
      .MuiFormControl-root {
        position: relative;
        max-width: 62px;
        max-height: 35px;
        &:hover {
          background: #00000010;
        }
        .MuiInputBase-root {
          padding: 0;
          box-shadow: unset !important;
        }
        .MuiInputAdornment-root {
          position: absolute;
          left: 12px;
          width: 100%;
          button {
            border-radius: unset;
            width: 100%;
            justify-content: start;
            &:hover {
              background-color: transparent;
            }
            svg {
              opacity: 0;
            }
          }
        }
        //&:last-child {
        //  .MuiInputBase-root {
        //    padding-left: 0;
        //    input {
        //      padding-left: unset;
        //      padding-right: unset;
        //    }
        //  }
        //  .MuiInputAdornment-root {
        //    button {
        //      svg {
        //        opacity: 0;
        //      }
        //    }
        //  }
        //}
      }

      .MuiInputBase-root {
        input {
          text-align: center;
          padding: 0;
        }
      }
    }
  }
  p {
    margin: 0;
    margin-bottom: 0.3rem;
  }
  .ant-picker {
    width: 100%;
    .ant-picker-input {
      min-width: 40px;
      width: 60px;
    }
    .ant-picker-suffix {
      display: none;
    }
  }
`;
type Props = {
  formik: any;
};
const FieldTimePicker = ({ formik }: Props) => {
  const slotProps: any = {
    inputAdornment: {
      position: 'start',
    },
  };
  return (
    <>
      <FieldTimePickerStyles>
        <div className={'box-time-range-picker'}>
          <div className={'w-1/2'}>
            <p>เวลาเริ่ม-เลิกงาน</p>
            <div className={'select-time-range-picker'}>
              <QueryBuilderRoundedIcon />
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <TimePicker
                  slotProps={slotProps}
                  ampm={false}
                  maxTime={dayjs(formik.values.endTime)}
                  value={dayjs(formik.values.startTime)}
                  onChange={(value) => {
                    formik.setFieldValue('startTime', value);
                  }}
                />
                <div>-</div>
                <TimePicker
                  slotProps={slotProps}
                  ampm={false}
                  minTime={dayjs(formik.values.startTime)}
                  value={dayjs(formik.values.endTime)}
                  onChange={(value) => {
                    formik.setFieldValue('endTime', value);
                  }}
                />
              </LocalizationProvider>
            </div>
          </div>
          <div className={'w-1/2'}>
            <p>เวลาพักเที่ยง</p>
            <div className={'select-time-range-picker'}>
              <QueryBuilderRoundedIcon />
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <TimePicker
                  slotProps={{
                    inputAdornment: {
                      position: 'start',
                    },
                  }}
                  ampm={false}
                  maxTime={dayjs(formik.values.breakEnd)}
                  value={dayjs(formik.values.breakStart)}
                  onChange={(value) => {
                    formik.setFieldValue('breakStart', value);
                  }}
                />
                <div>-</div>
                <TimePicker
                  slotProps={{
                    inputAdornment: {
                      position: 'start',
                    },
                  }}
                  ampm={false}
                  minTime={dayjs(formik.values.breakStart)}
                  value={dayjs(formik.values.breakEnd)}
                  onChange={(value) => {
                    formik.setFieldValue('breakEnd', value);
                  }}
                />
              </LocalizationProvider>
            </div>
          </div>
        </div>
        <div className={'box-time-range-picker'}>
          <div className={'w-1/2'}>
            {formik.touched.startTime &&
              formik.errors.startTime &&
              formik.touched.endTime &&
              formik.errors.endTime && (
                <FormHelperText error>{formik.errors.startTime}</FormHelperText>
              )}
          </div>
          <div className={'w-1/2'}>
            {formik.touched.breakStart &&
              formik.errors.breakStart &&
              formik.touched.breakEnd &&
              formik.errors.breakEnd && (
                <FormHelperText error>
                  {formik.errors.breakStart}
                </FormHelperText>
              )}
          </div>
        </div>
      </FieldTimePickerStyles>
    </>
  );
};

export default FieldTimePicker;
