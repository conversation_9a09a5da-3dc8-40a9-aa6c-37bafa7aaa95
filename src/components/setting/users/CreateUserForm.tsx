import React, { useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import * as yup from 'yup';
import { useFormik } from 'formik';
import apiRole from '@/services/core/role';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import apiCompany from '@/services/core/company';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const CreateUserStyle = styled.div`
  width: 100%;
  padding-bottom: 24px;
  display: flex;
  justify-content: center;
  animation: ${LoadingFadein} 0.3s ease-in;
  .form-wrap {
    width: 640px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    padding: 0 24px;
    &.animate {
      animation: ${LoadingFadein} 0.6s ease-in;
    }
    .text-error {
      color: #d32f2f;
    }
    .topic {
      font-size: 22px;
      font-weight: 600;
      margin: 80px 0 16px;
    }
    .permission-detail {
      width: 100%;
      border-radius: 8px;
      background-color: #f5f7f8;
      padding: 8px 16px;
      margin-top: 8px;
      animation: ${LoadingFadein} 0.3s ease-in;
      .permission-group {
        display: flex;
        flex-direction: column;
        .group {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 16px;
          &:first-child {
            margin: 0;
          }
          .name {
            font-size: 12px;
            font-weight: 600;
          }
          .setting {
            font-size: 12px;
            font-weight: 400;
            text-decoration: underline;
            cursor: pointer;
            user-select: none;
          }
        }
        ul {
          margin: 0;
          padding-inline-start: 24px;
          li {
            font-size: 12px;
            &::marker {
              font-size: 8px;
            }
          }
        }
      }
    }
    p {
      margin: 24px 0 8px;
    }
  }
`;

const validationSchema = yup.object({
  email: yup
    .string()
    .email('รูปแบบอีเมล์ไม่ถูกต้อง')
    .required('กรุณากรอกอีเมล'),
  roleId: yup.number().required('กรุณาระบุสิทธิการใช้งาน'),
});

type CreateUserFormProps = {
  type?: string;
  initialValue?: any;
};
const CreateUserForm = (props: CreateUserFormProps) => {
  const { type, initialValue } = props;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { id } = router.query;
  const [role, setRole] = useState<any>([]);
  const [companyRoleById, setCompanyRoleById] = useState<any>(null);
  const [permissionDetail, setPermissionDetail] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      email: !isEmpty(initialValue) ? initialValue.email : '',
      roleId: !isEmpty(initialValue) ? initialValue.roleId : '',
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values: any) => {
      setLoading(true);
      if (type !== 'edit') {
        const res = await apiCompany.inviteMemberCompany(values);
        if (!res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: `เชิญ ${values.email} สำเร็จ`,
              severity: 'success',
            })
          );
          setDisable(true);
          await router.push('/company/manage-users/users');
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: `${res.message.message}`,
              severity: 'error',
            })
          );
        }
      } else if (type === 'edit') {
        const sendData = {
          companyUserId: id,
          companyRoleId: values.roleId,
        };
        const res = await apiCompany.updateMemberCompany(sendData);
        if (!res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: `แก้ไข ${
                !isEmpty(initialValue.name) ? initialValue.name : values.email
              } สำเร็จ`,
              severity: 'success',
            })
          );
          setDisable(true);
          await router.push('/company/manage-users/users');
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: `${res.message.message}`,
              severity: 'error',
            })
          );
        }
      }
      setLoading(false);
    },
  });

  const fetchRoleByCompany = async () => {
    const res = await apiRole.getRole({
      size: 100,
      page: 0,
    });
    if (!res.isError) {
      setRole(res.content);
    }
  };

  useEffect(() => {
    fetchRoleByCompany();
  }, []);

  const fetchCompanyRoleById = async (id: any) => {
    const res = await apiRole.getRoleById(id);
    setCompanyRoleById(res);
  };
  useEffect(() => {
    if (formik.values.roleId) {
      fetchCompanyRoleById(formik.values.roleId);
    }
  }, [formik.values.roleId]);

  useEffect(() => {
    if (!isEmpty(companyRoleById)) {
      const groupedByServiceGroup = companyRoleById.rolePermissions.reduce(
        (acc: any, obj: any) => {
          const { serviceGroup, permissionName } = obj;
          if (!acc[serviceGroup]) {
            acc[serviceGroup] = {
              group: serviceGroup,
              permission: [],
            };
          }
          acc[serviceGroup].permission.push({
            name: permissionName,
          });

          return acc;
        },
        {}
      );
      const resultArray = Object.values(groupedByServiceGroup);
      setPermissionDetail(resultArray);
    }
  }, [companyRoleById]);

  return (
    <CreateUserStyle>
      <div className="form-wrap">
        <form onSubmit={formik.handleSubmit}>
          <p>อีเมล</p>
          <TextField
            type="email"
            name="email"
            placeholder="ระบุอีเมล"
            disabled={type === 'edit'}
            value={formik.values.email}
            onChange={formik.handleChange}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && (formik.errors.email as string)}
          />
          <>
            <p>สิทธิการใช้งาน</p>
            <FormControl sx={{ width: '100%' }}>
              <Select
                sx={{
                  height: '40px',
                  border: '0',
                }}
                displayEmpty
                name="roleId"
                value={formik.values.roleId}
                onChange={formik.handleChange}
                input={<OutlinedInput />}
                renderValue={(selected) => {
                  if (!selected) {
                    return (
                      <div className="text-[14px] text-[#B0BEC5]">
                        ระบุสิทธิการใช้งาน
                      </div>
                    );
                  }
                  const selectedItem = role.find(
                    (item: any) => item.id === selected
                  );
                  return selectedItem ? selectedItem.name : '';
                }}
                error={formik.touched.roleId && Boolean(formik.errors.roleId)}
              >
                <MenuItem disabled value="">
                  <em>-- Please select --</em>
                </MenuItem>
                {role.map((item: any) => (
                  <MenuItem key={item.id} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.roleId && formik.errors.roleId && (
                <FormHelperText error>
                  {formik.errors.roleId as string}
                </FormHelperText>
              )}
            </FormControl>
          </>
          {!isEmpty(permissionDetail) && (
            <div className="permission-detail">
              <div className="permission-group">
                {permissionDetail?.map((item: any, index: number) => (
                  <>
                    <div className="group" key={index}>
                      <div className="name">{item.group}</div>
                      {index === 0 && (
                        <div
                          className="setting"
                          onClick={() => {
                            router.push(
                              `/setting/permission/${formik.values.roleId}`
                            );
                          }}
                        >
                          ตั้งค่าสิทธิ
                        </div>
                      )}
                    </div>
                    <ul>
                      {item.permission?.map((item: any, index: number) => (
                        <>
                          <li key={index}>{item.name}</li>
                        </>
                      ))}
                    </ul>
                  </>
                ))}
              </div>
            </div>
          )}
          <div className="mt-[40px]" />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="dark"
            disabled={disable}
          >
            {loading ? (
              <CircularProgress size={25} />
            ) : type !== 'edit' ? (
              'เชิญผู้ใช้งาน'
            ) : (
              'แก้ไขผู้ใช้งาน'
            )}
          </Button>
        </form>
      </div>
    </CreateUserStyle>
  );
};

export default CreateUserForm;
