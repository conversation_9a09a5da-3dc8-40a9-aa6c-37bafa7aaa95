import React, { ChangeEvent, useEffect, useState } from 'react';
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import { isEmpty, isNull } from 'lodash';
import styled from 'styled-components';
import * as yup from 'yup';
import { useDebounce } from 'usehooks-ts';
import apiAddress from '@/services/stock/address';

const ModalAddContactAddressStyles = styled.div`
  padding-top: 1.4rem;
  .box-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    h3 {
      margin: 0;
    }
    p {
      margin: 0;
      margin-bottom: 0.3rem;
    }
    .select {
      fieldset {
        border: 1px solid rgba(0, 0, 0, 0.23) !important;
      }
    }
  }
`;
const validationSchema = yup.object({
  name: yup
    .string()
    .required('กรุณาระบุชื่อจริง นามสกุล')
    .matches(/^[\u0E00-\u0E7Fa-zA-Z\s]+$/, 'กรุณากรอกตัวอักษาไทย, อังกฤษ'),
  phoneNumber: yup
    .string()
    .min(10, `กรุณาระบุหมายเลขโทรศัพท์ 10 ตัว`)
    .max(10, `กรุณาระบุหมายเลขโทรศัพท์ไม่เกิน 10 ตัว`)
    .matches(/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
    .required('กรุณาระบุหมายเลขโทรศัพท์'),
  address: yup.string().required('กรุณาระบุที่อยู่'),
  provinceId: yup.string().required('กรุณาระบุจังหวัด'),
  districtId: yup.string().required('กรุณาระบุอำเภอ'),
  subDistrictId: yup.string().required('กรุณาระบุตำบล'),
  zipcode: yup
    .string()
    .min(5, 'กรุณาระบุรหัสไปรษณีย์ 5 หลัก')
    .required('กรุณาระบุรหัสไปรษณีย์'),
});
type Props = {
  open: boolean;
  handleClose: () => void;
  dataEdit: any;
  onSubmit: (data: any) => void;
};
const ModalAddContactAddress = ({
  open,
  handleClose,
  dataEdit,
  onSubmit,
}: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const [loadingZipcode, setLoadingZipcode] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedZipcode = useDebounce<string>(keyword, 600);
  const [districtList, setDistrictList] = useState<any>([]);
  const [subDistrictList, setSubDistrictList] = useState<any>([]);
  const [initPage, setInitPage] = useState<boolean>(false);
  const [displayProvince, setDisplayProvince] = useState<string>('');
  const formik = useFormik({
    initialValues: {
      id: '',
      name: '',
      phoneNumber: '',
      address: '',
      subDistrictId: '',
      districtId: '',
      provinceId: '',
      zipcode: '',
      contactId: Number(id),
      isDefault: false,
    },
    validationSchema,
    onSubmit: (values: any) => {
      onSubmit(values);
      handleClose();
    },
  });

  const handleChangeZipcode = (value: string) => {
    if (value.length === 5) {
      setLoadingZipcode(true);
    } else {
      setDistrictList([]);
      setSubDistrictList([]);
      if (initPage) {
        formik.setFieldValue('provinceId', '');
        formik.setFieldValue('districtId', '');
        formik.setFieldValue('subDistrictId', '');
      }
    }
    formik.setFieldValue('zipcode', value);
    setKeyword(value);
  };
  useEffect(() => {
    if (debouncedZipcode.length === 5) {
      getAddressByZipcode(debouncedZipcode);
    }
  }, [debouncedZipcode]);
  const getAddressByZipcode = async (zipcode: string, isGetList?: boolean) => {
    const res = await apiAddress.getAddressByZipcode(zipcode);
    if (!res.isError) {
      if (isGetList) {
        setDisplayProvince(res.data.province.name);
        setDistrictList(res.data.district);
      } else {
        await formik.setFieldValue('provinceId', res.data.province.id);
        if (!isEmpty(res.data.district)) {
          await formik.setFieldValue('districtId', res.data.district[0].id);
          getSubDistrictByDistrictId(res.data.district[0].id);
        }
        setDisplayProvince(res.data.province.name);
        setDistrictList(res.data.district);
      }
    } else {
      formik.setFieldError('zipcode', res.message.message);
    }
    setLoadingZipcode(false);
  };
  const getSubDistrictByDistrictId = async (
    districtId: number,
    isGetList?: boolean
  ) => {
    const res = await apiAddress.getSubDistrictByDistrictId(districtId);
    if (!res.isError) {
      if (isGetList) {
        setSubDistrictList(res.data);
      } else {
        await formik.setFieldValue('subDistrictId', res.data[0].id);
        setSubDistrictList(res.data);
      }
    }
  };
  useEffect(() => {
    if (isEmpty(dataEdit)) {
      setDistrictList([]);
      setSubDistrictList([]);
      if (initPage) {
        formik.setFieldValue('provinceId', '');
        formik.setFieldValue('districtId', '');
        formik.setFieldValue('subDistrictId', '');
      }
      if (
        !initPage &&
        !isNull(formik.values.zipcode) &&
        formik.values.zipcode.length === 5
      ) {
        getAddressByZipcode(formik.values.zipcode);
        setInitPage(true);
      }
    }
  }, [formik.values.zipcode]);
  useEffect(() => {
    if (!isEmpty(dataEdit)) {
      formik.setValues({
        id: dataEdit.id,
        name: dataEdit.name,
        phoneNumber: dataEdit.phoneNumber,
        address: dataEdit.address,
        zipcode: dataEdit.zipcode,
        subDistrictId: dataEdit.subDistrict.id,
        districtId: dataEdit.district.id,
        provinceId: dataEdit.province.id,
        contactId: Number(id),
        isDefault: true,
      });
      getAddressByZipcode(dataEdit.zipcode, true);
      getSubDistrictByDistrictId(Number(dataEdit.district.id), true);
      setInitPage(true);
    } else {
      formik.resetForm();
    }
  }, [dataEdit]);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`เพิ่มข้อมูลการจัดส่งสินค้า`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <ModalAddContactAddressStyles>
              <form onSubmit={formik.handleSubmit}>
                <div className={'box-form'}>
                  <h3>ผู้รับ</h3>
                  <div className={'label'}>
                    <p>ชื่อจริง นามสกุล</p>
                    <TextField
                      type={'text'}
                      id={'name'}
                      name={'name'}
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      placeholder={'ระบุชื่อจริง นามสกุล'}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={formik.touched.name && formik.errors.name}
                    />
                  </div>
                  <div className={'label'}>
                    <p>โทรศัพท์</p>
                    <TextField
                      id={'phoneNumber'}
                      name={'phoneNumber'}
                      value={formik.values.phoneNumber}
                      onChange={formik.handleChange}
                      placeholder={'ระบุหมายเลขโทรศัพท์'}
                      inputProps={{ maxLength: 10 }}
                      onKeyPress={(event) => {
                        if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                          event.preventDefault();
                        }
                      }}
                      error={
                        formik.touched.phoneNumber &&
                        Boolean(formik.errors.phoneNumber)
                      }
                      helperText={
                        formik.touched.phoneNumber && formik.errors.phoneNumber
                      }
                    />
                  </div>
                  <h3 style={{ marginTop: '2rem' }}>ที่อยู่</h3>
                  <div className={'label'}>
                    <p>ที่อยู่</p>
                    <TextField
                      id={'address'}
                      name={'address'}
                      value={formik.values.address}
                      onChange={formik.handleChange}
                      placeholder={'เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ'}
                      error={
                        formik.touched.address && Boolean(formik.errors.address)
                      }
                      helperText={
                        formik.touched.address && formik.errors.address
                      }
                    />
                  </div>
                  <div className={'label'}>
                    <p>รหัสไปรษณีย์</p>
                    <TextField
                      id={'zipcode'}
                      name={'zipcode'}
                      value={formik.values.zipcode}
                      placeholder={'ระบุรหัสไปรษณีย์'}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => {
                        handleChangeZipcode(e.target.value);
                      }}
                      inputProps={{ maxLength: 5 }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            {loadingZipcode && <CircularProgress size={20} />}
                          </InputAdornment>
                        ),
                      }}
                      onKeyPress={(event) => {
                        if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                          event.preventDefault();
                        }
                      }}
                      error={
                        formik.touched.zipcode && Boolean(formik.errors.zipcode)
                      }
                      helperText={
                        formik.touched.zipcode && formik.errors.zipcode
                      }
                    />
                  </div>
                  {formik.values.provinceId && (
                    <div className="fade ">
                      <p className="field-title">จังหวัด</p>
                      <TextField type="text" value={displayProvince} disabled />
                    </div>
                  )}
                  {formik.values.districtId && (
                    <div className="fade select">
                      <p className="field-title">อำเภอ</p>
                      <Select
                        className={'w-full'}
                        displayEmpty
                        value={formik.values.districtId}
                        onChange={(e) => {
                          formik.setFieldValue('districtId', e.target.value);
                        }}
                        disabled={districtList.length <= 1}
                      >
                        {districtList.map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </div>
                  )}
                  {!isEmpty(subDistrictList) && (
                    <div className="fade select">
                      <p className="field-title">ตำบล/แขวง</p>
                      <Select
                        className={'w-full'}
                        displayEmpty
                        value={formik.values.subDistrictId}
                        onChange={(e) => {
                          formik.setFieldValue('subDistrictId', e.target.value);
                        }}
                        disabled={subDistrictList.length <= 1}
                      >
                        {subDistrictList.map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </div>
                  )}
                </div>
                <div className="w-full flex justify-between mt-[34px] gap-5">
                  <Button
                    variant="outlined"
                    color="blueGrey"
                    fullWidth
                    onClick={() => {
                      handleClose();
                    }}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    fullWidth
                  >
                    บันทึก
                  </Button>
                </div>
              </form>
            </ModalAddContactAddressStyles>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalAddContactAddress;
