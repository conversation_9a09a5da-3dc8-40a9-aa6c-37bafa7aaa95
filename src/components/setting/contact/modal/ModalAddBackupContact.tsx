import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogContent, TextField } from '@mui/material';
import FormModal from '@/components/global/form/FormModal';
import { useFormik } from 'formik';
import * as yup from 'yup';
import styled from 'styled-components';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';

const ModalAddBackupContactStyles = styled.div`
  padding-top: 1.4rem;
  .box-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    p {
      margin: 0;
      margin-bottom: 0.3rem;
    }
  }
`;
const validationSchema = yup.object({
  name: yup
    .string()
    .required('กรุณาระบุชื่อจริง นามสกุล')
    .matches(/^[\u0E00-\u0E7Fa-zA-Z\s]+$/, 'กรุณากรอกตัวอักษาไทย, อังกฤษ'),
  phoneNumber: yup
    .string()
    .min(10, `กรุณาระบุหมายเลขโทรศัพท์ 10 ตัว`)
    .max(10, `กรุณาระบุหมายเลขโทรศัพท์ไม่เกิน 10 ตัว`)
    .required('กรุณาระบุหมายเลขโทรศัพท์'),
  // department: yup.string().required('กรุณาระบุแผนก'),
  email: yup
    .string()
    .email('รูปแบบอีเมล์ไม่ถูกต้อง')
    .required('กรุณาระบุอีเมล'),
  lineId: yup.string().required('กรุณาระบุไอดีไลน์'),
});
type Props = {
  open: boolean;
  handleClose: () => void;
  dataEdit: any;
  onSubmit: (data: any) => void;
};
const ModalAddBackupContact = ({
  open,
  handleClose,
  dataEdit,
  onSubmit,
}: Props) => {
  const router = useRouter();
  const { id } = router.query;
  const formik = useFormik({
    initialValues: {
      id: '',
      name: '',
      phoneNumber: '',
      // department: '',
      email: '',
      lineId: '',
      contactId: Number(id),
    },
    validationSchema,
    onSubmit: (values: any) => {
      onSubmit(values);
      handleClose();
    },
  });
  useEffect(() => {
    if (!isEmpty(dataEdit)) {
      formik.setValues({
        id: dataEdit.id,
        name: dataEdit.name,
        phoneNumber: dataEdit.phoneNumber,
        email: dataEdit.email,
        lineId: dataEdit.lineId,
        contactId: Number(id),
      });
    } else {
      formik.resetForm();
    }
  }, [dataEdit]);
  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          handleClose();
        }}
      >
        <DialogContent>
          <FormModal
            title={`เพิ่มข้อมูลติดต่อสำรอง`}
            handleClose={() => {
              handleClose();
            }}
            width={492}
          >
            <ModalAddBackupContactStyles>
              <form onSubmit={formik.handleSubmit}>
                <div className={'box-form'}>
                  <div className={'label'}>
                    <p>ชื่อจริง นามสกุล</p>
                    <TextField
                      type={'text'}
                      id={'name'}
                      name={'name'}
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      placeholder={'ระบุชื่อจริง นามสกุล'}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={formik.touched.name && formik.errors.name}
                    />
                  </div>
                  <div className={'label'}>
                    <p>โทรศัพท์</p>
                    <TextField
                      id={'phoneNumber'}
                      name={'phoneNumber'}
                      value={formik.values.phoneNumber}
                      onChange={formik.handleChange}
                      placeholder={'ระบุหมายเลขโทรศัพท์'}
                      inputProps={{ maxLength: 10 }}
                      onKeyPress={(event) => {
                        if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                          event.preventDefault();
                        }
                      }}
                      error={
                        formik.touched.phoneNumber &&
                        Boolean(formik.errors.phoneNumber)
                      }
                      helperText={
                        formik.touched.phoneNumber && formik.errors.phoneNumber
                      }
                    />
                  </div>
                  {/* <div className={'label'}> */}
                  {/*  <p>แผนก</p> */}
                  {/*  <TextField */}
                  {/*    id={'department'} */}
                  {/*    name={'department'} */}
                  {/*    value={formik.values.department} */}
                  {/*    onChange={formik.handleChange} */}
                  {/*    placeholder={'ระบุแผนก'} */}
                  {/*    error={ */}
                  {/*      formik.touched.department && */}
                  {/*      Boolean(formik.errors.department) */}
                  {/*    } */}
                  {/*    helperText={ */}
                  {/*      formik.touched.department && formik.errors.department */}
                  {/*    } */}
                  {/*  /> */}
                  {/* </div> */}
                  <div className={'label'}>
                    <p>อีเมล</p>
                    <TextField
                      id={'email'}
                      name={'email'}
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      placeholder={'ระบุอีเมล'}
                      error={
                        formik.touched.email && Boolean(formik.errors.email)
                      }
                      helperText={formik.touched.email && formik.errors.email}
                    />
                  </div>
                  <div className={'label'}>
                    <p>Line ID</p>
                    <TextField
                      id={'lineId'}
                      name={'lineId'}
                      value={formik.values.lineId}
                      onChange={formik.handleChange}
                      placeholder={'ระบุไอดีไลน์'}
                      error={
                        formik.touched.lineId && Boolean(formik.errors.lineId)
                      }
                      helperText={formik.touched.lineId && formik.errors.lineId}
                    />
                  </div>
                </div>
                <div className="w-full flex justify-between mt-[34px] gap-5">
                  <Button
                    variant="outlined"
                    color="blueGrey"
                    fullWidth
                    onClick={() => {
                      handleClose();
                    }}
                  >
                    <span>ยกเลิก</span>
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    color="dark"
                    fullWidth
                  >
                    บันทึก
                  </Button>
                </div>
              </form>
            </ModalAddBackupContactStyles>
          </FormModal>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalAddBackupContact;
