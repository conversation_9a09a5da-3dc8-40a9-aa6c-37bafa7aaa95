import React from 'react';
import ContactDetailPanel from '@/components/setting/contact/ContactDetailPanel';
import DataTableContactDetail from '@/components/setting/contact/DataTableContactDetail';

type Props = {
  contactById: any;
  // addressByContactId: any;
  // reloadAddressByContactId: () => void;
};
const ContactDetail = ({ contactById }: Props) => {
  return (
    <>
      <ContactDetailPanel contactById={contactById} />
      <DataTableContactDetail />
    </>
  );
};

export default ContactDetail;
