import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import {
  Autocomplete,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
} from '@mui/material';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { isEmpty, isNull } from 'lodash';
import apiAddress from '@/services/stock/address';
import { useDebounce } from 'usehooks-ts';
import { useRouter } from 'next/router';

type ModalShippingAddressFormProps = {
  open: boolean;
  onClose: () => void;
  handleSubmitModalAddress: (value: any) => void;
  addressByAddressId: any;
  actionMode: string;
  submitting: boolean;
  disable: boolean;
};
const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อ'),
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องมี 10 หลัก')
    .required('กรุณากรอกเบอร์โทรศัพท์'),
  address: yup.string().required('กรุณากรอกที่อยู่'),
  zipcode: yup
    .string()
    .required('กรุณากรอกรหัสไปรษณีย์')
    .matches(/^\d{5}$/, 'รหัสไปรษณีย์ต้องมี 5 หลักเท่านั้น'),
  province: yup.object().required('กรุณาระบุ จังหวัด'),
  district: yup.object().required('กรุณาระบุ อำเภอ'),
  subDistrict: yup.object().required('กรุณาระบุ ตำบล/แขวง'),
});

const ModalShippingAddressForm = (props: ModalShippingAddressFormProps) => {
  const {
    open,
    onClose,
    addressByAddressId,
    actionMode,
    handleSubmitModalAddress,
    submitting,
    disable,
  } = props;
  const router = useRouter();
  const { id } = router.query;
  const wrapperRef = useRef(null);
  const [districtList, setDistrictList] = useState<any>([]);
  const [subDistrictList, setSubDistrictList] = useState<any>([]);
  const [loadingZipcode, setLoadingZipcode] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const debouncedZipcode = useDebounce<string>(keyword, 600);
  useOutsideAlerter(wrapperRef);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          handleClose();
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }

  const formik = useFormik({
    initialValues: {
      id: actionMode === 'edit' ? addressByAddressId.id : null,
      name: actionMode === 'edit' ? addressByAddressId.name : '',
      phoneNumber: actionMode === 'edit' ? addressByAddressId.phoneNumber : '',
      address: actionMode === 'edit' ? addressByAddressId.address : '',
      zipcode: actionMode === 'edit' ? addressByAddressId.zipcode : '',
      province: actionMode === 'edit' ? addressByAddressId.province : '',
      district: actionMode === 'edit' ? addressByAddressId.district : null,
      subDistrict:
        actionMode === 'edit' ? addressByAddressId.subDistrict : null,
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      const sendValue = {
        ...values,
        provinceId: values.province.id,
        districtId: values.district.id,
        subDistrictId: values.subDistrict.id,
        contactId: Number(id),
      };
      delete sendValue.subDistrict;
      delete sendValue.district;
      delete sendValue.province;
      handleSubmitModalAddress(sendValue);
    },
  });
  const handleClose = () => {
    onClose();
  };
  const handleChangeZipcode = (value: string) => {
    if (value.length === 5) {
      setLoadingZipcode(true);
    }
    formik.setFieldValue('zipcode', value);
    setKeyword(value);
  };

  useEffect(() => {
    formik.setFieldValue('subDistrict', null);
    if (debouncedZipcode.length === 5) {
      getAddressByZipcode();
    }
  }, [debouncedZipcode]);

  const getAddressByZipcode = async () => {
    const res = await apiAddress.getAddressByZipcode(formik.values.zipcode);
    if (!res.isError) {
      await formik.setFieldValue('province', res.data.province);
      setDistrictList(res.data.district);
    } else {
      formik.setFieldError('zipcode', res.message.message);
    }
    setLoadingZipcode(false);
  };

  useEffect(() => {
    setDistrictList([]);
    setSubDistrictList([]);
    formik.setFieldValue('province', '');
    formik.setFieldValue('district', null);
    formik.setFieldValue('subDistrict', null);
    if (!isNull(formik.values.zipcode) && formik.values.zipcode.length === 5) {
      getAddressByZipcode();
    }
  }, [formik.values.zipcode]);

  useEffect(() => {
    if (!isEmpty(formik.values.district)) {
      getSubDistrictByDistrictId();
    }
  }, [formik.values.district]);
  const getSubDistrictByDistrictId = async () => {
    const districtId = formik.values.district.id;
    const res = await apiAddress.getSubDistrictByDistrictId(districtId);
    if (!res.isError) {
      setSubDistrictList(res.data);
    }
  };
  const handleChangeDistrict = (event: ChangeEvent<{}>, option: any) => {
    formik.setFieldValue('district', option);
  };
  const handleChangeSubDistrict = (event: ChangeEvent<{}>, option: any) => {
    formik.setFieldValue('subDistrict', option);
  };

  useEffect(() => {
    if (open) {
      formik.resetForm();
    }
  }, [open]);
  return (
    <>
      <Dialog open={open}>
        <DialogContent ref={wrapperRef}>
          <FormModalStyle $width={492}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">เพิ่มที่อยู่สำหรับจัดส่ง</div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <form
                  onSubmit={formik.handleSubmit}
                  style={{
                    rowGap: '0',
                  }}
                >
                  <div
                    className="topic"
                    style={{
                      marginTop: '24px',
                    }}
                  >
                    ผู้รับ
                  </div>
                  <p>ชื่อจริง นามสกุล</p>
                  <TextField
                    type="text"
                    name="name"
                    placeholder="ชื่อจริง นามสกุล / ชื่อบริษัท"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={
                      formik.touched.name && (formik.errors.name as string)
                    }
                  />
                  <p>โทรศัพท์</p>
                  <TextField
                    name="phoneNumber"
                    placeholder="ระบุหมายเลขโทรศัพท์"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    inputProps={{ maxLength: 10 }}
                    onKeyPress={(event) => {
                      if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    error={
                      formik.touched.phoneNumber &&
                      Boolean(formik.errors.phoneNumber)
                    }
                    helperText={
                      formik.touched.phoneNumber &&
                      (formik.errors.phoneNumber as string)
                    }
                  />
                  <div className="topic mt-[20px]">ที่อยู่สำหรับจัดส่ง</div>
                  <p>ที่อยู่</p>
                  <TextField
                    type="text"
                    name="address"
                    placeholder="เลขที่, หมู่บ้าน, อาคาร, ถนน ฯลฯ"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.address && Boolean(formik.errors.address)
                    }
                    helperText={
                      formik.touched.address &&
                      (formik.errors.address as string)
                    }
                  />
                  <p className="field-title">รหัสไปรษณีย์</p>
                  <TextField
                    type="text"
                    name="zipcode"
                    placeholder="รหัสไปรษณีย์"
                    value={formik.values.zipcode}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => {
                      handleChangeZipcode(e.target.value);
                    }}
                    inputProps={{ maxLength: 5 }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          {loadingZipcode && <CircularProgress size={20} />}
                        </InputAdornment>
                      ),
                    }}
                    onKeyPress={(event) => {
                      if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    error={Boolean(formik.errors.zipcode)}
                    helperText={formik.errors.zipcode as string}
                  />
                  {!isEmpty(formik.values.province) && (
                    <div className="fade">
                      <p className="field-title">จังหวัด</p>
                      <TextField
                        type="text"
                        value={formik.values.province.name}
                        disabled
                      />
                    </div>
                  )}
                  {!isEmpty(districtList) && (
                    <div className="fade">
                      <p className="field-title">อำเภอ</p>
                      <Autocomplete
                        disablePortal
                        options={districtList}
                        getOptionLabel={(option) => option.name}
                        value={formik.values.district}
                        noOptionsText={'ไม่มีข้อมูล'}
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        renderInput={(params) => (
                          <TextField
                            type="text"
                            placeholder="เลือก หรือกรอก"
                            name="district"
                            value={
                              formik.values.district
                                ? formik.values.district.name
                                : ''
                            }
                            error={
                              formik.touched.district &&
                              Boolean(formik.errors.district)
                            }
                            helperText={
                              formik.touched.district &&
                              (formik.errors.district as string)
                            }
                            {...params}
                          />
                        )}
                        onChange={handleChangeDistrict}
                      />
                    </div>
                  )}
                  {!isEmpty(subDistrictList) && (
                    <div className="fade">
                      <p>ตำบล/แขวง</p>
                      <Autocomplete
                        disablePortal
                        options={subDistrictList}
                        getOptionLabel={(option) => option.name}
                        value={formik.values.subDistrict}
                        noOptionsText={'ไม่มีข้อมูล'}
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        renderInput={(params) => (
                          <TextField
                            type="text"
                            placeholder="เลือก หรือกรอก"
                            name="subDistrict"
                            value={
                              formik.values.subDistrict
                                ? formik.values.subDistrict.name
                                : ''
                            }
                            error={
                              formik.touched.subDistrict &&
                              Boolean(formik.errors.subDistrict)
                            }
                            helperText={
                              formik.touched.subDistrict &&
                              (formik.errors.subDistrict as string)
                            }
                            {...params}
                          />
                        )}
                        onChange={handleChangeSubDistrict}
                      />
                    </div>
                  )}
                  <div className="w-full flex justify-between mt-[34px] gap-5">
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="outlined"
                      color="blueGrey"
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => {
                        handleClose();
                      }}
                    >
                      ยกเลิก
                    </LoadingButton>
                    <LoadingButton
                      type="submit"
                      variant="contained"
                      color="dark"
                      disabled={disable}
                      sx={{
                        fontWeight: '600',
                        fontSize: '16px',
                      }}
                      fullWidth
                    >
                      {submitting ? <CircularProgress size={20} /> : 'บันทึก'}
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalShippingAddressForm;
