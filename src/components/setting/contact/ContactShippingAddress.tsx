import React, { useState } from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import { AddCircle } from '@mui/icons-material';
import ActionButton from '@/components/ActionButton';
import ModalShippingAddressForm from '@/components/setting/contact/ModalShippingAddressForm';
import apiContactAddress from '@/services/core/contactAddress';
import ContactPreviewAddress from '@/components/setting/contact/ContactPreviewAddress';
import { useAppDispatch, useAppSelector } from '@/store';
import { setSnackBar } from '@/store/features/alert';
import { isAllowed } from '@/utils/permission';
import { permissionSelector } from '@/store/features/permission/reducer';

const ContactShippingAddressStyled = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  display: flex;
  flex-direction: column;
  align-items: center;
  .empty-address {
    animation: ${LoadingFadein} 0.3s ease-in;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #cfd8dc;
    height: calc(100vh - 448px);
    justify-content: center;
    row-gap: 4px;
    .title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  .title-with-action {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48px;
    animation: ${LoadingFadein} 0.3s ease-in;
    .title {
      font-size: 22px;
      font-weight: 600;
    }
  }
  .shipping-address-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 40px;
    row-gap: 8px;
    animation: ${LoadingFadein} 0.3s ease-in;
  }
`;
type ContactShippingAddressProps = {
  addressByContactId: any;
  reloadAddressByContactId: () => void;
};
const initialModalAddress = {
  status: false,
  actionMode: 'create',
};
const ContactShippingAddress = ({
  addressByContactId,
  reloadAddressByContactId,
}: ContactShippingAddressProps) => {
  const dispatch = useAppDispatch();
  const [modalAddress, setModalAddress] = useState<any>(initialModalAddress);
  const [addressByAddressId, setAddressByAddressId] = useState<any>({});
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [disable, setDisable] = useState<boolean>(false);
  const { permissions } = useAppSelector(permissionSelector);
  const onClose = () => {
    setModalAddress({
      ...modalAddress,
      status: false,
    });
  };
  const handleSubmitModalAddress = async (value: any) => {
    setSubmitting(true);
    setDisable(true);
    if (modalAddress.actionMode === 'create') {
      const res = await apiContactAddress.createShippingAddressByContactId(
        value
      );
      if (!res.isError) {
        setModalAddress({
          ...modalAddress,
          status: false,
        });
        dispatch(
          setSnackBar({
            status: true,
            text: 'เพิ่มที่อยู่จัดส่งสำเร็จ',
            severity: 'success',
          })
        );
        reloadAddressByContactId();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    } else if (modalAddress.actionMode === 'edit') {
      const res = await apiContactAddress.updateShippingAddressByContactId(
        value
      );
      if (!res.isError) {
        setModalAddress({
          ...modalAddress,
          status: false,
        });
        dispatch(
          setSnackBar({
            status: true,
            text: 'แก้ไขที่อยู่จัดส่งสำเร็จ',
            severity: 'success',
          })
        );
        reloadAddressByContactId();
      } else {
        dispatch(
          setSnackBar({
            status: true,
            text: 'เกิดข้อผิดพลาด',
            severity: 'error',
          })
        );
      }
    }
    setSubmitting(false);
    setDisable(false);
  };
  const handleOpenShippingAddressModal = (actionMode: string) => {
    setModalAddress({
      status: true,
      actionMode: `${actionMode}`,
    });
  };
  const handleOpenEdit = async (data: any) => {
    setAddressByAddressId(data);
    setModalAddress({
      status: true,
      actionMode: 'edit',
    });
  };
  return (
    <ContactShippingAddressStyled>
      {isEmpty(addressByContactId) ? (
        <div className="empty-address">
          <div className="title">ไม่มีข้อมูล</div>
          <div>ผู้ติดต่อยังไม่มีข้อมูลสำหรับขอใบกำกับภาษี</div>
          <div
            style={{
              marginTop: '14px',
            }}
          >
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="เพิ่มที่อยู่"
              borderRadius={'8px'}
              onClick={() => {
                handleOpenShippingAddressModal('create');
              }}
            />
          </div>
        </div>
      ) : (
        <>
          <div className="title-with-action">
            <div className="title">ข้อมูลจัดส่งสินค้า</div>
            <ActionButton
              variant="outlined"
              color="blueGrey"
              icon={<AddCircle />}
              text="เพิ่มที่อยู่"
              borderRadius={'8px'}
              onClick={() => {
                handleOpenShippingAddressModal('create');
              }}
              disabled={!isAllowed(permissions, 'company.contact.update')}
            />
          </div>
          <div className="shipping-address-list">
            {addressByContactId.map((item: any, index: React.Key) => {
              return (
                <ContactPreviewAddress
                  key={index}
                  addressData={item}
                  reloadAddressByContactId={reloadAddressByContactId}
                  makeEditData={(data: any) => {
                    handleOpenEdit(data);
                  }}
                />
              );
            })}
          </div>
        </>
      )}
      <ModalShippingAddressForm
        open={modalAddress.status}
        onClose={() => {
          onClose();
        }}
        addressByAddressId={addressByAddressId}
        actionMode={modalAddress.actionMode as string}
        handleSubmitModalAddress={(value: any) => {
          handleSubmitModalAddress(value);
        }}
        submitting={submitting}
        disable={disable}
      />
    </ContactShippingAddressStyled>
  );
};

export default ContactShippingAddress;
