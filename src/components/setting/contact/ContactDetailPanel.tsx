import React, { useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import KebabTable from '@/components/KebabTable';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import IOSSwitch from '@/components/job/IOSSwitch';
import DataProductsOrderPanel from '@/components/job/DataProductsOrderPanel';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { Avatar } from '@mui/material';

const ContactDetailPanelStyles = styled.div`
  padding: 24px;
  border-bottom: 1px solid #dbe2e5;
  background: #fff;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  .box-detail-panel {
    display: flex;
    align-items: start;
    justify-content: space-between;
  }
  .box-btn {
    min-height: 100px;
    display: flex;
    align-items: end;
    flex-direction: column;
    justify-content: space-between;
    .date {
      font-size: 14px;
      color: #90a4ae;
    }
    .box-btn-action {
      font-size: 12px !important;
      display: flex;
      align-items: center;
      gap: 1rem;
      .btn-switch {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #dbe2e5;
        padding: 0 0.5rem;
        border-radius: 8px;
        min-height: 38px;
        label {
          margin: 0 !important;
        }
        .MuiSwitch-root {
          margin: 0;
          width: 38px;
          height: 22px;
          .MuiSwitch-thumb {
            width: 18px;
            height: 18px;
          }
          .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
            background-color: #16d5c5 !important;
          }
        }
      }
      .btn-kebab {
        border: 1px solid #dbe2e5;
        border-radius: 8px;
        padding: 2px;
      }
    }
  }
  .detail {
    display: flex;
    align-items: center;
    gap: 4rem;
    .image-name {
      display: flex;
      align-items: start;
      gap: 2rem;
      h2 {
        margin: 0;
      }
      .name-code {
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .tag {
          display: flex;
          align-items: center;
          .tag-icon {
            display: flex;
            align-items: center;
            justify-content: start;
            gap: 0.4rem;
            min-width: 60px;
          }
          .tag-customLevel {
            border-radius: 30px;
            padding: 4px 8px;
            background: #d0f7f3;
            &.level-2 {
              background: #fff5d3;
            }
            &.level-3 {
              background: #efeffd;
            }
          }
        }
      }
    }
    .value-contact {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      min-width: 200px;
      .value {
        display: flex;
        align-items: center;
        gap: 1rem;
      }
    }
  }
`;
type Props = {
  contactById: any;
};
const ContactDetailPanel = ({ contactById }: Props) => {
  const [isActive] = useState<boolean>(true);
  const customerText = (level: number, name: string) => {
    switch (level) {
      case 1:
        return (
          <div className={'tag-icon'}>
            <Image
              src={'/icons/ic_person.svg'}
              alt={'icon'}
              width={22}
              height={22}
            />
            {name}
          </div>
        );
      case 2:
        return (
          <div className={'tag-icon'}>
            <Image
              src={'/icons/crown.svg'}
              alt={'icon'}
              width={20}
              height={20}
            />
            {name}
          </div>
        );
      case 3:
        return (
          <div className={'tag-icon'}>
            <Image
              src={'/icons/ic_factory.svg'}
              alt={'icon'}
              width={20}
              height={20}
            />
            {name}
          </div>
        );
      default:
        return '';
    }
  };
  const dataPanel = [
    { label: 'รายการสั่งซื้อ', value: '', per: 'OD' },
    { label: 'รายการสินค้า', value: '', per: 'LD' },
    { label: 'มูลค่าที่รับชำระแล้ว', value: '', per: 'บาท' },
    { label: 'ค้างชำระเงิน', value: '', per: 'บาท' },
    { label: 'มูลค่าสุทธิที่ต้องรับชำระทั้งสิ้น', value: '', per: 'บาท' },
  ];
  return (
    <ContactDetailPanelStyles>
      <div className={'box-detail-panel'}>
        <div className={'detail'}>
          <div className={'image-name'}>
            <Avatar
              alt={contactById.name}
              src={
                !isEmpty(contactById?.imageUrl) ? contactById?.imageUrl : '#'
              }
              sx={{
                width: 100,
                height: 100,
                backgroundColor: '#30D5C7',
                textTransform: 'uppercase',
              }}
            />
            <div className={'name-code'}>
              <div>
                <h2>{contactById.name}</h2>
                <div>ID: {contactById.code}</div>
              </div>
              <div className={'tag'}>
                <div
                  className={`tag-customLevel level-${contactById.customerLevel}`}
                >
                  {customerText(
                    contactById.customerLevel,
                    contactById.customerLevelName
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={'value-contact'}>
            <div className={'value'}>
              <Image
                src={'/icons/person.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>{contactById.contactType?.name || '-'}</div>
            </div>
            <div className={'value'}>
              <Image
                src={'/icons/phone_in_talk.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>{contactById?.phoneNumber || '-'}</div>
            </div>
            <div className={'value'}>
              <Image
                src={'/icons/mail_FILL.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>{contactById?.email || '-'}</div>
            </div>
          </div>
          <div className={'value-contact'}>
            <div className={'value'}>
              <Image
                src={'/icons/percent.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>{contactById.creditEnum}</div>
            </div>
            <div className={'value'}>
              <Image
                src={'/icons/credit_card.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>
                {contactById.creditType
                  ? `เครดิต ${contactById.creditType.day} วัน`
                  : '-'}
              </div>
            </div>
            <div className={'value'}>
              <Image
                src={'/icons/money_bag.svg'}
                width={24}
                height={24}
                alt="icons"
              />
              <div>{contactById.depositType?.name || '-'}</div>
            </div>
          </div>
        </div>
        <div className={'box-btn'}>
          <div className={'box-btn-action'}>
            <div className={'btn-switch'}>
              เปิดใช้งาน
              <IOSSwitch
                value={isActive}
                onChangeActive={(checked: boolean) => {
                  console.log(checked);
                }}
              />
            </div>
            <div className={'btn-kebab pointer-events-none'}>
              <KebabTable
                item={''}
                iconHorizonDot={<MoreHorizRoundedIcon />}
                isRemove={true}
              />
            </div>
          </div>
          <div className={'date'}>
            การใช้งานล่าสุด {dayjs(new Date()).format('DD/MM/YYYY HH:mm น.')}
          </div>
        </div>
      </div>
      <div className={'box-data-detail-panel'}>
        <DataProductsOrderPanel data={dataPanel} />
      </div>
    </ContactDetailPanelStyles>
  );
};

export default ContactDetailPanel;
