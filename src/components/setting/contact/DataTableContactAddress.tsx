import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { isEmpty } from 'lodash';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import { useRouter } from 'next/router';
import { useAppDispatch } from '@/store';
import KebabTable from '@/components/KebabTable';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import apiContact from '@/services/core/contact';
import { setSnackBar } from '@/store/features/alert';
import ModalAddContactAddress from '@/components/setting/contact/modal/ModalAddContactAddress';

const DataTableContactAddressStyles = styled.div`
  padding: 24px;
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    h2 {
      margin: 0;
    }
  }
  .box-new-table {
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
    .MuiDataGrid-virtualScrollerRenderZone {
      .MuiDataGrid-row {
        &:last-child {
          > div {
            border-bottom: none !important;
          }
        }
      }
    }
  }
`;
const DataTableContactAddress = () => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState<boolean>(false);
  const [dataEdit, setDataEdit] = useState<any>();
  const [rows, setRows] = useState<any[]>([]);
  // const [totalElements, setTotalElements] = useState<number>(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    search: '',
    contactId: Number(id),
  });
  const columns: GridColDef[] = [
    {
      field: 'no',
      headerName: '#',
      headerAlign: 'left',
      align: 'left',
      maxWidth: 100,
      flex: 1,
    },
    {
      field: 'name',
      headerName: 'ชื่อ-นามสกุล',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: 'phoneNumber',
      headerName: 'โทรศัพท์',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: 'address',
      headerName: 'ที่อยู่',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
      renderCell: (params: any) => {
        return `${params.row.address}, ตำบล${params.row.subDistrict.name}, อำเภอ${params.row.district.name}, จังหวัด${params.row.province.name}, ${params.row.zipcode}`;
      },
    },
    {
      field: '#',
      headerName: 'จัดการ',
      headerAlign: 'right',
      align: 'right',
      flex: 0.5,
      width: 100,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div style={{ border: '1px solid #dbe2e5', borderRadius: '8px' }}>
            <KebabTable
              item={''}
              iconHorizonDot={<MoreHorizRoundedIcon />}
              isEdit={{
                status: true,
                action: () => {
                  setOpen(true);
                  setDataEdit(params.row);
                },
              }}
              // isRemove={true}
              isDelete={{
                status: true,
                action: () => onDeleteContactAddress(params.row.id),
              }}
            />
          </div>
        );
      },
    },
  ];
  const getContactAddressByContactId = async (id: number) => {
    const res = await apiContact.getContactAddressByContactId(id);
    if (!res.isError) {
      setRows(
        res.data.map((item: any, index: number) => {
          return { no: index + 1, ...item };
        })
      );
      // setTotalElements(res.data.totalElements);
    }
  };
  const onCreateContactAddress = async (data: any) => {
    const res = await apiContact.createContactAddress(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มข้อมูลรายการที่อยู่จัดส่งสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      getContactAddressByContactId(Number(id));
    }
  };
  const onUpdateContactAddress = async (data: any) => {
    const res = await apiContact.updateContactAddress(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขข้อมูลรายการที่อยู่จัดส่งสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      getContactAddressByContactId(Number(id));
    }
  };
  const onDeleteContactAddress = async (id: number) => {
    const res = await apiContact.deleteContactAddress(id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบข้อมูลรายการที่อยู่จัดส่งสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      getContactAddressByContactId(Number(id));
    }
  };
  useEffect(() => {
    if (id) getContactAddressByContactId(Number(id));
  }, [id]);
  return (
    <DataTableContactAddressStyles>
      <header>
        <h2>รายการที่อยู่จัดส่ง</h2>
        <Button
          onClick={() => setOpen(true)}
          variant={'contained'}
          startIcon={<AddRoundedIcon />}
        >
          เพิ่มรายการ
        </Button>
        <ModalAddContactAddress
          open={open}
          handleClose={() => {
            setOpen(false);
            setDataEdit({});
          }}
          dataEdit={dataEdit}
          onSubmit={(data: any) =>
            isEmpty(dataEdit)
              ? onCreateContactAddress(data)
              : onUpdateContactAddress(data)
          }
        />
      </header>
      <AppTableStyle $rows={rows}>
        <div className="content-wrap ">
          <div className={'box-new-table'}>
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="รายละเอียด" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={rows || []}
                columns={columns}
                paginationMode="server"
                rowCount={0}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
          <div className="pb-[10px]">
            <AppPagination
              filters={filters}
              totalElements={0}
              handleChangeFilters={(newValues: any) => {
                setFilters(newValues);
              }}
            />
          </div>
        </div>
      </AppTableStyle>
    </DataTableContactAddressStyles>
  );
};

export default DataTableContactAddress;
