import React, { useState } from 'react';
import styled from 'styled-components';
import apiContactAddress from '@/services/core/contactAddress';
import { IconButton, Menu, MenuItem } from '@mui/material';
import Image from 'next/image';
import { LoadingFadein } from '@/styles/share.styled';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const ContactPreviewAddressStyle = styled.div`
  width: 100%;
  min-height: 106px;
  display: flex;
  flex-direction: column;
  row-gap: 4px;
  padding: 0 24px;
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  justify-content: center;
  position: relative;
  animation: ${LoadingFadein} 0.3s ease-in;
  .name {
    font-size: 16px;
    font-weight: 600;
  }
`;

type ContactPreviewAddressProps = {
  addressData: any;
  makeEditData: (data: any) => void;
  reloadAddressByContactId: () => void;
};
const ContactPreviewAddress = ({
  addressData,
  makeEditData,
  reloadAddressByContactId,
}: ContactPreviewAddressProps) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const dispatch = useAppDispatch();
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const deleteAddress = async (id: number) => {
    handleClose();
    const res = await apiContactAddress.deleteAddress(id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบที่อยู่จัดส่งสินค้าสำเร็จ',
          severity: 'success',
        })
      );
      reloadAddressByContactId();
    } else {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เกิดข้อผิดพลาด',
          severity: 'error',
        })
      );
    }
  };

  return (
    <ContactPreviewAddressStyle>
      <div className="name">{addressData.name}</div>
      <div>โทร. {addressData.phoneNumber}</div>
      <div>
        {addressData.address} ต.{addressData.subDistrict.name} อ.
        {addressData.district.name} จ.{addressData.province.name}{' '}
        {addressData.zipcode}
      </div>
      <div>
        <IconButton
          size="small"
          onClick={handleClick}
          sx={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            borderRadius: '8px',
            height: '32px',
            width: '32px',
          }}
        >
          <div className="action-dot">
            <div className="dot" />
          </div>
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          sx={{
            '.MuiList-root': {
              padding: '8px',
            },
          }}
        >
          <MenuItem
            onClick={handleClose}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
            }}
          >
            <div
              className="drop-menu"
              onClick={() => {
                makeEditData(addressData);
              }}
            >
              <Image
                src="/icons/edit-black.svg"
                alt=""
                width={24}
                height={24}
              />
              แก้ไข
            </div>
          </MenuItem>
          <MenuItem
            onClick={() => {
              deleteAddress(addressData.id);
            }}
            sx={{
              padding: '0 8px',
              height: '40px',
              width: '100px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FDE8EF',
              },
            }}
          >
            <div className="drop-menu text-[#D32F2F]">
              <Image
                src="/icons/icon-trash-red.svg"
                alt=""
                width={24}
                height={24}
              />
              ลบ
            </div>
          </MenuItem>
        </Menu>
      </div>
    </ContactPreviewAddressStyle>
  );
};

export default ContactPreviewAddress;
