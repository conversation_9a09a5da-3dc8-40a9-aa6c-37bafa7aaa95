import React, { useState } from 'react';
import { Tab, Tabs, Tooltip } from '@mui/material';
import { ContactPreviewStyle } from '@/styles/contact.styled';
import Image from 'next/image';
import ContentCopyRoundedIcon from '@mui/icons-material/ContentCopyRounded';
import styled from 'styled-components';
import ContactInfo from '@/components/setting/contact/ContactInfo';
import ContactShippingAddress from '@/components/setting/contact/ContactShippingAddress';

const TabContactDetailStyle = styled(Tabs)`
  width: 800px;
  max-width: 100%;
  padding: 0 24px;
  .MuiTabs-flexContainer {
    display: flex;
    justify-content: space-between;
    button {
      flex: 1;
      font-size: 14px;
      height: 56px;
    }
  }
  .MuiTabs-indicator {
    height: 2px;
  }
  button {
    font-size: 14px;
    font-weight: 400;
    color: #607d8b;
    text-transform: initial;
    min-width: 0;
    padding: 0;
    span {
      display: none;
    }
  }
  .Mui-selected {
    color: #263238;
  }
`;

type ContactPreviewProps = {
  contactById: any;
  addressByContactId: any;
  reloadAddressByContactId: () => void;
};
const ContactPreview = ({
  contactById,
  addressByContactId,
  reloadAddressByContactId,
}: ContactPreviewProps) => {
  const [tab, setTab] = useState<number>(0);
  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
  };
  const handleChangeTab = (event: React.SyntheticEvent, newValue: number) => {
    setTab(newValue);
  };
  return (
    <>
      <ContactPreviewStyle>
        <div className="header">
          <div className="profile">
            <div className="image-wrap">
              <Image
                src={contactById.imageUrl || '/icons/blank-profile.svg'}
                width={100}
                height={100}
                alt=""
              />
            </div>
            <div className="name">{contactById.name}</div>
            <div className="code">
              <div className="id">ID : {contactById.code}</div>
              <Tooltip title={'คัดลอก'} placement="right" arrow>
                <div
                  className="copy"
                  onClick={() => {
                    copyToClipboard(`${contactById.code}`);
                  }}
                >
                  <ContentCopyRoundedIcon />
                </div>
              </Tooltip>
            </div>
          </div>
          <TabContactDetailStyle value={tab} onChange={handleChangeTab}>
            <Tab label="ข้อมูลส่วนตัว" />
            <Tab label="ที่อยู่สำหรับจัดส่ง" />
          </TabContactDetailStyle>
          <div className="line" />
        </div>
        <div className="content">
          {tab === 0 && <ContactInfo contactById={contactById} />}
          {tab === 1 && (
            <ContactShippingAddress
              addressByContactId={addressByContactId}
              reloadAddressByContactId={reloadAddressByContactId}
            />
          )}
        </div>
      </ContactPreviewStyle>
    </>
  );
};

export default ContactPreview;
