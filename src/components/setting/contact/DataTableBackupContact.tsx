import { Button } from '@mui/material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import KebabTable from '@/components/KebabTable';
import MoreHorizRoundedIcon from '@mui/icons-material/MoreHorizRounded';
import ModalAddBackupContact from '@/components/setting/contact/modal/ModalAddBackupContact';
import apiContact from '@/services/core/contact';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';

const DataTableBackupContactStyles = styled.div`
  padding: 24px;
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    h2 {
      margin: 0;
    }
  }
  .box-new-table {
    border: 1px solid #dbe2e5;
    border-radius: 16px;
    overflow: hidden;
    .MuiDataGrid-virtualScrollerRenderZone {
      .MuiDataGrid-row {
        &:last-child {
          > div {
            border-bottom: none !important;
          }
        }
      }
    }
  }
`;
const DataTableBackupContact = () => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const [open, setOpen] = useState<boolean>(false);
  const [dataEdit, setDataEdit] = useState<any>();
  const [rows, setRows] = useState<any[]>([]);
  const [totalElements, setTotalElements] = useState<number>(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    search: '',
    contactId: Number(id),
  });
  const columns: GridColDef[] = [
    {
      field: 'no',
      headerName: '#',
      headerAlign: 'left',
      align: 'left',
      maxWidth: 100,
      flex: 1,
    },
    {
      field: 'name',
      headerName: 'ชื่อ-นามสกุล',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 1,
    },
    // {
    //   field: '123',
    //   headerName: 'แผนก',
    //   headerAlign: 'left',
    //   align: 'left',
    //   minWidth: 128,
    //   flex: 0.7,
    // },
    {
      field: 'phoneNumber',
      headerName: 'โทรศัพท์',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: 'email',
      headerName: 'Email',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: 'lineId',
      headerName: 'Line ID',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      headerAlign: 'right',
      align: 'right',
      flex: 0.5,
      width: 100,
      cellClassName: 'stickyCell',
      renderCell: (params: any) => {
        return (
          <div style={{ border: '1px solid #dbe2e5', borderRadius: '8px' }}>
            <KebabTable
              item={''}
              iconHorizonDot={<MoreHorizRoundedIcon />}
              isEdit={{
                status: true,
                action: () => {
                  setOpen(true);
                  setDataEdit(params.row);
                },
              }}
              isDelete={{
                status: true,
                action: () => onDeleteContactAlternate(params.row.id),
              }}
            />
          </div>
        );
      },
    },
  ];
  const getPaginationContactAlternate = async (filters: any) => {
    const res = await apiContact.getPaginationContactAlternate(filters);
    if (!res.isError) {
      setRows(
        res.data.content.map((item: any, index: number) => {
          return { no: index + 1, ...item };
        })
      );
      setTotalElements(res.data.totalElements);
    }
  };
  const onCreateContactAlternate = async (data: any) => {
    const res = await apiContact.createContactAlternate(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'เพิ่มรายการผู้ติดต่อสำรองสำเร็จ',
          severity: 'success',
        })
      );
      getPaginationContactAlternate(filters);
    }
  };
  const onUpdateContactAlternate = async (data: any) => {
    const res = await apiContact.updateContactAlternate(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขรายการผู้ติดต่อสำรองสำเร็จ',
          severity: 'success',
        })
      );
      getPaginationContactAlternate(filters);
    }
  };
  const onDeleteContactAlternate = async (id: number) => {
    const res = await apiContact.deleteContactAlternate(id);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'ลบรายการผู้ติดต่อสำรองสำเร็จ',
          severity: 'success',
        })
      );
      getPaginationContactAlternate(filters);
    }
  };
  useEffect(() => {
    getPaginationContactAlternate(filters);
  }, [filters]);
  return (
    <DataTableBackupContactStyles>
      <header>
        <h2>ผู้ติดต่อสำรอง</h2>
        <Button
          onClick={() => setOpen(true)}
          variant={'contained'}
          startIcon={<AddRoundedIcon />}
        >
          เพิ่มรายการ
        </Button>
        <ModalAddBackupContact
          open={open}
          handleClose={() => {
            setOpen(false);
            setDataEdit({});
          }}
          dataEdit={dataEdit}
          onSubmit={(data: any) =>
            isEmpty(dataEdit)
              ? onCreateContactAlternate(data)
              : onUpdateContactAlternate(data)
          }
        />
      </header>
      <AppTableStyle $rows={rows}>
        <div className="content-wrap ">
          <div className={'box-new-table'}>
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="รายละเอียด" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={rows || []}
                columns={columns}
                paginationMode="server"
                rowCount={totalElements || 0}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
          <div className="pb-[10px]">
            <AppPagination
              filters={filters}
              totalElements={totalElements || 0}
              handleChangeFilters={(newValues: any) => {
                setFilters(newValues);
              }}
            />
          </div>
        </div>
      </AppTableStyle>
    </DataTableBackupContactStyles>
  );
};

export default DataTableBackupContact;
