import React from 'react';
import ActionButton from '@/components/ActionButton';
import Image from 'next/image';
import { useRouter } from 'next/router';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { useAppSelector } from '@/store';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';

const ContactInfoStyled = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  .title-with-action {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48px;
    .title {
      font-size: 22px;
      font-weight: 600;
    }
  }
  .list-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 40px;
    .list {
      min-height: 64px;
      display: flex;
      align-items: center;
      column-gap: 16px;
      border-top: 1px solid #dbe2e5;
      &:last-child {
        border-bottom: 1px solid #dbe2e5;
        margin-bottom: 40px;
      }
      .icon {
        display: flex;
        align-items: center;
        justify-items: center;
        width: 24px;
        height: 24px;
      }
      .value {
        font-size: 16px;
        display: flex;
        align-items: center;
        column-gap: 4px;

        .tag {
          padding: 0 16px 0 4px;
          border-radius: 20px;
          border: 1px solid #263238;
          display: flex;
          align-items: center;
          column-gap: 8px;
          font-size: 12px;
          height: 24px;
        }
      }
    }
  }
`;
type ContactInfoProps = {
  contactById: any;
};
const ContactInfo = ({ contactById }: ContactInfoProps) => {
  const router = useRouter();
  const { id } = router.query;
  const { permissions } = useAppSelector(permissionSelector);
  return (
    <ContactInfoStyled>
      <div className="title-with-action">
        <div className="title">ข้อมูลส่วนตัว</div>

        <ActionButton
          variant="outlined"
          color="blueGrey"
          icon={
            <Image
              src={'/icons/edit-black.svg'}
              width={24}
              height={24}
              alt=""
            />
          }
          text="แก้ไข"
          borderRadius={'8px'}
          onClick={() => {
            router.push(`/company/contact/${id}/edit`);
          }}
          disabled={!isAllowed(permissions, 'company.contact.update')}
        />
      </div>
      <div className="list-wrap">
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/account-circle-outline.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{contactById.contactType.name}</div>
        </div>
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/icon-id-card.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{`เลขผู้เสียภาษี ${contactById.taxNumber}`}</div>
        </div>
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/icon-group.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{contactById.contactRole?.name || '-'}</div>
        </div>
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/icon-percent.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{`เครดิต ${
            contactById.creditType?.day
              ? `${contactById.creditType.day} วัน`
              : '-'
          }`}</div>
        </div>
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/company-info/icon-phone.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{contactById.phoneNumber}</div>
        </div>
        <div className="list">
          <div className="icon">
            <Image
              src={'/icons/company-info/icon-email.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="value">{contactById.email}</div>
        </div>
        {/* <div className="list"> */}
        {/*  <div className="icon"> */}
        {/*    <Image */}
        {/*      src={'/icons/company-info/icon-address.svg'} */}
        {/*      width={24} */}
        {/*      height={24} */}
        {/*      alt="" */}
        {/*    /> */}
        {/*  </div> */}
        {/*  <div className="value"> */}
        {/*    {contactById.taxAddress} ต.{contactById.subDistrict?.name} อ. */}
        {/*    {contactById.district?.name} จ.{contactById.province?.name}{' '} */}
        {/*    {contactById.zipcode} */}
        {/*  </div> */}
        {/* </div> */}
      </div>
    </ContactInfoStyled>
  );
};

export default ContactInfo;
