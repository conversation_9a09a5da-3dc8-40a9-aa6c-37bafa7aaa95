import React, { useState } from 'react';
import styled from 'styled-components';
import { AppTableStyle, ScrollBarStyled } from '@/styles/share.styled';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import AppPagination from '@/components/global/AppPagination';
import { Button } from '@mui/material';

const DataTableCalculatePriceStyles = styled.div``;
const DataTableCalculatePrice = () => {
  const [rows] = useState<any[]>([]);
  const [totalElements] = useState<number>(0);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
  });
  const columns: GridColDef[] = [
    {
      field: 'no',
      headerName: 'เลข',
      headerAlign: 'left',
      align: 'left',
      maxWidth: 500,
      flex: 1,
    },
    {
      field: 'product',
      headerName: 'สินค้า',
      headerAlign: 'left',
      align: 'left',
      flex: 0.5,
    },
    {
      field: 'price',
      headerName: 'มูลค่ารวม (บาท)',
      headerAlign: 'left',
      align: 'left',
      flex: 0.5,
    },
    {
      field: 'status',
      headerName: 'สถานะ',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: 'user',
      headerName: 'ผู้ดูแลการผลิต',
      headerAlign: 'left',
      align: 'left',
      minWidth: 128,
      flex: 0.5,
    },
    {
      field: '#',
      headerName: 'จัดการ',
      headerAlign: 'right',
      align: 'right',
      flex: 0.5,
      width: 100,
      cellClassName: 'stickyCell',
      renderCell: () => {
        return (
          <>
            <Button variant={'outlined'}>รายละเอียด</Button>
          </>
        );
      },
    },
  ];
  return (
    <DataTableCalculatePriceStyles>
      <AppTableStyle $rows={rows}>
        <div className="content-wrap ">
          <div className={'box-new-table'}>
            <ScrollBarStyled>
              {/* <HeaderColumnAction text="รายละเอียด" width={100} /> */}
              <DataGrid
                hideFooter={true}
                rows={rows || []}
                columns={columns}
                paginationMode="server"
                rowCount={totalElements || 0}
                disableSelectionOnClick={false}
                autoHeight={true}
                sortModel={[]}
                getRowHeight={() => 56}
                headerHeight={48}
                components={{
                  NoRowsOverlay: () => <TableNoRowsOverlay />,
                  LoadingOverlay: () => <TableLoadingOverlay />,
                }}
              />
            </ScrollBarStyled>
          </div>
          <div className="px-4 pb-[10px]">
            <AppPagination
              filters={filters}
              totalElements={totalElements || 0}
              handleChangeFilters={(newValues: any) => {
                setFilters(newValues);
              }}
            />
          </div>
        </div>
      </AppTableStyle>
    </DataTableCalculatePriceStyles>
  );
};

export default DataTableCalculatePrice;
