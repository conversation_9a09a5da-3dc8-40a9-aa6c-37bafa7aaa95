import React, { useState } from 'react';
import { FilterWrapStyled, ScrollBarStyled } from '@/styles/share.styled';
import { Tab, Tabs } from '@mui/material';
import DataTableBackupContact from '@/components/setting/contact/DataTableBackupContact';
import DataTableCalculatePrice from '@/components/setting/contact/DataTableCalculatePrice';
import DataTableContactAddress from '@/components/setting/contact/DataTableContactAddress';

const DataTableContactDetail = () => {
  const [tabValue, setTabValue] = useState<number>(1);
  const [filters, setFilters] = useState({
    page: 0,
    size: 10,
    searchName: '',
  });
  const handleChangeTab = (_event: React.SyntheticEvent, newValue: number) => {
    setFilters({ ...filters });
    setTabValue(newValue);
  };
  const dataTab = [
    {
      label: 'คำนวณราคา',
      value: 1,
    },
    {
      label: 'รายการคำสั่งซื้อ',
      value: 2,
    },
    {
      label: 'ที่อยู่การจัดส่ง',
      value: 3,
    },
    {
      label: 'ทีอยู่ใบกำกับภาษี',
      value: 4,
    },
    {
      label: 'ผู้ติดต่อสำรอง',
      value: 5,
    },
  ];
  return (
    <div>
      <ScrollBarStyled>
        <FilterWrapStyled>
          <div
            style={{
              minWidth: '472px',
            }}
          >
            <Tabs
              value={tabValue}
              onChange={handleChangeTab}
              variant="standard"
            >
              {dataTab.map((item) => (
                <Tab
                  key={item.value}
                  label={
                    <div
                      className="flex items-center"
                      style={{
                        columnGap: '8px',
                        minHeight: '32px',
                      }}
                    >
                      {item.label}
                    </div>
                  }
                  value={item.value}
                  sx={{
                    color: tabValue !== item.value ? '#78909C' : '',
                  }}
                />
              ))}
            </Tabs>
          </div>
        </FilterWrapStyled>
      </ScrollBarStyled>
      <div>
        {tabValue !== 5 && tabValue !== 3 && <DataTableCalculatePrice />}
        {tabValue === 3 && <DataTableContactAddress />}
        {tabValue === 5 && <DataTableBackupContact />}
      </div>
    </div>
  );
};

export default DataTableContactDetail;
