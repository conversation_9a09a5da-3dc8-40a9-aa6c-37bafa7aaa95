import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import {
  Badge,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  FormControlLabel,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
} from '@mui/material';
import {
  AppTableStyle,
  FilterRoleStyled,
  FormModalStyle,
} from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { LoadingButton } from '@mui/lab';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { isEmpty } from 'lodash';
import useSWR from 'swr';
import AppPagination from '@/components/global/AppPagination';
import TuneRoundedIcon from '@mui/icons-material/TuneRounded';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import { Search } from '@mui/icons-material';
import CancelIcon from '@mui/icons-material/Cancel';
import { useDebounce } from 'usehooks-ts';
import { SelectRowsType } from '@/types/rolePermission';
import { FiltersType } from '@/types/app';
import apiPermission from '@/services/core/permission';
import apiRole from '@/services/core/role';
import IconCheckboxBlack from '@/components/IconCheckboxBlack';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';

type ModalPermissionProps = {
  open: boolean;
  onClose: () => void;
  onSubmitModal: (value: any) => void;
  rowsInForm: any;
};
const fetcher = async (params: FiltersType) => {
  return apiPermission.getData(params);
};
const fetcherRole = async (params: FiltersType) => {
  return apiRole.getRole(params);
};
const ModalPermission = (props: ModalPermissionProps) => {
  const { open, onClose, onSubmitModal, rowsInForm } = props;
  const [selectedId, setSelectedId] = useState<number[]>([]);
  const [openFilter, setOpenFilter] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<
    (SelectRowsType | undefined)[]
  >([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [hasInitialized, setHasInitialized] = useState<boolean>(false);
  const [hasInitializedModal, setHasInitializedModal] =
    useState<boolean>(false);
  const [stackRow, setStackRow] = useState<SelectRowsType[]>([]);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchInput, setSearchInput] = useState<string>('');
  const [keyword, setKeyword] = useState<string>('');
  const debouncedQ = useDebounce<string>(keyword, 600);
  const wrapperRef = useRef(null);
  const wrapperRefFilter = useRef(null);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
  });
  const { data: dataList, mutate: mutateDataList } = useSWR(filters, fetcher, {
    revalidateOnFocus: false,
  });
  const { data: roleList, isLoading: isLoadingRoleList } = useSWR(
    open
      ? {
          size: 100,
          page: 0,
        }
      : null,
    fetcherRole
  );

  useEffect(() => {
    if (!hasInitialized && rowsInForm) {
      setStackRow(rowsInForm);
      setHasInitialized(true);
    }
  }, []);
  const reloadDataList = async () => {
    const newData = await apiPermission.getData({
      ...filters,
      name: !isEmpty(keyword) ? keyword : null,
      roleIds: !isEmpty(selectedRoleIds) ? selectedRoleIds.toString() : null,
    });

    if (newData && newData.content && Array.isArray(newData.content)) {
      const mergedDataList = [
        ...stackRow,
        ...newData.content.filter(
          (item: SelectRowsType) =>
            !stackRow.some(
              (itemStackRow: SelectRowsType) => itemStackRow.id === item.id
            )
        ),
      ];
      setStackRow(mergedDataList);
      await mutateDataList(newData, { revalidate: false });
    } else {
      console.error('Invalid newData format');
    }
  };
  useEffect(() => {
    reloadDataList();
    setSearchLoading(false);
  }, [filters, debouncedQ]);
  const handleSelectAll = (event: any) => {
    if (event.target.checked) {
      const idsToAdd = dataList.content
        .filter((item: SelectRowsType) => !selectedId.includes(item.id))
        .map((item: SelectRowsType) => item.id);

      setSelectedId([...selectedId, ...idsToAdd]);
    } else {
      const idsToRemove = dataList.content
        .filter((item: SelectRowsType) => selectedId.includes(item.id))
        .map((item: SelectRowsType) => item.id);

      setSelectedId(
        selectedId.filter((id: number) => !idsToRemove.includes(id))
      );
    }
  };
  useEffect(() => {
    if (!hasInitializedModal && open) {
      const selectedRowsData = selectedId.map((id: number) => {
        return stackRow.find((row: SelectRowsType) => row.id === id);
      });
      const selectedOldRowsData = selectedId.map((id: number) => {
        return rowsInForm.find((row: SelectRowsType) => row.id === id);
      });
      const mergedSelectedRowsData = [
        ...selectedOldRowsData,
        ...selectedRowsData.filter(
          (item: any) =>
            item !== undefined &&
            !selectedOldRowsData.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
        ),
      ];
      setHasInitializedModal(true);
      setSelectedRows(mergedSelectedRowsData);
    } else {
      const selectedRowsData = selectedId.map((id: number) => {
        return stackRow.find((row: SelectRowsType) => row.id === id);
      });
      setSelectedRows(selectedRowsData);
    }
  }, [selectedId]);

  const columns: GridColDef[] = [
    {
      field: 'checkbox',
      headerName: '',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 52,
      disableColumnMenu: true,
      sortable: false,
      renderHeader: (_params: any) => (
        <Checkbox
          color="primary"
          checked={
            !isEmpty(selectedId) &&
            dataList?.content.every((item: SelectRowsType) =>
              selectedId.includes(item.id)
            )
          }
          onChange={(event: any) => {
            handleSelectAll(event);
          }}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
      renderCell: (params: any) => (
        <Checkbox
          color="primary"
          checked={selectedId.includes(params.id)}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          onChange={(e: any) => {
            handleSelected(e, params.row);
          }}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
    },
    {
      field: 'name',
      headerName: 'รายการสิทธิ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      minWidth: 184,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <Tooltip title={params.row.name} placement="right" arrow>
            <div className="overflow-hidden text-ellipsi">
              {params.row.name}
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: 'serviceGroup',
      headerName: 'กลุ่ม',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      minWidth: 184,
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      headerClassName: '!pr-[14px]',
      renderCell: (params: any) => {
        return (
          <Tooltip title={params.row.service} placement="left" arrow>
            <div className="text-[14px] text-[#CFD8DC] overflow-hidden text-ellipsis pr-[4px]">
              {params.row.service}
            </div>
          </Tooltip>
        );
      },
    },
  ];

  useOutsideAlerter(wrapperRef);
  useOutsideAlerterFilter(wrapperRefFilter);
  function useOutsideAlerter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        const target = event.target as HTMLElement;
        if (ref.current && !ref.current.contains(target)) {
          const classListArray = Array.from(target.classList);
          const classesToCheck = [
            'MuiBackdrop-root',
            'MuiSelect-select',
            'select-text',
            'MuiMenu-list',
            'MuiButtonBase-root',
          ];
          const checkValue = classesToCheck.some((className) =>
            classListArray.includes(className)
          );
          if (!checkValue) {
            onClose();
          }
        }
      }

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }
  function useOutsideAlerterFilter(ref: any) {
    useEffect(() => {
      function handleClickOutside(event: Event) {
        if (ref.current && !ref.current.contains(event.target)) {
          setOpenFilter(false);
        }
      }
      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref]);
  }

  useEffect(() => {
    if (open) {
      setSelectedId([]);
      const selectedIds = rowsInForm.map((item: SelectRowsType) => {
        return item.id;
      });
      setSelectedId(selectedIds);
    }
  }, [open]);
  const handleSelected = (e: any, row: SelectRowsType) => {
    if (e.target.checked) {
      if (!selectedId.includes(row.id)) {
        setSelectedId([...selectedId, row.id]);
      }
    } else {
      setSelectedId(selectedId.filter((item: number) => item !== row.id));
    }
  };

  // console.log('setSelectedId', selectedId);
  // console.log('selectedRows', selectedRows);
  // console.log('stackRow', stackRow);

  const handleSubmitModal = () => {
    onSubmitModal(selectedRows);
  };
  const handleClickRoleFilter = (e: any, id: number) => {
    if (e.target.checked) {
      setSelectedRoleIds([...selectedRoleIds, id]);
    } else {
      const removeRole = selectedRoleIds.filter((item: number) => item !== id);
      setSelectedRoleIds(removeRole);
    }
  };
  const handleSearchChange = (text: string) => {
    setSearchLoading(true);
    setSearchInput(text);
    setKeyword(text);
  };
  // console.log(dataList);
  return (
    <>
      <Dialog open={open}>
        <DialogContent
          sx={{
            padding: '0 0 !important',
            overflowX: 'hidden',
          }}
          ref={wrapperRef}
        >
          <FormModalStyle $width={640} style={{ maxHeight: '100%' }}>
            <div className="content-wrap">
              <div className="header" style={{ transform: 'none' }}>
                <div className="title">สิทธิการใช้งาน</div>
                <div
                  className="x-close"
                  onClick={() => {
                    onClose();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap data-grid-row-pointer">
                <FilterRoleStyled
                  $openFilter={openFilter}
                  style={{ padding: '16px 16px 0 16px' }}
                >
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="ค้นหาสิทธิ์"
                    value={searchInput}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => {
                      handleSearchChange(e.target.value);
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          {searchInput && (
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              size="small"
                              onClick={() => handleSearchChange('')}
                            >
                              {!searchLoading ? (
                                <CancelIcon />
                              ) : (
                                <CircularProgress size={20} />
                              )}
                            </IconButton>
                          )}
                        </InputAdornment>
                      ),
                    }}
                    style={{
                      height: '40px',
                    }}
                  />
                  <div className="filter-role" ref={wrapperRefFilter}>
                    <div className="filter-btn">
                      <Badge
                        badgeContent={selectedRoleIds.length}
                        color="primary"
                      >
                        <Button
                          type="button"
                          variant="outlined"
                          color="blueGrey"
                          onClick={() => {
                            setOpenFilter(!openFilter);
                          }}
                        >
                          <TuneRoundedIcon
                            style={{
                              fontSize: '20px',
                            }}
                          />
                        </Button>
                      </Badge>
                    </div>
                    {!isLoadingRoleList && (
                      <div className="filter-role-card">
                        <div className="header-card">
                          <div className="topic">กลุ่มสิทธิ</div>
                          <div
                            className="clear"
                            onClick={() => {
                              setSelectedRoleIds([]);
                            }}
                          >
                            ล้างทั้งหมด
                          </div>
                        </div>
                        <div className="checkbox-card">
                          {roleList &&
                            !isEmpty(roleList.content) &&
                            roleList.content.map(
                              (item: SelectRowsType, index: number) => (
                                <FormControlLabel
                                  key={index}
                                  control={
                                    <Checkbox
                                      color={'dark'}
                                      checked={selectedRoleIds.includes(
                                        item.id
                                      )}
                                      onClick={(event: any) => {
                                        handleClickRoleFilter(event, item.id);
                                      }}
                                      icon={<IconUnCheckbox />}
                                      checkedIcon={<IconCheckboxBlack />}
                                    />
                                  }
                                  label={item.name}
                                />
                              )
                            )}
                        </div>
                        <div className="btn-wrap">
                          <Button
                            type="button"
                            variant="contained"
                            color="dark"
                            size="small"
                            fullWidth
                            style={{
                              fontSize: '16px',
                            }}
                            onClick={() => {
                              reloadDataList();
                              setOpenFilter(false);
                            }}
                          >
                            แสดง
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </FilterRoleStyled>
                <form>
                  <AppTableStyle $rows={dataList?.content}>
                    <div className="content-wrap !min-h-0">
                      <DataGrid
                        hideFooter={true}
                        rows={
                          !isEmpty(dataList?.content) ? dataList.content : []
                        }
                        columns={columns}
                        disableSelectionOnClick={false}
                        autoHeight={true}
                        paginationMode="server"
                        rowCount={dataList?.totalElements || 0}
                        sortModel={[]}
                        getRowHeight={() => 42}
                        headerHeight={42}
                        components={{
                          NoRowsOverlay: () => <TableNoRowsOverlay />,
                          LoadingOverlay: () => <TableLoadingOverlay />,
                        }}
                      />
                    </div>
                  </AppTableStyle>
                  <div style={{ padding: '0 16px 16px 16px' }}>
                    <AppPagination
                      filters={filters}
                      totalElements={dataList?.totalElements || 0}
                      handleChangeFilters={(newValues: FiltersType) => {
                        setFilters(newValues);
                      }}
                    />
                  </div>
                  <div className="hr-custom" />
                  <div className={'w-full'} style={{ padding: '16px' }}>
                    <LoadingButton
                      type="button"
                      disabled={false}
                      variant="contained"
                      color="dark"
                      sx={{
                        boxShadow: 'none',
                        fontWeight: '600',
                        color: '#FFFFFF !important',
                        fontSize: '16px',
                      }}
                      fullWidth
                      onClick={() => {
                        handleSubmitModal();
                      }}
                    >
                      เพิ่มสิทธิ์
                    </LoadingButton>
                  </div>
                </form>
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ModalPermission;
