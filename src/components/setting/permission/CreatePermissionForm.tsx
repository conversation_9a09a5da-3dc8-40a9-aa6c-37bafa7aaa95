import React, { useEffect, useState } from 'react';
import { Button, Checkbox, CircularProgress, TextField } from '@mui/material';
import styled from 'styled-components';
import {
  AppContentStyle,
  AppTableStyle,
  LoadingFadein,
} from '@/styles/share.styled';
import * as yup from 'yup';
import { useFormik } from 'formik';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Image from 'next/image';
import { isEmpty } from 'lodash';
import ModalPermission from '@/components/setting/permission/ModalPermission';
import { numberWithCommas } from '@/utils/number';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import apiRole from '@/services/core/role';
import { useRouter } from 'next/router';
import AppPagination from '@/components/global/AppPagination';
import { FiltersType } from '@/types/app';
import { RoleByIdPermission, SelectRowsType } from '@/types/rolePermission';
import TableNoRowsOverlay from '@/components/TableNoRowsOverlay';
import TableLoadingOverlay from '@/components/TableLoadingOverlay';
import { useAppDispatch } from '@/store';
import { setSnackBar } from '@/store/features/alert';

const CreateRolePermissionStyle = styled.div`
  width: 100%;
  padding-bottom: 24px;
  display: flex;
  justify-content: center;
  animation: ${LoadingFadein} 0.3s ease-in;

  .form-wrap {
    width: 640px;
    max-width: 85%;
    display: flex;
    flex-direction: column;
    margin-top: 48px;
    &.animate {
      animation: ${LoadingFadein} 0.6s ease-in;
    }
    .MuiDataGrid-root {
      padding: 0 !important;
      .MuiDataGrid-columnHeaderTitle {
        font-size: 16px !important;
        font-weight: 600 !important;
      }
      span:first-child {
        div {
          display: flex;
          width: 24px;
          height: 24px;
        }
      }
      .MuiDataGrid-columnHeaderTitleContainer {
        overflow: visible !important;
        .MuiDataGrid-columnHeaderTitleContainerContent {
          overflow: visible !important;
        }
      }
      .MuiDataGrid-columnHeadersInner .MuiDataGrid-columnHeader {
        &:nth-child(2) {
          padding-left: 4px !important;
        }
      }
      .MuiDataGrid-cell {
        font-size: 16px !important;
        &:nth-child(2) {
          padding-left: 4px !important;
        }
      }
    }
    .text-error {
      color: #d32f2f;
    }
    .topic {
      font-size: 22px;
      font-weight: 600;
      margin: 80px 0 16px;
    }
    .permission-wrap {
      margin-top: 42px;
      width: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        button {
          border-radius: 8px;
          min-width: 0 !important;
          height: 40px !important;
        }
        .trash-btn {
          display: flex;
        }
      }
    }
  }
`;

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อตำแหน่ง'),
});
type RolePermission = {
  id: number;
  roleId: number;
  permissionId: number;
  permissionName: string;
  serviceName: string;
};

type Role = {
  id: number;
  name: string;
  description: string;
  createdDate: number;
  modifiedDate: number;
  rolePermissions: RolePermission[];
  isActive: boolean;
};

type CreatePermissionFormProps = {
  action: string;
  roleById?: Role;
};

const CreatePermissionForm = (props: CreatePermissionFormProps) => {
  const { action, roleById } = props;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [selectRows, setSelectRows] = useState<SelectRowsType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectDeleteId, setSelectDeleteId] = useState<number[]>([]);
  const [disable, setDisable] = useState<boolean>(false);
  const [filters, setFilters] = useState<FiltersType>({
    size: 10,
    page: 0,
  });
  const [open, setOpen] = useState<boolean>(false);
  const columns: GridColDef[] = [
    {
      field: 'checkbox',
      headerName: '',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      width: 52,
      disableColumnMenu: true,
      sortable: false,
      renderHeader: (_params: any) => (
        <Checkbox
          color="primary"
          checked={
            !isEmpty(selectDeleteId)
              ? selectRows.every((item: any) =>
                  selectDeleteId.includes(item.id)
                )
              : false
          }
          onChange={(event: any) => {
            handleSelectAll(event);
          }}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
      renderCell: (params: any) => (
        <Checkbox
          color="primary"
          checked={selectDeleteId.includes(params.id)}
          icon={<IconUnCheckbox />}
          checkedIcon={<IconCheckbox />}
          onChange={(e: any) => {
            handleSelected(e, params.row);
          }}
          sx={{
            marginLeft: '-8px',
          }}
        />
      ),
    },
    {
      field: 'name',
      headerName: 'รายการสิทธิ์',
      editable: false,
      headerAlign: 'left',
      align: 'left',
      flex: 1,
      minWidth: 184,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return <div>{params.row.name}</div>;
      },
    },
    {
      field: 'serviceGroup',
      headerName: 'กลุ่ม',
      editable: false,
      headerAlign: 'right',
      align: 'right',
      flex: 1,
      minWidth: 184,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: any) => {
        return (
          <div className="text-[14px] text-[#CFD8DC]">{params.row.service}</div>
        );
      },
    },
  ];
  const formik = useFormik({
    initialValues: {
      name: roleById ? roleById.name : '',
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: async (values: any) => {
      setLoading(true);
      const allValue = {
        ...values,
        permissions: selectRows.map((item: SelectRowsType) => item.id),
        description: '',
      };
      if (action === 'create') {
        const res = await apiRole.addCustomRole(allValue);
        if (!res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: 'บันทึกสิทธิ์สำเร็จ',
              severity: 'success',
            })
          );
          setDisable(true);
          await router.push('/company/manage-users/permissions');
        } else if (
          res.message.message === `Role name ${values.name} already exists`
        ) {
          dispatch(
            setSnackBar({
              status: true,
              text: res.message.message,
              severity: 'error',
            })
          );
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: 'เกิดข้อผดพลาด',
              severity: 'error',
            })
          );
        }
      } else if (action === 'edit') {
        const res = await apiRole.updateCustomRole({
          ...allValue,
          id: roleById?.id,
        });
        if (!res.isError) {
          dispatch(
            setSnackBar({
              status: true,
              text: 'บันทึกสิทธิ์สำเร็จ',
              severity: 'success',
            })
          );
          setDisable(true);
          await router.push('/company/manage-users/permissions');
        } else {
          dispatch(
            setSnackBar({
              status: true,
              text: 'เกิดข้อผดพลาด',
              severity: 'error',
            })
          );
        }
      }

      setLoading(false);
    },
  });
  useEffect(() => {
    if (action === 'edit' && roleById) {
      const oldRows = roleById.rolePermissions.map(
        (item: RoleByIdPermission) => {
          return {
            id: item.permissionId,
            name: item.permissionName,
            service: item.serviceName,
          };
        }
      );
      setSelectRows(oldRows);
    }
  }, [roleById]);

  const handleSelectAll = (event: any) => {
    if (event.target.checked) {
      const idsToAdd = selectRows
        .filter((item: SelectRowsType) => !selectDeleteId.includes(item.id))
        .map((item: SelectRowsType) => item.id);

      setSelectDeleteId([...selectDeleteId, ...idsToAdd]);
    } else {
      const idsToRemove = selectRows
        .filter((item: SelectRowsType) => selectDeleteId.includes(item.id))
        .map((item: SelectRowsType) => item.id);

      setSelectDeleteId(
        selectDeleteId.filter((id: number) => !idsToRemove.includes(id))
      );
    }
  };
  const handleSelected = (e: any, row: any) => {
    if (e.target.checked) {
      if (!selectDeleteId.includes(row.id)) {
        setSelectDeleteId([...selectDeleteId, row.id]);
      }
    } else {
      setSelectDeleteId(
        selectDeleteId.filter((item: number) => item !== row.id)
      );
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };
  const handleSubmitModal = async (value: any) => {
    setSelectRows(value);
    handleClose();
  };
  const handleDelete = () => {
    const newSelectRows = selectRows.filter(
      (row: SelectRowsType) => !selectDeleteId.includes(row.id)
    );

    setSelectRows(newSelectRows);
    setSelectDeleteId([]);
  };
  return (
    <>
      <AppContentStyle>
        <div className={`content-wrap`}>
          <CreateRolePermissionStyle>
            <div className="form-wrap">
              <form onSubmit={formik.handleSubmit}>
                <p>ตำแหน่ง</p>
                <TextField
                  type="text"
                  name="name"
                  placeholder="ชื่อตำแหน่ง"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
                <div className="permission-wrap">
                  <div className="header">
                    <div>
                      {numberWithCommas(selectRows.length)} สิทธิ์การใช้งาน
                    </div>
                    <div className="flex items-center gap-[8px]">
                      {!isEmpty(selectDeleteId) && (
                        <div
                          className="trash-btn"
                          onClick={() => {
                            handleDelete();
                          }}
                        >
                          <Button
                            variant="outlined"
                            color="blueGrey"
                            style={{
                              padding: 0,
                              width: '40px',
                            }}
                          >
                            <Image
                              src={'/icons/icon-trash-red.svg'}
                              width={24}
                              height={24}
                              alt=""
                            />
                          </Button>
                        </div>
                      )}
                      <div
                        onClick={() => {
                          onOpen();
                        }}
                      >
                        <ActionButton
                          variant="outlined"
                          color="blueGrey"
                          icon={<AddCircle />}
                          text="เพิ่มสิทธิ์"
                        />
                      </div>
                    </div>
                  </div>
                  <AppTableStyle $rows={selectRows}>
                    <div className="content-wrap !min-h-0">
                      <DataGrid
                        hideFooter={true}
                        rows={selectRows}
                        columns={columns}
                        disableSelectionOnClick={false}
                        autoHeight={true}
                        sortModel={[]}
                        pageSize={filters.size}
                        page={filters.page}
                        getRowHeight={() => 48}
                        headerHeight={48}
                        components={{
                          NoRowsOverlay: () => <TableNoRowsOverlay />,
                          LoadingOverlay: () => <TableLoadingOverlay />,
                        }}
                      />
                      {!isEmpty(selectRows) && (
                        <AppPagination
                          filters={filters}
                          totalElements={selectRows.length}
                          handleChangeFilters={(newValues: FiltersType) => {
                            setFilters(newValues);
                          }}
                        />
                      )}
                    </div>
                  </AppTableStyle>
                </div>
                <div className="mt-[40px]" />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="dark"
                  disabled={disable}
                >
                  {loading ? <CircularProgress size={25} /> : 'บันทึก'}
                </Button>
              </form>
            </div>
            <ModalPermission
              open={open}
              onClose={() => {
                handleClose();
              }}
              onSubmitModal={(value: any) => {
                handleSubmitModal(value);
              }}
              rowsInForm={selectRows}
            />
          </CreateRolePermissionStyle>
        </div>
      </AppContentStyle>
    </>
  );
};

export default CreatePermissionForm;
