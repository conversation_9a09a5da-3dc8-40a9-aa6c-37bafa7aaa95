import React, { useEffect, useState } from 'react';
import Image<PERSON>ield from '@/components/ImageField';
import {
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import styled, { css } from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import * as yup from 'yup';
import { useFormik } from 'formik';
import apiBusiness from '@/services/core/business';
import apiCompany from '@/services/core/company';
import { isEmpty } from 'lodash';
import { useAppDispatch, useAppSelector } from '@/store';
import { getUserProfile, userSelector } from '@/store/features/user';
import Swal from 'sweetalert2';
import { showAsideSelector } from '@/store/features/layout';
import SignatureUpload from '@/components/setting/company/SignatureUpload';
import { setSnackBar } from '@/store/features/alert';
import CompanyWorkingDayForm from '@/components/setting/company/CompanyWorkingDayForm';
import ToggleButtonGroup from '@/components/common/ToggleButtonGroup';
// import { DemoContainer } from '@mui/x-date-pickers/internals/demo';

const CompanyInfoStyle = styled.div<{ $isCheck: boolean }>`
  width: 100%;
  padding-bottom: 24px;
  display: flex;
  justify-content: center;
  animation: ${LoadingFadein} 0.3s ease-in;
  .form-wrap {
    width: 640px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 40px;
    padding: 0 24px;
    &.animate {
      animation: ${LoadingFadein} 0.6s ease-in;
    }
    form {
      animation: ${LoadingFadein} 0.6s ease-in;
    }

    .text-error {
      color: #d32f2f;
    }
    .topic {
      font-size: 22px;
      font-weight: 600;
      margin: 16px 0 16px;
    }
    .check-box {
      label {
        display: flex;
        align-items: start;
        column-gap: 8px;
        position: relative;
        cursor: pointer;
        user-select: none;
        font-size: 16px;
        @media screen and (max-width: 480px) {
          font-size: 14px;
        }
        .icon {
          opacity: 0;
          justify-content: center;
          align-items: center;
          position: absolute;
          color: white;
          height: 24px;
          aspect-ratio: 1/1;
          display: flex;
          transition: 0.15s;
          @media screen and (max-width: 480px) {
            height: 20px;
          }
          ${({ $isCheck }) =>
            $isCheck &&
            css`
              opacity: 1 !important;
            `}
          svg {
            font-size: 18px;
          }
        }
        input {
          margin: 0;
          height: 24px;
          aspect-ratio: 1/1;
          border-radius: 6px;
          appearance: none;
          transition: 0.15s;
          border: 2px solid #dbe2e5;
          @media screen and (max-width: 480px) {
            height: 20px;
          }
          ${({ $isCheck }) =>
            $isCheck &&
            css`
              background-color: #263238 !important;
              border: none;
            `}
        }
      }
    }
  }
`;

const validationSchema = yup.object({
  name: yup.string().required('กรุณากรอกชื่อชื่อบริษัท'),
  documentIssuerName: yup
    .string()
    .required('กรุณากรอกชื่อชื่อบริษัท (สำหรับออกเอกสาร)'),
  businessTypeId: yup.number().required('กรุณาระบุประเภทธุรกิจ'),
  taxId: yup
    .string()
    .matches(/^[0-9]{13}$/, 'หมายเลขผู้เสียภาษีต้องมี 13 หลัก')
    .required('กรุณากรอกหมายเลขผู้เสียภาษี'),
  phoneNumber: yup
    .string()
    .matches(/^[0-9]{10}$/, 'เบอร์โทรศัพท์ต้องมี 10 หลัก'),
  email: yup.string().email('รูปแบบอีเมล์ไม่ถูกต้อง'),
});
type CompanyInfoFormProps = {
  isCheck: boolean;
  handleIsCheck: (val: boolean) => void;
  handleUpdateCompany: (val: any) => void;
  loading: boolean;
};
export type businessType = {
  id: number;
  name: string;
  description: string;
};
const CompanyInfoForm = (props: CompanyInfoFormProps) => {
  const { isCheck, handleIsCheck, handleUpdateCompany, loading } = props;
  const { user } = useAppSelector(userSelector);
  const dispatch = useAppDispatch();
  const [businessType, setBusinessType] = useState<businessType[]>([]);
  const [companyById, setCompanyById] = useState<any>({});
  const { playSwitchAnimation } = useAppSelector(showAsideSelector);
  const [tab, setTab] = useState<string>('ข้อมูล');
  // const token = getCookie('access_token');
  const formik = useFormik({
    initialValues: {
      imageFile: '',
      name: '',
      documentIssuerName: '',
      businessTypeId: '',
      taxId: '',
      address: '',
      phoneNumber: '',
      email: '',
      website: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      const sendValue = {
        ...values,
        id: companyById.id,
      };
      handleUpdateCompany(sendValue);
    },
  });

  const fetchCompanyById = async (id: number) => {
    const res = await apiCompany.getMeCompanyById(id);
    if (!res.isError) {
      setCompanyById(res.data);
    }
  };

  useEffect(() => {
    if (user && !isEmpty(user)) {
      fetchCompanyById(user.company.id).then();
    }
  }, [user]);

  const getBusinessType = async () => {
    const res = await apiBusiness.getBusinessType();
    if (!res.isError) {
      setBusinessType(res.data);
    }
  };

  useEffect(() => {
    getBusinessType().then();
  }, []);

  useEffect(() => {
    if (companyById && !isEmpty(companyById)) {
      const {
        businessType,
        name,
        documentIssuerName,
        taxId,
        address,
        phoneNumber,
        email,
        website,
      } = companyById;
      formik.setFieldValue('businessTypeId', businessType.id || '');
      formik.setFieldValue('name', name || '');
      formik.setFieldValue('documentIssuerName', documentIssuerName || '');
      formik.setFieldValue('taxId', taxId || '');
      formik.setFieldValue('address', address || '');
      formik.setFieldValue('phoneNumber', phoneNumber || '');
      formik.setFieldValue('email', email || '');
      formik.setFieldValue('website', website || '');
    }
  }, [companyById]);

  const swalSubmitPop = () => {
    Swal.fire({
      title: 'ยืนยันบันทึกข้อมูล',
      text:
        'คุณได้ทำการตรวจสอบความถูกต้องของการตั้งค่า\n' +
        'ข้อมูลบริษัททั้งหมดแล้ว?',
      showCancelButton: true,
      showConfirmButton: true,
      showCloseButton: true,
      confirmButtonText: 'ยืนยัน',
      cancelButtonText: 'ยกเลิก',
      reverseButtons: true,
      customClass: {
        popup: 'w-[600px] flex justify-center p-[40px] pt-[16px]',
        closeButton: 'swal-close',
        title: '!text-[#263238] mb-[16px] !text-[28px]',
        htmlContainer: 'w-[338px] max-w-[85%] !m-auto !text-[#263238]',
        actions: '!max-w-[100%]',
        confirmButton:
          'w-[248px] !max-w-[100%] font-prompt !text-[16px] !font-[400] !shadow-none',
        cancelButton:
          'w-[248px] !max-w-[100%] font-prompt !text-[16px] !font-[400] !bg-[#fff] !text-[#263238] ' +
          'border-cancel-btn-swal !shadow-none !bg-none',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        formik.handleSubmit();
      }
    });
  };

  const updateLogo = async (imageFile: any) => {
    if (imageFile && imageFile.length === 1) {
      const formData = new FormData();
      formData.append('file', imageFile[0]);
      const resImageFile = await apiCompany.updateCompanyLogo(formData);
      if (!resImageFile.isError) {
        await dispatch(getUserProfile());
        dispatch(
          setSnackBar({
            status: true,
            text: 'บันทึกสำเร็จ',
            severity: 'success',
          })
        );
      }
    }
  };

  return (
    <>
      {companyById &&
        !isEmpty(companyById) &&
        user &&
        !isEmpty(user.company) && (
          <CompanyInfoStyle $isCheck={isCheck}>
            <div
              className={`form-wrap ${playSwitchAnimation ? 'animate' : ''}`}
            >
              <ImageField
                defaultBackground={
                  user.company.logo !== null
                    ? user.company.logo
                    : '/images/company/empty-company.svg'
                }
                textUploadBtn="Upload"
                handleChange={(files) => {
                  updateLogo(files).then();
                }}
              />
              {/* <TabContainerStyled> */}
              {/*  <div */}
              {/*    className={`tab-item left ${tab === 'info' && 'active'}`} */}
              {/*    onClick={() => setTab('info')} */}
              {/*  > */}
              {/*    ข้อมูล */}
              {/*  </div> */}
              {/*  <div */}
              {/*    className={`tab-item ${tab === 'workingDay' && 'active'}`} */}
              {/*    onClick={() => setTab('workingDay')} */}
              {/*  > */}
              {/*    เวลาทำงาน */}
              {/*  </div> */}
              {/*  <div */}
              {/*    className={`tab-item right ${ */}
              {/*      tab === 'signature' && 'active' */}
              {/*    }`} */}
              {/*    onClick={() => setTab('signature')} */}
              {/*  > */}
              {/*    ตรายางอิเล็กทรอนิกส์ */}
              {/*  </div> */}
              {/* </TabContainerStyled> */}
              <div className={'py-6'}>
                <ToggleButtonGroup
                  buttonItem={[
                    'ข้อมูล',
                    'ข้อมูลเวลาทำการ',
                    'ตรายางอิเล็กทรอนิกส์',
                  ]}
                  value={tab}
                  handleToggle={(value: string) => {
                    setTab(value);
                  }}
                />
              </div>

              {tab === 'ข้อมูล' && (
                <form onSubmit={formik.handleSubmit}>
                  <div className="topic !mt-0">ข้อมูลบริษัท</div>
                  <p>
                    <span className="!m-0 !mr-[4px] text-error">*</span>
                    ชื่อบริษัท
                  </p>
                  <TextField
                    type="text"
                    name="name"
                    placeholder="Company Profile Name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={formik.touched.name && formik.errors.name}
                  />
                  <p>
                    <span className="!m-0 !mr-[4px] text-error">*</span>
                    ชื่อบริษัท (สำหรับออกเอกสาร)
                  </p>
                  <TextField
                    type="text"
                    name="documentIssuerName"
                    placeholder="ชื่อบริษัท (สำหรับออกเอกสาร)"
                    value={formik.values.documentIssuerName}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.documentIssuerName &&
                      Boolean(formik.errors.documentIssuerName)
                    }
                    helperText={
                      formik.touched.documentIssuerName &&
                      formik.errors.documentIssuerName
                    }
                  />
                  <p>
                    <span className="!m-0 !mr-[4px] text-error">*</span>
                    ประเภทธุรกิจ
                  </p>
                  <FormControl sx={{ width: '100%' }}>
                    <Select
                      sx={{
                        height: '40px',
                        border: '0',
                      }}
                      displayEmpty
                      name="businessTypeId"
                      value={formik.values.businessTypeId}
                      onChange={formik.handleChange}
                      input={<OutlinedInput />}
                      renderValue={(selected) => {
                        if (!selected) {
                          return (
                            <div className="text-[14px] text-[#B0BEC5]">
                              ระบุประเภท
                            </div>
                          );
                        }
                        const selectedItem = businessType.find(
                          (item: any) => item.id === selected
                        );
                        return selectedItem ? selectedItem.name : '';
                      }}
                      error={
                        formik.touched.businessTypeId &&
                        Boolean(formik.errors.businessTypeId)
                      }
                    >
                      <MenuItem disabled value="">
                        <em>-- Please select --</em>
                      </MenuItem>
                      {businessType.map((name: any, index: number) => (
                        <MenuItem key={index} value={name.id}>
                          {name.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.businessTypeId &&
                      formik.errors.businessTypeId && (
                        <FormHelperText error>
                          {formik.errors.businessTypeId}
                        </FormHelperText>
                      )}
                  </FormControl>
                  <p>
                    <span className="!m-0 !mr-[4px] text-error">*</span>
                    เลขประจำตัวผู้เสียภาษี
                  </p>
                  <TextField
                    name="taxId"
                    placeholder="หมายเลขผู้เสียภาษี"
                    value={formik.values.taxId}
                    onChange={formik.handleChange}
                    inputProps={{ maxLength: 13 }}
                    onKeyPress={(event) => {
                      if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    error={formik.touched.taxId && Boolean(formik.errors.taxId)}
                    helperText={formik.touched.taxId && formik.errors.taxId}
                  />
                  <div className="topic">การติดต่อ</div>
                  <p>ที่อยู่บริษัท</p>
                  <TextField
                    type="text"
                    name="address"
                    placeholder="ที่อยู่สำหรับออกเอกสาร"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.address && Boolean(formik.errors.address)
                    }
                    helperText={formik.touched.address && formik.errors.address}
                  />
                  <p>โทรศัพท์</p>
                  <TextField
                    name="phoneNumber"
                    placeholder="หมายเลขโทรศัพท์"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    inputProps={{ maxLength: 10 }}
                    onKeyPress={(event) => {
                      if (!/^[0-9]*\.?[0-9]*$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    error={
                      formik.touched.phoneNumber &&
                      Boolean(formik.errors.phoneNumber)
                    }
                    helperText={
                      formik.touched.phoneNumber && formik.errors.phoneNumber
                    }
                  />
                  <p>อีเมล</p>
                  <TextField
                    type="email"
                    name="email"
                    placeholder="Email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                  <p>เว็บไซต์</p>
                  <TextField
                    type="text"
                    name="website"
                    placeholder="URL"
                    value={formik.values.website}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.website && Boolean(formik.errors.website)
                    }
                    helperText={formik.touched.website && formik.errors.website}
                  />

                  <div className="check-box py-10">
                    <label>
                      <div className="icon">
                        <CheckIcon />
                      </div>
                      <input
                        type="checkbox"
                        onChange={(event) => {
                          handleIsCheck(event.target.checked);
                        }}
                      />
                      <div className="ml-1">
                        ฉันยืนยันข้อมูลบริษัทที่ระบุมาทั้งหมดนี้เป็นข้อมูลจริง
                      </div>
                    </label>
                  </div>
                  <Button
                    fullWidth
                    variant="contained"
                    color="dark"
                    disabled={!isCheck}
                    onClick={() => {
                      swalSubmitPop();
                    }}
                  >
                    {loading ? <CircularProgress size={25} /> : 'บันทึกข้อมูล'}
                  </Button>
                </form>
              )}
              {tab === 'ตรายางอิเล็กทรอนิกส์' && <SignatureUpload />}
              {tab === 'ข้อมูลเวลาทำการ' && (
                <CompanyWorkingDayForm companyById={companyById} />
              )}
            </div>
          </CompanyInfoStyle>
        )}
    </>
  );
};

export default CompanyInfoForm;
