import UploadFile from '@/components/global/UploadFile';
import { Button, CircularProgress } from '@mui/material';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import apiCompany from '@/services/core/company';
import Swal from 'sweetalert2';
import { LoadingFadein } from '@/styles/share.styled';

const SignatureUpload = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [file, setFile] = useState<boolean>(false);
  const [signatureImage, setSignatureImage] = useState<string | null>(null);

  useEffect(() => {
    fetchSignature();
  }, []);

  const fetchSignature = async () => {
    const res: any = await apiCompany.getCompanySignature();
    if (res.status) {
      setSignatureImage(res.data.signatureImage);
    }
  };
  const onUpload = async () => {
    if (file) {
      setLoading(true);
      const formData: any = new FormData();
      formData.append('file', file);
      const response = await apiCompany.updateCompanySignature(formData);
      if (response.status) {
        Swal.fire({
          title: 'บันทึกสำเร็จ',
          text: 'สร้างตรายางอิเล็กทรอนิกส์สำเร็จ',
          icon: 'success',
        }).then(() => {
          fetchSignature();
          setLoading(false);
        });
      } else {
        Swal.fire({
          title: 'บันทึกไม่สำเร็จ',
          text: 'โปรดลองใหม่อีกครั้ง',
          icon: 'error',
        }).then(() => {
          setLoading(false);
        });
      }
    }
  };

  const onConfirmDelete = async () => {
    Swal.fire({
      title: 'ต้องการลบตรายางอิเล็กทรอนิกส์หรือไม่',
      // text: 'ลบตรายางอิเล็กทรอนิกส์สำเร็จ',
      icon: 'question',
      confirmButtonText: 'ลบ',
      showCancelButton: true,
      cancelButtonText: 'ยกเลิก',
    }).then(async (result) => {
      if (result.isConfirmed) {
        onDelete();
      }
    });
  };

  const onDelete = async () => {
    const response = await apiCompany.deleteCompanySignature();
    if (response.status) {
      Swal.fire({
        title: 'ลบสำเร็จ',
        text: 'ลบตรายางอิเล็กทรอนิกส์สำเร็จ',
        icon: 'success',
      }).then(() => {
        setSignatureImage(null);
      });
    } else {
      Swal.fire({
        title: 'ลบไม่สำเร็จ',
        text: 'โปรดลองใหม่อีกครั้ง',
        icon: 'error',
      });
    }
  };

  return (
    <SignatureContainer>
      <div className={'title'}>ตรายางอิเล็กทรอนิกส์</div>
      <UploadFile
        imgUrl={signatureImage || null}
        sizeKBLimit={800}
        onFileChange={(file: any) => {
          setFile(file);
        }}
        onRemove={() => onConfirmDelete()}
      />
      <div className={'btn'}>
        <Button
          fullWidth
          variant="contained"
          color="dark"
          onClick={() => onUpload()}
        >
          {loading ? <CircularProgress size={25} /> : 'บันทึกตรายาง'}
        </Button>
      </div>
    </SignatureContainer>
  );
};

export default SignatureUpload;

const SignatureContainer = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  animation: ${LoadingFadein} 0.6s ease-in;
  .title {
    font-size: 22px;
  }
  .btn {
    svg {
      color: #fff !important;
    }
  }
`;
