import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import { useAppSelector } from '@/store';
import { userSelector } from '@/store/features/user';
import apiCompany from '@/services/core/company';
import styled from 'styled-components';
import { dateThaiFormat } from '@/utils/date';
import { LoadingFadein } from '@/styles/share.styled';
import { showAsideSelector } from '@/store/features/layout';
import apiCompanyWorking from '@/services/core/company-working';
import dayjs from 'dayjs';
import { permissionSelector } from '@/store/features/permission/reducer';
import { isAllowed } from '@/utils/permission';
import NotPermission from '@/components/NotPermission';

const CompanyInfoWrap = styled.div`
  width: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
  display: flex;
  justify-content: center;
  .profile-wrap {
    display: flex;
    flex-direction: column;
    width: 100%;
    &.animate {
      animation: ${LoadingFadein} 0.6s ease-in;
    }
    .profile {
      display: flex;
      justify-content: center;
      position: relative;
      height: 208px;
      .profile-pic {
        position: absolute;
        bottom: 0;
        width: 108px;
        height: 108px;
        border-radius: 50%;
        background-color: #fff;
        box-shadow: 0px 0px 8px 0px #2632381f;
        transform: translateY(50%);
        justify-content: center;
        align-items: center;
        display: flex;
        .profile-pic-bg {
          width: 100px;
          height: 100px;
          background-color: #f5f7f8;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          overflow: hidden;
        }
      }
      .company-name {
        font-size: 28px;
        font-weight: 600;
        position: absolute;
        bottom: -104px;
        @media screen and (max-width: 500px) {
          font-size: 24px;
        }
      }
    }
    .form-preview-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
      .form-preview {
        position: relative;
        width: 640px;
        margin-top: 168px;
        max-width: 100%;
        padding: 0 24px;
        .topic {
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 8px;
          @media screen and (max-width: 500px) {
            font-size: 18px;
          }
        }
        .list {
          width: 100%;
          border-bottom: 1px solid #dbe2e5;
          display: flex;
          justify-content: space-between;
          height: 64px;
          &:last-child {
            border: 0;
          }
          .right-group {
            display: flex;
            align-items: center;
            column-gap: 16px;
            &.time {
              justify-content: space-between;
              width: 100%;
            }
            .day {
              min-width: 100px;
            }
            .icon {
              width: 24px;
              height: 24px;
              position: relative;
            }
            .name {
              font-weight: 400;
            }
          }
          .left-group {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: 400;
            color: #b0bec5;
          }
        }
      }
    }
  }
`;
const CompanyInfoFormPreview = () => {
  const { user } = useAppSelector(userSelector);
  const [dataWorkingDay, setDataWorkingDay] = useState<any>([]);
  const [companyById, setCompanyById] = useState<any>({});
  const { playSwitchAnimation } = useAppSelector(showAsideSelector);
  const { permissions } = useAppSelector(permissionSelector);
  const fetchCompanyById = async (id: number) => {
    const res = await apiCompany.getMeCompanyById(id);
    if (!res.isError) {
      setCompanyById(res.data);
    }
  };
  const getCompanyWorkingByCompany = async (params: any) => {
    const res = await apiCompanyWorking.getCompanyWorkingByCompany(params);
    if (!res.isError) {
      console.log(res.data);
      setDataWorkingDay(res.data);
    }
  };
  useEffect(() => {
    if (companyById?.id)
      getCompanyWorkingByCompany({ companyId: companyById.id, isActive: true });
  }, [companyById]);
  useEffect(() => {
    if (user && !isEmpty(user)) {
      fetchCompanyById(user.company.id);
    }
  }, [user]);

  return (
    <>
      {companyById &&
        !isEmpty(companyById) &&
        isAllowed(permissions, 'company.info.list') && (
          <CompanyInfoWrap>
            <div
              className={`profile-wrap ${playSwitchAnimation ? 'animate' : ''}`}
            >
              <section className="profile">
                <Image
                  src="/images/company/company-info-bg.svg"
                  alt=""
                  fill
                  quality={100}
                  style={{ objectFit: 'cover' }}
                />
                <div className="profile-pic">
                  <div className="profile-pic-bg">
                    <Image
                      src={
                        companyById.logo
                          ? companyById.logo
                          : '/images/company/company-info-empty-profile.svg'
                      }
                      alt=""
                      fill
                      quality={100}
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                </div>
                <div className="company-name">
                  {companyById.name ? companyById.name : '-'}
                </div>
              </section>
              <section className="form-preview-wrap">
                <div className="form-preview">
                  <div className="topic">ข้อมูลบริษัท</div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-name.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.name ? companyById.name : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-name-paper.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.documentIssuerName
                          ? companyById.documentIssuerName
                          : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-business-type.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.businessType.name
                          ? companyById.businessType.name
                          : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-tax-id.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.taxId ? companyById.taxId : '-'}
                      </div>
                    </div>
                    <div className="left-group">เลขประจำตัวผู้เสียภาษีอากร</div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-calendar.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        อัปเดต{' '}
                        {companyById.modifiedDate
                          ? dateThaiFormat(companyById.modifiedDate)
                          : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="topic mt-[60px]">การติดต่อ</div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-address.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.address ? companyById.address : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-phone.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.phoneNumber
                          ? companyById.phoneNumber
                          : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-email.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.email ? companyById.email : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="list mb-[40px]">
                    <div className="right-group">
                      <div className="icon">
                        <Image
                          src="/icons/company-info/icon-website.svg"
                          alt=""
                          fill
                          quality={100}
                        />
                      </div>
                      <div className="name">
                        {companyById.website ? companyById.website : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="topic mt-[60px]">ข้อมูลเวลาทำการ</div>
                  {!isEmpty(dataWorkingDay) &&
                    dataWorkingDay?.map((data: any) => {
                      return data.workingDay?.map(
                        (workingDay: any, index: number) => {
                          const dayName = [
                            'จันทร์',
                            'อังคาร',
                            'พุธ',
                            'พฤหัสบดี',
                            'ศุกร์',
                            'เสาร์',
                            'อาทิตย์',
                          ];
                          const valueToday = dayjs(new Date()).format(
                            'YYYY-MM-DD'
                          );
                          return (
                            <div
                              className={`list ${
                                index === 6 ? 'mb-[40px]' : ''
                              }`}
                              key={index}
                            >
                              <div className="right-group time">
                                <div className="day">{dayName[index]}</div>
                                <div className="working">
                                  {!workingDay.active && 'ปิดทำการ'}
                                  {workingDay.active &&
                                    `${dayjs(
                                      `${valueToday}T${data.startTime}`
                                    ).format('HH:mm')}-${dayjs(
                                      `${valueToday}T${data.endTime}`
                                    ).format('HH:mm')}`}{' '}
                                </div>
                              </div>
                            </div>
                          );
                        }
                      );
                    })}
                </div>
              </section>
            </div>
          </CompanyInfoWrap>
        )}
      {!isAllowed(permissions, 'company.info.list') && <NotPermission />}
    </>
  );
};

export default CompanyInfoFormPreview;
