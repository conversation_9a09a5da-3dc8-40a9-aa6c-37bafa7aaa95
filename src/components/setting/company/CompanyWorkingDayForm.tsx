import React, { useEffect } from 'react';
import FieldTimePicker from '@/components/company/FieldTimePicker';
import FieldCheckBoxWorkDate from '@/components/company/FieldCheckBoxWorkDate';
import { Button } from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import apiCompanyWorking from '@/services/core/company-working';
import dayjs from 'dayjs';
import { setSnackBar } from '@/store/features/alert';
import { useAppDispatch } from '@/store';
import { useRouter } from 'next/router';

const validationSchema = yup.object({
  startTime: yup.string().required('กรุณาเลือกเวลาทำการ'),
  endTime: yup.string().required('กรุณาเลือกเวลาทำการ'),
  breakStart: yup.string().required('กรุณาเลือกเวลาพักเที่ยง'),
  breakEnd: yup.string().required('กรุณาเลือกเวลาพักเที่ยง'),
});
type Props = {
  companyById: any;
};
const CompanyWorkingDayForm = ({ companyById }: Props) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const formik = useFormik({
    initialValues: {
      id: '',
      companyId: '',
      startTime: '',
      endTime: '',
      breakStart: '',
      breakEnd: '',
      isActive: true,
      workingDay: [],
    },
    validationSchema,
    onSubmit: (values: any) => {
      const sendValue = {
        ...values,
        startTime: dayjs(values.startTime).format('HH:mm:ss'),
        endTime: dayjs(values.endTime).format('HH:mm:ss'),
        breakStart: dayjs(values.breakStart).format('HH:mm:ss'),
        breakEnd: dayjs(values.breakEnd).format('HH:mm:ss'),
        companyId: companyById.id,
      };
      updateCompanyWorking(sendValue);
    },
  });
  const getCompanyWorkingByCompany = async (params: any) => {
    const res = await apiCompanyWorking.getCompanyWorkingByCompany(params);
    if (!res.isError) {
      const valueToday = dayjs(new Date()).format('YYYY-MM-DD');
      formik.setFieldValue('id', res.data[0].id);
      formik.setFieldValue(
        'startTime',
        dayjs(`${valueToday}T${res.data[0].startTime}`)
      );
      formik.setFieldValue(
        'endTime',
        dayjs(`${valueToday}T${res.data[0].endTime}`)
      );
      formik.setFieldValue(
        'breakStart',
        dayjs(`${valueToday}T${res.data[0].breakStart}`)
      );
      formik.setFieldValue(
        'breakEnd',
        dayjs(`${valueToday}T${res.data[0].breakEnd}`)
      );
      formik.setFieldValue(`workingDay`, res.data[0].workingDay);
    }
  };
  const updateCompanyWorking = async (data: any) => {
    const res = await apiCompanyWorking.updateCompanyWorking(data);
    if (!res.isError) {
      dispatch(
        setSnackBar({
          status: true,
          text: 'แก้ไขข้อมูลเวลาทำการเรียบร้อย',
          severity: 'success',
        })
      );
      router.push('/company/info');
    }
  };
  useEffect(() => {
    if (companyById?.id)
      getCompanyWorkingByCompany({ companyId: companyById.id, isActive: true });
  }, [companyById]);
  return (
    <div>
      <div className="topic !mt-0">ข้อมูลเวลาทำการ</div>
      <form onSubmit={formik.handleSubmit}>
        <FieldTimePicker formik={formik} />
        <FieldCheckBoxWorkDate formik={formik} />
        <div className={'mt-6 flex items-center justify-center gap-2'}>
          <Button
            variant={'outlined'}
            className={'w-1/2'}
            onClick={() => formik.resetForm()}
          >
            ยกเลิก
          </Button>
          <Button variant={'contained'} className={'w-1/2'} type={'submit'}>
            บันทึก
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CompanyWorkingDayForm;
