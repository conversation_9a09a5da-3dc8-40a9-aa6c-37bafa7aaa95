import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import styled, { css } from 'styled-components';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';
import { numberWithCommas } from '@/utils/number';

const QuotationPapersStyled = styled.section<{
  $isRenderSummarizePage: boolean | undefined;
  $headerPapersHeight: number;
}>`
  max-width: 100%;
  overflow: auto;
  border: 1px #d3d3d3 solid;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  .page {
    width: 21cm;
    min-width: 21cm;
    max-width: 21cm;
    min-height: 29.7cm;
    max-height: 29.7cm;
    padding: 2cm;
    background: white;
    font-size: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    row-gap: 30px;
    ${({ $isRenderSummarizePage }) =>
      $isRenderSummarizePage &&
      css`
        justify-content: start;
      `};
    @media screen and (max-width: 1100px) {
      padding: 1.4cm;
    }
    .header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      column-gap: 40px;
      .lside {
        flex: 1.25 1 0%;
        display: flex;
        flex-direction: column;
        row-gap: 32px;
        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 6px;
          .customer {
            line-height: 0;
          }
          .title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            column-gap: 16px;
            @media screen and (max-width: 424px) {
              font-size: 20px;
            }
          }
          .contact {
            white-space: nowrap;
            span {
              color: #dbe2e5;
            }
          }
        }
      }
      .rside {
        flex: 1 1 0%;
        display: flex;
        row-gap: 20px;
        flex-direction: column;
        justify-content: space-between;
        .topic {
          font-size: 24px;
          font-weight: 600;
          height: 44.8px;
          display: flex;
          align-items: center;
          @media screen and (max-width: 424px) {
            font-size: 20px;
          }
        }
        .qt-info {
          display: flex;
          flex-direction: column;
          .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 12px;
            .no {
              line-height: 1;
            }
            .qt-code {
              font-weight: 600;
              font-size: 20px;
              @media screen and (max-width: 424px) {
                font-size: 18px;
              }
            }
          }
          .list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 34px;
            border-top: 1px solid #dbe2e5;
            &:last-child {
              border-bottom: 1px solid #dbe2e5;
            }
            .value {
              font-weight: 600;
            }
          }
        }
      }
    }

    .summarize-wrap {
      display: flex;
      flex-direction: column;
      row-gap: 88px;
      ${({ $isRenderSummarizePage, $headerPapersHeight }) =>
        $isRenderSummarizePage &&
        css`
          height: calc(100vh - ${$headerPapersHeight + 212}px);
          justify-content: space-between;
        `};

      .remark-wrap {
        display: flex;
        width: 100%;
        padding: 18px 0 0;
        border-top: 2px solid #263238;
        column-gap: 40px;
        .description {
          flex: 1.25 1 0%;
          .text {
            display: block;
          }
        }
        .calculate {
          flex: 1 1 0%;
          display: flex;
          flex-direction: column;
          row-gap: 16px;
          .calc-list {
            display: flex;
            justify-content: space-between;
            width: 100%;
            align-items: center;
            &:first-child {
              border-bottom: 1px solid #dbe2e5;
              height: 34px;
              align-items: start;
            }
            .key {
              display: block;
            }
            .value {
              font-weight: 600;
            }
          }
        }
      }
      .summation-bg {
        background: #f5f7f8;
        margin-top: 24px;
        width: 100%;
        .summation {
          width: 100%;
          display: flex;
          align-items: center;
          column-gap: 40px;
          .thai {
            flex: 1.25 1 0%;
            min-height: 48px;
            display: flex;
            align-items: center;
            padding: 0 0 0 16px;
          }
          .sum {
            flex: 1 1 0%;
            min-height: 48px;
            display: flex;
            align-items: center;
            color: white;
            background: #263238;
            justify-content: space-between;
            padding-left: 16px;
            .key {
              display: block;
            }
            .value {
              font-weight: 600;
              font-size: 20px;
              padding-right: 16px;
            }
          }
        }
      }
      .signature {
        width: 100%;
        display: flex;
        column-gap: 100px;
        align-items: end;
        .approver {
          flex: 1 1 0%;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          .approver-signature {
            width: 100%;
            border-top: 1px solid;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .proponent {
          flex: 1 1 0%;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          .proponent-signature {
            width: 100%;
            border-top: 1px solid;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  @page {
    size: A4;
    margin: 0;
  }
  @media print {
    overflow: hidden;
    display: flex;
    justify-content: center;
    border: none;
    box-shadow: none;
    .page {
      margin: 0;
      border: initial;
      border-radius: initial;
      width: initial;
      min-height: initial;
      box-shadow: initial;
      background: initial;
      page-break-after: always;

      .remark-wrap {
        page-break-inside: avoid;
      }
      .summation-bg {
        page-break-inside: avoid;
      }
      .signature {
        page-break-inside: avoid;
      }
    }
  }
`;
const TableQuotationPapersStyled = styled.div`
  width: 100%;
  overflow: auto;
  margin-top: 32px;
  * {
    font-size: 10px !important;
  }
  .MuiTable-root {
    border-collapse: initial;
    border-spacing: initial;
    overflow: hidden;
    .MuiTableHead-root {
      .MuiTableRow-head {
        .MuiTableCell-head {
          font-size: 16px;
          font-weight: 400;
          white-space: nowrap;
        }
      }
    }
    .MuiTableCell-head {
      border-bottom: 2px solid #263238;
      border-top: 2px solid #263238;
      padding: 8px 16px;
    }
    .MuiTableCell-root {
      color: #263238;
      background: white;
      font-size: 16px;
      vertical-align: top;
      position: relative;
      &:first-child {
        text-align: center;
      }
      &:last-child {
        text-align: end;
      }

      .ld-code {
        white-space: nowrap;
      }
      .product-spec-wrap {
        display: flex;
        flex-direction: column;
        width: 204px;
        .product-name {
          font-weight: 600;
        }
        .product-spec {
          font-size: 12px;
        }
      }
    }
    .MuiTableBody-root {
      position: relative;
      .blank {
        height: 168px;
        td {
          position: initial !important;
        }
      }
      .blank-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #cfd8dc;
      }
    }
  }
`;
type QuotationPapersProps = {
  data?: any;
  startIndex: number;
  makeNewData?: (rowThisSheet: any, remainingData: any) => void;
  makeRenderSummarizePage?: (isRender: boolean) => void;
  isRenderSummarizePage?: boolean;
};
const MAX_HEIGHT_TABLE = 838;
const QuotationPapers = (props: QuotationPapersProps) => {
  const {
    data,
    startIndex,
    makeNewData,
    makeRenderSummarizePage,
    isRenderSummarizePage,
  } = props;
  const [renderSummarizeComponent, setRenderSummarizeComponent] =
    useState<boolean>(true);
  const tableRef = useRef<any>(null);
  const headerPapersRef = useRef<any>(null);
  const [headerPapersHeight, setHeaderPapersHeight] = useState<number>(0);

  useEffect(() => {
    if (data && headerPapersRef.current) {
      // const screenWidth = window.innerWidth;
      const headerHeight =
        headerPapersRef.current.getBoundingClientRect().height;
      setHeaderPapersHeight(headerHeight);
    }
    if (isRenderSummarizePage && headerPapersRef.current) {
      const headerHeight =
        headerPapersRef.current.getBoundingClientRect().height;
      setHeaderPapersHeight(headerHeight);
    }
  }, [data, isRenderSummarizePage]);

  useEffect(() => {
    let totalHeight = 0;
    let rows = 0;

    if (tableRef.current && headerPapersHeight) {
      const rowsArray = tableRef.current.getElementsByTagName('tr');
      for (const row of rowsArray) {
        const rect = row.getBoundingClientRect();
        totalHeight += rect.height;
        if (totalHeight + headerPapersHeight > 424) {
          setRenderSummarizeComponent(false);
          if (makeRenderSummarizePage) {
            makeRenderSummarizePage(true);
          }
        }
        if (totalHeight > MAX_HEIGHT_TABLE - headerPapersHeight) {
          if (makeRenderSummarizePage) {
            makeRenderSummarizePage(false);
          }
          handleRowsPerSheet(rows > 0 ? rows : data.length);
          break;
        } else {
          rows++;
        }
      }
    }
  }, [headerPapersHeight]);

  const handleRowsPerSheet = (cutRowsIndex: number) => {
    if (data) {
      const rowThisSheet = data.slice(0, cutRowsIndex);
      const remainingData = data.slice(cutRowsIndex);
      if (makeNewData) {
        makeNewData(rowThisSheet, remainingData);
      }
    }
  };
  return (
    <QuotationPapersStyled
      $isRenderSummarizePage={isRenderSummarizePage}
      $headerPapersHeight={headerPapersHeight}
    >
      <div className="page">
        <div>
          <div ref={headerPapersRef} className="header">
            <div className="lside">
              <div className="text-group">
                <div className="title">
                  <Image
                    src="/images/digiboxs.svg"
                    width={38.5}
                    height={44.8}
                    alt=""
                  />
                  Digiboxs.Co.,Ltd.
                </div>
                <div>
                  เลขที่ 6 ซอยบางแค 12 แขวงบางแค เขตบางแค กรุงเทพฯ 10160
                </div>
                <div className="contact">
                  โทร. 020849982 <span>|</span> โทร. 020849983 <span>| </span>
                  <EMAIL>
                </div>
                <div>เลขประจำตัวผู้เสียภาษี 0105553024586</div>
              </div>
              <div className="text-group">
                <div className="customer">ลูกค้า</div>
                <div className="title">บริษัท อิมแพลนเทียม จำกัด</div>
                <div>
                  3886/2 ชั้นที่ 1 และชั้นที่ 3 ถ.พระรามที่ 4 แขวงพระโขนง
                  เขตคลองเตย กรุงเทพมหานคร 10110
                </div>
                <div className="contact">
                  นิติบุคคล <span>|</span> โทร. 0848379487 <span>| </span>
                  <EMAIL>
                </div>
                <div>เลขประจำตัวผู้เสียภาษี 0105557169325</div>
              </div>
            </div>
            <div className="rside">
              <div className="topic">ใบเสนอราคา</div>
              <div className="qt-info">
                <div className="header">
                  <div className="no">เลขที่</div>
                  <div className="qt-code">QT2023110001</div>
                </div>
                <div className="list">
                  <div className="key">วันที่สร้างเอกสาร</div>
                  <div className="value">29/12/2023</div>
                </div>
                <div className="list">
                  <div className="key">กำหนดยืนยันราคา</div>
                  <div className="value">29/01/2024</div>
                </div>
                <div className="list">
                  <div className="key">กำหนดการชำระเงิน</div>
                  <div className="value">29/02/2024</div>
                </div>
                <div className="list">
                  <div className="key">พนักงานขาย</div>
                  <div className="value">ธันยารัต สมมณี, 0861234567</div>
                </div>
              </div>
            </div>
          </div>
          {!isRenderSummarizePage && (
            <TableQuotationPapersStyled>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>#</TableCell>
                    <TableCell>เลขที่</TableCell>
                    <TableCell>ราการสินค้า</TableCell>
                    <TableCell>จำนวน</TableCell>
                    <TableCell>ราคาต่อหน่วย</TableCell>
                    <TableCell>ยอดรวม</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody ref={tableRef}>
                  <>
                    {data &&
                      data.map((item: any, index: number) => {
                        const orderNumber = startIndex + index + 1;
                        return (
                          <TableRow key={index}>
                            <TableCell>{orderNumber}</TableCell>
                            <TableCell>
                              <div className="ld-code">{item.ldCode}</div>
                            </TableCell>
                            <TableCell>
                              <div className="product-spec-wrap">
                                <div className="product-name">{item.name}</div>
                                <div className="product-spec">
                                  {item.detail}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>{numberWithCommas(item.quality, 2)}</div>
                            </TableCell>
                            <TableCell>
                              <div>
                                {numberWithCommas(item.pricePerUnit, 2)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>{numberWithCommas(item.total, 2)}</div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </>
                </TableBody>
              </Table>
            </TableQuotationPapersStyled>
          )}
        </div>
        {renderSummarizeComponent && (
          <div className="summarize-wrap">
            <div>
              <div className="remark-wrap">
                <div className="description">
                  <div className="text">
                    หมายเหตุ: ราคานี้ยังไม่รวมภาษีมูลค่าเพิ่ม 7%
                    เงื่อนไขการเริ่มงาน / การชำระเงิน มัดจำค่าสินค้า 80 %
                    ก่อนเริ่มงาน ส่วนที่เหลือชำระก่อนจัดส่ง ระยะเวลาผลิต
                    หลังมัดจำ และ ยืนยันม็อกอัฟเข้าผลิตประมาณ 12-15 วันทำการ
                    กรณียอดไม่ถึง 10000 บาท ชำระเต็ม100 % ก่อนเริ่มงาน
                    (ระยะเวลาขึ้นอยู่กับ จำนวนและสเปคงานค่ะ) - ยอดเกิน 15,000
                    บาท การจัดส่ง ฟรี กทม.// ตจว. ขนส่งเก็บปลายทาง (หมายเหตุ :
                    ทางบริษัทฯ ขอสงวนสิทธิ์ในการขอคืนเงินมัดจำทุกกรณี)
                  </div>
                </div>
                <div className="calculate">
                  <div className="calc-list">
                    <div className="key">ยอดรวม</div>
                    <div className="value">37,500.00 บาท</div>
                  </div>
                  <div className="calc-list">
                    <div className="key">ส่วนลด 10%</div>
                    <div className="value">175.00 บาท</div>
                  </div>
                  <div className="calc-list">
                    <div className="key">ภาษีมูลค่าเพิ่ม (VAT)</div>
                    <div className="value">2,625.00 บาท</div>
                  </div>
                  <div className="calc-list">
                    <div className="key">หัก ณ ที่จ่าย 3%</div>
                    <div className="value">175.00 บาท</div>
                  </div>
                  <div className="calc-list">
                    <div className="key">ค่าจัดส่ง</div>
                    <div className="value">0.00 บาท</div>
                  </div>
                </div>
              </div>
              <div className="summation-bg">
                <div className="summation">
                  <div className="thai">สี่หมื่นหนึ่งร้อยยี่สิบห้าบาทถ้วน</div>
                  <div className="sum">
                    <div className="key">ยอดชำระเงิน</div>
                    <div className="value">40,125.00 บาท</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="signature">
              <div className="approver">
                <Image
                  src={'/images/signature.png'}
                  width={160}
                  height={52}
                  alt=""
                />
                <div className="approver-signature">ผู้อนุมัติ</div>
              </div>
              <div className="proponent">
                <Image
                  src={'/images/signature.png'}
                  width={160}
                  height={52}
                  alt=""
                />
                <div className="proponent-signature">ผู้เสเนอราคา</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </QuotationPapersStyled>
  );
};

export default QuotationPapers;
