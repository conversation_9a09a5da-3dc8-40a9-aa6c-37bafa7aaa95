import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';
import styled from 'styled-components';
import { useRouter } from 'next/router';
import Image from 'next/image';

const TableCreateQuotationStyled = styled.div`
  width: 100%;
  overflow: auto;
  .MuiTable-root {
    border-collapse: initial;
    border-spacing: initial;
    overflow: hidden;
    .MuiTableHead-root {
      .MuiTableRow-head {
        .MuiTableCell-head {
          font-size: 16px;
          font-weight: 400;
          white-space: nowrap;
        }
      }
    }
    .MuiTableCell-root {
      color: #263238;
      background: white;
      font-size: 16px;
      vertical-align: top;
      position: relative;
      &:first-child {
        text-align: center;
      }
      &:last-child {
        text-align: end;
      }

      .ld-code {
        white-space: nowrap;
      }
      .product-spec-wrap {
        display: flex;
        flex-direction: column;
        width: 424px;
        .product-name {
          font-weight: 600;
        }
        .product-spec {
          font-size: 12px;
        }
      }
      .summation {
        display: flex;
        flex-direction: column;
        align-items: end;
        .remove-btn {
          height: 40px;
          width: 40px;
          border-radius: 6px;
          cursor: pointer;
          transition: 0.3s ease-out;
          background: #d32f2f;
          position: absolute;
          bottom: 16px;
          img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
          }
          &:hover {
            filter: brightness(0.9);
          }
        }
      }
    }
    .MuiTableBody-root {
      position: relative;
      .table-space {
        height: 4px;
        padding: 0;
        .MuiTableCell-root {
          padding: 0;
          background: #f5f7f8;
        }
      }
      .MuiTableRow-root {
        &:last-child {
          .MuiTableCell-root {
            border-bottom: 0;
          }
        }
      }
      .blank {
        height: 168px;
        td {
          position: initial !important;
        }
      }
      .blank-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #cfd8dc;
      }
    }
  }
`;
const TableCreateQuotationWrapStyled = styled.div`
  border-radius: 16px;
  border: 1px solid #dbe2e5;
  overflow: hidden;
  margin-top: 40px;
  width: 100%;
  @media screen and (max-width: 650px) {
    margin-top: 24px;
  }
`;

const TableCreateQuotation = () => {
  const router = useRouter();
  return (
    <TableCreateQuotationWrapStyled>
      <TableCreateQuotationStyled>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>#</TableCell>
              <TableCell>เลขที่</TableCell>
              <TableCell>ราการสินค้า</TableCell>
              <TableCell>จำนวน</TableCell>
              <TableCell>ราคาต่อหน่วย</TableCell>
              <TableCell>ยอดรวม</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow className="table-space">
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
              <TableCell />
            </TableRow>
            {router.pathname === '/corporate/quotation/create' ? (
              <TableRow className="blank">
                <TableCell>
                  <div className="blank-text">ไม่มีรายการสินค้า</div>
                </TableCell>
              </TableRow>
            ) : (
              <>
                <TableRow>
                  <TableCell>1</TableCell>
                  <TableCell>
                    <div className="ld-code">LD-2023001</div>
                  </TableCell>
                  <TableCell>
                    <div className="product-spec-wrap">
                      <div className="product-name">Mailer Box Standard</div>
                      <div className="product-spec">
                        ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม •
                        ด้านหน้า CMYK, ด้านหลัง CMYK และ PANTONE • เคลือบ Vanish
                        Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm
                        • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm
                        • ออกแบบอาร์ตเวิร์กใหม่
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="w-[100px]">500</div>
                  </TableCell>
                  <TableCell>
                    <div className="w-[100px]">39.00</div>
                  </TableCell>
                  <TableCell>
                    <div className="summation">
                      <div>19,500.00</div>
                      <div className="remove-btn">
                        <Image
                          src="/icons/delete-white.svg"
                          width={24}
                          height={24}
                          alt=""
                        />
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>2</TableCell>
                  <TableCell>
                    <div className="ld-code">LD-2023001</div>
                  </TableCell>
                  <TableCell>
                    <div className="product-spec-wrap">
                      <div className="product-name">Mailer Box Standard</div>
                      <div className="product-spec">
                        ขนาด 30.5x20x3 ซ.ม • กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม •
                        ด้านหน้า CMYK, ด้านหลัง CMYK และ PANTONE • เคลือบ Vanish
                        Glossy (Matt) • Embossing 3x2.5 cm • Embossing 1x4.3 cm
                        • Spot UV(เหมาเต็มผ่น) • Foil Stamping Rose Gold 2x2 cm
                        • ออกแบบอาร์ตเวิร์กใหม่
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="w-[100px]">500</div>
                  </TableCell>
                  <TableCell>
                    <div className="w-[100px]">39.00</div>
                  </TableCell>
                  <TableCell>
                    <div className="summation">
                      <div>19,500.00</div>
                      <div className="remove-btn">
                        <Image
                          src="/icons/delete-white.svg"
                          width={24}
                          height={24}
                          alt=""
                        />
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </>
            )}
          </TableBody>
        </Table>
      </TableCreateQuotationStyled>
    </TableCreateQuotationWrapStyled>
  );
};

export default TableCreateQuotation;
