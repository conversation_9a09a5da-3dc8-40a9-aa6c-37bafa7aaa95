import React from 'react';
import Image from 'next/image';
import ContactDialog from '@/components/layout-data/ContactDialog';
import TableCreateQuotation from '@/components/corporate/quotation/TableCreateQuotation';
import SummarizeCreateQuotation from '@/components/corporate/quotation/SummarizeCreateQuotation';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { useRouter } from 'next/router';
import { isEmpty } from 'lodash';
import CancelIcon from '@mui/icons-material/Cancel';
import { IconButton } from '@mui/material';

const QuotationFormStyled = styled.div`
  animation: ${LoadingFadein} 0.3s ease-in;
  display: flex;
  flex-direction: column;
  column-gap: 24px;
  width: 1920px;
  padding: 40px 80px;
  max-width: 100%;
  @media screen and (max-width: 1200px) {
    padding: 40px;
  }
  .sheet-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 40px;
    row-gap: 24px;
    width: 100%;
    flex-wrap: wrap;
    .rside {
      display: flex;
      align-items: center;
      column-gap: 8px;
      .company-group {
        display: flex;
        align-items: center;
        column-gap: 16px;
        font-size: 34px;
        font-weight: 600;
        flex-wrap: wrap;
        @media screen and (max-width: 1100px) {
          font-size: 30px;
        }
        @media screen and (max-width: 650px) {
          font-size: 24px;
        }
      }
    }
    .lside {
      .qt-code {
        font-size: 34px;
        font-weight: 600;
        line-height: 1;
        position: relative;
        @media screen and (max-width: 1100px) {
          font-size: 30px;
        }
        @media screen and (max-width: 650px) {
          font-size: 24px;
        }
        .no {
          font-size: 12px;
          position: absolute;
          top: -16px;
          font-weight: 400;
        }
      }
    }
  }
  .personal-wrap {
    width: 100%;
    display: flex;
    margin-top: 40px;
    column-gap: 40px;
    @media screen and (max-width: 1480px) {
      flex-direction: column;
      row-gap: 40px;
    }
    @media screen and (max-width: 650px) {
      row-gap: 24px;
    }
    .lside {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      width: 50%;
      @media screen and (max-width: 1480px) {
        width: auto;
      }
      .customer-bar {
        cursor: pointer;
        width: 100%;
        height: 48px;
        border-radius: 8px;
        border: 1px solid #dbe2e5;
        display: flex;
        column-gap: 16px;
        align-items: center;
        padding: 0 14px;
        transition: 0.3s ease-out;
        background: white;
        position: relative;
        &:hover {
          filter: brightness(0.9);
        }
        img {
          border-radius: 50%;
        }
        .close {
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 6px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .address-wrap {
        width: 100%;
        height: 192px;
        background-color: #f5f7f8;
        border-radius: 16px;
        position: relative;
        padding: 16px 16px 16px 24px;
      }
      .address {
        width: 100%;
        height: 100%;
        overflow: auto;
        .name {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        .address-text {
          display: flex;
          flex-direction: column;
          row-gap: 8px;
          .top {
          }
          .center {
            span {
              color: #dbe2e5;
            }
          }
          .bottom {
          }
        }
        .blank-text {
          color: #cfd8dc;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translateY(-50%) translateX(-50%);
          text-align: center;
          @media screen and (max-width: 360px) {
            max-width: 200px;
          }
        }
      }
    }
    .rside {
      flex: 1 1 0%;
      width: 50%;
      @media screen and (max-width: 1480px) {
        width: auto;
      }
      .card-date {
        width: 100%;
        height: 256px;
        display: flex;
        flex-direction: column;
        border: 1px solid #dbe2e5;
        border-radius: 16px;
        @media screen and (max-width: 1480px) {
          height: 128px;
        }
        .list {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #dbe2e5;
          padding: 0 16px;
          column-gap: 24px;
          width: 100%;
          overflow: hidden;
          @media screen and (max-width: 424px) {
            //flex-wrap: wrap;
          }
          &:last-child {
            border-bottom: none;
          }
          &.empty {
            @media screen and (max-width: 1480px) {
              display: none;
            }
          }
          .date-picker-wrap {
            height: 32px;
            display: flex;
            cursor: pointer;
            .date {
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 8px 0 0 8px;
              padding: 0 12px 0 0;
              white-space: nowrap;
              @media screen and (max-width: 424px) {
                padding: 0;
              }
              @media screen and (max-width: 360px) {
                display: none;
              }
            }
            .icon {
              height: 100%;
              width: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              border-radius: 50%;
              transition: 0.3s ease-out;
              @media screen and (max-width: 424px) {
                display: none;
              }
              @media screen and (max-width: 360px) {
                display: flex;
              }
              &:hover {
                background: rgba(48, 213, 199, 0.2);
              }
            }
          }
          .key {
            white-space: nowrap;
          }
          .value {
            font-weight: 600;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
`;
type QuotationFormProps = {
  findCustomer: (id: number) => void;
  dataAddressForRender: any;
  dataCustomerRender: any;
  removeContactAddress: () => void;
};
const QuotationForm = (props: QuotationFormProps) => {
  const {
    findCustomer,
    dataAddressForRender,
    dataCustomerRender,
    removeContactAddress,
  } = props;
  const router = useRouter();
  const handleRemoveContact = (event: any) => {
    event.stopPropagation();
    removeContactAddress();
  };
  return (
    <QuotationFormStyled>
      <div className="sheet-header">
        <div className="rside">
          <div className="company-group">
            <Image src="/images/digiboxs.svg" width={48} height={64} alt="" />
            Digiboxs.Co.,Ltd.
          </div>
        </div>
        {router.pathname !== '/corporate/quotation/create' && (
          <div className="lside">
            <div className="qt-code">
              QT2023110001 <div className="no">เลขที่</div>
            </div>
          </div>
        )}
      </div>
      <div className="personal-wrap">
        <div className="lside">
          <ContactDialog
            handleChooseContact={(contactId) => {
              findCustomer(contactId);
            }}
          >
            <div className="customer-bar">
              <Image
                src={
                  dataCustomerRender.imageUrl ||
                  '/icons/aside/user-menu/icon-user-data.svg'
                }
                width={24}
                height={24}
                alt=""
              />
              {dataCustomerRender.name || 'ค้นหาลูกค้า'}
              {!isEmpty(dataCustomerRender.name) && (
                <div
                  className="close"
                  onClick={(event: any) => {
                    handleRemoveContact(event);
                  }}
                >
                  <IconButton>
                    <CancelIcon />
                  </IconButton>
                </div>
              )}
            </div>
          </ContactDialog>
          <div className="address-wrap">
            <div className="address">
              {isEmpty(dataAddressForRender) ? (
                <div className="blank-text">ข้อมูลลูกค้าสำหรับใบเสนอราคา</div>
              ) : (
                <>
                  <div className="name">{dataAddressForRender.name}</div>
                  <div className="address-text">
                    <div className="top">
                      {dataAddressForRender.address}{' '}
                      {dataAddressForRender.district}{' '}
                      {dataAddressForRender.subDistrict}{' '}
                      {dataAddressForRender.province}{' '}
                      {dataAddressForRender.postalCode}
                    </div>
                    <div className="center">
                      โทร. {dataAddressForRender.phoneNumber} <span>| </span>
                      {dataCustomerRender.email}
                    </div>
                    <div className="bottom">
                      เลขประจำตัวผู้เสียภาษี 0105553024586
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="rside">
          <div className="card-date">
            <div className="list">
              <div className="key">วันที่สร้าง</div>
              <div className="date-picker-wrap">
                <div className="date">29/12/2023, 15:49 น.</div>
                <div className="icon">
                  <Image
                    src="/icons/icon-calendar.svg"
                    width={24}
                    height={24}
                    alt=""
                  />
                </div>
              </div>
            </div>
            <div className="list empty" />
            <div className="list empty" />
            <div className="list">
              <div className="key">พนักงานขาย</div>
              <div className="value">ธันยารัต สมมณี, 0861234567</div>
            </div>
          </div>
        </div>
      </div>
      <TableCreateQuotation />
      <SummarizeCreateQuotation />
    </QuotationFormStyled>
  );
};

export default QuotationForm;
