import React, { useState } from 'react';
import styled from 'styled-components';
import ActionButton from '@/components/ActionButton';
import { AddCircle } from '@mui/icons-material';
import {
  Button,
  Checkbox,
  Dialog,
  DialogContent,
  FormControlLabel,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material';
import IconUnCheckbox from '@/components/IconUnCheckbox';
import IconCheckbox from '@/components/IconCheckbox';
import { FormModalStyle } from '@/styles/share.styled';
import { CloseIcon } from 'next/dist/client/components/react-dev-overlay/internal/icons/CloseIcon';
import { isEmpty } from 'lodash';
import Image from 'next/image';

const SummarizeCreateQuotationStyled = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 40px;
  margin-top: 40px;
  width: 100%;
  @media screen and (max-width: 650px) {
    margin-top: 24px;
  }
  .event-section {
    display: flex;
    column-gap: 40px;
    width: 100%;
    @media screen and (max-width: 1200px) {
      flex-direction: column;
      row-gap: 40px;
    }
    @media screen and (max-width: 650px) {
      row-gap: 24px;
    }
    .lside {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      @media screen and (max-width: 1200px) {
        row-gap: 40px;
      }
      @media screen and (max-width: 650px) {
        row-gap: 24px;
      }
      .remark {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        width: 100%;
      }
    }
    .rside {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 72px;
        border-bottom: 1px solid;
        column-gap: 24px;
        flex-wrap: wrap;
        &:first-child {
          height: 48px;
          align-items: start;
        }
        .key-group {
          display: flex;
          align-items: center;
          .MuiInputBase-input {
            text-align: center;
          }
        }
        .value {
          font-weight: 600;
        }
      }
    }
  }
  .summation-section {
    width: 100%;
    display: flex;
    @media screen and (max-width: 1200px) {
      flex-direction: column;
    }
    .bar {
      min-height: 76px;
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f5f7f8;
      width: 100%;
      justify-content: space-between;
      column-gap: 24px;
      flex-wrap: wrap;
      &.r {
        background: #dbe2e5;
      }
      &.l {
        color: #90a4ae;
      }
    }
    .lside {
      flex: 1 1 0%;
    }
    .rside {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
      .sum {
        font-size: 24px;
        font-weight: 600;
      }
      .signature {
        padding: 0 0 0 20px;
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        row-gap: 24px;
        @media screen and (max-width: 1200px) {
          padding: 0;
          label {
            padding: 0 0 0 20px;
          }
        }
      }
    }
  }
`;
const ModalOrderContentStyled = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  row-gap: 24px;
  ::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .order-list {
    width: 100%;
    box-shadow: #dbe2e5 0px 0px 0px 1px inset;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    padding-top: 16px;
    row-gap: 16px;
    align-items: center;
    overflow: hidden;
    .order-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 16px;
      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        column-gap: 24px;
        width: 100%;
        overflow: hidden;
        .image-wrap {
          border-radius: 8px;
          overflow: hidden;
          background: #c7e8f6;
          min-height: 80px;
          height: 80px;
          min-width: 80px;
          width: 80px;
          img {
            border-radius: 8px;
            width: 100%;
            height: 100%;
          }
        }
        .text-group {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100%;
          width: 100%;
          overflow: hidden;
          .ld-code {
            background: #263238;
            width: fit-content;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 16px;
            color: white;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            @media screen and (max-width: 380px) {
              font-size: 14px;
            }
          }
          .name {
            font-size: 22px;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            max-width: 100%;
            text-overflow: ellipsis;
          }
          .date {
            font-size: 12px;
          }
        }
      }
      .check-box {
        height: 100%;
        @media screen and (max-width: 420px) {
          display: none;
        }
        > span:first-child {
          margin: -9px -9px 0 0;
        }
      }
    }
    .order-content {
      width: calc(100% - 4px);
      overflow: auto;
      .MuiTable-root {
        .MuiTableCell-head {
          height: 32px;
          padding: 0 16px;
          border: 0;
          border-top: 1px solid #dbe2e5 !important;
          background: #f5f7f8;
          color: #90a4ae;
          font-size: 12px;
          .text {
            min-width: 164px;
          }
        }
        .MuiTableCell-root {
          border: none;
        }
      }
    }
    &:hover {
      box-shadow: #263238 0px 0px 0px 1px inset;
    }
    &.active {
      box-shadow: #30d5c7 0px 0px 0px 2px inset;
    }
  }
  .empty-order {
    height: 536px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .content {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      text-align: center;
      width: 300px;
      max-width: 100%;
      align-items: center;
      .title {
        font-weight: 600;
      }
      .description {
        font-size: 12px;
      }
    }
  }
`;

const SummarizeCreateQuotation = () => {
  const [openModalOrder, setOpenModalOrder] = useState<boolean>(false);
  const [orderList] = useState<any>({
    test: 'a',
  });
  const handleChangeRemark = (e: any) => {
    console.log(e.target.value);
  };
  const handleChangeDiscount = (e: any) => {
    console.log(e.target.value);
  };
  const handleChangeDiscountValue = (e: any) => {
    console.log(e.target.value);
  };
  const handleChangeSignature = (e: any) => {
    console.log(e);
  };
  const onCloseModalOrder = () => {
    setOpenModalOrder(false);
  };
  const onOpenModalOrder = async () => {
    await setOpenModalOrder(true);
  };

  return (
    <>
      <SummarizeCreateQuotationStyled>
        <div className="event-section">
          <div className="lside">
            <div
              onClick={() => {
                onOpenModalOrder();
              }}
            >
              <ActionButton
                variant="contained"
                color="dark"
                icon={<AddCircle />}
                text="เพิ่มออเดอร์สินค้า"
              />
            </div>
            <div className="remark">
              หมายเหตุ
              <TextField
                name="remark"
                onChange={handleChangeRemark}
                multiline
                rows={5}
                placeholder="ระบุข้อความ"
              />
            </div>
          </div>
          <div className="rside">
            <div className="list">
              <div className="key-group">รวมเป็นเงิน</div>
              <div className="value">0.00 บาท</div>
            </div>
            <div className="list">
              <div className="key-group">
                <FormControlLabel
                  control={
                    <Checkbox
                      color="primary"
                      checked={true}
                      onChange={(event: any) => {
                        handleChangeDiscount(event);
                      }}
                      icon={<IconUnCheckbox />}
                      checkedIcon={<IconCheckbox />}
                    />
                  }
                  label="ส่วนลด"
                />
                <TextField
                  type="number"
                  name="discount"
                  placeholder="0"
                  onChange={handleChangeDiscountValue}
                  InputProps={{
                    endAdornment: <div>%</div>,
                  }}
                  sx={{
                    width: '74px',
                  }}
                />
              </div>
              <div className="value">0.00 บาท</div>
            </div>
            <div className="list">
              <div className="key-group">
                <FormControlLabel
                  control={
                    <Checkbox
                      color="primary"
                      checked={true}
                      onChange={(event: any) => {
                        handleChangeDiscount(event);
                      }}
                      icon={<IconUnCheckbox />}
                      checkedIcon={<IconCheckbox />}
                    />
                  }
                  label="ภาษีมูลค่าเพิ่ม 7%"
                />
              </div>
              <div className="value">0.00 บาท</div>
            </div>
            <div className="list">
              <div className="key-group">
                <FormControlLabel
                  control={
                    <Checkbox
                      color="primary"
                      checked={true}
                      onChange={(event: any) => {
                        handleChangeDiscount(event);
                      }}
                      icon={<IconUnCheckbox />}
                      checkedIcon={<IconCheckbox />}
                    />
                  }
                  label="หัก ณ ที่จ่าย 3%"
                />
              </div>
              <div className="value">0.00 บาท</div>
            </div>
          </div>
        </div>
        <div className="summation-section">
          <div className="lside">
            <div className="bar l">
              <div>ศูนย์บาทถ้วน</div>
            </div>
          </div>
          <div className="rside">
            <div className="bar r">
              <div>ยอดชำระเงิน</div>
              <div className="sum">0.00 บาท</div>
            </div>
            <div className="signature">
              <FormControlLabel
                control={
                  <Checkbox
                    color="primary"
                    checked={true}
                    onChange={(event: any) => {
                      handleChangeSignature(event);
                    }}
                    icon={<IconUnCheckbox />}
                    checkedIcon={<IconCheckbox />}
                  />
                }
                label="ลายเซ็นอิเล็กทรอนิกส์และตรายาง"
              />
              <Button type="button" variant="contained" color="dark" fullWidth>
                บันทึกเอกสาร
              </Button>
            </div>
          </div>
        </div>
      </SummarizeCreateQuotationStyled>
      <Dialog open={openModalOrder}>
        <DialogContent>
          <FormModalStyle $width={592}>
            <div className="content-wrap">
              <div className="header">
                <div className="title">รายการออเดอร์สินค้า</div>
                <div
                  className="x-close"
                  onClick={() => {
                    onCloseModalOrder();
                  }}
                >
                  <IconButton>
                    <CloseIcon />
                  </IconButton>
                </div>
              </div>
              <div className="form-wrap">
                <ModalOrderContentStyled>
                  {!isEmpty(orderList) ? (
                    <>
                      <div className="order-list active">
                        <div className="order-header">
                          <div className="info">
                            <div className="image-wrap">
                              <Image
                                src={'/images/product/default-img.svg'}
                                width={160}
                                height={160}
                                alt=""
                              />
                            </div>
                            <div className="text-group">
                              <div className="ld-code">LD-2023001</div>
                              <div className="name">Mailer Box Standard</div>
                              <div className="date">20 ธ.ค. 2566, 15:34 น.</div>
                            </div>
                          </div>
                          <div className="check-box">
                            <Checkbox
                              color="primary"
                              checked={true}
                              onChange={(_event: any) => {
                                // handleChangeDiscount(event);
                              }}
                              icon={<IconUnCheckbox />}
                              checkedIcon={<IconCheckbox />}
                            />
                          </div>
                        </div>
                        <div className="order-content">
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>
                                  <div className="text">จำนวน</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text">ราคาต่อหน่วย</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text text-end">ยอดรวม</div>
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              <TableRow>
                                <TableCell>
                                  <div>500</div>
                                </TableCell>
                                <TableCell>
                                  <div>39.00</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text-end">19,500.00</div>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                      <div className="order-list">
                        <div className="order-header">
                          <div className="info">
                            <div className="image-wrap">
                              <Image
                                src={'/images/product/default-img.svg'}
                                width={160}
                                height={160}
                                alt=""
                              />
                            </div>
                            <div className="text-group">
                              <div className="ld-code">LD-2023001</div>
                              <div className="name">Mailer Box Standard</div>
                              <div className="date">20 ธ.ค. 2566, 15:34 น.</div>
                            </div>
                          </div>
                          <div className="check-box">
                            <Checkbox
                              color="primary"
                              checked={false}
                              onChange={(_event: any) => {
                                // handleChangeDiscount(event);
                              }}
                              icon={<IconUnCheckbox />}
                              checkedIcon={<IconCheckbox />}
                            />
                          </div>
                        </div>
                        <div className="order-content">
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>
                                  <div className="text">จำนวน</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text">ราคาต่อหน่วย</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text text-end">ยอดรวม</div>
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              <TableRow>
                                <TableCell>
                                  <div>500</div>
                                </TableCell>
                                <TableCell>
                                  <div>39.00</div>
                                </TableCell>
                                <TableCell>
                                  <div className="text-end">19,500.00</div>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="empty-order">
                      <div className="content">
                        <div className="title">ไม่มีการสินค้า</div>
                        <div className="description">
                          ไม่มีออเดอร์ของคุณ “นิมิตร หมายมั่นคง” กรุณาตรวจสอบ
                          ข้อมูลลูกค้า หรือ สร้างออเดอร์สินค้าค้าใหม่
                        </div>
                        <div className="w-[146px] mt-[8px]">
                          <ActionButton
                            variant="outlined"
                            color="blueGrey"
                            icon={<AddCircle />}
                            text="สร้างออเดอร์"
                            borderRadius="20px"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </ModalOrderContentStyled>
                {!isEmpty(orderList) && (
                  <Button
                    type="button"
                    variant="contained"
                    color="dark"
                    fullWidth
                  >
                    เพิ่มรายการ
                  </Button>
                )}
              </div>
            </div>
          </FormModalStyle>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SummarizeCreateQuotation;
