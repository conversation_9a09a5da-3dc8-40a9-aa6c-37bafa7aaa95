import { Add, ArrowLeft, ArrowRight, Remove } from '@mui/icons-material';
// import { PackageMockup } from '@lucablockltd/packaging-mockup-3d';
import SvgCursorIcon from '@/components/svg-icon/SvgCursorIcon';
import SvgDragIcon from '@/components/svg-icon/SvgDragIcon';
import SvgPlayIcon from '@/components/svg-icon/SvgPlayIcon';
import { Slider } from '@mui/material';
import React, { useState } from 'react';
import styled from 'styled-components';
import { useWindowSize } from 'usehooks-ts';

type propsType = {
  modelId: string;
  width: number;
  height: number;
  length: number;
  isModel: boolean;
  setIsModel: any;
};

const ModelSimulate = ({ isModel, setIsModel }: propsType) => {
  const size = useWindowSize();
  const [is3D, setIs3D] = useState<boolean>(true);
  const [isPlay, setIsPlay] = useState<boolean>(false);
  const [isDrag, setIsDrag] = useState<boolean>(false);
  const [actionState, setActionState] = useState<number>(100);
  const [zoom, setZoom] = useState<number>(100);

  return (
    <>
      <CollapseCursor isModel={isModel} onClick={() => setIsModel(!isModel)}>
        {isModel ? (
          <ArrowRight className={'w-[36px]'} />
        ) : (
          <ArrowLeft className={'w-[36px]'} />
        )}
      </CollapseCursor>
      <div className={'model3d'}>
        <ModelModeSwitch is3D={is3D}>
          <div className="switch-item left" onClick={() => setIs3D(false)}>
            2D Dieline
          </div>
          <div className="switch-item right" onClick={() => setIs3D(true)}>
            3D Preview
          </div>
        </ModelModeSwitch>
        {/* <PackageMockup */}
        {/*  modelId={modelId} */}
        {/*  width={width} */}
        {/*  height={height} */}
        {/*  length={length} */}
        {/*  // unit={'cm'} */}
        {/*  is3D={is3D} */}
        {/*  isFreeControl={!isDrag} */}
        {/*  actionState={actionState} */}
        {/*  mode={0} */}
        {/*  side={1} */}
        {/*  isHelper={false} */}
        {/*  isPlay={isPlay} */}
        {/*  isMapControl={isDrag} */}
        {/*  onCanvasSize={() => null} */}
        {/*  onCropPositions={() => null} */}
        {/*  textures={{ in: [], out: [] } || null} */}
        {/*  material={null} */}
        {/*  baseSize={ */}
        {/*    size.width > 1130 */}
        {/*      ? 792 */}
        {/*      : size.width > 820 */}
        {/*      ? size.width - 316 */}
        {/*      : size.width - 16 */}
        {/*  } */}
        {/*  zoom={zoom} */}
        {/*  isDimension={false} */}
        {/* /> */}
        {is3D && (
          <ModelController>
            <div className={'container'}>
              <div
                className={`control-item ${!isDrag ? 'active' : ''}`}
                onClick={() => setIsDrag(false)}
              >
                <SvgCursorIcon color={!isDrag ? '#000' : '#90A4AE'} />
              </div>
              <div
                className={`control-item ${isDrag ? 'active' : ''}`}
                onClick={() => setIsDrag(true)}
              >
                <SvgDragIcon color={isDrag ? '#000' : '#90A4AE'} />
              </div>
              <div
                className={`control-item ${isPlay ? 'active' : ''}`}
                onClick={() => setIsPlay(!isPlay)}
              >
                <SvgPlayIcon color={isPlay ? '#000' : '#90A4AE'} />
              </div>
              <div className="divider" />
              <div className={`control-item`}>
                <div className={'slide-action'}>
                  <div className={'text'}>Open</div>
                  <Slider
                    valueLabelDisplay="auto"
                    defaultValue={actionState}
                    value={actionState}
                    orientation={size.width > 510 ? 'horizontal' : 'vertical'}
                    onChange={(_event, value) => setActionState(Number(value))}
                    sx={{
                      color: '#30D5C7',
                      height: `${size.width > 510 ? 8 : 100}`,
                      '& .MuiSlider-track': {
                        border: 'none',
                      },
                      '& .MuiSlider-thumb': {
                        height: 18,
                        width: 18,
                        backgroundColor: '#fff',
                        border: '2px solid currentColor',
                        '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
                          boxShadow: 'inherit',
                        },
                        '&:before': {
                          display: 'none',
                        },
                      },
                      '& .MuiSlider-valueLabel': {
                        lineHeight: 1.2,
                        fontSize: 12,
                        background: 'unset',
                        padding: 0,
                        width: 32,
                        height: 32,
                        borderRadius: '50% 50% 50% 0',
                        backgroundColor: '#30D5C7',
                        transformOrigin: 'bottom left',
                        transform:
                          'translate(50%, -100%) rotate(-45deg) scale(0)',
                        '&:before': { display: 'none' },
                        '&.MuiSlider-valueLabelOpen': {
                          transform:
                            'translate(50%, -100%) rotate(-45deg) scale(1)',
                        },
                        '& > *': {
                          transform: 'rotate(45deg)',
                        },
                      },
                    }}
                  />
                  <div className={'text'}>Close</div>
                </div>
              </div>
              <div className="divider" />
              <div className={'zoom'}>
                <div
                  className={'indicator'}
                  onClick={() => {
                    if (zoom > 5) {
                      setZoom(zoom - 5);
                    }
                  }}
                >
                  <Remove className={'icon'} />
                </div>
                <div className={'percent'}>{`${zoom}%`}</div>
                <div
                  className={'indicator'}
                  onClick={() => {
                    setZoom(zoom + 5);
                  }}
                >
                  <Add className={'icon'} />
                </div>
              </div>
            </div>
          </ModelController>
        )}
      </div>
    </>
  );
};

export default ModelSimulate;

const CollapseCursor = styled.div<{ isModel?: boolean }>`
  transition: 0.3s ease-out;
  width: 24px;
  height: 48px;
  border-radius: ${(props) =>
    props.isModel ? '0px 8px 8px 0px' : '8px 0px 0px 8px'};
  //border: 1px solid var(--Line, #dbe2e5);
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 6;
  cursor: pointer;
  top: 24px;
  left: ${(props) => (props.isModel ? '8px' : '0')};
  @media screen and (max-width: 820px) {
    top: ${(props) => (props.isModel ? '40px' : '176px')};
  }
`;

const ModelModeSwitch = styled.div<{ is3D?: boolean }>`
  width: 200px;
  height: 32px;
  border-radius: 24px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 24px;
  left: calc(50% - 100px);
  .switch-item {
    width: 100%;
    height: 100%;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .left {
    background: ${(props) => (!props.is3D ? '#30D5C7' : '#fff')};
    color: ${(props) => (!props.is3D ? '#fff' : '#000')};
    border-radius: 24px 0px 0px 24px;
  }
  .right {
    background: ${(props) => (props.is3D ? '#30D5C7' : '#fff')};
    color: ${(props) => (props.is3D ? '#fff' : '#000')};
    border-radius: 0px 24px 24px 0px;
  }
`;

const ModelController = styled.div`
  width: 100%;
  height: 40px;
  position: absolute;
  z-index: 2;
  bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  @media screen and (max-width: 510px) {
    justify-content: end;
    align-items: end;
    padding-right: 6px;
    bottom: 6px;
  }
  .container {
    width: fit-content;
    display: flex;
    align-items: center;
    column-gap: 4px;
    padding: 8px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
    @media screen and (max-width: 510px) {
      flex-direction: column;
    }
    .control-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      svg {
        width: 16px;
        height: 16px;
      }
      .slide-action {
        width: 200px;
        height: 16px;
        display: flex;
        align-items: center;
        column-gap: 16px;
        font-size: 14px;
        color: #90a4ae;
        @media screen and (max-width: 510px) {
          font-size: 12px;
          flex-direction: column-reverse;
          row-gap: 8px;
          width: 24px;
          height: 120px;
        }
      }
    }
    .zoom {
      display: flex;
      align-items: center;
      column-gap: 16px;
      padding: 8px;
      @media screen and (max-width: 510px) {
        flex-direction: column-reverse;
        row-gap: 16px;
      }
      .indicator {
        color: #90a4ae;
        line-height: 0;
        cursor: pointer;
        .icon {
          width: 14px;
          height: 14px;
        }
      }
      .percent {
        color: #000;
        font-weight: bold;
        line-height: 0;
        @media screen and (max-width: 510px) {
          font-size: 12px;
        }
      }
    }
    .active {
      //background: var(--Blue-Grey-50, #dbe2e5);
    }
    .divider {
      background: #dbe2e5;
      width: 1px;
      height: 24px;
      @media screen and (max-width: 510px) {
        width: 24px;
        height: 1px;
      }
    }
  }
`;
