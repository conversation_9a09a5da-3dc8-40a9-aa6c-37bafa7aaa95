import React, { useRef, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import {
  Button,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { LoadingFadein } from '@/styles/share.styled';

const LayModelFormStyled = styled.div`
  animation: ${LoadingFadein} 0.3s ease-in;
  .upload-group {
    display: flex;
    gap: 16px;
    margin: 34px 0 8px;
    .upload {
      display: flex;
      flex-direction: column;
      flex: 1;
      row-gap: 8px;
      .upload-title {
        font-size: 14px;
        font-weight: 600;
      }
      .upload-zone {
        height: 164px;
        width: 100%;
        border: 2px dashed #dbe2e5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: 0.15s ease-in-out;
        flex-direction: column;
        overflow: hidden;
        position: relative;
        &:hover {
          border: 2px dashed #30d5c7;
        }
        .trash {
          height: 24px;
          width: 24px;
          position: absolute;
          top: 8px;
          right: 8px;
          border-radius: 4px;
          background-color: #d32f2f;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: 0.3s ease-out;
          &:hover {
            filter: brightness(0.8);
          }
          img {
            width: 20px;
            height: 20px;
          }
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .icon {
          display: flex;
          align-items: center;
          padding: 0 8px;
        }
        .name {
          font-weight: 400;
          font-size: 14px;
          max-width: 300px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .condition {
    font-size: 12px;
    font-weight: 400;
    color: #b0bec5;
    margin-bottom: 34px;
  }
  .topic {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 8px;
  }
  .form-group {
    display: flex;
    gap: 8px;
  }
`;

const validationSchema = yup.object({
  cut: yup.number().required('Required'),
  subMaterialDetailId: yup.number().required('Required'),
  itemSizeId: yup.number().required('Required'),
  subItemSizeId: yup.number().required('Required'),
  amountPerSheet: yup.number().required('Required'),
  layType: yup.number().required('Required'),
  printerId: yup.number().required('Required'),
  dimensionWidth: yup.number().required('Required'),
  dimensionLength: yup.number().required('Required'),
});

type LayModelFormProps = {
  submitCustomLay: (value: any) => void;
  updateFile: (file: any, index: number) => void;
  imageFiles: any;
  imageBlob: any;
  makeActiveImage: (value: number) => void;
  deleteFile: (index: number) => void;
  dataForLay: any;
};
const CustomLayForm = (props: LayModelFormProps) => {
  const {
    submitCustomLay,
    updateFile,
    imageFiles,
    imageBlob,
    makeActiveImage,
    deleteFile,
    dataForLay,
  } = props;
  const [layTemplate] = useState([
    { id: 1, name: 'เลย์ครึ่งชิ้น (0.5)', value: 0.5 },
    { id: 2, name: 'เลย์เต็มชิ้น (1)', value: 1 },
  ]);
  const fileInputRef1 = useRef<HTMLInputElement | null>(null);
  const fileInputRef2 = useRef<HTMLInputElement | null>(null);
  const [errorFileMsg, setErrorFileMsg] = useState<string>('');
  const [unit, setUnit] = useState<any>('');
  const formik = useFormik({
    initialValues: {
      cut: '',
      subMaterialDetailId: '',
      itemSizeId: '',
      subItemSizeId: '',
      amountPerSheet: '',
      layType: '',
      printerId: '',
      dimensionWidth: '',
      dimensionLength: '',
      unitSizeId: '',
      plateId: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      if (imageFiles.imageFile1 !== '' && imageFiles.imageFile2 !== '') {
        submitCustomLay(values);
      } else {
        setErrorFileMsg('กรุณาอัปโหลดรูปภาพ');
      }
    },
  });

  const handleClickUpload = (index: number) => {
    if (index === 1) {
      if (imageFiles.imageFile1) {
        makeActiveImage(1);
      } else if (fileInputRef1.current) {
        fileInputRef1.current.click();
      }
    } else if (index === 2) {
      if (imageFiles.imageFile2) {
        makeActiveImage(2);
      } else if (fileInputRef2.current) {
        fileInputRef2.current.click();
      }
    }
  };

  const validateFile = (value: any) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedSize = 10 * 1024 * 1024; // 10MB in bytes
    if (value.length > 0) {
      if (!allowedTypes.includes(value[0].type)) {
        return 'Please select a valid image file (JPEG, PNG, SVG, or GIF)';
      }
      if (value[0].size > allowedSize) {
        return 'Icon sizes should be less than 10MB';
      }
    }
    return true;
  };

  const changeFile = (file: any, index: number) => {
    if (file && file[0]) {
      updateFile(file[0], index);
    }
  };

  // console.log('imageFiles', imageFiles);
  // console.log('dataForLay', dataForLay);
  // console.log('formik', formik.values);
  const handleChangeFullPrintId = async (value: any) => {
    formik.setFieldValue('subItemSizeId', '');
    const fullPrint = await dataForLay.subMaterialItem.itemSizeDto.find(
      (item: any) => item.id === value
    );
    const { unitSize } = fullPrint.subItemSizeValueDto[0];
    setUnit(unitSize);
    formik.setFieldValue('unitSizeId', unitSize.id);
  };
  const handleChangeMachine = async (value: any) => {
    const printer = await dataForLay.printer.find(
      (item: any) => item.machineId === value
    );
    const plateId = printer.plate.id;
    formik.setFieldValue('plateId', plateId);
  };
  // console.log(dataForLay.subMaterialItem.itemSizeDto);
  return (
    <LayModelFormStyled>
      <form onSubmit={formik.handleSubmit}>
        <div className="upload-group">
          <div className="upload">
            <div className="upload-title">รูปขนาดกางออก</div>
            <div
              className="upload-zone"
              onClick={() => {
                handleClickUpload(1);
              }}
            >
              {imageFiles.imageFile1 ? (
                <>
                  <div
                    className="trash"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      deleteFile(1);
                    }}
                  >
                    <Image
                      src="/icons/delete.svg"
                      width={20}
                      height={20}
                      alt=""
                    />
                  </div>
                  <Image
                    src={imageBlob.blob1}
                    width={256}
                    height={256}
                    alt=""
                  />
                </>
              ) : (
                <>
                  <div className="icon">
                    <Image
                      src={'/icons/icon-upload.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  </div>
                  <div className="name">อัปโหลด</div>
                </>
              )}
              <input
                style={{ display: 'none' }}
                ref={fileInputRef1}
                type="file"
                onChange={(e) => {
                  const validationResult = validateFile(e.target.files);
                  if (validationResult === true) {
                    changeFile(e.target.files, 1);
                    setErrorFileMsg('');
                  } else {
                    setErrorFileMsg(validationResult);
                  }
                }}
              />
            </div>
          </div>
          <div className="upload">
            <div className="upload-title">รูปเลย์</div>
            <div
              className="upload-zone"
              onClick={() => {
                handleClickUpload(2);
              }}
            >
              {imageFiles.imageFile2 ? (
                <>
                  <div
                    className="trash"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      deleteFile(2);
                    }}
                  >
                    <Image
                      src="/icons/delete.svg"
                      width={20}
                      height={20}
                      alt=""
                    />
                  </div>
                  <Image
                    src={imageBlob.blob2}
                    width={256}
                    height={256}
                    alt=""
                  />
                </>
              ) : (
                <>
                  <div className="icon">
                    <Image
                      src={'/icons/icon-upload.svg'}
                      width={24}
                      height={24}
                      alt=""
                    />
                  </div>
                  <div className="name">อัปโหลด</div>
                </>
              )}
              <input
                style={{ display: 'none' }}
                ref={fileInputRef2}
                type="file"
                onChange={(e) => {
                  const validationResult = validateFile(e.target.files);
                  if (validationResult === true) {
                    changeFile(e.target.files, 2);
                    setErrorFileMsg('');
                  } else {
                    setErrorFileMsg(validationResult);
                  }
                }}
              />
            </div>
          </div>
        </div>
        <div className="condition">ไฟล์ JPG, PNG ขนาดสูงสุดไม่เกิน 10 mb.</div>
        {errorFileMsg && (
          <div
            style={{
              color: 'red',
              fontSize: '12px',
              margin: '-24px 0 0',
            }}
          >
            {errorFileMsg}
          </div>
        )}
        {dataForLay && (
          <>
            <div>
              <div className="topic">วัสดุพิมพ์</div>
              <FormControl
                fullWidth
                size="small"
                error={
                  formik.touched.subMaterialDetailId &&
                  Boolean(formik.errors.subMaterialDetailId)
                }
              >
                <Select
                  name="subMaterialDetailId"
                  value={formik.values.subMaterialDetailId}
                  onChange={formik.handleChange}
                  displayEmpty
                >
                  <MenuItem disabled value="">
                    <div className="text-[#78909C]">Select Material</div>
                  </MenuItem>
                  <MenuItem value={dataForLay.subMaterialItem.id}>
                    {dataForLay.subMaterialItem.nameTh}
                  </MenuItem>
                </Select>
                <FormHelperText>
                  {formik.touched.subMaterialDetailId &&
                    formik.errors.subMaterialDetailId}
                </FormHelperText>
              </FormControl>
            </div>
            <div>
              <div className="topic">ขนาดใบเต็ม</div>
              <FormControl
                fullWidth
                size="small"
                error={
                  formik.touched.itemSizeId && Boolean(formik.errors.itemSizeId)
                }
              >
                <Select
                  name="itemSizeId"
                  value={formik.values.itemSizeId}
                  onChange={(e: any) => {
                    formik.handleChange(e);
                    handleChangeFullPrintId(e.target.value);
                  }}
                  displayEmpty
                >
                  <MenuItem disabled value="">
                    <div className="text-[#78909C]">Select Size</div>
                  </MenuItem>
                  {dataForLay.subMaterialItem.itemSizeDto.map(
                    (item: any, index: React.Key) => (
                      <MenuItem key={index} value={item.id}>
                        {item.itemSizeName}
                      </MenuItem>
                    )
                  )}
                </Select>
                <FormHelperText>
                  {formik.touched.itemSizeId && formik.errors.itemSizeId}
                </FormHelperText>
              </FormControl>
            </div>
            {formik.values.itemSizeId && (
              <div>
                <div className="topic">ขนาดใบพิมพ์</div>
                <FormControl
                  fullWidth
                  size="small"
                  error={
                    formik.touched.subItemSizeId &&
                    Boolean(formik.errors.subItemSizeId)
                  }
                >
                  <Select
                    name="subItemSizeId"
                    value={formik.values.subItemSizeId}
                    onChange={formik.handleChange}
                    displayEmpty
                  >
                    <MenuItem disabled value="">
                      <div className="text-[#78909C]">Select Size</div>
                    </MenuItem>
                    {dataForLay.subMaterialItem.itemSizeDto
                      .filter(
                        (item: any) => item.id === formik.values.itemSizeId
                      )
                      .map((item: any) =>
                        item.subItemSize.map((subItem: any) => (
                          <MenuItem key={subItem.id} value={subItem.id}>
                            {subItem.itemSizeName}
                          </MenuItem>
                        ))
                      )}
                  </Select>
                  <FormHelperText>
                    {formik.touched.subItemSizeId &&
                      formik.errors.subItemSizeId}
                  </FormHelperText>
                </FormControl>
              </div>
            )}
            <div>
              <div className="topic">ผ่าพิมพ์</div>
              <TextField
                type="number"
                name="cut"
                placeholder="0"
                value={formik.values.cut}
                onChange={formik.handleChange}
                error={formik.touched.cut && Boolean(formik.errors.cut)}
                helperText={formik.touched.cut && formik.errors.cut}
              />
            </div>
            <div>
              <div className="topic">จำนวนชิ้น/ใบพิมพ์</div>
              <TextField
                type="number"
                name="amountPerSheet"
                placeholder="0"
                value={formik.values.amountPerSheet}
                onChange={formik.handleChange}
                error={
                  formik.touched.amountPerSheet &&
                  Boolean(formik.errors.amountPerSheet)
                }
                helperText={
                  formik.touched.amountPerSheet && formik.errors.amountPerSheet
                }
              />
            </div>
            <div>
              <div className="topic">รูปแบบการเลย์</div>
              <FormControl
                fullWidth
                size="small"
                error={formik.touched.layType && Boolean(formik.errors.layType)}
              >
                <Select
                  name="layType"
                  value={formik.values.layType}
                  onChange={formik.handleChange}
                  displayEmpty
                >
                  <MenuItem disabled value="">
                    <div className="text-[#78909C]">Select Size</div>
                  </MenuItem>
                  {layTemplate.map((item: any, index: React.Key) => (
                    <MenuItem key={index} value={item.value}>
                      {item.name}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  {formik.touched.layType && formik.errors.layType}
                </FormHelperText>
              </FormControl>
            </div>
            <div>
              <div className="topic">เครื่องพิมพ์</div>
              <FormControl
                fullWidth
                size="small"
                error={
                  formik.touched.printerId && Boolean(formik.errors.printerId)
                }
              >
                <Select
                  name="printerId"
                  value={formik.values.printerId}
                  onChange={(e: any) => {
                    formik.handleChange(e);
                    handleChangeMachine(e.target.value);
                  }}
                  displayEmpty
                >
                  <MenuItem disabled value="">
                    <div className="text-[#78909C]">Select Size</div>
                  </MenuItem>
                  {dataForLay.printer.map((item: any, index: React.Key) => (
                    <MenuItem key={index} value={item.machineId}>
                      {item.machineName}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  {formik.touched.printerId && formik.errors.printerId}
                </FormHelperText>
              </FormControl>
            </div>
            <div className="topic">ขนาดกางออก</div>
            <div className="form-group">
              <div>
                <TextField
                  type="number"
                  name="dimensionWidth"
                  placeholder="0"
                  value={formik.values.dimensionWidth}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.dimensionWidth &&
                    Boolean(formik.errors.dimensionWidth)
                  }
                  helperText={
                    formik.touched.dimensionWidth &&
                    formik.errors.dimensionWidth
                  }
                />
              </div>
              <div className="mt-[8px]">X</div>
              <div>
                <TextField
                  type="number"
                  name="dimensionLength"
                  placeholder="0"
                  value={formik.values.dimensionLength}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.dimensionLength &&
                    Boolean(formik.errors.dimensionLength)
                  }
                  helperText={
                    formik.touched.dimensionLength &&
                    formik.errors.dimensionLength
                  }
                />
              </div>
              {unit && (
                <div className="h-100 flex items-center">{unit.name}</div>
              )}
            </div>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              fullWidth
              sx={{ fontSize: '16px', margin: '40px 0 0', maxHeight: '40px' }}
            >
              <div>ส่งคำนวณราคา</div>
            </Button>
          </>
        )}
      </form>
    </LayModelFormStyled>
  );
};

export default CustomLayForm;
