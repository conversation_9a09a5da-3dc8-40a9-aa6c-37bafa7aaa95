import React from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

// const PackagingLays: any = dynamic(
//   () =>
//     import('@lucablockltd/packaging-dielines-and-lays/dist').then(
//       (module) => module.PackagingLays
//     ),
//   {
//     ssr: false,
//   }
// );

const PackagingLaysComponentStyled = styled.div`
  width: 100%;
  height: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
type PackagingDieLinesProps = {
  lays: any;
};
const PackagingLaysComponent = (props: PackagingDieLinesProps) => {
  const { lays } = props;
  console.log(lays);
  return (
    <PackagingLaysComponentStyled>
      {/* <PackagingLays {...lays} /> */}
    </PackagingLaysComponentStyled>
  );
};

export default PackagingLaysComponent;
