import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { ToggleButton, ToggleButtonGroup } from '@mui/material';
import AutoLayForm from '@/components/corporate/product-order/lay/AutoLayForm';
import { LoadingFadein } from '@/styles/share.styled';
import CustomLayForm from '@/components/corporate/product-order/lay/CustomLayForm';

const LayFormStyled = styled.div`
  padding: 40px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  .lay-type {
    width: 100%;
    min-height: 48px;
    max-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: #f5f7f8;
    .MuiToggleButtonGroup-root {
      height: 40px;
      margin: 0 4px;
      .Mui-selected {
        background-color: #ffffff;
        border-radius: 8px;
        z-index: 1;
        color: #263238;
        font-weight: 600;
        box-shadow: 0px 0px 8px 0px #2632381f;
      }
      button {
        background-color: transparent;
        color: #78909c;
        font-size: 14px;
        font-weight: 400;
        border: 0;
      }
    }
  }
`;

type LayModelFormProps = {
  submitCustomLay: (value: any) => void;
  updateFile: (file: any, index: number) => void;
  makeCustomLay: (value: boolean) => void;
  clearFile: () => void;
  itemsRender: any;
  dataForLay: any;
  showIndexLayModel: (index: number) => void;
  imageFiles: any;
  imageBlob: any;
  makeActiveImage: (value: number) => void;
  deleteFile: (index: number) => void;
  captureDielineFile: any;
};
const SideLayForm = (props: LayModelFormProps) => {
  const {
    submitCustomLay,
    updateFile,
    makeCustomLay,
    clearFile,
    itemsRender,
    dataForLay,
    showIndexLayModel,
    imageFiles,
    imageBlob,
    makeActiveImage,
    deleteFile,
    captureDielineFile,
  } = props;
  const [tab, setTab] = useState<number>(1);
  const handleChangeAutoLay = (
    event: React.MouseEvent<HTMLElement>,
    newValue: number
  ) => {
    if (newValue) {
      setTab(newValue);
    }
  };
  useEffect(() => {
    if (tab === 2) {
      makeCustomLay(true);
    } else {
      makeCustomLay(false);
      clearFile();
    }
  }, [tab]);
  return (
    <LayFormStyled>
      <div className="lay-type">
        <ToggleButtonGroup
          color="primary"
          value={tab}
          exclusive
          onChange={handleChangeAutoLay}
          fullWidth
        >
          <ToggleButton value={1}>อัตโนมัติ</ToggleButton>
          <ToggleButton value={2}>กำหนดเอง</ToggleButton>
        </ToggleButtonGroup>
      </div>
      {tab === 1 && (
        <AutoLayForm
          itemsRender={itemsRender}
          dataForLay={dataForLay}
          showIndexLayModel={(index: number) => {
            showIndexLayModel(index);
          }}
          captureDielineFile={captureDielineFile}
        />
      )}
      {tab === 2 && (
        <CustomLayForm
          submitCustomLay={(value: any) => {
            submitCustomLay(value);
          }}
          updateFile={(file: any, index: number) => {
            updateFile(file, index);
          }}
          imageFiles={imageFiles}
          imageBlob={imageBlob}
          makeActiveImage={(value: number) => {
            makeActiveImage(value);
          }}
          deleteFile={(index: number) => {
            deleteFile(index);
          }}
          dataForLay={dataForLay}
        />
      )}
    </LayFormStyled>
  );
};

export default SideLayForm;
