import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import {
  Button,
  FormControl,
  FormControlLabel,
  FormHelperText,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { LoadingFadein } from '@/styles/share.styled';
import { isEmpty } from 'lodash';
import CameraAltRoundedIcon from '@mui/icons-material/CameraAltRounded';
import { useRouter } from 'next/router';

const LayModelFormStyled = styled.div`
  padding: 40px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  animation: ${LoadingFadein} 0.3s ease-in;
  form {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
  .radio-border {
    width: 100%;
    box-shadow: 0 0 0 1px #dbe2e5;
    padding: 0 14px;
    border-radius: 8px;
  }
  .space-low-width {
    display: none;
    min-height: 32px;
    @media screen and (max-height: 1000px) {
      display: block;
    }
  }
  .product-name {
    height: 40px;
    width: 100%;
    max-width: 100%;
    box-shadow: 0 0 0 1px #dbe2e5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-right: 8px;
    .icon {
      display: flex;
      align-items: center;
      padding: 0 8px;
    }
    .name {
      font-weight: 600;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .topic {
    font-size: 14px;
    font-weight: 600;
    margin: 24px 0 8px;
  }
  .form-group {
    display: flex;
    gap: 8px;
  }
  .label {
    font-size: 12px;
    color: #b0bec5;
    margin-bottom: 4px;
  }
  .grid-size {
    width: 100%;
    display: grid;
    column-gap: 8px;
    row-gap: 12px;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    @media screen and (max-width: 1200px) {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    &.paper {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      @media screen and (max-width: 1200px) {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      }
    }
  }
`;

const validationSchema = yup.object({
  length: yup.number().required('Required').min(0.1, '>= 0.1'),
  width: yup.number().required('Required').min(0.1, '>= 0.1'),
  height: yup.number().required('Required').min(0.1, '>= 0.1'),
  laySpace: yup.number().required('Required').min(0.1, '>= 0.1'),
  gripper: yup.number().required('Required').min(0.1, '>= 0.1'),
  borderLeft: yup.number().required('Required').min(0.1, '>= 0.1'),
  borderRight: yup.number().required('Required').min(0.1, '>= 0.1'),
  borderTop: yup.number().required('Required').min(0.1, '>= 0.1'),
  paperWidth: yup.number().required('Required').min(0.1, '>= 0.1'),
  paperHeight: yup.number().required('Required').min(0.1, '>= 0.1'),
  printMachine: yup.number().required('Required').min(0.1, '>= 0.1'),
});

type LayModelFormProps = {
  updateCanvas: (size: any, paper: any, unit: string) => void;
  dataForLay: any;
  modelProperties: any;
  paperProperties: any;
  makeRequestToCalculateAutoLay: (value: any) => void;
  makeCaptureDielineFile: (file: any) => void;
};
const SideModelForm = (props: LayModelFormProps) => {
  const {
    updateCanvas,
    dataForLay,
    modelProperties,
    paperProperties,
    makeRequestToCalculateAutoLay,
    makeCaptureDielineFile,
  } = props;
  const router = useRouter();
  const { id } = router.query;
  const [sizeOptionsProperties, setSizeOptionsProperties] = useState<any>([]);
  const [initialValues, setInitialValues] = useState<any>({});
  const [printMachineList, setPrintMachineList] = useState<any>([]);
  const [isPrint, setIsPrint] = useState<boolean>(false);
  const [requiredPrinter, setRequiredPrinter] = useState<boolean>(false);
  const [captureDielineFile, setCaptureDielineFile] = useState<any>(null);
  // const [paperPropertiesValue, setPaperPropertiesValue] = useState<any>([]);
  const formik = useFormik<any>({
    initialValues: {
      ...initialValues,
      laySpace: '',
      unit: 'mm',
      gripper: '',
      borderLeft: '',
      borderRight: '',
      borderTop: '',
      paperWidth: '',
      paperHeight: '',
      printMachine: '',
    },
    validationSchema,
    onSubmit: (values: any) => {
      if (isPrint && !requiredPrinter) {
        makeRequestToCalculateAutoLay(values);
      } else {
        setRequiredPrinter(true);
      }
    },
  });
  useEffect(() => {
    if (!isEmpty(dataForLay)) {
      if (dataForLay.printStatus) {
        setPrintMachineList(dataForLay.printer);
        setIsPrint(true);
      }
      formik.setFieldValue(
        'length',
        dataForLay.length || sizeOptionsProperties.length
      );
      formik.setFieldValue(
        'width',
        dataForLay.width || sizeOptionsProperties.width
      );
      formik.setFieldValue(
        'height',
        dataForLay.height || sizeOptionsProperties.height
      );
    }
  }, [dataForLay]);

  useEffect(() => {
    if (!isEmpty(modelProperties)) {
      const size = modelProperties.sizeOptionsProperties.map(
        (item: any) => item
      );
      setSizeOptionsProperties(size);
    }
  }, [modelProperties]);
  // useEffect(() => {
  //   if (!isEmpty(paperProperties)) {
  //     const paperArray = Object.keys(paperProperties).map((name: any) => {
  //       const item: any = { name };
  //       if (paperProperties[name].label) {
  //         item.label = paperProperties[name].label;
  //       }
  //       if (paperProperties[name].value !== undefined) {
  //         item.value = paperProperties[name].value;
  //       } else {
  //         item.value = paperProperties[name];
  //       }
  //       return item;
  //     });
  //
  //     paperArray.forEach((item: any) => {
  //       if (item.name === 'paperWidth') {
  //         item.label = 'ความกว้างกระดาษ';
  //       } else if (item.name === 'paperHeight') {
  //         item.label = 'ความสูงกระดาษ';
  //       }
  //     });
  //     setPaperPropertiesValue(paperArray);
  //   }
  // }, [paperProperties]);
  useEffect(() => {
    if (!isEmpty(paperProperties)) {
      formik.setFieldValue('laySpace', paperProperties.laySpace.value);
      formik.setFieldValue('gripper', paperProperties.gripper.value);
      formik.setFieldValue('borderLeft', paperProperties.borderLeft.value);
      formik.setFieldValue('borderRight', paperProperties.borderRight.value);
      formik.setFieldValue('borderTop', paperProperties.borderTop.value);
      formik.setFieldValue('paperWidth', paperProperties.paperWidth);
      formik.setFieldValue('paperHeight', paperProperties.paperHeight);
    }
  }, [paperProperties]);
  useEffect(() => {
    if (!isEmpty(sizeOptionsProperties)) {
      const newInitialValues = { ...initialValues };

      sizeOptionsProperties.forEach((item: any) => {
        if (
          item.name === 'length' ||
          item.name === 'width' ||
          item.name === 'height'
        ) {
          newInitialValues[item.name] = '';
        } else {
          newInitialValues[item.name] = item.value;
        }
      });
      setInitialValues(newInitialValues);

      sizeOptionsProperties.forEach((item: any) => {
        if (
          item.name !== 'length' &&
          item.name !== 'width' &&
          item.name !== 'height'
        ) {
          formik.setFieldValue(item.name, item.value);
        }
      });
    }
  }, [sizeOptionsProperties]);

  useEffect(() => {
    const updatedSizeOptionsProperties = sizeOptionsProperties.map(
      (item: any) => {
        const formikValue = formik.values[item.name];
        const updatedItem = {
          ...item,
          value: formikValue !== undefined ? formikValue : item.value,
        };
        return updatedItem;
      }
    );
    const updatedPaperProperties = { ...paperProperties };
    Object.keys(formik.values).forEach((key) => {
      if (updatedPaperProperties[key]) {
        if (typeof updatedPaperProperties[key] === 'object') {
          updatedPaperProperties[key].value = formik.values[key];
        } else {
          updatedPaperProperties[key] = formik.values[key];
        }
      }
    });
    setCaptureDielineFile(null);
    updateCanvas(
      updatedSizeOptionsProperties,
      updatedPaperProperties,
      formik.values.unit
    );
  }, [
    formik.values.laySpace,
    formik.values.gripper,
    formik.values.borderLeft,
    formik.values.borderRight,
    formik.values.borderTop,
    formik.values.paperWidth,
    formik.values.paperHeight,
    formik.values.dustFlap,
    formik.values.topFlap,
    formik.values.glueFlap,
    formik.values.unit,
  ]);
  useEffect(() => {
    if (formik.values.printMachine) {
      setRequiredPrinter(false);
    }
  }, [formik.values.printMachine]);
  const handleCaptureDieline = () => {
    const canvas = document.getElementById(
      'die-line'
    ) as HTMLCanvasElement | null;
    if (canvas) {
      // แปลง canvas เป็น data URL
      const dataUrl = canvas.toDataURL('image/jpeg');
      // แปลง data URL เป็น Blob
      const byteString = atob(dataUrl.split(',')[1]);
      const mimeString = dataUrl.split(',')[0].split(':')[1].split(';')[0];
      const arrayBuffer = new ArrayBuffer(byteString.length);
      const uint8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i);
      }
      // สร้าง File จาก Blob
      const blob = new Blob([uint8Array], { type: mimeString });
      // สร้าง URL จาก Blob
      const blobUrl = URL.createObjectURL(blob);
      // เปิดรูปภาพในหน้าต่างใหม่
      window.open(blobUrl, '', 'width=720,height=720');
      const dielineFile = new File([blob], `dieline_${id}.jpg`, {
        type: 'image/jpeg',
      });
      setCaptureDielineFile(dielineFile);
      makeCaptureDielineFile(dielineFile);
    } else {
      console.error('Canvas element not found');
    }
  };
  if (!isEmpty(modelProperties) && !isEmpty(paperProperties)) {
    return (
      <LayModelFormStyled>
        <form onSubmit={formik.handleSubmit}>
          <div>
            <div className="product-name">
              <div className="icon">
                <Image
                  src={'/icons/icon-package.svg'}
                  width={24}
                  height={24}
                  alt=""
                />
              </div>
              <div className="name">{dataForLay.modelName}</div>
            </div>
            <div className="topic">Display Unit</div>
            <div className="radio-border">
              <RadioGroup
                row
                name="unit"
                value={formik.values.unit}
                onChange={(e: any) => {
                  formik.handleChange(e);
                }}
              >
                <FormControlLabel value="mm" control={<Radio />} label="mm" />
                <FormControlLabel value="cm" control={<Radio />} label="cm" />
                <FormControlLabel
                  value="inch"
                  control={<Radio />}
                  label="inch"
                />
              </RadioGroup>
            </div>

            <div className="topic">ขนาดสินค้า</div>
            <div className="grid-size">
              {modelProperties.sizeOptionsProperties.map(
                (item: any, index: number) => {
                  const fieldName = item.name;
                  const fieldValue = formik.values[fieldName];
                  return (
                    <div key={index}>
                      <div className="label">{item.label}</div>
                      <TextField
                        type="number"
                        name={fieldName}
                        placeholder="0"
                        value={fieldValue}
                        onChange={formik.handleChange}
                        disabled={index < 3}
                        fullWidth
                        InputProps={{
                          endAdornment: <div className="p-[2px]">cm</div>,
                        }}
                        error={
                          formik.touched[fieldName] &&
                          Boolean(formik.errors[fieldName])
                        }
                        helperText={
                          formik.touched[fieldName] &&
                          (formik.errors[fieldName] as string)
                        }
                      />
                    </div>
                  );
                }
              )}
            </div>
            <div className="topic">ตั้งค่ากระดาษ</div>
            <div className="grid-size paper">
              <div>
                <div className="label">ระยะห่างเลย์</div>
                <TextField
                  type="number"
                  name="laySpace"
                  placeholder="0"
                  value={formik.values.laySpace}
                  onChange={formik.handleChange}
                  InputProps={{
                    endAdornment: <div className="p-[2px]">cm</div>,
                  }}
                  error={
                    formik.touched.laySpace && Boolean(formik.errors.laySpace)
                  }
                  helperText={
                    formik.touched.laySpace &&
                    (formik.errors.laySpace as string)
                  }
                />
              </div>
              <div>
                <div className="label">กริปเปอร์</div>
                <TextField
                  type="number"
                  name="gripper"
                  placeholder="0"
                  value={formik.values.gripper}
                  onChange={formik.handleChange}
                  InputProps={{
                    endAdornment: <div className="p-[2px]">cm</div>,
                  }}
                  error={
                    formik.touched.gripper && Boolean(formik.errors.gripper)
                  }
                  helperText={
                    formik.touched.gripper && (formik.errors.gripper as string)
                  }
                />
              </div>
            </div>
            <div className="grid-size mt-[12px]">
              <div>
                <div className="label">ขอบซ้าย</div>
                <TextField
                  type="number"
                  name="borderLeft"
                  placeholder="0"
                  value={formik.values.borderLeft}
                  onChange={formik.handleChange}
                  InputProps={{
                    endAdornment: <div className="p-[2px]">cm</div>,
                  }}
                  error={
                    formik.touched.borderLeft &&
                    Boolean(formik.errors.borderLeft)
                  }
                  helperText={
                    formik.touched.borderLeft &&
                    (formik.errors.borderLeft as string)
                  }
                />
              </div>
              <div>
                <div className="label">ขอบขวา</div>
                <TextField
                  type="number"
                  name="borderRight"
                  placeholder="0"
                  value={formik.values.borderRight}
                  onChange={formik.handleChange}
                  InputProps={{
                    endAdornment: <div className="p-[2px]">cm</div>,
                  }}
                  error={
                    formik.touched.borderRight &&
                    Boolean(formik.errors.borderRight)
                  }
                  helperText={
                    formik.touched.borderRight &&
                    (formik.errors.borderRight as string)
                  }
                />
              </div>
              <div>
                <div className="label">ขอบบน</div>
                <TextField
                  type="number"
                  name="borderTop"
                  placeholder="0"
                  value={formik.values.borderTop}
                  onChange={formik.handleChange}
                  InputProps={{
                    endAdornment: <div className="p-[2px]">cm</div>,
                  }}
                  error={
                    formik.touched.borderTop && Boolean(formik.errors.borderTop)
                  }
                  helperText={
                    formik.touched.borderTop &&
                    (formik.errors.borderTop as string)
                  }
                />
              </div>
            </div>
            {isPrint && (
              <div>
                <div className="topic">เครื่องพิมพ์</div>
                <FormControl
                  fullWidth
                  size="small"
                  error={
                    formik.touched.printMachine &&
                    Boolean(formik.errors.printMachine)
                  }
                >
                  <Select
                    name="printMachine"
                    value={formik.values.printMachine}
                    onChange={formik.handleChange}
                    displayEmpty
                  >
                    <MenuItem disabled value="">
                      <div>Select Machine</div>
                    </MenuItem>
                    {printMachineList.map((item: any) => (
                      <MenuItem key={item.machineId} value={item.machineId}>
                        {item.machineName}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    {requiredPrinter && (
                      <div className="text-red-500">กรุณาเลือกเครื่องพิมพ์</div>
                    )}
                  </FormHelperText>
                </FormControl>
              </div>
            )}
          </div>
          <div
            className="flex"
            style={{
              columnGap: '16px',
            }}
          >
            <Tooltip
              title={`กรุณาปรับตำแหน่งให้เห็นรายละเอียดทั้งหมด`}
              placement="left"
              arrow
            >
              <Button
                type="button"
                variant="contained"
                color="dark"
                fullWidth
                sx={{
                  fontSize: '16px',
                  margin: '32px 0 0',
                  maxHeight: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  columnGap: '8px',
                }}
                onClick={handleCaptureDieline}
              >
                <CameraAltRoundedIcon
                  sx={{
                    fontSize: '20px',
                  }}
                />
                บันทึกภาพ
              </Button>
            </Tooltip>
            <Button
              type="submit"
              variant="contained"
              color="dark"
              fullWidth
              disabled={captureDielineFile === null}
              sx={{ fontSize: '16px', margin: '32px 0 0', maxHeight: '40px' }}
            >
              อัปเดตข้อมูล
            </Button>
          </div>
          <div className="space-low-width" />
        </form>
      </LayModelFormStyled>
    );
  }
  return null;
};

export default SideModelForm;
