import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Tab, Tabs } from '@mui/material';
import SideModelForm from '@/components/corporate/product-order/lay/SideModelForm';
import SideLayForm from '@/components/corporate/product-order/lay/SideLayForm';
import { useRouter } from 'next/router';
import apiLay from '@/services/order/lay';

const LayFormStyled = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  @media screen and (max-width: 1200px) {
    ::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
`;
export const TabCustomStyle = styled(Tabs)`
  background: white;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 9;
  .hr-custom {
    position: absolute;
    border-bottom: 1px solid #dbe2e5;
    width: 100%;
    bottom: 0;
  }
  .MuiTabs-flexContainer {
    display: flex;
    justify-content: center;
    button {
      flex: 1;
      font-size: 14px;
      height: 64px;
    }
  }
  .MuiTabs-indicator {
    background-color: #263238;
    height: 1px;
  }
  button {
    font-size: 14px;
    font-weight: 400;
    color: #607d8b;
    text-transform: initial;
    min-width: 0;
    padding: 0;
    span {
      display: none;
    }
  }
  .Mui-selected {
    color: #263238;
    font-weight: 800;
  }
`;
type LayFormProps = {
  updateFile: (file: any, index: number) => void;
  makeCustomLay: (value: boolean) => void;
  clearFile: () => void;
  dataForLay: any;
  showModel: (value: boolean) => void;
  modelProperties: any;
  updateCanvas: (size: any, paper: any, unit: string) => void;
  paperProperties: any;
  makeRequestToCalculateAutoLay: (value: any) => void;
  itemsRender: any;
  showIndexLayModel: (index: number) => void;
  imageFiles: any;
  imageBlob: any;
  makeActiveImage: (value: number) => void;
  deleteFile: (index: number) => void;
};
const LayForm = (props: LayFormProps) => {
  const {
    updateFile,
    makeCustomLay,
    clearFile,
    dataForLay,
    showModel,
    modelProperties,
    updateCanvas,
    paperProperties,
    makeRequestToCalculateAutoLay,
    itemsRender,
    showIndexLayModel,
    imageFiles,
    imageBlob,
    makeActiveImage,
    deleteFile,
  } = props;
  const [tab, setTab] = React.useState(1);
  const router = useRouter();
  const { id } = router.query;
  const [captureDielineFile, setCaptureDielineFile] = useState<any>(null);
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTab(newValue);
  };

  const handleAddLay = async (value: any) => {
    const sendValue = {
      ...value,
      layDataId: Number(id),
    };
    await saveManualLay(sendValue);
  };
  const saveManualLay = async (sendValue: any) => {
    const formData: any = new FormData();
    formData.append('dimensionFile', imageFiles.imageFile1);
    formData.append('layoutFile', imageFiles.imageFile2);
    formData.append(
      'createLayoutRequest',
      JSON.stringify({
        ...sendValue,
      })
    );
    const res = await apiLay.saveLay(formData);
    if (!res.isError) {
      router.push('/corporate/laydata/?status=PREPARE_PRICE_CALCULATE');
    }
  };
  useEffect(() => {
    if (tab === 1) {
      makeCustomLay(false);
      showModel(true);
    } else {
      showModel(false);
    }
  }, [tab]);

  return (
    <LayFormStyled>
      <TabCustomStyle value={tab} onChange={handleChange}>
        <div className="hr-custom" />
        <Tab label="Model" />
        <Tab label="วางเลย์" id="lay-tab" />
      </TabCustomStyle>
      {tab === 1 && (
        <SideModelForm
          updateCanvas={(size: any, paper: any, unit: string) => {
            updateCanvas(size, paper, unit);
          }}
          dataForLay={dataForLay}
          modelProperties={modelProperties}
          paperProperties={paperProperties}
          makeRequestToCalculateAutoLay={(value: any) => {
            makeRequestToCalculateAutoLay(value);
          }}
          makeCaptureDielineFile={(file: any) => {
            setCaptureDielineFile(file);
          }}
        />
      )}
      {tab === 2 && (
        <SideLayForm
          submitCustomLay={(value: any) => {
            handleAddLay(value);
          }}
          updateFile={(file: any, index: number) => {
            updateFile(file, index);
          }}
          makeCustomLay={(value: boolean) => {
            makeCustomLay(value);
          }}
          clearFile={() => {
            clearFile();
          }}
          itemsRender={itemsRender}
          dataForLay={dataForLay}
          showIndexLayModel={(index: number) => {
            showIndexLayModel(index);
          }}
          imageFiles={imageFiles}
          imageBlob={imageBlob}
          makeActiveImage={(value: number) => {
            makeActiveImage(value);
          }}
          deleteFile={(index: number) => {
            deleteFile(index);
          }}
          captureDielineFile={captureDielineFile}
        />
      )}
    </LayFormStyled>
  );
};

export default LayForm;
