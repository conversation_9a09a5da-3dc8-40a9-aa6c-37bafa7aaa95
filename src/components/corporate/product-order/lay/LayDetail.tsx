import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import LayForm from '@/components/corporate/product-order/lay/LayForm';
import Image from 'next/image';
import { LoadingFadein } from '@/styles/share.styled';
import PackagingDieLinesComponent from '@/components/corporate/product-order/lay/PackagingDieLinesComponent';
import PackagingLaysComponent from '@/components/corporate/product-order/lay/PackagingLaysComponent';
import { isEmpty } from 'lodash';
import axios from 'axios';
// import { ModelsOptionsProperties } from '@lucablockltd/packaging-dielines-and-lays/dist';

const LayDetailStyled = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 64px);
  overflow: auto;
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: button !important;
  }
  @media screen and (max-width: 1200px) {
    ::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  @media screen and (max-width: 820px) {
    height: calc(100vh - 136px);
  }
  .content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: auto;
    @media screen and (max-width: 1200px) {
      flex-direction: column;
      max-height: none;
    }
    .canvas-zone {
      height: 100%;
      flex: 1;
      background-color: #f5f7f8;
      position: relative;
      animation: ${LoadingFadein} 0.3s ease-in;
      @media screen and (max-width: 1200px) {
        min-height: 50vh;
      }
      .update-text {
        animation: ${LoadingFadein} 0.3s ease-in;
      }
      .custom-lay-image-warp {
        width: 100%;
        img {
          object-fit: contain;
        }
      }
      .blank-file {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        height: 100%;
        left: 50%;
        transform: translateX(-50%);
        @media screen and (max-width: 1200px) {
          display: none;
        }
        .title {
          color: #cfd8dc;
          font-size: 22px;
          font-weight: 600;
        }
        .description {
          font-size: 12px;
          font-weight: 400;
          color: #cfd8dc;
        }
      }
    }
    .controller-zone {
      min-width: 280px;
      width: 422px;
      height: 100%;
      box-shadow: -5px 0 8px -6px #2632381f;
      @media screen and (max-width: 1200px) {
        width: 100%;
        height: auto;
      }
    }
  }
`;
type LayDetailProps = {
  dataForLay: any;
  activeStep: number;
};

// type ModelCode = keyof typeof ModelsOptionsProperties;

const LayDetail = (props: LayDetailProps) => {
  const { dataForLay } = props;
  const [isCustomLay, setIsCustomLay] = useState<boolean>(false);
  const [showModel, setShowModel] = useState<boolean>(true);
  const [modelProperties, setModelProperties] = useState<any>({});
  const [paperProperties, setPaperProperties] = useState<any>(null);
  const [itemsRender, setItemsRender] = useState<any>([]);
  const [lays, setLays] = useState<any>({});
  const [activeImageBlob, setActiveImageBlob] = useState<number | null>(null);
  const [imageFiles, setImageFiles] = useState<any>({
    imageFile1: '',
    imageFile2: '',
  });
  const [imageBlob, setImageBlob] = useState<{ blob1: string; blob2: string }>({
    blob1: '',
    blob2: '',
  });
  const [dieLines, setDieLines] = useState({});
  const [displayUnit, setDisplayUnit] = useState<string>('mm');

  // useEffect(() => {
  //   if (typeof window !== 'undefined') {
  //     import('@lucablockltd/packaging-dielines-and-lays/dist')
  //       .then((module) => {
  //         setPaperProperties(module.PaperProperties);
  //       })
  //       .catch((err) => console.error('Failed to load PaperProperties', err));
  //   }
  // }, []);
  //
  // useEffect(() => {
  //   if (!isEmpty(dataForLay) && dataForLay.modelCode) {
  //     setModelProperties(
  //       ModelsOptionsProperties[dataForLay.modelCode as ModelCode]
  //     );
  //   }
  // }, [dataForLay]);

  const handleActiveImage = (value: number) => {
    setActiveImageBlob(value);
  };
  const handleUpdateCanvas = (size: any, paper: any, unit: string) => {
    if (!isEmpty(size) && !isEmpty(paper) && !isEmpty(unit)) {
      setModelProperties({
        ...modelProperties,
        sizeOptionsProperties: size,
      });
      setPaperProperties(paper);
      setDisplayUnit(unit);
      setDieLines({
        ...dieLines,
        unit: unit,
      });
    }
  };
  useEffect(() => {
    if (!isEmpty(modelProperties)) {
      setDieLines({
        modelsOptionsProperties: { ...modelProperties },
        fontsUrl: '/fonts/Mitr Medium_Regular.typeface.json',
        bgColor: '#F4F5F5',
        unit: displayUnit,
      });
    }
  }, [modelProperties]);
  const calculateAutoLay = async (value: any) => {
    // console.log('dataForLay', dataForLay);
    // console.log('modelProperties', modelProperties);
    // console.log('paperProperties', paperProperties);
    let sendPrinter = {};
    if (value.printMachine) {
      const findPrinter = dataForLay.printer.find(
        (item: any) => item.machineId === value.printMachine
      );
      const {
        machineId: id,
        machineName: name,
        maxPrintHeight,
        maxPrintWidth,
        maxSheetHeight,
        maxSheetWidth,
        minSheetHeight,
        minSheetWidth,
      } = findPrinter;

      sendPrinter = {
        id,
        name,
        maxPrintHeight,
        maxPrintWidth,
        maxSheetHeight,
        maxSheetWidth,
        minSheetHeight,
        minSheetWidth,
      };
    }
    const sizeOptionsInCm = await modelProperties.sizeOptionsProperties.reduce(
      (result: any, item: any) => {
        result[item.name] = item.value;
        return result;
      },
      {}
    );

    const sizeOptionsInMm = Object.keys(sizeOptionsInCm).reduce(
      (result: any, key: string) => {
        result[key] = sizeOptionsInCm[key] * 10; // 1 cm = 10 mm
        return result;
      },
      {}
    );

    const itemProperty = {
      model: modelProperties.name,
      ...sizeOptionsInMm,
    };
    const materialsFormatted = await dataForLay.subMaterialItem.itemSizeDto.map(
      (item: any) => {
        const subMaterialsFormatted = item.subItemSize.map(
          (subItemSize: any) => {
            return {
              id: subItemSize.id,
              width: subItemSize.subItemSizeValueDto[0].rawValue,
              height: subItemSize.subItemSizeValueDto[1].rawValue,
              cut: subItemSize.cut,
            };
          }
        );

        return {
          id: item.id,
          name: dataForLay.subMaterialItem.nameTh,
          width: item.subItemSizeValueDto[0].rawValue,
          height: item.subItemSizeValueDto[1].rawValue,
          subMaterials: subMaterialsFormatted,
        };
      }
    );
    // console.log('materialsFormatted', materialsFormatted);
    const materialOptions = {
      gripper: paperProperties.gripper.value * 10,
      borderLeft: paperProperties.borderLeft.value * 10,
      borderRight: paperProperties.borderRight.value * 10,
      borderTop: paperProperties.borderTop.value * 10,
      laySpace: paperProperties.laySpace.value * 10,
    };
    const requestLaysData = {
      items: itemProperty,
      printer: !isEmpty(sendPrinter) ? sendPrinter : null,
      materials: materialsFormatted,
      materialOptions,
      printStatus: dataForLay.printStatus,
    };
    await sendAutoLay(
      'https://estimate-dev.honconnect.co/lays',
      requestLaysData
    );
  };

  const sendAutoLay = async (url: string, data: any) => {
    try {
      const response = await axios.post(url, data);
      if (response.status === 200) {
        setItemsRender(response.data.data);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };
  useEffect(() => {
    if (!isEmpty(itemsRender)) {
      const layTab = document.getElementById('lay-tab');
      if (layTab) {
        layTab.click();
      }
    }
  }, [itemsRender]);

  const handleChangeIndexLayModel = (index: number) => {
    setLays({
      ...lays,
      itemsRender: itemsRender[index],
    });
  };

  useEffect(() => {
    setLays({
      paperProperties,
      modelsOptionsProperties: modelProperties,
      itemsRender: itemsRender[0],
      fontsUrl: '/fonts/Mitr Medium_Regular.typeface.json',
      bgColor: '#F4F5F5',
    });
  }, [itemsRender]);

  const handleDeleteFile = (index: number) => {
    if (index === 1) {
      setImageBlob({
        ...imageBlob,
        blob1: '',
      });
      setImageFiles({
        ...imageFiles,
        imageFile1: '',
      });
      setActiveImageBlob(2);
    } else if (index === 2) {
      setImageBlob({
        ...imageBlob,
        blob2: '',
      });
      setImageFiles({
        ...imageFiles,
        imageFile2: '',
      });
      setActiveImageBlob(1);
    }
  };
  const handleUpdateFile = (file: any, index: number) => {
    const imageUrl = URL.createObjectURL(file);
    if (index === 1) {
      setImageBlob({
        ...imageBlob,
        blob1: imageUrl,
      });
      setImageFiles({
        ...imageFiles,
        imageFile1: file,
      });
    } else {
      setImageBlob({
        ...imageBlob,
        blob2: imageUrl,
      });
      setImageFiles({
        ...imageFiles,
        imageFile2: file,
      });
    }
  };
  const handleClearFile = () => {
    setImageBlob({
      blob1: '',
      blob2: '',
    });
    setImageFiles({
      imageFile1: '',
      imageFile2: '',
    });
  };
  const handleMakeCustomLay = (value: boolean) => {
    setIsCustomLay(value);
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
  };

  const handleDrop = (e: any) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (imageFiles.imageFile1 !== '' && imageFiles.imageFile2 !== '') {
      return null;
    }
    if (imageFiles.imageFile1) {
      handleUpdateFile(file, 2);
    } else if (imageFiles.imageFile2) {
      handleUpdateFile(file, 1);
    } else {
      handleUpdateFile(file, 1);
      setActiveImageBlob(1);
    }
  };
  // console.log('imageBlob', imageBlob);
  return (
    <>
      <LayDetailStyled>
        {/* <ProductOrderStep /> */}
        <div className="content-wrap">
          <div className="canvas-zone">
            {isCustomLay ? (
              !isEmpty(imageBlob.blob1) || !isEmpty(imageBlob.blob2) ? (
                <div
                  className="custom-lay-image-warp w-full h-full"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <Image
                    src={
                      activeImageBlob === null
                        ? !isEmpty(imageBlob.blob1)
                          ? imageBlob.blob1
                          : imageBlob.blob2
                        : activeImageBlob === 1
                        ? imageBlob.blob1
                        : imageBlob.blob2
                    }
                    fill
                    alt=""
                    draggable="false"
                  />
                </div>
              ) : (
                <>
                  <div
                    className="blank-file w-full"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                  >
                    <Image
                      src="/icons/icon-upload-large.svg"
                      alt=""
                      width={80}
                      height={80}
                    />
                    <div className="title">Drag & Drop or Upload</div>
                    <div className="description">
                      High resolution image (JPG, PNG, PDF)
                    </div>
                  </div>
                </>
              )
            ) : (
              <div className="w-full h-full flex justify-center items-center">
                {showModel ? (
                  !isEmpty(dieLines) && (
                    <PackagingDieLinesComponent dieLines={dieLines} />
                  )
                ) : !isEmpty(itemsRender) ? (
                  <PackagingLaysComponent lays={lays} />
                ) : (
                  <div className="update-text">
                    กรุณากดปุ่มอัปเดตข้อมูลเพื่อใช้งานเลย์อัตโนมัติ
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="controller-zone">
            <LayForm
              updateFile={(file: any, index: number) => {
                handleUpdateFile(file, index);
              }}
              makeCustomLay={(value: boolean) => {
                handleMakeCustomLay(value);
              }}
              clearFile={() => {
                handleClearFile();
              }}
              dataForLay={dataForLay}
              modelProperties={modelProperties}
              paperProperties={paperProperties}
              showModel={(value: boolean) => {
                setShowModel(value);
                if (value) {
                  setItemsRender([]);
                }
              }}
              updateCanvas={(size: any, paper: any, unit: string) => {
                handleUpdateCanvas(size, paper, unit);
              }}
              makeRequestToCalculateAutoLay={(value: any) => {
                calculateAutoLay(value);
              }}
              itemsRender={itemsRender}
              showIndexLayModel={(index: number) => {
                handleChangeIndexLayModel(index);
              }}
              imageFiles={imageFiles}
              imageBlob={imageBlob}
              makeActiveImage={(value: number) => {
                handleActiveImage(value);
              }}
              deleteFile={(index: number) => {
                handleDeleteFile(index);
              }}
            />
          </div>
        </div>
      </LayDetailStyled>
    </>
  );
};

export default LayDetail;
