import React from 'react';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';

// const PackagingDieLines: any = dynamic(
//   () =>
//     import('@lucablockltd/packaging-dielines-and-lays/dist').then(
//       (module) => module.PackagingDieLines
//     ),
//   {
//     ssr: false,
//   }
// );

const PackagingDieLinesComponentStyled = styled.div`
  width: 100%;
  height: 100%;
  animation: ${LoadingFadein} 0.3s ease-in;
`;
type PackagingDieLinesProps = {
  dieLines: any;
};
const PackagingDieLinesComponent = (props: PackagingDieLinesProps) => {
  const { dieLines } = props;
  console.log(dieLines);
  return (
    <PackagingDieLinesComponentStyled>
      {/* <PackagingDieLines {...dieLines} /> */}
    </PackagingDieLinesComponentStyled>
  );
};

export default PackagingDieLinesComponent;
