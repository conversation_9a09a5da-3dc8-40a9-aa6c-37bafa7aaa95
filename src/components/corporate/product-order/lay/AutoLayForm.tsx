import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Button, Tooltip } from '@mui/material';
import styled from 'styled-components';
import { LoadingFadein } from '@/styles/share.styled';
import { numberWithCommas } from '@/utils/number';
import { isEmpty } from 'lodash';
import KeyboardDoubleArrowDownRoundedIcon from '@mui/icons-material/KeyboardDoubleArrowDownRounded';
import { useRouter } from 'next/router';
import CameraAltRoundedIcon from '@mui/icons-material/CameraAltRounded';
import apiLay from '@/services/order/lay';

const AutoLayFormStyled = styled.div`
  animation: ${LoadingFadein} 0.3s ease-in;
  .product-name {
    height: 40px;
    width: 100%;
    border: 1px solid #dbe2e5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    .icon {
      display: flex;
      align-items: center;
      padding: 0 8px;
    }
    .name {
      font-weight: 600;
    }
  }
  .topic {
    font-size: 14px;
    font-weight: 600;
    margin: 24px 0 8px;
  }
  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
  .loss-wrap {
    max-height: 214px;
    overflow: auto;
    position: relative;
    .loss {
      width: 100%;
      height: 64px;
      border-radius: 8px;
      box-shadow: #dbe2e5 0px 0px 0px 1px inset;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 0 24px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: 0.3s ease-in-out;
      &:last-child {
        margin-bottom: 0;
      }
      &.active {
        box-shadow: #30d5c7 0px 0px 0px 2px inset;
      }
    }
  }
  .arrow-more {
    display: flex;
    justify-content: center;
  }
  .detail {
    width: 100%;
    margin-top: 16px;
    .list {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #dbe2e5;
      font-size: 14px;
      .title {
        font-weight: 400;
        color: #90a4ae;
      }
      .value {
        font-weight: 600;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
      }
    }
  }
`;
type AutoLayFormProps = {
  itemsRender: any;
  dataForLay: any;
  showIndexLayModel: (index: number) => void;
  captureDielineFile: any;
};
const AutoLayForm = (props: AutoLayFormProps) => {
  const { itemsRender, dataForLay, showIndexLayModel, captureDielineFile } =
    props;
  const router = useRouter();
  const [data, setData] = useState<any>({});
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [captureLayFile, setCaptureLayFile] = useState<any>(null);
  const { id } = router.query;
  const handleClickLoss = (data: any, index: number) => {
    setData(data);
    setActiveIndex(index);
    showIndexLayModel(index);
  };
  useEffect(() => {
    if (!isEmpty(itemsRender)) {
      setData(itemsRender[0]);
    }
  }, [itemsRender]);
  const captureLay = () => {
    const canvas = document.getElementById('lays') as HTMLCanvasElement | null;
    if (canvas) {
      // แปลง canvas เป็น data URL
      const dataUrl = canvas.toDataURL('image/jpeg');
      // แปลง data URL เป็น Blob
      const byteString = atob(dataUrl.split(',')[1]);
      const mimeString = dataUrl.split(',')[0].split(':')[1].split(';')[0];
      const arrayBuffer = new ArrayBuffer(byteString.length);
      const uint8Array = new Uint8Array(arrayBuffer);

      for (let i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i);
      }
      // สร้าง Blob จาก Uint8Array
      const blob = new Blob([uint8Array], { type: mimeString });
      // สร้าง URL จาก Blob
      const blobUrl = URL.createObjectURL(blob);

      // เปิดรูปภาพในหน้าต่างใหม่
      window.open(blobUrl, '', 'width=720,height=720');

      // สร้าง File จาก Blob
      const captureLayFile = new File([blob], `lay_${id}.jpg`, {
        type: 'image/jpeg',
      });
      setCaptureLayFile(captureLayFile);
    } else {
      console.error('Canvas element not found');
    }
  };
  const handleSubmitLay = async () => {
    // console.log('itemsRender', itemsRender);
    // console.log('data', data);
    // console.log('dataForLay', dataForLay);
    const fullPrint = await dataForLay.subMaterialItem.itemSizeDto.find(
      (item: any) => item.id === data.materials.id
    );
    const unitSizeId = fullPrint.subItemSizeValueDto[0].unitSize.id;

    const printer = await dataForLay.printer.filter(
      (item: any) => item.machineId === data.printer.id
    );

    const dataSubmitLay = {
      layDataId: Number(id),
      subMaterialDetailId: dataForLay.subMaterialItem.subMaterialDetail.id,
      plateId: printer[0].plate.id,
      cut: data.materials.subMaterials.cut,
      itemSizeId: data.materials.id,
      subItemSizeId: data.materials.subMaterials.id,
      amountPerSheet: data.total,
      layType: data.shapeType,
      printerId: data.printer.id,
      dimensionWidth: data.dimension.width,
      dimensionLength: data.dimension.height,
      unitSizeId,
    };
    saveAutoLay(dataSubmitLay, captureLayFile);
  };

  const saveAutoLay = async (dataSubmitLay: any, captureLayFile: any) => {
    // console.log('dataSubmitLay', dataSubmitLay);
    const formData: any = new FormData();
    formData.append('dimensionFile', captureDielineFile);
    formData.append('layoutFile', captureLayFile);
    formData.append(
      'createLayoutRequest',
      JSON.stringify({
        ...dataSubmitLay,
      })
    );
    const res = await apiLay.saveLay(formData);
    if (!res.isError) {
      router.push('/corporate/laydata/?status=PREPARE_PRICE_CALCULATE');
    }
  };
  if (!isEmpty(data)) {
    return (
      <AutoLayFormStyled>
        <div className="topic">Model</div>
        <div className="product-name">
          <div className="icon">
            <Image
              src={'/icons/icon-package.svg'}
              width={24}
              height={24}
              alt=""
            />
          </div>
          <div className="name">{dataForLay.modelName}</div>
        </div>
        <div className="topic">รูปแบบเลย์</div>
        <div className="loss-wrap">
          {itemsRender.map((item: any, index: number) => {
            return (
              <div
                key={index}
                className={`loss ${activeIndex === index ? 'active' : ''}`}
                onClick={() => {
                  handleClickLoss(item, index);
                }}
              >
                <div className="text">{`สูญเสีย ${item.wastage}%`}</div>
                <div className="text">{`${numberWithCommas(item.total)}x`}</div>
              </div>
            );
          })}
        </div>
        {itemsRender.length > 3 && (
          <div className="arrow-more">
            <KeyboardDoubleArrowDownRoundedIcon />
          </div>
        )}
        <div className="detail">
          <div className="list">
            <div className="title">ขนาดสินค้า</div>
            <Tooltip
              title={`${numberWithCommas(
                dataForLay.length
              )} x ${numberWithCommas(dataForLay.width)} x ${numberWithCommas(
                dataForLay.height
              )} cm`}
              placement="left"
              arrow
            >
              <div className="value">
                {`${numberWithCommas(dataForLay.length)} x ${numberWithCommas(
                  dataForLay.width
                )} x ${numberWithCommas(dataForLay.height)} cm`}
              </div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">ขนาดกางออก dimension</div>
            <Tooltip
              title={`${(data.dimension.width / 25.4).toFixed(2)} x ${(
                data.dimension.height / 25.4
              ).toFixed(2)} inch`}
              placement="left"
              arrow
            >
              <div className="value">
                {`${(data.dimension.width / 25.4).toFixed(2)} x ${(
                  data.dimension.height / 25.4
                ).toFixed(2)} inch`}
              </div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">ประเภทกระดาษ</div>
            <Tooltip title={data.materials.name} placement="left" arrow>
              <div className="value">{data.materials.name}</div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">จำนวนเลย์</div>
            <Tooltip
              title={numberWithCommas(data.total)}
              placement="left"
              arrow
            >
              <div className="value">{numberWithCommas(data.total)}</div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">ใบเต็ม</div>
            <Tooltip
              title={`${(data.materials.width / 25.4).toFixed(2)} x ${(
                data.materials.height / 25.4
              ).toFixed(2)} inch.`}
              placement="left"
              arrow
            >
              <div className="value">
                {`${(data.materials.width / 25.4).toFixed(2)} x ${(
                  data.materials.height / 25.4
                ).toFixed(2)} inch.`}
              </div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">ผ่า</div>
            <Tooltip
              title={data.materials.subMaterials.cut}
              placement="left"
              arrow
            >
              <div className="value">{data.materials.subMaterials.cut}</div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">ใบพิมพ์</div>
            <Tooltip
              title={`${(data.materials.subMaterials.width / 25.4).toFixed(
                2
              )} x ${(data.materials.subMaterials.height / 25.4).toFixed(
                2
              )} inch.`}
              placement="left"
              arrow
            >
              <div className="value">
                {`${(data.materials.subMaterials.width / 25.4).toFixed(2)} x ${(
                  data.materials.subMaterials.height / 25.4
                ).toFixed(2)} inch.`}
              </div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">รุ่นเครื่องพิมพ์</div>
            <Tooltip title={data.printer.name} placement="left" arrow>
              <div className="value">{data.printer.name}</div>
            </Tooltip>
          </div>
          <div className="list">
            <div className="title">สูญเสีย</div>
            <Tooltip title={`${data.wastage}%`} placement="left" arrow>
              <div className="value">{`${data.wastage}%`}</div>
            </Tooltip>
          </div>
        </div>
        <div
          className="flex"
          style={{
            columnGap: '16px',
          }}
        >
          <Tooltip
            title={`กรุณาปรับตำแหน่งให้เห็นรายละเอียดทั้งหมด`}
            placement="left"
            arrow
          >
            <Button
              type="button"
              variant="contained"
              color="dark"
              fullWidth
              sx={{
                fontSize: '16px',
                margin: '32px 0 0',
                maxHeight: '40px',
                display: 'flex',
                alignItems: 'center',
                columnGap: '8px',
              }}
              onClick={captureLay}
            >
              <CameraAltRoundedIcon
                sx={{
                  fontSize: '20px',
                }}
              />
              บันทึกภาพ
            </Button>
          </Tooltip>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{ fontSize: '16px', margin: '32px 0 0', maxHeight: '40px' }}
            onClick={handleSubmitLay}
            disabled={captureLayFile === null}
          >
            บันทึกเลย์
          </Button>
        </div>
      </AutoLayFormStyled>
    );
  }
  return (
    <AutoLayFormStyled>
      <div
        className="w-full flex justify-center text-center mt-[24px] text-[14px]"
        style={{
          color: '#cfd8dc',
        }}
      >
        กรุณากดปุ่มอัปเดตข้อมูลเพื่อใช้งานเลย์อัตโนมัติ
      </div>
    </AutoLayFormStyled>
  );
};

export default AutoLayForm;
