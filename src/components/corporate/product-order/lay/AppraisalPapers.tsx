import React from 'react';
import Image from 'next/image';
import styled from 'styled-components';

const AppraisalPapersStyled = styled.section`
  display: flex;
  width: 100%;
  padding: 24px;
  justify-content: center;
  .page {
    width: 21cm;
    min-height: 29.7cm;
    padding: 2cm;
    border: 1px #d3d3d3 solid;
    border-radius: 5px;
    background: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    font-size: 10px;
    overflow: hidden;
    @media screen and (max-width: 1140px) {
      width: 100%;
    }
    @media screen and (max-width: 1100px) {
      padding: 40px;
    }
    @media screen and (max-width: 424px) {
      padding: 24px;
    }
    .header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      column-gap: 40px;
      @media screen and (max-width: 1140px) {
        flex-wrap: wrap;
        column-gap: 40px;
        row-gap: 24px;
      }
      .lside {
        flex: 1 1 0%;
        display: flex;
        flex-direction: column;
        row-gap: 38px;
        .text-group {
          display: flex;
          flex-direction: column;
          row-gap: 8px;
          .customer {
            line-height: 0;
          }
          .title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            column-gap: 16px;
            @media screen and (max-width: 424px) {
              font-size: 20px;
            }
          }
          .contact {
            white-space: nowrap;
            @media screen and (max-width: 700px) {
              white-space: normal;
            }
            span {
              color: #dbe2e5;
            }
          }
        }
      }
      .rside {
        flex: 1 1 0%;
        @media screen and (max-width: 370px) {
          overflow: auto;
          flex: 1 1 280px;
        }
        .topic {
          font-size: 24px;
          font-weight: 600;
          height: 44.8px;
          display: flex;
          align-items: center;
          @media screen and (max-width: 424px) {
            font-size: 20px;
          }
        }
        .product {
          height: 100px;
          border-radius: 8px 8px 0 0;
          border: 1px solid #dbe2e5;
          border-bottom: none;
          position: relative;
          display: flex;
          align-items: center;
          overflow: hidden;
          padding: 0 16px;
          column-gap: 16px;
          img {
            border-radius: 8px;
          }
          .text-group {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .ld {
              font-size: 20px;
              font-weight: 600;
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .name {
              font-size: 14px;
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .info {
          height: 100px;
          border-radius: 0 0 8px 8px;
          border: 1px solid #dbe2e5;
          position: relative;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .row {
            width: 100%;
            display: flex;
            height: 50%;

            .column {
              width: 50%;
              height: 100%;
              display: flex;
              align-items: center;

              .text-group {
                display: flex;
                padding: 0 16px;
                flex-direction: column;
                overflow: hidden;

                .title {
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .text {
                  font-weight: 600;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }

          .line-y {
            width: 1px;
            position: absolute;
            height: 100%;
            background-color: #dbe2e5;
            left: 50%;
            transform: translateX(-50%);
          }

          .line-x {
            height: 1px;
            position: absolute;
            width: 100%;
            background-color: #dbe2e5;
            bottom: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
    .product-spec {
      width: 100%;
      border-top: 2px solid;
      margin-top: 40px;
      display: flex;
      flex-direction: column;
      .topic {
        font-size: 14px;
        font-weight: 600;
        margin: 16px 0;
      }
      .list-wrap {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        column-gap: 40px;
        overflow: auto;
        .lside {
          flex: 1 1 0%;
          display: flex;
          flex-direction: column;
          .list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 34px;
            border-bottom: 1px solid #dbe2e5;
            column-gap: 24px;
            .key {
              color: #90a4ae;
              white-space: nowrap;
              @media screen and (max-width: 424px) {
                width: 78px;
              }
            }
            .value {
              font-weight: 600;
              white-space: nowrap;
              @media screen and (max-width: 424px) {
                width: 174px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            &:first-child {
              border-top: 1px solid #dbe2e5;
            }
          }
        }

        .rside {
          flex: 1 1 0%;
          display: flex;
          flex-direction: column;
          .list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 34px;
            border-bottom: 1px solid #dbe2e5;
            column-gap: 24px;
            .key {
              color: #90a4ae;
              white-space: nowrap;
              @media screen and (max-width: 424px) {
                width: 78px;
              }
            }
            .value {
              font-weight: 600;
              white-space: nowrap;
              @media screen and (max-width: 424px) {
                width: 174px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            &:first-child {
              border-top: 1px solid #dbe2e5;
            }
          }
        }
      }
    }
    .amount {
      width: 100%;
      margin-top: 40px;
      overflow: auto;
      .list {
        width: 100%;
        display: flex;
        align-items: center;
        height: 40px;
        .lside {
          flex: 1 1 0%;
          min-width: 200px;
          display: flex;
          height: 100%;
          align-items: center;
          padding-left: 16px;
        }
        .rside {
          flex: 1 1 0%;
          display: flex;
          height: 100%;
          align-items: center;
          padding-left: 40px;

          .col {
            flex: 1 1 0%;
            min-width: 94px;
          }
        }
        &:nth-child(1) {
          .lside {
            border-top: 2px solid;
          }
          .rside {
            border-top: 2px solid;
          }
          .col {
            font-size: 12px;
            font-weight: 600;
          }
        }
        &:nth-child(2) {
          .lside {
            border-top: 1px solid;
          }
          .rside {
            border-top: 1px solid;
          }
        }
        &:nth-child(3) {
          .lside {
            background: #f5f7f8;
          }
          .rside {
            background: #f5f7f8;
          }
        }
        &:nth-child(5) {
          color: white;
          .lside {
            background: #263238;
          }
          .rside {
            background: #263238;
          }
          .col {
            font-size: 12px;
            font-weight: 600;
          }
        }
      }
    }
  }
  @page {
    size: A4;
    margin: 0;
  }
  @media print {
    .page {
      margin: 0;
      border: initial;
      border-radius: initial;
      width: initial;
      min-height: initial;
      box-shadow: initial;
      background: initial;
      page-break-after: always;
    }
  }
`;
const AppraisalPapers = () => {
  return (
    <AppraisalPapersStyled>
      <div className="page">
        <div className="header">
          <div className="lside">
            <div className="text-group">
              <div className="title">
                <Image
                  src="/images/digiboxs.svg"
                  width={38.5}
                  height={44.8}
                  alt=""
                />
                Digiboxs.Co.,Ltd.
              </div>
              <div>เลขที่ 6 ซอยบางแค 12 แขวงบางแค เขตบางแค กรุงเทพฯ 10160</div>
              <div className="contact">
                โทร. 020849982 <span>|</span> โทร. 020849983 <span>| </span>
                <EMAIL>
              </div>
              <div>เลขประจำตัวผู้เสียภาษี 0105553024586</div>
            </div>
            <div className="text-group">
              <div className="customer">ลูกค้า</div>
              <div className="title">บริษัท อิมแพลนเทียม จำกัด</div>
              <div>
                3886/2 ชั้นที่ 1 และชั้นที่ 3 ถ.พระรามที่ 4 แขวงพระโขนง
                เขตคลองเตย กรุงเทพมหานคร 10110
              </div>
              <div className="contact">
                นิติบุคคล <span>|</span> โทร. 0848379487 <span>| </span>
                <EMAIL>
              </div>
              <div>เลขประจำตัวผู้เสียภาษี 0105557169325</div>
            </div>
          </div>
          <div className="rside">
            <div className="topic">ใบประเมินราคา</div>
            <div className="product">
              <Image
                src={'/images/mock-image.jpg'}
                width={70}
                height={70}
                alt=""
              />
              <div className="text-group">
                <div className="ld">LD-2023001</div>
                <div className="name">Mailer Box Standard</div>
              </div>
            </div>
            <div className="info">
              <div className="line-y" />
              <div className="line-x" />
              <div className="row">
                <div className="column">
                  <div className="text-group">
                    <div className="title">Model</div>
                    <div className="text">Mailer Box Standard Type A</div>
                  </div>
                </div>
                <div className="column">
                  <div className="text-group">
                    <div className="title">ขนาดกางออก</div>
                    <div className="text">20.3 x 17.4 cm</div>
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="column">
                  <div className="text-group">
                    <div className="title">วันที่สร้างเอกสาร</div>
                    <div className="text">11/11/2023</div>
                  </div>
                </div>
                <div className="column">
                  <div className="text-group">
                    <div className="title">กำหนดยืนยันราคา</div>
                    <div className="text">11/11/2023</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-[8px]">
              พนักงานขาย:{' '}
              <span
                style={{
                  fontWeight: '600',
                }}
              >
                ธันยารัต สมมณี, 0861234567
              </span>
            </div>
          </div>
        </div>
        <div className="product-spec">
          <div className="topic">สเปคสินค้า </div>
          <div className="list-wrap">
            <div className="lside">
              <div className="list">
                <div className="key">ขนาดสินค้า</div>
                <div className="value">30.5x20x3 ซ.ม.</div>
              </div>
              <div className="list">
                <div className="key">วัสดุ</div>
                <div className="value">กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม</div>
              </div>
              <div className="list">
                <div className="key">ด้านพิมพ์</div>
                <div className="value">ด้านหน้า และ ด้านหลัง</div>
              </div>
              <div className="list">
                <div className="key">การพิมพ์</div>
                <div className="value">ระบบ Offset</div>
              </div>
              <div className="list">
                <div className="key">สี</div>
                <div className="value">
                  ด้านหน้า CMYK, ด้านหลัง CMYK และ PANTONE
                </div>
              </div>
              <div className="list">
                <div className="key">จำนวน/เทียบราคา</div>
                <div className="value">100, 100, 5000</div>
              </div>
              <div className="list">
                <div className="key">ตัวอย่างสินค้า</div>
                <div className="value">PDF, Digital Print Proof</div>
              </div>
            </div>
            <div className="rside">
              <div className="list">
                <div className="key">ออกแบบ</div>
                <div className="value">ออกแบบอาร์ตเวิร์กใหม่</div>
              </div>
              <div className="list">
                <div className="key">เคลือบผิว</div>
                <div className="value">Vanish Glossy</div>
              </div>
              <div className="list">
                <div className="key">เทคนิคพิเศษ 1</div>
                <div className="value">Embossing 3x2.5 cm</div>
              </div>
              <div className="list">
                <div className="key">เทคนิคพิเศษ 2</div>
                <div className="value">Embossing 1x4.3 cm</div>
              </div>
              <div className="list">
                <div className="key">เทคนิคพิเศษ 3</div>
                <div className="value">Spot UV(เหมาเต็มผ่น)</div>
              </div>
              <div className="list">
                <div className="key">เทคนิคพิเศษ 4</div>
                <div className="value">Foil Stamping Rose Gold 2x2 cm</div>
              </div>
            </div>
          </div>
        </div>
        <div className="amount">
          <div className="list">
            <div className="lside">จำนวน</div>
            <div className="rside">
              <div className="col">1,000</div>
              <div className="col">2,000</div>
              <div className="col">3,000</div>
            </div>
          </div>
          <div className="list">
            <div className="lside">ราคา/หน่วย</div>
            <div className="rside">
              <div className="col">64.50</div>
              <div className="col">55.25</div>
              <div className="col">51.75</div>
            </div>
          </div>
          <div className="list">
            <div className="lside">ราคารวม</div>
            <div className="rside">
              <div className="col">40,125.00</div>
              <div className="col">32,125.00</div>
              <div className="col">35,125.00</div>
            </div>
          </div>
          <div className="list">
            <div className="lside">ภาษีมูลค่าเพิ่ม VAT 7%</div>
            <div className="rside">
              <div className="col">3,200.50</div>
              <div className="col">2,400.75</div>
              <div className="col">2,200.50</div>
            </div>
          </div>
          <div className="list">
            <div className="lside">รวมเป็นเงิน</div>
            <div className="rside">
              <div className="col">40,125.00</div>
              <div className="col">40,125.00</div>
              <div className="col">40,125.00</div>
            </div>
          </div>
        </div>
      </div>
    </AppraisalPapersStyled>
  );
};

export default AppraisalPapers;
