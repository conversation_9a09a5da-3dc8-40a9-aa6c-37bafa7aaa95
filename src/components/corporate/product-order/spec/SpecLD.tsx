// import Image from 'next/image';
// import Link from 'next/link';
// import React from 'react';
// import styled from 'styled-components';
// import { LoadingFadein } from '@/styles/share.styled';
// import ProductOrderStep from '@/components/corporate/product-order/ProductOrderStep';
// import { useRouter } from 'next/router';
//
// const SpecLDStyle = styled.div`
//   .content-wrap {
//     width: 100%;
//     height: 100%;
//     display: flex;
//     overflow: auto;
//     @media screen and (max-width: 1200px) {
//       flex-direction: column;
//       max-height: none;
//     }
//     .product-detail-wrap {
//       display: flex;
//       height: 100%;
//       width: 100%;
//       padding: 0 0 0 40px;
//       align-items: flex-start;
//       gap: 40px;
//       animation: ${LoadingFadein} 0.3s ease-in;
//       position: relative;
//       @media screen and (max-width: 1500px) {
//         flex-direction: column;
//         padding: 40px;
//       }
//
//       .canvas {
//         display: flex;
//         flex-direction: column;
//         width: 50%;
//         height: calc(100vh - 80px);
//         background-color: #f5f7f8;
//         border-radius: 8px;
//         overflow: hidden;
//         position: sticky;
//         top: 0;
//         margin-top: 20px;
//         justify-content: center;
//         align-items: center;
//
//         @media screen and (max-width: 1500px) {
//           width: 100%;
//           min-height: 50vh;
//           position: relative;
//         }
//       }
//
//       .detail-zone {
//         width: 50%;
//         height: 100%;
//         overflow: auto;
//         padding: 40px 40px 0 0;
//         @media screen and (max-width: 1500px) {
//           width: 100%;
//           padding: 0;
//           overflow: initial;
//         }
//
//         .product-info {
//           display: flex;
//           justify-content: space-between;
//           flex-wrap: wrap;
//           row-gap: 24px;
//           overflow: hidden;
//
//           .product {
//             display: flex;
//             align-items: center;
//             column-gap: 40px;
//             overflow: hidden;
//
//             .image {
//               min-width: 100px;
//               width: 100px;
//               height: 100px;
//               border-radius: 8px;
//               overflow: hidden;
//
//               img {
//                 width: 100%;
//                 height: 100%;
//                 object-fit: cover;
//               }
//             }
//
//             .text {
//               display: flex;
//               flex-direction: column;
//               overflow: hidden;
//
//               .ld {
//                 font-weight: 600;
//                 font-sizes: 28px;
//                 white-space: nowrap;
//                 max-width: 100%;
//                 overflow: hidden;
//                 text-overflow: ellipsis;
//               }
//
//               .name {
//                 font-weight: 400;
//                 font-sizes: 22px;
//                 white-space: nowrap;
//                 max-width: 100%;
//                 overflow: hidden;
//                 text-overflow: ellipsis;
//               }
//             }
//           }
//
//           .create-date {
//             font-sizes: 12px;
//             color: #90a4ae;
//             font-weight: 400;
//           }
//         }
//
//         .person-info {
//           height: 156px;
//           width: 100%;
//           border-radius: 8px;
//           border: 1px solid #dbe2e5;
//           margin-top: 40px;
//           position: relative;
//           display: flex;
//           flex-direction: column;
//           overflow: hidden;
//
//           .row {
//             width: 100%;
//             display: flex;
//             height: 50%;
//
//             .column {
//               width: 50%;
//               height: 100%;
//               display: flex;
//               align-items: center;
//
//               .text-group {
//                 display: flex;
//                 padding: 0 24px;
//                 flex-direction: column;
//                 overflow: hidden;
//
//                 .title {
//                   font-sizes: 12px;
//                   color: #90a4ae;
//                   white-space: nowrap;
//                   overflow: hidden;
//                   text-overflow: ellipsis;
//                 }
//
//                 .text {
//                   font-weight: 600;
//                   white-space: nowrap;
//                   overflow: hidden;
//                   text-overflow: ellipsis;
//                 }
//               }
//
//               .profile-group {
//                 display: flex;
//                 column-gap: 16px;
//                 align-items: center;
//                 padding: 0 24px;
//                 overflow: hidden;
//
//                 .image {
//                   width: 40px;
//                   min-width: 40px;
//                   height: 40px;
//                   border-radius: 50%;
//                   overflow: hidden;
//
//                   img {
//                     width: 100%;
//                     height: 100%;
//                   }
//                 }
//
//                 .profile-text-group {
//                   display: flex;
//                   flex-direction: column;
//                   overflow: hidden;
//
//                   .name {
//                     font-weight: 600;
//                     white-space: nowrap;
//                     overflow: hidden;
//                     text-overflow: ellipsis;
//                   }
//
//                   .role {
//                     font-sizes: 12px;
//                     font-weight: 400;
//                     white-space: nowrap;
//                     overflow: hidden;
//                     text-overflow: ellipsis;
//                   }
//                 }
//               }
//             }
//           }
//
//           .line-y {
//             width: 1px;
//             position: absolute;
//             height: 100%;
//             background-color: #dbe2e5;
//             left: 50%;
//             transform: translateX(-50%);
//           }
//
//           .line-x {
//             height: 1px;
//             position: absolute;
//             width: 100%;
//             background-color: #dbe2e5;
//             bottom: 50%;
//             transform: translateY(-50%);
//           }
//         }
//
//         .remark {
//           margin-top: 40px;
//           max-width: 450px;
//         }
//
//         .technical-info {
//           width: 100%;
//           display: flex;
//           flex-direction: column;
//
//           .list {
//             height: 48px;
//             display: flex;
//             justify-content: space-between;
//             align-items: center;
//             border-top: 1px solid #dbe2e5;
//             column-gap: 40px;
//
//             &:first-child {
//               margin-top: 16px;
//             }
//
//             &:last-child {
//               border-bottom: 1px solid #dbe2e5;
//               @media screen and (max-width: 1500px) {
//                 margin-bottom: 40px;
//               }
//             }
//
//             .topic {
//               color: #90a4ae;
//               font-weight: 400;
//               white-space: nowrap;
//             }
//
//             .value {
//               font-weight: 600;
//               white-space: nowrap;
//               overflow: hidden;
//               text-overflow: ellipsis;
//             }
//           }
//         }
//       }
//     }
//   }
// `;
// const SpecLD = () => {
//   const router = useRouter();
//   const { id: numberLD } = router.query;
//   return (
//     <SpecLDStyle>
//       <ProductOrderStep numberLD={numberLD} />
//       <div className="content-wrap">
//         <div className="product-detail-wrap">
//           <div className="canvas">Image</div>
//           <div className="detail-zone">
//             <div className="product-info">
//               <div className="product">
//                 <div className="image">
//                   <Image
//                     src={'/images/mock-image.jpg'}
//                     width={174}
//                     height={174}
//                     alt=""
//                   />
//                 </div>
//                 <div className="text">
//                   <div className="ld">LD-2023001</div>
//                   <div className="name">Mailer Box Standard</div>
//                 </div>
//               </div>
//               <div className="create-date">
//                 สร้างเมื่อ 20 ธ.ค. 2566, 15:34 น.
//               </div>
//             </div>
//             <div className="person-info">
//               <div className="line-y" />
//               <div className="line-x" />
//               <div className="row">
//                 <div className="column">
//                   <div className="text-group">
//                     <div className="title">Model</div>
//                     <div className="text">Mailer Box Standard Type A</div>
//                   </div>
//                 </div>
//                 <div className="column">
//                   <div className="text-group">
//                     <div className="title">ขนาดกางออก</div>
//                     <div className="text">20.3 x 17.4 cm</div>
//                   </div>
//                 </div>
//               </div>
//               <div className="row">
//                 <div className="column">
//                   <div className="profile-group">
//                     <div className="image">
//                       <Image
//                         src={'/images/mock-image.jpg'}
//                         width={40}
//                         height={40}
//                         alt=""
//                       />
//                     </div>
//                     <div className="profile-text-group">
//                       <div className="name">นิมิตร หมายมั่นคง</div>
//                       <div className="role">ลูกค้า</div>
//                     </div>
//                   </div>
//                 </div>
//                 <div className="column">
//                   <div className="profile-group">
//                     <div className="image">
//                       <Image
//                         src={'/images/mock-image.jpg'}
//                         width={40}
//                         height={40}
//                         alt=""
//                       />
//                     </div>
//                     <div className="profile-text-group">
//                       <div className="name">ธันยารัต สมมณี</div>
//                       <div className="role">พนักงานขาย</div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//             <div className="remark">
//               <b>หมายเหตุ</b> :
//               บรรจุภัณฑ์นี้จะนำไปใช้กับอุปกรณ์การสื่อสารเคลื่อนที่ ช่วยแนะนำ
//               Bubble กันกระแทกสำหรับห่อหุ้มสินค้า
//             </div>
//             <div className="mt-[40px]">
//               <b>สเปคสินค้า</b>
//             </div>
//             <div className="technical-info">
//               <div className="list">
//                 <div className="topic">ขนาดสินค้า</div>
//                 <div className="value">30.5x20x3 ซ.ม.</div>
//               </div>
//               <div className="list">
//                 <div className="topic">วัสดุ</div>
//                 <div className="value">กระดาษอาร์ตการ์ด 1 หน้า 270 แกรม</div>
//               </div>
//               <div className="list">
//                 <div className="topic">เทียบราคา/จำนวน </div>
//                 <div className="value">100, 100, 5000</div>
//               </div>
//               <div className="list">
//                 <div className="topic">ด้านพิมพ์</div>
//                 <div className="value">
//                   ด้านหน้า CMYK, ด้านหลัง CMYK และ PANTONE
//                 </div>
//               </div>
//               <div className="list">
//                 <div className="topic">ตัวอย่างสินค้า</div>
//                 <div className="value">PDF, Digital Print Proof</div>
//               </div>
//               <div className="list">
//                 <div className="topic">การเคลือบ</div>
//                 <div className="value">Vanish Glossy</div>
//               </div>
//               <div className="list">
//                 <div className="topic">เทคนิคพิเศษ 1</div>
//                 <div className="value">Embossing 3x2.5 cm</div>
//               </div>
//               <div className="list">
//                 <div className="topic">เทคนิคพิเศษ 2</div>
//                 <div className="value">Embossing 1x4.3 cm</div>
//               </div>
//               <div className="list">
//                 <div className="topic">เทคนิคพิเศษ 3</div>
//                 <div className="value">Spot UV(เหมาเต็มผ่น)</div>
//               </div>
//               <div className="list">
//                 <div className="topic">เทคนิคพิเศษ 4</div>
//                 <div className="value">Foil Stamping Rose Gold 2x2 cm</div>
//               </div>
//               <div className="list">
//                 <div className="topic">ออกแบบ</div>
//                 <div className="value">ออกแบบอาร์ตเวิร์กใหม่</div>
//               </div>
//               <div className="list">
//                 <div className="topic">ลิงก์อาร์ตเวิร์ก/ตัวอย่างสินค้า</div>
//                 <div className="value">
//                   <Link
//                     href={'https://www.pinterest.com/pin/19914423344595127/'}
//                     target={'_blank'}
//                   >
//                     https://www.pinterest.com/pin/19914423344595127/
//                   </Link>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </SpecLDStyle>
//   );
// };
//
// export default SpecLD;
