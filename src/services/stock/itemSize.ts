import HttpStock from '@/api/HttpStock';

const PATH = '/item-size';

const apiItemSize = {
  getItemSize: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  addItemSize: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await HttpStock.post(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateItemSize: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await HttpStock.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getItemSizeById: async (id: number) => {
    try {
      const response = await HttpStock.get(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteItemSizeById: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getItemSizeDimensionConfig: async (id: any) => {
    try {
      // const response = await HttpStock.get(`${PATH}/dimension-config/${id}`);
      const response = await HttpStock.get(
        `${PATH}/list?dimensionConfigId=${id}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiItemSize;
