import HttpStock from '@/api/HttpStock';

const PATH = '/product-images';

const apiProductImages = {
  getList: async (productId: string) => {
    try {
      const response = await HttpStock.get(`${PATH}/${productId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  upload: async (productId: string, index: number, data: any) => {
    try {
      const response = await HttpStock.put(
        `${PATH}/${productId}?index=${index}`,
        data,
        {
          headers: {
            'content-type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadThumbnail: async (data: any) => {
    try {
      const response = await HttpStock.put(`/product/image`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  remove: async (productId: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${productId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductImages;
