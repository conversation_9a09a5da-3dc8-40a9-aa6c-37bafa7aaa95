import HttpStock from '@/api/HttpStock';

const PATH = '/lot';

const apiLot = {
  importLot: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}/confirm-order`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getLotDetail: async (poOrderListId: number, rawMaterialId: number) => {
    try {
      const response = await HttpStock.get(`${PATH}/po-order`, {
        params: {
          poOrderListId,
          rawMaterialId,
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadLotSheet: async (id: string, data: any) => {
    try {
      const response = await HttpStock.put(
        `${PATH}/upload-file-delivery-order?id=${id}`,
        data,
        {
          headers: {
            'content-type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiLot;
