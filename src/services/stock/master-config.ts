import HttpStock from '@/api/HttpStock';

const PATH = '/master-config';

const apiMasterConfig = {
  getProductSubMaterialCheckList: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/product-config`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  getListMasterConfigMaterialByMasterIdConfig: async (masterId: number) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/material/list?masterId=${masterId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  getListMasterConfigMaterial: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/material`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  getMasterConfigComponentList: async (filters?: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/component/list`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  // create: async (data: any) => {
  //   try {
  //     const response = await HttpStock.post(`${PATH}`, data);
  //     return response.data;
  //   } catch (error: any) {
  //     console.log('error', error);
  //     if (!error.response) {
  //       throw error;
  //     }
  //     return {
  //       status: 'rejected',
  //       message: error.response.data.message,
  //       isError: true,
  //     };
  //   }
  // },
  // update: async (data: any) => {
  //   try {
  //     const response = await HttpStock.put(`${PATH}/${data.id}`, data);
  //     return response.data;
  //   } catch (error: any) {
  //     console.log('error', error);
  //     if (!error.response) {
  //       throw error;
  //     }
  //     return {
  //       status: 'rejected',
  //       message: error.response.data.message,
  //       isError: true,
  //     };
  //   }
  // },
  // delete: async (id: number) => {
  //   try {
  //     const response = await HttpStock.delete(`${PATH}/${id}`);
  //     return response.data;
  //   } catch (error: any) {
  //     console.log('error', error);
  //     if (!error.response) {
  //       throw error;
  //     }
  //     return {
  //       status: 'rejected',
  //       message: error.response.data.message,
  //       isError: true,
  //     };
  //   }
  // },
};

export default apiMasterConfig;
