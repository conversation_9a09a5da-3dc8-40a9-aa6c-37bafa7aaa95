import HttpStock from '@/api/HttpStock';

const PATH = '/component-type';

const apiComponentType = {
  getAll: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  getAllByMaster: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list/master`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  getById: async (id: number) => {
    try {
      const response = await HttpStock.get(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  create: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  update: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}/${data.id}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  delete: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
};

export default apiComponentType;
