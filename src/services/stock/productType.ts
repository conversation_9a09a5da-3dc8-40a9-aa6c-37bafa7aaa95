import HttpStock from '@/api/HttpStock';

const PATH = '/product-type';

const apiProductType = {
  checkSlug: async (urlSlug: string) => {
    try {
      const res = await HttpStock.get(`${PATH}/check-url?urlSlug=${urlSlug}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListByCategoryId: async (categoryId: string) => {
    try {
      const response = await HttpStock.get(`${PATH}/category/${categoryId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductType;
