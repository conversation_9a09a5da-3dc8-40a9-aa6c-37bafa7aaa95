import HttpStock from '@/api/HttpStock';

const PATH = '/address';

const apiAddress = {
  getAddressByZipcode: async (value: string) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/zipcode-by-zipcode/${value}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getSubDistrictByDistrictId: async (districtId: number) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/sub-district/${districtId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiAddress;
