import HttpStock from '@/api/HttpStock';

const PATH = '/picking-type';

const apiPickingType = {
  getList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPickingType;
