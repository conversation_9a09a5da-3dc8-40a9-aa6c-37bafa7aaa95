import HttpStock from '@/api/HttpStock';

const PATH = `/product`;

const apiProduct = {
  getListProductConfigByProductId: async (productId: number) => {
    try {
      const res = await HttpStock.get(
        `/product-print-config/order/${productId}`
      );
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  checkProductUrl: async (urlSlug: string) => {
    try {
      const res = await HttpStock.get(`${PATH}/check-url?urlSlug=${urlSlug}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductPrintSystemConfig: async (productId: number) => {
    try {
      const res = await HttpStock.get(`/product-print-config/${productId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductListOrder: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}/list`, { params: filters });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductList: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}`, { params: filters });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductAllList: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}/list`, {
        params: filters,
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductById: async (productId: string | number) => {
    try {
      const res = await HttpStock.get(`${PATH}/${productId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductTemplate: async (data: any) => {
    try {
      const res = await HttpStock.get(`/master/product`, { params: data });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  importProducts: async (data: any) => {
    try {
      const res = await HttpStock.post(`${PATH}/import`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  // Start category zone
  getCategory: async (filters: any) => {
    try {
      const res = await HttpStock.get(`/product-category`, {
        params: filters,
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getCategoryById: async (id: string) => {
    try {
      const res = await HttpStock.get(`/product-category/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getTypeByCategoryId: async (id: string) => {
    try {
      const res = await HttpStock.get(`/product-type/category/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  addCategory: async (data: any) => {
    try {
      const url = `/product-category`;
      const res = await HttpStock.post(url, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCategory: async (data: any) => {
    try {
      const url = `/product-category/${data.id}`;
      const res = await HttpStock.put(url, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  changeStatusCategory: async (id: number, check: boolean) => {
    try {
      const url = `/product-category/status?id=${id}&isActive=${check}`;
      const res = await HttpStock.put(url);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteCategory: async (id: number) => {
    try {
      const res = await HttpStock.delete(`/product-category/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error,
        isError: true,
      };
    }
  },
  // End category zone

  // Start Type zone
  addType: async (data: any) => {
    try {
      const url = `/product-type`;
      const res = await HttpStock.post(url, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  uploadImageProduct: async (data: any) => {
    try {
      const res = await HttpStock.put(`${PATH}/image`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  create: async (data: any) => {
    try {
      const res = await HttpStock.post(`${PATH}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getTypeById: async (id: number) => {
    try {
      const res = await HttpStock.get(`/product-type/${id}?id=${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  update: async (data: any) => {
    try {
      const res = await HttpStock.put(`${PATH}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  updateType: async (data: any) => {
    try {
      const url = `/product-type`;
      const res = await HttpStock.put(`${url}/${data.id}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  remove: async (id: string) => {
    try {
      const res = await HttpStock.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  changeStatusType: async (id: number, check: boolean) => {
    try {
      const url = `/product-type/status?id=${id}&isActive=${check}`;
      const res = await HttpStock.put(url);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  updateStatus: async (productId: string, data: any) => {
    try {
      const res = await HttpStock.put(`${PATH}/status/${productId}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  deleteType: async (id: number) => {
    try {
      const res = await HttpStock.delete(`/product-type/${id}`);

      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProduct;
