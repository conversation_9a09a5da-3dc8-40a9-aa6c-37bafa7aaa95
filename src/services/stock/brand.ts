import HttpStock from '@/api/HttpStock';

const PATH = '/brand';

const apiBrand = {
  getList: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, { params: filters });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListBrand: async (params?: any) => {
    try {
      // const queryString = new URLSearchParams(params).toString();
      const response = await HttpStock.get(`${PATH}/list`, { params });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getBrandById: async (id: string, token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpStock.get(`${PATH}/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createBrand: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateBrand: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteBrand: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiBrand;
