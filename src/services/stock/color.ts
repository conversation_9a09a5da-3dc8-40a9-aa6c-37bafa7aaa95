import HttpStock from '@/api/HttpStock';

const PATH = '/color';

const apiColor = {
  getListColorsByPrintColorIdAndProductId: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/print-color/product`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListColorsBySubMaterialDetailId: async (
    colorSubMaterialDetailId: number
  ) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/sub-material-detail/${colorSubMaterialDetailId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  create: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  update: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  delete: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiColor;
