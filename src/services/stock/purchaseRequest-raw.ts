import HttpStock from '@/api/HttpStock';
import moment from 'moment/moment';

const PATH = '/pr-order-raw';

const apiPurchaseRequestRaw = {
  getPrOrderRawPage: async (
    filters:
      | {
          page: number;
          size: number;
          prOrderRawStatusId: number;
          startDate: string | null;
          endDate: string | null;
          search: string;
        }
      | any
  ) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params: {
          page: filters.page,
          size: filters.size,
          prOrderRawStatusId: filters.prOrderRawStatusId,
          startDate: filters.startDate || '1800-01-01',
          endDate: filters.endDate || moment().format('YYYY-MM-DD'),
          search: filters.search || '',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  getListPrOrderRaw: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, { params: filters });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPurchaseRequestRaw;
