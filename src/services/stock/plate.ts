import HttpStock from '@/api/HttpStock';

const PATH = '/machine-plate-config';

const apiPlate = {
  getPlateByMachineId: async (id: number) => {
    try {
      const response = await HttpStock.get(`${PATH}/machine/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPlate;
