import HttpStock from '@/api/HttpStock';
import { isEmpty } from 'lodash';

const PATH = '/product-extra';

const apiProductExtra = {
  getList: async (productId: string, keyword: string) => {
    try {
      const response = await HttpStock.get(`${PATH}/${productId}`, {
        params: {
          searchName: !isEmpty(keyword) ? keyword : null,
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  create: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  delete: async (productCoatingId: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${productCoatingId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductExtra;
