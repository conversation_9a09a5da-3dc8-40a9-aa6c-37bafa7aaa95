import HttpStock from '@/api/HttpStock';

const PATH = '/peak-product';
const apiProductPeak = {
  getAllPeakProduct: async () => {
    try {
      const res = await HttpStock.get(`${PATH}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createPeakProduct: async (data: { name: string; description?: string }) => {
    try {
      const res = await HttpStock.post(`${PATH}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updatePeakProduct: async (data: any) => {
    try {
      const res = await HttpStock.put(`${PATH}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deletePeakProduct: async (id: number) => {
    try {
      const res = await HttpStock.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductPeak;
