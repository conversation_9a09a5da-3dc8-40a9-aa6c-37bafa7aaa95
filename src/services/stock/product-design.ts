import HttpStock from '@/api/HttpStock';

const apiProductDesign = {
  getList: async () => {
    try {
      const res = await HttpStock.get(`/product-design`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductDesign;
