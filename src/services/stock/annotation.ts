import HttpStock from '@/api/HttpStock';

const PATH = '/annotation';

const apiAnnotation = {
  annotationTypeList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}/type`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  annotationTypeId: async (id: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/type/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  annotationList: async (typeId?: number) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params: { annotationTypeId: typeId },
      });

      return response.data;
    } catch (error: any) {
      console.error('error:', error);

      if (!error.response) {
        throw error;
      }

      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiAnnotation;
