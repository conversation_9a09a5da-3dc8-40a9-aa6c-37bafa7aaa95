import HttpStock from '@/api/HttpStock';

const PATH = '/machine';

const apiMachine = {
  getMachineList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}/list`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getMachineConfigList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}/config`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getMachineModelList: async (machineId: number) => {
    try {
      const response = await HttpStock.get(`${PATH}/model/${machineId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiMachine;
