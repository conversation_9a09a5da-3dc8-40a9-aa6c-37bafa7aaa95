import HttpStock from '@/api/HttpStock';

const PATH = '/raw-material';

const apiRawMaterial = {
  getList: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, { params: filters });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  checkMaster: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/check-master`, { params });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getSubMaterialById: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, { params: filters });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getSubMaterialDetail: async (id: number) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/lay-data?subMaterialDetailId=${id}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getOptions: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, { params: filters });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getRawMaterialById: async (id: any) => {
    try {
      const res = await HttpStock.get(`${PATH}/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createRawMaterial: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateRawMaterial: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  saveImage: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}/saveImage`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteRawMaterial: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  checkRawMaterialNo: async (no: string) => {
    try {
      const res = await HttpStock.get(
        `${PATH}/check-raw-material-no?rawMaterialNo=${no}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getRawMaterialLayData: async (subMaterialDetailId: number) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/lay-data?subMaterialDetailId=${subMaterialDetailId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListRawMatCheckMaster: async (id: number) => {
    try {
      const res = await HttpStock.get(
        `${PATH}/check-master?masterCategoryId=${id}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiRawMaterial;
