import HttpStock from '@/api/HttpStock';

const PATH = '/sub-item-size';

const apiSubItemSize = {
  getSubItemSizeListById: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getSubItemSizeById: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params,
      });
      console.log('response', response);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  addSubItemSize: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await HttpStock.post(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateSubItemSize: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await HttpStock.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteSubItemSizeById: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiSubItemSize;
