import HttpStock from '@/api/HttpStock';

const PATH = '/coating';

const apiCoating = {
  getList: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListForProduct: async (filters: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/product`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getInfo: async (coatingId: string) => {
    try {
      const response = await HttpStock.get(`${PATH}/${coatingId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  create: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  update: async (data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  delete: async (id: number) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiCoating;
