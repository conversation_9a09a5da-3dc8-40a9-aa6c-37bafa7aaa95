import HttpStock from '@/api/HttpStock';
import { PaginationResponse } from '@/types/pageable';
import { PurchaseRequestData } from '@/types/purchase-request';
import moment from 'moment';

const PATH = '/pr-order';

const apiPurchaseRequest = {
  getList: async (
    filters:
      | {
          page: number;
          size: number;
          startDate: string | null;
          endDate: string | null;
          search: string;
          status: number;
        }
      | any
  ) => {
    try {
      const response = await HttpStock.get(`${PATH}/page`, {
        params: {
          page: filters.page,
          size: filters.size,
          startDate: filters.startDate || '1800-01-01',
          endDate: filters.endDate || moment().format('YYYY-MM-DD'),
          search: filters.search || '',
          status: filters.status,
        },
      });
      return response.data as PaginationResponse<PurchaseRequestData>;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  getById: async (id: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/${id}`);
      return response.data.data as PurchaseRequestData;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getStatus: async () => {
    try {
      const response = await HttpStock.get(`${PATH}/status`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPrApproveList: async (id: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/approve-list/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPrOrderById: async (id: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListRoleApprove: async () => {
    try {
      const response = await HttpStock.get(`/role-approval-status`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getListPrOrder: async (params: any) => {
    try {
      const response = await HttpStock.get(`${PATH}/list`, {
        params,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createPR: async (body: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, body);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updatePrOrder: async (id: any, data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}/${id}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  saveDraft: async (id: any, data: any) => {
    try {
      const response = await HttpStock.put(`${PATH}/save-draft/${id}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  cancelPrOrder: async ({
    prOrderId,
    reason,
  }: {
    prOrderId: any;
    reason: { note: string; annotationId: number };
  }) => {
    try {
      const response = await HttpStock.put(
        `${PATH}/cancel/${prOrderId}`,
        reason
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  rejectPrOrder: async ({
    prOrderId,
    reason,
  }: {
    prOrderId: any;
    reason: { note: string; annotationId: number };
  }) => {
    try {
      const response = await HttpStock.put(
        `${PATH}/reject/${prOrderId}`,
        reason
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  approve: async (id: any) => {
    try {
      const response = await HttpStock.put(`${PATH}/approve/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getLogs: async ({ prOrderId }: { prOrderId: number }) => {
    try {
      const response = await HttpStock.get(`/pr-order-log/${prOrderId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  changeStatusToDraft: async (id: number) => {
    try {
      const response = await HttpStock.put(`${PATH}/status/draft/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPurchaseRequest;
