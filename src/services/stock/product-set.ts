import HttpStock from '@/api/HttpStock';

const PATH = `/product-set`;

const apiProductSet = {
  checkProductUrl: async (urlSlug: string) => {
    try {
      const res = await HttpStock.get(`${PATH}/check-url?urlSlug=${urlSlug}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductSetListOrder: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}/list`, { params: filters });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductSetList: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}`, { params: filters });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductBySetId: async (productSetId: string) => {
    try {
      const res = await HttpStock.get(`${PATH}/${productSetId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createProductSet: async (data: any) => {
    try {
      const res = await HttpStock.post(`${PATH}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateProductSet: async (id: number, data: any) => {
    try {
      const res = await HttpStock.put(`${PATH}/${id}`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateStatus: async (id: number, status: any) => {
    try {
      const res = await HttpStock.put(`${PATH}/${id}/status`, status);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  removeProductSet: async (id: string) => {
    try {
      const res = await HttpStock.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductSet;
