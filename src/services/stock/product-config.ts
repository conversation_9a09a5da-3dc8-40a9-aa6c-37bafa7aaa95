import HttpStock from '@/api/HttpStock';

const PATH = '/product-config';

const apiProductConfig = {
  deleteProductPrintConfig: async (id: number) => {
    try {
      const response = await HttpStock.delete(`/product-print-config/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  createProductPrintConfig: async (data: any) => {
    try {
      const response = await HttpStock.post(`/product-print-config`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  createProductConfig: async (data: any) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  deleteProductConfig: async (id: number) => {
    try {
      const res = await HttpStock.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductConfigOrder: async (params: any) => {
    try {
      const res = await HttpStock.get(`/product-config/order`, {
        params,
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductPrintConfigByProductId: async (id: string) => {
    try {
      const res = await HttpStock.get(`/product-print-config/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProductConfigBySubMaterialId: async (filters: any) => {
    try {
      const res = await HttpStock.get(`${PATH}/sub-material`, {
        params: filters,
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  create: async (data: {
    productId: number;
    masterId: number;
    configMaterial: {
      subMaterialDetailId: number;
    }[];
  }) => {
    try {
      const response = await HttpStock.post(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
};

export default apiProductConfig;
