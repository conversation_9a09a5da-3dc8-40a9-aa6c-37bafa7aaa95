import HttpStock from '@/api/HttpStock';

const PATH = '/service-lay';

const apiServiceLay = {
  getServiceLayList: async (params?: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, { params });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getEstimateServiceLayList: async (estimateProductId: number) => {
    try {
      const response = await HttpStock.get(
        `${PATH}/estimate/${estimateProductId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiServiceLay;
