import HttpStock from '@/api/HttpStock';

const PATH = '/product-material';

const apiProductMaterial = {
  getList: async (productId: string) => {
    try {
      const response = await HttpStock.get(`${PATH}/${productId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  import: async (data: any) => {
    try {
      const response = await HttpStock.post(`/product-material`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  delete: async (productId: string) => {
    try {
      const response = await HttpStock.delete(`${PATH}/${productId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProductMaterial;
