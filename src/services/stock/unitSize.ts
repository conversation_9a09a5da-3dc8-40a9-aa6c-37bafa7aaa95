import HttpStock from '@/api/HttpStock';

const PATH = `/unit-size`;

const apiUnitSize = {
  getDimensionList: async () => {
    try {
      const response = await HttpStock.get(`${PATH}/get/dimension`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getList: async (filters?: any) => {
    try {
      const response = await HttpStock.get(`${PATH}`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiUnitSize;
