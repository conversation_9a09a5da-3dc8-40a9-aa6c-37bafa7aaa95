import axios from 'axios';

const apiProxyImage = {
  fetchImageBlob: async (imageUrl: string): Promise<Blob | null> => {
    try {
      const response = await axios.get(
        `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`,
        {
          responseType: 'blob',
        }
      );
      return response.data as Blob;
    } catch (error: any) {
      console.error('Failed to fetch image blob:', error);
      return null;
    }
  },
};

export default apiProxyImage;
