import HttpOrder from '@/api/HttpOrder';

const PATH = '/estimate-product';

const apiEstimateProduct = {
  getEstimateProductById: async (
    estimateProductId: number | string,
    token?: string
  ) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`${PATH}/${estimateProductId}`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  uploadImageExample: async (
    data: any,
    config: any,
    estimateProductId: string
  ) => {
    try {
      const response = await HttpOrder.put(
        `${PATH}/upload-resolution/${estimateProductId}`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadFileExtraUrl: async (
    data: any,
    config: any,
    estimateProductId: number
  ) => {
    try {
      const response = await HttpOrder.put(
        `${PATH}/upload-file-extra/${estimateProductId}`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  deleteImageExample: async (estimateProductResolutionId: number) => {
    try {
      const response = await HttpOrder.delete(
        `${PATH}/delete-resolution/${estimateProductResolutionId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateEstimateProduct: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCalcRemark: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/calc-remark`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  confirmSpec: async (estimateProductId: number) => {
    try {
      const response = await HttpOrder.put(
        `${PATH}/approved/${estimateProductId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getEstimateExtraSizeByEstimateProductExtraId: async (
    estimateProductExtraId: number
  ) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/extra-size/${estimateProductExtraId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  saveEstimateProductExtraSize: async (data: number) => {
    try {
      const response = await HttpOrder.put(`${PATH}/extra-size`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
};

export default apiEstimateProduct;
