import HttpOrder from '@/api/HttpOrder';

const PATH = '/laydata';

const apiLay = {
  getLayList: async (params: any) => {
    try {
      const response = await HttpOrder.get(PATH, {
        params,
      });
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getDataForLay: async (id: any) => {
    try {
      const url = `${PATH}/for-lay/${id}`;
      const response = await HttpOrder.get(url);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },

  getData: async (id: any, token?: any) => {
    try {
      const url = `${PATH}/${id}`;
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};
      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const response = await HttpOrder.get(url, config);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  saveLay: async (data: any) => {
    try {
      const url = `/core/layoutdata`;
      const res = await HttpOrder.post(url, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getComparePricesList: async (id: any) => {
    try {
      const url = `${PATH}/lay-data-document/${id}`;
      const response = await HttpOrder.get(url);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  changeLd: async (id: any) => {
    try {
      const url = `${PATH}/change-spec/${id}`;
      const response = await HttpOrder.put(url);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteLayData: async (id: any) => {
    try {
      const url = `${PATH}/delete-data-order/${id}`;
      const response = await HttpOrder.delete(url);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  addLayData: async (data: any) => {
    try {
      const url = `${PATH}/add-data-order`;
      const response = await HttpOrder.post(url, data);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  checkMatchLayDataForPrintSheet: async (params: any) => {
    try {
      const url = `${PATH}/for-print-plate`;
      const response = await HttpOrder.get(url, {
        params,
      });
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiLay;
