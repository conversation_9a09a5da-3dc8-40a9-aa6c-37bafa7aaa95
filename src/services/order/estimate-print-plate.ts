import HttpOrder from '@/api/HttpOrder';

const PATH = '/estimate-print-plate';

const apiEstimatePrintPlate = {
  getEstimatePrintPlateById: async (estimatePrintPlateId: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${estimatePrintPlateId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateEstimatePrintPlate: async (data: any) => {
    try {
      const res = await HttpOrder.put(PATH, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  approveEstimatePrintPlate: async (estimatePrintPlateId: number) => {
    try {
      const res = await HttpOrder.put(
        `${PATH}/approve/${estimatePrintPlateId}`
      );
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  uploadLayoutFile: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/upload-layout`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadExtraLayout: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/upload-extra-file`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  convertEstimateToStatusPrintPlate: async (estimateProductId: number) => {
    try {
      const res = await HttpOrder.put(`${PATH}/convert/${estimateProductId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
};

export default apiEstimatePrintPlate;
