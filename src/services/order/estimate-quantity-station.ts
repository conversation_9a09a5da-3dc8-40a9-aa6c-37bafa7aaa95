import HttpOrder from '@/api/HttpOrder';

const PATH = '/estimate-quantity-station';

const apiEstimateQuantityStation = {
  getStationByEstimateId: async (estimateId: number) => {
    try {
      const response = await HttpOrder.get(`${PATH}/estimate/${estimateId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  getStationByEstimateProductId: async (estimateProductId: number) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/estimate-product/${estimateProductId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  addQuantity: async (data: any) => {
    try {
      const response = await HttpOrder.post(`${PATH}/add-quantity`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  updateQuantity: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/update-quantity`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  updatePrintSheetAllowance: async (data: any) => {
    try {
      const response = await HttpOrder.put(
        `${PATH}/print-sheet-allowance`,
        data
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  addServiceLay: async (data: any) => {
    try {
      const response = await HttpOrder.post(`${PATH}/add-service-lay`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  deleteEstimateQuantity: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/delete-quantity`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  deleteServiceLay: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/delete-service-lay`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  updateStation: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
};

export default apiEstimateQuantityStation;
