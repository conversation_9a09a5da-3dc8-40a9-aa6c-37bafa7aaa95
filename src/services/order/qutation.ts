import HttpOrder from '@/api/HttpOrder';
import { QuotationUpdateRequest } from '@/types/quotation';
import moment from 'moment/moment';

const PATH = '/payment-quotation';

const apiQuotation = {
  getStatusList: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/status`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getQuotations: async (filters: {
    paymentQuotationStatusId: number;
    page: number;
    size: number;
    startDate: string | null;
    endDate: string | null;
    paymentQuotationNo: string;
    layDataOrderNo: string;
    search: string;
  }) => {
    try {
      const res = await HttpOrder.get(`${PATH}/page`, {
        params: {
          paymentQuotationStatusId: filters.paymentQuotationStatusId,
          page: filters.page,
          size: filters.size,
          startDate: filters.startDate || '1800-01-01',
          endDate: filters.endDate || moment().format('YYYY-MM-DD'),
          paymentQuotationNo: filters.paymentQuotationNo,
          layDataOrderNo: filters.layDataOrderNo,
          search: filters.search,
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getQuotationList: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/list`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getQuotationDetail: async (data: { paymentQuotationId: number }) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${data.paymentQuotationId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createQuotation: async (data: { layDataOrderId: number }) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateQuotation: async (data: QuotationUpdateRequest) => {
    try {
      const res = await HttpOrder.put(`${PATH}/update`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataOrders: async (filters: { search: string }) => {
    try {
      const res = await HttpOrder.get(`/lay-data-order/quotation`, {
        params: filters,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataOrderDetail: async (data: { orderId: number }) => {
    try {
      const res = await HttpOrder.get(`/lay-data-order/${data.orderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateQuotationStatus: async (data: { paymentQuotationId: number }) => {
    try {
      const res = await HttpOrder.put(
        `${PATH}/update/status/${data.paymentQuotationId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  revokeQuotationStatus: async (data: {
    paymentQuotationId: number;
    statusId: number;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/revoke`, {
        paymentQuotationId: data.paymentQuotationId,
        paymentQuotationStatusId: data.statusId,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiQuotation;
