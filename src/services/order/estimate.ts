import HttpOrder from '@/api/HttpOrder';

const PATH = '/estimate';

const apiEstimate = {
  getSalesOrderStatus: async (token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`${PATH}/order/status`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getList: async (filters: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}`, {
        params: filters,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getListPaymentTypeEnum: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/payment-type`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  confirmSpec: async (id: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/confirm/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createSalesOrder: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getSalesOrderById: async (id: any, token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`${PATH}/${id}`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getOrderLD: async (id: string) => {
    try {
      const res = await HttpOrder.get(`${PATH}/get-lay-data/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  changeProduct: async (data: any) => {
    try {
      const res = await HttpOrder.put(
        `${PATH}/change-product/${data.orderId}`,
        data
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  changeCustomer: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/${data.orderId}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  cancelSalesOrder: async (id: number) => {
    try {
      const res = await HttpOrder.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  settingOrder: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/setting`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateRemarkEstimate: async (data: {
    estimateId: number;
    remarkEstimate: string;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/remark-estimate`, {
        estimateId: data.estimateId,
        remarkEstimate: data.remarkEstimate,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiEstimate;
