import HttpOrder from '@/api/HttpOrder';

const PATH = '/payment-quotation';

const apiPaymentQuotation = {
  approve: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/lay-data-order/approve`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  exportQuotation: async (paymentQuotationId: number) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/pdf/${paymentQuotationId}`,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'ใบเสนอราคา.pdf');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { isError: false };
    } catch (error: any) {
      console.log('error', error);
      return {
        status: 'rejected',
        message: 'Download failed',
        isError: true,
      };
    }
  },
};

export default apiPaymentQuotation;
