import HttpOrder from '@/api/HttpOrder';
import HttpStock from '@/api/HttpStock';
import {
  TAddArtwork,
  TAddLayData,
  TAddQuantity,
  TDataUpdateProductOrder,
} from '@/types/prepare-material';

const PATH = '/production-order';

const apiJob = {
  getListProductionOrderStatus: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/status`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getProductionOrder: async (productionId: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${productionId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  addRawMaterialToProductionOrder: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/raw-material`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveProOrderRawMaterial: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/raw-material`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  sendWithdrawal: async (data: { productionOrderId: number }) => {
    try {
      const res = await HttpStock.post(`/pr-order-raw/add`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createProductionOrder: async (data: { layDataId: number }) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getProductionOrderPage: async (params: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}`, { params });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveProductionOrder: async (data: TDataUpdateProductOrder) => {
    try {
      const res = await HttpOrder.put(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createAddQuantity: async (data: TAddQuantity) => {
    try {
      const res = await HttpOrder.post(`${PATH}/add-quantity`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createAddArtwork: async (data: TAddArtwork) => {
    try {
      const res = await HttpOrder.post(`${PATH}/add-artwork`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createAddLayData: async (data: TAddLayData) => {
    try {
      const res = await HttpOrder.post(`${PATH}/add-lay-data`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteLayDataToProductionOrder: async (
    productionOrderGroupLayDataId: number
  ) => {
    try {
      const res = await HttpOrder.delete(
        `${PATH}/lay-data/${productionOrderGroupLayDataId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveUploadLayoutFile: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/upload-layout`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approveProductionOrder: async (productionOrderId: number) => {
    try {
      const res = await HttpOrder.put(`${PATH}/approve/${productionOrderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getProductionOrderStageByProductionOrderId: async (
    productionOrderId: number
  ) => {
    try {
      const res = await HttpOrder.get(
        `/production-order-stage/${productionOrderId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approveProductionOrderQuantity: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/quantity/approve`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteProductionOrderArtwork: async (productionOrderArtworkId: number) => {
    try {
      const res = await HttpOrder.delete(
        `${PATH}/artwork/${productionOrderArtworkId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  rejectProductionOrder: async (data: {
    productionOrderId: number;
    reason: string;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/reject`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteProductionOrderQuantity: async (productionOrderQuantityId: number) => {
    try {
      const res = await HttpOrder.delete(
        `${PATH}/quantity/${productionOrderQuantityId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiJob;
