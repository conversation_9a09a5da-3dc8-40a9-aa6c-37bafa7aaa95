import HttpOrder from '@/api/HttpOrder';

const PATH = '/model-custom-object';

const apiModelCustomObject = {
  getModelCustomObj: async () => {
    try {
      const url = `${PATH}/list`;
      const response = await HttpOrder.get(url);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiModelCustomObject;
