import HttpOrder from '@/api/HttpOrder';

const apiLayData = {
  getLayDataStatus: async () => {
    try {
      const res = await HttpOrder.get(`/laydata/get/status`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getAll: async (filters: any) => {
    try {
      const res = await HttpOrder.get(`/laydata`, { params: filters });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getPaymentType: async () => {
    try {
      const res = await HttpOrder.get(`/lay-data-order/payment-type`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataList: async (filters?: { layDataOrdersId: number } | null) => {
    try {
      const res = await HttpOrder.get(`/laydata/list`, { params: filters });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getInfo: async (id: number | string, token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`/laydata/${id}`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  create: async (contactId: number) => {
    try {
      const res = await HttpOrder.post(`/laydata`, {
        contactId,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  update: async (data: any) => {
    try {
      const res = await HttpOrder.put(`/laydata`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approvedLayData: async (layDataId: number) => {
    try {
      const res = await HttpOrder.put(`/laydata/approved/${layDataId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataPage: async (params: any) => {
    try {
      const res = await HttpOrder.get(`/laydata/page`, { params });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataPayment: async (layDataOrderId: number) => {
    try {
      const res = await HttpOrder.get(`/lay-data-payment/${layDataOrderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getListLayData: async (params: any) => {
    try {
      const res = await HttpOrder.get(`/laydata/list`, { params });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  confirmLayDataSpec: async (layDataId: number) => {
    try {
      const res = await HttpOrder.put(`/laydata/approved/${layDataId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approveLayData: async (layDataId: number) => {
    try {
      const res = await HttpOrder.put(`/laydata/approved/${layDataId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiLayData;
