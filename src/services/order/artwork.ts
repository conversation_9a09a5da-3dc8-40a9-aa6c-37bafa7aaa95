import HttpOrder from '@/api/HttpOrder';

const PATH = '/lay-data-artwork';

const apiArtwork = {
  getArtworkList: async (layDataOrderId: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/order/${layDataOrderId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataArtworkDetail: async (layDataArtworkId: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${layDataArtworkId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getArtworkComment: async (artworkId: number) => {
    try {
      const res = await HttpOrder.get(
        `/artwork-detail/comment-area/${artworkId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createArtworkDetail: async (data: any) => {
    try {
      const res = await HttpOrder.post(`artwork-detail`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approvalArtworkDesign: async (data: any) => {
    try {
      const res = await HttpOrder.post(`artwork-detail/design`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateArtworkDetail: async (data: any, artworkDetailId: number) => {
    try {
      const res = await HttpOrder.put(
        `artwork-detail/${artworkDetailId}`,
        data
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateComment: async (commentId: number, data: any) => {
    try {
      const res = await HttpOrder.put(`/artwork-comments/${commentId}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteArtworkDetail: async (artworkDetailId: number) => {
    try {
      const res = await HttpOrder.delete(`artwork-detail/${artworkDetailId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  approvals: async (data: any) => {
    try {
      const res = await HttpOrder.put(`artwork-detail/approvals`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  convertStatus: async (data: any) => {
    try {
      const res = await HttpOrder.put(`artwork-detail/convert-status`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  productExampleApproval: async (artworkDetailId: number) => {
    try {
      const res = await HttpOrder.put(
        `artwork-detail/product-sample?artworkDetailId=${artworkDetailId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  uploadMediaArtwork: async (data: any, config: any) => {
    try {
      const response = await HttpOrder.post(
        `artwork-detail/media`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createComment: async (data: any) => {
    try {
      const response = await HttpOrder.post(`/artwork-comments`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createCommentImage: async (data: any, config: any) => {
    try {
      const response = await HttpOrder.post(
        `/artwork-comments/upload-image`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCommentImage: async (data: any, config: any, commentId: number) => {
    try {
      const response = await HttpOrder.put(
        `/artwork-comments/image/${commentId}`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteArtworkMedia: async (artworkMediaId: number) => {
    try {
      const response = await HttpOrder.delete(
        `artwork-detail/media/${artworkMediaId}`
      );
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteComment: async (commentId: number) => {
    try {
      const response = await HttpOrder.delete(`/artwork-comments/${commentId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiArtwork;
