import HttpOrder from '@/api/HttpOrder';

const PATH = '/payment-transaction';

const apiPaymentTransaction = {
  createTransaction: async (req: {
    price: number;
    paymentInvoiceId: number;
    paidType: number;
    paymentDate: string;
    paymentTime: string;
    remark: string | null;
    bankCompanyId: number | null;
  }) => {
    try {
      const response = await HttpOrder.post(`${PATH}`, req);
      return response.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadSlip: async (data: any, config: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/upload-slip`, data, config);
      return response.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadPayment: async (data: any, config: any) => {
    try {
      const response = await HttpOrder.put(
        `${PATH}/upload-payment`,
        data,
        config
      );
      return response.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPaymentTransaction;
