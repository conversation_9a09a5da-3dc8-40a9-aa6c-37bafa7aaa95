import HttpOrder from '@/api/HttpOrder';

const PATH = '/lay-data-order';

const apiOrder = {
  getOrderStatus: async (token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`${PATH}/order/status`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getList: async (filters: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}`, {
        params: filters,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  confirmSpec: async (id: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/confirm/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  createOrder: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getOrderById: async (id: any, token?: string) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};

      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await HttpOrder.get(`${PATH}/${id}`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getOrderLD: async (id: string) => {
    try {
      const res = await HttpOrder.get(`${PATH}/get-lay-data/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  changeProduct: async (data: any) => {
    try {
      const res = await HttpOrder.put(
        `${PATH}/change-product/${data.orderId}`,
        data
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  changeCustomer: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/${data.orderId}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  cancelOrder: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/delete/${data.orderId}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  settingOrder: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/setting`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiOrder;
