import HttpOrder from '@/api/HttpOrder';

const PATH = '/cost-calculation';

const apiCostCalculation = {
  getCostByLayDataId: async (layDataQuantityId: number) => {
    try {
      const res = await HttpOrder.get(
        `${PATH}?layDataQuantityId=${layDataQuantityId}`
      );
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getCostServiceChargeByLayDataQuantityId: async (params: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}/service`, {
        params,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getMandatoryCostServiceChargeByLayDataQuantityId: async (params: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}/service/mandatory`, {
        params,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getCostServiceChargeSelectList: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/service`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveCalculateCost: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveCalculateServiceCharge: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/service`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveCalculateMandatoryServiceCharge: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/service/mandatory`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiCostCalculation;
