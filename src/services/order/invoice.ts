import HttpOrder from '@/api/HttpOrder';

const PATH = '/payment-invoice';

const apiInvoice = {
  getInvoiceStatus: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/status`);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getInvoiceList: async (filters: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}/page`, {
        params: filters,
      });
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createInvoice: async (req: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, req);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getById: async (id: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  insertQuotationToPaymentInvoice: async (req: {
    paymentInvoiceId: number;
    paymentQuotationId: number;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/insert-quotation`, {
        ...req,
      });
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateInvoiceStatus: async (id: number) => {
    try {
      const res = await HttpOrder.put(`${PATH}/update/status/${id}`);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateInvoice: async (req: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/update`, req);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateInvoiceAnnotation: async (req: {
    paymentInvoiceId: number;
    annotationId: number;
    annotationRemark: string;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/update-annotation`, {
        paymentInvoiceId: req.paymentInvoiceId,
        annotationId: req.annotationId,
        annotationRemark: req.annotationRemark,
      });
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  reverseInvoice: async (id: number) => {
    try {
      const res = await HttpOrder.put(`${PATH}/reverse/${id}`);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  revokeInvoice: async (req: {
    paymentInvoiceId: number;
    paymentInvoiceStatusId: number;
  }) => {
    try {
      const res = await HttpOrder.put(`${PATH}/revoke`, req);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPaymentReceiptPage: async (params: any) => {
    try {
      const res = await HttpOrder.get(`/payment-receipt/page`, { params });
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPaymentReceiptById: async (id: number) => {
    try {
      const res = await HttpOrder.get(`/payment-receipt/${id}`);
      return res.data;
    } catch (error: any) {
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiInvoice;
