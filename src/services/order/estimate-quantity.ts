import HttpOrder from '@/api/HttpOrder';

const PATH = '/estimate-quantity';

const apiEstimateQuantity = {
  confirmEstimateQuantity: async (data: any) => {
    try {
      const response = await HttpOrder.put(`${PATH}/confirm`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiEstimateQuantity;
