import HttpOrder from '@/api/HttpOrder';

const PATH = '/lay-data-quantity';

const apiLayDataQuantity = {
  createQuantity: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateQuantity: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/quantity`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  confirm: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/confirm`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  saveServiceCost: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/cost/service`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  save: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  updateServiceCost: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/cost/service`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getLayDataQuantityByLayDataQuantityId: async (layDataQuantityId: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${layDataQuantityId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  removeServiceCharge: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/cost/service/delete`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  deleteQuantity: async (id: number) => {
    try {
      const res = await HttpOrder.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiLayDataQuantity;
