import HttpOrder from '@/api/HttpOrder';

const PATH = '/print-plate';

const apiPrintPlate = {
  uploadDimension: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}/upload-dimension`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deletePrintPlate: async (id: any) => {
    try {
      const res = await HttpOrder.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  savePrintPlate: async (data: any) => {
    try {
      const res = await HttpOrder.post(PATH, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  editPrintPlate: async (data: any) => {
    try {
      const res = await HttpOrder.put(PATH, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  approvePrintPlatet: async (printPlateId: number) => {
    try {
      const res = await HttpOrder.put(`${PATH}/approved/${printPlateId}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  uploadLayoutFile: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/upload-layout`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPrintPlateById: async (id: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getPrintPlateByOrderId: async (id: number) => {
    try {
      const res = await HttpOrder.get(`${PATH}/order/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPrintPlate;
