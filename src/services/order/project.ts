import HttpOrder from '@/api/HttpOrder';

const PATH = '/project-group';

const apiProject = {
  getProject: async (params: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}/page`, { params });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getProjectList: async () => {
    try {
      const res = await HttpOrder.get(`${PATH}/list`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createProject: async (data: any) => {
    try {
      const res = await HttpOrder.post(`${PATH}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        ...error.response.data,
        isError: true,
      };
    }
  },
  updateProject: async (data: any) => {
    try {
      const res = await HttpOrder.put(`${PATH}/${data.id}`, data);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteProject: async (id: number) => {
    try {
      const res = await HttpOrder.delete(`${PATH}/${id}`);
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiProject;
