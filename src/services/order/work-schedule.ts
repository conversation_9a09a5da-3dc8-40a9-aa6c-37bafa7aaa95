import HttpOrder from '@/api/HttpOrder';

const PATH = '/production-order';

const apiWorkSchedule = {
  getWorkScheduleDashboard: async (params: any) => {
    try {
      const res = await HttpOrder.get(`${PATH}/work-schedule`, { params });
      return res.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiWorkSchedule;
