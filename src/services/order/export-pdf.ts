import HttpOrder from '@/api/HttpOrder';

const PATH = '/export-pdf';

const apiExportPdf = {
  exportEstimatePrice: async (
    estimateProductId: number,
    selectedQuantity: string
  ) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/generate/${estimateProductId}?selectedQuantity=${selectedQuantity}`,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'ใบเสนอราคา.pdf');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { isError: false };
    } catch (error: any) {
      console.log('error', error);
      return {
        status: 'rejected',
        message: 'Download failed',
        isError: true,
      };
    }
  },
  exportInvoice: async (paymentInvoiceId: number) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/generate/invoice/${paymentInvoiceId}`,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'ใบแจ้งหนี้.pdf');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { isError: false };
    } catch (error: any) {
      console.log('error', error);
      return {
        status: 'rejected',
        message: 'Download failed',
        isError: true,
      };
    }
  },
  exportReceipt: async (paymentReceiptId: number) => {
    try {
      const response = await HttpOrder.get(
        `${PATH}/generate/receipt/${paymentReceiptId}`,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'ใบเสร็จ.pdf');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { isError: false };
    } catch (error: any) {
      console.log('error', error);
      return {
        status: 'rejected',
        message: 'Download failed',
        isError: true,
      };
    }
  },
};

export default apiExportPdf;
