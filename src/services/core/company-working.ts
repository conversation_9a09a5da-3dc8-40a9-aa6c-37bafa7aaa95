import Http from '@/api/Http';

const PATH = '/company-working';

const apiCompanyWorking = {
  updateCompanyWorking: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await Http.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getCompanyWorkingByCompany: async (params: any) => {
    try {
      const url = `${PATH}/get-by-company`;
      const response = await Http.get(url, { params });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiCompanyWorking;
