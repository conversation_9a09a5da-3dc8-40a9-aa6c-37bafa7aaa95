import Http from '@/api/Http';

const PATH = '/permissions';

const apiPermission = {
  getData: async (params: any) => {
    try {
      const response = await Http.get(PATH, {
        params,
      });
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiPermission;
