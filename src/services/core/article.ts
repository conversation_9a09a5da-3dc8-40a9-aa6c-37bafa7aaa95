import Http from '@/api/Http';

const apiArticle = {
  getAll: async (filters: any) => {
    try {
      const res = await Http.get(`/core/article/list`, { params: filters });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  findOne: async (id: string) => {
    try {
      const res = await Http.get(`/core/article/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  create: async (data: any) => {
    try {
      const res = await Http.post(`/core/article`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  update: async (id: string, data: any) => {
    try {
      const res = await Http.put(`/core/article`, {
        ...data,
        id,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  remove: async (id: string) => {
    try {
      const res = await Http.delete(`/core/article/${id}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiArticle;
