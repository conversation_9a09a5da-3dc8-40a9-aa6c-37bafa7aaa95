import Http from '@/api/Http';

const apiArticleImages = {
  getAll: async (filters: any) => {
    try {
      const res = await Http.get(`/core/article-image/list`, {
        params: filters,
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  create: async (data: any) => {
    try {
      const res = await Http.post(`/core/article-image`, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  remove: async (imageId: string) => {
    try {
      const res = await Http.delete(`/core/article-image/${imageId}`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiArticleImages;
