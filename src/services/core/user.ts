import Http from '@/api/Http';

import { LoginWithPasswordType } from '@/types/auth';
import HttpAuth from '@/api/HttpAuth';

// const PATH = '/core/user';

const apiUser = {
  getProfile: async (token?: any) => {
    try {
      const config: {
        headers?: {
          Authorization: string;
          // 'Content-Type': string
        };
      } = {};
      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }
      const res = await Http.get(`/user/me`, config);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  getUser: async (filters: any) => {
    try {
      const response = await Http.get(`/user`, {
        params: filters,
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data.message,
        isError: true,
      };
    }
  },
  login: async (data: LoginWithPasswordType) => {
    try {
      const res = await Http.post(`/user/login`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  register: async (data: LoginWithPasswordType) => {
    try {
      const res = await Http.post(`/user/register`, data);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
  logout: async () => {
    try {
      const res = await HttpAuth.get(`/logout`);
      return res.data;
    } catch (error) {
      return {
        isError: true,
        error,
      };
    }
  },
};

export default apiUser;
