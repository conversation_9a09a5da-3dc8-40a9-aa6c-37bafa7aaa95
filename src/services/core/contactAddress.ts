import Http from '@/api/Http';

const PATH = '/core/contact-address';

const apiContactAddress = {
  getAddressByCustomerId: async (customerId: number) => {
    try {
      const url = `${PATH}/contact/${customerId}`;
      const response = await Http.get(url);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  createShippingAddressByContactId: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await Http.post(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateShippingAddressByContactId: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await Http.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteAddress: async (id: any) => {
    try {
      const url = `${PATH}`;
      const response = await Http.delete(`${url}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiContactAddress;
