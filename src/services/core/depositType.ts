import Http from '@/api/Http';

const PATH = 'core/deposit-type';

const apiDeposit = {
  getDepositType: async () => {
    try {
      const response = await Http.get(`${PATH}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiDeposit;
