import Http from '@/api/Http';

const PATH = '/company';

const apiCompany = {
  getMeCompanyList: async () => {
    try {
      const response = await Http.get(`${PATH}/user`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getMeCompanyById: async (id: number) => {
    try {
      const response = await Http.get(`${PATH}/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  addCompany: async (data: any) => {
    try {
      const response = await Http.post(PATH, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  switchCompany: async (id: number) => {
    try {
      const response = await Http.put(`${PATH}/default/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCompany: async (data: any) => {
    try {
      const url = `${PATH}`;
      const response = await Http.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCompanyLogo: async (data: any) => {
    try {
      const url = `${PATH}/logo`;
      const response = await Http.put(url, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getMemberCompany: async (params: any) => {
    try {
      const response = await Http.get(`${PATH}/member`, {
        params,
      });
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  inviteMemberCompany: async (data: any) => {
    try {
      const response = await Http.post(`${PATH}/invite-user`, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  inviteMemberCompanyAgain: async (companyUserId: number) => {
    try {
      const response = await Http.post(`${PATH}/invite-user/${companyUserId}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteMemberCompany: async (id: number) => {
    try {
      const response = await Http.delete(`${PATH}/user/${id}`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getMemberById: async (companyUserId: any) => {
    try {
      const response = await Http.get(`${PATH}/user/${companyUserId}`);
      return response.data.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateMemberCompany: async (data: any) => {
    try {
      const url = `${PATH}/update-user`;
      const response = await Http.put(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  acceptInviteCompany: async (data: any) => {
    try {
      const url = `${PATH}/accept-invite`;
      const response = await Http.post(url, data);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  getCompanySignature: async () => {
    try {
      const response = await Http.get(`${PATH}/signature-image`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  updateCompanySignature: async (data: any) => {
    try {
      const url = `${PATH}/signature-image`;
      const response = await Http.put(url, data, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
  deleteCompanySignature: async () => {
    try {
      const response = await Http.delete(`${PATH}/signature-image`);
      return response.data;
    } catch (error: any) {
      console.log('error', error);
      if (!error.response) {
        throw error;
      }
      return {
        status: 'rejected',
        message: error.response.data,
        isError: true,
      };
    }
  },
};

export default apiCompany;
