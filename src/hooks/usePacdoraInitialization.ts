import { useEffect, useState } from 'react';

// Custom hook to initialize Pacdor<PERSON> and create a scene
function usePacdoraInitialization(scriptLoaded: boolean, templateId: number) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!scriptLoaded) return;

    const initializePacdora = async () => {
      try {
        await (window as any).Pacdora.init({
          userId: '1dcfacd274cf4',
          appId: '16b560465593ccbf',
          isDelay: true,
          theme: '#000000',
          localeResource: {
            'Upload & Design': 'Save and try',
          },
        });

        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize Pacdora:', error);
      }
    };

    initializePacdora();
  }, [scriptLoaded, templateId]);

  return isInitialized;
}

export default usePacdoraInitialization;
