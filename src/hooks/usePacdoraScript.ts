import { useEffect, useState } from 'react';

// Custom hook to load the Pacdora script
function usePacdoraScript() {
  const [scriptLoaded, setScriptLoaded] = useState(false);

  useEffect(() => {
    if ((window as any).Pacdora) {
      setScriptLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.pacdora.com/Pacdora-v1.1.8.js';
    script.async = true;

    script.onload = () => {
      setScriptLoaded(true);
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return scriptLoaded;
}

export default usePacdoraScript;
