import { useCallback, useEffect, useRef } from 'react';

const useSyncRowHeights = () => {
  const rowsRef = useRef<Map<number, HTMLElement[]>>(new Map());
  const observerRef = useRef<ResizeObserver | null>(null);

  const sync = useCallback(() => {
    rowsRef.current.forEach((cells) => {
      // reset
      cells.forEach((el) => (el.style.height = 'auto'));
      const max = Math.max(...cells.map((el) => el.offsetHeight));
      cells.forEach((el) => {
        if (el.style.height !== `${max}px`) el.style.height = `${max}px`;
      });
    });
  }, []);

  useEffect(() => {
    observerRef.current = new ResizeObserver(() => {
      try {
        sync();
      } catch (err) {
        // กัน error หลุดไป SES
        console.error('resize-sync error', err);
      }
    });
    window.addEventListener('resize', sync);
    return () => {
      observerRef.current?.disconnect();
      window.removeEventListener('resize', sync);
    };
  }, [sync]);

  const register = (row: number) => (el: HTMLElement | null) => {
    if (!el) return;

    if (!rowsRef.current.has(row)) rowsRef.current.set(row, []);
    const arr = rowsRef.current.get(row)!;
    if (!arr.includes(el)) arr.push(el);

    observerRef.current!.observe(el);
    sync();
  };

  return { register };
};
export default useSyncRowHeights;
