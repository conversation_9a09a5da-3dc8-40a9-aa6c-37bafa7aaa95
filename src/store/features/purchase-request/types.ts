export type PurchaseRequestState = {
  purchaseReq: PurchaseReqType;
  materialFormList: any[];
  pending: boolean;
  error: boolean;
};

export type PurchaseReqType = {
  status: number;
  detail: PurchaseRequestDetailType | null;
};

export enum PurchaseReqStatus {
  DRAFT = 0,
  WAITING = 1,
  APPROVED = 2,
  REJECT = 3,
  CANCEL = 4,
}

// const mockPurchaseReqStatus = [
//   {
//     value: 0,
//     name: 'DRAFT', // แบบร่าง
//   },
//   {
//     value: 1,
//     name: 'WAITING', // รออนุมัติใบขอซื้อ
//   },
//   {
//     value: 2,
//     name: 'APPROVED', // อนุมัติสำเร็จ
//   },
//   {
//     value: 3,
//     name: 'REJECTED', // ไม่อนุมัติ
//   },
//   {
//     value: 4,
//     name: 'CANCELED', // ยกเลิก
//   },
// ];

export type PurchaseReqBody = {
  jobRef: number;
  isNotify: boolean;
  note: string;
  contactId: number;
  prOrderList: {
    rawMaterialId: number;
    brandId: number;
    amount: number;
    prOrderRawId: number | null;
    isJobDefault: boolean;
  }[];
  prOrderApproval: {
    approveUserId: number;
    roleApprovalStatusId: number;
    sort: number;
  }[];
};

export interface PurchaseRequestDetailType {
  id: number;
  prOrderNo: string;
  user: User;
  prOrderLists: PrOrderList[];
  prOrderApprovals: PrOrderApproval[];
  deletedReason: string | null;
  annotation: string | null;
  status: number;
  statusEnum: string;
  note: string | null;
  requestedDeliveryDate: Date | null;
  contact: Contact;
  isDeleted: boolean;
  createdDate: number;
  modifiedDate: number;
  poOrderNo: string | null;
  isNotify: boolean;
}

export interface User {
  id: number;
  name: string;
  imageUrl: string | null;
}

export interface PrOrderList {
  id: number;
  name: string;
  rawMaterial: {
    id: number;
    rawMaterialNo: string;
    name: string;
    description: string;
    material: {
      id: number;
      name: string;
      countSubMaterial: number;
      dimensionConfig: {
        id: number;
        configName: string;
        dimension: {
          id: number;
          name: string;
          imageUrl: string;
        }[];
        materialSizeDimension: {
          id: number;
          name: string;
        }[];
      };
      materialType: {
        id: number;
        name: string;
      };
    };
    subMaterial: {
      id: number;
      name: string;
      imageUrl: string;
      countSubMaterialDetail: number;
    };
    subMaterialDetail: {
      id: number;
      name: string;
      side: number;
      subMaterialId: number;
      subMaterialImageUrl: string;
    };
    pickingType: {
      id: number;
      name: string;
    };
    imageUrl: null;
    isPurchase: true;
    isSell: true;
    isSerialNumber: true;
    isLotExpirationDate: true;
    itemSize: {
      id: number;
      name: string;
      subItemSizeDto: {
        id: number;
        itemSizeId: number;
        itemSizeName: string;
        cut: number;
        result: number;
        materialSizeRequest: {
          value: number;
          materialSizeDimensionId: number;
          materialSizeDimension: {
            id: number;
            name: string;
            imageUrl: string;
          };
        }[];
        dimensionConfigName: string;
        dimension: {
          id: number;
          name: string;
          imageUrl: string;
        };
      }[];
    };
    countDimension: {
      id: number;
      name: string;
      imageUrl: string;
    };
    rawPrice: number;
  };
  brand: {
    id: number;
    name: string;
    description: string;
    imageUrl: string;
  };
  prOrderRawId: number;
  jobNo: string;
  amount: number;
  isJobDefault: boolean;
  minAmount: number;
}

export interface PrOrderApproval {
  id: number;
  userApprove: {
    id: number;
    name: string;
    imageUrl: string;
    email: string;
  };
  role: {
    id: number;
    name: string;
    type: string;
    sort: number;
  };
  status: string;
  createdDate: any;
  modifiedDate: any;
}

export interface Contact {
  id: number;
  code: string;
  taxNumber: string;
  taxAddress: string;
  province: Province;
  district: District;
  subDistrict: SubDistrict;
  zipcode: string;
  name: string;
  email: string;
  phoneNumber: string;
  contactType: ContactType;
  imageUrl: string | null;
  creditType: CreditType;
}

export interface Province {
  id: number;
  geoId: number;
  provinceCode: string;
  name: string;
}

export interface District {
  id: number;
  geoId: number;
  districtCode: string;
  name: string;
}

export interface SubDistrict {
  id: number;
  geoId: number;
  subDistrictCode: string;
  name: string;
}

export interface ContactType {
  id: number;
  name: string;
}

export interface CreditType {
  id: number;
  day: number;
}
