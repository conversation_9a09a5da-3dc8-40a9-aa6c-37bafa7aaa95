import { createReducer } from '@reduxjs/toolkit';
import { setMaterialListForm, setPurchaseRequest } from './actions';
import { RootState } from '@/store';
import {
  PurchaseReqStatus,
  PurchaseRequestState,
} from '@/store/features/purchase-request/types';

const initialState: PurchaseRequestState = {
  purchaseReq: {
    status: PurchaseReqStatus.DRAFT,
    detail: null,
  },
  materialFormList: [],
  pending: false,
  error: false,
};

export const purchaseRequestReducer = createReducer(initialState, (builder) => {
  builder.addCase(setMaterialListForm, (state, action) => {
    state.materialFormList = action.payload;
  });
  builder.addCase(setPurchaseRequest, (state, action) => {
    state.purchaseReq = action.payload;
  });
});
export const purchaseRequestSelector = (state: RootState) =>
  state.purchaseRequest;
