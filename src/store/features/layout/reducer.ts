import { createReducer } from '@reduxjs/toolkit';
import { setPlaySwitchAnimation, setShowAside } from './actions';
import { RootState } from '@/store';

type LayoutState = {
  showAside: boolean;
  playSwitchAnimation: boolean;
};

const initialState: LayoutState = {
  showAside: true,
  playSwitchAnimation: false,
};

export const layoutReducer = createReducer(initialState, (builder) => {
  builder.addCase(setShowAside, (state, action) => {
    state.showAside = action.payload;
  });
  builder.addCase(setPlaySwitchAnimation, (state, action) => {
    state.playSwitchAnimation = action.payload;
  });
});
export const showAsideSelector = (state: RootState) => state.layout;
