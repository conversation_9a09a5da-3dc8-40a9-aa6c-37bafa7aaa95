import { QuotationDetailType } from '@/types/quotation';

export type QuotationState = {
  quotation: QuotationType;
  pending: boolean;
  error: boolean;
};

export type QuotationType = {
  status: number;
  quotationNo: string;
  detail: QuotationDetailType | null;
};

export enum QuotationStatus {
  DRAFT = 1,
  PENDING = 2,
  WAITING = 3,
  APPROVED = 4,
  REJECT = 5,
  EXPIRED = 6,
  CANCEL = 7,
}

// const mockQuotationStatus = [
//   { id: 1, value: 'draft', name: 'แบบร่าง', count: 2 },
//   { id: 2, value: 'pending', name: 'รออนุมัติ', count: 1 },
//   { id: 3, value: 'waiting', name: 'รอตอบรับ', count: 1 },
//   { id: 4, value: 'approved', name: 'ตอบรับแล้ว', count: 1 },
//   { id: 5, value: 'reject', name: 'ปฏิเสธ', count: 1 },
//   { id: 6, value: 'expired', name: 'พ้นกำหนด', count: 1 },
//   { id: 7, value: 'cancel', name: 'ยกเลิก', count: 1 },
// ];
