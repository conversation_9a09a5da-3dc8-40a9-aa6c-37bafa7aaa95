import { createReducer } from '@reduxjs/toolkit';
import { setQuotation } from './actions';
import { RootState } from '@/store';
import {
  QuotationState,
  QuotationStatus,
} from '@/store/features/quotation/types';

const initialState: QuotationState = {
  quotation: {
    status: QuotationStatus.DRAFT,
    quotationNo: '',
    detail: null,
  },
  pending: false,
  error: false,
};

export const quotationReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(setQuotation.pending, (state) => {
      state.pending = true;
    })
    .addCase(setQuotation.fulfilled, (state, { payload }) => {
      state.pending = false;
      state.quotation = payload;
    })
    .addCase(setQuotation.rejected, (state) => {
      state.pending = false;
      state.error = true;
    });
});

export const quotationSelector = (state: RootState) => state.quotation;
export default quotationReducer;
