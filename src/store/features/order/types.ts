export type OrderStateType = {
  orderById: Partial<OrderType>;
  layDataOrder: Partial<LayDataType>;
};

export type OrderType = {
  id: number;
  layDataOrderNo: string;
  contact: {
    id: number;
    name: string;
    imageUrl: string | null;
    phoneNumber: string;
    email: string;
    customerType: string;
    taxNumber: string;
    isCredit: boolean;
    creditType: {
      id: number;
      day: number;
    };
  };
  createdDate: number;
  createUser: {
    id: number;
    name: string;
    imageUrl: string | null;
    userTypeName: string;
  };
  layDataOrderStatus: {
    id: number;
    name: string;
  };
  creditDay: number;
  credit: number;
  expireDay: number;
  deliveryDate: number;
  totalPriceOrder: number;
  paidAmountOrder: number;
  layData: {
    id: number;
    layDataStatus: {
      id: number;
      name: string;
    };
    isSpec: boolean;
    isConfirm: boolean;
    ldCode: string;
    productModel: {
      productModelId: number;
      productModelName: string;
      imageUrl: string;
      productId: number;
      productName: string;
    };
    width: number;
    length: number;
    height: number;
    layDataQuantity: any[];
    materialMasterId: number | null;
    subMaterialDetail: any | null;
    productParts: any[];
    printingRequest: {
      printingSideDimension: any | null;
      printSystem: any | null;
      colorFront: any[];
      colorBack: any[];
    };
    coatingRequest: {
      coatingSideDimension: any | null;
      finishFront: any[];
      finishBack: any[];
    };
    extra: any[];
    productDesign: any | null;
    masterExample: any[];
    serviceLay: any[];
    note: string | null;
    printPlate: any | null;
  }[];
};

type LayDataStatusType = {
  id: number;
  name: string;
};

type ProductModelType = {
  productModelId: number;
  productModelName: string;
  imageUrl: string;
  productId: number;
  productName: string;
};

type PrintingRequestType = {
  printingSideDimension: any;
  printSystem: any;
  colorFront: any[];
  colorBack: any[];
};

type CoatingRequestType = {
  coatingSideDimension: any;
  finishFront: any[];
  finishBack: any[];
};

export type LayDataType = {
  id: number;
  layDataStatus: LayDataStatusType;
  isSpec: boolean;
  isConfirm: boolean;
  ldCode: string;
  productModel: ProductModelType;
  width: number;
  length: number;
  height: number;
  layDataQuantity: any[];
  materialMasterId: number | null;
  subMaterialDetail: any | null;
  productParts: any[];
  printingRequest: PrintingRequestType;
  coatingRequest: CoatingRequestType;
  extra: any[];
  productDesign: any | null;
  masterExample: any[];
  serviceLay: any[];
  note: string | null;
  printPlate: any | null;
};
