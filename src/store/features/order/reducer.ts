import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { setLayDataOrder, setOrderById } from '@/store/features/order/actions';
import { OrderStateType } from '@/store/features/order/types';

const initialState: OrderStateType = {
  orderById: {},
  layDataOrder: {},
};

export const orderReducer = createReducer(initialState, (builder) => {
  builder.addCase(setOrderById, (state, action) => {
    state.orderById = action.payload;
  });
  builder.addCase(setLayDataOrder, (state, action) => {
    state.layDataOrder = action.payload;
  });
});

export const orderSelector = (state: RootState) => state.order;
export default orderReducer;
