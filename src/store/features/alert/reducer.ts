import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { AlertState } from '@/store/features/alert/types';
import { setSnackBar } from '@/store/features/alert/actions';

const initialState: AlertState = {
  snackBar: {
    status: false,
    text: '',
    severity: 'success',
  },
};

export const alertReducer = createReducer(initialState, (builder) => {
  builder.addCase(setSnackBar, (state, action) => {
    state.snackBar = action.payload;
  });
});

export const alertSelector = (state: RootState) => state.appAlert;
export default alertReducer;
