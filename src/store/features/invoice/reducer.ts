import { InvoiceState } from '@/store/features/invoice/types';
import { createReducer } from '@reduxjs/toolkit';
import { getInvoice, setChangeInvoice } from './actions';
import { RootState } from '@/store';

const initialState: InvoiceState = {
  invoice: null,
  paymentQuotationNo: null,
  pending: false,
  error: false,
};

export const invoiceReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(getInvoice.pending, (state) => {
      state.pending = true;
    })
    .addCase(getInvoice.fulfilled, (state, { payload }) => {
      state.pending = false;
      state.invoice = payload;
    })
    .addCase(getInvoice.rejected, (state) => {
      state.pending = false;
      state.error = true;
    })
    .addCase(setChangeInvoice.fulfilled, (state, { payload }) => {
      state.invoice = payload;
    });
});

export const invoiceSelector = (state: RootState) => state.invoice;
export default invoiceReducer;
