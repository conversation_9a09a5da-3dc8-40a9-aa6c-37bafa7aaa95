import { createAsyncThunk } from '@reduxjs/toolkit';
import apiInvoice from '@/services/order/invoice';
import { InvoiceItemType, InvoiceType } from '@/store/features/invoice/types';
import { isEmpty } from 'lodash';

export const getInvoice = createAsyncThunk(
  'invoice/getInvoice',
  async (id: number) => {
    const res: any = await apiInvoice.getById(id);
    if (!res.isError) {
      const invoice = res.data;
      const { invoiceItem, transaction } = invoice;
      if (!isEmpty(invoiceItem)) {
        invoice.invoiceItem = invoiceItem.map(
          (item: InvoiceItemType, index: number) => {
            return {
              ...item,
              index: index + 1,
            };
          }
        );
      }

      if (!isEmpty(transaction)) {
        invoice.transaction = transaction.map((item: any, index: number) => {
          return {
            ...item,
            index: index + 1,
          };
        });
      }

      return invoice;
    }
    return res.isError;
  }
);

export const setChangeInvoice = createAsyncThunk(
  'invoice/setChangeInvoice',
  async (invoice: InvoiceType) => invoice
);
