export type InvoiceState = {
  invoice: InvoiceType | null;
  paymentQuotationNo: string | null;
  pending: boolean;
  error: boolean;
};
export type InvoiceType = {
  id: number;
  invoiceNo: string;
  paymentQuotationNo: string | null;
  layOrderNo: string | null;
  productQuantity: number;
  createdDate: string;
  dueDate: string;
  totalPrice: number;
  netPrice: number;
  discount: number;
  discountRate: number;
  vatRate: number;
  vatAmount: number;
  paidPayment: number;
  createUser: InvoiceCreateUserType;
  customer: InvoiceCustomerType;
  invoiceItem: InvoiceItemType[];
  invoicesStatus: InvoiceStatusType;
  transaction: [];
};
export type InvoiceCreateUserType = {
  id: number;
  name: string;
  imageUrl: string | null;
  userTypeName: string;
};
export type InvoiceCustomerType = {
  id: number;
  name: string;
  imageUrl: string | null;
  code: string;
  taxNumber: string;
  contactType: InvoiceContactType;
  creditType: InvoiceCreditType;
  email: string;
  phoneNumber: string;
  taxAddress: string;
  zipcode: string;
  subDistrict: SubDistrictType;
  district: DistrictType;
  province: PrivinceType;
};
export type InvoiceContactType = {
  id: number;
  name: string;
};
export type InvoiceCreditType = {
  id: number;
  day: number;
};
export type InvoiceItemType = {
  index?: number;
  id: number;
  layDataNo: string;
  name: string;
  description: string;
  quantity: number;
  price: number;
  priceUnit: number;
  discount: number;
  vatType: number;
  peakId: string;
};
export type InvoiceStatusType = {
  id: number;
  name: string;
  sort?: number;
  count?: number;
};
export type SubDistrictType = {
  id: number;
  geoId: number;
  subDistrictCode: string;
  name: string;
};
export type DistrictType = {
  id: number;
  geoId: number;
  districtCode: string;
  name: string;
};
export type PrivinceType = {
  id: number;
  geoId: number;
  provinceCode: string;
  name: string;
};
