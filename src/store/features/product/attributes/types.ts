type OptionTypeType = {
  id: number;
  optionsTypeId: number;
  optionsType: {
    id: number;
    name: string;
    uuid: string;
  };
};

type OptionsCategoryType = {
  id: number;
  name: string;
  optionType: OptionTypeType[];
};

type OptionsType = {
  id: number;
  name: string;
  optionsCostType: {
    id: number;
    name: string;
    description: string | null;
  };
  optionsCategory: OptionsCategoryType;
};

type SubMaterialDetailType = {
  id: number;
  name: string;
  side: number | null;
  subMaterialId: number;
  options: OptionsType;
};

type ConfigMaterialType = {
  id: number;
  subMaterialDetail: SubMaterialDetailType;
};

type MasterType = {
  id: number;
  name: string;
  imageUrl: string | null;
};

type ProductConfigType = {
  id: number;
  masterCategoryId: number;
  master: MasterType;
  configMaterial: ConfigMaterialType[];
};

export type BaseMaterialType = {
  id: number;
  name: string;
  sort: number;
  productConfig: ProductConfigType[];
};

export type ProductAttributesType = {
  productAttributes: Partial<BaseMaterialType>[];
};
