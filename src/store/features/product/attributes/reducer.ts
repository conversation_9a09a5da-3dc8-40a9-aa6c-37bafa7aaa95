import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { ProductAttributesType } from '@/store/features/product/attributes/types';
import { setProductAttributes } from '@/store/features/product/attributes/actions';

const initialState: ProductAttributesType = {
  productAttributes: [],
};

export const productAttributesReducer = createReducer(
  initialState,
  (builder) => {
    builder.addCase(setProductAttributes, (state, action) => {
      state.productAttributes = action.payload;
    });
  }
);

export const productAttributesSelector = (state: RootState) =>
  state.productAttributes;

export default productAttributesReducer;
