import { createReducer } from '@reduxjs/toolkit';
import { setScrollY } from './actions';
import { RootState } from '@/store';

type JobStateType = {
  layout: {
    scrollY: number;
  };
};

const initialState: JobStateType = {
  layout: {
    scrollY: 0,
  },
};

export const jobReducer = createReducer(initialState, (builder) => {
  builder.addCase(setScrollY, (state, action) => {
    state.layout.scrollY = action.payload;
  });
});
export const jobSelector = (state: RootState) => state.jobData;
