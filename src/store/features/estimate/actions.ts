import { SalesOrderType } from '@/store/features/estimate/types';
import { createAction } from '@reduxjs/toolkit';

export const setSalesOrderById = createAction<SalesOrderType>(
  'estimate/setSalesOrderById'
);
export const setEstimateDataSalesOrder = createAction<any>(
  'estimate/setEstimateDataSalesOrder'
);
export const setEstimateQuantityStation = createAction<any>(
  'estimate/setEstimateQuantityStation'
);
