import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { SalesOrderStateType } from '@/store/features/estimate/types';
import {
  setEstimateDataSalesOrder,
  setEstimateQuantityStation,
  setSalesOrderById,
} from '@/store/features/estimate/actions';

export const initialState: SalesOrderStateType = {
  salesOrderById: {},
  estimateDataSalesOrder: {},
  estimateQuantityStation: {},
};

export const salesOrderReducer = createReducer(initialState, (builder) => {
  builder.addCase(setSalesOrderById, (state, action) => {
    state.salesOrderById = action.payload;
  });
  builder.addCase(setEstimateDataSalesOrder, (state, action) => {
    state.estimateDataSalesOrder = action.payload;
  });
  builder.addCase(setEstimateQuantityStation, (state, action) => {
    state.estimateQuantityStation = action.payload;
  });
});

export const salesOrderSelector = (state: RootState) => state.salesOrder;
export default salesOrderReducer;
