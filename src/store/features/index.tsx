import { combineReducers } from '@reduxjs/toolkit';
import { userReducer } from '@/store/features/user';
import { layoutReducer } from '@/store/features/layout';
import { alertReducer } from '@/store/features/alert';
import { settingCostReducer } from '@/store/features/selling-price/setting-cost';
import { productAttributesReducer } from '@/store/features/product/attributes';
import { jobReducer } from '@/store/features/job';
import { purchaseRequestReducer } from '@/store/features/purchase-request';
import { orderReducer } from '@/store/features/order';
import { quotationReducer } from '@/store/features/quotation/reducer';
import { invoiceReducer } from '@/store/features/invoice/reducer';
import { permissionReducer } from '@/store/features/permission/reducer';
import { salesOrderReducer } from '@/store/features/estimate';

const rootReducer = combineReducers({
  user: userReducer,
  layout: layoutReducer,
  appAlert: alertReducer,
  settingCost: settingCostReducer,
  productAttributes: productAttributesReducer,
  purchaseRequest: purchaseRequestReducer,
  jobData: jobReducer,
  salesOrder: salesOrderReducer,
  order: orderReducer,
  quotation: quotationReducer,
  invoice: invoiceReducer,
  permission: permissionReducer,
});

export default rootReducer;
