import { createReducer } from '@reduxjs/toolkit';
import { getUserProfile } from './actions';
import { RootState } from '@/store';
import { UserState } from '@/store/features/user/types';

const initialState: UserState = {
  user: {
    id: 0,
    name: 'string',
    email: '',
    imageUrl: null,
    userType: {
      id: 0,
      name: '',
      isAdmin: false,
      isAnonymous: false,
      isOwner: false,
      isSuperAdmin: false,
      isUser: false,
    },
    permissions: [],
    company: {
      id: 0,
      logo: null,
      name: '',
      roleId: 0,
      roleName: '',
    },
  },
  pending: false,
  error: false,
};

export const userReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(getUserProfile.pending, (state) => {
      state.pending = true;
    })
    .addCase(getUserProfile.fulfilled, (state, { payload }) => {
      state.pending = false;
      state.user = payload;
    })
    .addCase(getUserProfile.rejected, (state) => {
      state.pending = false;
      state.error = true;
    });
});

export const userSelector = (state: RootState) => state.user;
export default userReducer;
