export type UserState = {
  user: UserDetailState;
  pending: boolean;
  error: boolean;
};

export type UserTypeState = {
  id: number;
  name: string;
  isAdmin: boolean;
  isAnonymous: boolean;
  isOwner: boolean;
  isSuperAdmin: boolean;
  isUser: boolean;
};

export type CompanyDetailState = {
  id: number;
  logo?: null;
  name: string;
  roleId: number;
  roleName: string;
};

export type UserDetailState = {
  id: number;
  name: string;
  email: string;
  imageUrl?: null;
  userType: UserTypeState;
  permissions: string[];
  company: CompanyDetailState;
};
