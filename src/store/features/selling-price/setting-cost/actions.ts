import { createAction } from '@reduxjs/toolkit';
import {
  CalculateCostValuesState,
  CalculateMandatoryServiceChargeValuesState,
  CalculateServiceChargeValuesState,
  CostDataState,
  CostMandatoryServiceChargeDataState,
  CostServiceChargeDataState,
} from '@/store/features/selling-price/setting-cost/types';

export const setCostData = createAction<Partial<CostDataState>[]>(
  'selling-price/setting-cost/setCostData'
);
export const setCostServiceChargeData = createAction<
  Partial<CostServiceChargeDataState>[]
>('selling-price/setting-cost/setCostServiceChargeData');

export const setMandatoryCostServiceChargeData = createAction<
  Partial<CostMandatoryServiceChargeDataState>[]
>('selling-price/setting-cost/setMandatoryCostServiceChargeData');

export const setCalculateCostValues = createAction<CalculateCostValuesState>(
  'selling-price/setting-cost/setCalculateCostValues'
);
export const setCalculateServiceChargeValues =
  createAction<CalculateServiceChargeValuesState>(
    'selling-price/setting-cost/setCalculateServiceChargeValues'
  );
export const setCalculateMandatoryServiceChargeValues =
  createAction<CalculateMandatoryServiceChargeValuesState>(
    'selling-price/setting-cost/setCalculateMandatoryServiceChargeValues'
  );
