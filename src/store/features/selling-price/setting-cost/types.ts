// CostData
export type CostDataState = {
  costType: number;
  title: string;
  priceCostConfig: number;
  optionCost: {
    id: number;
    name: string;
    rawMaterialId: number | null;
    componentId: number | null;
    optionsFormula: {
      id: number;
      name: string;
      select: boolean;
    }[];
    optionsPreset: {
      id: number;
      name: string;
      select: boolean;
    }[];
    priceCostConfigDetail: number;
  }[];
}[];

// CostValues
export type CalculateCostValuesState = {
  layDataQuantityId: number;
  printPlateCostId: number;
  costConfig: {
    costType: number;
    title: string;
    optionCost: {
      name: string;
      rawMaterialId: number | null;
      componentId: number | null;
      optionsFormulaId: number;
      optionsPresetId: number | null;
    }[];
  }[];
};

// ServiceChargeData
export type CostServiceChargeDataState = {
  costType: number;
  title: string;
  priceCostConfig: number;
  optionCost: {
    id: number;
    name: string;
    rawMaterialId: number | null;
    componentId: number;
    optionsFormula: {
      id: number;
      name: string;
      select: boolean;
    }[];
    optionsPreset: {
      id: number;
      name: string;
      select: boolean;
    }[];
    priceCostConfigDetail: number;
  }[];
};

// MandatoryServiceChargeData
export type CostMandatoryServiceChargeDataState = {
  costType: number;
  title: string;
  priceCostConfig: number;
  optionCost: {
    id: number;
    name: string;
    rawMaterialId: number | null;
    componentId: number;
    optionsFormula: {
      id: number;
      name: string;
      select: boolean;
    }[];
    optionsPreset: {
      id: number;
      name: string;
      select: boolean;
    }[];
    priceCostConfigDetail: number;
  }[];
};

// ServiceChargeValue
export type CalculateServiceChargeValuesState = {
  layDataQuantityId: number;
  printPlateCostId: number;
  costConfig: {
    costType: number;
    title: string;
    optionCost: {
      id: number;
      name: string;
      rawMaterialId: number | null;
      componentId: number;
      optionsFormulaId: number;
      optionsPresetId: number | null;
    }[];
  }[];
};

// MandatoryServiceChargeValue
export type CalculateMandatoryServiceChargeValuesState = {
  layDataQuantityId: number;
  printPlateCostId: number;
  costConfig: {
    costType: number;
    title: string;
    optionCost: {
      id: number;
      name: string;
      rawMaterialId: number | null;
      componentId: number;
      optionsFormulaId: number;
      optionsPresetId: number | null;
    }[];
  }[];
};

export type SettingCostState = {
  costData: Partial<CostDataState>[];
  calculateCostValues: Partial<CalculateCostValuesState>;
  costServiceChargeData: Partial<CostServiceChargeDataState>[];
  costMandatoryServiceChargeData: Partial<CostMandatoryServiceChargeDataState>[];
  calculateServiceChargeValues: Partial<CalculateServiceChargeValuesState>;
  calculateMandatoryServiceChargeValues: Partial<CalculateMandatoryServiceChargeValuesState>;
};
