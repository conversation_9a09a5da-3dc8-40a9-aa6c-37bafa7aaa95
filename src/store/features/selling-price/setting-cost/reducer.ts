import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import { SettingCostState } from '@/store/features/selling-price/setting-cost/types';
import {
  setCalculateCostValues,
  setCalculateMandatoryServiceChargeValues,
  setCalculateServiceChargeValues,
  setCostData,
  setCostServiceChargeData,
  setMandatoryCostServiceChargeData,
} from '@/store/features/selling-price/setting-cost/actions';

const initialState: SettingCostState = {
  costData: [],
  costServiceChargeData: [],
  costMandatoryServiceChargeData: [],
  calculateCostValues: {},
  calculateServiceChargeValues: {},
  calculateMandatoryServiceChargeValues: {},
};

export const settingCostReducer = createReducer(initialState, (builder) => {
  builder.addCase(setCostData, (state, action) => {
    state.costData = action.payload;
  });
  builder.addCase(setCostServiceChargeData, (state, action) => {
    state.costServiceChargeData = action.payload;
  });
  builder.addCase(setMandatoryCostServiceChargeData, (state, action) => {
    state.costMandatoryServiceChargeData = action.payload;
  });
  builder.addCase(setCalculateCostValues, (state, action) => {
    state.calculateCostValues = action.payload;
  });
  builder.addCase(setCalculateServiceChargeValues, (state, action) => {
    state.calculateServiceChargeValues = action.payload;
  });
  builder.addCase(setCalculateMandatoryServiceChargeValues, (state, action) => {
    state.calculateMandatoryServiceChargeValues = action.payload;
  });
});

export const settingCostSelector = (state: RootState) => state.settingCost;
export default settingCostReducer;
