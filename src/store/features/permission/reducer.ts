import { createReducer } from '@reduxjs/toolkit';
import { RootState } from '@/store';

import { PermissionState } from '@/store/features/permission/types';
import { setPermission } from '@/store/features/permission/actions';

const initialState: PermissionState = {
  permissions: [],
  pending: false,
  error: false,
};

export const permissionReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(setPermission.pending, (state) => {
      state.pending = true;
    })
    .addCase(setPermission.fulfilled, (state, { payload }) => {
      state.pending = false;
      state.permissions = payload;
    })
    .addCase(setPermission.rejected, (state) => {
      state.pending = false;
      state.error = true;
    });
});

export const permissionSelector = (state: RootState) => state.permission;
export default permissionReducer;
