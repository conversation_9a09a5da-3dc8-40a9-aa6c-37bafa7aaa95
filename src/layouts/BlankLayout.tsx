'use client';

import styled from 'styled-components';
import SettingPopup from '@/components/SettingPopup';
import React from 'react';
import { Alert, Snackbar } from '@mui/material';
import { alertSelector, setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';

const ContentStyle = styled.div`
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
`;

export default function BlankLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const dispatch = useAppDispatch();
  const { snackBar } = useAppSelector(alertSelector);
  return (
    <>
      <Snackbar
        open={snackBar.status}
        autoHideDuration={5000}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        onClose={() => {
          dispatch(
            setSnackBar({
              ...snackBar,
              status: false,
            })
          );
        }}
      >
        <Alert
          onClose={() => {
            dispatch(
              setSnackBar({
                ...snackBar,
                status: false,
              })
            );
          }}
          severity={snackBar.severity}
          sx={{
            width: '100%',
            marginTop: '-4px',
            '.MuiAlert-action': {
              padding: '3px 0 0 16px',
            },
          }}
        >
          {snackBar.text}
        </Alert>
      </Snackbar>
      <ContentStyle>{children}</ContentStyle>
      <SettingPopup />
    </>
  );
}
