import Aside from '@/components/aside/Aside';
import React, { ReactElement } from 'react';
import styled from 'styled-components';
import { Alert, Snackbar } from '@mui/material';
import { alertSelector, setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

const ContentStyle = styled.div`
  height: 100dvh;
  display: flex;
  overflow: auto;
  //background: #1b5c458f !important;
  background: ${() =>
    publicRuntimeConfig.DEV_MODE === 'true'
      ? '#1b5c458f !important'
      : 'transparent !important'};
  width: 100%;
  position: relative;
`;
const ContentFlexWrapperStyle = styled.div`
  flex: 4 1 0%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  overflow: auto;
  position: relative;
  @media screen and (max-width: 820px) {
    position: unset;
  }
`;

type MainLayoutProps = {
  children: ReactElement;
};
const MainLayout = ({ children }: MainLayoutProps) => {
  const dispatch = useAppDispatch();
  const { snackBar } = useAppSelector(alertSelector);

  return (
    <>
      <Snackbar
        open={snackBar.status}
        autoHideDuration={5000}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        onClose={() => {
          dispatch(
            setSnackBar({
              ...snackBar,
              status: false,
            })
          );
        }}
      >
        <Alert
          onClose={() => {
            dispatch(
              setSnackBar({
                ...snackBar,
                status: false,
              })
            );
          }}
          severity={snackBar.severity}
          sx={{
            width: '100%',
            marginTop: '-4px',
            '.MuiAlert-action': {
              padding: '3px 0 0 16px',
            },
          }}
        >
          {snackBar.text}
        </Alert>
      </Snackbar>
      <ContentStyle id="zoom-container">
        <Aside />
        <ContentFlexWrapperStyle>{children}</ContentFlexWrapperStyle>
      </ContentStyle>
    </>
  );
};

export default MainLayout;
