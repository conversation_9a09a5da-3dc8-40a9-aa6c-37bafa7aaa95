'use client';

import React from 'react';
import BasicHeader from '@/components/company/BasicHeader';
import styled from 'styled-components';
import { Alert, Snackbar } from '@mui/material';
import { alertSelector, setSnackBar } from '@/store/features/alert';
import { useAppDispatch, useAppSelector } from '@/store';

const AuthLayoutStyle = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  position: relative;
`;

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const dispatch = useAppDispatch();
  const { snackBar } = useAppSelector(alertSelector);
  return (
    <AuthLayoutStyle>
      <Snackbar
        open={snackBar.status}
        autoHideDuration={5000}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        onClose={() => {
          dispatch(
            setSnackBar({
              ...snackBar,
              status: false,
            })
          );
        }}
      >
        <Alert
          onClose={() => {
            dispatch(
              setSnackBar({
                ...snackBar,
                status: false,
              })
            );
          }}
          severity={snackBar.severity}
          sx={{
            width: '100%',
            marginTop: '-4px',
            '.MuiAlert-action': {
              padding: '3px 0 0 16px',
            },
          }}
        >
          {snackBar.text}
        </Alert>
      </Snackbar>
      <BasicHeader />
      <div
        className="flex-1"
        style={{
          marginBottom: '24px',
        }}
      >
        {children}
      </div>
    </AuthLayoutStyle>
  );
}
