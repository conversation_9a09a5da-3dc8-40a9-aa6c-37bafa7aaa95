{"name": "hon-app", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.dev next dev -p 3200", "dev:local": "env-cmd -f .env.local next dev -p 3200", "build": "next build", "build:dev": "env-cmd -f .env.dev next build", "start": "next start", "start:dev": "env-cmd -f .env.dev next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@fontsource/prompt": "^5.0.2", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^2.9.11", "@mui/icons-material": "^5.15.11", "@mui/lab": "^5.0.0-alpha.139", "@mui/material": "^5.14.4", "@mui/x-data-grid": "^5.17.25", "@mui/x-date-pickers": "^7.3.1", "@reduxjs/toolkit": "^1.9.5", "@tinymce/tinymce-react": "^4.3.0", "@types/react-beautiful-dnd": "^13.1.4", "antd": "^5.24.6", "autoprefixer": "10.4.14", "axios": "^1.4.0", "babel-plugin-styled-components": "^2.1.4", "cookies-next": "^2.1.1", "date-fns": "^3.0.6", "dayjs": "^1.11.9", "dayjs-plugin-utc": "^0.1.2", "dequal": "^2.0.3", "env-cmd": "^10.1.0", "file-saver": "^2.0.5", "focus-formik-error": "^1.1.0", "formik": "^2.4.1", "formik-error-focus": "^2.0.0", "framer-motion": "^12.4.7", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.518.0", "medium-zoom": "^1.1.0", "moment": "^2.29.4", "next": "^14.2.3", "next-images": "^1.8.5", "next-redux-wrapper": "^8.1.0", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "react": "18.2.0", "react-accessible-treeview": "^2.9.1", "react-beautiful-dnd": "^13.1.1", "react-circular-progressbar": "^2.1.0", "react-colorful": "^5.6.1", "react-countdown": "^2.3.6", "react-date-range": "2.0.1", "react-day-picker": "^8.10.0", "react-dom": "18.2.0", "react-feather": "^2.0.10", "react-hook-form": "^7.45.4", "react-image-file-resizer": "^0.4.8", "react-infinite-scroll-component": "^6.1.0", "react-number-format": "^5.4.3", "react-redux": "^8.0.7", "react-to-print": "^2.14.15", "sass": "^1.71.1", "scrollreveal": "^4.0.9", "sharp": "^0.33.4", "short-uuid": "^4.2.2", "styled-components": "^6.0.7", "sweetalert2": "^11.7.22", "swr": "^2.2.4", "tailwindcss": "3.3.2", "typescript": "5.0.4", "usehooks-ts": "^2.9.1", "yup": "^1.4.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.3", "@types/jwt-decode": "^2.2.1", "@types/lodash": "^4.14.195", "@types/node": "20.2.3", "@types/react": "18.2.7", "@types/react-date-range": "1.4.9", "@types/react-dom": "^18.2.4", "@types/scrollreveal": "^0.0.8", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@uidotdev/usehooks": "^2.4.1", "eslint": "8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-next": "13.4.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^2.0.0", "prettier": "^2.8.0"}}